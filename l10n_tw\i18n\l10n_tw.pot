# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tw
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-26 10:11+0000\n"
"PO-Revision-Date: 2022-04-26 10:11+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_217100
#: model:account.account,name:l10n_tw.2_tw_217100
#: model:account.account.template,name:l10n_tw.tw_217100
msgid "Accounts payable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_119100
#: model:account.account,name:l10n_tw.2_tw_119100
#: model:account.account.template,name:l10n_tw.tw_119100
msgid "Accounts receivable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_119150
#: model:account.account,name:l10n_tw.2_tw_119150
#: model:account.account.template,name:l10n_tw.tw_119150
msgid "Accounts receivable (PoS)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_155200
#: model:account.account,name:l10n_tw.2_tw_155200
#: model:account.account.template,name:l10n_tw.tw_155200
msgid "Accumulated amortization, other intangible assets"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_141300
#: model:account.account,name:l10n_tw.2_tw_141300
#: model:account.account.template,name:l10n_tw.tw_141300
msgid "Accumulated depreciation, buildings and structures"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_144200
#: model:account.account,name:l10n_tw.2_tw_144200
#: model:account.account.template,name:l10n_tw.tw_144200
msgid "Accumulated depreciation, leased assets"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_142200
#: model:account.account,name:l10n_tw.2_tw_142200
#: model:account.account.template,name:l10n_tw.tw_142200
msgid "Accumulated depreciation, machinery and equipment"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_143200
#: model:account.account,name:l10n_tw.2_tw_143200
#: model:account.account.template,name:l10n_tw.tw_143200
msgid "Accumulated depreciation, office equipment"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_335100
#: model:account.account,name:l10n_tw.2_tw_335100
#: model:account.account.template,name:l10n_tw.tw_335100
msgid "Accumulated profit and loss"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_222100
#: model:account.account,name:l10n_tw.2_tw_222100
#: model:account.account.template,name:l10n_tw.tw_222100
msgid "Advance sales receipts"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611800
#: model:account.account,name:l10n_tw.2_tw_611800
#: model:account.account.template,name:l10n_tw.tw_611800
msgid "Advertisement expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_211200
#: model:account.account,name:l10n_tw.2_tw_211200
#: model:account.account.template,name:l10n_tw.tw_211200
msgid "Bank loan"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_141100
#: model:account.account,name:l10n_tw.2_tw_141100
#: model:account.account.template,name:l10n_tw.tw_141100
msgid "Buildings and structures, cost"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_126800
#: model:account.account,name:l10n_tw.2_tw_126800
#: model:account.account.template,name:l10n_tw.tw_126800
msgid "Business tax paid (or Input VAT)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_219400
#: model:account.account,name:l10n_tw.2_tw_219400
#: model:account.account.template,name:l10n_tw.tw_219400
msgid "Business tax payable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_220400
#: model:account.account,name:l10n_tw.2_tw_220400
#: model:account.account.template,name:l10n_tw.tw_220400
msgid "Business tax received (or Output VAT)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_321100
#: model:account.account,name:l10n_tw.2_tw_321100
#: model:account.account.template,name:l10n_tw.tw_321100
msgid ""
"Capital surplus, additional paid-in capital arising from ordinary share"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_718500
#: model:account.account,name:l10n_tw.2_tw_718500
#: model:account.account.template,name:l10n_tw.tw_718500
msgid "Cash difference gains"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_718600
#: model:account.account,name:l10n_tw.2_tw_718600
#: model:account.account.template,name:l10n_tw.tw_718600
msgid "Cash difference losses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_613100
#: model:account.account,name:l10n_tw.2_tw_613100
#: model:account.account.template,name:l10n_tw.tw_613100
msgid "Commissions expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_723200
#: model:account.account,name:l10n_tw.2_tw_723200
#: model:account.account.template,name:l10n_tw.tw_723200
msgid "Commissions revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_511100
#: model:account.account,name:l10n_tw.2_tw_511100
#: model:account.account.template,name:l10n_tw.tw_511100
msgid "Cost of sales"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_156100
#: model:account.account,name:l10n_tw.2_tw_156100
#: model:account.account.template,name:l10n_tw.tw_156100
msgid "Deferred tax assets"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612600
#: model:account.account,name:l10n_tw.2_tw_612600
#: model:account.account.template,name:l10n_tw.tw_612600
msgid "Depletions and amortizations"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516300
#: model:account.account,name:l10n_tw.1_tw_612500
#: model:account.account,name:l10n_tw.2_tw_516300
#: model:account.account,name:l10n_tw.2_tw_612500
#: model:account.account.template,name:l10n_tw.tw_516300
#: model:account.account.template,name:l10n_tw.tw_612500
msgid "Depreciations"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_514100
#: model:account.account,name:l10n_tw.2_tw_514100
#: model:account.account.template,name:l10n_tw.tw_514100
msgid "Direct labor"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_714100
#: model:account.account,name:l10n_tw.2_tw_714100
#: model:account.account.template,name:l10n_tw.tw_714100
msgid "Dividend revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_220100
#: model:account.account,name:l10n_tw.2_tw_220100
#: model:account.account.template,name:l10n_tw.tw_220100
msgid "Dividends payable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612200
#: model:account.account,name:l10n_tw.2_tw_612200
#: model:account.account.template,name:l10n_tw.tw_612200
msgid "Donation expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_121100
#: model:account.account,name:l10n_tw.2_tw_121100
#: model:account.account.template,name:l10n_tw.tw_121100
msgid "Earned revenue receivable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612900
#: model:account.account,name:l10n_tw.2_tw_612900
#: model:account.account.template,name:l10n_tw.tw_612900
msgid "Employee benefits/welfare"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612100
#: model:account.account,name:l10n_tw.2_tw_612100
#: model:account.account.template,name:l10n_tw.tw_612100
msgid "Entertainment expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_123500
#: model:account.account,name:l10n_tw.2_tw_123500
#: model:account.account.template,name:l10n_tw.tw_123500
msgid "Finished goods"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_718100
#: model:account.account,name:l10n_tw.2_tw_718100
#: model:account.account.template,name:l10n_tw.tw_718100
msgid "Foreign exchange gains"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_718200
#: model:account.account,name:l10n_tw.2_tw_718200
#: model:account.account.template,name:l10n_tw.tw_718200
msgid "Foreign exchange losses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611500
#: model:account.account,name:l10n_tw.2_tw_611500
#: model:account.account.template,name:l10n_tw.tw_611500
msgid "Freight"
msgstr ""

#. module: l10n_tw
#: model:account.tax.group,name:l10n_tw.tax_group_gst_5
msgid "GST 5%"
msgstr ""

#. module: l10n_tw
#: model:account.tax,name:l10n_tw.1_tw_tax_purchase_inc_5
#: model:account.tax,name:l10n_tw.2_tw_tax_purchase_inc_5
#: model:account.tax.template,name:l10n_tw.tw_tax_purchase_inc_5
msgid "GST Inc Purchase (5%)"
msgstr ""

#. module: l10n_tw
#: model:account.tax,name:l10n_tw.1_tw_tax_sale_inc_5
#: model:account.tax,name:l10n_tw.2_tw_tax_sale_inc_5
#: model:account.tax.template,name:l10n_tw.tw_tax_sale_inc_5
msgid "GST Inc Sale (5%)"
msgstr ""

#. module: l10n_tw
#: model:account.tax,description:l10n_tw.1_tw_tax_purchase_inc_5
#: model:account.tax,description:l10n_tw.2_tw_tax_purchase_inc_5
#: model:account.tax.template,description:l10n_tw.tw_tax_purchase_inc_5
msgid "GST Inclusive Purchases"
msgstr ""

#. module: l10n_tw
#: model:account.tax,description:l10n_tw.1_tw_tax_sale_inc_5
#: model:account.tax,description:l10n_tw.2_tw_tax_sale_inc_5
#: model:account.tax.template,description:l10n_tw.tw_tax_sale_inc_5
msgid "GST Inclusive Sale"
msgstr ""

#. module: l10n_tw
#: model:account.tax,description:l10n_tw.1_tw_tax_purchase_5
#: model:account.tax,description:l10n_tw.2_tw_tax_purchase_5
#: model:account.tax.template,description:l10n_tw.tw_tax_purchase_5
msgid "GST Purchase"
msgstr ""

#. module: l10n_tw
#: model:account.tax,description:l10n_tw.1_tw_tax_sale_5
#: model:account.tax,description:l10n_tw.2_tw_tax_sale_5
#: model:account.tax.template,description:l10n_tw.tw_tax_sale_5
msgid "GST Sales"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_719100
#: model:account.account,name:l10n_tw.2_tw_719100
#: model:account.account.template,name:l10n_tw.tw_719100
msgid "Gains on disposals of investment property"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_720200
#: model:account.account,name:l10n_tw.2_tw_720200
#: model:account.account.template,name:l10n_tw.tw_720200
msgid "Gains on disposals of property, plant and equipment"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_158300
#: model:account.account,name:l10n_tw.2_tw_158300
#: model:account.account.template,name:l10n_tw.tw_158300
msgid "Guarantee deposits paid"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516800
#: model:account.account,name:l10n_tw.2_tw_516800
#: model:account.account.template,name:l10n_tw.tw_516800
msgid "Indirect materials"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516000
#: model:account.account,name:l10n_tw.1_tw_612000
#: model:account.account,name:l10n_tw.2_tw_516000
#: model:account.account,name:l10n_tw.2_tw_612000
#: model:account.account.template,name:l10n_tw.tw_516000
#: model:account.account.template,name:l10n_tw.tw_612000
msgid "Insurance expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_715100
#: model:account.account,name:l10n_tw.2_tw_715100
#: model:account.account.template,name:l10n_tw.tw_715100
msgid "Interest expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_711100
#: model:account.account,name:l10n_tw.2_tw_711100
#: model:account.account.template,name:l10n_tw.tw_711100
msgid "Interest revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_717100
#: model:account.account,name:l10n_tw.2_tw_717100
#: model:account.account.template,name:l10n_tw.tw_717100
msgid "Investment income accounted for using equity method"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_717200
#: model:account.account,name:l10n_tw.2_tw_717200
#: model:account.account.template,name:l10n_tw.tw_717200
msgid "Investment loss accounted for using equity method"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_137100
#: model:account.account,name:l10n_tw.2_tw_137100
#: model:account.account.template,name:l10n_tw.tw_137100
msgid "Investments accounted for using equity method"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_139100
#: model:account.account,name:l10n_tw.2_tw_139100
#: model:account.account.template,name:l10n_tw.tw_139100
msgid "Land, cost"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_144100
#: model:account.account,name:l10n_tw.2_tw_144100
#: model:account.account.template,name:l10n_tw.tw_144100
msgid "Leased assets, cost"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_l10n_tw_chart_template_liquidity_transfer
#: model:account.account,name:l10n_tw.2_l10n_tw_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_tw.l10n_tw_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_719200
#: model:account.account,name:l10n_tw.2_tw_719200
#: model:account.account.template,name:l10n_tw.tw_719200
msgid "Losses on disposals of investment property"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612400
#: model:account.account,name:l10n_tw.2_tw_612400
#: model:account.account.template,name:l10n_tw.tw_612400
msgid "Losses on doubtful debts"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612700
#: model:account.account,name:l10n_tw.2_tw_612700
#: model:account.account.template,name:l10n_tw.tw_612700
msgid "Losses on export sales"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_142100
#: model:account.account,name:l10n_tw.2_tw_142100
#: model:account.account.template,name:l10n_tw.tw_142100
msgid "Machinery and equipment, cost"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516500
#: model:account.account,name:l10n_tw.1_tw_612800
#: model:account.account,name:l10n_tw.2_tw_516500
#: model:account.account,name:l10n_tw.2_tw_612800
#: model:account.account.template,name:l10n_tw.tw_516500
#: model:account.account.template,name:l10n_tw.tw_612800
msgid "Meal expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_123100
#: model:account.account,name:l10n_tw.2_tw_123100
#: model:account.account.template,name:l10n_tw.tw_123100
msgid "Merchandise inventory"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_720100
#: model:account.account,name:l10n_tw.2_tw_720100
#: model:account.account.template,name:l10n_tw.tw_720100
msgid "Net gain or loss on disposals of property, plant and equipment"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_216100
#: model:account.account,name:l10n_tw.2_tw_216100
#: model:account.account.template,name:l10n_tw.tw_216100
msgid "Notes payable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_118100
#: model:account.account,name:l10n_tw.2_tw_118100
#: model:account.account.template,name:l10n_tw.tw_118100
msgid "Notes receivable"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_143100
#: model:account.account,name:l10n_tw.2_tw_143100
#: model:account.account.template,name:l10n_tw.tw_143100
msgid "Office equipment, cost"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_126400
#: model:account.account,name:l10n_tw.2_tw_126400
#: model:account.account.template,name:l10n_tw.tw_126400
msgid "Office supplies"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_311100
#: model:account.account,name:l10n_tw.2_tw_311100
#: model:account.account.template,name:l10n_tw.tw_311100
msgid "Ordinary share"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_219700
#: model:account.account,name:l10n_tw.2_tw_219700
#: model:account.account.template,name:l10n_tw.tw_219700
msgid "Other accrued expenses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_222300
#: model:account.account,name:l10n_tw.2_tw_222300
#: model:account.account.template,name:l10n_tw.tw_222300
msgid "Other advance receipts"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_341500
#: model:account.account,name:l10n_tw.2_tw_341500
#: model:account.account.template,name:l10n_tw.tw_341500
msgid "Other equity interest, others"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_155100
#: model:account.account,name:l10n_tw.2_tw_155100
#: model:account.account.template,name:l10n_tw.tw_155100
msgid "Other intangible assets, net"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_123150
#: model:account.account,name:l10n_tw.2_tw_123150
#: model:account.account.template,name:l10n_tw.tw_123150
msgid "Other inventory (pending acceptance)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_158600
#: model:account.account,name:l10n_tw.2_tw_158600
#: model:account.account.template,name:l10n_tw.tw_158600
msgid "Other non-current assets, others"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_591100
#: model:account.account,name:l10n_tw.2_tw_591100
#: model:account.account.template,name:l10n_tw.tw_591100
msgid "Other operating costs"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_613400
#: model:account.account,name:l10n_tw.2_tw_613400
#: model:account.account.template,name:l10n_tw.tw_613400
msgid "Other operating expenses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_414100
#: model:account.account,name:l10n_tw.2_tw_414100
#: model:account.account.template,name:l10n_tw.tw_414100
msgid "Other operating revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516900
#: model:account.account,name:l10n_tw.2_tw_516900
#: model:account.account.template,name:l10n_tw.tw_516900
msgid "Other overheads"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_126500
#: model:account.account,name:l10n_tw.2_tw_126500
#: model:account.account.template,name:l10n_tw.tw_126500
msgid "Other prepaid expenses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_127000
#: model:account.account,name:l10n_tw.2_tw_127000
#: model:account.account.template,name:l10n_tw.tw_127000
msgid "Other prepayments"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_724700
#: model:account.account,name:l10n_tw.2_tw_724700
#: model:account.account.template,name:l10n_tw.tw_724700
msgid "Other revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_126900
#: model:account.account,name:l10n_tw.2_tw_126900
#: model:account.account.template,name:l10n_tw.tw_126900
msgid "Overpaid sales tax"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_239300
#: model:account.account,name:l10n_tw.2_tw_239300
#: model:account.account.template,name:l10n_tw.tw_239300
msgid "Owner (shareholder) accounts, credit"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_158400
#: model:account.account,name:l10n_tw.2_tw_158400
#: model:account.account.template,name:l10n_tw.tw_158400
msgid "Owner (shareholder) accounts, debit"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_515800
#: model:account.account,name:l10n_tw.2_tw_515800
#: model:account.account.template,name:l10n_tw.tw_515800
msgid "Packing expenses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_219900
#: model:account.account,name:l10n_tw.2_tw_219900
#: model:account.account.template,name:l10n_tw.tw_219900
msgid "Payable on machinery and equipment"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_128200
#: model:account.account,name:l10n_tw.2_tw_128200
#: model:account.account.template,name:l10n_tw.tw_128200
msgid "Payment on behalf of others"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611600
#: model:account.account,name:l10n_tw.2_tw_611600
#: model:account.account.template,name:l10n_tw.tw_611600
msgid "Postage expenses"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_126600
#: model:account.account,name:l10n_tw.2_tw_126600
#: model:account.account.template,name:l10n_tw.tw_126600
msgid "Prepayment for purchases"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_158200
#: model:account.account,name:l10n_tw.2_tw_158200
#: model:account.account.template,name:l10n_tw.tw_158200
msgid "Prepayments for business facilities"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_516100
#: model:account.account,name:l10n_tw.2_tw_516100
#: model:account.account.template,name:l10n_tw.tw_516100
msgid "Processing expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_217150
#: model:account.account,name:l10n_tw.2_tw_217150
#: model:account.account.template,name:l10n_tw.tw_217150
msgid "Provisional estimate payable"
msgstr ""

#. module: l10n_tw
#: model:account.tax,name:l10n_tw.1_tw_tax_purchase_5
#: model:account.tax,name:l10n_tw.2_tw_tax_purchase_5
#: model:account.tax.template,name:l10n_tw.tw_tax_purchase_5
msgid "Purchase (5%)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_512100
#: model:account.account,name:l10n_tw.2_tw_512100
#: model:account.account.template,name:l10n_tw.tw_512100
msgid "Purchase of goods"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_513100
#: model:account.account,name:l10n_tw.2_tw_513100
#: model:account.account.template,name:l10n_tw.tw_513100
msgid "Purchase of raw materials"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_512400
#: model:account.account,name:l10n_tw.2_tw_512400
#: model:account.account.template,name:l10n_tw.tw_512400
msgid "Purchases discounts and allowances"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_512300
#: model:account.account,name:l10n_tw.2_tw_512300
#: model:account.account.template,name:l10n_tw.tw_512300
msgid "Purchases returns"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_123900
#: model:account.account,name:l10n_tw.2_tw_123900
#: model:account.account.template,name:l10n_tw.tw_123900
msgid "Raw materials"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_513400
#: model:account.account,name:l10n_tw.2_tw_513400
#: model:account.account.template,name:l10n_tw.tw_513400
msgid "Raw materials purchase discounts and allowances"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_513300
#: model:account.account,name:l10n_tw.2_tw_513300
#: model:account.account.template,name:l10n_tw.tw_513300
msgid "Raw materials purchase returns"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_225200
#: model:account.account,name:l10n_tw.2_tw_225200
#: model:account.account.template,name:l10n_tw.tw_225200
msgid "Receipts under custody"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_128400
#: model:account.account,name:l10n_tw.2_tw_128400
#: model:account.account.template,name:l10n_tw.tw_128400
msgid "Refundable deposits"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_515200
#: model:account.account,name:l10n_tw.1_tw_611200
#: model:account.account,name:l10n_tw.2_tw_515200
#: model:account.account,name:l10n_tw.2_tw_611200
#: model:account.account.template,name:l10n_tw.tw_515200
#: model:account.account.template,name:l10n_tw.tw_611200
msgid "Rent expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_712100
#: model:account.account,name:l10n_tw.2_tw_712100
#: model:account.account.template,name:l10n_tw.tw_712100
msgid "Rent income"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_515700
#: model:account.account,name:l10n_tw.1_tw_611700
#: model:account.account,name:l10n_tw.2_tw_515700
#: model:account.account,name:l10n_tw.2_tw_611700
#: model:account.account.template,name:l10n_tw.tw_515700
#: model:account.account.template,name:l10n_tw.tw_611700
msgid "Repairs and maintenance expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_613000
#: model:account.account,name:l10n_tw.2_tw_613000
#: model:account.account.template,name:l10n_tw.tw_613000
msgid "Research and development expense"
msgstr ""

#. module: l10n_tw
#: model:account.tax,name:l10n_tw.1_tw_tax_sale_5
#: model:account.tax,name:l10n_tw.2_tw_tax_sale_5
#: model:account.tax.template,name:l10n_tw.tw_tax_sale_5
msgid "Sale (5%)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_411400
#: model:account.account,name:l10n_tw.2_tw_411400
#: model:account.account.template,name:l10n_tw.tw_411400
msgid "Sales discounts and allowances"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_411300
#: model:account.account,name:l10n_tw.2_tw_411300
#: model:account.account.template,name:l10n_tw.tw_411300
msgid "Sales returns"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_411100
#: model:account.account,name:l10n_tw.2_tw_411100
#: model:account.account.template,name:l10n_tw.tw_411100
msgid "Sales revenue"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_613300
#: model:account.account,name:l10n_tw.2_tw_613300
#: model:account.account.template,name:l10n_tw.tw_613300
msgid "Services expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611300
#: model:account.account,name:l10n_tw.2_tw_611300
#: model:account.account.template,name:l10n_tw.tw_611300
msgid "Stationery supplies"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_124000
#: model:account.account,name:l10n_tw.2_tw_124000
#: model:account.account.template,name:l10n_tw.tw_124000
msgid "Supplies"
msgstr ""

#. module: l10n_tw
#: model:account.chart.template,name:l10n_tw.l10n_tw_chart_template
msgid "Taiwan Tax and Account Chart Template"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_821100
#: model:account.account,name:l10n_tw.2_tw_821100
#: model:account.account.template,name:l10n_tw.tw_821100
msgid "Tax expense (income)"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_612300
#: model:account.account,name:l10n_tw.2_tw_612300
#: model:account.account.template,name:l10n_tw.tw_612300
msgid "Taxes"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_225100
#: model:account.account,name:l10n_tw.2_tw_225100
#: model:account.account.template,name:l10n_tw.tw_225100
msgid "Temporary credits"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_128100
#: model:account.account,name:l10n_tw.2_tw_128100
#: model:account.account.template,name:l10n_tw.tw_128100
msgid "Temporary payments"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_613200
#: model:account.account,name:l10n_tw.2_tw_613200
#: model:account.account.template,name:l10n_tw.tw_613200
msgid "Training expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611400
#: model:account.account,name:l10n_tw.2_tw_611400
#: model:account.account.template,name:l10n_tw.tw_611400
msgid "Traveling Expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_515900
#: model:account.account,name:l10n_tw.1_tw_611900
#: model:account.account,name:l10n_tw.2_tw_515900
#: model:account.account,name:l10n_tw.2_tw_611900
#: model:account.account.template,name:l10n_tw.tw_515900
#: model:account.account.template,name:l10n_tw.tw_611900
msgid "Utilities expense"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_611100
#: model:account.account,name:l10n_tw.2_tw_611100
#: model:account.account.template,name:l10n_tw.tw_611100
msgid "Wages and salaries"
msgstr ""

#. module: l10n_tw
#: model:account.account,name:l10n_tw.1_tw_123700
#: model:account.account,name:l10n_tw.2_tw_123700
#: model:account.account.template,name:l10n_tw.tw_123700
msgid "Work in progress"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_206
msgid "七堵區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_883
msgid "七美鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_724
msgid "七股區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_901
msgid "三地門鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_237
msgid "三峽區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_266
msgid "三星鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_807
msgid "三民區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_352
msgid "三灣鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_367
msgid "三義鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_252
msgid "三芝區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_241
msgid "三重區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_735
msgid "下營區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_400
msgid "中區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_235
msgid "中和區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_606
msgid "中埔鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_320
msgid "中壢區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_541
msgid "中寮鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_104
#: model:res.city,name:l10n_tw.city_tw_203
msgid "中山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_100
#: model:res.city,name:l10n_tw.city_tw_202
msgid "中正區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_700
msgid "中西區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_904
msgid "九如鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_649
msgid "二崙鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_526
msgid "二林鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_530
msgid "二水鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_311
msgid "五峰鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_268
msgid "五結鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_248
msgid "五股區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_717
msgid "仁德區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_200
msgid "仁愛區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_546
msgid "仁愛鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_814
msgid "仁武區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_509
msgid "伸港鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_931
msgid "佳冬鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_722
msgid "佳里區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_922
msgid "來義鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_110
#: model:res.city,name:l10n_tw.city_tw_201
msgid "信義區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_556
msgid "信義鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_655
msgid "元長鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_976
msgid "光復鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_912
msgid "內埔鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_114
msgid "內湖區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_845
msgid "內門區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_334
msgid "八德區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_249
msgid "八里區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_363
msgid "公館鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_734
msgid "六甲區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_615
msgid "六腳鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_844
msgid "六龜區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_269
msgid "冬山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_801
msgid "前金區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_806
msgid "前鎮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_300
#: model:res.city,name:l10n_tw.city_tw_404
#: model:res.city,name:l10n_tw.city_tw_704
msgid "北區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_314
msgid "北埔鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_406
msgid "北屯區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_112
msgid "北投區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_521
msgid "北斗鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_651
msgid "北港鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_210
msgid "北竿鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_727
msgid "北門區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_954
msgid "卑南鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_982
msgid "卓溪鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_369
msgid "卓蘭鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_716
msgid "南化區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_402
#: model:res.city,name:l10n_tw.city_tw_702
msgid "南區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_408
msgid "南屯區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_926
msgid "南州鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_353
msgid "南庄鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_540
msgid "南投市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_ntc
msgid "南投縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_819
msgid "南沙群島"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_115
msgid "南港區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_272
msgid "南澳鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_209
msgid "南竿鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_653
msgid "口湖鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_646
msgid "古坑鄉"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_tcc
msgid "台中市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_tpc
msgid "台北市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_tnh
msgid "台南市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_950
msgid "台東市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_tth
msgid "台東縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_636
msgid "台西鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_973
msgid "吉安鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_551
msgid "名間鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_421
msgid "后里區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_424
msgid "和平區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_508
msgid "和美鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_264
msgid "員山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_510
msgid "員林市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_741
msgid "善化區"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_cic
msgid "嘉義市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_cih
msgid "嘉義縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_654
msgid "四湖鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_544
msgid "國姓鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_236
msgid "土城區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_633
msgid "土庫鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_232
msgid "坪林區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_513
msgid "埔心鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_545
msgid "埔里鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_516
msgid "埔鹽鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_523
msgid "埤頭鄉"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_klc
msgid "基隆市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_111
msgid "士林區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_263
msgid "壯圍鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_974
msgid "壽豐鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_438
msgid "外埔區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_742
msgid "大內區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_103
msgid "大同區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_267
msgid "大同鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_337
msgid "大園區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_527
msgid "大城鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_607
msgid "大埔鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_631
msgid "大埤鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_106
#: model:res.city,name:l10n_tw.city_tw_439
msgid "大安區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_831
msgid "大寮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_515
msgid "大村鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_622
msgid "大林鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_840
msgid "大樹區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_965
msgid "大武鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_364
msgid "大湖鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_335
msgid "大溪區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_437
msgid "大甲區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_815
msgid "大社區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_432
msgid "大肚區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_412
msgid "大里區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_428
msgid "大雅區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_612
msgid "太保市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_411
msgid "太平區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_963
msgid "太麻里鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_726
msgid "學甲區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_709
msgid "安南區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_745
msgid "安定區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_708
msgid "安平區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_204
msgid "安樂區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_720
msgid "官田區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_260
msgid "宜蘭市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_ilh
msgid "宜蘭縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_983
msgid "富里鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_308
msgid "寶山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_725
msgid "將軍區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_812
msgid "小港區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_313
msgid "尖石鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_900
msgid "屏東市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_pth
msgid "屏東縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_743
msgid "山上區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_820
msgid "岡山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_315
msgid "峨眉鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_924
msgid "崁頂鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_637
msgid "崙背鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_813
msgid "左營區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_713
msgid "左鎮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_625
msgid "布袋鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_226
msgid "平溪區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_324
msgid "平鎮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_953
msgid "延平鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_827
msgid "彌陀區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_500
msgid "彰化市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_chh
msgid "彰化縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_731
msgid "後壁區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_356
msgid "後龍鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_336
msgid "復興區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_946
msgid "恆春鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_961
msgid "成功鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_116
msgid "文山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_640
msgid "斗六市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_630
msgid "斗南鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_712
msgid "新化區"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_ntpc
msgid "新北市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_932
msgid "新園鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_971
msgid "新城鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_305
msgid "新埔鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_925
msgid "新埤鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_327
msgid "新屋區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_744
msgid "新市區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_231
msgid "新店區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_616
msgid "新港鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_730
msgid "新營區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_426
msgid "新社區"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_hct
msgid "新竹市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_hch
msgid "新竹縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_800
msgid "新興區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_242
msgid "新莊區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_304
msgid "新豐鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_842
msgid "旗山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_805
msgid "旗津區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_942
msgid "春日鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_205
msgid "暖暖區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_882
msgid "望安鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_613
msgid "朴子市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_846
msgid "杉林區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_423
msgid "東勢區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_635
msgid "東勢鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_300_1
#: model:res.city,name:l10n_tw.city_tw_401
#: model:res.city,name:l10n_tw.city_tw_600
#: model:res.city,name:l10n_tw.city_tw_701
msgid "東區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_733
msgid "東山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_212
msgid "東引鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_817
msgid "東沙群島"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_959
msgid "東河鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_928
msgid "東港鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_614
msgid "東石鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_105
msgid "松山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_220
msgid "板橋區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_940
msgid "枋寮鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_941
msgid "枋山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_643
msgid "林內鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_244
msgid "林口區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_832
msgid "林園區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_927
msgid "林邊鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_736
msgid "柳營區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_330
msgid "桃園區"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_tyc
msgid "桃園市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_848
msgid "桃源區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_603
msgid "梅山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_826
msgid "梓官區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_435
msgid "梧棲區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_326
msgid "楊梅區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_811
msgid "楠梓區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_715
msgid "楠西區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_238
msgid "樹林區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_825
msgid "橋頭區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_312
msgid "橫山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_711
msgid "歸仁區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_621
msgid "民雄鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_608
msgid "水上鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_652
msgid "水林鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_553
msgid "水里鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_234
msgid "永和區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_828
msgid "永安區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_710
msgid "永康區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_512
msgid "永靖鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_221
msgid "汐止區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_958
msgid "池上鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_433
msgid "沙鹿區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_365
msgid "泰安鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_243
msgid "泰山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_921
msgid "泰武鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_957
msgid "海端鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_251
msgid "淡水區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_222
msgid "深坑區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_436
msgid "清水區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_829
msgid "湖內區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_303
msgid "湖口鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_885
msgid "湖西鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_623
msgid "溪口鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_524
msgid "溪州鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_514
msgid "溪湖鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_947
msgid "滿州鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_427
msgid "潭子區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_920
msgid "潮州鎮"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_phc
msgid "澎湖縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_894
msgid "烈嶼鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_233
msgid "烏來區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_896
msgid "烏坵鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_414
msgid "烏日區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_824
msgid "燕巢區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_945
msgid "牡丹鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_943
msgid "獅子鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_354
msgid "獅潭鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_714
msgid "玉井區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_981
msgid "玉里鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_929
msgid "琉球鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_978
msgid "瑞穗鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_224
msgid "瑞芳區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_903
msgid "瑪家鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_520
msgid "田中鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_823
msgid "田寮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_522
msgid "田尾鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_847
msgid "甲仙區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_602
msgid "番路鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_884
msgid "白沙鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_732
msgid "白河區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_422
msgid "石岡區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_223
msgid "石碇區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_253
msgid "石門區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_262
msgid "礁溪鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_511
msgid "社頭鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_429
msgid "神岡區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_506
msgid "福興鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_972
msgid "秀林鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_504
msgid "秀水鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_302
msgid "竹北市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_350
msgid "竹南鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_525
msgid "竹塘鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_557
msgid "竹山鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_604
msgid "竹崎鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_310
msgid "竹東鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_911
msgid "竹田鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_951
msgid "綠島鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_507
msgid "線西鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_265
msgid "羅東鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_843
msgid "美濃區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_624
msgid "義竹鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_307
msgid "芎林鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_502
msgid "芬園鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_503
msgid "花壇鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_970
msgid "花蓮市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_hlh
msgid "花蓮縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_528
msgid "芳苑鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_358
msgid "苑裡鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_802
msgid "苓雅區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_360
msgid "苗栗市"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_mlh
msgid "苗栗縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_851
msgid "茂林區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_852
msgid "茄萣區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_542
msgid "草屯鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_211
msgid "莒光鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_647
msgid "莿桐鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_913
msgid "萬丹鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_923
msgid "萬巒鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_979
msgid "萬榮鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_108
msgid "萬華區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_207
msgid "萬里區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_247
msgid "蘆洲區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_338
msgid "蘆竹區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_270
msgid "蘇澳鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_952
msgid "蘭嶼鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_632
msgid "虎尾鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_634
msgid "褒忠鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_403
#: model:res.city,name:l10n_tw.city_tw_600_1
msgid "西區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_407
msgid "西屯區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_881
msgid "西嶼鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_723
msgid "西港區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_368
msgid "西湖鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_648
msgid "西螺鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_328
msgid "觀音區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_420
msgid "豐原區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_977
msgid "豐濱鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_228
msgid "貢寮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_821
msgid "路竹區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_944
msgid "車城鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_357
msgid "通霄鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_361
msgid "造橋鄉"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_lcc
msgid "連江縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_966
msgid "達仁鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_849
msgid "那瑪夏區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_905
msgid "里港鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_893
msgid "金城鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_892
msgid "金寧鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_208
msgid "金山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_964
msgid "金峰鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_890
msgid "金沙鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_891
msgid "金湖鎮"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_kmc
msgid "金門縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_290
msgid "釣魚臺"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_366
msgid "銅鑼鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_908
msgid "長治鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_962
msgid "長濱鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_956
msgid "關山鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_718
msgid "關廟區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_306
msgid "關西鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_822
msgid "阿蓮區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_605
msgid "阿里山鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_552
msgid "集集鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_227
msgid "雙溪區"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_ylh
msgid "雲林縣"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_902
msgid "霧台鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_413
msgid "霧峰區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_351
msgid "頭份市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_261
msgid "頭城鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_362
msgid "頭屋鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_300_2
msgid "香山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_880
msgid "馬公市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_906
msgid "高樹鄉"
msgstr ""

#. module: l10n_tw
#: model:res.country.state,name:l10n_tw.state_tw_khc
msgid "高雄市"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_555
msgid "魚池鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_833
msgid "鳥松區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_830
msgid "鳳山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_975
msgid "鳳林鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_239
msgid "鶯歌區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_907
msgid "鹽埔鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_803
msgid "鹽埕區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_737
msgid "鹽水區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_505
msgid "鹿港鎮"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_611
msgid "鹿草鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_558
msgid "鹿谷鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_955
msgid "鹿野鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_909
msgid "麟洛鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_638
msgid "麥寮鄉"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_721
msgid "麻豆區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_804
msgid "鼓山區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_434
msgid "龍井區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_719
msgid "龍崎區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_325
msgid "龍潭區"
msgstr ""

#. module: l10n_tw
#: model:res.city,name:l10n_tw.city_tw_333
msgid "龜山區"
msgstr ""
