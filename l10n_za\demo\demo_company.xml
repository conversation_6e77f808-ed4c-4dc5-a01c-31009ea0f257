<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_za" model="res.partner">
        <field name="name">ZA Company</field>
        <field name="vat"></field>
        <field name="street">A</field>
        <field name="city">Cape Town</field>
        <field name="country_id" ref="base.za"/>
        <field name="state_id" ref="base.state_za_wc"/>
        <field name="zip">7845</field>
        <field name="phone">+27 71 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.zaexample.com</field>
    </record>

    <record id="demo_company_za" model="res.company">
        <field name="name">ZA Company</field>
        <field name="partner_id" ref="partner_demo_company_za"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_za')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_za.demo_company_za'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_za.default_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_za.demo_company_za')"/>
    </function>
</odoo>
