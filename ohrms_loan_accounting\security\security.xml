<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">


        <record id="hr_loan_manager_account_rule" model="ir.rule">
            <field name="name">Loan Forms Modification Accounts</field>
            <field name="model_id" ref="model_hr_loan"/>
            <field name="groups" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>


    </data>
</odoo>