# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-31 17:26+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"<br/><br/>\n"
"                Thanks,<br/>\n"
"                <b>The Odoo Team</b>"
msgstr ""

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_acquirer.py:0
#, python-format
msgid "Add your PayPal account to Odoo"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_email_account
msgid "Email"
msgstr "Eメール"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid ""
"Hello,\n"
"                <br/><br/>\n"
"                You have received a payment through PayPal.<br/>\n"
"                Kindly follow the instructions given by PayPal to create your account.<br/>\n"
"                Then, help us complete your Paypal credentials in Odoo.<br/><br/>"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_acquirer_form
msgid "How to configure your paypal account?"
msgstr "PayPalアカウントをどのように設定すればよいですか？"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_seller_account
msgid "Merchant Account ID"
msgstr "販売者アカウントID"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr " txn_id (%(txn_id)s) または txn_type (%(txn_type)s)用の欠損値"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "参照に一致する取引が見つかりません%s。"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid "Notification data were not acknowledged."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDTIDトークン"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "PayPalトランザクションタイプ"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "決済サービス"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_account_payment_method
msgid "Payment Methods"
msgstr "支払方法"

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "決済トランザクション"

#. module: payment_paypal
#: model:account.payment.method,name:payment_paypal.payment_method_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_acquirer__provider__paypal
msgid "Paypal"
msgstr "PayPal"

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr "Paypalインスタント決済通知"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__provider
msgid "Provider"
msgstr "プロバイダ"

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment status: %s"
msgstr "無効な支払ステータスのデータを受信しました: %s"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"Received unrecognized authentication check response code: received %s, "
"expected VERIFIED or INVALID."
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.mail_template_paypal_invite_user_to_configure
msgid "Set Paypal credentials"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr "Paypalのアカウントを識別するためにのみ使用される公開取引先ID"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the 'Payment data transfer' option is not enabled on the PayPal "
"dashboard."
msgstr ""
"参照 %(ref)sの取引ステータスが同期されなかったのは、PayPalダッシュボードで'支払いデータ転送'オプションが有効になっていないためです。"

#. module: payment_paypal
#: code:addons/payment_paypal/controllers/main.py:0
#, python-format
msgid ""
"The status of transaction with reference %(ref)s was not synchronized "
"because the PDT Identify Token is not configured on the acquirer "
"%(acq_link)s."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_transaction__paypal_type
msgid "This has no use in Odoo except for debugging."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer__paypal_use_ipn
msgid "Use IPN"
msgstr "IPNを使用"
