# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# a0002ef8927c0b0b9c58a7cc5f73028e_ba3d803 <aeb43a6499fcef2a5a423b3f4a45b153_32479>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON>ra <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-10 08:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Original)"
msgstr "%dpx (Alkuperäinen)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Suggested)"
msgstr "%dpx (ehdotettu)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""
"'Alt-tunniste' määrittää vaihtoehtoisen tekstin kuvalle, jos kuvaa ei voida "
"näyttää (hidas yhteys, puuttuva kuva, ruudunlukuohjelma ...)."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""
"'Otsikkotunniste' näytetään työkaluvihjeenä, kun viet kuvan yläpuolelle."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(ALT-tunniste)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(TITLE-tunniste)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(URL-osoite tai upota)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr "1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr "2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr "3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr "4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">deg</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2 ml-3\">Y</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2\">X</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr "<span>Lohkot</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Style</span>"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""
"Tapahtui palvelinvirhe. Tarkista, että olet kirjautunut oikein ja että "
"tallentamasi tiedosto on oikein muotoiltu."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr "Yläpuolella"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Accepts"
msgstr "Hyväksytään"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Add"
msgstr "Lisää"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr "Lisää sarake"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "Lisää rivi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add URL"
msgstr "Lisää URL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section."
msgstr "Lisää blockquote-osio."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button."
msgstr "Lisää painike."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section."
msgstr "Lisää koodiosa."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column left"
msgstr "Lisää sarake vasemmalle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column right"
msgstr "Lisää sarake oikealle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link."
msgstr "Lisää linkki."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row above"
msgstr "Lisää rivi yläpuolelle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row below"
msgstr "Lisää rivi alapuolelle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add document"
msgstr "Lisää asiakirja"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "Airy & Zigs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "Tasaa keskelle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "Tasaa vasemmalle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "Tasaa oikealle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "Kohdistus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All"
msgstr "Kaikki"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr "Kaikki SCSS-tiedostot"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All images have been loaded"
msgstr "Kaikki kuvat on ladattu"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "Alt-tunniste"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "Kulma"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr "Animoitu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "Käytä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet: %s ?"
msgstr "Oletko varma, että haluat poistaa pätkän: %s ?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr "Haluatko varmasti poistaa tämän tiedoston?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "Kuvasuhde"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "Sisältötyökalut"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "Liite"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "Liitteen URL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Auto"
msgstr "Automaattinen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr "Automaattinen muuntaminen suhteelliseksi linkiksi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoplay"
msgstr "Automaattinen toisto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "Tausta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "Taustaväri"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "Taustakuvan sijainti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Basic blocks"
msgstr "Peruslohkot"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Basics"
msgstr "Perusasiat"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr "Alla"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading."
msgstr "Iso väliotsikko."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "Blobs"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "Ryhmä"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "Lohkot & sateinen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "Sumennus"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "Lihavoitu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr "Reunuksen väri"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr "Reunaviivan tyyli"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr "Reunuksen leveys"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "Brannan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "Kirkkaus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr "Luettelo ranskalaisilla viivoilla"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "Nappi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr "Varovasti!"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "Keskellä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr "Muuta median kuvausta ja työkaluvihjettä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr "Tarkistuslista"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr "Valitse tietue..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Close"
msgstr "Sulje"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "Koodi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "Väri"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr "Sarake"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "Yleiset värit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "Vahvista"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirmation"
msgstr "Vahvistus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content conflict"
msgstr "Sisällön ristiriita"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "Kontrasti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy Link"
msgstr "Kopioi linkki"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "Kopioi ja liitä URL-osoite tai upotuskoodi tähän"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module <strong>%s</strong>"
msgstr "Moduulia ei voitu asentaa <strong>%s</strong>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "Kansi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "Luo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering."
msgstr "Luo luettelo, jossa on numerointi."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list."
msgstr "Luo yksinkertainen luettelomuotoinen lista."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr "Luo URL-osoite."

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "Luotu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr "Rajaa kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "Mukautettu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr "Mukautettu %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr "Katkoviiva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default"
msgstr "Oletus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr "Oletus + pyöristetty"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "Määritä mukautettu kaltevuus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr "Poista %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Delete current table"
msgstr "Poista nykyinen taulukko"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "Kuvaus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "Hylkää"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "Hävitä tietue"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install the %s App?"
msgstr "Haluatko asentaa \"%s\" sovelluksen?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Document"
msgstr "Dokumentti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr "Pisteviiva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr "Tuplaviiva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr "Muokkaa kaksoisnapsauttamalla"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "Kahdenna säiliö"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "Dynaamiset värit"

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr "VIRHE: latausurlia ei saatu mediakirjastosta."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "EarlyBird"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Edit Link"
msgstr "Muokkaa linkkiä"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "Muokkaa kuvaa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr "Muokkaa median kuvausta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Sisäänrakennetun tiedoston muokkaaminen tämän editorin avulla ei ole "
"suositeltavaa, koska se estää sen päivittämisen tulevien sovelluspäivitysten"
" aikana."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr "Upota kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr "Upota Youtube-video"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr "Upota kuva asiakirjaan."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr "Upota youtube-video asiakirjaan."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr "Tyhjä lainaus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr "Odotettu "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "Jatketaan lähimpään kulmaan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "Jatketaan lähimmälle puolelle"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "Jatketaan kauimmaiseen nurkkaan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "Jatketaan kauimmaiselle puolelle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File could not be saved"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File has been uploaded"
msgstr "Tiedosto on ladattu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "Täytä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr "Täyttö + Pyöristetty"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Filter"
msgstr "Suodatin"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "Ohjauspaneelin painikkeet"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "Litteä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "Flexible"
msgstr "Joustava"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "Käännä vaakasuoraan"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "Käännä pystysuoraan"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr "Kelluvat muodot"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Floats"
msgstr "Kellukkeet"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr "Kirjasimen väri"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr "Fonttikoko"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr "Tätä lohkoa ei voida teknisistä syistä tiputtaa tähän"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "Muotoilu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "Koko ruutu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"Get the perfect image by searching in our library of copyright free photos "
"and illustrations."
msgstr ""
"Hae täydellinen kuva etsimällä tekijänoikeusvapaiden valokuvien ja "
"kuvitusten kirjastostamme."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr "Liukuväri"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr "Otsikko 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr "Otsikko 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr "Otsikko 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr "Otsikko 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr "Otsikko 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr "Otsikko 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 1"
msgstr "Väliotsikko 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 2"
msgstr "Väliotsikko 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 3"
msgstr "Väliotsikko 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 4"
msgstr "Väliotsikko 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 5"
msgstr "Väliotsikko 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 6"
msgstr "Väliotsikko 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr "Piilota Dailymotion-logo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Youtube logo"
msgstr "Piilota Youtube-logo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide fullscreen button"
msgstr "Piilota koko näytön painike"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide player controls"
msgstr "Piilota toiston ohjainpainikkeet"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide sharing button"
msgstr "Piilota jakopainike"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#, python-format
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "Kuvake"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr "Kuvakkeen muotoilu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr "Kuvakkeen koko 1x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr "Kuvakkeen koko 2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr "Kuvakkeen koko 3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr "Kuvakkeen koko 4x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr "Kuvakkeen koko 5x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Jos perut nykyiset muutokset, kaikki tallentamattomat muokkaukset katoavat. "
"Voit perua palataksesi muokkaustilaan."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""
"Jos nollaat tämän tiedoston, kaikki mukautuksesi menetetään, koska se "
"palautetaan oletustiedostoksi."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Illustrations"
msgstr "Kuvitukset"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "Kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr "Ei muotoilua"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "Kuvan lorkeus"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "Kuvan lähde"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "Kuvan leveys"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr "Kuvan sisämarginaali"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Inkwell"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr "Inline-teksti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table."
msgstr "Lisää taulukko."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video."
msgstr "Lisää video."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert an horizontal rule separator."
msgstr "Lisää vaakasuora erotin."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image."
msgstr "Lisää kuva."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr "Lisää media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr "Lisää tai muokkaa linkkiä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert table"
msgstr "Lisää taulukko"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "Asenna"

#. module: web_editor
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr "Kentän %s arvo on virheellinen: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr "Asenna %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install in progress"
msgstr "Asennus käynnissä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr "Näkymättömät elementit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr "Kohde"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr "JS"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr "JS-tiedosto: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "Suuri"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "Vasen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "Lineaarinen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Lines"
msgstr "Rivit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "Linkki"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "Linkin otsikko"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr "Linkki kopioitu leikepöydälle."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Link to"
msgstr "Linkki kohteeseen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "List"
msgstr "Lista"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Load more..."
msgstr "Lataa lisää..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Loop"
msgstr "Silmukka"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "Pääväri"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "Media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Medias"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "Media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading."
msgstr "Keskikokoinen lohkon otsikko."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr "Lisätietoja tästä sovelluksesta."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "My Images"
msgstr "Minun kuvani"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "Nimi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "Navigaatio"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "Ei"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr "URL-osoitetta ei ole määritetty"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No documents found."
msgstr "Asiakirjoja ei löytynyt."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No images found."
msgstr "Kuvia ei löytynyt."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr "Ei paikkaa, johon voi tiputtaa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr "Ei enempää tietueita"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/powerbox/Powerbox.js:0
#, python-format
msgid "No results"
msgstr "Ei tuloksia"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "Ei mitään"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "Normaali"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr "Numeroitu luettelo"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr "Vain mukautetut SCSS-tiedostot"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr "Vain sivun SCSS-tiedostot"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr "Vain näytöt"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in a new tab"
msgstr "Avaa uudessa välilehdessä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in new window"
msgstr "Avaa uudessa ikkunassa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Optimized"
msgstr "Optimoitu"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "Alkuperäinen (optimoimaton, kokoa muuttamaton) liitetiedosto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "Alkuperät"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "Ääriviivat"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr "Outline + pyöristetty"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "Sisämarginaali"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block."
msgstr "Tekstikappaleen lohko."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr "Liitä URL-osoitteena"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "Mallit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Pictogram"
msgstr "Kuvake"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Position"
msgstr "Asema"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "Esikatselu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr "Ensisijainen"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Mitä h-vettiä tuo pannukakku tekee, tabernaakkeli?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Se ei saapunut Torontoon"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Että hänen piti saapua Torontoon"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "Laatu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "Sitaatti"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "Qweb-kenttä"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb kentän kontakti"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "Qweb-kenttä Päivämäärä"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "Qweb-kenttä Datetime"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "Qweb-kentän kesto"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "Qweb-kenttä Liukuluku"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Qweb-kenttä HTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "Qweb kentän kuva"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "Qweb-kenttä Kokonaisluku"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "Qweb-kenttä Monesta yhteen"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "Qweb-kenttä Raha"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "Qweb-kenttä Suhteellinen"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "Qweb-kenttä Valinta"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "Qweb-kenttä Teksti"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "Qweb-kenttä qweb"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "Radiaalinen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr "Vain luku kenttä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr "Ohjaa käyttäjä muualle, kun hän klikkaa mediaa."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr "Poista (DELETE)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "Poista lohko"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr "Poista nykyinen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Remove Link"
msgstr "Poista linkki"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "Poista valittu väri"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current column"
msgstr "Poista nykyinen sarake"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current row"
msgstr "Poista nykyinen rivi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr "Poista muotoilu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr "Poista linkki"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr "Nimeä uudelleen %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "Toista kuvio"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "Korvaa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr "Vaihda media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "Palauta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "Nollaa kuva"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "Nollaa sato"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "Nollaa siirtymämuunnos"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "Näkymien palauttamista ei vielä tueta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr "Muuta kokonaiseksi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr "Muuta puoleen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr "Muuta neljännekseen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "Oikea"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "Pyöritä vasemmalle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "Pyöritä oikealle"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr "Rivi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr "SCSS (CSS)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr "SCSS-tiedosto: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "Saturaatio"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "Tallenna"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr "Tallenna ja asenna"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr "Tallenna ja lataa uudelleen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "Tallenna tietue"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search"
msgstr "Haku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a document"
msgstr "Etsi asiakirjaa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a pictogram"
msgstr "Hae piktogrammia"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search an image"
msgstr "Hae kuvaa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr "Etsi tietueita..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr "Etsi lisää..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr "Etsi näyttääksesi lisää tietueita"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search..."
msgstr "Hae..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr "Toissijainen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Select a Media"
msgstr "Valitse media"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr "Valitse lohko sivultasi muotoillaksesi sen."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Separator"
msgstr "Erotin"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "Punaruskea (seepia)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr "Palvelinvirhe"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr "Varjo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "Muoto"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr "Muoto: Ympyrä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr "Muoto: Pyöristetty"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr "Muoto: Pienkuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Show optimized images"
msgstr "Näytä optimoidut kuvat"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "Koko"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "Koko 1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "Koko 2x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "Koko 3x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "Koko 4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "Koko 5x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "Pieni"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading."
msgstr "Pieni väliotsikko."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr "Kiinteä"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "Kiinteät"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "Erikoisuudet"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr "Tyyli"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Suggestions"
msgstr "Ehdotukset"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr "Vaihda suunta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction."
msgstr "Vaihda tekstin suuntaa."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "Pöytä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr "Taulukkovaihtoehdot"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table tools"
msgstr "Taulukkotyökalut"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr "Mallipohjan ID: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "Teksti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr "Tekstin tasaus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr "Tekstityyli"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr "URL-osoite ei näytä toimivan."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL seems valid."
msgstr "URL-osoite vaikuttaa pätevältä."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""
"Kuvaa ei voitu poistaa, koska se on käytössä seuraavilla sivuilla tai "
"näkymillä:"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr "Annettu url ei viittaa mihinkään tuettuun videoon"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url is not valid"
msgstr "Annettu url ei ole voimassa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                    If you need to keep your changes, copy the content below and edit the new document."
msgstr ""
"Käytetään tietokannassa olevaa versiota.\n"
"                    Jos haluat säilyttää muutokset, kopioi alla oleva sisältö ja muokkaa uutta asiakirjaa."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "Teema"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "Teeman värit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr "Versiosi ja tietokannassa olevan version välillä on ristiriita."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "Tämä URL-osoite on virheellinen. Esikatselua ei voitu päivittää."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated"
msgstr "Tämä lohko on vanhentunut"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr "Tätä asiakirjaa ei ole tallennettu!"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is a public view attachment."
msgstr "Tämä tiedosto on julkinen liitetiedosto."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is attached to the current record."
msgstr "Tämä tiedosto on liitetty nykyiseen tietueeseen."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "This image is an external image"
msgstr "Tämä kuva on ulkoinen kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""
"Tämäntyyppisiä kuvia ei voi rajata.<br/>Jos haluat rajata kuvan, lataa se "
"ensin alkuperäisestä lähteestä ja lataa se Odoon."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "Otsikko"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "Otsikkotunniste"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To apply this change, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid ""
"To make changes, drop this block and use the new options in the last "
"version."
msgstr ""
"Jos haluat tehdä muutoksia, jätä tämä lohko pois ja käytä viimeisimmän "
"version uusia vaihtoehtoja."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""
"Jos haluat tallentaa pätkän, on tallennettava kaikki aiemmat muutokset ja "
"ladattava sivu uudelleen."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "Tehtävälista"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "Huomioviesti"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr "Vaihda lihavointi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr "Vaihda tarkistuslista"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr "Kuvakkeen pyöräytyksen vaihtaminen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr "Vaihda kursiivi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr "Vaihda järjestettyä luetteloa"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr "Vaihda yliviivausta"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr "Vaihda alleviivaus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr "Vaihda järjestämätön lista"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr "Työkaluvinkki"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist."
msgstr "Seuraa tehtäviä tarkistuslistan avulla."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "Muunna"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "Muunna kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr "Muunna kuva (nollaa muunnos napsauttamalla kahdesti)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "Käännä"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "Käännös"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr "Läpinäkyvät värit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "Tyyppi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr "Kirjoita \"/\" komentoja varten"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "Url tai sähköposti"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "Poista kohdistus"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr "Odottamaton"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload a document"
msgstr "Lataa asiakirja"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload an image"
msgstr "Lataa kuva"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Uploaded image's format is not supported. Try with:"
msgstr ""

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr "Ladatun kuvan muotoa ei tueta. Kokeile: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "Valencia"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr "Videon muotoilu"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video code"
msgstr "Videon koodi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr "Videot mykistetään, kun automaattinen toisto on käytössä"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "Näytä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr "Näkymät ja Assets-niput"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""
"Varoitus: Kun suljet tämän ikkunan, versio, jota työstit, hylätään, eikä se "
"ole enää käytettävissä."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "Aaltoileva"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "Web Editor Converter -alatesti"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "Web Editor Converter testi"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Width"
msgstr "Leveys"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr "XL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr "XML (HTML)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "Kyllä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""
"Voit ladata asiakirjoja näytön vasemmassa yläkulmassa olevasta painikkeesta."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr ""
"Voit ladata kuvia näytön vasemmassa yläkulmassa olevasta painikkeesta."

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr "Sinun on määritettävä joko data tai url luodaksesi liitetiedoston."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Your URL"
msgstr "URL-osoitteesi"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "Lähennä"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Loitonna"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "lisää"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "and"
msgstr "ja"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "automaattinen"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "tummenna"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "default"
msgstr "oletus"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "poissulkeminen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/mydocument"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "vaalenna"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "kerro"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "päällekkäinen"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "px"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "näyttö"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "videos"
msgstr "videot"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "www.example.com"
msgstr "www.esimerkki.com"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Border"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Color filter"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Colors"
msgstr "⌙ Värit"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Fill Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Flip"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Height"
msgstr "⌙ Korkeus"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Link Label"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Main Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Open in new window"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Position"
msgstr "⌙ Paikka"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "⌙ Shape"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Style"
msgstr "⌙ Tyyli"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Text Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Width"
msgstr "⌙ Leveys"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "⌙ Your URL"
msgstr ""
