<?xml version="1.0" encoding="utf-8"?>
<odoo>

	<record id="res_config_settings_view_form_purchase" model="ir.ui.view">
		<field name="name">res.config.settings.view.form.inherit.purchase</field>
		<field name="model">res.config.settings</field>
		<field name="priority" eval="25"/>
		<field name="inherit_id" ref="purchase.res_config_settings_view_form_purchase"/>
		<field name="arch" type="xml">
			<xpath expr="//div[@data-key='purchase']" position="inside">
				<field name="is_installed_sale" invisible="1"/>
				<h2 attrs="{'invisible': [('is_installed_sale', '=', False)]}">Logistics</h2>
				<div class="row mt16 o_settings_container" name="request_vendor_setting_container">
					<div class="col-12 col-lg-6 o_setting_box" attrs="{'invisible': [('is_installed_sale', '=', False)]}" title="This adds a dropshipping route to apply on products in order to request your vendors to deliver to your customers. A product to dropship will generate a purchase request for quotation once the sales order confirmed. This is a on-demand flow. The requested delivery address will be the customer delivery address and not your warehouse.">
						<div class="o_setting_left_pane">
							<field name="module_stock_dropshipping"/>
						</div>
						<div class="o_setting_right_pane">
							<label for="module_stock_dropshipping"/>
							<a href="https://www.odoo.com/documentation/15.0/applications/inventory_and_mrp/inventory/management/delivery/dropshipping.html" title="Documentation" class="o_doc_link" target="_blank"></a>
							<div class="text-muted">
								Request your vendors to deliver to your customers
							</div>
						</div>
					</div>
				</div>
			</xpath>
		</field>
	</record>

	<record id="res_config_settings_view_form_stock" model="ir.ui.view">
		<field name="name">res.config.settings.view.form.inherit.purchase.stock</field>
		<field name="model">res.config.settings</field>
		<field name="inherit_id" ref="stock.res_config_settings_view_form"/>
		<field name="arch" type="xml">
			<xpath expr="//h2[@id='schedule_info']" position="attributes">
				<attribute name="invisible">0</attribute>
			</xpath>
			<div id="purchase_po_lead" position="replace">
				<div class="col-12 col-lg-6 o_setting_box"
                                    title="Margin of error for vendor lead times. When the system generates Purchase Orders for reordering products,they will be scheduled that many days earlier to cope with unexpected vendor delays."
                                    name="schedule_receivings_setting_container">
					<div class="o_setting_left_pane">
						<field name="use_po_lead"/>
					</div>
					<div class="o_setting_right_pane">
						<label for="use_po_lead"/>
						<a href="https://www.odoo.com/documentation/15.0/applications/inventory_and_mrp/inventory/management/planning/scheduled_dates.html" title="Documentation" class="mr-2 o_doc_link" target="_blank"></a>
						<span class="fa fa-lg fa-building-o" title="Values set here are company-specific." groups="base.group_multi_company"/>
						<div class="text-muted">
							Schedule automatically generated request for quotations earlier to avoid delays
						</div>
						<div class="content-group">
							<div class="mt16" attrs="{'invisible': [('use_po_lead','=',False)]}">
								<span>Move forward expected request creation date by <field name="po_lead" class="oe_inline"/> days</span>
							</div>
						</div>
					</div>
				</div>
				<div class="col-12 col-lg-6 o_setting_box">
					<div class="o_setting_right_pane">
						<label for="days_to_purchase"/>
						<span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company"/>
						<div class="text-muted">
							Days needed to confirm a PO, define when a PO should be validated
						</div>
						<div class="content-group">
							<div class="mt16">
								<span><field name="days_to_purchase" class="oe_inline"/> days</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</field>
	</record>

</odoo>
