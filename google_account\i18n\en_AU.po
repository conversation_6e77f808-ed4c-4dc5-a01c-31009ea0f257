# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-01-14 10:12+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (Australia) (http://www.transifex.com/odoo/odoo-9/"
"language/en_AU/)\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_uid
msgid "Created by"
msgstr "Created by"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_date
msgid "Created on"
msgstr "Created on"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: google_account
#: code:addons/google_account/google_account.py:98
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:37
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:128
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/google_account.py:173
#, python-format
msgid "Something went wrong with your request to google"
msgstr ""

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "google.service"
msgstr ""
