# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_ldap
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_filter
msgid ""
"    Filter used to look up user accounts in the LDAP database. It is an    arbitrary LDAP filter in string representation. Any `%s` placeholder    will be replaced by the login (identifier) provided by the user, the filter    should contain at least one such placeholder.\n"
"\n"
"    The filter must result in exactly one (1) result, otherwise the login will    be considered invalid.\n"
"\n"
"    Example (actual attributes depend on LDAP server and setup):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "
msgstr ""
"    ตัวกรองที่ใช้เพื่อค้นหาบัญชีผู้ใช้ในฐานข้อมูล LDAP ซึ่งเป็นตัวกรอง LDAP โดยพลการในการแทนค่าสตริง `%s` ตัวอย่างข้อความจะถูกแทนที่ด้วยการเข้าสู่ระบบ (ตัวระบุ) ​​ที่ผู้ใช้ให้มา ตัวกรองควรมีอย่างน้อยหนึ่งตัวอย่างข้อความดังกล่าว\n"
"\n"
"    ตัวกรองต้องส่งผลเพียงหนึ่ง (1) ผลลัพธ์ มิฉะนั้น การเข้าสู่ระบบจะถือว่าไม่ถูกต้อง\n"
"\n"
"    ตัวอย่าง (แอ็ตทริบิวต์จริงขึ้นอยู่กับเซิร์ฟเวอร์ LDAP และการตั้งค่า):\n"
"\n"
"        (&(objectCategory=person)(objectClass=user)(sAMAccountName=%s))\n"
"\n"
"    or\n"
"\n"
"        (|(mail=%s)(uid=%s))\n"
"    "

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__create_user
msgid ""
"Automatically create local user accounts for new users authenticating via "
"LDAP"
msgstr ""
"สร้างบัญชีผู้ใช้ภายในเครื่องโดยอัตโนมัติสำหรับผู้ใช้ใหม่ที่ตรวจสอบสิทธิ์ผ่าน"
" LDAP"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__company
msgid "Company"
msgstr "บริษัท"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_company_ldap
msgid "Company LDAP configuration"
msgstr "การกำหนดค่า LDAP ของบริษัท"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_user
msgid "Create User"
msgstr "สร้างผู้ใช้"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_base
msgid ""
"DN of the user search scope: all descendants of this base will be searched "
"for users."
msgstr ""
"DN ของขอบเขตการค้นหาผู้ใช้: descendants ทั้งหมดของ base นี้จะถูกค้นหาผู้ใช้"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__id
msgid "ID"
msgstr "ไอดี"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_company_ldap_view_tree
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "LDAP Configuration"
msgstr "การกำหนดค่า LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company__ldaps
#: model:ir.model.fields,field_description:auth_ldap.field_res_config_settings__ldaps
msgid "LDAP Parameters"
msgstr "พารามิเตอร์ LDAP"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.res_config_settings_view_form
msgid "LDAP Server"
msgstr "เซิร์ฟเวอร์ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server
msgid "LDAP Server address"
msgstr "ที่อยู่เซิร์ฟเวอร์ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_server_port
msgid "LDAP Server port"
msgstr "พอร์ตเซิร์ฟเวอร์ LDAP"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_base
msgid "LDAP base"
msgstr "LDAP พื้นฐาน"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_binddn
msgid "LDAP binddn"
msgstr "LDAP binddn"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_filter
msgid "LDAP filter"
msgstr "LDAP ตัวกรอง"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_password
msgid "LDAP password"
msgstr "LDAP รหัส"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Login Information"
msgstr "ข้อมูลล็อกอิน"

#. module: auth_ldap
#: code:addons/auth_ldap/models/res_company_ldap.py:0
#, python-format
msgid "No local user found for LDAP login and not configured to create one"
msgstr ""
"ไม่พบผู้ใช้ท้องถิ่นในเการเข้าสู่ระบบ LDAP และไม่ได้กำหนดค่าให้สร้างขึ้น"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Process Parameter"
msgstr "พารามิเตอร์กระบวนการ"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_tls
msgid ""
"Request secure TLS/SSL encryption when connecting to the LDAP server. This "
"option requires a server with STARTTLS enabled, otherwise all authentication"
" attempts will fail."
msgstr ""
"ขอการเข้ารหัส TLS/SSL ที่ปลอดภัยเมื่อเชื่อมต่อกับเซิร์ฟเวอร์ LDAP "
"ตัวเลือกนี้ต้องใช้เซิร์ฟเวอร์ที่เปิดใช้งาน STARTTLS "
"ไม่เช่นนั้นการพยายามตรวจสอบสิทธิ์ทั้งหมดจะล้มเหลว"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "Server Information"
msgstr "ข้อมูลเซิร์ฟเวอร์"

#. module: auth_ldap
#: model:ir.actions.act_window,name:auth_ldap.action_ldap_installer
msgid "Setup your LDAP Server"
msgstr "ตั้งค่าเซิร์ฟเวอร์ LDAP ของคุณ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__user
msgid "Template User"
msgstr "เทมเพลตผู้ใช้"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_password
msgid ""
"The password of the user account on the LDAP server that is used to query "
"the directory."
msgstr "รหัสผ่านของบัญชีผู้ใช้บนเซิร์ฟเวอร์ LDAP ที่ใช้ในการสืบค้นทิศทาง"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__ldap_binddn
msgid ""
"The user account on the LDAP server that is used to query the directory. "
"Leave empty to connect anonymously."
msgstr ""
"บัญชีผู้ใช้บนเซิร์ฟเวอร์ LDAP ที่ใช้ในการสืบค้นทิศทาง "
"เว้นว่างไว้เพื่อเชื่อมต่อโดยไม่ระบุชื่อ"

#. module: auth_ldap
#: model:ir.model.fields,field_description:auth_ldap.field_res_company_ldap__ldap_tls
msgid "Use TLS"
msgstr "ใช้ TLS"

#. module: auth_ldap
#: model_terms:ir.ui.view,arch_db:auth_ldap.view_ldap_installer_form
msgid "User Information"
msgstr "ข้อมูลผู้ใช้"

#. module: auth_ldap
#: model:ir.model.fields,help:auth_ldap.field_res_company_ldap__user
msgid "User to copy when creating new users"
msgstr "ผู้ใช้ที่จะคัดลอกเมื่อสร้างผู้ใช้ใหม่"

#. module: auth_ldap
#: model:ir.model,name:auth_ldap.model_res_users
msgid "Users"
msgstr "ผู้ใช้"
