# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing_sms
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"canceled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message have been canceled and will not be "
"sent.</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"failed_text_sms\" attrs=\"{'invisible': [('mailing_type', '!=',"
" 'sms')]}\">SMS Text Message could not be sent.</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"next_departure_text\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">This SMS marketing is scheduled for </span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"scheduled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message are in queue and will be sent soon.</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"sent_sms\" attrs=\"{'invisible': [('mailing_type', '!=', "
"'sms')]}\">SMS Text Message have been sent.</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid "<span>Valid SMS Recipients</span>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears you don't have enough IAP credits. Click here to buy credits.\n"
"                        </strong>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears your SMS account is not registered. Click here to set up your account.\n"
"                        </strong>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be sent.</strong>"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This number appears to be invalid.</strong>"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "Action Needed"
msgstr "عملیات مورد نیاز است"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "BOUNCED (%i)"
msgstr ""

#. module: mass_mailing_sms
#: model:utm.tag,name:mass_mailing_sms.mailing_tag_0
msgid "Bioutifoul SMS"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid ""
"Blacklist through SMS Marketing unsubscribe (mailing ID: %s - model: %s)"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "لیست سیاه"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "تلفن لیست‌سیاه شده، موبایل است"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.phone_blacklist_menu
msgid "Blacklisted Phone Numbers"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "تلفن لیست سیاه شده، تلفن است"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "CLICKED (%i)"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Campaign"
msgstr "کمپین"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.menu_email_campaigns
msgid "Campaigns"
msgstr "کمپین‌ها"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Cancel"
msgstr "لغو"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Carriage-return-separated list of phone numbers"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_code
msgid "Code"
msgstr "کد"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid ""
"Come back once some SMS Mailings are sent to check out aggregated results."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_configuration
msgid "Configuration"
msgstr "پیکربندی"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid "Create a Mailing List"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid "Create a SMS Marketing Mailing"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid "Create a mailing contact"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_uid
msgid "Created by"
msgstr "ایجادشده توسط"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_date
msgid "Created on"
msgstr "ایجاد شده در"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__display_name
msgid "Display Name"
msgstr "نام نمایشی"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_duplicate
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Duplicate"
msgstr "تکثیر کردن"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#, python-format
msgid "Email Marketing"
msgstr "بازاریابی ایمیل"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Exclude Blacklisted Phone"
msgstr ""

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_1
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_1
#: model:utm.source,name:mass_mailing_sms.mailing_sms_1_utm_source
msgid "Extra Promo"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "نوع شکست"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"فیلد مورد استفاده برای نگهداری شماره تلفن مدنظر. به بالا بردن جستجوها و "
"مقایسه‌ها کمک می‌کند."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "دنبال‌کنندگان"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr "دنبال کنندگان (طرف حساب ها)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"For an Email, Subject your Recipients will see in their inbox.\n"
"                    For an SMS Text Message, internal Title of the Message."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_subject
msgid ""
"For an email, the subject your recipients will see in their inbox.\n"
"For an SMS, the internal title of the message."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__has_message
msgid "Has Message"
msgstr "دارای پیام"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__id
msgid "ID"
msgstr "شناسه"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid ""
"ID of the related sms.sms. This field is an integer field because the "
"related sms.sms can be deleted separately from its statistics. However the "
"ID is needed for several action and controllers."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread
msgid "If checked, new messages require your attention."
msgstr "در صورت علامت‌گذاری، پیام‌های جدید نیاز به توجه شما دارند."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "در صورت بررسی ، برخی پیام ها خطای تحویل دارند."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"اگر شماره تلفن مدنظر در لیست سیاه باشد، مخاطب‌ دیگر پیامک‌های پستی انبوه را "
"از هیچ لیستی، دریافت نخواهد کرد"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid ""
"Immediately send the SMS Mailing instead of queuing up. Use at your own "
"risk."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_allow_unsubscribe
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mass_sms_allow_unsubscribe
msgid "Include opt-out link"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"نشان می دهد که آیا شماره تلفن ثبت شده در لیست سیاه، یک شماره تلفن همراه است."
" هنگامی که در یک مدل هم فیلد تلفن همراه و هم تلفن وجود دارد، می توان تشخیص "
"داد که کدام شماره در لیست سیاه قرار می گیرد."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"نشان می دهد که آیا شماره تلفن ثبت شده در لیست سیاه، یک شماره تلفن همراه است."
" هنگامی که در یک مدل هم فیلد تلفن همراه و هم تلفن وجود دارد، می توان تشخیص "
"داد که کدام شماره در لیست سیاه قرار می گیرد."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "اعتبار ناکافی"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "Insufficient IAP credits"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Insufficient credits"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr "دنبال می کند"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test____last_update
msgid "Last Modified on"
msgstr "آخرین تغییر در"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_uid
msgid "Last Updated by"
msgstr "آخرین به‌روز‌رسانی توسط"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_date
msgid "Last Updated on"
msgstr "آخرین به روز رسانی در"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.link_tracker_menu
msgid "Link Tracker"
msgstr "پیگیری لینک"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__mailing_id
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Mailing"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_contact
msgid "Mailing Contact"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_list
msgid "Mailing List"
msgstr "لیست ایمیل"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_contact_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_contact_menu_sms
msgid "Mailing List Contacts"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_list_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_list_menu_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_contacts
msgid "Mailing Lists"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"contact directory."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_main_attachment_id
msgid "Main Attachment"
msgstr "پیوست اصلی"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__manual
msgid "Manual"
msgstr "دستی"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Marketing"
msgstr "بازاریابی"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_id
msgid "Mass Mailing"
msgstr "ایمیل دسته‌جمعی"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_ids
msgid "Mass SMS"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_ids
msgid "Messages"
msgstr "پیام‌ها"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_missing
msgid "Missing Number"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile
msgid "Mobile"
msgstr "تلفن همراه"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_search_sms
msgid "My SMS Marketing"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid "No data yet!"
msgstr "هنوز اطلاعاتی وجود ندارد!"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid ""
"No need to import mailing lists, you can send SMS Text Messages to contacts "
"saved in other Odoo apps."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_number
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Number"
msgstr "شماره"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid "Number %s not found"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد عملیات"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_count
msgid "Number of Mass SMS"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "تعداد پیام ها که نیاز به اقدام دارد"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیام‌های با خطای تحویل"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Number of unread messages"
msgstr "تعداد پیام‌های خوانده نشده"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Number(s)"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "Open Recipient"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_optout
msgid "Opted Out"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_sms
msgid "Outgoing SMS"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "تلفن در لیست سیاه"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_mobile_search
msgid "Phone/Mobile"
msgstr "تلفن/ موبایل"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Please enter your phone number"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Recipients"
msgstr "گیرندگان"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Report for %(expected)i %(mailing_type)s Sent"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_reporting
msgid "Reporting"
msgstr "گزارش گیری"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_mailing__mailing_type__sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__trace_type__sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_kanban
msgid "SMS"
msgstr "پیامک"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__body_plaintext
msgid "SMS Body"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_list__contact_count_sms
msgid "SMS Contacts"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "SMS Content"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS ID"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid "SMS ID (tech)"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_mailing_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_mass_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#, python-format
msgid "SMS Marketing"
msgstr "بازاریابی پیامکی"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_trace_report_action_sms
msgid "SMS Marketing Analysis"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "SMS Subscription"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_template_id
msgid "SMS Template"
msgstr "قالب پیامک"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "SMS Text Message"
msgstr "پیام متنی"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS Trace"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "SMS Traces"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_sms_winner_selection
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_sms_winner_selection
msgid "SMS Winner Selection"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/sms_composer.py:0
#, python-format
msgid "STOP SMS : %s"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Scheduled"
msgstr "برنامه ریزی شد"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send"
msgstr "ارسال"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid "Send Directly"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send Now"
msgstr "هم اکنون ارسال شود"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Send SMS"
msgstr "ارسال اس ام اس"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a Sample SMS"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"Send a sample SMS for testing purpose to the numbers below (carriage-return-"
"separated list)."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_server
msgid "Server Error"
msgstr "خطای سرور"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_trace_ids
msgid "Statistics"
msgstr "آمار"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Status"
msgstr "وضعیت"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_sms_test
msgid "Test SMS Mailing"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_sms_test_action
msgid "Test SMS Marketing"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS could not be sent to %s:<br>%s"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Test SMS marketing"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS successfully sent to %s"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "The following numbers are not correctly encoded: %s"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "There was an error when trying to unsubscribe"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"این شماره تلفن برای بازاریابی پیامکی در لیست سیاه قرار گرفته است. برای لغو "
"لیست سیاه کلیک کنید."

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"This will send SMS to all recipients now. Do you still want to proceed ?"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "This will send SMS to all recipients. Do you still want to proceed ?"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_subject
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Title"
msgstr "عنوان"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__trace_type
msgid "Type"
msgstr "نوع"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_campaign
msgid "UTM Campaign"
msgstr "کمپین UTM"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "UX Field to propose to Register the SMS IAP account"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "UX Field to propose to buy IAP credits"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread
msgid "Unread Messages"
msgstr "پیام‌های خوانده‌‌نشده"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Unread Messages Counter"
msgstr "شمارنده پیام‌های خوانده‌ نشده"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_acc
msgid "Unregistered Account"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "Unregistered IAP account"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Unregistered account"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Unsubscribe me"
msgstr ""

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Unsupported %s for mass SMS"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_res_users
msgid "Users"
msgstr "کاربران"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Valid SMS Recipients"
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr "پیام‌های وب‌سایت"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباطات با وبسایت"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Winner Selection"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid ""
"Write an appealing SMS Text Message, define recipients and track its "
"results."
msgstr ""

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr ""

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_0
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_0
#: model:utm.campaign,name:mass_mailing_sms.utm_campaign_0
#: model:utm.source,name:mass_mailing_sms.mailing_sms_0_utm_source
msgid "XMas Promo"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "e.g. Black Friday SMS coupon"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully blacklisted"
msgstr ""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully removed from"
msgstr ""
