<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="escompte_template" model="account.reconcile.model.template">
        <field name="chart_template_id" ref="l10nbe_chart_template"/>
        <field name="name">Escompte</field>
    </record>
    <record id="escompte_line_template" model="account.reconcile.model.line.template">
        <field name="model_id" ref="l10n_be.escompte_template"/>
        <field name="account_id" ref="a653"/>
        <field name="amount_type">percentage</field>
        <field name="amount_string">100</field>
        <field name="label">Escompte accordé</field>
    </record>
    <record id="frais_bancaires_htva_template" model="account.reconcile.model.template">
        <field name="chart_template_id" ref="l10nbe_chart_template"/>
        <field name="name">Frais bancaires HTVA</field>
    </record>
    <record id="frais_bancaires_htva_line_template" model="account.reconcile.model.line.template">
        <field name="model_id" ref="l10n_be.frais_bancaires_htva_template"/>
        <field name="account_id" ref="a6560" />
        <field name="amount_type">percentage</field>
        <field name="amount_string">100</field>
        <field name="label">Frais bancaires HTVA</field>
    </record>
    <record id="frais_bancaires_tva21_template" model="account.reconcile.model.template">
        <field name="chart_template_id" ref="l10nbe_chart_template"/>
        <field name="name">Frais bancaires TVA21</field>
    </record>
    <record id="frais_bancaires_tva21_line_template" model="account.reconcile.model.line.template">
        <field name="model_id" ref="l10n_be.frais_bancaires_tva21_template"/>
        <field name="account_id" ref="a6560"/>
        <field name="amount_type">percentage</field>
        <field name="tax_ids" eval="[(6, 0, [ref('l10n_be.attn_TVA-21-inclus-dans-prix')])]"/>
        <field name="amount_string">100</field>
        <field name="label">Frais bancaires TVA21</field>
    </record>
    <record id="virements_internes_template" model="account.reconcile.model.template">
        <field name="chart_template_id" ref="l10nbe_chart_template"/>
        <field name="name">Virements internes</field>
        <field name="to_check" eval="False"/>
    </record>
    <record id="virements_internes_line_template" model="account.reconcile.model.line.template">
        <field name="model_id" ref="l10n_be.virements_internes_template"/>
        <field name="account_id" search="[('code', '=like', obj().env.ref('l10n_be.l10nbe_chart_template').transfer_account_code_prefix + '%'), ('chart_template_id', '=', obj().env.ref('l10n_be.l10nbe_chart_template').id)]"/>
        <field name="amount_type">percentage</field>
        <field name="amount_string">100</field>
        <field name="label">Virements internes</field>
    </record>
    <record id="compte_attente_template" model="account.reconcile.model.template">
        <field name="chart_template_id" ref="l10nbe_chart_template"/>
        <field name="name">Compte Attente</field>
        <field name="to_check" eval="True"/>
    </record>
    <record id="compte_attente_line_template" model="account.reconcile.model.line.template">
        <field name="model_id" ref="l10n_be.compte_attente_template"/>
        <field name="account_id" ref="a499"/>
        <field name="amount_type">percentage</field>
        <field name="amount_string">100</field>
        <field name="label"></field>
    </record>
</odoo>
