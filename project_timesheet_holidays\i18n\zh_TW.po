# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_holidays
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析項目"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave__timesheet_ids
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_resource_calendar_leaves__timesheet_ids
msgid "Analytic Lines"
msgstr "分析項目"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#, python-format
msgid ""
"Both the internal project and task are required to generate a timesheet for "
"the time off %s. If you don't want a timesheet, you should leave the "
"internal project and task empty."
msgstr "內部專案和任務都需要為 %s休假生成工時表。如果您不需要工時表，則應將內部專案和任務留空。"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "公司"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr "從休假類型生成的時程表的預設專案值。"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_employee
msgid "Employee"
msgstr "員工"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid "Generate Timesheet"
msgstr "生成時間表"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__global_leave_id
msgid "Global Time Off"
msgstr "公眾休假"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_generate
msgid ""
"If checked, when validating a time off, timesheet will be generated in the "
"Vacation Project of the company."
msgstr "如果選中，在驗證休假時，將在公司的休假專案中生成時程表。"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#, python-format
msgid "Internal"
msgstr "內部"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__internal_project_id
msgid "Internal Project"
msgstr "內部專案"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line__holiday_id
msgid "Leave Request"
msgstr "休假申請"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "專案"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "資源休假詳細資訊"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_leave_type__timesheet_task_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "任務"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_leave_type__timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a time off is "
"validated."
msgstr "專案將包含驗證休假時生成的工時表。"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: code:addons/project_timesheet_holidays/models/res_company.py:0
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave
#, python-format
msgid "Time Off"
msgstr "休假"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:0
#: code:addons/project_timesheet_holidays/models/resource_calendar_leaves.py:0
#, python-format
msgid "Time Off (%s/%s)"
msgstr "休假 (%s/%s)"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company__leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings__leave_timesheet_task_id
msgid "Time Off Task"
msgstr "休假任務"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_leave_type
msgid "Time Off Type"
msgstr "休假類型"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheets"
msgstr "工時表"

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:0
#, python-format
msgid ""
"You cannot delete timesheets linked to time off. Please, cancel the time off"
" instead."
msgstr "您不能刪除連接到休假的時間表，請取消休假。"
