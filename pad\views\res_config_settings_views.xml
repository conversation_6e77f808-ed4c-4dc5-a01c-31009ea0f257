<?xml version="1.0" encoding="utf-8"?>     
<odoo>        
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.pad</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div id="msg_module_pad" position="replace">
                <div class="content-group" id="pad_configuration_settings" attrs="{'invisible': [('module_pad', '=', False)]}">
                    <div class="mt16 row">
                        <label for="pad_server" string="Server" class="col-3 col-lg-3 o_light_label"/>
                        <field name="pad_server" placeholder="e.g. beta.primarypad.com" attrs="{'required': [('module_pad', '!=', False)]}"/>
                        <label for="pad_key" string="API Key" class="col-3 col-lg-3 o_light_label"/>
                        <field name="pad_key" attrs="{'required': [('module_pad', '!=', False)]}"/>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
