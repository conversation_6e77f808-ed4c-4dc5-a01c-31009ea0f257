# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_timesheet
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-03-31 15:42+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Albanian (http://www.transifex.com/odoo/odoo-9/language/sq/)\n"
"Language: sq\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_company
msgid "Companies"
msgstr "Kompanitë"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_hr_employee
msgid "Employee"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Shëmbull i Produktit"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Project"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_project_project_id
msgid "Project associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.project_time_mode_id_duplicate_xmlid
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right "
"unit of measure in your employees."
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_hr_employee_timesheet_cost
msgid "Timesheet Cost"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.project_time_mode_id_duplicate_xmlid
msgid "Timesheet UoM"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_count
msgid "Timesheet activities"
msgstr ""

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr ""

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.hr_timesheet_employee_extd_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "Timesheets"
msgstr ""

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_timesheet.py:128
#, python-format
msgid ""
"You can use only one product on timesheet within the same sale order. You "
"should split your order to include only one contract based on time and "
"material."
msgstr ""

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "account analytic line"
msgstr ""
