# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project_timesheet_holidays
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analiticki red"

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_holidays_timesheet_ids
msgid "Analytic Lines"
msgstr "Analitički redovi"

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_company_leave_timesheet_project_id
#: model:ir.model.fields,help:project_timesheet_holidays.field_res_config_settings_leave_timesheet_project_id
msgid "Default project value for timesheet generated from leave type."
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/hr_holidays.py:30
#, python-format
msgid ""
"For the leaves to generate timesheet, the internal project and task are "
"requried."
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_holidays_status_timesheet_generate
msgid "Generate Timesheet"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_holidays_status_timesheet_generate
msgid ""
"If checked, when validating a leave, timesheet will be generated in the "
"Vacation Project of the company."
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:30
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_holidays_status_timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company_leave_timesheet_project_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings_leave_timesheet_project_id
#, python-format
msgid "Internal Project"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_hr_holidays_status_timesheet_task_id
msgid "Internal Task for timesheet"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_holidays
msgid "Leave"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_account_analytic_line_holiday_id
msgid "Leave Request"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_company_leave_timesheet_task_id
#: model:ir.model.fields,field_description:project_timesheet_holidays.field_res_config_settings_leave_timesheet_task_id
msgid "Leave Task"
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_hr_holidays_status
msgid "Leave Type"
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/res_company.py:40
#, python-format
msgid "Leaves"
msgstr ""

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Project"
msgstr "Projekat"

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.res_config_settings_view_form
msgid "Task"
msgstr "Zadatak"

#. module: project_timesheet_holidays
#: model:ir.model.fields,help:project_timesheet_holidays.field_hr_holidays_status_timesheet_project_id
msgid ""
"The project will contain the timesheet generated when a leave is validated."
msgstr ""

#. module: project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:project_timesheet_holidays.hr_holiday_status_view_form_inherit
msgid "Timesheet"
msgstr ""

#. module: project_timesheet_holidays
#: code:addons/project_timesheet_holidays/models/account_analytic.py:16
#, python-format
msgid ""
"You cannot delete timesheet lines attached to a leaves. Please cancel the "
"leaves instead."
msgstr ""

#. module: project_timesheet_holidays
#: model:ir.model,name:project_timesheet_holidays.model_res_config_settings
msgid "res.config.settings"
msgstr ""
