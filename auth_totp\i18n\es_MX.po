# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "%(browser)s en %(platform)s"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr "Asistente de la configuración de la autenticación de dos factores"

#. module: auth_totp
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "2-Factor authentication is now enabled."
msgstr "Se habilitó la autenticación de dos factores."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Your account is protected!</span>"
msgstr ""
"<span attrs=\"{'invisible': [('totp_enabled', '=', False)]}\" class=\"text-"
"muted\">Su cuenta está protegida</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">O instale una aplicación de autenticación</span>\n"
"                                        <span class=\"d-none d-md-block\">Instale una aplicación de autenticación en su dispositivo móvil</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""
"<span class=\"d-none d-md-block\">Cuando se le pida hacerlo, escanee el código de barras de abajo</span>\n"
"                                    <span class=\"d-block d-md-none\">Cuando se le pida, copie la clave de abajo</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""
"<span class=\"text-muted\">Las aplicaciones populares incluyen Authy, Google"
" Authenticator o Microsoft Authenticator.</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Account Security"
msgstr "Seguridad de la cuenta"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "Activar"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "Agregado en"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? Two-factor authentication will be required again on all your "
"devices"
msgstr ""
"¿Está seguro? La autenticación de dos factores volverá a ser obligatoria en "
"todos sus dispositivos"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "Código de autenticación"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "Dispositivo de autenticación"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "Configuración de la aplicación de autenticación"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "¿No lo puede escanear?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "Haga clic en este enlace para abrir la aplicación de autenticación "

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Creado el"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "Descripción"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device Name"
msgstr "Nombre del dispositivo"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "Deshabilitar la A2F"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "Deshabilitar la autenticación de dos factores"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "No volver a preguntar en este dispositivo"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "Habilitar la A2F"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "Ingrese su código de 6 dígitos abajo"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "ID"

#. module: auth_totp
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "Formato incorrecto del código de autenticación."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device____last_update
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "Más información"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Login"
msgstr "Iniciar sesión"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr "Busque el botón \"agregar una cuenta\""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "En Apple Store"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "En Google Play"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr "Código QR"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "Revocar"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr "Revocar todo"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "Alcance"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "Contraseña"

#. module: auth_totp
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "El código de autenticación solo debe contener números"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"Para iniciar sesión, ingrese el código de autenticación de seis dígitos que aparece en la aplicación de autentificación.\n"
"<br/>"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr "Contraseña TOTP"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Device"
msgstr "Dispositivo de confianza"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "Dispositivos de confianza"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-Factor Authentication Activation"
msgstr "Activación de la autenticación de dos factores"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "Autenticación de dos pasos"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"La autenticación de dos factores (A2F) es un sistema de autenticación doble.\n"
"La primera autentificación se hace con su contraseña, la segunda con un código que obtendrá de una aplicación específica para celulares.\n"
"Entre las aplicaciones populares se encuentran Authy, Google Authenticator o Microsoft Authenticator."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                            The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                            Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"La autenticación de dos factores (A2F) es un sistema de autenticación doble.\n"
"La primera autentificación se hace con su contraseña, la segunda con un código que obtendrá de una aplicación específica para celulares.\n"
"Entre las aplicaciones populares se encuentran Authy, Google Authenticator o Microsoft Authenticator."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "Autenticación de dos factores"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "Se deshabilitó la autenticación de dos factores"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "Se habilitó la autenticación de dos factores"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "La autenticación de dos factores ya está habilitada"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "Solo usted puede activar la autenticación de dos factores"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr ""
"Se deshabilitó la autenticación de dos factores para los siguientes "
"usuarios: %s"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "URL"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Usuario"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Código de verificación"

#. module: auth_totp
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "La verificación falló, revise el código de 6 dígitos"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "Por ejemplo, 123456"
