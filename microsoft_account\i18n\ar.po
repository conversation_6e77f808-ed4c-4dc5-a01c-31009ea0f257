# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""
"الطريقة غير مدعومة [%s] ليست واحدة من [GET, POST, PUT, PATCH or DELETE]! "

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr "رمز تحديث Microsoft "

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr "خدمة Microsoft "

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr "صلاحية رمز Microsoft "

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr "رمز مستخدم Microsoft "

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""
"حدث خطأ أثناء إنشاء كلمة الرمز الخاص بك. قد يكون رمز التفويض غير صالح "

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""
"حدث خطأ ما خلال إنشاء الرمز الخاص بك. قد يكون رمز التفويض غير صالح أو نتهت "
"مدة صلاحيته بالفعل "

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "Users"
msgstr "المستخدمين "
