<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Invite to user 2FA -->
    <record model="ir.actions.server" id="action_invite_totp">
        <field name="name">Invite to use two-factor authentication</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="binding_model_id" ref="base.model_res_users"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">
            action = records.action_totp_invite()
        </field>
        <field name="groups_id" eval="[(4, ref('base.group_erp_manager'))]"/>
    </record>

    <!--Action called when using the link in "Invite to use 2FA" mail-->
    <record model="ir.actions.server" id="action_activate_two_factor_authentication">
        <field name="name">Open two-factor authentication configuration</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="state">code</field>
        <field name="code">
user = env.user
action = user.action_open_my_account_settings()
        </field>
        <field name="groups_id" eval="[(4, ref('base.group_user'))]"/>
    </record>
</odoo>
