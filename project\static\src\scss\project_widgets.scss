.o_status_bubble {
    @extend .o_status;

    @for $size from 2 through length($o-colors) {
        // Note: the first color is supposed to be invisible so it's ignored
        &.o_color_bubble_#{$size - 1} {
            background-color: nth($o-colors, $size);
        }
    }

    &.o_color_bubble_20 {
        background-color: $o-success;
    }
    &.o_color_bubble_21 {
        background-color: $o-info;
    }
    &.o_color_bubble_22 {
        background-color: $o-warning;
    }
    &.o_color_bubble_23 {
        background-color: $o-danger;
    }
}

.o_status_with_color {
    span {
        vertical-align: middle;
    }
    &.o_field_widget {
        span {
            display: inline-block;
        }
    }
}

.o_project_update_description a[type="object"] {
    cursor: pointer;
}

.o_kanban_view.o_pupdate_kanban {
    overflow: auto;
    &.o_kanban_ungrouped {
        padding:0px;
        .o_pupdate_kanban_card {
            width: 100%;
            margin: 0px;
            border-right: 0px;
            border-top: 0px;
            overflow: hidden;
            .o_kanban_detail_ungrouped {
                padding-left: $o-horizontal-padding - $o-kanban-color-border-width - $o-kanban-record-margin;
                > div {
                    display: grid;
                    align-items: center;
                }
                .o_pupdate_kanban_image {
                    width: 56px;
                    height: 56px;
                    top: 0px;
                    position: absolute;
                }
                .o_pupdate_name {
                    overflow-wrap: break-word;
                }
                .o_pupdate_kanban_actions_ungrouped {
                    button {
                        float: right;
                        margin: 4px;
                    }
                }
                .o_country_flag {
                    margin-right: 8px;
                }
            }
        }
    }

    .oe_kanban_color_20 {
        border-left-color: $o-success;
        &:after {
            background-color: $o-success;
        }
    }
    .oe_kanban_color_21 {
        border-left-color: $o-info;
        &:after {
            background-color: $o-info;
        }
    }
    .oe_kanban_color_22 {
        border-left-color: $o-warning;
        &:after {
            background-color: $o-warning;
        }
    }
    .oe_kanban_color_23 {
        border-left-color: $o-danger;
        &:after {
            background-color: $o-danger;
        }
    }
}

.o_kanban_project_tasks .o_field_many2manytags, .o_kanban_tags{
    margin: 0px;
}

.o_project_m2m_avatar {
    width: 20px;
    height: 20px;
    margin-right: -1px;
    display: inline-block;

    img {
        border-radius: 50%;
        width: 20px;
        height: 20px;
        object-fit: cover;
        margin-right: 4px;
    }
}
