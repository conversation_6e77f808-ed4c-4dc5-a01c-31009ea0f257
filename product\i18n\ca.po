# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product
# 
# Translators:
# <PERSON> i Bochaca <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<PERSON><PERSON><PERSON>@hotmail.com>, 2021
# <PERSON><PERSON> Consulting <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# Harcogourmet, 2022
# Jonatan Gk, 2022
# marc<PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# eriiikgt, 2022
# jabel<PERSON>, 2022
# <PERSON>, 2022
# martioodo hola, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: martioodo hola, 2023\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_count
msgid "# Product Variants"
msgstr "# Variants de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__product_count
msgid "# Products"
msgstr "# Productes"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"%(base)s with a %(discount)s %% discount and %(surcharge)s extra fee\n"
"Example: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"
msgstr ""
"%(base)s amb un %(discount)s %% descompte i %(surcharge)s tarifa extra\n"
"Exemple: %(amount)s * %(discount_charge)s + %(price_surcharge)s → %(total_amount)s"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "%(percentage)s %% discount and %(price)s surcharge"
msgstr "%(percentage)s %% descompte i %(price)s recàrrec"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "%s %% discount"
msgstr "%s %% descompte"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (còpia)"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "%s : end date (%s) should be greater than start date (%s)"
msgstr ""
"%s: la data final (%s) hauria de ser més gran que la data d'inici (%s)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_template_label
#: model:ir.actions.report,print_report_name:product.report_product_template_label_dymo
msgid "'Products Labels - %s' % (object.name)"
msgstr "'Etiquetes de producte - %s' % (object.name)"

#. module: product
#: model:ir.actions.report,print_report_name:product.report_product_packaging
msgid "'Products packaging - %s' % (object.name)"
msgstr "'Embalatge de productes - %s' % (object.name)"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__create_variant
msgid ""
"- Instantly: All possible variants are created as soon as the attribute and its values are added to a product.\n"
"        - Dynamically: Each variant is created only when its corresponding attributes and values are added to a sales order.\n"
"        - Never: Variants are never created for the attribute.\n"
"        Note: the variants creation mode cannot be changed once the attribute is used on at least one product."
msgstr ""
"- Instantàniament: totes les variants possibles es creen tan aviat com l' atribut i els seus valors s' afegeixin a un producte.\n"
"        - Dinàmicament: cada variant es crea només quan s' afegeixen els seus atributs i valors corresponents a un ordre de vendes.\n"
"        - Mai: els vinats mai no es creen per a l' atribut.\n"
"        Nota: el mode de creació de variants no es pot canviar una vegada que l' atribut s' usi almenys un producte."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "1 year"
msgstr "1 any"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4_product_template
msgid "160x80cm, with large legs."
msgstr "160x80cm, amb cames grans."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7 amb preu"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_6
msgid "2 year"
msgstr "2 anys"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12 amb preu"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7 amb preu"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2 oe_edit_only\" aria-label=\"Arrow "
"icon\" title=\"Arrow\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_kanban
msgid "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"
msgstr "<i class=\"fa fa-money\" role=\"img\" aria-label=\"Currency\" title=\"Currency\"/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid ""
"<span attrs=\"{'invisible': [('pricelist_item_count', '=', 1)]}\">\n"
"                                        Extra Prices\n"
"                                    </span>\n"
"                                    <span attrs=\"{'invisible': [('pricelist_item_count', '!=', 1)]}\">\n"
"                                        Extra Price\n"
"                                    </span>"
msgstr ""
"<span attrs=\"{'invisible': [('pricelist_item_count', '=', 1)]}\">\n"
"                                        Preus addicionals\n"
"                                    </span>\n"
"                                    <span attrs=\"{'invisible': [('pricelist_item_count', '!=', 1)]}\">\n"
"                                        Preu addicional\n"
"                                    </span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr "<span class=\"o_stat_text\"> Productes</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "<span class=\"o_stat_text\">Related Products</span>"
msgstr "<span class=\"o_stat_text\">Productes relacionats</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>All general settings about this product are managed on</span>"
msgstr "<span>Tota la configuració d'aquest producte es gestiona a</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_packagingbarcode
msgid "<strong>Qty: </strong>"
msgstr "<strong>Quantitat: </strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "<strong>Sales Order Line Quantities (price per unit)</strong>"
msgstr ""
"<strong>Quantitats de línia de comanda de vendes (preu per unitat)</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back\n"
"                                    here to set up the feature."
msgstr ""
"<strong>GuardaS</strong> aquesta pàgina i torna\n"
"                                    aquí per configurar la funció."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""
"<strong>Advertència</strong>: afegir o eliminar atributs esborrarà i "
"recrearà les variants existents i portarà a la pèrdua de les seves possibles"
" personalitzacions."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_product_barcode_uniq
msgid "A barcode can only be assigned to one product !"
msgstr "Un codi de barres només pot ser assignat a un producte!"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__description_sale
#: model:ir.model.fields,help:product.field_product_template__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"Una descripció del producte que es vol comunicar als seus clients. Aquesta "
"descripció serà copiada a cada comanda de venda, albarà de sortida i factura"
" de client."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price is a set of sales prices or rules to compute the price of sales order lines based on products, product categories, dates and ordered quantities.\n"
"                This is the perfect tool to handle several pricings, seasonal discounts, etc."
msgstr ""
"Un preu és un conjunt de preus o regles de venda per calcular el preu de les línies de comandes de vendes en funció de productes, categories de productes, dates i quantitats ordenades.\n"
"                Aquesta és l'eina perfecta per gestionar diversos preus, descomptes estacionals, etc."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__detailed_type
#: model:ir.model.fields,help:product.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Un producte emmagatzemable és un producte pel qual gestioneu estoc. Cal instal·lar l'aplicació Inventari.\n"
"Un consumible és un producte pel qual no es gestiona estoc.\n"
"Un servei és un producte no material que subministreu. "

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Acoustic Bloc Screens"
msgstr "Screens Bloc acústic"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction
msgid "Action Needed"
msgstr "Cal fer alguna acció"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__active
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__active
#: model:ir.model.fields,field_description:product.field_product_product__active
#: model:ir.model.fields,field_description:product.field_product_template__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__active
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_active
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Active"
msgstr "Actiu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_ids
#: model:ir.model.fields,field_description:product.field_product_template__activity_ids
msgid "Activities"
msgstr "Activitats"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoració de l'activitat d'excepció"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_state
#: model:ir.model.fields,field_description:product.field_product_template__activity_state
msgid "Activity State"
msgstr "Estat de l'activitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icona de tipus d'activitat"

#. module: product
#. openerp-web
#: code:addons/product/static/src/xml/pricelist_report.xml:0
#, python-format
msgid "Add a quantity"
msgstr "Afegeix una quantitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_sale_pricelist
#: model:res.groups,name:product.group_sale_pricelist
msgid "Advanced Pricelists"
msgstr "Tarifes avançades"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__advanced
msgid "Advanced price rules (discounts, formulas)"
msgstr "Regles avançades de preu (dis counts, fórmules)"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__3_global
#, python-format
msgid "All Products"
msgstr "Tots els Productes"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:product.field_product_template_attribute_value__is_custom
msgid "Allow users to input custom values for this attribute value"
msgstr ""
"Permet als usuaris introduir valors personalitzats per aquest valor "
"d'atribut"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""
"Permet gestionar diferents preus basats en regles per categories de clients.\n"
"Per exemple: 10% pels minoristes, promoció de 5€ en aquest producte, etc."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
#: model:product.template.attribute.value,name:product.product_11_attribute_1_value_2
#: model:product.template.attribute.value,name:product.product_4_attribute_1_value_2
msgid "Aluminium"
msgstr "Alumini"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
msgid "Applicable On"
msgstr "Aplicable en"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
msgid "Applied On"
msgstr "Aplicat en"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__applied_on
msgid "Apply On"
msgstr "Aplicar en"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Archived"
msgstr "Arxivat"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__sequence
msgid "Assigns the priority to the list of product vendor."
msgstr "Assigna la prioritat a la llista de productes del proveïdor"

#. module: product
#. openerp-web
#: code:addons/product/static/src/js/product_pricelist_report.js:0
#, python-format
msgid ""
"At most %d quantities can be displayed simultaneously. Remove a selected "
"quantity to add others."
msgstr ""
"A la majoria de les quantitats %d es poden mostrar simultàniament. Elimina "
"una quantitat seleccionada per afegir altres."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_attachment_count
#: model:ir.model.fields,field_description:product.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre d'adjunts"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__attribute_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_id
msgid "Attribute"
msgstr "Atribut"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__attribute_line_id
msgid "Attribute Line"
msgstr "Línia d' atributs"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Attribute Name"
msgstr "Nom de l'atribut"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_template_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_attribute_value_id
msgid "Attribute Value"
msgstr "Valor de l'atribut"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Valors de atribut"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Attributes"
msgstr "Atributs"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Attributes & Variants"
msgstr "Atributs i variants"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Availability"
msgstr "Disponibilitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__barcode
#: model:ir.model.fields,field_description:product.field_product_product__barcode
#: model:ir.model.fields,field_description:product.field_product_template__barcode
msgid "Barcode"
msgstr "Codi de barres"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__barcode
msgid ""
"Barcode used for packaging identification. Scan this packaging barcode from "
"a transfer in the Barcode app to move all the contained units"
msgstr ""
"Codi de barres usat per a la identificació de paquets. Escaneja aquest codi "
"de paquets des d' una transferència a l' aplicació de codi de barres de "
"barres per moure totes les unitats contingudes"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__base
msgid ""
"Base price for computation.\n"
"Sales Price: The base price will be the Sales Price.\n"
"Cost Price : The base price will be the cost price.\n"
"Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""
"El preu base per al càlcul.\n"
"El preu de la base serà el Preu de vendes.\n"
"Cost Price: El preu base serà el preu cost.\n"
"Altres cotitzacions: Computació del preu base basat en una altra llista de preus."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base
msgid "Based on"
msgstr "Basat en"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Basic Pricelists"
msgstr "Llistes de cotitzacions bàsiques"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
#: model:product.template.attribute.value,name:product.product_4_attribute_2_value_2
msgid "Black"
msgstr "Negre"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Cabinet with Doors"
msgstr "A l'habitació amb porta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:product.field_product_template__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Pot la imatge 1024 ser ampliada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr "Imatge Canàriant 1024 s' ha fet zoom"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template__purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "Pot ser comprat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__sale_ok
#: model:ir.model.fields,field_description:product.field_product_template__sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Pot ser venut"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category"
msgstr "Categoria"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Category: %s"
msgstr "Categoria: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__child_id
msgid "Child Categories"
msgstr "Categories filles"

#. module: product
#: model:ir.actions.act_window,name:product.action_open_label_layout
msgid "Choose Labels Layout"
msgstr "Escolliu la disposició de les etiquetes"

#. module: product
#: model:ir.model,name:product.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Trieu la disposició del full per imprimir les etiquetes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Codis"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__color
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__color
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Color"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__color
#: model:ir.model.fields,field_description:product.field_product_product__color
#: model:ir.model.fields,field_description:product.field_product_template__color
msgid "Color Index"
msgstr "Índex de color"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__columns
msgid "Columns"
msgstr "Columnes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__combination_indices
msgid "Combination Indices"
msgstr "Combinació índexs"

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist__company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__company_id
#: model:ir.model.fields,field_description:product.field_product_product__company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__company_id
#: model:ir.model.fields,field_description:product.field_product_template__company_id
msgid "Company"
msgstr "Empresa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__complete_name
msgid "Complete Name"
msgstr "Nom complert"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Computation"
msgstr "Càlcul"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__compute_price
msgid "Compute Price"
msgstr "Preu de l'ordinador"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Conditions"
msgstr "Condicions"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11_product_template
msgid "Conference Chair"
msgstr "Presidenta de la conferència"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "Conference room table"
msgstr "Taula d'habitació de la conferència"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Configuration"
msgstr "Configuració"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Configure"
msgstr "Configura"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Confirm"
msgstr "Confirmar"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__consu
#: model:ir.model.fields.selection,name:product.selection__product_template__type__consu
msgid "Consumable"
msgstr "Consumible"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"Consumables are physical products for which you don't manage the inventory "
"level: they are always available."
msgstr ""
"Els consumibles són productes físics dels quals no gestioneu el nivell "
"d'inventari: sempre estan disponibles."

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Contact Us"
msgstr "Contacta'ns"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__qty
msgid "Contained Quantity"
msgstr "Quantitat continguda"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_packaging_positive_qty
msgid "Contained Quantity should be positive."
msgstr "L' import contét ha de ser positiu."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Contained quantity"
msgstr "Quantitat continguda"

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "Corner Desk Left Sit"
msgstr "Assetlla de cantonada a l' esquerra seu"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Corner Desk Right Sit"
msgstr "Sigueu a la cantonada desk cap a la dreta"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__standard_price
#: model:ir.model.fields,field_description:product.field_product_template__standard_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__standard_price
msgid "Cost"
msgstr "Cost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__cost_currency_id
#: model:ir.model.fields,field_description:product.field_product_template__cost_currency_id
msgid "Cost Currency"
msgstr "Moneda de cost"

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Grup de paisos "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__country_group_ids
msgid "Country Groups"
msgstr "Grups de paisos "

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Create a new pricelist"
msgstr "Crea una llista de preus nova"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
msgid "Create a new product"
msgstr "Crea un nou producte"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Create a new product variant"
msgstr "Creeu una nova variant de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_uid
#: model:ir.model.fields,field_description:product.field_product_category__create_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_uid
#: model:ir.model.fields,field_description:product.field_product_product__create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_uid
#: model:ir.model.fields,field_description:product.field_product_template__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__create_date
#: model:ir.model.fields,field_description:product.field_product_category__create_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__create_date
#: model:ir.model.fields,field_description:product.field_product_packaging__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__create_date
#: model:ir.model.fields,field_description:product.field_product_product__create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__create_date
#: model:ir.model.fields,field_description:product.field_product_template__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__create_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__create_date
msgid "Created on"
msgstr "Creat el"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__1
msgid "Cubic Feet"
msgstr "Peus cúbics"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_volume_volume_in_cubic_feet__0
msgid "Cubic Meters"
msgstr "Metres cúbiques"

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist__currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__currency_id
#: model:ir.model.fields,field_description:product.field_product_product__currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__currency_id
#: model:ir.model.fields,field_description:product.field_product_template__currency_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__custom_value
msgid "Custom Value"
msgstr "Valor personalitzat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__partner_ref
msgid "Customer Ref"
msgstr "Referència de client"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4_product_template
msgid "Customizable Desk"
msgstr "Desk personalitzable"

#. module: product
#: model:product.product,uom_name:product.expense_hotel
#: model:product.template,uom_name:product.expense_hotel_product_template
msgid "Days"
msgstr "Dies"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "Decimal Precision"
msgstr "Precisió decimals"

#. module: product
#: code:addons/product/models/res_company.py:0
#: code:addons/product/models/res_company.py:0
#, python-format
msgid "Default %(currency)s pricelist"
msgstr "Llista de preus %(currency)s per defecte"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,help:product.field_product_product__uom_id
#: model:ir.model.fields,help:product.field_product_template__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unit de mesura per defecte utilitzada per totes les operacions d'estoc."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__uom_po_id
#: model:ir.model.fields,help:product.field_product_template__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""
"Unitat per omissió de mesura usada per a les ordres de compra. Ha d' estar "
"en la mateixa categoria que la unitat per omissió de mesura."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__seller_ids
#: model:ir.model.fields,help:product.field_product_template__seller_ids
msgid "Define vendor pricelists."
msgstr "Defineix les llistes de preus del venedor."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your volume unit of measure"
msgstr "Defineix la vostra unitat de volum de mesura"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Define your weight unit of measure"
msgstr "Defineix la vostra unitat de pes de mesura"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__delay
msgid "Delivery Lead Time"
msgstr "Temps inicial d'entrega"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description
#: model:ir.model.fields,field_description:product.field_product_template__description
msgid "Description"
msgstr "Descripció"

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Desk Combination"
msgstr "Combinació desk"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Desk Stand with Screen"
msgstr "Enrere en moviment amb pantalla"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid "Desk combination, black-brown: chair + desk + drawer."
msgstr "Combinació de Dessk, negre-bong: cadira + escriptori + calaix."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__sequence
#: model:ir.model.fields,help:product.field_product_attribute_value__sequence
msgid "Determine the display order"
msgstr "Determini l'ordre de visualització"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_label_layout_form
msgid "Discard"
msgstr "Descartar"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__percentage
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Discount"
msgstr "Descompte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__discount_policy
msgid "Discount Policy"
msgstr "Política de Descompte"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__with_discount
msgid "Discount included in the price"
msgstr "Descompte inclòs en el preu"

#. module: product
#: model:res.groups,name:product.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Descompte a les línies "

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_discount_per_so_line
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Discounts"
msgstr "Descomptes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_name
#: model:ir.model.fields,field_description:product.field_product_category__display_name
#: model:ir.model.fields,field_description:product.field_product_label_layout__display_name
#: model:ir.model.fields,field_description:product.field_product_packaging__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist__display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__display_name
#: model:ir.model.fields,field_description:product.field_product_product__display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__display_name
#: model:ir.model.fields,field_description:product.field_product_template__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__display_name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_name
msgid "Display Name"
msgstr "Nom a mostrar"

#. module: product
#. openerp-web
#: code:addons/product/static/src/xml/pricelist_report.xml:0
#, python-format
msgid "Display Pricelist"
msgstr "Mostra la llista de cotitzacions"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__display_type
#: model:ir.model.fields,field_description:product.field_product_attribute_value__display_type
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__display_type
msgid "Display Type"
msgstr "Tipus de visualització"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentació "

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Drawer"
msgstr "Dibuixador"

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Drawer Black"
msgstr "Dibuixa negre"

#. module: product
#: model_terms:product.product,description:product.product_product_27
#: model_terms:product.template,description:product.product_product_27_product_template
msgid "Drawer with two routing possiblities."
msgstr "Dibuixa amb dues pol·liques de fum."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Duration"
msgstr "Durada"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_label_layout__print_format__dymo
msgid "Dymo"
msgstr "Dymo"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__dynamic
msgid "Dynamically"
msgstr "Dinàmicament"

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_template_attribute_value_attribute_value_unique
msgid "Each value should be defined only once per attribute per product."
msgstr ""
"Cada valor s' ha de definir només una vegada per atribut per producte."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_end
msgid "End Date"
msgstr "Data de finalització"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_end
msgid "End date for this vendor price"
msgstr "Data final per aquest preu de proveïdor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_end
msgid ""
"Ending datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Hora final de la data per a la validació de l' element preulist\n"
"El valor mostrat depèn de la zona horària establerta a les vostres preferències."

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Ergonomic"
msgstr "Ergonòmic"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__exclude_for
msgid "Exclude for"
msgstr "Exclou per"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__name
#: model:ir.model.fields,help:product.field_product_pricelist_item__price
msgid "Explicit rule name for this pricelist line."
msgstr "Nom de regla explícita per aquesta línia de tarifa."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__extra_html
msgid "Extra Content"
msgstr "Contingut extra"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Extra Fee"
msgstr "Tarifa extra"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__price_extra
msgid ""
"Extra price for the variant with this attribute value on sale price. eg. 200"
" price extra, 1000 + 200 = 1200."
msgstr ""
"Més preu per a la variant amb aquest valor d' atribut en el preu de la "
"venda. Per exemple. 200 dòlars extra, 1000 + 200 = 1200."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__priority
#: model:ir.model.fields,field_description:product.field_product_template__priority
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__1
msgid "Favorite"
msgstr "Favorit"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Favorites"
msgstr "Favorits"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__fixed_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__fixed
msgid "Fixed Price"
msgstr "Preu fix"

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Flipover"
msgstr "Girada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_follower_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_follower_ids
msgid "Followers"
msgstr "Seguidors"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_partner_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidors (Partners)"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_type_icon
#: model:ir.model.fields,help:product.field_product_template__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icona Font Awesome p.e. fa-tasks"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""
"Perquè s'apliqui la regla, la quantitat comprada/venuda ha de ser més gran o"
" igual que la quantitat mínima especificada en aquest camp. Expressada en la"
" UdM per defecte del producte."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__print_format
msgid "Format"
msgstr "Formatar"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__compute_price__formula
msgid "Formula"
msgstr "Fórmula"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Four Person Desk"
msgstr "Escriptori per a quatre persones"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid "Four person modern office workstation"
msgstr "Estació d'oficina moderna de quatre persones"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Future Activities"
msgstr "Activitats futures"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Informació general"

#. module: product
#: model:ir.actions.server,name:product.action_product_price_list_report
msgid "Generate Pricelist"
msgstr "Genera la llista de cotitzacions"

#. module: product
#: model:ir.actions.server,name:product.action_product_template_price_list_report
msgid "Generate Pricelist Report"
msgstr "Genera l' informe de la llista de cotitzacions"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Get product pictures using Barcode"
msgstr "Obtén fotografies de producte usant codi de barres"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__packaging_ids
#: model:ir.model.fields,help:product.field_product_template__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "Dóna les diferents maneres d'empaquetar el mateix producte."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__sequence
#: model:ir.model.fields,help:product.field_product_template__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Dona l'ordre de seqüència quan mostris una llista de productes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Google Images"
msgstr "Imatges de Google"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Group By"
msgstr "Agrupar per"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr "Índex de color HTML"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_message
#: model:ir.model.fields,field_description:product.field_product_template__has_message
msgid "Has Message"
msgstr "Té un missatge"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__html_color
#: model:ir.model.fields,help:product.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a specific HTML color index (e.g. #ff0000) to display the "
"color if the attribute type is 'Color'."
msgstr ""
"Si el tipus d'atribut és \"Color\", aquí podeu especificar un índex HTML de "
"color (p.e. #ff0000) per a mostrar el color."

#. module: product
#: model:product.product,name:product.expense_hotel
#: model:product.template,name:product.expense_hotel_product_template
msgid "Hotel Accommodation"
msgstr "Allotjament hotel"

#. module: product
#: model:product.product,uom_name:product.product_product_1
#: model:product.product,uom_name:product.product_product_2
#: model:product.template,uom_name:product.product_product_1_product_template
#: model:product.template,uom_name:product.product_product_2_product_template
msgid "Hours"
msgstr "Hores"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__id
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:product.field_product_attribute_value__id
#: model:ir.model.fields,field_description:product.field_product_category__id
#: model:ir.model.fields,field_description:product.field_product_label_layout__id
#: model:ir.model.fields,field_description:product.field_product_packaging__id
#: model:ir.model.fields,field_description:product.field_product_pricelist__id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__id
#: model:ir.model.fields,field_description:product.field_product_product__id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__id
#: model:ir.model.fields,field_description:product.field_product_template__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__id
msgid "ID"
msgstr "ID"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,field_description:product.field_product_template__activity_exception_icon
msgid "Icon"
msgstr "Icona"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_exception_icon
#: model:ir.model.fields,help:product.field_product_template__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icona que indica una activitat d'excepció."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction
#: model:ir.model.fields,help:product.field_product_product__message_unread
#: model:ir.model.fields,help:product.field_product_template__message_needaction
#: model:ir.model.fields,help:product.field_product_template__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si està marcat, hi ha nous missatges que requereixen la vostra atenció."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error
#: model:ir.model.fields,help:product.field_product_template__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si està marcat, alguns missatges tenen un error d'entrega."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_id
msgid ""
"If not set, the vendor price will apply to all variants of this product."
msgstr ""
"Si no està establerta, el preu del venedor s' aplicarà a totes les variants "
"d' aquest producte."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist__active
#: model:ir.model.fields,help:product.field_product_pricelist_item__active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr "Si es desmarca, permet ocultar la tarifa sense eliminar-la."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__active
#: model:ir.model.fields,help:product.field_product_template__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr "Si es desmarca, permet ocultar el producte sense eliminar-lo."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1920
#: model:ir.model.fields,field_description:product.field_product_template__image_1920
msgid "Image"
msgstr "Imatge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_1024
#: model:ir.model.fields,field_description:product.field_product_template__image_1024
msgid "Image 1024"
msgstr "Imatge 1024Comment"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_128
#: model:ir.model.fields,field_description:product.field_product_template__image_128
msgid "Image 128"
msgstr "Imatge 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_256
#: model:ir.model.fields,field_description:product.field_product_template__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_512
#: model:ir.model.fields,field_description:product.field_product_template__image_512
msgid "Image 512"
msgstr "Imatge 512"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Import Template for Pricelists"
msgstr "Importa una plantilla per a les llistes de cotitzacions"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Plantilla d'importació de Productes"

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "Import Template for Vendor Pricelists"
msgstr "Importa una plantilla per a les llistes de cotitzacions del venedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__standard_price
#: model:ir.model.fields,help:product.field_product_template__standard_price
msgid ""
"In Standard Price & AVCO: value of the product (automatically computed in AVCO).\n"
"        In FIFO: value of the next unit that will leave the stock (automatically computed).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"En Price estàndard & AVCO: valor del producte ( calculat automàticament en AVCO).\n"
"        En FIFO: valor de la següent unitat que deixarà les accions ( calculat automàticament).\n"
"        S'utilitza per a valorar el producte quan el cost de compra no és conegut (p. ex. Ajust de l' inventari).\n"
"        S'utilitza per calcular els marges en les ordres de venda."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_search
msgid "Inactive"
msgstr "Inactiu"

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Individual Workplace"
msgstr "place de treball individual"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__always
msgid "Instantly"
msgstr "Instantàniament"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Notes"
msgstr "Notes internes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__default_code
#: model:ir.model.fields,field_description:product.field_product_template__default_code
msgid "Internal Reference"
msgstr "Referència interna"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__barcode
msgid "International Article Number used for product identification."
msgstr ""
"Número d'article internacional utilitzat per la identificació de producte."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Inventari"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_is_follower
#: model:ir.model.fields,field_description:product.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "És seguidor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__is_product_variant
msgid "Is Product Variant"
msgstr "És la variant del producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__has_configurable_attributes
#: model:ir.model.fields,field_description:product.field_product_template__has_configurable_attributes
msgid "Is a configurable product"
msgstr "És un producte configurable"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template__is_product_variant
msgid "Is a product variant"
msgstr "És una variant de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__is_custom
msgid "Is custom value"
msgstr "És valor personalitzat"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__0
msgid "Kilograms"
msgstr "Kilogram"

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "Large Cabinet"
msgstr "Gran govern"

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "Large Desk"
msgstr "Dessk gran"

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Large Meeting Table"
msgstr "Taula de gran reunió"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute____last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value____last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value____last_update
#: model:ir.model.fields,field_description:product.field_product_category____last_update
#: model:ir.model.fields,field_description:product.field_product_label_layout____last_update
#: model:ir.model.fields,field_description:product.field_product_packaging____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist____last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item____last_update
#: model:ir.model.fields,field_description:product.field_product_product____last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo____last_update
#: model:ir.model.fields,field_description:product.field_product_template____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line____last_update
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_uid
#: model:ir.model.fields,field_description:product.field_product_category__write_uid
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_uid
#: model:ir.model.fields,field_description:product.field_product_product__write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_uid
#: model:ir.model.fields,field_description:product.field_product_template__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_uid
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value__write_date
#: model:ir.model.fields,field_description:product.field_product_category__write_date
#: model:ir.model.fields,field_description:product.field_product_label_layout__write_date
#: model:ir.model.fields,field_description:product.field_product_packaging__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist__write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__write_date
#: model:ir.model.fields,field_description:product.field_product_product__write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__write_date
#: model:ir.model.fields,field_description:product.field_product_template__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__write_date
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Late Activities"
msgstr "Activitats endarrerides"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""
"Termini de lliurament en dies entre la confirmació de la comanda de compra i"
" la recepció dels productes al seu magatzem. Utilitzat pel planificador per "
"al càlcul automàtic de la planificació de la comanda de compra."

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Legs"
msgstr "Cames"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_attribute_value__pav_attribute_line_ids
msgid "Lines"
msgstr "Línies"

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid "Locally handmade"
msgstr "Fet a mà localment"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Logistics"
msgstr "Logística"

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Looking for a custom bamboo stain to match existing furniture? Contact us "
"for a quote."
msgstr ""
"Buscant una taca de bambú personalitzada per a trobar mobles existents? "
"Contacte amb nosaltres per citar-nos."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_main_attachment_id
#: model:ir.model.fields,field_description:product.field_product_template__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunt principal"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_attribute_value__exclude_for
msgid ""
"Make this attribute value not compatible with other values of the product or"
" some attribute values of optional and accessory products."
msgstr ""
"Fes que aquest valor d' atribut no sigui compatible amb altres valors del "
"producte o d' alguns valors d' atribut de productes opcionals i accessoris."

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr "Administrar l'empaquetatge del producte"

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Gestioneu variants de productes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Margins"
msgstr "Marges"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Marge màx."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_max_margin
msgid "Max. Price Margin"
msgstr "Marge preu màx."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "Error d'entrega del missatge"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_ids
#: model:ir.model.fields,field_description:product.field_product_template__message_ids
msgid "Messages"
msgstr "Missatges"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Marge mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_min_margin
msgid "Min. Price Margin"
msgstr "Marge preu mín."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__min_quantity
msgid "Min. Quantity"
msgstr "Quantitat Mín."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_pricelist_setting__basic
msgid "Multiple prices per product"
msgstr "Múltiples preus per producte"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings__product_pricelist_setting
msgid ""
"Multiple prices: Pricelists with fixed price rules by product,\n"
"Advanced rules: enables advanced price rules for pricelists."
msgstr ""
"Múltiples preus: Prepistes amb regles de preu fixes per producte,\n"
"Regles avançades: habilita regles avançades de preu per a les llistes de preus."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__my_activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Venciment de l'activitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_custom_value__name
#: model:ir.model.fields,field_description:product.field_product_category__name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__name
#: model:ir.model.fields,field_description:product.field_product_product__name
#: model:ir.model.fields,field_description:product.field_product_template__name
msgid "Name"
msgstr "Nom"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__create_variant__no_variant
msgid "Never (option)"
msgstr "Mai (opció)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_calendar_event_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Proper esdeveniment del calendari d'activitats"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_date_deadline
#: model:ir.model.fields,field_description:product.field_product_template__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data límit de la següent activitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_summary
#: model:ir.model.fields,field_description:product.field_product_template__activity_summary
msgid "Next Activity Summary"
msgstr "Resum de la següent activitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_type_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_type_id
msgid "Next Activity Type"
msgstr "Tipus de la següent activitat"

#. module: product
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid ""
"No product to print, if the product is archived please unarchive it before "
"printing its label."
msgstr ""
"No hi ha cap producte a imprimir, si el producte s'arxiva, desarxiveu-lo "
"abans d'imprimir la seva etiqueta."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid "No vendor pricelist found"
msgstr "No s' ha trobat cap llista de preus del proveïdor"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__priority__0
msgid "Normal"
msgstr "Normal"

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "Note:"
msgstr "Nota:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__number_related_products
msgid "Number Related Products"
msgstr "Productes relacionats del nombre"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'accions"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'errors"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_needaction_counter
#: model:ir.model.fields,help:product.field_product_template__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de missatges que requereixen una acció"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_has_error_counter
#: model:ir.model.fields,help:product.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de missatges amb error d'entrega"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_item_count
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_item_count
msgid "Number of price rules"
msgstr "Nombre de regles de cotització"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__message_unread_counter
#: model:ir.model.fields,help:product.field_product_template__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de missatges no llegits"

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Office Chair"
msgstr "Cadira d'oficina"

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Office Chair Black"
msgstr "Presidenta Black de l' oficinaName"

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Office Design Software"
msgstr "Programari de disseny de l' oficina"

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Office Lamp"
msgstr "Làmpada de l' oficinaName"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"On the product %s you cannot associate the value %s with the attribute %s "
"because they do not match."
msgstr ""
"En el producte %s no podeu associar el valor %s amb l' atribut %s perquè no "
"coincideixen."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"On the product %s you cannot transform the attribute %s into the attribute "
"%s."
msgstr "En el producte %s no podeu transformar l' atribut %s a l' atribut %s."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__base_pricelist_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__pricelist
msgid "Other Pricelist"
msgstr "Altra tarifa"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Empaquetament"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_id
msgid "Parent Category"
msgstr "Categoria pare"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category__parent_path
msgid "Parent Path"
msgstr "Ruta arrel"

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Pedal Bin"
msgstr "Safata Pedal"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__percent_price
msgid "Percentage Price"
msgstr "Percentatge del preu"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__pills
msgid "Pills"
msgstr "Pastilles"

#. module: product
#. openerp-web
#: code:addons/product/static/src/js/product_pricelist_report.js:0
#, python-format
msgid "Please enter a positive whole number"
msgstr "Si us plau, introduïu un nombre positiu sencer"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Please specify the category for which this rule should be applied"
msgstr ""
"Si us plau, especifiqueu la categoria per a la qual s' hauria d' aplicar "
"aquesta regla"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Please specify the product for which this rule should be applied"
msgstr ""
"Si us plau, especifiqueu el producte pel qual s' ha d' aplicar aquesta regla"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"Please specify the product variant for which this rule should be applied"
msgstr ""
"Especifiqueu la variant del producte per a la qual s' hauria d' aplicar "
"aquesta regla"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__res_config_settings__product_weight_in_lbs__1
msgid "Pounds"
msgstr "Lliura"

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"Press a button and watch your desk glide effortlessly from sitting to "
"standing height in seconds."
msgstr ""
"Premeu un botó i mireu el vostre escriptori planant-se sense esforç de seure"
" a alçada en segons."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price
#: model:ir.model.fields,field_description:product.field_product_product__price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__price
#: model:ir.model.fields,field_description:product.field_product_template__price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Price"
msgstr "Preu"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Càlcul del preu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_discount
msgid "Price Discount"
msgstr "Descompte preu"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_round
msgid "Price Rounding"
msgstr "Arrodoniment preu"

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#: model:ir.actions.act_window,name:product.product_pricelist_item_action
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#, python-format
msgid "Price Rules"
msgstr "Regles de preus"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__price_surcharge
msgid "Price Surcharge"
msgstr "Sobrecàrrega preu"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__list_price
#: model:ir.model.fields,help:product.field_product_template__list_price
msgid "Price at which the product is sold to customers."
msgstr "Preu en què es ven el producte als clients."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Preu:"

#. module: product
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product__pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template__pricelist_id
#: model:ir.model.fields,field_description:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users__property_product_pricelist
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Pricelist"
msgstr "Tarifa"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__applied_on
msgid "Pricelist Item applicable on selected option"
msgstr "Llista de preus aplicable a l'opció seleccionada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__name
msgid "Pricelist Name"
msgstr "Nom tarifa"

#. module: product
#. openerp-web
#: code:addons/product/static/src/js/product_pricelist_report.js:0
#: model:ir.model,name:product.model_report_product_report_pricelist
#, python-format
msgid "Pricelist Report"
msgstr "Informe de la llista de cotitzacions"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Pricelist Rule"
msgstr "Regla de llista de cotitzacions"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist__item_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view_from_product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_inherit
msgid "Pricelist Rules"
msgstr "Regles de la llista de cotitzacions"

#. module: product
#. openerp-web
#: code:addons/product/static/src/xml/pricelist_report.xml:0
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
#, python-format
msgid "Pricelist:"
msgstr "Tarifa:"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_country_group__pricelist_ids
msgid "Pricelists"
msgstr "Tarifes de preus"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_pricelist_setting
msgid "Pricelists Method"
msgstr "Mètode de llistes de cotitzacions"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Les tarifes de preus són gestionats en"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Fixar preu"

#. module: product
#. openerp-web
#: code:addons/product/static/src/js/product_pricelist_report.js:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Print Labels"
msgstr "Imprimir etiquetes"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_ids
#: model:ir.model.fields,field_description:product.field_product_packaging__product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__1_product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
msgid "Product"
msgstr "Producte"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_value_view_form
msgid "Product Attribute"
msgstr "Atribut del producte"

#. module: product
#: model:ir.model,name:product.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Valor personalitzat d' atribut de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_template_value_ids
msgid "Product Attribute Values"
msgstr "Valors d'atribut de producte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Product Attribute and Values"
msgstr "Atributs i valors del producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__attribute_line_ids
msgid "Product Attributes"
msgstr "Atributs del producte"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Categories de producte"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__categ_id
#: model:ir.model.fields,field_description:product.field_product_product__categ_id
#: model:ir.model.fields,field_description:product.field_product_template__categ_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__2_product_category
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Product Category"
msgstr "Categoria del producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_configurator
msgid "Product Configurator"
msgstr "Configurador de productes"

#. module: product
#: model:ir.actions.report,name:product.report_product_template_label
#: model:ir.actions.report,name:product.report_product_template_label_dymo
msgid "Product Label (PDF)"
msgstr "Etiqueta de producte (PDF)"

#. module: product
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel
#: model:ir.model,name:product.model_report_product_report_producttemplatelabel_dymo
msgid "Product Label Report"
msgstr "Informe d' etiquetes de producte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Nom producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template__packaging_ids
msgid "Product Packages"
msgstr "Paquets de productes"

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model:ir.model.fields,field_description:product.field_product_packaging__name
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr "Embalatge de productes"

#. module: product
#: model:ir.actions.report,name:product.report_product_packaging
msgid "Product Packaging (PDF)"
msgstr "Embalatge de producte (PDF)"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packagings"
msgstr "Embalatge de productes"

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_product__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_exclusion__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Plantilla de producte"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_exclusion
msgid "Product Template Attribute Exclusion"
msgstr "Exclusió d' atribut de plantilla de producte"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Línia d' atributs de plantilla de producte"

#. module: product
#: model:ir.model,name:product.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Valor d' atribut de plantilla de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Plantilla de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_tooltip
#: model:ir.model.fields,field_description:product.field_product_template__product_tooltip
msgid "Product Tooltip"
msgstr "Consell de producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__detailed_type
#: model:ir.model.fields,field_description:product.field_product_template__detailed_type
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Product Type"
msgstr "Tipus de producte"

#. module: product
#: model:ir.model,name:product.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unitat de mesura del producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_id
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__applied_on__0_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
msgid "Product Variant"
msgstr "Variants de producte"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "Product Variant Values"
msgstr "Valors del producte"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_product_view_activity
msgid "Product Variants"
msgstr "Variants de producte"

#. module: product
#: code:addons/product/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr ""
"No s' ha definit el model de producte, contacteu amb l' administrador."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Product: %s"
msgstr "Producte: %s"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product__product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template__product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_view_activity
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "Products"
msgstr "Productes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Preu dels productes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Llista de preus de productes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Products Price Rules Search"
msgstr "Cerca de regles de cotitzacions"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Cerca preu dels productes"

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "Products: %(category)s"
msgstr "Productes: %(category)s"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr "Tarifa pública"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Purchase"
msgstr "Compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_purchase
#: model:ir.model.fields,field_description:product.field_product_template__description_purchase
msgid "Purchase Description"
msgstr "Descripció de compra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template__uom_po_id
msgid "Purchase UoM"
msgstr "Compra UoM"

#. module: product
#. openerp-web
#: code:addons/product/static/src/xml/pricelist_report.xml:0
#, python-format
msgid "Quantities:"
msgstr "IDitats:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__custom_quantity
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__min_qty
msgid "Quantity"
msgstr "Quantitat"

#. module: product
#. openerp-web
#: code:addons/product/static/src/js/product_pricelist_report.js:0
#, python-format
msgid "Quantity already present (%d)."
msgstr "Quantitat ja present (%d)."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__qty
msgid "Quantity of products contained in the packaging."
msgstr "Quantitat de productes continguts en l'empaquetament."

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__radio
msgid "Radio"
msgstr "Ràdio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__code
msgid "Reference"
msgstr "Referència"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_supplierinfo_type_action
msgid ""
"Register the prices requested by your vendors for each product, based on the"
" quantity and the period."
msgstr ""
"Registra els preus sol·licitats per cada producte, basat en la quantitat i "
"el període."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#: model:ir.model.fields,field_description:product.field_product_attribute__product_tmpl_ids
#, python-format
msgid "Related Products"
msgstr "Productes relacionats"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__ptav_product_variant_ids
msgid "Related Variants"
msgstr "Variants relacionats"

#. module: product
#. openerp-web
#: code:addons/product/static/src/xml/pricelist_report.xml:0
#, python-format
msgid "Remove quantity"
msgstr "Elimina quantitat"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__activity_user_id
#: model:ir.model.fields,field_description:product.field_product_template__activity_user_id
msgid "Responsible User"
msgstr "Usuari responsable"

#. module: product
#: model:product.product,name:product.expense_product
#: model:product.template,name:product.expense_product_product_template
msgid "Restaurant Expenses"
msgstr "Despeses del restaurant"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Mètode d'arrodoniment"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_label_layout__rows
msgid "Rows"
msgstr "Files"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__rule_tip
msgid "Rule Tip"
msgstr "Consell de regla"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Vendes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__description_sale
#: model:ir.model.fields,field_description:product.field_product_template__description_sale
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales Description"
msgstr "Descripció de vendes"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__module_sale_product_matrix
msgid "Sales Grid Entry"
msgstr "Graella d'entrada de vendes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__list_price
#: model:ir.model.fields,field_description:product.field_product_template__list_price
#: model:ir.model.fields.selection,name:product.selection__product_pricelist_item__base__list_price
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Sales Price"
msgstr "Preus de venda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__lst_price
msgid "Sales Price"
msgstr "Preu de vendes"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_attribute__display_type__select
msgid "Select"
msgstr "Seleccionar"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__categ_id
#: model:ir.model.fields,help:product.field_product_template__categ_id
msgid "Select category for the current product"
msgstr "Seleccioneu la categoria pel producte actual."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value__sequence
#: model:ir.model.fields,field_description:product.field_product_packaging__sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist__sequence
#: model:ir.model.fields,field_description:product.field_product_product__sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__sequence
#: model:ir.model.fields,field_description:product.field_product_template__sequence
msgid "Sequence"
msgstr "Seqüència"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_template__detailed_type__service
#: model:ir.model.fields.selection,name:product.selection__product_template__type__service
msgid "Service"
msgstr "Servei"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Services"
msgstr "Serveis"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Calcula el preu de manera que sigui un múltiple d'aquest valor.\n"
"L'arrodoniment s'aplica després del descompte i abans de l'increment.\n"
"Per tenir els preus acabats en 9,99, arrodoniment 10, increment -0,01."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostra tots els registres en que la data de següent acció és abans d'avui"

#. module: product
#: model:ir.model.fields.selection,name:product.selection__product_pricelist__discount_policy__without_discount
msgid "Show public price & discount to the customer"
msgstr "Mostrar al client el preu públic i el descompte"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""
"Especifica una categoria de producte si aquesta regla només s'aplica als "
"productes pertinents a aquesta categoria o a les seves categories filles. "
"Deixi-ho en blanc en cas contrari."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""
"Especifica un producte si aquesta regla només s'aplica a un producte. En un "
"altre cas deixi'l buit."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""
"Especifiqui una plantilla si aquesta regla només s'aplica a una plantilla de"
" producte. Deixi'l buit en cas contrari."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_surcharge
msgid ""
"Specify the fixed amount to add or subtract(if negative) to the amount "
"calculated with the discount."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr "Especifiqui l'import màxim del marge sobre el preu base."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr "Especifiqui l'import mínim del marge sobre el preu base."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item__date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__date_start
msgid "Start Date"
msgstr "Data inicial"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__date_start
msgid "Start date for this vendor price"
msgstr "Data inicial per aquest preu de venedor"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__date_start
msgid ""
"Starting datetime for the pricelist item validation\n"
"The displayed value depends on the timezone set in your preferences."
msgstr ""
"Hora inicial de la validació de l' element de la llista de preus\n"
"El valor mostrat depèn de la zona horària establerta a les vostres preferències."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_state
#: model:ir.model.fields,help:product.field_product_template__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estat basat en activitats\n"
"Sobrepassat: La data límit ja ha passat\n"
"Avui: La data de l'activitat és avui\n"
"Planificat: Activitats futures."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
#: model:product.template.attribute.value,name:product.product_11_attribute_1_value_1
#: model:product.template.attribute.value,name:product.product_4_attribute_1_value_1
msgid "Steel"
msgstr "Steel"

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Storage Box"
msgstr "Caixa d' emmagatzematge"

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tarifa de proveïdor"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Technical compute"
msgstr "calculació tècnica"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__pricelist_id
#: model:ir.model.fields,help:product.field_product_template__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""
"Camp tècnic. S'utilitza per buscar en tarifes, no emmagatzemades en la base "
"dades."

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Internal Reference '%s' already exists."
msgstr "La referència interna '%s' ja existeix."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The Type of this product doesn't match the Detailed Type"
msgstr "El tipus d' aquest producte no concorda amb el tipus detallat"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "The attribute %s must have at least one value for the product %s."
msgstr "L'atribut %s  ha de tenir almenys un valor per al producte %s."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value__attribute_id
msgid ""
"The attribute cannot be changed once the value is used on at least one "
"product."
msgstr ""
"L' atribut no es pot canviar una vegada que s' usi el valor com a mínim un "
"producte."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""
"El preu calculat s'expressa en la unitat de mesura per defecte del producte."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The default Unit of Measure and the purchase Unit of Measure must be in the "
"same category."
msgstr ""
"La unitat per omissió de mesura i l' unitat de compra de mesura ha d' estar "
"en la mateixa categoria."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute__display_type
#: model:ir.model.fields,help:product.field_product_attribute_value__display_type
#: model:ir.model.fields,help:product.field_product_template_attribute_value__display_type
msgid "The display type used in the Product Configurator."
msgstr "El tipus de pantalla emprat en el Configurador del producte."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging__sequence
msgid "The first in the sequence is the default one."
msgstr "El primer en la seqüència és aplicat per defecte."

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"The minimum height is 65 cm, and for standing work the maximum height "
"position is 125 cm."
msgstr ""
"L'alçada mínima és 65 cm, i per treballar en la posició màxima d'alçada és "
"125 cm."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "The minimum margin should be lower than the maximum margin."
msgstr "El marge mínim ha de ser inferior al marge màxim."

#. module: product
#: model:ir.model.fields,help:product.field_product_category__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""
"El nombre de productes sota aquesta categoria (No es consideren les "
"categories de filles)"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"The number of variants to generate is too high. You should either not "
"generate variants for each combination or generate them on demand from the "
"sales order. To do so, open the form view of attributes and change the mode "
"of *Create Variants*."
msgstr ""
"El nombre de variants a generar és massa alt. No hauríeu de generar variants"
" per a cada combinació o generar-los sota demanda de l'ordre de vendes. Per "
"a fer-ho, obriu la vista de forma d' atributs i canvieu el mode de *Crea els"
" Viriats *."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__price
msgid "The price to purchase a product"
msgstr "El preu al qual es compra un producte"

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "The product template is archived so no combination is possible."
msgstr ""
"La plantilla de producte s' ha arxivat de manera que no es possible "
"combinació."

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "The product variant must be a variant of the product template."
msgstr ""
"La variant del producte ha de ser una variant de la plantilla del producte."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__min_qty
msgid ""
"The quantity to purchase from this vendor to benefit from the price, "
"expressed in the vendor Product Unit of Measure if not any, in the default "
"unit of measure of the product otherwise."
msgstr ""
"La quantitat a comprar d' aquest proveïdor a beneficiar del preu, expressada"
" en la unitat de producte del proveïdor de mesura si no en qualsevol, en la "
"unitat per omissió de mesura del producte en cas contrari."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "The rounding method must be strictly positive."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""
"El preu de venda s' ha gestionat de la plantilla de producte. Cliqueu al "
"botó 'ConfigureVarits' per establir els preus de l' atribut extra."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "The value %s is not defined for the attribute %s on the product %s."
msgstr "El valor %s no està definit per a l' atribut %s en el producte %s."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no possible combination."
msgstr "No hi ha cap combinació possible."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining closest combination."
msgstr "No hi ha combinació més propera."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "There are no remaining possible combination."
msgstr "No hi ha cap combinació possible."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_uom
msgid "This comes from the product form."
msgstr "Això prové del formulari del producte."

#. module: product
#: code:addons/product/models/product_template.py:0
#, python-format
msgid ""
"This configuration of product attributes, values, and exclusions would lead "
"to no possible variant. Please archive or delete your product directly if "
"intended."
msgstr ""
"Aquesta configuració dels atributs del producte, els valors i les excepcions"
" no comportarien cap variant possible. Si us plau, ompliu o esborreu el "
"vostre producte directament si voleu."

#. module: product
#: model:ir.model.fields,help:product.field_product_product__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Aquesta és la suma dels preus extra de tots els atributs"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is added to sales orders and invoices."
msgstr "Aquesta nota s'afegeix a les comandes de venda i les factures."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note is only for internal purposes."
msgstr "Aquesta nota només té finalitats internes."

#. module: product
#: model:ir.model.fields,help:product.field_res_partner__property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Aquesta tarifa s'utilitzarà, en lloc de la per defecte, per a les vendes de "
"l'empresa actual."

#. module: product
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid ""
"This rounding precision is higher than the Decimal Accuracy (%s digits).\n"
"This may cause inconsistencies in computations.\n"
"Please set a precision between %s and 1."
msgstr ""
"Aquesta precisió d' arrodoniment és més alta que el valor decimal (%s).\n"
"Això pot causar inconsistències en els càlculs.\n"
"Si us plau, establiu una precisió entre %s i 1."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Aquest codi de producte del venedor s'utilitza per imprimir una sol·licitud "
"de pressupost. Deixeu-ho buit per usar el nom intern."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""
"Aquest nom de producte del venedor s'utilitza per imprimir una sol·licitud "
"de pressupost. Deixeu-ho buit per usar el nom intern."

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "Three Seater Sofa with Lounger in Steel Grey Colour"
msgstr "Tres Marter Sofa amb Lounger a Steel Grey Color"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Three-Seat Sofa"
msgstr "Sofà de tres places"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Today Activities"
msgstr "Activitats d'avui"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__type
#: model:ir.model.fields,field_description:product.field_product_template__type
msgid "Type"
msgstr "Tipus"

#. module: product
#: model:ir.model.fields,help:product.field_product_product__activity_exception_decoration
#: model:ir.model.fields,help:product.field_product_template__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipus d'activitat d'excepció registrada."

#. module: product
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "Unable to find report template for %s format"
msgstr "No s'ha pogut trobar la plantilla d'informe per al format %s"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Unit Price"
msgstr "Preu un."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging__product_uom_id
#: model:ir.model.fields,field_description:product.field_product_product__uom_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_uom
#: model:ir.model.fields,field_description:product.field_product_template__uom_id
msgid "Unit of Measure"
msgstr "Unitat de mesura"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__uom_name
#: model:ir.model.fields,field_description:product.field_product_template__uom_name
msgid "Unit of Measure Name"
msgstr "Unitat del nom de mesura"

#. module: product
#: model:product.product,uom_name:product.consu_delivery_01
#: model:product.product,uom_name:product.consu_delivery_02
#: model:product.product,uom_name:product.consu_delivery_03
#: model:product.product,uom_name:product.expense_product
#: model:product.product,uom_name:product.product_delivery_01
#: model:product.product,uom_name:product.product_delivery_02
#: model:product.product,uom_name:product.product_order_01
#: model:product.product,uom_name:product.product_product_10
#: model:product.product,uom_name:product.product_product_11
#: model:product.product,uom_name:product.product_product_11b
#: model:product.product,uom_name:product.product_product_12
#: model:product.product,uom_name:product.product_product_13
#: model:product.product,uom_name:product.product_product_16
#: model:product.product,uom_name:product.product_product_20
#: model:product.product,uom_name:product.product_product_22
#: model:product.product,uom_name:product.product_product_24
#: model:product.product,uom_name:product.product_product_25
#: model:product.product,uom_name:product.product_product_27
#: model:product.product,uom_name:product.product_product_3
#: model:product.product,uom_name:product.product_product_4
#: model:product.product,uom_name:product.product_product_4b
#: model:product.product,uom_name:product.product_product_4c
#: model:product.product,uom_name:product.product_product_4d
#: model:product.product,uom_name:product.product_product_5
#: model:product.product,uom_name:product.product_product_6
#: model:product.product,uom_name:product.product_product_7
#: model:product.product,uom_name:product.product_product_8
#: model:product.product,uom_name:product.product_product_9
#: model:product.template,uom_name:product.consu_delivery_01_product_template
#: model:product.template,uom_name:product.consu_delivery_02_product_template
#: model:product.template,uom_name:product.consu_delivery_03_product_template
#: model:product.template,uom_name:product.expense_product_product_template
#: model:product.template,uom_name:product.product_delivery_01_product_template
#: model:product.template,uom_name:product.product_delivery_02_product_template
#: model:product.template,uom_name:product.product_order_01_product_template
#: model:product.template,uom_name:product.product_product_10_product_template
#: model:product.template,uom_name:product.product_product_11_product_template
#: model:product.template,uom_name:product.product_product_12_product_template
#: model:product.template,uom_name:product.product_product_13_product_template
#: model:product.template,uom_name:product.product_product_16_product_template
#: model:product.template,uom_name:product.product_product_20_product_template
#: model:product.template,uom_name:product.product_product_22_product_template
#: model:product.template,uom_name:product.product_product_24_product_template
#: model:product.template,uom_name:product.product_product_25_product_template
#: model:product.template,uom_name:product.product_product_27_product_template
#: model:product.template,uom_name:product.product_product_3_product_template
#: model:product.template,uom_name:product.product_product_4_product_template
#: model:product.template,uom_name:product.product_product_5_product_template
#: model:product.template,uom_name:product.product_product_6_product_template
#: model:product.template,uom_name:product.product_product_7_product_template
#: model:product.template,uom_name:product.product_product_8_product_template
#: model:product.template,uom_name:product.product_product_9_product_template
msgid "Units"
msgstr "Unitats"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_uom
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Unitats de mesura"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread
#: model:ir.model.fields,field_description:product.field_product_template__message_unread
msgid "Unread Messages"
msgstr "Missatges pendents de llegir"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__message_unread_counter
#: model:ir.model.fields,field_description:product.field_product_template__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Comptador de missatges no llegits"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist_page
msgid "UoM"
msgstr "UdM"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Upsell & Cross-Sell"
msgstr "Venda addicional i venda creuada"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__is_used_on_products
msgid "Used on Products"
msgstr "S' usa en els productes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Línies d' atribut de producte vàlides"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Validesa"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value__name
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__name
msgid "Value"
msgstr "Valor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_count
msgid "Value Count"
msgstr "Comptador de valors"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_attribute_value__price_extra
msgid "Value Price Extra"
msgstr "Valor Preu extra"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__value_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line__value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
#: model_terms:ir.ui.view,arch_db:product.product_template_attribute_line_form
msgid "Values"
msgstr "Valors"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_view_search
msgid "Variant"
msgstr "Variant"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_variant_count
msgid "Variant Count"
msgstr "Comptador de variants"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1920
msgid "Variant Image"
msgstr "Imatge de la variable"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_1024
msgid "Variant Image 1024"
msgstr "Imatge Variat 1024Comment"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_128
msgid "Variant Image 128"
msgstr "Imatge variant 128"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_256
msgid "Variant Image 256"
msgstr "Imatge variant 256"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__image_variant_512
msgid "Variant Image 512"
msgstr "Imatge variant 512"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Informació variant"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__price_extra
msgid "Variant Price Extra"
msgstr "Preu extra de les variants"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__variant_seller_ids
msgid "Variant Seller"
msgstr "Venedor de variants"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__product_template_variant_value_ids
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
msgid "Variant Values"
msgstr "Valors de les variables"

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid "Variant: %s"
msgstr "Variant: %s"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__group_product_variant
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Variants"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute__create_variant
msgid "Variants Creation Mode"
msgstr "Mode de creació dels variants"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Proveïdor "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendor Bills"
msgstr "Factures de proveïdors "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Informació del venedor"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr "Llistes de preus de proveïdor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_code
msgid "Vendor Product Code"
msgstr "Codi de producte del proveïdor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo__product_name
msgid "Vendor Product Name"
msgstr "Nombre del producte del proveïdor"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo__name
msgid "Vendor of this product"
msgstr "Venedor d'aquest producte"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__seller_ids
#: model:ir.model.fields,field_description:product.field_product_template__seller_ids
msgid "Vendors"
msgstr "Proveïdors "

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Virtual Home Staging"
msgstr "Esternar a la llar virtualName"

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "Virtual Interior Design"
msgstr "Disseny virtual de l' interior"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume
#: model:ir.model.fields,field_description:product.field_product_template__volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Volume"
msgstr "Volum"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_volume_volume_in_cubic_feet
msgid "Volume unit of measure"
msgstr "Unitat de volum de mesura"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__volume_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Unitat de volum de l' etiqueta de mesura"

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#: code:addons/product/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "Avís. "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Warnings"
msgstr "Avisos"

#. module: product
#: model_terms:product.product,website_description:product.product_product_4
#: model_terms:product.product,website_description:product.product_product_4b
#: model_terms:product.product,website_description:product.product_product_4c
#: model_terms:product.product,website_description:product.product_product_4d
#: model_terms:product.template,website_description:product.product_product_4_product_template
msgid ""
"We pay special attention to detail, which is why our desks are of a superior"
" quality."
msgstr ""
"Paguem atenció especial als detalls, per això els nostres pupitres són d'una"
" qualitat superior."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight
#: model:ir.model.fields,field_description:product.field_product_template__weight
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Weight"
msgstr "Pes"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Unitat de mesura de pes"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product__weight_uom_name
#: model:ir.model.fields,field_description:product.field_product_template__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Unitat de pes de l'etiqueta de mesura"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
#: model:product.template.attribute.value,name:product.product_4_attribute_2_value_1
msgid "White"
msgstr "Blanc"

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You are setting a Decimal Accuracy less precise than the UOMs:\n"
"%s\n"
"This may cause inconsistencies in computations.\n"
"Please increase the rounding of those units of measure, or the digits of this Decimal Accuracy."
msgstr ""
"Esteu establint un valor decimal menys precís que els UOM:\n"
"%s\n"
"Això pot causar inconsistències en els càlculs.\n"
"Si us plau, augmenteu la ronda d'aquestes unitats de mesura, o els dígits d' aquesta precisió decimal."

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item__percent_price
#: model:ir.model.fields,help:product.field_product_pricelist_item__price_discount
msgid "You can apply a mark-up by setting a negative discount."
msgstr "Podeu aplicar una marca en establir un descompte negatiu."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"You can assign pricelists to your customers or select one when creating a "
"new sales quotation."
msgstr ""
"Podeu assignar llistes de preu als vostres clients o seleccionar- ne una "
"quan creeu una nova citació de vendes."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot assign the Main Pricelist as Other Pricelist in PriceList Item"
msgstr ""
"No podeu assignar la llista de cotitzacions a l' element Preu de la llista "
"de cotitzacions"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the Variants Creation Mode of the attribute %s because it is used on the following products:\n"
"%s"
msgstr ""
"No podeu canviar el mode de creació de Varites de l' atribut %s perquè s'utilitza en els següents productes:\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot change the attribute of the value %s because it is used on the "
"following products:%s"
msgstr ""
"No podeu canviar l' atribut del valor %s perquè s'utilitza en els següents "
"productes: %s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "You cannot change the product of the value %s set on product %s."
msgstr ""
"No podeu canviar el producte del valor %s establert en el producte %s."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "You cannot change the value of the value %s set on product %s."
msgstr "No podeu canviar el valor del valor %s establert en el producte %s."

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "You cannot create recursive categories."
msgstr "No podeu crear categories recursives."

#. module: product
#: model:ir.model.constraint,message:product.constraint_product_attribute_value_value_company_uniq
msgid ""
"You cannot create two values with the same name for the same attribute."
msgstr "No podeu crear dos valors amb el mateix nom pel mateix atribut."

#. module: product
#: code:addons/product/models/decimal_precision.py:0
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""
"No pot definir una precisió decimal dels comptes que sigui major que el "
"factor d'arrodoniment de la moneda actual de la companyia."

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid "You cannot delete the %s product category."
msgstr "No podeu esborrar la categoria de producte %s."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the attribute %s because it is used on the following products:\n"
"%s"
msgstr ""
"No podeu eliminar l' atribut %s perquè s'utilitza en els següents productes:\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot delete the value %s because it is used on the following products:\n"
"%s\n"
" If the value has been associated to a product in the past, you will not be able to delete it."
msgstr ""
"No podeu suprimir el valor %s perquè s'utilitza en els productes següents:\n"
"%s\n"
" Si el valor s'ha associat a un producte en el passat, no el podreu suprimir."

#. module: product
#: code:addons/product/models/product.py:0
#, python-format
msgid ""
"You cannot delete this product category, it is the default generic category."
msgstr ""
"No podeu eliminar aquesta categoria de producte, és la categoria genèrica "
"per omissió."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot delete those pricelist(s):\n"
"(%s)\n"
", they are used in other pricelist(s):\n"
"%s"
msgstr ""
"No podeu eliminar aquesta llista de preus(s):\n"
"(%s)\n"
", s'utilitzen a una altra llista de preus(s):\n"
"%s"

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid "You cannot delete value %s because it was used in some products."
msgstr ""
"No podeu suprimir el valor %s perquè s'ha utilitzat en alguns productes."

#. module: product
#: code:addons/product/models/product_pricelist.py:0
#, python-format
msgid ""
"You cannot disable a pricelist rule, please delete it or archive its "
"pricelist instead."
msgstr ""
"No podeu deshabilitar una regla de la llista de preus, si us plau, esborreu-"
" la o ompliu la seva llista de preus."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot move the attribute %s from the product %s to the product %s."
msgstr "No podeu moure l'atribució %s del producte %s al producte %s."

#. module: product
#: code:addons/product/models/product_attribute.py:0
#: code:addons/product/models/product_attribute.py:0
#, python-format
msgid ""
"You cannot update related variants from the values. Please update related "
"values from the variants."
msgstr ""
"No podeu actualitzar les variants relacionades amb els valors. Si us plau, "
"actualitzeu els valors relacionats des de les variants."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Heu de definir un producte per a tot el que veneu o compreu,\n"
"                 ja sigui un producte emmagatzemable, un consumible o un servei."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Has de definir un producte per tot el que venguis o compres.\n"
"                Si es tracta d'un producte esorables, d'un servei o bé d'un servei.\n"
"                El formulari de producte conté informació per simplificar el procés de venda:\n"
"                preu, notes a les cites, dades comptables, mètodes d'adquisició, etc."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
msgid ""
"You must define a product for everything you sell, whether it's a physical product,\n"
"                a consumable or a service you offer to customers.\n"
"                The product form contains information to simplify the sale process:\n"
"                price, notes in the quotation, accounting data, procurement methods, etc."
msgstr ""
"Has de definir un producte per tot el que vens, si és un producte físic,\n"
"                un estafador o un servei que ofereix als clients.\n"
"                El formulari de producte conté informació per simplificar el procés de venda:\n"
"                preu, notes a les cites, dades comptables, mètodes d'adquisició, etc."

#. module: product
#: code:addons/product/wizard/product_label_layout.py:0
#, python-format
msgid "You need to set a positive quantity."
msgstr "Has de posar una quantitat positiva."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_view_kanban
msgid "days"
msgstr "dies"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "e.g. Cheese Burger"
msgstr "p. ex. Hamburguesa de formatge"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "p. ex. Llums"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr "e.x. Subscripció a Odoo Enterprise"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr "p. ex. USD Minoristes"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "per"
msgstr "per"

#. module: product
#: code:addons/product/models/product.py:0
#: code:addons/product/models/product_template.py:0
#, python-format
msgid "product"
msgstr "producte"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "la empresa principal"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template."
msgstr "la plantilla de producte."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "fins"
