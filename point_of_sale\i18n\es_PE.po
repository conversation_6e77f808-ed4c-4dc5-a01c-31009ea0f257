# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * point_of_sale
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-02-16 14:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:642
#, python-format
msgid " REFUND"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_supplier_invoice_count
msgid "# Vendor Bills"
msgstr "# Facturas de Proveedor"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_nbr_lines
msgid "# of Lines"
msgstr "# de Líneas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_purchase_order_count
msgid "# of Purchase Order"
msgstr "# de Orden de Compra"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1283
#, python-format
msgid "% discount"
msgstr "% descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1410
#: code:addons/point_of_sale/static/src/xml/pos.xml:1450
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(actualizar)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "+ Transactions"
msgstr "+ Transacciones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:279
#, python-format
msgid "123.14 €"
msgstr "123.14 €"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, fuzzy
msgid ""
"<span class=\"o_stat_text\">Put</span>\n"
"                                <span class=\"o_stat_text\">Money In</span>"
msgstr ""
"<span class=\"o_stat_text\">Poner</span>\n"
"                                    <span class=\"o_stat_text\">Dinero</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Closing Balance</span>"
msgstr "<span class=\"o_stat_text\">Establecer Balance de Cierre</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Set Opening Balance</span>"
msgstr "<span class=\"o_stat_text\">Establecer Balance de Apertura</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, fuzzy
msgid ""
"<span class=\"o_stat_text\">Take</span>\n"
"                                <span class=\"o_stat_text\">Money Out</span>"
msgstr ""
"<span class=\"o_stat_text\">Sacar</span>\n"
"                                    <span class=\"o_stat_text\">Dinero</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Cash Balance</span>"
msgstr "<span>Balance de Efectivo</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Última Fecha de Cierre</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reports</span>"
msgstr "<span>Reportes</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Ver</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Company</strong>:<br/>"
msgstr "<strong>Compañía</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Ending Balance</strong>:<br/>"
msgstr "<strong>Balance Final</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Journal</strong>:<br/>"
msgstr "<strong>Diario</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Opening Date</strong>:<br/>"
msgstr "<strong>Fecha de Apertura</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Print Date</strong>:<br/>"
msgstr "<strong>Fecha de Impresión</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Starting Balance</strong>:<br/>"
msgstr "<strong>Balance Inicial</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Starting Date</strong>:<br/>"
msgstr "<strong>Fecha Inicial</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "<strong>Statement Name</strong>:<br/>"
msgstr "<strong>Nombre de Extracto</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>Total</strong>"
msgstr "Total"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "<strong>User</strong>:<br/>"
msgstr "<strong>Usuario</strong>:<br/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "= Theoretical Closing Balance"
msgstr "= Balance de Cierre Teórico"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1885
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Si hace click en \"Confirmar\" se validará el pago."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1171
#, python-format
msgid "A Customer Name Is Required"
msgstr "Se Requiere un Nombre de Cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_pos_security_pin
msgid ""
"A Security PIN used to protect sensible functionality in the Point of Sale"
msgstr ""
"Un PIN de Seguridad usado para proteger funciones sensibles del Punto de "
"Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "A custom receipt footer message"
msgstr "Un mensaje de pie personalizado para el recibo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "A custom receipt header message"
msgstr "Un mensaje de encabezado personalizado para el recibo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data"
msgstr ""
"Un identificador global único para esta configuración de tpv, usado para "
"prevenir conflictos con datos generados por los clientes"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Un número de secuencia que se incrementa cada vez que un usuario reanuda la "
"sesión de tpv"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Un número de secuencia que se incrementa con cada pedido"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which\n"
"                you sell through the point of sale. The user has to check "
"the\n"
"                currencies in your cash registers at the beginning and the "
"end\n"
"                of each session."
msgstr ""
"Una sesión es un periodo de tiempo, normalmente un día, durante el cual\n"
"                se vende a través del punto de venta. El usuario tiene que "
"verificar el\n"
"                efectivo en la caja registradora al comienzo y al final de "
"cada sesión."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Un número de secuencia único por sesión para el pedido"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt"
msgstr "Un texto corto que se insertará como pie en el recibo impreso"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_receipt_header
msgid "A short text that will be inserted as a header in the printed receipt"
msgstr "Un texto corto que se insertará como encabezado en el recibo impreso"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1449
#, python-format
msgid "ABC"
msgstr "ABC"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting Information"
msgstr "Información Contable"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order_sale_journal
msgid "Accounting journal used to post sales entries."
msgstr "Diario contable usado para contabilizar los asientos."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_5
#: model:product.template,name:point_of_sale.partner_product_5_product_template
msgid "Acsone.eu"
msgstr "Acsone.eu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
#: selection:pos.config,state:0
msgid "Active"
msgstr "Activo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal_journal_user
msgid "Active in Point of Sale"
msgstr "Activo en Punto de Venta"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1729
#, python-format
msgid "Add Tip"
msgstr "Agregar Propina"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_discount
msgid "Add a Global Discount"
msgstr "Agregar un Descuento Global"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:374
#: code:addons/point_of_sale/static/src/xml/pos.xml:452
#, python-format
msgid "Address"
msgstr "Dirección"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Todas las líneas de ventas"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_reprint:0
msgid "Allow cashier to reprint receipts"
msgstr "Permitir al cajero reimprimir recibos"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_discount:0
msgid "Allow discounts on order lines only"
msgstr "Permitir descuentos sólo en líneas de pedido"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_discount:0
msgid "Allow global discounts"
msgstr "Permitir descuentos globales"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_mercury:0
msgid "Allows customers to pay with credit cards."
msgstr "Permitir a los clientes pagar con tarjeta de crédito."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_discount
msgid ""
"Allows the cashier to quickly give a percentage sale discount for all the "
"sales order to a customer"
msgstr ""
"Permite al cajero otorgar un porcentaje de descuento por toda la orden de "
"venta para un cliente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_loyalty
msgid ""
"Allows you to define a loyalty program in the point of sale, where the "
"customers earn loyalty points and get rewards"
msgstr ""
"Permite definir un programa de lealtad en el punto de venta, donde los "
"clientes ganan puntos de lealtad y reciben recompensas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_amount
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "Amount"
msgstr "Cantidad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Monto de la Diferencia Permitida"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Monto total"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_name
msgid "An internal identification of the point of sale"
msgstr "Identificación interna del TPV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Apply"
msgstr "Aplicar"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_discount
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_discount
msgid "Apply Discount"
msgstr "Aplicar Descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1877
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Está seguro que el cliente desea pagar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Impresión automática del recibo"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_cashdrawer
msgid "Automatically open the cashdrawer"
msgstr "Abrir automáticamente la gaveta de dinero"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_journal_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_journal_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Available Payment Methods"
msgstr "Métodos de Pago Disponibles"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_available_in_pos
msgid "Available in the Point of Sale"
msgstr "Disponible en el Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_average_price
msgid "Average Price"
msgstr "Precio Promedio"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.bacardi
#: model:product.template,name:point_of_sale.bacardi_product_template
msgid "Bacardi"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:267
#: code:addons/point_of_sale/static/src/xml/pos.xml:587
#, python-format
msgid "Back"
msgstr "Atrás"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.baileys
#: model:product.template,name:point_of_sale.baileys_product_template
msgid "Baileys"
msgstr ""

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_statement_ids
msgid "Bank Statement"
msgstr "Extracto Bancario"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Línea de Extracto Bancario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:351
#: code:addons/point_of_sale/static/src/xml/pos.xml:393
#, python-format
msgid "Barcode"
msgstr "Código de Barras"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Nomenclaturas de Código de Barras"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1157
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
#, python-format
msgid "Barcode Scanner"
msgstr "Lector de Código de Barras"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_barcode_nomenclature_id
msgid "Barcodes"
msgstr "Códigos de Barra"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.beer
msgid "Beers"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
#: model:pos.category,name:point_of_sale.beverage
msgid "Beverages"
msgstr "Bebidas"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.raisins_noir
#: model:product.template,name:point_of_sale.raisins_noir_product_template
msgid "Black Grapes"
msgstr "Uvas Negras"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.bloody_mary
#: model:product.template,name:point_of_sale.bloody_mary_product_template
msgid "Bloody Mary"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.boni_orange
#: model:product.template,name:point_of_sale.boni_orange_product_template
msgid "Boni Oranges"
msgstr "Naranjas Boni"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.budweiser
#: model:product.template,name:point_of_sale.budweiser_product_template
msgid "Budweiser"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:868
#, python-format
msgid "Button"
msgstr "Botón"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy"
msgstr "Saltar impresión del navegador e imprimir vía el proxy hardware"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:781
#, python-format
msgid "CHANGE"
msgstr "VUELTO"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.caipirinha
#: model:product.template,name:point_of_sale.caipirinha_product_template
msgid "Caipirinha"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:420
#: code:addons/point_of_sale/static/src/xml/pos.xml:909
#: code:addons/point_of_sale/static/src/xml/pos.xml:926
#: code:addons/point_of_sale/static/src/xml/pos.xml:943
#: code:addons/point_of_sale/static/src/xml/pos.xml:963
#: code:addons/point_of_sale/static/src/xml/pos.xml:1015
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_discount
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: point_of_sale
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1866
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "No se puede entregar vuelto sin un pago en efectivo"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.captain_morgan
#: model:product.template,name:point_of_sale.captain_morgan_product_template
msgid "Captain Morgan"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.capuccino
#: model:product.template,name:point_of_sale.capuccino_product_template
msgid "Capuccino"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.carlsberg
#: model:product.template,name:point_of_sale.carlsberg_product_template
msgid "Carlsberg"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.carotte
#: model:product.template,name:point_of_sale.carotte_product_template
msgid "Carrots"
msgstr "Zanahorias"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_cash_control
msgid "Cash Control"
msgstr "Control de Efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_journal_id
msgid "Cash Journal"
msgstr "Diario de Efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_id
msgid "Cash Register"
msgstr "Caja Registradora"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:106
#, python-format
msgid "Cash control can only be applied to cash journals."
msgstr ""
"El control de efectivo solamente puede ser aplicado a diarios de efectivo."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_cashdrawer
msgid "Cashdrawer"
msgstr "Gaveta de dinero"

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:19
#, python-format
msgid "Cashier"
msgstr "Cajero"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Las categorías son usadas para poder navegar a través de los productos\n"
"                usando la interfaz de pantalla táctil."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:493
#, python-format
msgid "Change"
msgstr "Vuelto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:102
#, python-format
msgid "Change Cashier"
msgstr "Cambiar Cajero"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1116
#, python-format
msgid "Change Customer"
msgstr "Cambiar Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1729
#, python-format
msgid "Change Tip"
msgstr "Vuelto de Propina"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:649
#: code:addons/point_of_sale/static/src/xml/pos.xml:1339
#, python-format
msgid "Change:"
msgstr "Vuelto:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_to_weight
msgid ""
"Check if the product should be weighted using the hardware scale integration"
msgstr ""
"Marque si el producto debe ser pesado usando la integración hardware de la "
"balanza"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_available_in_pos
msgid "Check if you want this product to appear in the Point of Sale"
msgstr "Marque esta casilla si quiere que este producto aparezca en el TPV"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "Revisar la cantidad en caja al inicio y al cierre."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal_journal_user
msgid ""
"Check this box if this journal define a payment method that can be used in a "
"point of sale."
msgstr ""
"Marca esta casilla de si este diario define un método de pago que puede ser "
"utilizado en el punto de venta."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_by
msgid ""
"Check this if you want to group the Journal Items by Product while closing a "
"Session"
msgstr ""
"Marque si desea agrupar los apuntes contable por producto al cierre de sesión"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1917
#, python-format
msgid "Check your internet connection and try again."
msgstr "Revise su conexión de internet e intente nuevamente."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_child_id
msgid "Children Categories"
msgstr "Categorías Hijo."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:322
#: code:addons/point_of_sale/static/src/xml/pos.xml:323
#, python-format
msgid "City"
msgstr "Provincia"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid "Click to add a new product."
msgstr "Haga click para añadir un nuevo producto."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid "Click to add a payment method."
msgstr "Haga click para añadir un método de pago."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Click to create a new PoS config."
msgstr "Haga cick para crear una nueva configuración de PdV."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "Click to create a new order."
msgstr "Haga click para crear un nuevo pedido."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Click to define a new category."
msgstr "Haga click para definir una nueva categoría."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "Click to start a new session."
msgstr "Haga click para iniciar una nueva sesión."

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:18
#, fuzzy, python-format
msgid "Client"
msgstr "ID de cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:679
#: code:addons/point_of_sale/static/src/js/chrome.js:687
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Closed & Posted"
msgstr "Cerrado & Contabilizado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:335
#, python-format
msgid "Closing ..."
msgstr "Cerrando ..."

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Closing Control"
msgstr "Control de Cierre"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_stop_at
msgid "Closing Date"
msgstr "Fecha de Cierre"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.coke
#: model:product.template,name:point_of_sale.coke_product_template
#, fuzzy
msgid "Coca-Cola"
msgstr "Coca-Cola Light 33cl"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.cocktail
msgid "Cocktails"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.coffee
#: model:product.template,name:point_of_sale.coffee_product_template
msgid "Coffee"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_company_id
msgid "Company"
msgstr "Compañia"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poire_conference
#: model:product.template,name:point_of_sale.poire_conference_product_template
msgid "Conference pears"
msgstr "Conference pears"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Configuración"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
msgid "Configure Point of Sale"
msgstr "Configurar Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid ""
"Configure at least one Point of Sale to be able to sell through the PoS "
"interface."
msgstr ""
"Configure al menos un Punto de Venta para poder vender a través de la "
"interfaz de PdV."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:684
#: code:addons/point_of_sale/static/src/xml/pos.xml:906
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:115
#, python-format
msgid "Connecting to the PosBox"
msgstr "Conectando al PosBox"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "Continuar Vendiendo"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.corona
#: model:product.template,name:point_of_sale.corona_product_template
msgid "Corona"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.cosmopolitan
#: model:product.template,name:point_of_sale.cosmopolitan_product_template
msgid "Cosmopolitan"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1259
#, python-format
msgid "Could Not Read Image"
msgstr "No se Pudo Leer la Imagen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:330
#, python-format
msgid "Country"
msgstr "País"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_create_date
msgid "Created on"
msgstr "Creado en"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_create_date
msgid "Creation Date"
msgstr "Fecha de Creacion"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_mercury
msgid "Credit Cards"
msgstr "Tarjetas de Credito"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.cuba_libre
#: model:product.template,name:point_of_sale.cuba_libre_product_template
msgid "Cuba Libre"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_current_session_id
msgid "Current Session"
msgstr "Sesion Actual"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_current_session_state
#, fuzzy
msgid "Current session state"
msgstr "Sesion Actual"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1740
#: code:addons/point_of_sale/static/src/xml/pos.xml:116
#: code:addons/point_of_sale/static/src/xml/pos.xml:617
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:459
#, python-format
msgid "Customer Invoice"
msgstr "Factura de Cliente"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.daiquiri
#: model:product.template,name:point_of_sale.daiquiri_product_template
msgid "Daiquiri"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Tablero"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_7
#: model:product.template,name:point_of_sale.partner_product_7_product_template
msgid "Datalp.com"
msgstr "Datalp.com"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_date
msgid "Date Order"
msgstr "Fecha de Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1147
#, python-format
msgid "Debug Window"
msgstr "Ventana de Depuracion"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_account_id
msgid "Default Debit Account"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_default_fiscal_position_id
#, fuzzy
msgid "Default Fiscal Position"
msgstr "Posicion Fiscal"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers"
msgstr ""
"Define que tipos de codigos de barra estan disponibles y como se asignan a "
"productos, clientes y cajeros"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_delay_validation
msgid "Delay Validation"
msgstr "Validacion Retrasada"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1167
#, python-format
msgid "Delete Paid Orders"
msgstr "Borrar Pedidos Pagados"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:261
#, python-format
msgid "Delete Paid Orders ?"
msgstr "¿Borrar Pedidos Pagados?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1168
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Borrar Pedidos Impagos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:271
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "¿Borrar Pedidos Impagos?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_property_delivery_carrier_id
msgid "Delivery Method"
msgstr "Metodo de Envio"

#. module: point_of_sale
#: selection:pos.config,state:0
msgid "Deprecated"
msgstr "Descatalogado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1119
#, python-format
msgid "Deselect Customer"
msgstr "Remover Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:53
#, python-format
msgid "Destroy Current Order ?"
msgstr "¿Destruir el Pedido Actual?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_difference
msgid "Difference"
msgstr "Diferencia"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr ""
"Diferencia entre el balance de cierre teorico y el balance de cierre real."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.disaronno
#: model:product.template,name:point_of_sale.disaronno_product_template
msgid "Disaronno"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:139
#, python-format
msgid "Disc"
msgstr "Desc"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_discount
msgid "Discount"
msgstr "Descuento"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_discount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_discount
msgid "Discount (%)"
msgstr "Descuento (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_notice
msgid "Discount Notice"
msgstr "Nota de Descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:724
#: code:addons/point_of_sale/static/src/xml/pos.xml:1312
#, python-format
msgid "Discount:"
msgstr "Descuento:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:17
#, python-format
msgid "Discounted Product"
msgstr "Producto con Descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:790
#, python-format
msgid "Discounts"
msgstr "Descuentos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_display_categ_images
msgid "Display Category Pictures"
msgstr "Mostrar Imágenes en Categorías"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct_display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid ""
"Do not forget to set the price and the point of sale category\n"
"                in which it should appear. If a product has no point of "
"sale\n"
"                category, you can not sell it through the point of sale\n"
"                interface."
msgstr ""
"No olvide establecer el precio y la categoría de punto de venta\n"
"                en cuál debe de aparecer. Sí un producto no tiene categoría "
"de\n"
"                punto de venta, no se va a poder vender a travez de la "
"interfaz\n"
"                del punto de venta."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Do you want to open cash registers?"
msgstr "¿Quiere abrir cajas registradoras?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Done"
msgstr "Realizado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:491
#, python-format
msgid "Due"
msgstr "Vencido"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_9
#: model:product.template,name:point_of_sale.partner_product_9_product_template
msgid "EGGS-solutions.fr"
msgstr "EGGS-solutions.fr"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_3
#: model:product.template,name:point_of_sale.partner_product_3_product_template
msgid "Eezee-It"
msgstr "Eezee-It"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.efes
#: model:product.template,name:point_of_sale.efes_product_template
msgid "Efes"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_4
#: model:product.template,name:point_of_sale.partner_product_4_product_template
msgid "Ekomurz.nl"
msgstr "Ekomurz.nl"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1150
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_electronic_scale
#, python-format
msgid "Electronic Scale"
msgstr "Balanza Electrónica"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:343
#: code:addons/point_of_sale/static/src/xml/pos.xml:378
#, python-format
msgid "Email"
msgstr "Email"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1837
#, python-format
msgid "Empty Order"
msgstr "Pedido Vacío"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_scan_via_proxy
msgid "Enable barcode scanning with a remotely connected barcode scanner"
msgstr ""
"Habilitar escaneado de código de barras con un escáner remoto conectado"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_electronic_scale
msgid "Enables Electronic Scale integration"
msgstr "Habilita integración de Balanza Electrónica"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_payment_terminal
msgid "Enables Payment Terminal integration"
msgstr "Habilitar integración de un Terminal de Pago"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_vkeyboard
msgid "Enables an integrated Virtual Keyboard"
msgstr "Habilitar un Teclado Virtual integrado"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_invoicing
msgid "Enables invoice generation from the Point of Sale"
msgstr "Habilita generación de facturas desde el Punto de Ventas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "End of Session"
msgstr "Fin de la Sesion"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Balance Final"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:13
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1188
#, python-format
msgid "Error: Could not Save Changes"
msgstr "Error: No puede Guardar Cambios"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:239
#, python-format
msgid ""
"Error: The Point of Sale User must belong to the same company as the Point "
"of Sale. You are probably trying to load the point of sale as an "
"administrator in a multi-company setup, with the administrator account set "
"to the wrong company."
msgstr ""
"Error: El usuario del PdV debe pertenecer a la misma compañía que el PdV. "
"Probablemente está intentando cargar el PdV como administrador en un entorno "
"multi-compañía, con la cuenta de administrador establecida en la compañía "
"equivocada."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.espresso
#: model:product.template,name:point_of_sale.espresso_product_template
msgid "Espresso"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1169
#, python-format
msgid "Export Paid Orders"
msgstr "Exportar Pedidos Pagados"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1170
#, python-format
msgid "Export Unpaid Orders"
msgstr "Exportar Pedidos Impagos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.chicon_flandria_extra
#: model:product.template,name:point_of_sale.chicon_flandria_extra_product_template
msgid "Extra Flandria chicory"
msgstr "Extra Flandria chicory"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Informacion Extra"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.fanta
#: model:product.template,name:point_of_sale.fanta_product_template
msgid "Fanta"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Features"
msgstr "Características"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1029
#, python-format
msgid "Finished Importing Orders"
msgstr "Se Finalizó la Importación de Pedidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_fiscal_position_id
msgid "Fiscal Position"
msgstr "Posicion Fiscal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_fiscal_position_ids
msgid "Fiscal Positions"
msgstr "Posiciones Fiscales"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Footer"
msgstr "Pie"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_big_scrollbars
msgid "For imprecise industrial touchscreens"
msgstr "Para pantalles tactiles industriables imprecisas"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.fosters
#: model:product.template,name:point_of_sale.fosters_product_template
msgid "Foster's"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.fruits_vegetables
msgid "Fruits and Vegetables"
msgstr "Frutas y Vegetales"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "General Information"
msgstr "Informacion General"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_golden_perlim
#: model:product.template,name:point_of_sale.pomme_golden_perlim_product_template
msgid "Golden Apples Perlim"
msgstr "Golden Apples Perlim"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.gordon
#: model:product.template,name:point_of_sale.gordon_product_template
msgid "Gordon"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_granny_smith
#: model:product.template,name:point_of_sale.pomme_granny_smith_product_template
msgid "Granny Smith apples"
msgstr "Granny Smith apples"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_verts
#: model:product.template,name:point_of_sale.poivron_verts_product_template
msgid "Green Peppers"
msgstr "Green Peppers"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.green_tea
#: model:product.template,name:point_of_sale.green_tea_product_template
#, fuzzy
msgid "Green Tea"
msgstr "Green Peppers"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Agrupado por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_by
msgid "Group Journal Items"
msgstr "Agrupar Apuntes Diarios"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.guinness
#: model:product.template,name:point_of_sale.guinness_product_template
msgid "Guinness"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1181
#, python-format
msgid "Hardware Events"
msgstr "Eventos de Hardware"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Hardware Proxy / PosBox"
msgstr "Proxy Hardware / PosBox"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1177
#, python-format
msgid "Hardware Status"
msgstr "Estado de Hardware"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_control
msgid "Has Cash Control"
msgstr "Tiene Control de Efectivo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Header"
msgstr "Encabezado"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.hot
msgid "Hot Drinks"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/popups.js:113
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "IMPORTANTE: Reporte de Error del Punto de Venta de Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_proxy_ip
msgid "IP Address"
msgstr "Direccion IP"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.ice_tea
#: model:product.template,name:point_of_sale.ice_tea_product_template
msgid "Ice Tea"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"If you put a photo on the category, the layout of the\n"
"                touchscreen interface will automatically. We suggest not to "
"put\n"
"                a photo on categories for small (1024x768) screens."
msgstr ""
"Si pones una imagen en la categoría, el diseño de la interfaz\n"
"                táctil automáticamente se ajustara. Sugerimos no poner "
"imagen\n"
"                a las categorías en pantallas pequeñas (1024x768)."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image
msgid "Image"
msgstr "Imagen"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.alcohol
#, fuzzy
msgid "Import Drinks"
msgstr "Importar Pedidos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1171
#, python-format
msgid "Import Orders"
msgstr "Importar Pedidos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_data_drinks
msgid "Import common drinks data"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
#, fuzzy
msgid "Importable Point of Sales Data"
msgstr "Punto de Venta Inicial"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tomate_en_grappe
#: model:product.template,name:point_of_sale.tomate_en_grappe_product_template
msgid "In Cluster Tomatoes"
msgstr "In Cluster Tomatoes"

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "In Progress"
msgstr "En Progreso"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:406
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "Para poder eliminar una venta, ésta debe ser nueva o cancelada."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
#: selection:pos.config,state:0
msgid "Inactive"
msgstr "Inactivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_tax_included
msgid "Include Taxes in Prices"
msgstr "Incluir Impuestos en Precios"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:266
#, python-format
msgid "Incorrect Password"
msgstr "Contraseña Incorrecta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_note
msgid "Internal Notes"
msgstr "Notas Internas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:625
#: model:ir.actions.report.xml,name:point_of_sale.pos_invoice_report
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_invoice_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Factura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Invoiced"
msgstr "Facturada"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_invoicing
msgid "Invoicing"
msgstr "Facturacion"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.irish_coffee
#: model:product.template,name:point_of_sale.irish_coffee_product_template
msgid "Irish Coffee"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_bank_statement_account_id
msgid "It acts as a default account for debit amount"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.jack_daniels
#: model:product.template,name:point_of_sale.jack_daniels_product_template
msgid "Jack Daniel's"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.jim_beam
#: model:product.template,name:point_of_sale.jim_beam_product_template
msgid "Jim Beam"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_jonagold
#: model:product.template,name:point_of_sale.pomme_jonagold_product_template
msgid "Jonagold apples"
msgstr "Jonagold apples"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_journal_id
msgid "Journal"
msgstr "Diario"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_account_move
msgid "Journal Entry"
msgstr "Asiento Contable"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Journals"
msgstr "Diarios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Barras de Desplazamiento Largas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_invoice___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_statement___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_point_of_sale_report_usersproduct___last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order___last_update
msgid "Last Modified on"
msgstr "Ultima Modificación"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_write_uid
msgid "Last Updated by"
msgstr "Actualizado última vez por"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_discount_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_open_statement_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_write_date
msgid "Last Updated on"
msgstr "Ultima Actualización"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_last_session_closing_cash
msgid "Last session closing cash"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_last_session_closing_date
msgid "Last session closing date"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poireaux_poireaux
#: model:product.template,name:point_of_sale.poireaux_poireaux_product_template
msgid "Leeks"
msgstr "Leeks"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.citron
#: model:product.template,name:point_of_sale.citron_product_template
msgid "Lemon"
msgstr "Lemon"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_name
msgid "Line No"
msgstr "Línea No"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Lines of Point of Sale"
msgstr "Líneas de Punto de Venta"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:33
#, python-format
msgid "List of Cash Registers"
msgstr "Lista de Cajas Registradoras"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:484
#: code:addons/point_of_sale/static/src/xml/pos.xml:38
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_location_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_location_id
msgid "Location"
msgstr "Lugar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_login_number
msgid "Login Sequence Number"
msgstr "Numero de Secuencia de Ingreso"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:293
#, python-format
msgid "Login as a Manager"
msgstr "Ingresar como Administrador"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_loyalty
msgid "Loyalty Program"
msgstr "Programa de Fidelización"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Realizar Pago"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.malibu
#: model:product.template,name:point_of_sale.malibu_product_template
msgid "Malibu"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Manage loyalty program with points and rewards for customers"
msgstr ""
"Administrar programa de fidelización con puntos y recompensas para clientes"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Manager"
msgstr "Jefe de Área"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.manhattan
#: model:product.template,name:point_of_sale.manhattan_product_template
msgid "Manhattan"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.margarita
#: model:product.template,name:point_of_sale.margarita_product_template
msgid "Margarita"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.martini
#: model:product.product,name:point_of_sale.martini_cocktail
#: model:product.template,name:point_of_sale.martini_cocktail_product_template
#: model:product.template,name:point_of_sale.martini_product_template
msgid "Martini"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image_medium
msgid "Medium-sized image"
msgstr "Imagen de tamaño mediano"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image_medium
msgid ""
"Medium-sized image of the category. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Imagen de tamaño mediano de la categoría. Se redimensiona de forma "
"automática como una imagen 128x128px, se mantiene el ratio de aspecto. Use "
"este campo en vista de formulario y kanban."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:494
#, python-format
msgid "Method"
msgstr "Metodo"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_banana
#: model:product.template,name:point_of_sale.milkshake_banana_product_template
msgid "Milkshake Banana"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_cherry
#: model:product.template,name:point_of_sale.milkshake_cherry_product_template
msgid "Milkshake Cherry"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_chocolate
#: model:product.template,name:point_of_sale.milkshake_chocolate_product_template
msgid "Milkshake Chocolate"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_strawberry
#: model:product.template,name:point_of_sale.milkshake_strawberry_product_template
msgid "Milkshake Strawberry"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.milkshake_vanilla
#: model:product.template,name:point_of_sale.milkshake_vanilla_product_template
msgid "Milkshake Vanilla"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.milkshake
msgid "Milkshakes"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.miller
#: model:product.template,name:point_of_sale.miller_product_template
msgid "Miller"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.minute_maid
#: model:product.template,name:point_of_sale.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Miscellaneous"
msgstr "Misceláneos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.mojito
#: model:product.template,name:point_of_sale.mojito_product_template
msgid "Mojito"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Month of order date"
msgstr "Mes de fecha de pedido"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr "Más <i class=\"fa fa-caret-down\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "My Sales"
msgstr "Mis Ventas"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:387
#: code:addons/point_of_sale/static/src/xml/pos.xml:398
#: code:addons/point_of_sale/static/src/xml/pos.xml:407
#, python-format
msgid "N/A"
msgstr "N/A"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:310
#: code:addons/point_of_sale/static/src/xml/pos.xml:451
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
#, python-format
msgid "Name"
msgstr "Nombre"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1847
#, python-format
msgid "Negative Bank Payment"
msgstr "Pago Bancario Negativo"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.nescafe
#: model:product.template,name:point_of_sale.nescafe_product_template
msgid "Nescafe"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "New"
msgstr "Nuevo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Nueva Sesion"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:650
#, python-format
msgid "Next Order"
msgstr "Siguiente Pedido"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:134
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"No se ha encontrado ningún extracto de caja para esta sesión. Imposible "
"registrar el efectivo devuelto."

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_mercury:0
msgid "No credit card"
msgstr "Sin tarjeta de credito"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:25
#, python-format
msgid "No link to an invoice for %s."
msgstr "Sin enlace a una factura para %s."

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_reprint:0
msgid "No reprint"
msgstr "No reimprimir"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:24
#, python-format
msgid "No sequence defined on the journal"
msgstr "No hay secuencia definida en el diario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1962
#: code:addons/point_of_sale/static/src/xml/pos.xml:332
#, python-format
msgid "None"
msgstr "Ninguno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "No Facturada"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"Note that you may use the menu <i>Your Session</i>\n"
"                to quickly open a new session."
msgstr ""
"Tenga en cuenta que usted puede utilizar el menú <i>Su sesión</i>\n"
"                para abrir rápidamente una nueva sesión."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "Notas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_nb_print
msgid "Number of Print"
msgstr "Número de Impresiones"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.index
msgid "Odoo POS"
msgstr "PdV de Odoo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:388
#, python-format
msgid "Offline"
msgstr "Desconectado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:317
#, python-format
msgid "Offline Orders"
msgstr "Pedido Desconetado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:838
#: code:addons/point_of_sale/static/src/xml/pos.xml:852
#: code:addons/point_of_sale/static/src/xml/pos.xml:866
#: code:addons/point_of_sale/static/src/xml/pos.xml:892
#: code:addons/point_of_sale/static/src/xml/pos.xml:923
#: code:addons/point_of_sale/static/src/xml/pos.xml:940
#: code:addons/point_of_sale/static/src/xml/pos.xml:1018
#: code:addons/point_of_sale/static/src/xml/pos.xml:1054
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.Onions
#: model:product.template,name:point_of_sale.Onions_product_template
msgid "Onions"
msgstr "Onions"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1245
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported"
msgstr "Sólo se soportan formatos compatibles web como .png o .jpeg"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Open"
msgstr "Abrir"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_open_statement
msgid "Open Cash Register"
msgstr "Abrir Caja Registradora"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:636
#: code:addons/point_of_sale/static/src/xml/pos.xml:1183
#, python-format
msgid "Open Cashbox"
msgstr "Abrir Gaveta de Dinero"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr "Abrir Menu de PdV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Registers"
msgstr "Abrir Registros"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Open Session"
msgstr "Abrir Sesión"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_open_statement
#: model:ir.model,name:point_of_sale.model_pos_open_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid "Open Statements"
msgstr "Abrir Extractos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Opening Balance"
msgstr "Saldo de Apertura"

#. module: point_of_sale
#: selection:pos.session,state:0
msgid "Opening Control"
msgstr "Control de Apertura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_start_at
msgid "Opening Date"
msgstr "Fecha de Apertura"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.papillon_orange
#: model:product.template,name:point_of_sale.papillon_orange_product_template
msgid "Orange Butterfly"
msgstr "Orange Butterfly"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:282
#, python-format
msgid "Order"
msgstr "Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:1515
#: code:addons/point_of_sale/static/src/js/models.js:1546
#, python-format
msgid "Order "
msgstr "Pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_date_order
msgid "Order Date"
msgstr "Fecha de Pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_sequence_id
msgid "Order IDs Sequence"
msgstr "Secuencia de IDs de Pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_lines
msgid "Order Lines"
msgstr "Líneas de Pedido"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Month"
msgstr "Mes del Pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_name
msgid "Order Ref"
msgstr "Referencia del Pedido"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_sequence_number
msgid "Order Sequence Number"
msgstr "Número de Secuencia del Pedido"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Líneas del Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1164
#: model:ir.actions.act_window,name:point_of_sale.act_pos_session_orders
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Orders"
msgstr "Pedidos"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Analisis de Pedidos"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:180
#, fuzzy, python-format
msgid "POS Order %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "Línea de Pedido de PDV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "Líneas de Pedido de PDV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "Pedidos de PDV"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "Líneas de Pedido de PDV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:191
#, fuzzy, python-format
msgid "POS order line %s"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "POS ordered created during current year"
msgstr "Pedidos creados con PDV durante el año actual"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line_pos_statement_id
msgid "POS statement"
msgstr "Extracto de PDV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_paid
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Paid"
msgstr "Pagado"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_parent_id
msgid "Parent Category"
msgstr "Categoria Padre"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Partner"
msgstr "Socio"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.partner_services
msgid "Partner Services"
msgstr "Servicios de Partners"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:263
#, python-format
msgid "Password ?"
msgstr "¿Contraseña?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Pagar Pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:121
#: code:addons/point_of_sale/static/src/xml/pos.xml:590
#: code:addons/point_of_sale/wizard/pos_payment.py:51
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_payment_date
msgid "Payment Date"
msgstr "Fecha de Pago"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_account_journal_form
#: model:ir.ui.menu,name:point_of_sale.menu_action_account_journal_form_open
msgid "Payment Methods"
msgstr "Metodos de Pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_journal_id
msgid "Payment Mode"
msgstr "Forma de Pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment_payment_name
msgid "Payment Reference"
msgstr "Referencia de Pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_payment_terminal
msgid "Payment Terminal"
msgstr "Terminal de Pago"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_account_journal_form
msgid ""
"Payment methods are defined by accounting journals having the\n"
"                field <i>PoS Payment Method</i> checked. In order to be "
"useable\n"
"                from the touchscreen interface, you must set the payment "
"method\n"
"                on the <i>Point of Sale</i> configuration."
msgstr ""
"Formas de pago se definen por las revistas de contabilidad que tienen el\n"
"                campo <i>Método Pago PDV</i> marcado. Con el fin de ser "
"usado\n"
"                desde la interfaz de pantalla táctil, debe establecer el "
"método\n"
"                pago en la configuracion del <i>Punto de Venta</i>."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_statement_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Payments"
msgstr "Pagos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.peche
#: model:product.template,name:point_of_sale.peche_product_template
msgid "Peaches"
msgstr "Peaches"

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
#, fuzzy
msgid "Per session"
msgstr "Nueva Sesion"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Persona que usa la caja registradora. Puede ser un reemplazo, un estudiante "
"o un empleado interno."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:347
#: code:addons/point_of_sale/static/src/xml/pos.xml:382
#: code:addons/point_of_sale/static/src/xml/pos.xml:453
#, python-format
msgid "Phone"
msgstr "Telefono"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1261
#, python-format
msgid "Phone:"
msgstr "Telefono:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_picking_id
msgid "Picking"
msgstr "Picking"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_picking_type_id
msgid "Picking Type"
msgstr "Tipo de Picking"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pina_colada
#: model:product.template,name:point_of_sale.pina_colada_product_template
msgid "Pina Colada"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1876
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Por Favor Confirme la Cantidad Grande"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:272
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr ""
"Por favor favor la cuenta de ingresos para este producto: \"%s\"(id:%d)."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:437
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Por favor proporcione un partner para la venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:474
#, python-format
msgid "Please select a payment method."
msgstr "Por favor seleccione un metodo de pago."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1908
#, python-format
msgid "Please select the Customer"
msgstr "Por favor seleccione el Cliente"

#. module: point_of_sale
#: model:stock.picking.type,name:point_of_sale.picking_type_posout
msgid "PoS Orders"
msgstr "Pedidos de PdV"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model,name:point_of_sale.model_pos_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_users_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_sale_config_settings_form_pos
msgid "Point of Sale"
msgstr "Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Analisis de Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_pos_categ_id
msgid "Point of Sale Category"
msgstr "Categoria de Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Config de Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Configuración de Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Grupo de Administradores de Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_name
msgid "Point of Sale Name"
msgstr "Nombre de Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Pedidos de Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Statistics"
msgstr "Estadisticas de Pedidos de Punto de Venta"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Payment"
msgstr "Pago de Punto de Venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Sesion de Punto de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Grupo de Usuarios de Punto de Venta"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_restaurant:0
msgid "Point of sale for shops"
msgstr "Punto de Venta para tiendas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Categories"
msgstr "Categorias de PdV"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_product_pos_category
msgid "Pos Product Categories"
msgstr "Categorias de Producto de PdV"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_pos_session_username
msgid "Pos session username"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:326
#, python-format
msgid "Postcode"
msgstr "Codigo Postal"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: selection:pos.order,state:0 selection:report.pos.order,state:0
msgid "Posted"
msgstr "Contabilizado"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pomme_de_terre
#: model:product.template,name:point_of_sale.pomme_de_terre_product_template
msgid "Potatoes"
msgstr "Potatoes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_precompute_cash
msgid "Prefill Cash Payment"
msgstr "Llenado Previo de Pago en Efectivo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:144
#, python-format
msgid "Price"
msgstr "Precio"

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:16
#, python-format
msgid "Priced Product"
msgstr "Producto con Precio"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_pricelist_id
msgid "Pricelist"
msgstr "Tarifa"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:657
#: code:addons/point_of_sale/static/src/xml/pos.xml:1184
#, python-format
msgid "Print Receipt"
msgstr "Imprimir Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Imprimir via Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:377
#, python-format
msgid "Printer"
msgstr "Impresora"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/devices.js:425
#, python-format
msgid "Printing Error: "
msgstr "Error de Impresion:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Categorías de Producto de Producto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_qty
msgid "Product Quantity"
msgstr "Cantidad de Producto"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de Producto"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Productos"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_pos_categ_id
msgid "Public Category"
msgstr "Categoría Publica"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_in
msgid "Put Money In"
msgstr "Poner Dinero"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:134
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
#, python-format
msgid "Qty"
msgstr "Cant."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Re-Print"
msgstr "Re-Imprimir"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1185
#, python-format
msgid "Read Weighting Scale"
msgstr "Leer Peso de Balanza"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Real Closing Balance"
msgstr "Saldo de Cierre Real"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Receipt"
msgstr "Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_receipt_footer
msgid "Receipt Footer"
msgstr "Pie del Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_receipt_header
msgid "Receipt Header"
msgstr "Cabecera del Recibo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Receipt Printer"
msgstr "Impresora del Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_pos_reference
msgid "Receipt Ref"
msgstr "Referencia del Recibo"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.red_label
#: model:product.template,name:point_of_sale.red_label_product_template
#, fuzzy
msgid "Red Label"
msgstr "Usar Etiquetas"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_rouges
#: model:product.template,name:point_of_sale.poivron_rouges_product_template
msgid "Red Pepper"
msgstr "Red Pepper"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.red_wine
#: model:product.template,name:point_of_sale.red_wine_product_template
msgid "Red Wine"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.pamplemousse_rouge_pamplemousse
#: model:product.template,name:point_of_sale.pamplemousse_rouge_pamplemousse_product_template
msgid "Red grapefruit"
msgstr "Red grapefruit"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.redbull
#: model:product.template,name:point_of_sale.redbull_product_template
msgid "RedBull"
msgstr ""

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reports"
msgstr "Reportes"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_reprint
msgid "Reprints"
msgstr "Reimpreciones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1154
#, python-format
msgid "Reset"
msgstr "Restablecer"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_settings_module_pos_restaurant
msgid "Restaurant"
msgstr "Restaurante"

#. module: point_of_sale
#: selection:pos.config.settings,module_pos_restaurant:0
msgid "Restaurant: activate table management"
msgstr "Restaurante: activar gestion de mesas"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Resume"
msgstr "Reanudar"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:652
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Devolver Productos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_return
msgid "Returned"
msgstr "Devueltos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.rose_wine
#: model:product.template,name:point_of_sale.rose_wine_product_template
msgid "Rose Wine"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.safari
#: model:product.template,name:point_of_sale.safari_product_template
msgid "Safari"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_sale_journal
msgid "Sale Journal"
msgstr "Diario de Ventas"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Línea de venta"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sales Orders"
msgstr "Pedidos de Ventas"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Salesman"
msgstr "Vendedor"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Salesperson"
msgstr "Vendedor"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:385
#, python-format
msgid "Scale"
msgstr "Balanza"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1160
#, python-format
msgid "Scan"
msgstr "Escanear"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1161
#, python-format
msgid "Scan EAN-13"
msgstr "Escanear EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Escanear a traves de Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:368
#, python-format
msgid "Scanner"
msgstr "Escaner"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.schweppes
#: model:product.template,name:point_of_sale.schweppes_product_template
msgid "Schweppes"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:424
#, python-format
msgid "Search Customers"
msgstr "Buscar Clientes"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:191
#, python-format
msgid "Search Products"
msgstr "Buscar Productos"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Buscar Pedidos de Venta"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_pos_security_pin
msgid "Security PIN"
msgstr "PIN de Seguridad"

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_users.py:15
#, python-format
msgid "Security PIN can only contain digits"
msgstr "El PIN de Seguridad solo puede contener digitos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:432
#, python-format
msgid "Select Customer"
msgstr "Seleccionar Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:237
#, python-format
msgid "Select User"
msgstr "Seleccionar Usuario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1973
#, python-format
msgid "Select tax"
msgstr "Seleccionar impuesto"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:202
#, python-format
msgid "Selected orders do not have the same session!"
msgstr "¡Los pedidos seleccionados no tienen la misma sesión!"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_sequence_number
msgid "Sequence Number"
msgstr "Número de Secuencia"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:704
#, python-format
msgid "Served by"
msgstr "Atendido por"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1921
#, python-format
msgid "Server Error"
msgstr "Error de Servidor"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:249
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Session"
msgstr "Sesion"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_name
msgid "Session ID"
msgstr "ID de Sesion"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1043
#, python-format
msgid "Session ids:"
msgstr "Ids de la Sesion:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Session:"
msgstr "Sesion:"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.act_pos_config_sessions
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Sesiones"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1114
#, python-format
msgid "Set Customer"
msgstr "Establecer Cliente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1153
#, python-format
msgid "Set Weight"
msgstr "Establecer Peso"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Active"
msgstr "Establecer como Activo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Deprecated"
msgstr "Establecer como Obsoleto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_form
msgid "Set to Inactive"
msgstr "Establecer como Inactivo"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Configuración"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1263
#, python-format
msgid "Shop:"
msgstr "Tienda:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:42
#, python-format
msgid "Skip"
msgstr "Saltar"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_print_skip_screen
msgid "Skip Receipt Screen"
msgstr "Saltar Pantalla de Recibo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category_image_small
msgid "Small-sized image"
msgstr "Imagen de tamaño pequeño"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image_small
msgid ""
"Small-sized image of the category. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""
"Imagen de tamaño pequeño de la categoría. Se redimensiona de forma "
"automatica como una imagen de 64x64px, con un ratio de aspecto preservado. "
"Use este campo donde quiera que una imagen pequeña se requiera."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.smirnoff
#: model:product.template,name:point_of_sale.smirnoff_product_template
msgid "Smirnoff"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.soft
msgid "Soft Drinks"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/gui.js:322
#, python-format
msgid "Some orders could not be submitted to"
msgstr "Algunos pedidos no se pudieron registrar"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.sprite
#: model:product.template,name:point_of_sale.sprite_product_template
msgid "Sprite"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_start_categ_id
msgid "Start Category"
msgstr "Categoria Inicial"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_start
msgid "Starting Balance"
msgstr "Saldo Inicial"

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.action_report_account_statement
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_statement
msgid "Statement"
msgstr "Extracto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Statement lines"
msgstr "Líneas del Extracto"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Statements"
msgstr "Extractos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Status"
msgstr "Estado"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.stella_artois
#: model:product.template,name:point_of_sale.stella_artois_product_template
#, fuzzy
msgid "Stella Artois"
msgstr "Stella Artois 33cl"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_stock_location_id
msgid "Stock Location"
msgstr "Ubicacion de las Existencias"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:318
#: code:addons/point_of_sale/static/src/xml/pos.xml:319
#, python-format
msgid "Street"
msgstr "Calle"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.limon
#: model:product.template,name:point_of_sale.limon_product_template
msgid "Stringers"
msgstr "Stringers"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:752
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Subtotal sin Impuestos"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_price_sub_total
msgid "Subtotal w/o discount"
msgstr "Subtotal sin Descuentos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1298
#, python-format
msgid "Subtotal:"
msgstr "Subtotal:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1035
#, python-format
msgid "Successfully  imported"
msgstr "Importado satisfactoriamente"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1034
#, python-format
msgid "Successfully imported"
msgstr "Importado satisfactoriamente"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr "Suma de saldos de apertura y transacciones."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Suma de subtotales"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Summary by Payment Methods"
msgstr "Resumen por Metodos de Pago"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_users_property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Moneda del Proveedor"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:765
#, python-format
msgid "TOTAL"
msgstr "TOTAL"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money Out"
msgstr "Sacar Dinero"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:300
#: code:addons/point_of_sale/static/src/js/screens.js:1983
#, python-format
msgid "Tax"
msgstr "Impuesto"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:355
#: code:addons/point_of_sale/static/src/xml/pos.xml:402
#, python-format
msgid "Tax ID"
msgstr "ID de Impuesto"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_tax_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_tax_ids_after_fiscal_position
msgid "Taxes"
msgstr "Impuestos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1133
#, python-format
msgid "Taxes:"
msgstr "Impuestos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:684
#, python-format
msgid "Tel:"
msgstr "Tel:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:492
#, python-format
msgid "Tendered"
msgstr "Atendido"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tequila
#: model:product.template,name:point_of_sale.tequila_product_template
msgid "Tequila"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:260
#, python-format
msgid "The POS order must have lines when calling this method"
msgstr "El pedido del PDV debe tener líneas cuando se llame a este método"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:887
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee\n"
"                    or action associated with the scanned barcode."
msgstr ""
"El Punto de Venta no pudo encontrar ningun producto, cliente, empleado\n"
"                    o accion asociada con el codigo de barras escaneado."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:150
#, python-format
msgid ""
"The company of a payment method is different than the one of point of sale"
msgstr ""
"La compañia de un metodo de pago es diferente que la del punto de venta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:145
#, python-format
msgid ""
"The company of the sale journal is different than the one of point of sale"
msgstr ""
"La compañía del diario de ventas es diferente que la del punto de venta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:140
#, python-format
msgid ""
"The company of the stock location is different than the one of point of sale"
msgstr ""
"La compañía de la ubicación de existencias es diferente que la del punto de "
"venta"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:155
#, python-format
msgid ""
"The default fiscal position must be included in the available fiscal "
"positions of the point of sale"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_tax_included
msgid ""
"The displayed prices will always include all taxes, even if the taxes have "
"been setup differently"
msgstr ""
"Los precios mostrados siempre incluiran impuestos, aun si los impuestos han "
"sido configurados de forma distinta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty"
msgstr ""
"El nombre de equipo o la dirección IP del proxy hardware. Será autodetectado "
"si se deja vacío"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:93
#: sql_constraint:pos.session:0
#, python-format
msgid "The name of this POS Session must be unique !"
msgstr "¡El nombre de esta Sesión de PDV debe ser único!"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1916
#, python-format
msgid "The order could not be sent"
msgstr "El pedido no pudo ser enviado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1927
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr ""
"El pedido no pudo ser enviado al servidor debido a un error desconocido"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_precompute_cash
msgid ""
"The payment input will behave similarily to bank payment input, and will be "
"prefilled with the exact due amount"
msgstr ""
"El ingreso del pago se comportara de forma similar a un deposito bancario y "
"se llenara con el monto adeudado exacto"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session_config_id
msgid "The physical point of sale you will use."
msgstr "El punto de venta fisico que usted va a utilizar"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown"
msgstr ""
"El punto de venta mostrará esta categoría de producto por defecto. Si no se "
"especifica ninguna categoría, todos los productos disponibles se mostrarán"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr "Las categorias de productos se mostrarán con imagenes."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_tip_product_id
msgid ""
"The product used to encode the customer tip. Leave empty if you do not "
"accept tips."
msgstr ""
"El producto utilizado para codificar la propina del cliente. Dejar en blanco "
"si usted no acepta propinas."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1260
#, python-format
msgid "The provided file could not be read due to an unknown error"
msgstr "El archivo provisto no se puede leer debido a un error desconocido"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Se omitirá la pantalla de recibo si el recibo se puede imprimir de forma "
"automática."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_iface_print_auto
#, fuzzy
msgid "The receipt will automatically be p-rinted at the end of each order"
msgstr "El recibo de imprimirá de forma automaticá al final de cada pedido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1922
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "El servidor encontró un error mientras recibía su pedido."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_open_statement
msgid ""
"The system will open all cash registers, so that you can start recording "
"payments. We suggest you to control the opening balance of each register, "
"using their CashBox tab."
msgstr ""
"El sistema abrirá todos los registros de caja, por lo que puedes empezar a "
"realizar pagos. Te sugerimos que controles el balance abierto de cada "
"registro, usando su pestaña Caja."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_mercury
msgid "The transactions are processed by MercuryPay"
msgstr "Las transacciones son procesadas por MercuryPay"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:215
#, python-format
msgid "The type of the journal for your payment method should be bank or cash "
msgstr "El tipo de diario para el método de pago debe ser banco o efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Saldo de Cierre Teorico"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:94
#, python-format
msgid ""
"There are pending operations that could not be saved into the database, are "
"you sure you want to exit?"
msgstr ""
"Hay operaciones pendientes que podrían no ser guardadas en la base de datos, "
"¿está seguro que desea salir?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1867
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle "
"the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of "
"sale configuration"
msgstr ""
"No hay ningún método de pago de caja disponible en este PDV para calcular el "
"vuelto.\n"
"\n"
" Por favor pague el importe exacto o añada un método de pago en efectivo en "
"la configuración del PDV"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:21
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr "No existe una caja registradora por esta Sesión de PdV"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:607
#, python-format
msgid ""
"There is no receivable account defined to make payment for the partner: \"%s"
"\" (id:%d)."
msgstr ""
"No hay una cuenta contable por cobrar definida para hacer el pago de esta "
"empresa: \"%s\" (id:%d)."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:605
#, python-format
msgid "There is no receivable account defined to make payment."
msgstr "No hay una cuenta contable por cobrar definida para hacer el pago."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1838
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated"
msgstr ""
"Debe haber al menos un producto en su pedido antes de que pueda ser validao"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""
"Esta moneda será usada, en vez de la prederminada, para las compras del "
"partner actual"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_users_property_delivery_carrier_id
msgid "This delivery method will be used when invoicing from picking."
msgstr ""
"Este método de entrega será utilizado cuando se facture a partir del picking."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_account_journal_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance "
"and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Este campo representa la diferencia máxima permitida entre el saldo final y "
"el efectivo teórico al cerrar una sesión, para usuarios no administradores "
"de PDV. Si se alcanza este máximo, el usuario tendrá un mensaje de error en "
"el cierre de su sesión diciendo que él tiene que ponerse en contacto con su "
"administrador."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category_image
msgid ""
"This field holds the image used as image for the cateogry, limited to "
"1024x1024px."
msgstr ""
"Este campo contiene la imagen usada como imagen de la categoría, limitada a "
"1024x1024px."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client"
msgstr ""
"Este campo está allí para pasar el id del administrador de grupo de pdv  al "
"cliente del punto de venta hasta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client"
msgstr ""
"Este campo está allí para pasar el id del administrador de grupo de pdv  al "
"cliente del punto de venta hasta"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_settings_module_pos_restaurant
msgid ""
"This module adds several restaurant features to the Point of Sale: \n"
"\n"
"- Bill Printing: Allows you to print a receipt before the order is paid \n"
"\n"
"- Bill Splitting: Allows you to split an order into different orders \n"
"\n"
"- Kitchen Order Printing: allows you to print orders updates to kitchen or "
"bar printers"
msgstr ""
"Este módulo agrega varias características de restaurantes para el Punto de "
"Venta: \n"
"\n"
"- Imprimir Recibo: Permite imprimir un recibo antes de que se pague la "
"orden \n"
"\n"
"- Dividir Cuenta: Permite dividir una orden en diferentes órdenes\n"
"\n"
"- Imprimir Orden en Cocina: permite imprimir órdenes de cambios a impresoras "
"de cocina o barra"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:262
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Esta operación destruirá permanentemente todos los pedidos pagados del "
"almacenamiento local. Perderá todos los datos. Esta operación no se puede "
"deshacer."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:272
#, python-format
msgid ""
"This operation will permanently destroy all unpaid orders from all sessions "
"that have been put in the local storage. You will lose all the data and exit "
"the point of sale. This operation cannot be undone."
msgstr ""
"Esta operación destruirá permanentemente todos los pedidos pendientes de "
"pago de todas las sesiones que se han puesto en el almacenamiento local. "
"Perderá todos los datos y saldrá del punto de venta. Esta operación no se "
"puede deshacer."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Esta secuencia es creada automáticamente por Odoo pero puede cambiarla para "
"personalizar la referencia de los números de sus pedidos."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_template_pos_categ_id
msgid "Those categories are used to group similar products for point of sale."
msgstr ""
"Esas categorías son usadas para grupos similares de productos para el punto "
"de venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:631
#, python-format
msgid "Tip"
msgstr "Propina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_tip_product_id
msgid "Tip Product"
msgstr "Producto de Propina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_template_to_weight
msgid "To Weigh With Scale"
msgstr "Para Pesar con Balanza"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:638
#, python-format
msgid ""
"To return product(s), you need to open a session that will be used to "
"register the refund."
msgstr ""
"Para devolver producto(s), usted necesita abrir una sesión que se usará para "
"registrar el reembolso."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Today"
msgstr "Hoy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_amount_total
msgid "Total"
msgstr "Total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session_cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Transacción Total de Efectivo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_total_discount
msgid "Total Discount"
msgstr "Descuento Total"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_price_total
msgid "Total Price"
msgstr "Precio Total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:802
#, python-format
msgid "Total Taxes"
msgstr "Impuestos Totales"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_total_entry_encoding
msgid "Total of all paid sale orders"
msgstr "Total de todos los pedidos de venta pagados"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr "Total de las líneas de cierre del control de caja."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session_cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr "Total de las líneas de apertura del control de caja."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Cant Total"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1132
#: code:addons/point_of_sale/static/src/xml/pos.xml:1318
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:312
#, python-format
msgid "Trade Receivables"
msgstr "Créditos por ventas"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tuborg
#: model:product.template,name:point_of_sale.tuborg_product_template
msgid "Tuborg"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:134
#, python-format
msgid ""
"Unable to open the session. You have to assign a sale journal to your point "
"of sale."
msgstr ""
"Imposible abrir la sesión. Debe asignar un diario de ventas a su punto de "
"venta."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line_price_unit
msgid "Unit Price"
msgstr "Precio Unitario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:883
#, python-format
msgid "Unknown Barcode"
msgstr "Código de Barras Desconocido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1926
#, python-format
msgid "Unknown Error"
msgstr "Error Desconocido"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1244
#, python-format
msgid "Unsupported File Format"
msgstr "Formato de Archivo no Soportado"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid ""
"Use this menu to browse previous orders. To record new\n"
"                orders, you may use the menu <i>Your Session</i> for\n"
"                the touchscreen interface."
msgstr ""
"Utilice este menú para navegar a pedidos anteriores. Para grabar nuevos\n"
"                pedidos, puede usar el menú <i>Su Sesión</i> para\n"
"                la interfaz de pantalla táctil."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model:res.groups,name:point_of_sale.group_pos_user
msgid "User"
msgstr "Usuario"

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Usar Etiquetas"

#. module: point_of_sale
#: model:ir.actions.report.xml,name:point_of_sale.action_report_pos_users_product
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_usersproduct
msgid "User's Product"
msgstr "Producto de Usuario"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1262
#, python-format
msgid "User:"
msgstr "Usuario:"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_uuid
msgid "Uuid"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:687
#, python-format
msgid "VAT:"
msgstr "RUC:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:591
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Validate Closing & Post Entries"
msgstr "Validar Cierre y Contabilizar Asientos"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_6
#: model:product.template,name:point_of_sale.partner_product_6_product_template
msgid "Vauxoo.com"
msgstr "Vauxoo.com"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config_iface_vkeyboard
msgid "Virtual KeyBoard"
msgstr "Teclado Virtual"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order_stock_location_id
msgid "Warehouse"
msgstr "Almacén"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.water
#: model:product.template,name:point_of_sale.water_product_template
msgid "Water"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/barcode_rule.py:15
#, python-format
msgid "Weighted Product"
msgstr "Producto Pesado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1179
#, python-format
msgid "Weighting"
msgstr "Pesando"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.white_russian
#: model:product.template,name:point_of_sale.white_russian_product_template
msgid "White Russian"
msgstr ""

#. module: point_of_sale
#: model:product.product,name:point_of_sale.white_wine
#: model:product.template,name:point_of_sale.white_wine_product_template
msgid "White Wine"
msgstr ""

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.wine
msgid "Wine"
msgstr ""

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1105
#: code:addons/point_of_sale/static/src/xml/pos.xml:1282
#, python-format
msgid "With a"
msgstr "Con un"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Year"
msgstr "Año"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.poivron_jaunes
#: model:product.template,name:point_of_sale.poivron_jaunes_product_template
msgid "Yellow Peppers"
msgstr "Yellow Peppers"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.tea
#: model:product.template,name:point_of_sale.tea_product_template
#, fuzzy
msgid "Yellow Tea"
msgstr "Yellow Peppers"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid ""
"You can define another list of available currencies on the\n"
"                                <i>Cash Registers</i> tab of the"
msgstr ""

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:396
#, python-format
msgid ""
"You cannot change the partner of a POS order for which an invoice has "
"already been issued."
msgstr ""
"No puede cambiar el cliente de un pedido de PDV para el que ya se ha emitido "
"una factura."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:25
#, python-format
msgid ""
"You cannot confirm all orders of this session, because they have not the "
"'paid' status"
msgstr ""
"No puede confirmar todos los pedidos de esta sesión, porque ellos no tienen "
"el estado 'pagado'"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:117
#, python-format
msgid ""
"You cannot create two active sessions related to the same point of sale!"
msgstr ""
"¡No puede crear dos sesiones activas relaciones con el mismo punto de venta!"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:112
#, python-format
msgid "You cannot create two active sessions with the same responsible!"
msgstr "¡No puede crear dos sesiones activas con el mismo responsable!"

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:21
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"No puede eliminar un producto vendible en el punto de venta mientras alguna "
"sesión siga abierta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1848
#, python-format
msgid ""
"You cannot have a negative amount in a Bank payment. Use a cash payment "
"method to return money to the customer."
msgstr ""
"No puedes tener un monto negativo en un pago Bancario. Use un método de pago "
"en efectivo para devolver dinero al cliente."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:231
#, python-format
msgid ""
"You cannot use the session of another users. This session is owned by %s. "
"Please first close this one to use this point of sale."
msgstr ""
"No puede usar la sesión de otros usuarios. Esta sesión le pertenece a %s. "
"Por favor primero cierre esta sesión para poder usar este punto de venta."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:18
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / "
"Configuration / Journals / Journals\". Select a journal and check the field "
"\"PoS Payment Method\" from the \"Point of Sale\" tab. You can also create "
"new payment methods directly from menu \"PoS Backend / Configuration / "
"Payment Methods\"."
msgstr ""
"Tiene que definir qué método de pago debe estar disponible en el PDV "
"reusando los bancos y cajas existentes a través de \"Contabilidad / "
"Configuración / Diarios / Diarios\". Seleccione un diario y marque la "
"casilla \"Método de pago de PDV\" desde la pestaña PDV. Puede crear también "
"métodos de pago directamente desde el menú \"Backend PDV / Configuración / "
"Métodos de pago\"."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:621
#, python-format
msgid "You have to open at least one cashbox."
msgstr "Debe abrir por lo menos una caja de dinero."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:724
#, python-format
msgid "You have to select a pricelist in the sale form !"
msgstr "¡ Debe seleccionar una tarifa en formulario de venta !"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:712
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"¡ Debe seleccionar una tarifa en formulario de venta !\n"
"Por favor selecciona una antes de escoger algún producto."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_template_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Debe definir un producto para todo lo que usted vende a través\n"
"                de la interfaz de punto de venta."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1909
#, python-format
msgid "You need to select the customer before you can invoice an order."
msgstr "Debe seleccionar el cliente antes de poder facturar un pedido."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:123
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Debería asignar un Punto de Venta a su sesión."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/chrome.js:54
#, python-format
msgid "You will lose any data associated with the current order"
msgstr "Usted perderá cualquier información asociada al pedido actual"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1189
#, python-format
msgid "Your Internet connection is probably down."
msgstr "Su conexión a internet esta probablemente caída."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:213
#, python-format
msgid ""
"Your ending balance is too different from the theoretical cash closing "
"(%.2f), the maximum allowed is: %.2f. You can contact your manager to force "
"it."
msgstr ""
"Su saldo final es demasiado diferente del saldo teórico de cierre (%.2f), el "
"máximo permitido es: %.2f. Puede contactar con su administrador para "
"cerrarlo."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1124
#, python-format
msgid "Your shopping cart is empty"
msgstr "Su carro de compras esta vacío"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:327
#, python-format
msgid "ZIP"
msgstr "Código postal o Distrito"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.courgette
#: model:product.template,name:point_of_sale.courgette_product_template
msgid "Zucchini"
msgstr "Zucchini"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1097
#, python-format
msgid "at"
msgstr "en"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "barcode.rule"
msgstr "barcode.rule"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1040
#, python-format
msgid "belong to another session:"
msgstr "pertenece a otra sesión:"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.partner_product_8
#: model:product.template,name:point_of_sale.partner_product_8_product_template
msgid "camptocamp.com"
msgstr "camptocamp.com"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1385
#, python-format
msgid "caps lock"
msgstr "bloqueo mayúsculas"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_in
msgid "cash.box.in"
msgstr "cash.box.in"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "cash.box.out"
msgstr "cash.box.out"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1412
#: code:addons/point_of_sale/static/src/xml/pos.xml:1454
#, python-format
msgid "close"
msgstr "cerrar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1370
#: code:addons/point_of_sale/static/src/xml/pos.xml:1447
#, python-format
msgid "delete"
msgstr "eliminar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1107
#, python-format
msgid "discount"
msgstr "descuento"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/screens.js:1881
#, python-format
msgid "for an order of"
msgstr "por un pedido de"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:171
#, python-format
msgid "not used"
msgstr "no usado"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1034
#, python-format
msgid "paid orders"
msgstr "pedidos pagados"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, fuzzy
msgid "payment method."
msgstr "Metodos de Pago"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
msgid "pos.config"
msgstr "pos.config"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config_settings
msgid "pos.config.settings"
msgstr "pos.config.settings"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
msgid "pos.session"
msgstr "pos.session"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "report.point_of_sale.report_invoice"
msgstr "report.point_of_sale.report_invoice"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_statement
msgid "report.point_of_sale.report_statement"
msgstr "report.point_of_sale.report_statement"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_usersproduct
msgid "report.point_of_sale.report_usersproduct"
msgstr "report.point_of_sale.report_usersproduct"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_order.py:139
#: code:addons/point_of_sale/static/src/xml/pos.xml:1397
#: code:addons/point_of_sale/static/src/xml/pos.xml:1452
#, python-format
msgid "return"
msgstr "devolver"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1398
#: code:addons/point_of_sale/static/src/xml/pos.xml:1409
#, python-format
msgid "shift"
msgstr "mayúscula"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1371
#, python-format
msgid "tab"
msgstr "pestaña"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1035
#, python-format
msgid "unpaid orders"
msgstr "pedidos sin pagar"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1037
#, python-format
msgid "unpaid orders could not be imported"
msgstr "pedidos sin pagar no pueden ser importados"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/pos.xml:1039
#, python-format
msgid "were duplicates of existing orders"
msgstr "eran duplicados de órdenes existentes"

#~ msgid "&lt;!DOCTYPE html&gt;"
#~ msgstr "&lt;!DOCTYPE html&gt;"

#~ msgid "2L Evian"
#~ msgstr "2L Evian"

#~ msgid "50cl Perrier"
#~ msgstr "50cl Perrier"

#~ msgid ""
#~ "<br/>\n"
#~ "                        Date:"
#~ msgstr ""
#~ "<br/>\n"
#~ "                        Fecha:"

#~ msgid "<strong>Closing Date</strong>:<br/>"
#~ msgstr "<strong>Fecha de Cierre</strong>:<br/>"

#~ msgid ""
#~ "<strong>Company</strong>:\n"
#~ "                            <br/>"
#~ msgstr ""
#~ "<strong>Compañía</strong>:\n"
#~ "                            <br/>"

#~ msgid "<strong>Currency</strong>"
#~ msgstr "<strong>Moneda</strong>"

#~ msgid "<strong>Date</strong>"
#~ msgstr "<strong>Fecha</strong>"

#~ msgid "<strong>Description</strong>"
#~ msgstr "<strong>Descripción</strong>"

#~ msgid "<strong>Difference</strong>"
#~ msgstr "<strong>Diferencia</strong>"

#~ msgid "<strong>Disc(%)</strong>"
#~ msgstr "<strong>Descuento(%)</strong>"

#~ msgid "<strong>Disc. (%)</strong>"
#~ msgstr "<strong>Desc. (%)</strong>"

#~ msgid "<strong>Disc.(%)</strong>"
#~ msgstr "<strong>Desc.(%)</strong>"

#~ msgid "<strong>End Period</strong>:<br/>"
#~ msgstr "<strong>Fin del Periodo</strong>:<br/>"

#~ msgid "<strong>Ending Balance</strong>"
#~ msgstr "<strong>Balance Final</strong>"

#~ msgid "<strong>Invoiced</strong>"
#~ msgstr "<strong>Facturado</strong>"

#~ msgid "<strong>Journal</strong>"
#~ msgstr "<strong>Diario</strong>"

#~ msgid "<strong>Net Total</strong>"
#~ msgstr "<strong>Total Neto</strong>"

#~ msgid "<strong>No. Of Articles</strong>:<br/>"
#~ msgstr "<strong># Artículos</strong>:<br/>"

#~ msgid "<strong>Order</strong>"
#~ msgstr "<strong>Pedido</strong>"

#~ msgid "<strong>Payment</strong>"
#~ msgstr "<strong>Pago</strong>"

#~ msgid "<strong>Point of Sale</strong>:<br/>"
#~ msgstr "<strong>Punto de Venta</strong>:<br/>"

#~ msgid "<strong>Price</strong>"
#~ msgstr "<strong>Precio</strong>"

#~ msgid ""
#~ "<strong>Print date</strong>:\n"
#~ "                            <br/>"
#~ msgstr ""
#~ "<strong>Fecha de Impresión</strong>:\n"
#~ "                            <br/>"

#~ msgid "<strong>Print date</strong>:<br/>"
#~ msgstr "<strong>Fecha de Impresión</strong>:<br/>"

#~ msgid "<strong>Product</strong>"
#~ msgstr "<strong>Producto</strong>"

#~ msgid "<strong>Qty of product</strong>"
#~ msgstr "<strong>Cant. de producto</strong>"

#~ msgid "<strong>Qty</strong>"
#~ msgstr "<strong>Cant</strong>"

#~ msgid "<strong>Quantity</strong>"
#~ msgstr "<strong>Cantidad</strong>"

#~ msgid "<strong>Reference</strong>"
#~ msgstr "<strong>Referencia</strong>"

#~ msgid "<strong>Responsible</strong>:<br/>"
#~ msgstr "<strong>Responsable</strong>:<br/>"

#~ msgid "<strong>Sales total(Revenue)</strong>"
#~ msgstr "<strong>Total de ventas(Ingresos)</strong>"

#~ msgid "<strong>Start Period</strong>:<br/>"
#~ msgstr "<strong>Periodo de Inicio</strong>:<br/>"

#~ msgid "<strong>Starting Balance</strong>"
#~ msgstr "<strong>Balance Inicial</strong>"

#~ msgid "<strong>Status</strong>"
#~ msgstr "<strong>Estado</strong>"

#~ msgid "<strong>Summary</strong>"
#~ msgstr "<strong>Resumen</strong>"

#~ msgid "<strong>Tax</strong>"
#~ msgstr "<strong>Impuesto</strong>"

#~ msgid "<strong>Taxes</strong>"
#~ msgstr "<strong>Impuestos</strong>"

#~ msgid "<strong>Total Transactions</strong>"
#~ msgstr "<strong>Total de Transacciones</strong>"

#~ msgid "<strong>Total Without Taxes</strong>"
#~ msgstr "<strong>Total Sin Impuestos</strong>"

#~ msgid "<strong>Total discount</strong>"
#~ msgstr "<strong>Descuento total</strong>"

#~ msgid "<strong>Total invoiced</strong>"
#~ msgstr "<strong>Total facturado</strong>"

#~ msgid "<strong>Total of the day</strong>"
#~ msgstr "<strong>Total del día</strong>"

#~ msgid "<strong>Total paid</strong>"
#~ msgstr "<strong>Total pagado</strong>"

#~ msgid "<strong>Unit Price</strong>"
#~ msgstr "<strong>Precio Unitario</strong>"

#~ msgid "<strong>Users</strong>:<br/>"
#~ msgstr "<strong>Usuarios</strong>:<br/>"

#~ msgid "Account"
#~ msgstr "Cuenta"

#~ msgid "Activate the customer portal"
#~ msgstr "Activar el portal de cliente"

#~ msgid "Activation"
#~ msgstr "Activación"

#~ msgid "Alcohol"
#~ msgstr "Alcohol"

#~ msgid "Alias Domain"
#~ msgstr "Apodo del Dominio"

#~ msgid "Allow documents sharing"
#~ msgstr "Permitir compartir documentos"

#~ msgid "Allow external users to sign up"
#~ msgstr "Permitir ingresar a usuarios externos"

#~ msgid "Allow the users to synchronize their calendar  with Google Calendar"
#~ msgstr ""
#~ "Permite a los usuarios sincronizar su calendario con el Calendario de "
#~ "Google"

#~ msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
#~ msgstr ""
#~ "Permitir a los usuarios importar datos desde archivos CSV/XLS/XLSX/ODS"

#~ msgid "Allow users to sign in with Google"
#~ msgstr "Permitir a los usuarios ingresar con Google"

#~ msgid "Attach Google documents to any record"
#~ msgstr "Adjuntar documentos de Google a cualquier registro"

#~ msgid "Authorization Code"
#~ msgstr "Código de Autorización"

#~ msgid "Auto-generated session for orphan orders, ignored in constraints"
#~ msgstr ""
#~ "Sesión auto-generada para órdenes huérfanas, ignoradas en las "
#~ "restricciones"

#~ msgid "Belle-Vue Kriek 25cl"
#~ msgstr "Belle-Vue Kriek 25cl"

#~ msgid "Cash Journals"
#~ msgstr "Diarios de Efectivo"

#~ msgid "Chaudfontaine Petillante 1.5l"
#~ msgstr "Chaudfontaine Petillante 1.5l"

#~ msgid "Chimay Red 33cl"
#~ msgstr "Chimay Red 33cl"

#~ msgid "Client_id"
#~ msgstr "Client_id"

#~ msgid "Client_key"
#~ msgstr "Client_key"

#~ msgid "Coca-Cola Light 50cl"
#~ msgstr "Coca-Cola Light 50cl"

#~ msgid "Coca-Cola Light Lemon 2L"
#~ msgstr "Coca-Cola Light Lemon 2L"

#~ msgid "Coca-Cola Regular 33cl"
#~ msgstr "Coca-Cola Regular 33cl"

#~ msgid "Coca-Cola Regular 50cl"
#~ msgstr "Coca-Cola Regular 50cl"

#~ msgid "Could not close the point of sale."
#~ msgstr "No se pudo cerrar el punto de venta."

#~ msgid "Date"
#~ msgstr "Fecha"

#~ msgid "Date End"
#~ msgstr "Fecha Final"

#~ msgid "Date Start"
#~ msgstr "Fecha Inicial"

#~ msgid "Dates"
#~ msgstr "Fechas"

#~ msgid "Default Point of Sale"
#~ msgstr "Punto de Venta por defecto"

#~ msgid "Description"
#~ msgstr "Descripción"

#~ msgid "Details of Sales"
#~ msgstr "Detalles de Ventas"

#~ msgid "Disc."
#~ msgstr "Desc."

#~ msgid "Display the Point of Sale in full screen mode"
#~ msgstr "Mostrar el Punto de Venta en modo pantalla completa"

#~ msgid "Enable password reset from Login page"
#~ msgstr ""
#~ "Habilitar restablecimiento de contraseña desde la página de inicio de "
#~ "sesión"

#~ msgid "Error!"
#~ msgstr "Error!"

#~ msgid "Evian 50cl"
#~ msgstr "Evian 50cl"

#~ msgid "Fail Mail"
#~ msgstr "Correo Fallido"

#~ msgid "Fanta Orange 25cl"
#~ msgstr "Fanta Orange 25cl"

#~ msgid "Fanta Orange 33cl"
#~ msgstr "Fanta Orange 33cl"

#~ msgid "Fanta Orange 50cl"
#~ msgstr "Fanta Orange 50cl"

#~ msgid "Fanta Orange Zero 1.5L"
#~ msgstr "Fanta Orange Zero 1.5L"

#~ msgid "Fanta Zero Orange 33cl"
#~ msgstr "Fanta Zero Orange 33cl"

#~ msgid "Fullscreen"
#~ msgstr "Pantalla completa"

#~ msgid "Generate Entries"
#~ msgstr "Generar Asientos"

#~ msgid "Generate Journal Entries"
#~ msgstr "Generar Asientos Diarios"

#~ msgid ""
#~ "Generate all sale journal entries for non invoiced orders linked to a "
#~ "closed cash register or statement."
#~ msgstr ""
#~ "Generar todos los asientos del diario de ventas para los pedidos no "
#~ "facturados vinculados a una caja registradora cerrada o extracto."

#~ msgid "Give your customers access to their documents."
#~ msgstr "Brinde a sus clientes acceso a sus documentos."

#~ msgid ""
#~ "Gives the probability to assign a lead to this partner. (0 means no "
#~ "assignation.)"
#~ msgstr ""
#~ "Indica la probabilidad de asignar una iniciativa a este partner. (0 "
#~ "significa ninguna asignación.)"

#~ msgid "Grisette Cherry 25cl"
#~ msgstr "Grisette Cherry 25cl"

#~ msgid "Guests:"
#~ msgstr "Invitados:"

#~ msgid "If unchecked, only invited users may sign up."
#~ msgstr ""
#~ "Si esta desmarcado, solamente usuarios invitados pueden registrarse."

#~ msgid ""
#~ "If you have setup a catch-all email domain redirected to the Odoo server, "
#~ "enter the domain name here."
#~ msgstr ""
#~ "Si tiene un dominio de correo electrónico general redireccionado a un "
#~ "servidor de Odoo, ponga el nombre del dominio aquí."

#~ msgid "Implementation References"
#~ msgstr "Referencias de Implementación"

#~ msgid "Implemented by"
#~ msgstr "Implementado por"

#~ msgid "Invoice Amount"
#~ msgstr "Monto de Factura"

#~ msgid "Invoices"
#~ msgstr "Facturas"

#~ msgid "Latest Partner Review"
#~ msgstr "Ultima Revisión de Partner"

#~ msgid "Leffe Blonde 33cl"
#~ msgstr "Leffe Blonde 33cl"

#~ msgid "Leffe Brune 33cl"
#~ msgstr "Leffe Brune 33cl"

#~ msgid "Level"
#~ msgstr "Nivel"

#~ msgid "Level Weight"
#~ msgstr "Nivel de Peso"

#~ msgid "Lindemans Kriek 37.5cl"
#~ msgstr "Lindemans Kriek 37.5cl"

#~ msgid "Manage Inter Company"
#~ msgstr "Administrar Inter Compañías"

#~ msgid "Manage Product Variants"
#~ msgstr "Administrar Variantes de Producto"

#~ msgid "Manage multiple companies"
#~ msgstr "Administrar multiples compañias"

#~ msgid "Nbr Invoice"
#~ msgstr "Num factura"

#~ msgid "Next Partner Review"
#~ msgstr "Siguiente Revision de Partner"

#~ msgid "Number of Transaction"
#~ msgstr "Número de Transaccion"

#~ msgid "Orval 33cl"
#~ msgstr "Orval 33cl"

#~ msgid "POS"
#~ msgstr "PDV"

#~ msgid "POS Details"
#~ msgstr "Detalles de PDV"

#~ msgid "POS Lines"
#~ msgstr "Líneas de PDV"

#~ msgid "POS Report"
#~ msgstr "Reporte de PDV"

#~ msgid "Partnership Date"
#~ msgstr "Fecha de Asociamiento"

#~ msgid "Post All Orders"
#~ msgstr "Contabilizar Todos los Pedidos"

#~ msgid "Post POS Journal Entries"
#~ msgstr "Contabilizar Asientos de Diario del PDV"

#~ msgid "Print Report"
#~ msgstr "Imprimir Reporte"

#~ msgid "Product Nb."
#~ msgstr "Número de Producto."

#~ msgid "Reference"
#~ msgstr "Referencia"

#~ msgid "Reprint"
#~ msgstr "Reimprimir"

#~ msgid "Rescue session"
#~ msgstr "Recuperar sesion"

#~ msgid "Rochefort \"8\" 33cl"
#~ msgstr "Rochefort \"8\" 33cl"

#~ msgid "Sale Details"
#~ msgstr "Detalles de Venta"

#~ msgid "Sales Details"
#~ msgstr "Detalles de Ventas"

#~ msgid "Sales Journal"
#~ msgstr "Diario de Ventas"

#~ msgid "Sales Lines"
#~ msgstr "Líneas de Ventas"

#~ msgid "Sales by User"
#~ msgstr "Ventas por Usuario"

#~ msgid "Sales by User Monthly"
#~ msgstr "Ventas Mensuales por Usuario"

#~ msgid "Sales by day"
#~ msgstr "Ventas por dia"

#~ msgid "Sales by month"
#~ msgstr "Ventas por mes"

#~ msgid "Sales by user"
#~ msgstr "Ventas por usuario"

#~ msgid "Sales by user monthly"
#~ msgstr "Ventas mensuales por usuario"

#~ msgid "Salespeople"
#~ msgstr "Personal de Ventas"

#~ msgid "San Pellegrino 1L"
#~ msgstr "San Pellegrino 1L"

#~ msgid "Session Summary"
#~ msgstr "Resumen de la Sesión"

#~ msgid "Session Summary:"
#~ msgstr "Resumen de la Sesion:"

#~ msgid "Share or embbed any screen of Odoo."
#~ msgstr "Comparta o incruste cualquier pantalla de Odoo."

#~ msgid "Share partners to all companies"
#~ msgstr "Comparta partners a todas las compañias"

#~ msgid "Share product to all companies"
#~ msgstr "Compartir producto a todas las compañías"

#~ msgid ""
#~ "Share your partners to all companies defined in your instance.\n"
#~ " * Checked : Partners are visible for every companies, even if a company "
#~ "is defined on the partner.\n"
#~ " * Unchecked : Each company can see only its partner (partners where "
#~ "company is defined). Partners not related to a company are visible for "
#~ "all companies."
#~ msgstr ""
#~ "Comparta sus empresas entre todas las compañías definidas en su "
#~ "instancia.\n"
#~ " * Marcado: Las empresas son visibles para todas las compañías, incluso "
#~ "si se define una compañía en la empresa.\n"
#~ " * Desmarcado: Cada compañía podrá ver solo sus empresas (empresas en las "
#~ "que la compañía está definida). Las empresas sin compañía establecida "
#~ "serán visibles para todas las compañías."

#~ msgid ""
#~ "Share your product to all companies defined in your instance.\n"
#~ " * Checked : Product are visible for every company, even if a company is "
#~ "defined on the partner.\n"
#~ " * Unchecked : Each company can see only its product (product where "
#~ "company is defined). Product not related to a company are visible for all "
#~ "companies."
#~ msgstr ""
#~ "Comparte su producto a todas las empresas definidas en su instancia.\n"
#~ " * Marcada: Los productos son visibles para todas las compañías, incluso "
#~ "si una empresa se ​​define en la empresa.\n"
#~ " * Desactivada: Cada compañía sólo puede ver sus producto (producto donde "
#~ "la compañía se ​​define). Los productos no relacionados con una empresa son "
#~ "visibles por todas las compañías."

#~ msgid "Show Tutorial"
#~ msgstr "Mostrar Tutorial"

#~ msgid "Soft"
#~ msgstr "Suave"

#~ msgid "Statement Details:"
#~ msgstr "Detalles del Extrato:"

#~ msgid "Statement Summary"
#~ msgstr "Resumen del Extracto"

#~ msgid "Stella Artois 50cl"
#~ msgstr "Stella Artois 50cl"

#~ msgid "Template user for new users created through signup"
#~ msgstr ""
#~ "Plantilla de usuario para nuevos usuarios creados a traves del ingreso"

#~ msgid "The URL to generate the authorization code from Google"
#~ msgstr "La URL para generar el codigo de autorizacion de Google"

#~ msgid "This allows users to trigger a password reset from the Login page."
#~ msgstr ""
#~ "Esto permite a los usuarios lanzar un restablecimiento de la contraseña "
#~ "desde la página de inicio de sesión."

#~ msgid "This installs the module google_calendar."
#~ msgstr "Esto instala el módulo google_calendar."

#~ msgid "This installs the module google_docs."
#~ msgstr "Esto instala el módulo google_docs."

#~ msgid ""
#~ "This installs the module inter_company_rules.\n"
#~ " Configure company rules to automatically create SO/PO when one of your "
#~ "company sells/buys to another of your company."
#~ msgstr ""
#~ "Esto instala el módulo inter_company_rules.\n"
#~ " Configure reglas de compañía para crear automáticamente PV/OC cuando una "
#~ "de sus compañías venda/compre a otra de sus compañías."

#~ msgid "Timmermans Kriek 37.5cl"
#~ msgstr "Timmermans Kriek 37.5cl"

#~ msgid "Today's Payment"
#~ msgstr "Pagos de Hoy"

#~ msgid "Today's Payments"
#~ msgstr "Pagos de Hoy"

#~ msgid "Total Transaction"
#~ msgstr "Transacción Total"

#~ msgid "URI"
#~ msgstr "URI"

#~ msgid "URI for tuto"
#~ msgstr "URI para tutoriales"

#~ msgid "Unable to cancel the picking."
#~ msgstr "Imposible cancelar el picking"

#~ msgid "Use external authentication providers, sign in with Google..."
#~ msgstr ""
#~ "Use proveedores de autenticación externa, inicie sesión con Google..."

#~ msgid ""
#~ "Work in multi-company environments, with appropriate security access "
#~ "between companies."
#~ msgstr ""
#~ "Trabaje en entornos multi-compañía, con la seguridad de acceso apropiada "
#~ "entre compañías."

#~ msgid ""
#~ "Work with product variant allows you to define some variant of the same "
#~ "products, an ease the product management in the ecommerce for example"
#~ msgstr ""
#~ "Trabajar con variantes de producto le permite definir algunas variante de "
#~ "los mismos productos, una facilidad de la gestión de productos en el "
#~ "comercio electrónico, por ejemplo."

#~ msgid "Your internet connection is probably down."
#~ msgstr "Su conexión de Internet está probablemente caída."

#~ msgid "report.point_of_sale.report_detailsofsales"
#~ msgstr "report.point_of_sale.report_detailsofsales"

#~ msgid "report.point_of_sale.report_payment"
#~ msgstr "report.point_of_sale.report_payment"

#~ msgid "report.point_of_sale.report_receipt"
#~ msgstr "report.point_of_sale.report_receipt"

#~ msgid "report.point_of_sale.report_saleslines"
#~ msgstr "report.point_of_sale.report_saleslines"

#~ msgid "transaction for the pos"
#~ msgstr "transacción para el punto de venta"

#~ msgid "unknown"
#~ msgstr "desconocido"

#~ msgid "uuid"
#~ msgstr "uuid"
