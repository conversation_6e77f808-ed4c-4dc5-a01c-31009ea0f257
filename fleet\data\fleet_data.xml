<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="ir_cron_contract_costs_generator" model="ir.cron">
            <field name="name">Fleet: Generate contracts costs based on costs frequency</field>
            <field name="model_id" ref="model_fleet_vehicle_log_contract"/>
            <field name="state">code</field>
            <field name="code">model.run_scheduler()</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field eval="False" name="doall" />
        </record>

        <record id="fleet_vehicle_state_new_request" model="fleet.vehicle.state">
            <field name="name">New Request</field>
            <field name="sequence">4</field>
        </record>

        <record id="fleet_vehicle_state_to_order" model="fleet.vehicle.state">
            <field name="name">To Order</field>
            <field name="sequence">5</field>
        </record>

        <record id="fleet_vehicle_state_registered" model="fleet.vehicle.state">
            <field name="name">Registered</field>
            <field name="sequence">7</field>
        </record>

        <record id="fleet_vehicle_state_downgraded" model="fleet.vehicle.state">
            <field name="name">Downgraded</field>
            <field name="sequence">8</field>
        </record>
    </data>
</odoo>
