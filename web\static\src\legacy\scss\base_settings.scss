// MIXINS
@mixin o-base-settings-horizontal-padding($padding-base: $input-btn-padding-y-sm) {
    padding: $padding-base $o-horizontal-padding;

    @include media-breakpoint-up(xl) {
        padding-left: $o-horizontal-padding*2;;
    }
}

// Use a very specif selector to overwrite generic form-view rules
.o_form_view.o_form_nosheet.o_base_settings {
    display: flex;
    flex-flow: column nowrap;
    padding: 0px;
}

// BASE SETTINGS LAYOUT
.o_base_settings {
    height: 100%;

    .o_control_panel {
        flex: 0 0 auto;

        .o_panel {
            display: flex;
            flex-flow: row wrap;
            width: 100%;

            .o_settings_title, .o_setting_search {
                flex: 1 0 auto;
                width: map-get($o-extra-grid-breakpoints, vsm) / 2;
            }

            .o_setting_search {
                position: relative;

                .searchInput {
                    border: 0px;
                    max-width: 400px;
                    border-bottom: 1px solid $border-color;
                    box-shadow: none;
                }

                .searchIcon {
                    @include o-position-absolute($right: 0);
                    color: gray('700');
                }

                &:focus-within {
                    .searchInput {
                        border-bottom-color: $primary;
                    }
                }
            }
        }

        .o_form_statusbar {
            padding: 0;
            margin: 0;
            border: 0;
        }
    }

    .o_setting_container {
        display: flex;
        flex: 1 1 auto;

        overflow: auto;

        .settings_tab {
            display: flex;
            flex: 0 0 auto;
            flex-flow: column nowrap;
            background: gray('900');
            overflow: auto;

            .selected {
                background-color: gray('800');
                box-shadow: inset 2px 0 0 $o-brand-primary;
            }

            .tab {
                display: flex;
                padding: 0 $o-horizontal-padding*2 0 $o-horizontal-padding;
                height: 40px;
                color: gray('400');
                font-size: 13px;
                line-height: 40px;
                cursor: pointer;
                white-space: nowrap;

                .icon {
                    width: 23px;
                    min-width: 23px;
                    margin-right: 10px;
                }

                &:hover, &.selected {
                    color: white;
                }
            }
        }

        .settings {
            position: relative;
            flex: 1 1 100%;
            background-color: $o-view-background-color;
            overflow: auto;

            > .app_settings_block {
                h2 {
                    margin: 0 0 !important;
                    @include o-base-settings-horizontal-padding(.4rem);
                    background-color: gray('200');
                    font-size: 15px;

                    &.o_invisible_modifier + .o_settings_container {
                        display: none;
                    }
                }

                .o_settings_container {
                    max-width: map-get($grid-breakpoints, lg); // Provide a maximum container size to ensure readability
                    @include o-base-settings-horizontal-padding(0);
                    margin-bottom: 24px;
                }
            }

            .settingSearchHeader {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                @include o-base-settings-horizontal-padding(.8rem);
                background-color: gray('200');

                .icon {
                    width: 1.4em;
                    height: 1.4em;
                    margin-right: 10px;
                }
            }

            .highlighter {
                background: yellow;
            }
        }

        .d-block {
            display: block!important;
        }
    }
}
