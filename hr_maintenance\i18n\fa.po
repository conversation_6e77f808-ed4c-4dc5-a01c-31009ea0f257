# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_maintenance
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "تاریخ تخصیص داده‌شده"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__department_id
msgid "Assigned Department"
msgstr "دپارتمان تخصیص داده‌شده"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__employee_id
msgid "Assigned Employee"
msgstr "کارمند تخصیص داده‌شده"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_count
msgid "Assigned Equipments"
msgstr "تجهیزات تخصیص داده‌شده"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr "ایجادشده توسط"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "ایجاد شده توسط کاربر"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__department
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Department"
msgstr "دپارتمان"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_employee
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__employee_id
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__employee
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Employee"
msgstr "کارمند"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_ids
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__equipment_id
msgid "Equipment"
msgstr "تجهیز"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_count
msgid "Equipments"
msgstr "تجهیزات"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "تجهیزات نگهداری"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Request"
msgstr "درخواست نگهداری"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_ids
msgid "Managed Equipments"
msgstr "مدیریت تجهیزات"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Maintenances"
msgstr "تعمیر و نگهداری من"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__other
msgid "Other"
msgstr "سایر"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__owner_user_id
msgid "Owner"
msgstr "مالک‌"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_kanban_inherit_hr
msgid "Unassigned"
msgstr "تخصیص‌نیافته"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__equipment_assign_to
msgid "Used By"
msgstr "استفاده‌شده توسط"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_res_users
msgid "Users"
msgstr "کاربران"
