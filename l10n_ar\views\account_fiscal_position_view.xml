<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_position_form" model="ir.ui.view">
        <field name="name">account.fiscal.position.form</field>
        <field name="model">account.fiscal.position</field>
        <field name="inherit_id" ref="account.view_account_position_form"/>
        <field name="arch" type="xml">
            <field name="auto_apply" position="after">
                <field name="l10n_ar_afip_responsibility_type_ids" options="{'no_open': True, 'no_create': True}" attrs="{'invisible': [('auto_apply', '!=', True)]}" groups="base.group_no_one" widget="many2many_tags"/>
            </field>
        </field>
    </record>

</odoo>
