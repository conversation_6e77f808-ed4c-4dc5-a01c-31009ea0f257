<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="partner_page" name="Partner Page">
    <t t-call="website.layout">
        <div id="wrap">
            <div class="oe_structure" id="oe_structure_website_partner_partner_1"/>
            <div class="container">
                <div class="row">
                    <t t-call="website_partner.partner_detail"></t>
                </div>
            </div>
            <div class="oe_structure" id="oe_structure_website_partner_partner_2"/>
        </div>
    </t>
</template>

<template id="partner_detail" name="Partner Details">
    <h1 class="col-lg-12 text-center" id="partner_name" t-field="partner.display_name"/>
    <div class="col-lg-4">
        <div t-field="partner.avatar_1920" t-options='{"widget": "image", "preview_image": "avatar_512", "class": "d-block mx-auto mb16"}'/>
        <address>
             <div t-field="partner.self" t-options='{
                 "widget": "contact",
                 "fields": ["address", "website", "phone", "email"]
             }'/>
        </address>
        <t t-out="left_column or ''"/>
    </div>
    <div class="col-lg-8 mt32">
        <t t-if="partner">
            <div t-field="partner.website_description"/>
            <t groups="website.group_website_publisher">
                <h2 class="css_non_editable_mode_hidden o_not_editable">Short Description for List View</h2>
                <div class="css_non_editable_mode_hidden" t-field="partner.website_short_description"/>
            </t>
        </t>
        <t t-out="right_column or ''"/>
    </div>
</template>
</odoo>
