# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2021
# j<PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr "<span class=\"o_form_label oe_edit_only\">Empleats permesos </span>"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Caixer"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Change Cashier"
msgstr "Canviar el Caixer"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Empleat"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "Empleat:%s - PoS Config(s): %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr "Empleats amb accés"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr "Si es deixa buit, tots els empleats poden entrar a la sessió del PoS"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Incorrect Password"
msgstr "Contrasenya incorrecta"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Log in to"
msgstr "Inicia la sessió a"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/useSelectEmployee.js:0
#, python-format
msgid "Password ?"
msgstr "Contrasenya ?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Persones que utilitzen la caixa registradora. Pot ser un substitut, un "
"estudiant o un empleat interí."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuració del Punt de Venda"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comandes del Punt de Venda"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Informe de tiquets de punt de venda"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Escanegi la seva identificació"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "Seleccioneu Cashier"

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"No es pot suprimir un empleat que pugui ser utilitzat en una sessió activa "
"de PoS, tancar primer les session(s):\n"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "or"
msgstr "o"

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/CashierName.xml:0
#, python-format
msgid "selectCashier"
msgstr "seleccionarCaixer"
