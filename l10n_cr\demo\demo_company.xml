<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_cr" model="res.partner">
        <field name="name">CR Company</field>
        <field name="vat">4000961586</field>
        <field name="street"></field>
        <field name="city"></field>
        <field name="country_id" ref="base.cr"/>
        <field name="state_id" ref="base.state_L"/>
        <field name="zip"></field>
        <field name="phone">+506 8312 3456</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.crexample.com</field>
    </record>

    <record id="demo_company_cr" model="res.company">
        <field name="name">CR Company</field>
        <field name="partner_id" ref="partner_demo_company_cr"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_cr')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_cr.demo_company_cr'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_cr.account_chart_template_0')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_cr.demo_company_cr')"/>
    </function>
</odoo>
