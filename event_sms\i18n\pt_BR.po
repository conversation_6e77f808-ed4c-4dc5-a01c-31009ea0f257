# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sms
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail
msgid "Event Automated Mailing"
msgstr "Automação de email no evento"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_registration
msgid "Event Registration"
msgstr "Inscrição no Evento"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_registration
msgid "Event: Registration"
msgstr "Evento: Inscrição"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_reminder
msgid "Event: Reminder"
msgstr "Evento: Lembrete"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Agendamento de e-mail na categoria de evento"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Agendador de E-mail de Registro"

#. module: event_sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__notification_type__sms
msgid "SMS"
msgstr "SMS"

#. module: event_sms
#: model:ir.model,name:event_sms.model_sms_template
msgid "SMS Templates"
msgstr "Modelos de SMS"

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__notification_type
msgid "Send"
msgstr "Enviar"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_reminder
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are excited to remind you that the {{ "
"object.event_id.name }} event is starting {{ object.get_date_range_str() }}."
" We confirm your registration and hope to meet you there."
msgstr ""

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_registration
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are happy to confirm your registration for the "
"{{ object.event_id.name }} event."
msgstr ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: Estamos felizes em confirmar sua participação no "
"evento {{ object.event_id.name }}."
