<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!--
        This template is used to generate the SVG containing the signature when
        using mode 'auto'.

        The following variables have to be defined:
            - width: (number) the width of the resulting image in px
            - height: (number) the height of the resulting image in px
            - font: base64 encoded font to use
            - text: (string) the name to draw
            - type: (string)
                -> if set to 'signature', it will adapt the characters width to
                fit the whole text in the image.
                -> otherwise, it will adapt the space between characters to fill
                the image with the text.
    -->
    <t t-name="web.sign_svg_text" name="SVG Signature Text">
        <svg t-att-width="width" t-att-height="height">
            <defs>
                <style type="text/css">
                    @font-face {
                        font-family: "font";
                        src: url(data:font/ttf;base64,<t t-esc="font"/>) format("woff");
                        font-weight: normal;
                        font-style: normal;
                    }
                </style>
            </defs>
            <!-- adapt textLength to prevent distortion and left overflow with small words -->
            <text t-esc="text" x="50%" t-att-y="height * 3 / 5" t-att-font-size="height * 3 / 5"
                t-att-textLength="width * 4 / 5 * (type === 'signature' ? Math.min(1, text.length / 7) : 1)"
                t-att-lengthAdjust="'spacing' + (type === 'signature' ? 'AndGlyphs' : '')"
                t-attf-style="font-family:'font'; stroke: none; fill: {{color}};"  text-anchor="middle"/>
        </svg>
    </t>

    <!-- Template for the widget webSignNameAndDraw. -->
    <t t-name="web.sign_name_and_signature">
        <div class="o_web_sign_name_and_signature">
            <div class="o_web_sign_name_group form-group">
                <label class="col-form-label" t-att-for="'o_web_sign_name_input_' + widget.htmlId">Full Name</label>
                <input type="text" name="signer" t-att-id="'o_web_sign_name_input_' + widget.htmlId" class="o_web_sign_name_input form-control" t-att-value="widget.defaultName" placeholder="Your name" required=""/>
            </div>

            <!--
                Overflow hidden is necessary because the card has a rounded
                border, but we remove the padding from the body, so the canvas
                will overflow on the corners without this rule.
            -->
            <div class="o_web_sign_signature_group bg-white card mt-3" style="overflow: hidden;">
                <div class="card-header">
                    <div class="row no-gutters">
                        <div class="col-auto">
                            <a role="button" href="#" t-attf-class="o_web_sign_draw_button mr-2 btn btn-light {{ widget.signMode === 'draw' ? 'active': '' }}">
                                Draw
                            </a>
                        </div>
                        <div class="col-auto">
                            <a role="button" href="#" t-attf-class="o_web_sign_auto_button mr-2 btn btn-light {{ widget.signMode === 'auto' ? 'active': '' }}">
                                Auto
                            </a>
                        </div>
                        <div class="col-auto">
                            <a role="button" href="#" t-attf-class="o_web_sign_load_button mr-2 btn btn-light {{ widget.signMode === 'load' ? 'active': '' }}">
                                Load
                            </a>
                        </div>
                        <div t-attf-class="o_web_sign_draw_clear col-auto ml-auto {{ widget.signMode !== 'draw' ? 'd-none' : '' }}">
                            <a role="button" href="#" class="btn btn-link">
                                Clear
                            </a>
                        </div>
                        <div t-attf-class="o_web_sign_auto_select_style col-auto ml-auto {{ widget.signMode !== 'auto' ? 'd-none' : '' }}">
                            <a role="button" href="#" class="btn btn-link">
                                Style
                            </a>
                        </div>
                        <div t-attf-class="o_web_sign_load_file col-auto {{ widget.signMode !== 'load' ? 'd-none' : '' }}">
                            <input type="file" role="button" name="files[]" class="btn btn-link py-0"/>
                        </div>
                    </div>
                </div>

                <div class="o_signature_stroke position-absolute"/>

                <div class="o_web_sign_signature p-0 bg-transparent position-relative"/>

                <div t-attf-class="o_web_sign_load_invalid card-footer d-none">
                    <div class="alert alert-danger mb-0" role="alert">
                        This file is invalid. Please select an image.
                    </div>
                </div>
            </div>

            <div class="o_web_sign_auto_font_selection card d-none h-100 flex-column">
                <div class="card-header">Styles</div>
                <div class="o_web_sign_auto_font_list card-body text-center p-3 flex-grow-1"/>
            </div>
        </div>
    </t>
    <div t-name="web.signature_dialog">
        <div class="o_web_sign_name_and_signature"/>

        <div class="mt16 small">By clicking Adopt and Sign, I agree that the chosen signature/initials will be a valid electronic representation of my hand-written signature/initials for all purposes when it is used on documents, including legally binding contracts.</div>
    </div>
</templates>
