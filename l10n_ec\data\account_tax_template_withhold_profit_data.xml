<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <data>
        <!-- 
		PURCHASE WITHHOLDS OVER ANTICIPADED PROFIT *IMPUESTO A LA RENTA
		-->
        <record id="tax_withhold_profit_303" model="account.tax.template">
            <field name="name">303 10% honorarios profesionales y demas pagos por servicios relacionados con el titulo profesional</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-10.0</field>
            <field name="description">303</field>
            <field name="l10n_ec_code_applied">353</field>
            <field name="l10n_ec_code_base">303</field>
            <field name="l10n_ec_code_ats">303</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_303')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_10x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_353')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_303')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_10x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_353')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304" model="account.tax.template">
            <field name="name">304 8% servicios predomina el intelecto no relacionados con el titulo profesional</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304A" model="account.tax.template">
            <field name="name">304a 8% comisiones y demas pagos por servicios predomina intelecto no relacionados con el titulo profesional</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304B" model="account.tax.template">
            <field name="name">304b 8% pagos a notarios y registradores de la propiedad y mercantil por sus actividades ejercidas como tales</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304B</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304C" model="account.tax.template">
            <field name="name">304c 8% pagos a deportistas, entrenadores, arbitros, miembros del cuerpo tecnico por sus actividades ejercidas como tales</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304C</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304D" model="account.tax.template">
            <field name="name">304d 8% pagos a artistas por sus actividades ejercidas como tales</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304D</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_304E" model="account.tax.template">
            <field name="name">304e 8% honorarios y demas pagos por servicios de docencia</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">304</field>
            <field name="l10n_ec_code_applied">354</field>
            <field name="l10n_ec_code_base">304</field>
            <field name="l10n_ec_code_ats">304E</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_304')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_354')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_307" model="account.tax.template">
            <field name="name">307 2% servicios predomina mano de obra</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.0</field>
            <field name="description">307</field>
            <field name="l10n_ec_code_applied">357</field>
            <field name="l10n_ec_code_base">307</field>
            <field name="l10n_ec_code_ats">307</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_307')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_357')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_307')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_357')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_308" model="account.tax.template">
            <field name="name">308 10% utilizacion o aprovechamiento de la imagen o renombre</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-10.0</field>
            <field name="description">308</field>
            <field name="l10n_ec_code_applied">358</field>
            <field name="l10n_ec_code_base">308</field>
            <field name="l10n_ec_code_ats">308</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_308')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_10x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_358')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_308')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_10x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_358')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_309" model="account.tax.template">
            <field name="name">309 1.75% servicios prestados por medios de comunicación y agencias de publicidad</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">309</field>
            <field name="l10n_ec_code_applied">359</field>
            <field name="l10n_ec_code_base">309</field>
            <field name="l10n_ec_code_ats">309</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_309')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_359')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_309')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_10x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_359')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_310" model="account.tax.template">
            <field name="name">310 1% servicio de transporte privado de pasajeros o transporte publico o privado de carga</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.0</field>
            <field name="description">310</field>
            <field name="l10n_ec_code_applied">360</field>
            <field name="l10n_ec_code_base">310</field>
            <field name="l10n_ec_code_ats">310</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_310')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_360')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_310')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_360')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_311" model="account.tax.template">
            <field name="name">311 2% por pagos a traves de liquidacion de compra (nivel cultural o rusticidad)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.0</field>
            <field name="description">311</field>
            <field name="l10n_ec_code_applied">361</field>
            <field name="l10n_ec_code_base">311</field>
            <field name="l10n_ec_code_ats">311</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_311')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_361')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_311')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_361')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_312" model="account.tax.template">
            <field name="name">312 1.75% transferencia de bienes muebles de naturaleza corporal</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">312</field>
            <field name="l10n_ec_code_applied">362</field>
            <field name="l10n_ec_code_base">312</field>
            <field name="l10n_ec_code_ats">312</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_312')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_362')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_312')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_362')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_312A" model="account.tax.template">
            <field name="name">312a 1% compra de bienes de origen agricola, avicola, pecuario, apicola, cunicula, bioacuatico, y forestal</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.0</field>
            <field name="description">312</field>
            <field name="l10n_ec_code_applied">362</field>
            <field name="l10n_ec_code_base">312</field>
            <field name="l10n_ec_code_ats">312A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_312')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_362')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_312')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_362')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_314A" model="account.tax.template">
            <field name="name">314a 8% regalias por concepto de franquicias de acuerdo a ley de propiedad intelectual - pago a personas naturales</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">314</field>
            <field name="l10n_ec_code_applied">364</field>
            <field name="l10n_ec_code_base">314</field>
            <field name="l10n_ec_code_ats">314A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_314B" model="account.tax.template">
            <field name="name">314b 8% casales, derechos de autor, marcas, patentes y similares de acuerdo a ley de propiedad intelectual – pago a personas naturales</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">314</field>
            <field name="l10n_ec_code_applied">364</field>
            <field name="l10n_ec_code_base">314</field>
            <field name="l10n_ec_code_ats">314B</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_314C" model="account.tax.template">
            <field name="name">314c 8% regalias por concepto de franquicias de acuerdo a ley de propiedad intelectual - pago a sociedades</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">314</field>
            <field name="l10n_ec_code_applied">364</field>
            <field name="l10n_ec_code_base">314</field>
            <field name="l10n_ec_code_ats">314C</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_314D" model="account.tax.template">
            <field name="name">314d 8% casales, derechos de autor, marcas, patentes y similares de acuerdo a ley de propiedad intelectual – pago a sociedades</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">314</field>
            <field name="l10n_ec_code_applied">364</field>
            <field name="l10n_ec_code_base">314</field>
            <field name="l10n_ec_code_ats">314D</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_314')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_364')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_319" model="account.tax.template">
            <field name="name">319 1.75% cuotas de arrendamiento mercantil (prestado por sociedades), inclusive la de opción de compra</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">319</field>
            <field name="l10n_ec_code_applied">369</field>
            <field name="l10n_ec_code_base">319</field>
            <field name="l10n_ec_code_ats">319</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_319')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_369')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_319')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_369')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_320" model="account.tax.template">
            <field name="name">320 8% por arrendamiento bienes inmuebles</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">320</field>
            <field name="l10n_ec_code_applied">370</field>
            <field name="l10n_ec_code_base">320</field>
            <field name="l10n_ec_code_ats">320</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_320')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_370')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_320')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_8x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_370')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_322" model="account.tax.template">
            <field name="name">322 1.75% seguros y reaseguros (primas y cesiones) 1.75%	</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">322</field>
            <field name="l10n_ec_code_applied">372</field>
            <field name="l10n_ec_code_base">322</field>
            <field name="l10n_ec_code_ats">322</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_322')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_372')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_322')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_372')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_332" model="account.tax.template">
            <field name="name">332 0% otras compras de bienes y servicios no sujetas a retencion</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332A" model="account.tax.template">
            <field name="name">332a 0% enajenacion de derechos representativos de capital y otros derechos exentos (mayo 2016)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332B" model="account.tax.template">
            <field name="name">332b 0% compra de bienes inmuebles</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332B</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332C" model="account.tax.template">
            <field name="name">332c 0% transporte publico de pasajeros</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332C</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332D" model="account.tax.template">
            <field name="name">332d 0% pagos en el pais por transporte de pasajeros o transporte internacional de carga, a compañias nacionales o extranjeras de aviacion o maritimas</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332D</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332G" model="account.tax.template">
            <field name="name">332g 0% pagos con tarjeta de credito</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332G</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_332I" model="account.tax.template">
            <field name="name">332i 0% pagos a través de convenios de débito (clientes ifi`s)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">0.0</field>
            <field name="description">332</field>
            <field name="l10n_ec_code_base">332</field>
            <field name="l10n_ec_code_ats">332I</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_332')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_others'),
                })]"/>
        </record>
        <record id="tax_withhold_profit_343A" model="account.tax.template">
            <field name="name">343a 1% por energia electrica</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.0</field>
            <field name="description">343</field>
            <field name="l10n_ec_code_applied">393</field>
            <field name="l10n_ec_code_base">343</field>
            <field name="l10n_ec_code_ats">343A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_343B" model="account.tax.template">
            <field name="name">343b 1% por actividades de construccion de obra material inmueble, urbanizacion, lotizacion o actividades similares</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.0</field>
            <field name="description">343</field>
            <field name="l10n_ec_code_applied">393</field>
            <field name="l10n_ec_code_base">343</field>
            <field name="l10n_ec_code_ats">343B</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_3440" model="account.tax.template">
            <field name="name">3440 2.75% otras retenciones aplicables el 2,75%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.0</field>
            <field name="description">3440</field>
            <field name="l10n_ec_code_applied">394</field>
            <field name="l10n_ec_code_base">3440</field>
            <field name="l10n_ec_code_ats">3440</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_3440')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_394')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_3440')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2_75x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_394')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_346" model="account.tax.template">
            <field name="name">346 1.75% microempresas (otras retenciones aplicables a otros porcentajes)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">346</field>
            <field name="l10n_ec_code_applied">396</field>
            <field name="l10n_ec_code_base">346</field>
            <field name="l10n_ec_code_ats">346</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_1_75x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_347_346" model="account.tax.template">
            <field name="name">347-346 2% donaciones en dinero -impuesto a la donaciones</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.0</field>
            <field name="description">346</field>
            <field name="l10n_ec_code_applied">396</field>
            <field name="l10n_ec_code_base">346</field>
            <field name="l10n_ec_code_ats">347</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_2x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_501_411" model="account.tax.template">
            <field name="name">501-411 22% pago al exterior - beneficios empresariales (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_ats">501</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_501_422" model="account.tax.template">
            <field name="name">501-422 22% pago al exterior - beneficios empresariales (sin convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">422</field>
            <field name="l10n_ec_code_applied">472</field>
            <field name="l10n_ec_code_base">422</field>
            <field name="l10n_ec_code_ats">501</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_502_411" model="account.tax.template">
            <field name="name">502-411 22% pago al exterior - servicios empresariales (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_ats">502</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_502_422" model="account.tax.template">
            <field name="name">502-422 22% pago al exterior - servicios empresariales (sin convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">422</field>
            <field name="l10n_ec_code_applied">472</field>
            <field name="l10n_ec_code_base">422</field>
            <field name="l10n_ec_code_ats">502</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_509_411" model="account.tax.template">
            <field name="name">509-411 22% pago al exterior - casales, derechos de autor, marcas, patentes y similares (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_ats">509</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_509_422" model="account.tax.template">
            <field name="name">509-422 pago al exterior - casales, derechos de autor, marcas, patentes y similares (sin convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">422</field>
            <field name="l10n_ec_code_base">422</field>
            <field name="l10n_ec_code_applied">472</field>
            <field name="l10n_ec_code_ats">509</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_422')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_472')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_511_411" model="account.tax.template">
            <field name="name">511-411 22% pago al exterior - servicios profesionales independientes (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_ats">511</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_512_411" model="account.tax.template">
            <field name="name">512-411 22% pago al exterior - servicios profesionales dependientes (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_ats">512</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_517_411" model="account.tax.template">
            <field name="name">517-411 22% pago al exterior - reembolso de gastos (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_ats">517</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_520D_411" model="account.tax.template">
            <field name="name">520d-411 22% pago al exterior - comisiones por exportaciones y por promocion de turismo receptivo (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">411</field>
            <field name="l10n_ec_code_base">411</field>
            <field name="l10n_ec_code_applied">461</field>
            <field name="l10n_ec_code_ats">520D</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_411')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_461')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_522A_410" model="account.tax.template">
            <field name="name">522a-410 22% pago al exterior - servicios tecnicos, administrativos o de consultoria y regalias con convenio de doble tributacion (con convenio de doble tributación)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">410</field>
            <field name="l10n_ec_code_base">410</field>
            <field name="l10n_ec_code_applied">460</field>
            <field name="l10n_ec_code_ats">522A</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_410')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'plus_report_line_ids': [ref('tax_report_line_103_460')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_410')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ret_ir_22x100'),
                    'minus_report_line_ids': [ref('tax_report_line_103_460')],
                })]"/>
        </record>
        <!-- 
		SALES WITHHOLDS OVER ANTICIPADED PROFIT *IMPUESTO A LA RENTA VENTA
		-->
        <record id="tax_withhold_profit_sale_1x100" model="account.tax.template">
            <field name="name">1% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.0</field>
            <field name="description">1%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_343')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_393')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_1_75x100" model="account.tax.template">
            <field name="name">1.75% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-1.75</field>
            <field name="description">1.75%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_2x100" model="account.tax.template">
            <field name="name">2% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.0</field>
            <field name="description">2%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_344')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_394')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_344')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_394')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_2_75x100" model="account.tax.template">
            <field name="name">2.75% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-2.75</field>
            <field name="description">2.75%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_3440')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_3940')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_3440')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_3940')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_5x100" model="account.tax.template">
            <field name="name">5% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-5.0</field>
            <field name="description">5%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_8x100" model="account.tax.template">
            <field name="name">8% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-8.0</field>
            <field name="description">8%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_345')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_395')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_345')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_395')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_10x100" model="account.tax.template">
            <field name="name">10% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-10.0</field>
            <field name="description">10%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_15x100" model="account.tax.template">
            <field name="name">15% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-15.0</field>
            <field name="description">15%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
        <record id="tax_withhold_profit_sale_22x100" model="account.tax.template">
            <field name="name">22% retenciones de la fuente</field>
            <field name="type_tax_use">sale</field>
            <field name="amount_type">percent</field>
            <field name="sequence">70</field>
            <field name="amount">-22.0</field>
            <field name="description">22%</field>
            <field name="chart_template_id" ref="l10n_ec.l10n_ec_ifrs"/>
            <field name="tax_group_id" ref="tax_group_withhold_income"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'plus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_103_346')],
                }),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('l10n_ec.ec_sale_profit_withhold'),
                    'minus_report_line_ids': [ref('tax_report_line_103_396')],
                })]"/>
        </record>
    </data>
</odoo>
