<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="tax_group_fix" model="account.tax.group">
            <field name="name">Taxes</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_gst_5" model="account.tax.group">
            <field name="name">GST 5%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_pst_5" model="account.tax.group">
            <field name="name">PST 5%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_gst_7" model="account.tax.group">
            <field name="name">GST 7%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_gst_8" model="account.tax.group">
            <field name="name">GST 8%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_pst_8" model="account.tax.group">
            <field name="name">PST 8%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_qst_9975" model="account.tax.group">
            <field name="name">QST 9.975%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_hst_13" model="account.tax.group">
            <field name="name">HST 13%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_hst_14" model="account.tax.group">
            <field name="name">HST 14%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
        <record id="tax_group_hst_15" model="account.tax.group">
            <field name="name">HST 15%</field>
            <field name="country_id" ref="base.ca"/>
        </record>
    </data>
</odoo>
