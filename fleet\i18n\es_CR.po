# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * fleet
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:54+0000\n"
"PO-Revision-Date: 2017-09-20 09:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Costa Rica) (https://www.transifex.com/odoo/teams/41243/es_CR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:198
#, python-format
msgid "%s %s has been added to the fleet!"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_cost.py:274
#, python-format
msgid "%s contract(s) will expire soon and should be renewed and/or closed!"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>g/km</span>"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "<span>kW</span>"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_1
msgid "A/C Compressor Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_2
msgid "A/C Condenser Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_3
msgid "A/C Diagnosis"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_4
msgid "A/C Evaporator Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_5
msgid "A/C Recharge"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Activation Cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_active
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_active
msgid "Active"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Additional Details"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Additional Properties"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_6
msgid "Air Filter Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "All vehicles"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_7
msgid "Alternator Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_cost_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_cost_amount
msgid "Amount"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Archived"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_9
msgid "Assistance"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,transmission:0
msgid "Automatic"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_auto_generated
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_auto_generated
msgid "Automatically Generated"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_8
msgid "Ball Joint Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_9
msgid "Battery Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_10
msgid "Battery Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_11
msgid "Brake Caliper Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_12
msgid "Brake Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_13
msgid "Brake Pad(s) Replacement"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model_brand
msgid "Brand of the vehicle"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_break
msgid "Break"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_co2
msgid "CO2 Emissions"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_co2
msgid "CO2 emissions of the vehicle"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_1
msgid "Calculation Benefit In Kind"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_14
msgid "Car Wash"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_car_value
msgid "Catalog Value (VAT Incl.)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_15
msgid "Catalytic Converter Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_category
msgid "Category"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_cost_type
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_cost_type
msgid "Category of the cost"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_16
msgid "Charging System Diagnosis"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_vin_sn
msgid "Chassis Number"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_state
msgid "Choose whether the contract is still valid or not"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_service_type_category
msgid ""
"Choose whether the service refer to contracts, vehicle services or both"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_tag_action
msgid "Click to add a new tag."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid "Click to create a new contract."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_costs_action
msgid "Click to create a new cost."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_fuel_action
msgid "Click to create a new fuel log."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_brand_action
msgid "Click to create a new make."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "Click to create a new model."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid "Click to create a new odometer log."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid "Click to create a new service entry."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_contract_types_action
msgid "Click to create a new type of contract."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Click to create a new type of service."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid "Click to create a new vehicle."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid "Click to create a vehicle status."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Close Contract"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
msgid "Closed"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_color
msgid "Color"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_color
msgid "Color Index"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_color
msgid "Color of the vehicle"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_compact
msgid "Compact"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_company_id
msgid "Company"
msgstr "Compañía"

#. module: fleet
#: model:ir.ui.menu,name:fleet.fleet_configuration
msgid "Configuration"
msgstr ""

#. module: fleet
#: selection:fleet.service.type,category:0
#: selection:fleet.vehicle.cost,cost_type:0
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_contract_id
msgid "Contract"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_graph
msgid "Contract Costs Per Month"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_expiration_date
msgid "Contract Expiration Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_ins_ref
msgid "Contract Reference"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_start_date
msgid "Contract Start Date"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_contract_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_contract_types_menu
msgid "Contract Types"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_contract_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_contract_id
msgid "Contract attached to this cost"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Contract details"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_contract
msgid "Contract information on a vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_tree
msgid "Contract logs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_kanban
msgid "Contract(s)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_purchaser_id
msgid "Contractor"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_contract_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contracts
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Contracts"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_convertible
msgid "Convertible"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_cost_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_cost_id
msgid "Cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_description
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_description
msgid "Cost Description"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_form
msgid "Cost Details"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Cost Subtype"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Cost Type"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_cost
msgid "Cost related to a vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Cost that is paid only once at the creation of the contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_cost_subtype_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_cost_subtype_id
msgid "Cost type purchased with this cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_count
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_costs
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Costs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_action
msgid "Costs Analysis"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_graph
msgid "Costs Per Month"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_cost_generated
msgid ""
"Costs paid at regular intervals, depending on the cost frequency.If the cost"
" frequency is set to unique, the cost will be logged at the start date"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid ""
"Create a new contract automatically with all the same informations except "
"for the date that will start at the end of current contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_create_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_create_uid
msgid "Created by"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_create_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_create_date
msgid "Created on"
msgstr "Creado en"

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state_id
msgid "Current state of the vehicle"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Daily"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_date
msgid "Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_date
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_date
msgid "Date when the cost has been executed"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_start_date
msgid "Date when the coverage of the contract begins"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_expiration_date
msgid ""
"Date when the coverage of the contract expirates (by default, one year after"
" begin date)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_acquisition_date
msgid "Date when the vehicle has been immatriculated"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_2
msgid "Depreciation and Interests"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Diesel"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_display_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_display_name
msgid "Display Name"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_17
msgid "Door Window Motor/Regulator Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_doors
msgid "Doors Number"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_driver_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_driver_id
msgid "Driver"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_driver_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer_driver_id
msgid "Driver of the vehicle"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:216
#, python-format
msgid "Driver: from '%s' to '%s'"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Each contract (e.g.: leasing) may include several services\n"
"            (reparation, insurances, periodic maintenance)."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_service_types_action
msgid "Each service can used in contracts, as a standalone service or both."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Effective Costs"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Electric"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_17
msgid "Emissions"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_leasing
msgid "Employee Car"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_cost.py:44
#, python-format
msgid "Emptying the odometer value of a vehicle is not allowed."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_18
msgid "Engine Belt Inspection"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_19
msgid "Engine Coolant Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Engine Options"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_20
msgid "Engine/Drive Belt(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_13
msgid "Entry into service tax"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_21
msgid "Exhaust Manifold Replacement"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expired"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Expiring Soon"
msgstr ""

#. module: fleet
#: model:ir.module.category,name:fleet.module_fleet_category
#: model:ir.ui.menu,name:fleet.menu_root
msgid "Fleet"
msgstr ""

#. module: fleet
#: model:ir.actions.server,name:fleet.ir_cron_contract_costs_generator_ir_actions_server
#: model:ir.cron,cron_name:fleet.ir_cron_contract_costs_generator
#: model:ir.cron,name:fleet.ir_cron_contract_costs_generator
msgid "Fleet: Generate contracts costs based on costs frequency"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_cost_type
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_cost_type
msgid "For internal purpose only"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_cost_frequency
msgid "Frequency of the recuring cost"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Fuel"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_graph
msgid "Fuel Costs Per Month"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_22
msgid "Fuel Injector Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_fuel_logs_count
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_tree
msgid "Fuel Logs"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_23
msgid "Fuel Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_fuel_type
msgid "Fuel Type"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_fuel_type
msgid "Fuel Used by the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_fuel
msgid "Fuel log for vehicles"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Gasoline"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "General Properties"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_generated_cost_ids
msgid "Generated Costs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Generated Recurring Costs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Has Alert(s)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_contract_renewal_overdue
msgid "Has Contracts Overdue"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_contract_renewal_due_soon
msgid "Has Contracts to renew"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_24
msgid "Head Gasket(s) Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_25
msgid "Heater Blower Motor Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_26
msgid "Heater Control Valve Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_27
msgid "Heater Core Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_28
msgid "Heater Hose Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_fuel_action
msgid ""
"Here you can add refuelling entries for all vehicles.  You can\n"
"            also filter logs of a particular vehicle using the search\n"
"            field."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_odometer_action
msgid ""
"Here you can add various odometer entries for all vehicles.\n"
"            You can also show odometer value for a particular vehicle using\n"
"            the search field."
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_horsepower
msgid "Horsepower"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_horsepower_tax
msgid "Horsepower Taxation"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,fuel_type:0
msgid "Hybrid"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_id_3068
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_id_3057
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_id_2903
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_id
msgid "ID"
msgstr "ID"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_29
msgid "Ignition Coil Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_acquisition_date
msgid "Immatriculation Date"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "In Progress"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_cost_ids
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_cost_ids
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Included Services"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,state:0
msgid "Incoming"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Indicative Cost"
msgstr ""

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting_indicative_costs
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Indicative Costs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_costs_reporting_non_effective_action
msgid "Indicative Costs Analysis"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_sum_cost
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Indicative Costs Total"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle
msgid "Information on a vehicle"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_30
msgid "Intake Manifold Gasket Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Invoice Date"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_inv_ref
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_inv_ref
msgid "Invoice Reference"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_junior
msgid "Junior"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,odometer_unit:0
msgid "Kilometers"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_kanban
msgid "Km"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state___last_update
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag___last_update
msgid "Last Modified on"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer
msgid "Last Odometer"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_write_uid
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_write_uid
msgid "Last Updated by"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_write_date
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_write_date
msgid "Last Updated on"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_leasing
msgid "Leasing"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_license_plate
msgid "License Plate"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:223
#, python-format
msgid "License Plate: from '%s' to '%s'"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_license_plate
msgid "License plate number of the vehicle (i = plate number for a car)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_liter
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_kanban
msgid "Liter"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_location
msgid "Location"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_location
msgid "Location of the vehicle (garage, ...)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_image
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_image
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_image
msgid "Logo"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_image_medium
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_image_medium
msgid "Logo (medium)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_image_small
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_image_small
msgid "Logo (small)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_name
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Make"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand_id
msgid "Make of the vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_contract_action
msgid ""
"Manage all your contracts (leasing, insurances, etc.) with\n"
"            their related services, costs. Odoo will automatically warn\n"
"            you when some contracts have to be renewed."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_11
msgid "Management Fee"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_manager
msgid "Manager"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,transmission:0
msgid "Manual"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_image_medium
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand_image_medium
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_image_medium
msgid ""
"Medium-sized logo of the brand. It is automatically resized as a 128x128px "
"image, with aspect ratio preserved. Use this field in form views or some "
"kanban views."
msgstr ""

#. module: fleet
#: selection:fleet.vehicle,odometer_unit:0
msgid "Miles"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Model"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_brand_view_tree
msgid "Model Make"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_year
msgid "Model Year"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_brand_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_brand_menu
msgid "Model make of Vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_name
msgid "Model name"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_model
msgid "Model of a vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_id
msgid "Model of the vehicle"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:212
#, python-format
msgid "Model: from '%s' to '%s'"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_tree
msgid "Models"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Month"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Monthly"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_service_type_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_name
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_name
msgid "Name"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_contract_renewal_name
msgid "Name of contract to renew soon"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "No"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:211
#: code:addons/fleet/models/fleet_vehicle.py:215
#: code:addons/fleet/models/fleet_vehicle.py:219
#: code:addons/fleet/models/fleet_vehicle.py:222
#, python-format
msgid "None"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_notes
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Notes"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_doors
msgid "Number of doors of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_seats
msgid "Number of seats of the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_odometer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_count
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Odometer"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Odometer Details"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_tree
msgid "Odometer Logs"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_unit
msgid "Odometer Unit"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_odometer
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_value
msgid "Odometer Value"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_graph
msgid "Odometer Values Per Vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_odometer
msgid "Odometer at creation"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Odometer details"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_odometer
msgid "Odometer log for a vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_odometer
msgid "Odometer measure of the vehicle at the moment of the contract creation"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_odometer
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_odometer_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer
msgid "Odometer measure of the vehicle at the moment of this log"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_log_services_action
msgid ""
"Odoo helps you keeping track of all the services done\n"
"            on your vehicle. Services can be of many type: occasional\n"
"            repair, fixed maintenance, etc."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_costs_action
msgid ""
"Odoo helps you managing the costs for your different\n"
"                vehicles. Costs are created automatically from services,\n"
"                contracts (fixed or recurring) and fuel logs."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_non_effective_action
msgid ""
"Odoo helps you managing the costs for your different vehicles\n"
"          Costs are generally created from services and contract and appears here."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid ""
"Odoo will warn you when services or contract have to be\n"
"            renewed."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_31
msgid "Oil Change"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_32
msgid "Oil Pump Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_omnium
msgid "Omnium"
msgstr ""

#. module: fleet
#: sql_constraint:fleet.vehicle:0
msgid "Only one car can be assigned to the same employee!"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_16
msgid "Options"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
msgid "Other"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_33
msgid "Other Maintenance"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_34
msgid "Oxygen Sensor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_parent_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_parent_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Parent"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_parent_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_parent_id
msgid "Parent cost to this current cost"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_purchaser_id
msgid "Person to which the contract is signed for"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_power
msgid "Power"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_35
msgid "Power Steering Hose Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_36
msgid "Power Steering Pump Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_power
msgid "Power in kW of the vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_tree
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Price"
msgstr "Precio"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_price_per_liter
msgid "Price Per Liter"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_purchased
msgid "Purchased"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_purchaser_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_purchaser_id
msgid "Purchaser"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_37
msgid "Radiator Repair"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_generated
msgid "Recurring Cost Amount"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_frequency
msgid "Recurring Cost Frequency"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_refueling
msgid "Refueling"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Refueling Details"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle_cost.py:200
#: model:ir.actions.act_window,name:fleet.act_renew_contract
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#, python-format
msgid "Renew Contract"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_12
msgid "Rent (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_8
msgid "Repair and maintenance"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_contract_repairing
msgid "Repairing"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_10
msgid "Replacement Vehicle"
msgstr ""

#. module: fleet
#: model:ir.ui.menu,name:fleet.menu_fleet_reporting
msgid "Reporting"
msgstr "Informes"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_residual_value
msgid "Residual Value"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_15
msgid "Residual value (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_19
msgid "Residual value in %"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_38
msgid "Resurface Rotors"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_39
msgid "Rotate Tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_40
msgid "Rotor Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_seats
msgid "Seats Number"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_sedan
msgid "Sedan"
msgstr ""

#. module: fleet
#: model:fleet.vehicle.tag,name:fleet.vehicle_tag_senior
msgid "Senior"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: fleet
#: selection:fleet.service.type,category:0
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Service"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Service Type"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_service_types_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_service_types_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_service_types_view_tree
msgid "Service Types"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.cost,cost_type:0
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_service_count
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "Services"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_graph
msgid "Services Costs Per Month"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Services Details"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Services Logs"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Set Contract In Progress"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_brand_image_small
msgid "Small-sized image"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_image_small
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand_image_small
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_image_small
msgid ""
"Small-sized logo of the brand. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_6
msgid "Snow tires"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_41
msgid "Spark Plug Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_42
msgid "Starter Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_state_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_state_view_tree
msgid "State"
msgstr ""

#. module: fleet
#: sql_constraint:fleet.vehicle.state:0
msgid "State name already exists"
msgstr ""

#. module: fleet
#: code:addons/fleet/models/fleet_vehicle.py:220
#, python-format
msgid "State: from '%s' to '%s'"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_state
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
msgid "Status"
msgstr "Estado"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_5
#: model:fleet.service.type,name:fleet.type_service_service_7
msgid "Summer tires"
msgstr ""

#. module: fleet
#: sql_constraint:fleet.vehicle.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_tag_ids
msgid "Tags"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_3
msgid "Tax roll"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_notes
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Terms and Conditions"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_action
#: model_terms:ir.actions.act_window,help:fleet.fleet_costs_reporting_non_effective_action
msgid ""
"Thanks to the different filters, Odoo can only print the effective\n"
"          costs, sort them by type and by vehicle."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_43
msgid "Thermostat Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_image
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_brand_image
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_image
msgid ""
"This field holds the image used as logo for the brand, limited to "
"1024x1024px."
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_44
msgid "Tie Rod End Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_45
msgid "Tire Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_46
msgid "Tire Service"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_tree
msgid "Total"
msgstr "Total"

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_amount
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_amount
msgid "Total Price"
msgstr "Precio total"

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_14
msgid "Total expenses (Excluding VAT)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_contract_renewal_total
msgid "Total of contracts due or overdue minus one"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_service_18
msgid "Touring Assistance"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_transmission
msgid "Transmission"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_47
msgid "Transmission Filter Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_48
msgid "Transmission Fluid Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_49
msgid "Transmission Replacement"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_transmission
msgid "Transmission Used by the vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_cost_subtype_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_cost_subtype_id
msgid "Type"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_service_type
msgid "Type of services available on a vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_vin_sn
msgid "Unique number written on the vehicle motor (VIN/SN number)"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_odometer_unit
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_unit_2892
msgid "Unit"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer_unit
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_odometer_unit_2892
msgid "Unit of the odometer "
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_state_sequence
msgid "Used to order the note stages"
msgstr ""

#. module: fleet
#: model:res.groups,name:fleet.fleet_group_user
msgid "User"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_car_value
msgid "Value of the bought vehicle"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_cost_vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_vehicle_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_odometer_vehicle_id
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_tree
msgid "Vehicle"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_costs_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_costs_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_tree
msgid "Vehicle Costs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Vehicle Costs by Month"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Vehicle Details"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_model_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_model_menu
msgid "Vehicle Model"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_state_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_state_menu
msgid "Vehicle Status"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_tag_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_tag_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_tag_view_view_form
msgid "Vehicle Tags"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_cost_vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_fuel_vehicle_id
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_services_vehicle_id
msgid "Vehicle concerned by this log"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_form
msgid "Vehicle costs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_menu
#: model:ir.ui.menu,name:fleet.fleet_vehicles
msgid "Vehicles"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_contract_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_contract_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_search
msgid "Vehicles Contracts"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_fuel_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_fuel_menu
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_search
msgid "Vehicles Fuel Logs"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_odometer_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_odometer_menu
msgid "Vehicles Odometer"
msgstr ""

#. module: fleet
#: model:ir.actions.act_window,name:fleet.fleet_vehicle_log_services_action
#: model:ir.ui.menu,name:fleet.fleet_vehicle_log_services_menu
msgid "Vehicles Services Logs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_indicative_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_indicative_view_pivot
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_graph
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_cost_view_pivot
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_search
msgid "Vehicles costs"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_odometer_view_search
msgid "Vehicles odometers"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_insurer_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_fuel_vendor_id
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_services_vendor_id
msgid "Vendor"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_model_vendors
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "Vendors"
msgstr ""

#. module: fleet
#: model:ir.model.fields,field_description:fleet.field_fleet_vehicle_log_contract_days_left
msgid "Warning Date"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_50
msgid "Water Pump Replacement"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Weekly"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_51
msgid "Wheel Alignment"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_52
msgid "Wheel Bearing Replacement"
msgstr ""

#. module: fleet
#: model:fleet.service.type,name:fleet.type_service_53
msgid "Windshield Wiper(s) Replacement"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "Write here all other information relative to this contract"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_log_contract_notes
msgid "Write here all supplementary information relative to this contract"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_fuel_view_form
msgid "Write here any other information"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_services_view_form
msgid "Write here any other information related to the service completed."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_costs_view_search
msgid "Year"
msgstr ""

#. module: fleet
#: model:ir.model.fields,help:fleet.field_fleet_vehicle_model_year
msgid "Year of the model"
msgstr ""

#. module: fleet
#: selection:fleet.vehicle.log.contract,cost_frequency:0
msgid "Yearly"
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_state_action
msgid ""
"You can customize available status to track the evolution of\n"
"            each vehicle. Example: Active, Being Repaired, Sold."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_model_action
msgid "You can define several models (e.g. A3, A4) for each make (Audi)."
msgstr ""

#. module: fleet
#: model_terms:ir.actions.act_window,help:fleet.fleet_vehicle_action
msgid ""
"You will be able to manage your fleet by keeping track of the\n"
"            contracts, services, fixed and recurring costs, odometers and\n"
"            fuel logs associated to each vehicle."
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_log_contract_view_form
msgid "amount"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. Model S"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "e.g. PAE 326"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_model_view_form
msgid "e.g. Tesla"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_state
msgid "fleet.vehicle.state"
msgstr ""

#. module: fleet
#: model:ir.model,name:fleet.model_fleet_vehicle_tag
msgid "fleet.vehicle.tag"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show all the costs for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the contract for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the fuel logs for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the odometer logs for this vehicle"
msgstr ""

#. module: fleet
#: model_terms:ir.ui.view,arch_db:fleet.fleet_vehicle_view_form
msgid "show the services logs for this vehicle"
msgstr ""
