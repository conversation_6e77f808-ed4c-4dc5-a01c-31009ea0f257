# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_adyen
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:187
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:185
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:229
#, python-format
msgid "Adyen: feedback error"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:197
#, python-format
msgid "Adyen: invalid merchantSig, received %s, computed %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:183
#, python-format
msgid "Adyen: received data for reference %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment.py:176
#, python-format
msgid ""
"Adyen: received data with missing reference (%s) or missing pspReference "
"(%s)"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_adyen
#: model_terms:ir.ui.view,arch_db:payment_adyen.acquirer_form_adyen
msgid "How to configure your Adyen account?"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_merchant_account
msgid "Merchant Account"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Payment Acquirer"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__provider
msgid "Provider"
msgstr "Provider"

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_code
msgid "Skin Code"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_skin_hmac_key
msgid "Skin HMAC Key"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_adyen
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""
