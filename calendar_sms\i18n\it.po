# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar_sms
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_event
msgid "Calendar Event"
msgstr "Evento in calendario"

#. module: calendar_sms
#: model:sms.template,name:calendar_sms.sms_template_data_calendar_reminder
msgid "Calendar Event: Reminder"
msgstr "Evento in calendario - Promemoria"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm
msgid "Event Alarm"
msgstr "Avviso evento"

#. module: calendar_sms
#: model:ir.model,name:calendar_sms.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "Gestore avvisi evento"

#. module: calendar_sms
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "Event reminder: %(name)s, %(time)s."
msgstr "Promemoria evento: %(name)s, %(time)s."

#. module: calendar_sms
#: model:sms.template,body:calendar_sms.sms_template_data_calendar_reminder
msgid "Event reminder: {{ object.name }}, {{ object.display_time }}"
msgstr "Promemoria evento: {{ object.name }}, {{ object.display_time }}"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "SMS"
msgstr "SMS"

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__sms_template_id
msgid "SMS Template"
msgstr "Modello SMS"

#. module: calendar_sms
#: model:ir.model.fields.selection,name:calendar_sms.selection__calendar_alarm__alarm_type__sms
msgid "SMS Text Message"
msgstr "Messaggio SMS"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_tree_inherited
msgid "Send SMS"
msgstr "Invia SMS"

#. module: calendar_sms
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "Send SMS Text Message"
msgstr "Invia messaggio SMS"

#. module: calendar_sms
#: model_terms:ir.ui.view,arch_db:calendar_sms.view_calendar_event_form_inherited
msgid "Send SMS to attendees"
msgstr "Invia SMS ai partecipanti"

#. module: calendar_sms
#: model:ir.model.fields,help:calendar_sms.field_calendar_alarm__sms_template_id
msgid "Template used to render SMS reminder content."
msgstr "Modello utilizzato per rendere il contenuto del promemoria SMS."

#. module: calendar_sms
#: code:addons/calendar_sms/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "Non ci sono partecipanti a questi eventi"

#. module: calendar_sms
#: model:ir.model.fields,field_description:calendar_sms.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "Tipologia"
