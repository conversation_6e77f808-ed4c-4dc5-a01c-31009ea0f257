<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="tag_cl_sale_mnt_iva" model="account.account.tag">
            <!-- TotMntIVA -->
            <field name="name">Ventas - Monto IVA</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_mnt_fuera_plazo" model="account.account.tag">
            <!-- TotIVAFueraPlazo -->
            <field name="name">Ventas - IVA Fuera de Plazo</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_propio" model="account.account.tag">
            <!-- TotIVAPropio -->
            <field name="name">Ventas - IVA Propio</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_terceros" model="account.account.tag">
            <!-- TotIVATerceros -->
            <field name="name">Ventas - IVA Terceros</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_18211" model="account.account.tag">
            <!-- TotLey18211 -->
            <field name="name">Ventas - IVA Ley 18211</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_otros_imp" model="account.account.tag">
            <!-- TotOtrosImp -->
            <field name="name">Ventas - Otros Impuestos</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_ret_total" model="account.account.tag">
            <!-- TotIVARetTotal -->
            <field name="name">Ventas - IVA Retenido Total</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_ret_parcial" model="account.account.tag">
            <!-- TotIVARetParcial -->
            <field name="name">Ventas - IVA Retenido Parcial</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_cred_ec" model="account.account.tag">
            <!-- TotCredEC -->
            <field name="name">Ventas - Credito Especial 65% Empresas Constructoras</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_dep_env" model="account.account.tag">
            <!-- TotDepEnvase -->
            <field name="name">Ventas - Depósito de Envases</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_comisiones" model="account.account.tag">
            <!-- TotValComIVA -->
            <field name="name">Ventas - IVA Comisiones y Otros Cargos</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_sale_iva_no_retenido" model="account.account.tag">
            <!-- TotOpIVANoRetenido -->
            <field name="name">Ventas - IVA No Retenido</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva" model="account.account.tag">
            <!-- TotMntIVA -->
            <field name="name">Compras - Monto IVA Recuperable</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva_actf" model="account.account.tag">
            <!-- TotMntIVAActivoFijo -->
            <field name="name">Compras - Monto IVA Activo Fijo</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva_actf_uso_comun" model="account.account.tag">
            <!-- TotMntIVAActivoFijo -->
            <field name="name">Compras - Monto IVA Activo Fijo (Uso Común)</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva_actf_no_recup" model="account.account.tag">
            <!-- TotMntIVAActivoFijo -->
            <field name="name">Compras - Monto IVA Activo Fijo (Uso Común)</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>


        <record id="tag_cl_purchase_mnt_iva_no_rec" model="account.account.tag">
            <!-- TotMntIVANoRec -->
            <field name="name">Compras - Monto IVA No Recuperable</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva_uso_comun" model="account.account.tag">
            <!-- TotMntIVANoRec -->
            <field name="name">Compras - Monto IVA Uso Comun</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_iva_supermercado" model="account.account.tag">
            <field name="name">Compras - Monto IVA Compras Supermercado</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_mnt_otros_imp" model="account.account.tag">
            <!-- TotOtrosImp -->
            <field name="name">Compras - Monto Otros Impuestos</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_imp_sin_credito" model="account.account.tag">
            <!-- TotImpSinCredito -->
            <field name="name">Compras - Impuestos Sin Crédito</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_iva_no_ret" model="account.account.tag">
            <!-- TotIVANoRetenido -->
            <field name="name">Compras - IVA No Retenido Fac de compra</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_imp_42" model="account.account.tag">
            <!-- TotCredImp -->
            <field name="name">Compras - Credito Imp Art 42</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_imp_sin_cred" model="account.account.tag">
            <!-- TotImpSinCredito -->
            <field name="name">Compras - Impuesto Sin Credito</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_iva_no_reten" model="account.account.tag">
            <!-- TotIVANoRetenido -->
            <field name="name">Compras - IVA No Retenido</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

        <record id="tag_cl_purchase_imp_vehic" model="account.account.tag">
            <!-- TotImpVehiculo -->
            <field name="name">Compras - Impuesto a Vehículos Automotores</field>
            <field name="applicability">taxes</field>
            <field name="country_id" ref="base.cl"/>
        </record>

    </data>
</odoo>