<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">

    <!--=================-->
    <!-- Edition Iframe -->
    <!--=================-->
    <t t-name="wysiwyg.iframeContent"><head>
            <meta charset="utf-8"/>
            <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <t t-foreach="assets || []" t-as="asset">
                <t t-foreach="asset.cssLibs || []" t-as="cssLib">
                    <link type="text/css" rel="stylesheet" t-att-href="cssLib"/>
                </t>
                <t t-foreach="asset.cssContents || []" t-as="cssContent">
                    <style type="text/css" t-out="cssContent"/>
                </t>
                <t t-foreach="asset.jsLibs || []" t-as="jsLib">
                    <script type="text/javascript" t-att-src="jsLib"/>
                </t>
                <t t-foreach="asset.jsContents || []" t-as="jsContent" t-if="jsContent.indexOf('inline asset') !== -1">
                    <script type="text/javascript" t-out="jsContent"/>
                </t>
            </t>
            <script type="text/javascript">
                odoo.define('web.session', function () {
                    return window.top.odoo.__DEBUG__.services['web.session'];
                });

                odoo.define('root.widget', function (require) {
                    'use strict';
                    var Widget = require('web.Widget');
                    var widget = new Widget();
                    widget.appendTo(document.body);
                    return widget;
                });

                odoo.define('web.core.top', function (require) {
                    var core = require('web.core');
                    core.qweb.templates = window.top.odoo.__DEBUG__.services['web.core'].qweb.templates;
                });
            </script>
        </head>
        <body class="o_in_iframe">
            <div id="iframe_target"/>
            <script type="text/javascript">
                odoo.define('web_editor.wysiwyg.iniframe', function (require) {
                    'use strict';
                    if (window.top.<t t-esc="updateIframeId"/>) {
                        window.top.<t t-esc="updateIframeId"/>(<t t-esc="avoidDoubleLoad"/>);
                    }
                });
            </script>
        </body>
    </t>

    <!--=================-->
    <!-- Edition Dialogs -->
    <!--=================-->

    <!-- Alt Dialog (allows to change alt and title of page images) -->
    <form t-name="wysiwyg.widgets.alt" action="#">
        <div class="form-group row">
            <label class="col-md-3 col-form-label" for="alt"
                   title="'Alt tag' specifies an alternate text for an image, if the image cannot be displayed (slow connection, missing image, screen reader ...).">
               Description <small>(ALT Tag)</small>
            </label>
            <div class="col-md-8">
                <input class="form-control" id="alt" required="required" t-att-value="widget.alt" type="text"/>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-3 col-form-label" for="title"
                   title="'Title tag' is shown as a tooltip when you hover the picture.">
               Tooltip  <small>(TITLE Tag)</small>
            </label>
            <div class="col-md-8">
                <input class="form-control" id="title" required="required" t-att-value="widget.tag_title" type="text"/>
            </div>
        </div>
    </form>

    <!-- Media Dialog (allows to choose an img/pictogram/video) -->
    <div t-name="wysiwyg.widgets.media">
        <ul class="nav nav-tabs" role="tablist">
            <li t-if="!widget.options.noImages" class="nav-item"><a t-attf-class="nav-link #{widget.isImageActive() ? 'active' : ''}" id="editor-media-image-tab" data-toggle="tab" href="#editor-media-image" role="tab" aria-controls="editor-media-image" t-att-aria-selected="widget.isImageActive().toString()">Image</a></li>
            <li t-if="!widget.options.noDocuments" class="nav-item"><a t-attf-class="nav-link #{widget.isDocumentActive() ? 'active' : ''}" id="editor-media-document-tab" data-toggle="tab" href="#editor-media-document" role="tab" aria-controls="editor-media-document" t-att-aria-selected="widget.isDocumentActive().toString()">Document</a></li>
            <li t-if="!widget.options.noIcons" class="nav-item"><a t-attf-class="nav-link #{widget.isIconActive() ? 'active' : ''}" id="editor-media-icon-tab" data-toggle="tab" href="#editor-media-icon" role="tab" aria-controls="editor-media-icon" t-att-aria-selected="widget.isIconActive().toString()">Pictogram</a></li>
            <li t-if="!widget.options.noVideos" class="nav-item"><a t-attf-class="nav-link #{widget.isVideoActive() ? 'active' : ''}" id="editor-media-video-tab" data-toggle="tab" href="#editor-media-video" role="tab" aria-controls="editor-media-video" t-att-aria-selected="widget.isVideoActive().toString()">Video</a></li>
        </ul>
        <!-- Tab panes -->
        <div class="tab-content">
            <div t-if="!widget.options.noImages" t-attf-class="tab-pane fade #{widget.isImageActive() ? 'show active': ''}" id="editor-media-image" role="tabpanel" aria-labelledby="editor-media-image-tab"/>
            <div t-if="!widget.options.noDocuments" t-attf-class="tab-pane fade #{widget.isDocumentActive() ? 'show active': ''}" id="editor-media-document" role="tabpanel" aria-labelledby="editor-media-document-tab"/>
            <div t-if="!widget.options.noIcons" t-attf-class="tab-pane fade #{widget.isIconActive() ? 'show active': ''}" id="editor-media-icon" role="tabpanel" aria-labelledby="editor-media-icon-tab"/>
            <div t-if="!widget.options.noVideos" t-attf-class="tab-pane fade #{widget.isVideoActive() ? 'show active': ''}" id="editor-media-video" role="tabpanel" aria-labelledby="editor-media-video-tab"/>
        </div>
    </div>

    <t t-name="wysiwyg.widgets.media.search">
        <div class="input-group mr-auto">
            <input type="text" class="form-control o_we_search" t-att-placeholder="searchPlaceholder.trim()"/>
            <div class="input-group-append">
                <div class="input-group-text o_we_search_icon">
                    <i class="fa fa-search" title="Search" role="img" aria-label="Search"/>
                </div>
            </div>
        </div>
    </t>

    <!-- File choosing part of the Media Dialog -->
    <t t-name="wysiwyg.widgets.file">
        <form>
            <t t-call="wysiwyg.widgets.files.submenu"/>
            <div class="form-text o_we_error_text"/>
            <div class="o_we_existing_attachments"/>
            <div class="mt-4 text-center mx-auto o_we_load_more">
                <button class="btn btn-primary o_load_more d-none" type="button">Load more...</button>
                <div class="mt-4 o_load_done_msg d-none">
                    <span><i>All images have been loaded</i></span>
                </div>
            </div>
        </form>
    </t>

    <t t-name="wysiwyg.widgets.files.submenu">
        <div class="form-inline align-items-center py-4">
            <t t-call="wysiwyg.widgets.media.search"/>
            <t t-out="0"/>

            <input type="file" class="d-none o_file_input" name="upload" t-att-accept="widget.options.accept" t-att-multiple="widget.options.multiImages &amp;&amp; 'multiple'"/>

            <div class="input-group align-items-center ml-2">
                <input type="text" class="form-control o_we_url_input o_we_horizontal_collapse o_we_transition_ease" name="url" t-att-placeholder="urlPlaceholder"/>
                <div class="input-group-append align-items-center">
                    <button type="button" class="btn btn-secondary o_upload_media_url_button">
                        <t t-out="addText"/>
                    </button>
                    <div class="mx-2">
                        <span class="o_we_url_success text-success d-none fa fa-lg fa-check" title="The URL seems valid."/>
                        <span t-if="urlWarningTitle" class="o_we_url_warning text-warning d-none fa fa-lg fa-warning" t-att-title="urlWarningTitle"/>
                        <span class="o_we_url_error text-danger d-none fa fa-lg fa-times" title="The URL does not seem to work."/>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-primary o_upload_media_button">
                    <t t-out="uploadText"/>
                </button>
            </div>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.image">
        <t t-call="wysiwyg.widgets.file">
            <t t-set="uploadText">Upload an image</t>
            <t t-set="urlPlaceholder">https://www.odoo.com/logo.png</t>
            <t t-set="addText">Add URL</t>
            <t t-set="searchPlaceholder">Search an image</t>
            <t t-set="urlWarningTitle">Uploaded image's format is not supported. Try with: </t>
            <t t-set="urlWarningTitle" t-value="urlWarningTitle + widget.IMAGE_EXTENSIONS.join(', ')"/>
            <div class="d-flex justify-content-start flex-grow-1 ml-2">
                <div t-attf-class="custom-control custom-switch #{__debug__ and 'd-flex' or 'd-none'} align-items-center mr-2">
                    <input class="o_we_show_optimized ml-2 custom-control-input" type="checkbox" id="o_we_show_optimized_switch"/>
                    <label class="custom-control-label" for="o_we_show_optimized_switch">
                        Show optimized images
                    </label>
                </div>
                <select class="custom-select o_we_search_select d-none">
                    <option value="all">All</option>
                    <option value="database">My Images</option>
                    <option t-if="widget.options.useMediaLibrary" value="media-library">Illustrations</option>
                </select>
            </div>
        </t>
    </t>

    <t t-name="wysiwyg.widgets.document">
        <t t-call="wysiwyg.widgets.file">
            <t t-set="uploadText">Upload a document</t>
            <t t-set="urlPlaceholder">https://www.odoo.com/mydocument</t>
            <t t-set="addText">Add URL</t>
            <t t-set="searchPlaceholder">Search a document</t>
        </t>
    </t>

    <t t-name="wysiwyg.widgets.image.existing.attachments">
        <div class="o_we_existing_attachments o_we_images d-flex flex-wrap w-100 justify-content-between align-items-stretch my-0">
            <t t-if="!widget.hasContent()">
                <div t-if="widget.needle" class="o_nocontent_help">
                    <p class="o_empty_folder_image">No images found.</p>
                    <p class="o_empty_folder_subtitle">You can upload images with the button located in the top left of the screen.</p>
                </div>
                <div t-else="" class="o_we_search_prompt">
                    <h2>Get the perfect image by searching in our library of copyright free photos and illustrations.</h2>
                </div>
            </t>
            <t t-else="">
                <t t-if="['all', 'database'].includes(widget.searchService)" t-foreach="attachments" t-as="attachment">
                    <t t-call="wysiwyg.widgets.image.existing.attachment"/>
                </t>
                <t t-if="['all', 'media-library'].includes(widget.searchService)" t-foreach="libraryMedia" t-as="media">
                    <t t-call="wysiwyg.widgets.image.library_media"/>
                </t>
                <!-- 20 placeholders is just enough for a 5K screen, change this if ImageWidget.MIN_ROW_HEIGHT changes -->
                <t t-foreach="20">
                    <div class="o_we_attachment_placeholder"/>
                </t>
            </t>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.image.existing.attachment">
        <t t-set="isOptimized" t-value="!!attachment.original_id"/>
        <div t-attf-class="o_existing_attachment_cell position-relative bg-light #{isOptimized and 'o_we_attachment_optimized d-none' or ''} align-items-center justify-content-center" t-att-data-id="attachment.id">
            <t t-call="wysiwyg.widgets.file.existing.remove"/>
            <div class="o_we_media_dialog_img_wrapper">
                <img class="img img-fluid o_we_attachment_highlight" t-attf-src="#{attachment.thumbnail_src or attachment.image_src}" t-att-alt="attachment.name" t-att-title="attachment.name"/>
            </div>
            <span t-if="isOptimized" class="badge badge-success">Optimized</span>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.image.library_media">
        <div t-attf-class="o_existing_attachment_cell position-relative bg-light align-items-center justify-content-center o_library_media_cell" t-att-data-media-id="media.id">
            <div class="o_we_media_dialog_img_wrapper">
                <img class="img img-fluid o_we_attachment_highlight" t-attf-src="#{media.thumbnail_url}" t-att-title="media.tooltip or ''" crossorigin="anonymous"/>
                <a t-if="media.author" class="o_we_media_author" t-att-href="media.author_link" target="_blank" t-esc="media.author"/>
            </div>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.document.existing.attachments">
        <div class="o_we_existing_attachments o_we_documents">
            <div t-if="!attachments.length" class="o_nocontent_help">
                <p class="o_empty_folder_image">No documents found.</p>
                <p class="o_empty_folder_subtitle">You can upload documents with the button located in the top left of the screen.</p>
            </div>
            <div t-else="" class="row mx-auto">
                <t t-foreach="attachments" t-as="attachment">
                    <div class="col-2 o_existing_attachment_cell o_we_attachment_highlight my-2" t-att-data-id="attachment.id">
                        <t t-call="wysiwyg.widgets.file.existing.remove"/>

                        <div t-att-data-url="attachment.url" role="img" t-att-aria-label="attachment.name" t-att-title="attachment.name" t-att-data-mimetype="attachment.mimetype" t-attf-class="o_image d-flex align-items-center justify-content-center"/>
                        <small class="o_file_name" t-esc="attachment.name"/>
                    </div>
                </t>
            </div>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.file.existing.remove">
        <t t-set="removeTitle" t-if="attachment.res_model === 'ir.ui.view'">This file is a public view attachment.</t>
        <t t-set="removeTitle" t-else="">This file is attached to the current record.</t>
        <i class="fa fa-trash o_existing_attachment_remove p-2" t-att-title="removeTitle" role="img" t-att-aria-label="removeTitle"/>
    </t>

    <t t-name="wysiwyg.widgets.image.existing.error">
        <div class="form-text">
            <p>The image could not be deleted because it is used in the
               following pages or views:</p>
            <ul t-as="view" t-foreach="views">
                <li>
                    <a t-attf-href="/web#model=ir.ui.view&amp;id=#{view.id}">
                        <t t-esc="view.name"/>
                    </a>
                </li>
            </ul>
        </div>
    </t>

    <t t-name="wysiwyg.widgets.upload.progressbar">
        <div t-attf-class="o_we_progressbar js_progressbar_#{fileId}">
            <div class="d-flex text-info">
                <span><i class="fa fa-spinner fa-pulse"/></span>
                <span class="font-italic font-weight-bold text-truncate flex-grow-1 mx-2" t-esc="fileName"/>
                <span class="text-nowrap" t-esc="fileSize"/>
            </div>
            <div class='js_progressbar_txt'>
                <span class="text-success d-none"><i class='fa fa-check'/> File has been uploaded</span>
                <span class="text-danger d-none"><i class='fa fa-times float-left m-1'/> <span class="o_we_error_text">File could not be saved</span></span>
            </div>
            <div class="progress">
                <div class="progress-bar bg-info progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;">0%</div>
            </div>
            <hr/>
        </div>
    </t>
    <t t-name="wysiwyg.widgets.upload.progress_toast">
        <div class="o_notification_manager">
            <div t-attf-class="o_notification bg-info fade show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="o_notification_body">
                    <button type="button" class="close o_notification_close"
                    aria-label="Close">
                        <span class="d-inline" aria-hidden="true">×</span>
                    </button>
                    <div class="mr-auto o_notification_content"/>
                </div>
            </div>
        </div>
    </t>

    <!-- Icon choosing part of the Media Dialog -->
    <t t-name="wysiwyg.widgets.font-icons">
        <form action="#">
            <div class="form-inline align-items-center py-4">
                <t t-call="wysiwyg.widgets.media.search">
                    <t t-set="searchPlaceholder">Search a pictogram</t>
                </t>
            </div>
            <div class="font-icons-icons">
                <t t-call="wysiwyg.widgets.font-icons.icons">
                    <t t-set="iconsParser" t-value="widget.iconsParser"/>
                </t>
            </div>
        </form>
    </t>
    <t t-name="wysiwyg.widgets.font-icons.icons">
        <div t-if="!iconsParser.length" class="o_nocontent_help">
            <p class="o_empty_folder_image">No pictograms found.</p>
            <p class="o_empty_folder_subtitle">Try searching with other keywords.</p>
        </div>
        <t t-as="data" t-foreach="iconsParser">
            <span t-foreach="data.cssData" t-as="cssData"
                t-att-data-id="cssData.names[0]"
                t-att-title="cssData.names[0]"
                t-att-aria-label="cssData.names[0]" role="img"
                t-attf-class="font-icons-icon #{data.base} #{cssData.names[0]}"
                t-att-data-alias="cssData.names.join(',')"/>
        </t>
    </t>

    <!-- Video choosing part of the Media Dialog -->
    <t t-name="wysiwyg.widgets.video">
        <form action="#" class="row">
            <div class="col mt-4 o_video_dialog_form">
                <div class="form-group mb-2" id="o_video_form_group">
                    <label class="col-form-label" for="o_video_text">
                        Video code <small>(URL or Embed)</small>
                    </label>
                    <div class="text-left">
                        <small class="text-muted">Accepts <b><i>Youtube</i></b>, <b><i>Vimeo</i></b>, <b><i>Dailymotion</i></b> and <b><i>Youku</i></b> videos</small>
                    </div>
                    <textarea class="form-control" id="o_video_text" placeholder="Copy-paste your URL or embed code here"/>
                </div>
                <div class="o_video_dialog_options d-none mt-4">
                    <div class="o_youtube_option o_vimeo_option o_dailymotion_option">
                      <label class="o_switch mb0">
                        <input id="o_video_autoplay" type="checkbox"/><span/>
                        <span style="margin-right: 8px;">Autoplay</span>
                        <span class="small text-muted" style="margin-left: auto;">Videos are muted when autoplay is enabled</span>
                      </label>
                    </div>
                    <div class="o_youtube_option o_vimeo_option">
                        <label class="o_switch mb0"><input id="o_video_loop" type="checkbox"/><span/>Loop</label>
                    </div>
                    <div class="o_youtube_option o_dailymotion_option o_vimeo_option">
                        <label class="o_switch mb0"><input id="o_video_hide_controls" type="checkbox"/><span/>Hide player controls</label>
                    </div>
                    <div class="o_youtube_option">
                        <label class="o_switch mb0"><input id="o_video_hide_fullscreen" type="checkbox"/><span/>Hide fullscreen button</label>
                    </div>
                    <div class="o_youtube_option">
                        <label class="o_switch mb0"><input id="o_video_hide_yt_logo" type="checkbox"/><span/>Hide Youtube logo</label>
                    </div>
                    <div class="o_dailymotion_option">
                        <label class="o_switch mb0"><input id="o_video_hide_dm_logo" type="checkbox"/><span/>Hide Dailymotion logo</label>
                    </div>
                    <div class="o_dailymotion_option">
                        <label class="o_switch mb0"><input id="o_video_hide_dm_share" type="checkbox"/><span/>Hide sharing button</label>
                    </div>
                </div>
                <t t-if="widget._vimeoPreviewIds || widget._youtubePreviewIds">
                    <span class="font-weight-bold">Suggestions</span>
                    <div id="video-suggestion" class="mt-4 d-flex flex-wrap mh-75 overflow-auto">
                        <t t-foreach="widget._vimeoPreviewIds" t-as="vimeoId">
                            <div class="o_sample_video w-25 mh-100" t-att-data-vimeo="vimeoId"/>
                        </t>
                    </div>
                </t>
            </div>
            <div class="col-md-6">
                <div id="video-preview" class="p-3">
                    <div class="o_video_dialog_preview_text small mb-2 d-none">Preview</div>
                    <div class="media_iframe_video">
                        <div class="media_iframe_video_size"/>
                        <iframe class="o_video_dialog_iframe" allowfullscreen="allowfullscreen" frameborder="0" src=""/>
                    </div>
                </div>
            </div>
        </form>
    </t>


    <!-- Link Dialog (allows to choose a style and content for a link on the page) -->
    <div t-name="wysiwyg.widgets.link" class="o_link_dialog">
        <div class="row">
            <form class="col-lg-8">
                <div t-attf-class="form-group row#{widget.needLabel ? '' : ' d-none'}">
                    <label class="col-form-label col-md-3" for="o_link_dialog_label_input">Link Label</label>
                    <div class="col-md-9">
                        <input type="text" name="label" class="form-control" id="o_link_dialog_label_input" required="required" t-att-value="widget.data.content"/>
                    </div>
                </div>
                <div t-attf-class="form-group row o_url_input#{widget.isButton ? ' d-none' : ''}">
                    <label class="col-form-label col-md-3" for="o_link_dialog_url_input">URL or Email</label>
                    <div class="col-md-9">
                        <input type="text" name="url" class="form-control" id="o_link_dialog_url_input" required="required"/>
                        <div class="form-check o_strip_domain d-none">
                            <input type="checkbox" id="o_link_dialog_url_strip_domain" checked="checked" class="form-check-input"/>
                            <label for="o_link_dialog_url_strip_domain" class="form-check-label font-weight-normal">
                                Autoconvert to relative link
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-form-label col-md-3">Type</label>
                    <div class="col-md-9 d-flex align-items-center">
                        <div t-attf-class="#{widget.colorCombinationClass ? ('p-2 ' + widget.colorCombinationClass) : ''}">
                            <t t-foreach="widget.colorsData" t-as="colorData">
                                <label role="button" class="m-0 mr-2" t-if="colorData.type !== 'custom'">
                                    <input type="radio" name="link_style_color" class="d-none link-style" t-att-value="colorData.type"/>
                                    <span t-esc="colorData.label"
                                            t-attf-class="o_btn_preview btn btn-sm btn-#{colorData.btnPreview} #{colorData.type ? '' : 'px-0'}"/>
                                </label>
                            </t>
                        </div>
                    </div>
                </div>
                <div id="o_link_dialog_button_opts_collapse" class="collapse">
                    <div class="form-group row">
                        <label class="col-form-label col-md-3">Size</label>
                        <div class="col-md-9">
                            <select name="link_style_size" class="form-control link-style">
                                <option value="sm">Small</option>
                                <option value="" selected="selected">Medium</option>
                                <option value="lg">Large</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-form-label col-md-3">Style</label>
                        <div class="col-md-9">
                            <select name="link_style_shape" class="form-control link-style">
                                <option value="" selected="selected">Default</option>
                                <option value="rounded-circle">Default + Rounded</option>
                                <option value="outline">Outline</option>
                                <option value="outline,rounded-circle">Outline + Rounded</option>
                                <option value="fill">Fill</option>
                                <option value="fill,rounded-circle">Fill + Rounded</option>
                                <option value="flat">Flat</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group row" t-if="!widget.isButton &amp;&amp; !widget.options.forceNewWindow">
                    <div class="offset-md-3 col-md-9">
                        <label class="o_switch">
                            <input type="checkbox" name="is_new_window" t-att-checked="widget.data.isNewWindow ? 'checked' : undefined"/>
                            <span/>
                            Open in new window
                        </label>
                    </div>
                </div>
            </form>
            <div class="col-lg-4 o_link_dialog_preview">
                <div class="form-group text-center">
                    <label>Preview</label>
                    <div t-attf-class="#{widget.colorCombinationClass ? ('p-2 ' + widget.colorCombinationClass) : ''}"
                         style="overflow-x: auto; max-width: 100%; max-height: 200px;">
                        <a href="#" id="link-preview" aria-label="Preview" title="Preview"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Link Edition : Popover -->
    <div t-name="wysiwyg.widgets.link.edit.tooltip" class="d-flex">
        <span class="mr-2 o_we_preview_favicon"><i class="fa fa-globe"/><img class="align-baseline d-none"/></span>
        <div class="w-100">
            <div class="d-flex">
                <a href="#" target="_blank" class="o_we_url_link font-weight-bold flex-grow-1 text-truncate" t-esc="widget.href" title="Open in a new tab"/>
                <a href="#" class="mx-1 o_we_copy_link text-dark" data-toggle="tooltip" data-placement="top" title="Copy Link">
                    <i class="fa fa-clone"/>
                </a>
                <a href="#" class="mx-1 o_we_edit_link text-dark" data-toggle="tooltip" data-placement="top" title="Edit Link">
                    <i class="fa fa-edit"/>
                </a>
                <a href="#" class="ml-1 o_we_remove_link text-dark" data-toggle="tooltip" data-placement="top" title="Remove Link">
                    <i class="fa fa-chain-broken"/>
                </a>
            </div>
            <a href="#" target="_blank" class="o_we_full_url mt-1 text-muted d-none" t-esc="widget.href" title="Open in a new tab"/>
        </div>
    </div>
    <we-customizeblock-option t-name="wysiwyg.widgets.linkTools">
        <we-row t-attf-class="{{widget.needLabel ? '' : 'd-none'}}">
            <we-title>⌙ Link Label</we-title>
            <we-input class="o_we_user_value_widget">
                <div>
                    <input name="label" id="o_link_dialog_label_input" t-att-value="widget.data.content" type="text"/>
                </div>
            </we-input>
        </we-row>
        <we-row id="url_row" t-attf-class="#{widget.isButton ? ' d-none' : ''}">
            <we-input class="o_we_user_value_widget">
                <we-title class="o_short_title">⌙ </we-title>
                <div class="o_url_input">
                    <input name="url" id="o_link_dialog_url_input" type="text" placeholder="Your URL"/>
                </div>
            </we-input>
        </we-row>
        <we-row class="o_strip_domain d-none" t-attf-class="#{widget.isButton ? ' d-none' : ''}">
            <we-button class="o_we_user_value_widget o_we_checkbox_wrapper active">
                <we-title class="o_long_title">⌙ Autoconvert to relative link</we-title>
                    <div class="o_switch">
                        <we-checkbox name="do_strip_domain" t-att-checked="widget.data.doStripDomain ? 'checked' : undefined"/>
                    </div>
            </we-button>
        </we-row>
        <we-row>
            <we-select class="o_we_user_value_widget">
                <we-title>⌙ Style</we-title>
                <div class="dropdown">
                    <button class="dropdown-toggle"
                        data-toggle="dropdown" title="" tabindex="-1"
                        data-original-title="Link Style" aria-expanded="false">
                        <we-toggler/>
                    </button>
                    <we-selection-items class="dropdown-menu" name="link_style_color">
                        <t t-foreach="widget.colorsData" t-as="colorData">
                            <we-button class="dropdown-item" href="#" t-att-data-value="colorData.type">
                                <t t-esc="colorData.label"/>
                            </we-button>
                        </t>
                    </we-selection-items>
                    <span class="o_we_dropdown_caret"></span>
                </div>
            </we-select>
        </we-row>
        <we-row class="link-custom-color link-custom-color-text">
            <we-select title="Color" class="o_we_user_value_widget o_we_so_color_palette">
                <we-title> ⌙ Text Color</we-title><!-- &emsp; -->
                <div class="dropdown">
                    <span data-toggle="dropdown" data-css-property="color" class="o_we_color_preview"></span>
                    <we-selection-items class="dropdown-menu" name="link_text_color"/>
                </div>
            </we-select>
        </we-row>
        <we-row class="link-custom-color link-custom-color-fill">
            <we-select title="Color" class="o_we_user_value_widget o_we_so_color_palette">
                <we-title> ⌙ Fill Color</we-title><!-- &emsp; -->
                <div class="dropdown">
                    <span data-toggle="dropdown" data-css-property="background-color" class="o_we_color_preview"></span>
                    <we-selection-items class="dropdown-menu" name="link_fill_color"/>
                </div>
            </we-select>
        </we-row>
        <we-row class="link-custom-color link-custom-color-border">
            <we-title> ⌙ Border</we-title><!-- &emsp; -->
            <div>
                <we-input class="o_we_user_value_widget" title="Border Width">
                    <div class="o_custom_border_width_input">
                        <input name="custom_border_width" type="text" class="text-right" size="2"/>
                        <span>px</span>
                    </div>
                </we-input>
                <we-select class="o_we_user_value_widget" title="Border Style">
                    <div class="dropdown">
                        <button class="dropdown-toggle"
                            data-toggle="dropdown" title="" tabindex="-1"
                            data-original-title="Link Border Style" aria-expanded="false">
                            <we-toggler/>
                        </button>
                        <we-selection-items class="dropdown-menu" name="link_border_style">
                            <we-button title="Solid" class="dropdown-item" data-value="solid"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: solid;"/></we-button>
                            <we-button title="Dashed" class="dropdown-item" data-value="dashed"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: dashed;"/></we-button>
                            <we-button title="Dotted" class="dropdown-item" data-value="dotted"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: dotted;"/></we-button>
                            <we-button title="Double" class="dropdown-item" data-value="double"><div class="o_we_fake_img_item o_we_border_preview" style="border-style: double; border-left: none; border-right: none;"/></we-button>
                        </we-selection-items>
                    </div>
                </we-select>
                <we-select title="Border Color" class="o_we_user_value_widget o_we_so_color_palette">
                    <div class="dropdown">
                        <span data-toggle="dropdown" data-css-property="border-color" class="o_we_color_preview"></span>
                        <we-selection-items class="dropdown-menu" name="link_border_color"/>
                    </div>
                </we-select>
            </div>
        </we-row>
        <we-row class="link-size-row">
            <we-select class="o_we_user_value_widget">
                <we-title>⌙ Size</we-title>
                <div>
                    <div class="dropdown">
                        <button class="dropdown-toggle"
                            data-toggle="dropdown" title="" tabindex="-1"
                            data-original-title="Link Size" aria-expanded="false">
                            <we-toggler>
                                Medium
                            </we-toggler>
                        </button>
                        <we-selection-items name="link_style_size" class="dropdown-menu link-style">
                            <we-button class="dropdown-item" href="#" data-value="sm">
                                Small
                            </we-button>
                            <we-button class="dropdown-item active" href="#" data-value="">
                                Medium
                            </we-button>
                            <we-button class="dropdown-item" href="#" data-value="lg">
                                Large
                            </we-button>
                        </we-selection-items>
                        <span class="o_we_dropdown_caret"></span>
                    </div>
                </div>
            </we-select>
        </we-row>
        <we-row class="link-shape-row">
            <we-select class="o_we_user_value_widget">
                <we-title>⌙ Shape</we-title>
                <div>
                    <div class="dropdown">
                        <button class="dropdown-toggle"
                            data-toggle="dropdown" title="" tabindex="-1"
                            data-original-title="Link Shape" aria-expanded="false">
                            <we-toggler>
                                Default
                            </we-toggler>
                        </button>
                        <we-selection-items name="link_style_shape" class="dropdown-menu link-style">
                            <we-button class="dropdown-item active" data-value="">Default</we-button>
                            <we-button class="dropdown-item" data-value="rounded-circle">Default + Rounded</we-button>
                            <we-button class="dropdown-item" data-value="outline">Outline</we-button>
                            <we-button class="dropdown-item" data-value="outline,rounded-circle">Outline + Rounded</we-button>
                            <we-button class="dropdown-item" data-value="fill">Fill</we-button>
                            <we-button class="dropdown-item" data-value="fill,rounded-circle">Fill + Rounded</we-button>
                            <we-button class="dropdown-item" data-value="flat">Flat</we-button>
                        </we-selection-items>
                    </div>
                </div>
            </we-select>
        </we-row>
        <we-row  t-if="!widget.isButton &amp;&amp; !widget.options.forceNewWindow">
            <we-button t-attf-class="o_we_user_value_widget o_we_checkbox_wrapper #{widget.data.isNewWindow ? 'active' : ''}">
                <we-title class="o_long_title">⌙ Open in new window</we-title>
                    <div class="o_switch">
                        <we-checkbox name="is_new_window" t-att-checked="widget.data.isNewWindow ? 'checked' : undefined"/>
                    </div>
            </we-button>
        </we-row>
    </we-customizeblock-option>

    <!-- ImageCropWidget controls (allows to crop images on the page) -->
    <div t-name="wysiwyg.widgets.crop" class="o_we_crop_widget" contenteditable="false">
        <div class="o_we_cropper_wrapper">
            <img class="o_we_cropper_img"/>
            <div class="o_we_crop_buttons text-center mt16 position-absolute o_we_no_overlay" contenteditable="false">
                <div class="btn-group btn-group-toggle" title="Aspect Ratio" data-toggle="buttons">
                    <t t-foreach="widget.aspectRatios" t-as="ratio">
                        <t t-set="is_active" t-value="ratio === widget.aspectRatio"/>
                        <label t-attf-class="btn #{is_active and 'active' or ''}" data-action="ratio" t-att-data-value="ratio">
                            <input type="radio" /><t t-esc="ratio_value.label"/>
                        </label>
                    </t>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" title="Zoom In" data-action="zoom" data-value="0.1"><i class="fa fa-fw fa-search-plus"/></button>
                    <button type="button" title="Zoom Out" data-action="zoom" data-value="-0.1"><i class="fa fa-fw fa-search-minus"/></button>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" title="Rotate Left" data-action="rotate" data-value="-90"><i class="fa fa-fw fa-rotate-left"/></button>
                    <button type="button" title="Rotate Right" data-action="rotate" data-value="90"><i class="fa fa-fw fa-rotate-right"/></button>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" title="Flip Horizontal" data-action="flip" data-scale-direction="scaleX"><i class="fa fa-fw fa-arrows-h"/></button>
                    <button type="button" title="Flip Vertical" data-action="flip" data-scale-direction="scaleY"><i class="fa fa-fw fa-arrows-v"/></button>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" title="Reset Image" data-action="reset"><i class="fa fa-refresh"/> Reset Image</button>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" title="Apply" data-action="apply" class="btn btn-primary"><i class="fa fa-check"/> Apply</button>
                    <button type="button" title="Discard" data-action="discard" class="btn btn-danger"><i class="fa fa-times"/> Discard</button>
                </div>
            </div>
        </div>
    </div>

</templates>
