# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_links
# 
# Translators:
# <PERSON>, 2021
# Pornviboo<PERSON> T<PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " clicks"
msgstr "คลิก"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " countries"
msgstr "ประเทศ"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "# of clicks"
msgstr "# ของคลิก"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Link Tracker</span>"
msgstr "<span title=\"ติดตามเพจนี้เพื่อนับจำนวนคลิก\">ตัวติดตามลิงก์</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>แคมเปญ</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>สื่อกลาง</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>URL เดิม</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>เปลี่ยนเส้นทาง URL</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>แหล่งที่มา</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>ติดตามลิงก์</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "เวลาทั้งหมด"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"
msgstr ""
"แคมเปญ <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Copied"
msgstr "ทำสำเนาแล้ว"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Copy"
msgstr "คัดลอก"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "Edit code"
msgstr "แก้ไขโค้ด"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Generating link..."
msgstr "กำลังสร้างลิงก์..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "รับลิงก์ติดตาม"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Icon"
msgstr "ไอคอน"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "เดือนที่แล้ว"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "สัปดาห์ที่แล้ว"

#. module: website_links
#: model:ir.model,name:website_links.model_link_tracker
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "ตัวติดตามลิงก์"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"
msgstr ""
"สื่อกลาง <i class=\"fa fa-info-circle\" data-toggle=\"ทูลทิป\" data-"
"placement=\"บน\" role=\"img\" aria-label=\"ข้อมูลทูลทิป\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "คลิกมากที่สุด"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "ใหม่ล่าสุด"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "No data"
msgstr "ไม่มีข้อมูล"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "ใช้ล่าสุด"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr ""
"แชร์หน้านี้ด้วย<strong>ลิงก์สั้น</strong> ที่รวมตัว "
"<strong>ติดตามการวิเคราะห์</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"
msgstr ""
"แหล่งที่มา <i class=\"fa fa-info-circle\" data-toggle=\"ทูลทิป\" data-"
"placement=\"บน\" role=\"img\" aria-label=\"ข้อมูลทูลทิป\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#: model_terms:ir.ui.view,arch_db:website_links.link_tracker_view_tree
msgid "Statistics"
msgstr "สถิติ"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Stats"
msgstr "สแตท"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "The code cannot be left empty"
msgstr "ไม่สามารถเว้นโค้ดว่างได้"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "There is no data to show"
msgstr "ไม่มีข้อมูลที่จะแสดง"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "This code is already taken"
msgstr "โค้ดนี้ถูกใช้ไปแล้ว"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr ""
"เครื่องมือติดตามเหล่านั้นสามารถใช้ใน Google Analytics "
"เพื่อติดตามการคลิกและผู้เยี่ยมชม หรือใช้ในรายงาน Odoo "
"เพื่อติดตามโอกาสและรายได้ที่เกี่ยวข้อง"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "URL"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Unable to get recent links"
msgstr "ไม่สามารถรับลิงก์ล่าสุดได้"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "Undefined"
msgstr "ยังไม่กำหนด"

#. module: website_links
#: code:addons/website_links/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage Statistics"
msgstr "เยี่ยมชมสถิติหน้าเว็บ"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "You don't have any recent links."
msgstr "คุณไม่มีลิงก์ล่าสุด"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "คุณติดตามลิงก์"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "ยกเลิก"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "clicks"
msgstr "คลิก"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "ทำสำเนา"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "เช่น จดหมายข่าว เครือข่ายทางสังคม และอื่น ๆ "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr "เช่น โปรโมชั่นเดือนมิถุนายน จดหมายข่าวหน้าหนาว และอื่น ๆ "

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "เช่น Search Engine, Website page, .."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/contactus"
msgstr "เช่น  https://www.odoo.com/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "โอเค"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "หรือ"
