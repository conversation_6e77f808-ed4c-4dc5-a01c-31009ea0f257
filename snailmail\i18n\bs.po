# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * snailmail
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# Bo<PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-30 12:25+0000\n"
"PO-Revision-Date: 2018-08-24 09:25+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:249
#, python-format
msgid "An error occured when sending the document by post.<br>Error: %s"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:215
#, python-format
msgid "An unknown error happened. Please contact the support."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Zakačka"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "Otkaži"

#. module: snailmail
#: selection:snailmail.letter,state:0
msgid "Canceled"
msgstr "Otkazano"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.res_config_settings_view_form
msgid "Choose the ink used to print documents"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.res_config_settings_view_form
msgid "Choose the layout to print documents"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Boja"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Kompanija"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr ""

#. module: snailmail
#: selection:snailmail.letter,state:0
msgid "Draft"
msgstr "U pripremi"

#. module: snailmail
#: selection:snailmail.letter,state:0
msgid "Error"
msgstr "Greška"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: selection:snailmail.letter,state:0
msgid "In Queue"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Informacije"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:211
#, python-format
msgid "One or more required fields are empty."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Opcioni izvještaj za ispis i zakačku"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:264
#, python-format
msgid "Post letter: an error occured."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.res_config_settings_view_form
msgid "Postal Printings"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Primalac"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Pošalji odmah"

#. module: snailmail
#: selection:snailmail.letter,state:0
msgid "Sent"
msgstr "Poslano"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
msgid "Snailmail Letter"
msgstr ""

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
#: model:ir.cron,cron_name:snailmail.snailmail_print
#: model:ir.cron,name:snailmail.snailmail_print
msgid "Snailmail: process letters queue"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Status"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:213
#, python-format
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:209
#, python-format
msgid "The country of the partner is not covered by Snailmail."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:242
#, python-format
msgid ""
"The document was correctly sent by post.<br>The tracking id is %ssend_id"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "User sending the letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Draft'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:207
#, python-format
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:204
#, python-format
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
