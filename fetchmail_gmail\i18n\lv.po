# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail_gmail
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-25 14:36+0000\n"
"PO-Revision-Date: 2022-03-03 13:46+0000\n"
"Last-Translator: ievaputnina <<EMAIL>>, 2023\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid "Authorization Code"
msgstr ""

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid "Gmail"
msgstr ""

#. module: fetchmail_gmail
#: code:addons/fetchmail_gmail/models/fetchmail_server.py:0
#, python-format
msgid "Gmail authentication only supports IMAP server type."
msgstr ""

#. module: fetchmail_gmail
#: model:ir.model,name:fetchmail_gmail.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "Ienākošā pasta serveris"

#. module: fetchmail_gmail
#: model_terms:ir.ui.view,arch_db:fetchmail_gmail.fetchmail_server_view_form
msgid ""
"Setup your Gmail API credentials in the general settings to link a Gmail "
"account."
msgstr ""
