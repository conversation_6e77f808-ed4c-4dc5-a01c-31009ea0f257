# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_responsive
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2020-11-07 15:08+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<PERSON><PERSON><EMAIL>>\n"
"Language-Team: none\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"X-Generator: Weblate 3.10\n"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/search_panel/search_panel.xml:0
#, python-format
msgid "All"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/control_panel/control_panel.xml:0
#, python-format
msgid "CLEAR"
msgstr ""

#. module: web_responsive
#: model:ir.model.fields,field_description:web_responsive.field_res_users__chatter_position
msgid "Chatter Position"
msgstr "موقع الدردشة"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/js/web_responsive.js:0
#, python-format
msgid "Clear"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Create"
msgstr "إنشاء"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Discard"
msgstr "تجاهل"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Edit"
msgstr "تعديل"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/control_panel/control_panel.xml:0
#: code:addons/web_responsive/static/src/components/search_panel/search_panel.xml:0
#, python-format
msgid "FILTER"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/apps_menu/apps_menu.xml:0
#, python-format
msgid "Home Menu"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Maximize"
msgstr "تكبير"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Minimize"
msgstr "تصغير"

#. module: web_responsive
#: model:ir.model.fields.selection,name:web_responsive.selection__res_users__chatter_position__normal
msgid "Normal"
msgstr "عادي"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Quick actions"
msgstr "اجراءات سريعة"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/control_panel/control_panel.xml:0
#: code:addons/web_responsive/static/src/components/search_panel/search_panel.xml:0
#, python-format
msgid "SEE RESULT"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Save"
msgstr "حفظ"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/apps_menu/apps_menu.xml:0
#, python-format
msgid "Search menus..."
msgstr "بحث في القوائم..."

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/control_panel/control_panel.xml:0
#, python-format
msgid "Search..."
msgstr ""

#. module: web_responsive
#: model:ir.model.fields.selection,name:web_responsive.selection__res_users__chatter_position__sided
msgid "Sided"
msgstr "جانبي"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/xml/form_buttons.xml:0
#, python-format
msgid "Today"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/legacy/js/kanban_renderer_mobile.js:0
#, python-format
msgid "Undefined"
msgstr ""

#. module: web_responsive
#: model:ir.model,name:web_responsive.model_res_users
msgid "Users"
msgstr "المستخدمون"

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/control_panel/control_panel.xml:0
#, python-format
msgid "View switcher"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/hotkey/hotkey.xml:0
#, python-format
msgid "props.withAccessKey ? 'x' : false"
msgstr ""

#. module: web_responsive
#. openerp-web
#: code:addons/web_responsive/static/src/components/hotkey/hotkey.xml:0
#, python-format
msgid "props.withAccessKey ? 'z' : false"
msgstr ""

#~ msgid "#menu_id=#{app.menuID}&action_id=#{app.actionID}"
#~ msgstr "#menu_id=#{app.menuID}&action_id=#{app.actionID}"

#~ msgid "Close"
#~ msgstr "إغلاق"

#~ msgid ""
#~ "btn btn-secondary o_mail_discuss_button_multi_user_channel d-md-block d-"
#~ "none"
#~ msgstr ""
#~ "btn btn-secondary o_mail_discuss_button_multi_user_channel d-md-block d-"
#~ "none"

#~ msgid "false"
#~ msgstr "false"

#~ msgid ""
#~ "modal o_modal_fullscreen o_document_viewer o_responsive_document_viewer"
#~ msgstr ""
#~ "modal o_modal_fullscreen o_document_viewer o_responsive_document_viewer"
