<!DOCTYPE html> 
<html>
<head>
  <title>Javascript Finite State Machine</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/> 
  <link href="demo/demo.css" media="screen, print" rel="stylesheet" type="text/css" /> 
</head> 
 
<body> 

  <div id="demo" class='green'>

    <h1> Finite State Machine </h1>

    <div id="controls">
      <button id="clear" onclick="Demo.clear();">clear</button>
      <button id="calm"  onclick="Demo.calm();">calm</button>
      <button id="warn"  onclick="Demo.warn();">warn</button>
      <button id="panic" onclick="Demo.panic();">panic!</button>
    </div>

    <div id="diagram">
    </div>

    <div id="notes">
      <i>dashed lines are asynchronous state transitions (3 seconds)</i>
    </div>

    <textarea id="output">
    </textarea>

  </div>


  <script src="state-machine.js"></script>
  <script src="demo/demo.js"></script>

</body> 
</html>
