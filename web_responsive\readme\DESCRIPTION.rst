This module adds responsiveness to web backend.

**Features for all devices**:

* New navigation with the fullscreen app menu

  .. image:: ../static/img/appmenu.gif

* Quick menu search inside the app menu

  .. image:: ../static/img/appsearch.gif

* Sticky header & footer in list view

  .. image:: ../static/img/listview.gif

* Sticky statusbar in form view

  .. image:: ../static/img/formview.gif

* Bigger checkboxes in list view

  .. image:: ../static/img/listview.gif

* Increase the size of the labels in extra large screens

  .. image:: ../static/img/label_size_small.png

  .. image:: ../static/img/label_size_large.png

**Features for mobile**:

* App-specific submenus are shown on full screen when toggling them from the
  "hamburger" menu

  .. image:: ../static/img/hamburger.gif

* User-specific submenus are shown on full screen when toggling them from the
  "avatar" menu

  .. image:: ../static/img/usermenu.gif

* View type picker dropdown displays comfortably

  .. image:: ../static/img/viewtype.gif

* Top app bar is always visible, but the control panel is hidden when
  scrolling down, to save some valuable vertical space

  .. image:: ../static/img/navbar.gif

* Form status bar action and status buttons are collapsed in dropdowns.
  Other control panel buttons use icons to save space.

  .. image:: ../static/img/form_buttons.gif

* Breadcrumbs navigation is collapsed with a "back arrow" button.

  .. image:: ../static/img/breadcrumbs.gif

* Search panel is collapsed to mobile version on small screens.

  .. image:: ../static/img/search_panel.gif

* Followers and send button is displayed on mobile. Avatar is hidden.

  .. image:: ../static/img/chatter.gif

* Scrollable dropdowns

  .. image:: ../static/img/dropdown_scroll.gif

* Kanban interface adopted to mobile

  .. image:: ../static/img/kanban.gif

* Calendar interface adopted to mobile

  .. image:: ../static/img/calendar.gif

* Interface is adapted dynamically on device rotation

  .. image:: ../static/img/device_rotation.gif

* Big inputs on form in edit mode

**Features for desktop computers**:

* Keyboard shortcuts for easier navigation,
  **using `Alt + Shift + [NUM]`** combination instead of
  just `Alt + [NUM]` to avoid conflict with Firefox Tab switching.
  Standard Odoo keyboard hotkeys changed to be more intuitive or
  accessible by fingers of one hand.
  F.x. `Alt + S` for `Save`

  .. image:: ../static/img/shortcuts.gif

* Autofocus on search menu box when opening the app menu

  .. image:: ../static/img/appsearch.gif

* Full width form sheets

  .. image:: ../static/img/formview.gif

* Set chatter on the side of the screen, optional per user

  .. image:: ../static/img/chatter_sided.gif

* Sticky chatter topbar

  .. image:: ../static/img/chatter_topbar.gif

* When the chatter is configured on the side part, the document viewer fills that
  part for side-by-side reading instead of full screen. You can still put it on full
  width preview clicking on the new maximize button.

  .. image:: ../static/img/document_viewer.gif
