# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Cashdrawer"
msgstr "ลิ้นชักเก็บเงิน"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"ตรวจสอบการกำหนดค่าเครื่องพิมพ์สำหรับการตั้งค่า 'รหัสอุปกรณ์' ควรตั้งค่าเป็น:"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "การเชื่อมต่อกับเครื่องพิมพ์ล้มเหลว"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "IP เครื่องพิมพ์ Epson "

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "ที่อยู่ IP เครื่องพิมพ์ใบเสร็จของ Epson "

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s"
msgstr ""
"หากคุณอยู่บนเซิร์ฟเวอร์ที่ปลอดภัย (HTTPS) "
"โปรดตรวจสอบว่าคุณยอมรับการรับรองด้วยตนเองโดยเข้าไปที่ %s"

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "ที่อยู่ Local IP ในเครื่องพิมพ์ใบเสร็จของ Epson"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "เครื่องพิมพ์ตรวจไม่พบกระดาษ"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr "โปรดตรวจสอบว่าเครื่องพิมพ์มีกระดาษเพียงพอและพร้อมที่จะพิมพ์หรือไม่"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "โปรดตรวจสอบว่าเครื่องพิมพ์ยังคงเชื่อมต่ออยู่หรือไม่"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "กำหนดค่าการขายหน้าร้าน"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "Printing failed"
msgstr "การพิมพ์ล้มเหลว"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"เครื่องพิมพ์ใบเสร็จของ Epson "
"จะใช้แทนเครื่องพิมพ์ใบเสร็จที่เชื่อมต่อกับกล่องไอโอที"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "เครื่องพิมพ์ได้รับรหัสข้อผิดพลาดต่อไปนี้:"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "เข้าถึงเครื่องพิมพ์สำเร็จแล้ว แต่ไม่สามารถพิมพ์ได้"

#. module: pos_epson_printer
#. openerp-web
#: code:addons/pos_epson_printer/static/src/js/printers.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
"หากต้องการดูรายละเอียดเพิ่มเติมเกี่ยวกับสาเหตุของข้อผิดพลาด "
"โปรดค้นหาทางออนไลน์ด้วย:"
