<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.timesheet</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="55"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="js_class">hr_timesheet_config_form</attribute>
            </xpath>
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Timesheets" string="Timesheets" data-key="hr_timesheet" groups="hr_timesheet.group_timesheet_manager" id="timesheets">
                    <h2>Time Encoding</h2>
                    <div class="row mt16 o_settings_container" name="time_encoding_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box"
                            id="time_mode_setting"
                            attrs="{'invisible':[('project_time_mode_id', '!=', False)]}">
                            <div class="o_setting_right_pane">
                                <label for="project_time_mode_id"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." groups="base.group_multi_company"/>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="project_time_mode_id" options="{'no_create': True, 'no_open': True}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="time_unit_timesheets_setting">
                            <div class="o_setting_right_pane">
                                <label for="timesheet_encode_uom_id"/>
                                <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." groups="base.group_multi_company"/>
                                <div class="row">
                                    <div class="text-muted col-md-12">
                                        Time unit used to record your timesheets
                                    </div>
                                </div>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="timesheet_encode_uom_id" options="{'no_create': True, 'no_open': True}" required="1" class="col-lg-5 pl-0"/>
                                        <field name="is_encode_uom_days" invisible="1"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="synchronize_web_mobile_setting" invisible="1">
                            <div class="o_setting_left_pane">
                                <field name="module_project_timesheet_synchro" widget="upgrade_boolean"/>
                            </div>
                            <div style="width:130%" class="o_setting_right_pane">
                                <label for="module_project_timesheet_synchro"/>
                                <div class="text-muted">
                                    Track your time from anywhere, even offline, with our web/mobile apps
                                </div>
                                <div class="content-group">
                                    <div class="row mt16 oe_center">
                                        <div class="col-lg-3 pr-0 o_chrome_store_link d-none d-sm-inline-block">
                                            <a href="http://www.odoo.com/app/timesheet?platform=chrome" class="align-middle" target="_blank">
                                                <img alt="Google Chrome Store" class="img img-fluid align-middle mt-1" style="height: 85% !important;" src="project/static/src/img/chrome_store.png"/>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 pr-0">
                                            <img alt="Apple App Store" class="img img-fluid o_config_app_store mt-1" style="height: 85% !important; cursor: pointer;" src="project/static/src/img/app_store.png"/>
                                        </div>
                                        <div class="col-lg-3 pr-0">
                                            <img alt="Google Play Store" class="img img-fluid o_config_play_store mt-1" style="height: 85% !important; cursor: pointer;" src="project/static/src/img/play_store.png"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2>Timesheets Control</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="reminder_user_allow" widget="upgrade_boolean"/>
                            </div>
                            <div class="o_setting_right_pane" id="reminder_user_allow">
                                <label for="reminder_user_allow"/>
                                <span class="fa fa-lg fa-building-o " title="Values set here are company-specific." groups="base.group_multi_company"/>
                                <div class="text-muted">
                                    Send a periodical email reminder to timesheets users
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="reminder_manager_allow" widget="upgrade_boolean"/>
                            </div>
                            <div class="o_setting_right_pane" id="reminder_manager_allow">
                                <label for="reminder_manager_allow"/>
                                <span class="fa fa-lg fa-building-o " title="Values set here are company-specific." groups="base.group_multi_company"/>
                                <div class="text-muted">
                                    Send a periodical email reminder to timesheets managers
                                </div>
                            </div>
                        </div>
                    </div>
                    <div name="section_leaves" groups="base.group_no_one">
                        <h2>Time Off</h2>
                        <div class="row mt16 o_settings_container" name="timesheet_control">
                            <div class="col-12 col-lg-6 o_setting_box" id="timesheet_off_validation_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_project_timesheet_holidays"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_project_timesheet_holidays"/>
                                    <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." groups="base.group_multi_company"/>
                                    <div class="text-muted">
                                        Generate timesheets upon time off validation
                                    </div>
                                    <div class="content-group">
                                        <div id="module_project_timesheet_holidays"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="hr_timesheet_config_settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'hr_timesheet', 'bin_size': False}</field>
    </record>

    <menuitem id="hr_timesheet_menu_configuration" name="Configuration" parent="timesheet_menu_root"
        action="hr_timesheet_config_settings_action" groups="base.group_system" sequence="100"/>
</odoo>
