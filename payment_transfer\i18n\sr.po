# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_transfer
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:83
#, python-format
msgid "; multiple order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:81
#, python-format
msgid "; no order found"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:41
#, python-format
msgid ""
"<div>\n"
"<h3>Please use the following transfer details</h3>\n"
"<h4>%(bank_title)s</h4>\n"
"%(bank_accounts)s\n"
"<h4>Communication</h4>\n"
"<p>Please use the order name as communication reference.</p>\n"
"</div>"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Adyen"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Authorize.Net"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:39
#, python-format
msgid "Bank Account"
msgstr "Bankovni račun"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:39
#, python-format
msgid "Bank Accounts"
msgstr "Банковни рачуни"

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Buckaroo"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Manual Configuration"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Ogone"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "PayUmoney"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Paypal"
msgstr ""

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Sips"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Stripe"
msgstr ""

#. module: payment_transfer
#: selection:payment.acquirer,provider:0
msgid "Wire Transfer"
msgstr ""

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment.py:79
#, python-format
msgid "received data for reference %s"
msgstr ""
