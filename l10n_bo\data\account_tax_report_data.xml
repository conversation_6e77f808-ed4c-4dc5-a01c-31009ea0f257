<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.bo"/>
    </record>

	<record id="tax_report_impuesto_25" model="account.tax.report.line">
        <field name="name">Impuesto a las Utilidades de la Empresa IUE (25%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
    </record>

    <record id="tax_report_impuesto_trans" model="account.tax.report.line">
        <field name="name">Impuesto a las Transacciones IT (3%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
    </record>

    <record id="tax_report_impuesto_trans_3" model="account.tax.report.line">
        <field name="name">Impuesto a las Transacciones IT (3%)</field>
        <field name="tag_name">Impuesto a las Transacciones IT (3%)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_impuesto_trans"/>
    </record>

    <record id="tax_report_impuesto_ttl" model="account.tax.report.line">
        <field name="name">Impuesto al Valor Agregado (IVA) Total a Pagar</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
    </record>

    <record id="tax_report_impuesto_ttl_cob" model="account.tax.report.line">
        <field name="name">Impuesto Cobrado</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl"/>
    </record>

    <record id="tax_report_impuesto_ttl_cob_fuera" model="account.tax.report.line">
        <field name="name">Impuesto Cobrado Fuera de Ámbito</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_cob"/>
    </record>

    <record id="tax_report_impuesto_ttl_cob_exon" model="account.tax.report.line">
        <field name="name">Impuesto Cobrado de Exonerados al IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_cob"/>
    </record>

    <record id="tax_report_impuesto_ttl_cob_iva" model="account.tax.report.line">
        <field name="name">Impuesto Cobrado IVA</field>
        <field name="tag_name">Impuesto Cobrado IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_cob"/>
    </record>

    <record id="tax_report_impuesto_ttl_pag" model="account.tax.report.line">
        <field name="name">Impuesto Pagado</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl"/>
    </record>

    <record id="tax_report_impuesto_ttl_pag_fuera" model="account.tax.report.line">
        <field name="name">Impuesto Pagado Fuera de Ámbito</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_pag"/>
    </record>

    <record id="tax_report_impuesto_ttl_pag_exon" model="account.tax.report.line">
        <field name="name">Impuesto Pagado de Exonerados al IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_pag"/>
    </record>

    <record id="tax_report_impuesto_ttl_pag_iva" model="account.tax.report.line">
        <field name="name">Impuesto Pagado IVA</field>
        <field name="tag_name">Impuesto Pagado IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_impuesto_ttl_pag"/>
    </record>

    <record id="tax_report_base_imponible" model="account.tax.report.line">
        <field name="name">Base Imponible</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
    </record>

    <record id="tax_report_base_imponible_compras" model="account.tax.report.line">
        <field name="name">Base Imponible - Compras</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_base_imponible"/>
    </record>

    <record id="tax_report_base_imponible_compras_fuera" model="account.tax.report.line">
        <field name="name">Compras Gravadas Fuera de Ámbito</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_base_imponible_compras"/>
    </record>

    <record id="tax_report_base_imponible_compras_gravadas" model="account.tax.report.line">
        <field name="name">Compras NO Gravadas (Exoneradas)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_base_imponible_compras"/>
    </record>

    <record id="tax_report_base_imponible_compras_iva" model="account.tax.report.line">
        <field name="name">Compras Gravadas con IVA</field>
        <field name="tag_name">Compras Gravadas con IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_base_imponible_compras"/>
    </record>

    <record id="tax_report_base_imponible_venras" model="account.tax.report.line">
        <field name="name">Base Imponible - Ventas</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_base_imponible"/>
    </record>

    <record id="tax_report_base_imponible_venras_fuera" model="account.tax.report.line">
        <field name="name">Ventas Gravadas Fuera de Ámbito</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_base_imponible_venras"/>
    </record>

    <record id="tax_report_base_imponible_venras_exon" model="account.tax.report.line">
        <field name="name">Ventas NO Gravadas (Exoneradas)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_base_imponible_venras"/>
    </record>

    <record id="tax_report_base_imponible_venras_iva" model="account.tax.report.line">
        <field name="name">Impuesto al Valor Agregado con IVA</field>
        <field name="tag_name">Impuesto al Valor Agregado con IVA</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_base_imponible_venras"/>
    </record>

</odoo>
