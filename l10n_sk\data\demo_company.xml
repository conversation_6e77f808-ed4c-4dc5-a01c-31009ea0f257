<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_sk" model="res.partner">
        <field name="name">SK Company</field>
        <field name="vat">SK2022749619</field>
        <field name="street">Pařížská Street 25/31</field>
        <field name="city">Bratislava</field>
        <field name="country_id" ref="base.sk"/>
        <field name="zip"></field>
        <field name="phone">+421 5 12 34 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.skexample.com</field>
    </record>

    <record id="demo_company_sk" model="res.company">
        <field name="name">SK Company</field>
        <field name="partner_id" ref="partner_demo_company_sk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_sk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_sk.demo_company_sk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_sk.sk_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_sk.demo_company_sk')"/>
    </function>
</odoo>
