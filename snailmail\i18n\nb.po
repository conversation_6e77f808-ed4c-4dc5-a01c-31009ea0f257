# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Adresse"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending a letter with Snailmail."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An unknown error happened. Please contact the support."
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "An unknown error occurred. Please contact our"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/wizard/snailmail_letter_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s snailmail delivery failures? You won't "
"be able to re-send these letters later!"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Vedlegg"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#, python-format
msgid "Awaiting Dispatch"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Begge sider"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "Buy credits"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "Kanseller"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Cancel letter"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
#, python-format
msgid "Canceled"
msgstr "Avbrutt"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Sted"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Close"
msgstr "Lukk"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Farge"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Firma"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Confirm"
msgstr "Bekreft"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Land"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
msgid "Discard delivery failures"
msgstr ""

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_cancel_action
msgid "Discard snailmail delivery failures"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_cancel
msgid "Dismiss notification for resend by model"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Dokument"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "Dokument-ID"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
#, python-format
msgid "Error"
msgstr "Feil"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.js:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
#, python-format
msgid "Failed letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr ""

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__help_message
msgid "Help message"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red paper-plane "
"next to each message."
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "I kø"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Informasjon"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Brev"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Letter sent by post with Snailmail"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Melding"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Meldingsvarsler"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__model
msgid "Model"
msgstr "Modell"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_confirm__model_name
msgid "Model Name"
msgstr "Modellnavn"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Varslinger"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "One or more required fields are empty."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Valgfri rapport til å skrive ut og legge ved"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "Partner"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb-felt Kontakt"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "Re-send letter"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Mottaker"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Rapporthandling"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Send nå"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
#, python-format
msgid "Sent"
msgstr "Sendt"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/wizard/snailmail_confirm.py:0
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
#, python-format
msgid "Snailmail"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_confirm
msgid "Snailmail Confirm"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Snailmail Confirmation"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Snailmail Failures"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr ""

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr ""

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
#: model:ir.cron,cron_name:snailmail.snailmail_print
#: model:ir.cron,name:snailmail.snailmail_print
msgid "Snailmail: process letters queue"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "Modus"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Status"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Gate"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Gate 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Gate..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Gate 2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The address of the recipient is not complete"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The country of the partner is not covered by Snailmail."
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Type"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "You are about to send this"
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "You need credits on your IAP account to send a letter."
msgstr ""

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "Postnummer"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "Postnummer"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "by post. Are you sure you want to continue?"
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "for further assistance."
msgstr ""

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "support"
msgstr ""
