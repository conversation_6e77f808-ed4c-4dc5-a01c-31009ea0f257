.o_document_layout .o_group {
    display: flex;
    .o_document_layout_company {
        flex: 5;
        @include media-breakpoint-up(sm, $o-extra-grid-breakpoints) {
            max-width: 50%;
        }
        img {
            max-height: 100px;
        }
        .o_document_layout_colors {
            margin-bottom: 32px;
            vertical-align: middle;
            display: flex;
            flex-direction: row;
            align-items: center;
            .o_field_widget {
                width: 30px;
                margin: 0 5px 0 0;
            }
            .btn {
                padding: 0;
                margin-left: 10px;
                .o_form_label {
                    height: 30px;
                    font-size: 18px;
                    margin: 0;
                    cursor: pointer;
                }
            }
        }
        select.o_input {
            height: 25px;
        }

    }
}

@media (max-width: 620px) {
    .o_document_layout .o_group {
        flex-direction: column;
    }
}
