<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="gd_taxr_1" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Izstopni DDV 0%</field>
        <field name="description">DDV-I-0</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_opr')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_opr')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="gd_taxr_2" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Izstopni DDV 9.5%</field>
        <field name="description">DDV-I-9.5</field>
        <field name="amount_type">percent</field>
        <field name="amount">9.5</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_95"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'account_id': ref('gd_acc_260001'),
                'repartition_type': 'tax',
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_zni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'account_id': ref('gd_acc_260001'),
                'repartition_type': 'tax',
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_zni')],
            }),
        ]"/>
    </record>

    <record id="gd_taxr_3" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Izstopni DDV 22%</field>
        <field name="description">DDV-I-22</field>
        <field name="amount_type">percent</field>
        <field name="amount">22</field>
        <field name="type_tax_use">sale</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260002'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_osn')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_izstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260002'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_osn')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_1" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 0%</field>
        <field name="description">DDV-V-0</field>
        <field name="amount_type">percent</field>
        <field name="amount">0</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_opr')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_opr')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>

    <record id="gd_taxp_2" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 9.5%</field>
        <field name="description">DDV-V-9.5</field>
        <field name="amount_type">percent</field>
        <field name="amount">9.5</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_95"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160002'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_zni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160002'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_zni')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_3" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 22%</field>
        <field name="description">DDV-V-22</field>
        <field name="amount_type">percent</field>
        <field name="amount">22</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160003'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_osn')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160003'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_osn')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_nr_1" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 9.5% - neodbitni</field>
        <field name="description">DDV-V-9.5-NE</field>
        <field name="amount_type">percent</field>
        <field name="amount">9.5</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_95"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_neo_sdtopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160001'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_neo_vstopni_zni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_neo_sdtopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160001'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_neo_vstopni_zni')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_nr_2" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 22% - neodbitni</field>
        <field name="description">DDV-V-22-NE</field>
        <field name="amount_type">percent</field>
        <field name="amount">22</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="1"/>
        <field name="tax_group_id" ref="tax_group_22"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_neo_sdtopni_osno')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160001'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_neo_vstopni_osnnovna')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_neo_sdtopni_osno')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160001'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_neo_vstopni_osnnovna')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_st_1" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 9.5% - samoobdavčitev</field>
        <field name="description">DDV-V-9.5-SO</field>
        <field name="amount_type">percent</field>
        <field name="amount">9.5</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160002'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260001'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_zni')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160002'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_zni')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260001'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_zni')],
            }),
        ]"/>
    </record>

    <record id="gd_taxp_st_2" model="account.tax.template">
        <field name="chart_template_id" ref="gd_chart"/>
        <field name="name">Vstopni DDV 22% - samoobdavčitev</field>
        <field name="description">DDV-V-22-SO</field>
        <field name="amount_type">percent</field>
        <field name="amount">22</field>
        <field name="type_tax_use">purchase</field>
        <field name="price_include" eval="0"/>
        <field name="tax_group_id" ref="tax_group_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'plus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160003'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260002'),
                'plus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_osn')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'minus_report_line_ids': [ref('tax_report_zo_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_160003'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_vstopni_osn')],
            }),
            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('gd_acc_260002'),
                'minus_report_line_ids': [ref('tax_report_zn_dvd_odb_izstopni_osn')],
            }),
        ]"/>
    </record>

</odoo>
