<?xml version="1.0" ?>
<odoo><data>

<!-- Slide template for the fullscreen mode -->
<template id="slide_fullscreen" name="Fullscreen">
    <t t-set="head">
        <link rel="canonical" t-att-href="slide.website_url" />
    </t>
    <t t-call="website.layout">
        <div class="o_wslides_fs_main d-flex flex-column font-weight-light"
            t-att-data-channel-id="slide.channel_id.id"
            t-att-data-channel-enroll="slide.channel_id.enroll"
            t-att-data-signup-allowed="signup_allowed"
            t-att-data-session-answers="session_answers">

            <div class="o_wslides_slide_fs_header d-flex flex-shrink-0 text-white">
                <div class="d-flex">
                    <a class="o_wslides_fs_toggle_sidebar d-flex align-items-center px-3" href="#" title="Lessons">
                        <i class="fa fa-bars"/><span class="d-none d-md-inline-block ml-1">Lessons</span>
                    </a>
                    <a class="o_wslides_fs_review d-flex align-items-center" title="Reviews" t-if="slide.channel_id.allow_comment">
                        <t t-call="portal_rating.rating_stars_static_popup_composer">
                            <t t-set="rating_avg" t-value="rating_avg"/>
                            <t t-set="rating_total" t-value="rating_count"/>
                            <t t-set="object" t-value="channel"/>
                            <t t-set="token" t-value="channel.access_token"/>
                            <t t-set="hash" t-value="message_post_hash"/>
                            <t t-set="pid" t-value="message_post_pid"/>
                            <t t-set="default_message" t-value="last_message"/>
                            <t t-set="default_message_id" t-value="last_message_id"/>
                            <t t-set="default_rating_value" t-value="last_rating_value"/>
                            <t t-set="default_attachment_ids" t-value="last_message_attachment_ids"/>
                            <t t-set="force_submit_url" t-value="'/slides/mail/update_comment' if last_message_id else False"/>
                            <t t-set="disable_composer" t-value="not channel.can_review"/>
                            <t t-set="_link_btn_classes" t-value="'d-inline-block text-white font-weight-light shadow-none'"/>
                            <t t-set="icon" t-value="'fa fa-pencil'"/>
                            <t t-set="_text_classes" t-value="'d-none d-md-inline-block'"/>
                            <t t-set="hide_rating_avg" t-value="True"/>
                        </t>
                    </a>
                    <a class="o_wslides_fs_share d-flex align-items-center px-3" href="#" title="Share">
                        <i class="fa fa-share-alt"/><span class="d-none d-md-inline-block ml-1">Share</span>
                    </a>
                </div>
                <div class="d-flex ml-auto">
                    <a class="d-flex align-items-center px-3 o_wslides_fs_exit_fullscreen" t-attf-href="/slides/slide/#{slug(slide)}">
                        <i class="fa fa-sign-out"/><span class="d-none d-md-inline-block ml-1">Exit Fullscreen</span>
                    </a>
                    <a class="d-flex align-items-center px-3" t-attf-href="/slides/#{slug(slide.channel_id)}">
                        <i class="fa fa-home"/><span class="d-none d-md-inline-block ml-1">Back to course</span>
                    </a>
                </div>
            </div>

            <div class="o_wslides_fs_container d-flex position-relative overflow-hidden flex-grow-1">
                <div class="o_wslides_fs_content align-items-stretch justify-content-center d-flex flex-grow-1 order-2"></div>

                <div class="o_wslides_fs_sidebar o_wslides_fs_sidebar_hidden text-white flex-shrink-0 order-1">
                    <div class="o_wslides_fs_sidebar_content d-flex flex-column px-3 pt-3 h-100">
                        <div class="o_wslides_fs_sidebar_header mb-3">
                            <a class="h5 d-block mb-1" t-attf-href="/slides/#{slug(slide.channel_id)}">
                                <span t-field="slide.channel_id.name"/>
                            </a>
                            <div t-if="not is_public_user" class="d-flex align-items-center">
                                <t t-if="slide.channel_id.completed">
                                    <span class="badge badge-pill badge-success py-1 px-2" style="font-size: 1em"><i class="fa fa-check"/> Completed</span>
                                </t>
                                <t t-else="">
                                    <div class="progress flex-grow-1 bg-black-50" style="height: 6px;">
                                        <div class="progress-bar" role="progressbar" t-attf-style="width: #{slide.channel_id.completion}%" t-att-aria-valuenow="slide.channel_id.completion" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="ml-3 small">
                                        <span class="o_wslides_progress_percentage" t-esc="slide.channel_id.completion"/> %
                                    </div>
                                </t>
                            </div>
                        </div>
                        <ul class="mx-n3 list-unstyled my-0 pb-2 overflow-auto">
                            <t t-foreach="category_data" t-as="category">
                                <t t-if="category.get('slides')">
                                    <t t-call="website_slides.slide_fullscreen_sidebar_category">
                                        <t t-set="slides" t-value="category['slides']"/>
                                        <t t-set="current_slide" t-value="slide"/>
                                    </t>
                                </t>
                            </t>
                        </ul>
                    </div>
                    <a href="#" class="o_wslides_fs_toggle_sidebar d-lg-none bg-black-50"/>
                </div>
            </div>
        </div>
    </t>
</template>


<template id="slide_fullscreen_sidebar_category" name="Slides category template for fullscreen view side bar">
    <t t-if="category" t-set="category" t-value="category.get('category')"/>
    <li class="o_wslides_fs_sidebar_section py-2 px-3">
        <a t-if="category" class="text-uppercase text-500 py-1 small d-block" t-attf-id="category-collapse-#{category.id if category else 0}" data-toggle="collapse" role="button" aria-expanded="true" t-att-href="('#collapse-%s') % (category.id if category else 0)" t-attf-aria-controls="collapse-#{category.id if category else 0}">
            <b t-field="category.name"/>
        </a>
        <ul class="o_wslides_fs_sidebar_section_slides collapse show position-relative px-0 pb-1 my-0 mx-n3" t-att-id="('collapse-%s') % (category.id if category else 0)">
            <t t-set="is_member" t-value="current_slide.channel_id.is_member"/>
            <t t-set="can_access_channel" t-value="is_member or current_slide.channel_id.can_publish"/>
            <t t-foreach="slides" t-as="slide">
                <t t-set="slide_completed" t-value="channel_progress[slide.id].get('completed')"/>
                <t t-set="can_access" t-value="can_access_channel or slide.is_preview"/>
                <li t-att-class="'o_wslides_fs_sidebar_list_item d-flex align-items-top py-1 %s' % ('active' if slide.id == current_slide.id else '')"
                    t-att-data-id="slide.id"
                    t-att-data-can-access="can_access"
                    t-att-data-name="slide.name"
                    t-att-data-type="slide.slide_type"
                    t-att-data-slug="slug(slide)"
                    t-att-data-has-question="1 if slide.question_ids else 0"
                    t-att-data-is-quiz="0"
                    t-att-data-completed="1 if slide_completed else 0"
                    t-att-data-embed-code="slide.embed_code if slide.slide_type in ['video', 'document', 'presentation', 'infographic'] else False"
                    t-att-data-is-member="is_member"
                    t-att-data-session-answers="session_answers">
                    <span class="ml-3">
                        <i t-if="slide_completed and is_member" class="o_wslides_slide_completed fa fa-check fa-fw text-success" t-att-data-slide-id="slide.id"/>
                        <i t-if="not slide_completed and is_member" class="fa fa-circle-thin fa-fw" t-att-data-slide-id="slide.id"/>
                    </span>
                    <div class="ml-2 overflow-hidden">
                        <a t-if="can_access" class="d-block pt-1" href="#">
                            <div class="d-flex ">
                                <t t-call="website_slides.slide_icon"/>
                                <div class="o_wslides_fs_slide_name text-truncate" t-esc="slide.name"/>
                            </div>
                        </a>
                        <span t-else="" class="d-block pt-1" href="#">
                            <div class="d-flex ">
                                <t t-set="icon_class" t-value="'mr-2 text-600'"/>
                                <t t-call="website_slides.slide_icon"/>
                                <div class="o_wslides_fs_slide_name text-600 text-truncate" t-esc="slide.name"/>
                            </div>
                        </span>
                        <ul class="list-unstyled w-100 pt-2 small" t-if="slide.link_ids or slide._has_additional_resources() or (slide.question_ids and not slide.slide_type =='quiz')" >
                            <li t-if="slide.link_ids" t-foreach="slide.link_ids" t-as="link" class="pl-0 mb-1">
                                <a t-if="can_access" class="o_wslides_fs_slide_link" t-att-href="link.link" target="_blank">
                                    <i class="fa fa-link mr-2"/><span t-esc="link.name"/>
                                </a>
                                <span t-else="" class="o_wslides_fs_slide_link text-600">
                                    <i class="fa fa-link mr-2"/><span t-esc="link.name"/>
                                </span>
                            </li>
                            <div class="o_wslides_js_course_join pl-0" t-if="slide._has_additional_resources()">
                                <t t-if="can_access_channel">
                                    <li t-foreach="slide.slide_resource_ids" t-as="resource" class="mb-1">
                                        <a class="o_wslides_fs_slide_link" t-attf-href="/web/content/slide.slide.resource/#{resource.id}/data?download=true">
                                            <i class="fa fa-download mr-2"/><span t-esc="resource.name"/>
                                        </a>
                                    </li>
                                </t>
                                <li t-elif="slide.channel_id.enroll == 'public'" class="o_wslides_fs_slide_link mb-1">
                                    <i class="fa fa-download mr-1"/>
                                    <t t-call="website_slides.join_course_link"/>
                                </li>
                            </div>
                            <li class="o_wslides_fs_sidebar_list_item pl-0 mb-1" t-if="slide.question_ids and not slide.slide_type == 'quiz'"
                                t-att-data-id="slide.id"
                                t-att-data-can-access="can_access"
                                t-att-data-name="slide.name"
                                t-att-data-type="slide.slide_type"
                                t-att-data-slug="slug(slide)"
                                t-att-data-has-question="1 if slide.question_ids else 0"
                                t-att-data-is-quiz="1"
                                t-att-data-completed="1 if slide_completed else 0"
                                t-att-data-is-member="is_member"
                                t-att-data-session-answers="session_answers">
                                <a t-if="can_access" class="o_wslides_fs_slide_quiz" href="#" t-att-index="i">
                                    <i class="fa fa-flag-checkered text-warning mr-2"/>Quiz
                                </a>
                                <span t-else="" class="text-600">
                                    <i class="fa fa-flag-checkered text-warning mr-2"/>Quiz
                                </span>
                            </li>
                        </ul>
                    </div>
                </li>
            </t>
        </ul>
    </li>
</template>


</data></odoo>
