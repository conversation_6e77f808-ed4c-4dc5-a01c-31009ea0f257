# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2019-08-26 09:13+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "<span class=\"o_form_label oe_edit_only\">Allowed Employees </span>"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#: code:addons/pos_hr/static/src/js/screens.js:0
#, python-format
msgid "Change Cashier"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Close session"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr ""

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
msgid "Employees with access"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Incorrect Password"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/chrome.js:0
#, python-format
msgid "Lock"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Log in to"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Password ?"
msgstr ""

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr ""

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Price Control"
msgstr ""

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
msgid "Restrict price modification to managers"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Scan your badge"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "Select Cashier"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/js/gui.js:0
#, python-format
msgid "Select User"
msgstr ""

#. module: pos_hr
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""

#. module: pos_hr
#. openerp-web
#: code:addons/pos_hr/static/src/xml/pos.xml:0
#, python-format
msgid "or"
msgstr ""
