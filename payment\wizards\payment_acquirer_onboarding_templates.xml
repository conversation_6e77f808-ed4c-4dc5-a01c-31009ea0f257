<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- onboarding step -->
    <template id="onboarding_payment_acquirer_step">
        <t t-call="base.onboarding_step">
            <t t-set="title">Online Payments</t>
            <t t-set="description">Enable credit &amp; debit card payments supported by Stripe</t>
            <t t-set="btn_text">Activate Stripe</t>
            <t t-set="done_text">Online payments enabled</t>
            <t t-set="method" t-value="'action_open_payment_onboarding_payment_acquirer'" />
            <t t-set="model" t-value="'res.company'" />
            <t t-set="state" t-value="state.get('payment_acquirer_onboarding_state')" />
        </t>
    </template>

    <record model="ir.ui.view" id="payment_acquirer_onboarding_wizard_form">
        <field name="name">payment.acquirer.onboarding.wizard.form</field>
        <field name="model">payment.acquirer.onboarding.wizard</field>
        <field name="arch" type="xml">
            <form string="Choose a payment method" class="o_onboarding_payment_acquirer_wizard">
                <div class="container">
                    <div class="row align-items-start">
                        <div class="col col-4" name="left-column">
                            <group>
                                <field name="payment_method" widget="radio" nolabel="1"/>
                            </group>
                        </div>
                        <div class="col" name="right-column">
                            <div attrs="{'invisible': [('payment_method', '!=', 'paypal')]}">
                                <group>
                                    <field name="paypal_email_account" attrs="{'required': [('payment_method', '=', 'paypal')]}" string="Email"/>
                                    <field name="paypal_pdt_token" password="True" attrs="{'required': [('payment_method', '=', 'paypal')]}" />
                                </group>
                                <p>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/general/payment_acquirers/paypal.html" target="_blank">
                                        <span><i class="fa fa-arrow-right"/> How to configure your PayPal account</span>
                                    </a>
                                </p>
                            </div>
                            <div invisible="1">
                                <group>
                                    <field name="stripe_secret_key" password="True"/>
                                    <field name="stripe_publishable_key" password="True"/>
                                </group>
                                <p>
                                    <a href="https://dashboard.stripe.com/account/apikeys" target="_blank">
                                        <span><i class="fa fa-arrow-right"/> Get my Stripe keys</span>
                                    </a>
                                </p>
                            </div>

                            <div attrs="{'invisible': [('payment_method', '!=', 'other')]}">
                                <button type="object" name="action_onboarding_other_payment_acquirer">
                                    Check here
                                </button>
                                to choose another payment method.
                            </div>
                            <div attrs="{'invisible': [('payment_method', '!=', 'manual')]}">
                                <group>
                                    <field name="manual_name" attrs="{'required': [('payment_method', '=', 'manual')]}"/>
                                    <field name="journal_name" attrs="{'required': [('payment_method', '=', 'manual')]}"/>
                                    <field name="acc_number" attrs="{'required': [('payment_method', '=', 'manual')]}"/>
                                    <field name="manual_post_msg" attrs="{'required': [('payment_method', '=', 'manual')]}"/>
                                </group>
                            </div>
                        </div>
                    </div>
                </div>
                <footer>
                    <button name="add_payment_methods" string="Apply" class="oe_highlight"
                            type="object" data-hotkey="q" />
                    <button special="cancel" data-hotkey="z" string="Cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_open_payment_onboarding_payment_acquirer_wizard" model="ir.actions.act_window">
        <field name="name">Choose a payment method</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">payment.acquirer.onboarding.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="payment.payment_acquirer_onboarding_wizard_form" />
        <field name="target">new</field>
    </record>
</odoo>
