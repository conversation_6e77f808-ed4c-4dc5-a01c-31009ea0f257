<?xml version="1.0" encoding='UTF-8'?>
<odoo>
	<record model="ir.cron" id="maintenance_requests_cron">
        <field name="name">Maintenance: generate preventive maintenance requests</field>
        <field name="model_id" ref="model_maintenance_equipment"/>
        <field name="state">code</field>
        <field name="code">model._cron_generate_requests()</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="doall" eval="False"/>
    </record>
</odoo>