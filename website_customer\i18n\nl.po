# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-labels\"/>Alle"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"Externe link\""
" title=\"Externe link\"/>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "Actief"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Alle landen"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "Alle industrieën"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Bootstrap class om de kleur te personaliseren"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "Kan publiceren"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "Naam categorie"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "Class"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "Sluiten"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "Maak een nieuw contactlabel aan"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "Geïmplementeerd door"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "Is gepubliceerd"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""
"Beheer contactlabels om contacten beter te beheren voor opvolging en "
"analyses."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "Geen resultaat gevonden"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "Onze referenties"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Relatielabel"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"Relatielabels - Deze labels kunnen gebruikt worden op de website om klanten "
"te vinden op sector, of ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "Relaties"

#. module: website_customer
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "Referenties"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "Referenties per land"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "References by Industries"
msgstr "Referenties per industrie"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "Referenties op label"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Zoek"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "Zoek relatie label"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"De volledige URL om toegang tot het document te krijgen via de website."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "Vertrouwd door miljoenen wereldwijd"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "Zichtbaar op huidige website"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "Website"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Websitelabels"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "Websitelabels"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Wereldkaart"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "referentie(s)"
