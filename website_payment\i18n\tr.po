# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_payment
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> Altinisik <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# omerfarukcak<PERSON>k <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>l <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid ""
".\n"
"                        <br/>\n"
"                        We appreciate your support for our organization as such.\n"
"                        <br/>\n"
"                        Regards."
msgstr ""
".\n"
"                        <br/>\n"
"                        Bu anlamda derneğimize verdiğiniz destek için teşekkür ederiz..\n"
"                        <br/>\n"
"                        Saygılarımızla."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Comment:</b>"
msgstr "<b>Yorum:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donation Date:</b>"
msgstr "<b>Bağış Tarihi:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Email:</b>"
msgstr "<b>Bağışçı E-postası:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Donor Name:</b>"
msgstr "<b>Bağışçı Adı:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment ID:</b>"
msgstr "<b>Ödeme ID:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "<b>Payment Method:</b>"
msgstr "<b>Ödeme şekli:</b>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Ülke...</option>"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Uygun bir ödeme seçeneği bulunamadı.</strong><br/>Bunun bir hata "
"olduğunu düşünüyorsanız, lütfen web sitesi yöneticisine başvurun."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Uyarı</strong> Para birimi eksik veya hatalı."

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "A donation has been made on your website"
msgstr "Web sitenize bağış yapıldı"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "A year of cultural awakening."
msgstr "Kültürel uyanış yılı."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Buraya bir açıklama ekleyin"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/options.js:0
#, python-format
msgid "Add new pre-filled option"
msgstr "Yeni önceden doldurulmuş seçenek ekle"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Amount"
msgstr "Tutar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Amount ("
msgstr "Tutar ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Amount("
msgstr "Tutar ("

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Caring for a baby for 1 month."
msgstr "1 aylık bebek bakımı."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#, python-format
msgid "Choose Your Amount"
msgstr "Tutarınızı Seçin"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Country"
msgstr "Ülke"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Country\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Ülke\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Country is required."
msgstr "Ülke bilgisi zorunlu."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: code:addons/website_payment/static/src/snippets/s_donation/000.xml:0
#: model_terms:ir.ui.view,arch_db:website_payment.donation_input
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
#, python-format
msgid "Custom Amount"
msgstr "Özel Tutar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Dear"
msgstr "Sayın"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Default Amount"
msgstr "Varsayılan Tutar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Display Options"
msgstr "Görüntüleme seçenekleri"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "Donate Now"
msgstr "Şimdi Bağış yap"

#. module: website_payment
#: model:mail.template,name:website_payment.mail_template_donation
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Donation"
msgstr "Bağış"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Donation amount must be at least %.2f."
msgstr "Bağış miktarı en az %.2f olmalıdır."

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Donation confirmation"
msgstr "bağış onayı"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Donation notification"
msgstr "bağış bildirimi"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email"
msgstr "E-Posta"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Email\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"Eposta\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Email is invalid"
msgstr "e-posta geçersiz"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Email is required."
msgstr "Eposta zorunlu"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Field '%s' is mandatory"
msgstr "'%s' alanı zorunludur"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Input"
msgstr "Giriş"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_account_payment__is_donation
msgid "Is Donation"
msgstr "Bağış mı"

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_transaction__is_donation
msgid "Is donation"
msgstr "bağış mı"

#. module: website_payment
#: model:ir.model.fields,help:website_payment.field_account_payment__is_donation
#: model:ir.model.fields,help:website_payment.field_payment_transaction__is_donation
msgid "Is the payment a donation"
msgstr "ödeme bağış mı"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Make a Donation"
msgstr "Bağış yap"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Name"
msgstr "Adı"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid ""
"Name\n"
"                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"İsim\n"
"                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website_payment
#: code:addons/website_payment/controllers/portal.py:0
#, python-format
msgid "Name is required."
msgstr "İsim zorunlu"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "None"
msgstr "Hiçbiri"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in elementary school."
msgstr "İlkokulda bir yıl."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_button
msgid "One year in high school."
msgstr "Lisede bir yıl."

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Ödeme Alıcısı"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Payment Details"
msgstr "Ödeme detayları"

#. module: website_payment
#: model:ir.model,name:website_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödeme İşlemi"

#. module: website_payment
#: code:addons/website_payment/models/payment_transaction.py:0
#, python-format
msgid "Payment received from donation with following details:"
msgstr "Aşağıdaki ayrıntılarla birlikte bağıştan alınan ödeme:"

#. module: website_payment
#: model:ir.model,name:website_payment.model_account_payment
msgid "Payments"
msgstr "Ödemeler"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "Please select or enter an amount"
msgstr "Lütfen bir miktar seçin veya girin"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Pre-filled Options"
msgstr "Önceden Doldurulmuş Seçenekler"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Recipient Email"
msgstr "Alıcının E-posta"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Sunucu Hatası"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "Slider"
msgstr "Slayt"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation
msgid "Small or large, your contribution is essential."
msgstr "Küçük veya büyük, katkınız çok önemlidir."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Some information is missing to process your payment."
msgstr "Ödemenizi işleme koymak için bazı bilgiler eksik."

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "Thank you for your donation of"
msgstr "bağışınız için teşekkür ederiz"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/snippets/s_donation/000.js:0
#, python-format
msgid "The minimum donation amount is %s%s%s"
msgstr "Minimum bağış miktarı %s%s%s"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_pay
msgid "There is nothing to pay."
msgstr "Ödeyecek bir şey yok."

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "Validation Error"
msgstr "Doğrulama hatası"

#. module: website_payment
#. openerp-web
#: code:addons/website_payment/static/src/js/website_payment_form.js:0
#, python-format
msgid "We could not obtain payment fees."
msgstr "Ödeme ücretlerini alamadık."

#. module: website_payment
#: model:ir.model.fields,field_description:website_payment.field_payment_acquirer__website_id
msgid "Website"
msgstr "Websitesi"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Write us a comment"
msgstr "Bize bir yorum yazın"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.payment_checkout
msgid "Your comment"
msgstr "Senin yorumun"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.donation_mail_body
msgid "made on"
msgstr "üzerinde yapılan"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Descriptions"
msgstr "⌙ Açıklamalar"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Maximum"
msgstr "⌙ Maksimum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Minimum"
msgstr "⌙ Minimum"

#. module: website_payment
#: model_terms:ir.ui.view,arch_db:website_payment.s_donation_options
msgid "⌙ Step"
msgstr "⌙ Adım"
