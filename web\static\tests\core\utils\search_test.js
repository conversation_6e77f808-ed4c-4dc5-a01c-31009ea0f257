/** @odoo-module **/

import { fuzzyLookup, fuzzyTest } from "@web/core/utils/search";

QUnit.module("utils", () => {
    QUnit.module("Fuzzy Search");

    QUnit.test("fuzzyLookup", function (assert) {
        const data = [
            { name: "<PERSON>" },
            { name: "<PERSON>" },
            { name: "<PERSON>" },
            { name: "<PERSON>" },
            { name: "<PERSON><PERSON><PERSON><PERSON>" },
        ];
        assert.deepEqual(
            fuzzyLookup("ba", data, (d) => d.name),
            [{ name: "<PERSON>" }, { name: "<PERSON>" }]
        );
        assert.deepEqual(
            fuzzyLookup("g", data, (d) => d.name),
            [{ name: "<PERSON>" }]
        );
        assert.deepEqual(
            fuzzyLookup("z", data, (d) => d.name),
            []
        );
        assert.deepEqual(
            fuzzyLookup("brand", data, (d) => d.name),
            [{ name: "<PERSON>" }]
        );
        assert.deepEqual(
            fuzzyLookup("jâ", data, (d) => d.name),
            [{ name: "<PERSON>" }]
        );
        assert.deepEqual(
            fuzzyLookup("je", data, (d) => d.name),
            [{ name: "<PERSON><PERSON><PERSON>my <PERSON>" }, { name: "<PERSON>" }]
        );
        assert.deepEqual(
            fuzzyLookup("", data, (d) => d.name),
            []
        );
    });

    QUnit.test("fuzzyTest", function (assert) {
        assert.ok(fuzzyTest("a", "Abby White"));
        assert.ok(fuzzyTest("ba", "Brandon Green"));
        assert.ok(fuzzyTest("je", "Jérémy red"));
        assert.ok(fuzzyTest("jé", "Jeremy red"));
        assert.notOk(fuzzyTest("z", "Abby White"));
        assert.notOk(fuzzyTest("ba", "Abby White"));
    });
});
