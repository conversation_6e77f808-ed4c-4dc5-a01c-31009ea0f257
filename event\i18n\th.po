# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# Khwu<PERSON><PERSON>awang <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>dev <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 16:40+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_partner__event_count
#: model:ir.model.fields,field_description:event.field_res_users__event_count
msgid "# Events"
msgstr "# อีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_count_done
msgid "# Sent"
msgstr "# ส่งแล้ว"

#. module: event
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (สำเนา)"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_foldable_badge
msgid ""
"'Foldable Badge - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), object.name.replace('/',''))"
msgstr ""
"'ป้ายพับได้ - %s - %s' % ((object.event_id.name or 'Event').replace('/',''),"
" object.name.replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_foldable_badge
msgid "'Foldable Badge - %s' % (object.name or 'Event').replace('/','')"
msgstr "'ป้ายพับได้ - %s' % (object.name or 'Event').replace('/','')"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_registration_full_page_ticket
msgid ""
"'Full Page Ticket - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), object.name.replace('/',''))"
msgstr ""
"'ทิกเก็ตเต็มหน้า - %s - %s' % ((object.event_id.name or "
"'Event').replace('/',''), object.name.replace('/',''))"

#. module: event
#: model:ir.actions.report,print_report_name:event.action_report_event_event_full_page_ticket
msgid "'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')"
msgstr "'ทิกเก็ตเต็มหน้า - %s' % (object.name or 'Event').replace('/','')"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_2
msgid "10-14"
msgstr "10-14"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_3
msgid "15-18"
msgstr "15-18"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_4
msgid "18+"
msgstr "18+"

#. module: event
#: model:event.tag,name:event.event_tag_category_1_tag_1
msgid "5-10"
msgstr "5-10"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Business Room</b> - To discuss implementation methodologies, best sales "
"practices, etc."
msgstr ""
"<b>ห้องธุรกิจ</b> - เพื่อพูดคุยเกี่ยวกับวิธีการใช้งาน "
"แนวทางปฏิบัติในการขายที่ดีที่สุด และอิ่น ๆ "

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>Technical Rooms</b> - One dedicated to advanced Odoo developers, one for "
"new developers."
msgstr ""
"<b>ห้องเทคนิค</b> - หนึ่งรายการสำหรับนักพัฒนา Odoo ขั้นสูง "
"อีกหนึ่งรายการสำหรับนักพัฒนาใหม่"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The Design Fair is preceded by 2 days of Training Sessions for "
"experts!</b><br> We propose 3 different training sessions, 2 days each."
msgstr ""
"<b>งานออกแบบก่อนหน้าเซสชั่นการฝึกอบรม 2 วันสำหรับผู้เชี่ยวชาญ!</b><br> "
"เราเสนอการฝึกอบรมที่แตกต่างกัน 3 ครั้ง ครั้งละ 2 วัน"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The plenary sessions in the morning will be shorter</b> and we will give "
"more time for thematical meetings, conferences, workshops and tutorial "
"sessions in the afternoon."
msgstr ""
"<b>เซสชันเต็มในช่วงเช้าจะสั้นลง</b> "
"และเราจะให้เวลามากขึ้นสำหรับการประชุมตามหัวข้อ การประชุม เวิร์คช็อป "
"และการฝึกสอนในช่วงบ่าย"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<b>The whole event is open to all public!</b> <br>We ask a participation fee"
" of 49.50€ for the costs for the 3 days (coffee breaks, catering, drinks and"
" a surprising concert and beer party).<br> For those who don't want to "
"contribute, there is a free ticket, therefore, catering and access to "
"evening events aren't included."
msgstr ""
"<b>อีเวนต์ทั้งหมดเปิดให้ประชาชนทั่วไป!</b> <br>เราขอค่าธรรมเนียมการเข้าร่วม "
"49.50 ยูโรสำหรับค่าใช้จ่าย 3 วัน (การพักดื่มกาแฟ จัดเลี้ยง เครื่องดื่ม "
"และคอนเสิร์ตที่น่าประทับใจและปาร์ตี้เบียร์)<br>สำหรับผู้ที่ไม่ต้องการเสียเงิน"
" มีตั๋วฟรีซึ่งจึงไม่รวมการจัดเลี้ยงและการเข้าร่วมกิจกรรมตอนเย็น"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "<b>Workshop Room</b> - Mainly for developers."
msgstr "<b>ห้องเวิร์คช็อป</b> - ส่วนใหญ่สำหรับนักพัฒนา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                            <span class=\"text-muted\">Air your tracks online through a Youtube integration</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"text-muted\">ออกอากาศเพลงของคุณทางออนไลน์ผ่านการผสานกับ Youtube</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<br/>\n"
"                                            <span class=\"text-muted\">Share a quiz to your attendees once a track is over</span>"
msgstr ""
"<br/>\n"
"                                            <span class=\"text-muted\">แชร์แบบทดสอบให้ผู้เข้าร่วมของคุณฟังเมื่อเพลงจบ</span>"

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"    Thank you for your inquiry.<br/>\n"
"    Here is your badge for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.<br/>\n"
"    If you have any questions, please let us know.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    เรียน <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"    ขอบคุณสำหรับคำถามของคุณ<br/>\n"
"    นี้คือป้ายอีเวนต์ของคุณ<t t-out=\"object.event_id.name or ''\"> การเปิดเผยออนไลน์ของ OpenWood Collection </t><br/>\n"
"    หากคุณมีคำถามใด ๆ โปรดแจ้งให้เราทราบ\n"
"    <br/><br/>\n"
"    ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<em>If you wish to make a presentation, please send your topic proposal as "
"soon as possible for approval to Mr. Famke Jenssens at ngh (a) yourcompany "
"(dot) com. The presentations should be, for example, a presentation of a "
"community module, a case study, methodology feedback, technical, etc. Each "
"presentation must be in English.</em>"
msgstr ""
"<em>หากคุณต้องการนำเสนอ "
"โปรดส่งข้อเสนอหัวข้อของคุณโดยเร็วที่สุดเพื่อขออนุมัติไปยัง Mr. Famke "
"Jenssens ที่ ngh (a) yourcompany (dot) com การนำเสนอควรเป็นในรูปแบบนี้ เช่น "
"การนำเสนอโมดูลชุมชน กรณีศึกษา ข้อเสนอแนะเกี่ยวกับวิธีการ เทคนิค และอื่น ๆ "
"การนำเสนอแต่ละรายการต้องเป็นภาษาอังกฤษ</em>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-arrow-right o_event_fontsize_09\" title=\"End date\"/>"
msgstr "<i class=\"fa fa-arrow-right o_event_fontsize_09\" title=\"วันสิ้นสุด\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""
"<i class=\"fa fa-check fa-3x\" role=\"img\" aria-label=\"ปุ่มยืนยัน\" "
"title=\"ยืนยันการลงทะเบียน\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"Confirm button\" "
"title=\"Confirm Registration\"/>"
msgstr ""
"<i class=\"fa fa-check\" role=\"img\" aria-label=\"ปุ่มยืนยัน\" "
"title=\"ยืนยันการลงทะเบียน\"/>"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"<i class=\"fa fa-info-circle mr-2\"></i>This event and all the conferences "
"are in <b>English</b>!"
msgstr ""
"<i class=\"fa fa-info-circle mr-2\"></i>อีเวนต์นี้และการประชุมทั้งหมดเป็น "
"<b>ภาษาอังกฤษ</b>!"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-level-up fa-rotate-90\" title=\"Confirmed\"/>"
msgstr "<i class=\"fa fa-level-up fa-rotate-90\" title=\"ยืนยันแล้ว\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"ลูกศร\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "<i class=\"fa fa-map-marker\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-map-marker\" title=\"ตำแหน่ง\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-user-plus fa-3x\" role=\"img\" aria-label=\"Attended "
"button\" title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-user-plus fa-3x\" role=\"img\" aria-"
"label=\"ปุ่มการเข้าร่วม\" title=\"ยีนยันการเข้าร่วม\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<i class=\"fa fa-user-plus\" role=\"img\" aria-label=\"Attended button\" "
"title=\"Confirm Attendance\"/>"
msgstr ""
"<i class=\"fa fa-user-plus\" role=\"img\" aria-label=\"ปุ่มการเข้าร่วม\" "
"title=\"ยืนยันการเข้าร่วม\"/>"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<span class=\"badge badge-secondary o_wevent_badge float-"
"right\">SPEAKER</span>"
msgstr ""
"<span class=\"badge badge-secondary o_wevent_badge float-"
"right\">ผู้พูด</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" "
"title=\"ค่าที่ตั้งไว้นี้เป็นค่าเฉพาะบริษัท\" "
"groups=\"base.group_multi_company\"/>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid ""
"<span class=\"text-muted\" states=\"done\">Attended</span>\n"
"                                    <span class=\"text-muted\" states=\"cancel\">Canceled</span>"
msgstr ""
"<span class=\"text-muted\" states=\"done\">เข้าร่วมแล้ว</span>\n"
"                                    <span class=\"text-muted\" states=\"cancel\">ยกเลิกแล้ว</span>"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "<span>John Doe</span>"
msgstr "<span>John Doe</span>"

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Oscar Morgan</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Event\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"                        We are excited to remind you that the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        is starting <strong t-out=\"object.get_date_range_str() or ''\">today</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        We confirm your registration and hope to meet you there,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> <t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>Get the best mobile experience.</strong>\n"
"                        <a href=\"/event\">Install our mobile app</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <table t-if=\"event_address\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-attf-src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C{{ location }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ส่วนหัว -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">การลงทะเบียน</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Oscar Morgan</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-attf-href=\"{{ object.event_id.website_url }}\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            ดูอีเวนต์\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!--รายละเอียดงานอีเวนต์-->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        สวัสดี <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"                        เรารู้สึกตื่นเต้นที่จะแจ้งคุณว่าอีเวนต์นี้\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">การเปิดตัวออนไลน์ของ OpenWood Collection </a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        จะเริ่มขึ้น <strong t-out=\"object.get_date_range_str() or ''\">วันนี้</strong>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>เพิ่มอีเวนต์นี้ในปฏิทินของคุณ</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        เราได้ยืนยันการลงทะเบียนของคุณและหวังว่าจะได้พบคุณที่นั่น<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            ทีมงาน <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- รายละเอียด -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>จาก</strong> <t t-out=\"object.event_id.date_begin_located or ''\">พฤษภาคม 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>ถึง</strong><t t-out=\"object.event_id.date_end_located or ''\">พฤษภาคม 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> <t t-out=\"object.event_id.date_tz or ''\">ยุโรป/บรัสเซลส์</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">อาร์เจนตินา</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- ติดต่อผู้จัดงาน -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">มีคำถามเกี่ยวกับอีเวนต์นี้หรือไม่?</span>\n"
"                            <div>โปรดติดต่อผู้จัดงาน:</div>\n"
"                            <ul>\n"
"                                <li t-out=\"event_organizer.name or ''\">YourCompany</li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>เมล: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"/></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>โทรศัพท์: <t t-out=\"event_organizer.phone or ''\"/></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- ติดต่อผู้จัดงานแยก -->\n"
"                    <hr t-if=\"is_online or event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <div t-if=\"is_online\">\n"
"                        <strong>รับประสบการณ์การใช้งานมือถือที่ดีที่สุด</strong>\n"
"                        <a href=\"/event\">ติดตั้งแอปบนมือถือของเรา</a>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <hr t-if=\"is_online and event_address\" width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- ลิงก์แผนที่ GOOGLE -->\n"
"                    <table t-if=\"event_address\" style=\"width:100%;\"><tr><td>\n"
"                        <div>\n"
"                            <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                <img t-attf-src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C{{ location }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                    </td></tr></table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- ส่วนท้ายโดย -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table t-if=\"object.company_id\" width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        ส่งโดย <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"        <t t-if=\"'website_url' in object.event_id and object.event_id.website_url\">\n"
"            <br/>\n"
"            ค้นพบ <a href=\"/event\" style=\"color:#875A7B;\">อีเวนต์ทั้งหมด</a>\n"
"        </t>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your registration</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Oscar Morgan</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-att-href=\"object.event_id.website_url\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            View Event\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- EVENT DESCRIPTION -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Hello <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"                        We are happy to confirm your registration to the event\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        for attendee <t t-out=\"object.name or ''\">Oscar Morgan</t>.\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>Add this event to your calendar</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        See you soon,<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> Team\n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- DETAILS -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>From</strong> <t t-out=\"object.event_id.date_begin_located or ''\">May 4, 2021, 7:00:00 AM</t></div>\n"
"                                <div><strong>To</strong> <t t-out=\"object.event_id.date_end_located or ''\">May 6, 2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>TZ</strong> <t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- CONTACT ORGANIZER -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">Questions about this event?</span>\n"
"                            <div>Please contact the organizer:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>Mail: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>Phone: <t t-out=\"event_organizer.phone or ''\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- CONTACT ORGANIZER SEPARATION -->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>Get the best mobile experience.</strong>\n"
"                            <a href=\"/event\">Install our mobile app</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- GOOGLE MAPS LINK -->\n"
"                    <t t-if=\"event_address\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-attf-src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C{{ location }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- FOOTER BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            Sent by <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                Discover <a href=\"/event\" style=\"color:#875A7B;\">all our events</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<t t-set=\"date_begin\" t-value=\"format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"date_end\" t-value=\"format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)\"/>\n"
"<t t-set=\"is_online\" t-value=\"'is_published' in object.event_id and object.event_id.is_published\"/>\n"
"<t t-set=\"event_organizer\" t-value=\"object.event_id.organizer_id\"/>\n"
"<t t-set=\"event_address\" t-value=\"object.event_id.address_id\"/>\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- ส่วนหัว -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">การลงทะเบียนของคุณ</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Oscar Morgan</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <t t-if=\"is_online\">\n"
"                        <a t-att-href=\"object.event_id.website_url\" style=\"padding: 8px 12px; font-size: 12px; color: #FFFFFF; text-decoration: none !important; font-weight: 400; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"                            ดูอีเวนต์\n"
"                        </a>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <img t-att-src=\"'/logo.png?company=%s' % object.company_id.id\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"'%s' % object.company_id.name\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- คำอธิบายอีเวนต์ -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                       สวัสดี <t t-out=\"object.name or ''\">Oscar Morgan</t>,<br/>\n"
"                      เรายินดีที่จะยืนยันการลงทะเบียนเข้าร่วมงานอีเวนต์ของคุณ\n"
"                        <t t-if=\"is_online\">\n"
"                            <a t-att-href=\"object.event_id.website_url\" style=\"color:#875A7B;text-decoration:none;\" t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</a>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <strong t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</strong>\n"
"                        </t>\n"
"                        สำหรับผู้เข้าร่วม <t t-out=\"object.name or ''\">Oscar Morgan</t>\n"
"                    </div>\n"
"                    <div>\n"
"                        <br/>\n"
"                        <strong>เพิ่มอีเวนต์นี้ในปฏิทินของคุณ</strong>\n"
"                        <a t-attf-href=\"https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Google</a>\n"
"                        <a t-attf-href=\"/event/{{ slug(object.event_id) }}/ics\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\"><img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> iCal/Outlook</a>\n"
"                        <a t-attf-href=\"https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\\'T\\'HHmmss') }}\" style=\"padding:3px 5px;border:1px solid #875A7B;color:#875A7B;text-decoration:none;border-radius:3px;\" target=\"new\">\n"
"                            <img src=\"/web_editor/font_to_img/61525/rgb(135,90,123)/16\" style=\"vertical-align:middle;\" height=\"16\" alt=\"\"/> Yahoo\n"
"                        </a>\n"
"                        <br/><br/>\n"
"                    </div>\n"
"                    <div>\n"
"                        แล้วเจอกัน<br/>\n"
"                        <span style=\"color: #454748;\">\n"
"                        -- <br/>\n"
"                        <t t-if=\"event_organizer\">\n"
"                            <t t-out=\"event_organizer.name or ''\">YourCompany</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            ทีม The <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t> \n"
"                        </t>\n"
"                        </span>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- รายละเอียด -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;\">\n"
"                        <tr>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <img src=\"/web_editor/font_to_img/61555/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;\">\n"
"                                <div><strong>จาก</strong> <t t-out=\"object.event_id.date_begin_located or ''\">พฤษภาคม 4 2021 7:00:00 AM</t></div>\n"
"                                <div><strong>ถึง</strong> <t t-out=\"object.event_id.date_end_located or ''\">พฤษภาคม 6  2021, 5:00:00 PM</t></div>\n"
"                                <div style=\"font-size:12px;color:#9e9e9e\"><i><strong>โซนเวลา</strong> <t t-out=\"object.event_id.date_tz or ''\">Europe/Brussels</t></i></div>\n"
"                            </td>\n"
"                            <td style=\"vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <img src=\"/web_editor/font_to_img/61505/rgb(81,81,102)/34\" style=\"padding:4px;max-width:inherit;\" height=\"34\" alt=\"\"/>\n"
"                                </t>\n"
"                            </td>\n"
"                            <td style=\"padding: 0px 10px 0px 10px;width:50%;vertical-align:top;\">\n"
"                                <t t-if=\"event_address\">\n"
"                                    <t t-set=\"location\" t-value=\"''\"/>\n"
"                                    <t t-if=\"object.event_id.address_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.name or ''\">Teksa SpA</div>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street\">\n"
"                                        <div t-out=\"object.event_id.address_id.street or ''\">Puerto Madero 9710</div>\n"
"                                        <t t-set=\"location\" t-value=\"object.event_id.address_id.street\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.street2\">\n"
"                                        <div t-out=\"object.event_id.address_id.street2 or ''\">Of A15, Santiago (RM)</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.street2)\"/>\n"
"                                    </t>\n"
"                                    <div>\n"
"                                    <t t-if=\"object.event_id.address_id.city\">\n"
"                                        <t t-out=\"object.event_id.address_id.city or ''\">Pudahuel</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.city)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.state_id.name\">\n"
"                                        <t t-out=\"object.event_id.address_id.state_id.name or ''\">C1</t>,\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.state_id.name)\"/>\n"
"                                    </t>\n"
"                                    <t t-if=\"object.event_id.address_id.zip\">\n"
"                                        <t t-out=\"object.event_id.address_id.zip or ''\">98450</t>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.zip)\"/>\n"
"                                    </t>\n"
"                                    </div>\n"
"                                    <t t-if=\"object.event_id.address_id.country_id.name\">\n"
"                                        <div t-out=\"object.event_id.address_id.country_id.name or ''\">Argentina</div>\n"
"                                        <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.event_id.address_id.country_id.name)\"/>\n"
"                                    </t>\n"
"                                </t>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- ติดต่อผู้จัดงาน -->\n"
"                    <t t-if=\"event_organizer\">\n"
"                        <div>\n"
"                            <span style=\"font-weight:300;margin:10px 0px\">มีคำถามเกี่ยวกับอีเวนต์นี้หรือไม่?</span>\n"
"                            <div>โปรดติดต่อผู้จัดงาน:</div>\n"
"                            <ul>\n"
"                                <li><t t-out=\"event_organizer.name or ''\">YourCompany</t></li>\n"
"                                <t t-if=\"event_organizer.email\">\n"
"                                    <li>เมล: <a t-attf-href=\"mailto:{{ event_organizer.email }}\" style=\"text-decoration:none;color:#875A7B;\" t-out=\"event_organizer.email or ''\"><EMAIL></a></li>\n"
"                                </t>\n"
"                                <t t-if=\"event_organizer.phone\">\n"
"                                    <li>โทรศัพท์: <t t-out=\"event_organizer.phone or ''\">******-123-4567</t></li>\n"
"                                </t>\n"
"                            </ul>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- ติดต่อผู้จัดงานแยก-->\n"
"                    <t t-if=\"is_online or event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- PWA MARKGETING -->\n"
"                    <t t-if=\"is_online\">\n"
"                        <div>\n"
"                            <strong>รับประสบการณ์การใช้งานมือถือที่ดีที่สุด</strong>\n"
"                            <a href=\"/event\">ติดตั้งแอปบนมือถือของเรา</a>\n"
"                        </div>\n"
"                    </t>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <!-- PWA MARKGETING SEPARATION-->\n"
"                    <t t-if=\"is_online and event_address\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </t>\n"
"                </td></tr>\n"
"\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <!-- ลิงก์แผนที่ GOOGLE -->\n"
"                    <t t-if=\"event_address\">\n"
"                        <table style=\"width:100%;\"><tr><td>\n"
"                            <div>\n"
"                                <a t-attf-href=\"https://maps.google.com/maps?q={{ location }}\" target=\"new\">\n"
"                                    <img t-attf-src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&amp;size=598x200&amp;maptype=roadmap&amp;format=png&amp;visual_refresh=true&amp;markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C{{ location }}\" style=\"vertical-align:bottom; width: 100%;\" alt=\"Google Maps\"/>\n"
"                                </a>\n"
"                            </div>\n"
"                        </td></tr></table>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- ส่วนท้ายโดย -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <t t-if=\"object.company_id\">\n"
"        <table width=\"590\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"        <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"            ส่งโดย <a target=\"_blank\" t-attf-href=\"{{ object.company_id.website }}\" style=\"color: #875A7B;\" t-out=\"object.company_id.name or ''\">YourCompany</a>\n"
"            <t t-if=\"is_online\">\n"
"                <br/>\n"
"                ค้นพบ <a href=\"/event\" style=\"color:#875A7B;\">อีเวนต์ทั้งหมดของคุณ</a>.\n"
"            </t>\n"
"        </td></tr>\n"
"        </table>\n"
"    </t>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__description
#: model:ir.model.fields,help:event.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "คำอธิบายของทิกเก็ตที่คุณต้องการสื่อสารกับลูกค้าของคุณ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__active
#: model:ir.model.fields,field_description:event.field_event_registration__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_ids
#: model:ir.model.fields,field_description:event.field_event_registration__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_2
msgid "Activity"
msgstr "กิจกรรม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_state
#: model:ir.model.fields,field_description:event.field_event_registration__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Add a description..."
msgstr "เพิ่มคำอธิบาย"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Add a navigation menu to your event web pages with schedule, tracks, a track"
" proposal form, etc."
msgstr ""
"เพิ่มเมนูการนำทางไปยังหน้าเว็บอีเวนต์ของคุณด้วยกำหนดการ เส้นทาง "
"แบบฟอร์มข้อเสนอการติดตาม และอื่นๆ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Add a note..."
msgstr "เพิ่มโน้ต..."

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Administrator"
msgstr "ผู้ดูแลระบบ"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_exhibitor
msgid "Advanced Sponsors"
msgstr "ผู้ให้การสนับสนุนขั้นสูง"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_sub
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_sub
msgid "After each registration"
msgstr "หลังจากการลงทะเบียนแต่ละครั้ง"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__after_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__after_event
msgid "After the event"
msgstr "หลังจากอีเวนต์"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_1
msgid "Age"
msgstr "อายุ"

#. module: event
#: model:event.event,name:event.event_6
msgid "An unpublished event"
msgstr "อีเวนต์ที่ไม่ได้เผยแพร่"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"And this time, we go fully ONLINE! Meet us in our live streams from the comfort of your house.<br>\n"
"        Special discount codes will be handed out during the various streams, make sure to be there on time."
msgstr ""
"และครั้งนี้ ลุย ONLINE เต็มที่! พบกับเราในสตรีมสดของเราจากความสะดวกสบายในบ้านของคุณ<br>\n"
"        รหัสส่วนลดพิเศษจะถูกแจกในระหว่างการสตรีมต่าง ๆ อย่าลืมไปให้ตรงเวลา"

#. module: event
#: model:event.stage,name:event.event_stage_announced
msgid "Announced"
msgstr "ประกาศ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"Around one hundred ballons will simultaneously take flight and turn the sky "
"into a beautiful canvas of colours."
msgstr ""
"ลูกโป่งราวหนึ่งร้อยลูกจะโบยบินไปพร้อม ๆ กัน "
"และเปลี่ยนท้องฟ้าให้กลายเป็นผืนผ้าใบหลากสีสันที่สวยงาม"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "As a team, we are happy to contribute to this event."
msgstr "ในฐานะทีม เรายินดีที่จะมีส่วนร่วมในอีเวนต์นี้"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"เมื่ออายุเพียง 13 ปี John DOE "
"ได้เริ่มพัฒนาแอปพลิเคชันทางธุรกิจครั้งแรกสำหรับลูกค้า "
"หลังจากเชี่ยวชาญด้านวิศวกรรมโยธา เขาได้ก่อตั้ง TinyERP ซึ่งเป็นระยะแรกของ "
"OpenERP ซึ่งต่อมาได้กลายเป็น Odoo "
"ซึ่งเป็นซอฟต์แวร์ธุรกิจโอเพ่นซอร์สที่มีการติดตั้งมากที่สุดทั่วโลก"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_attachment_count
#: model:ir.model.fields,field_description:event.field_event_registration__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Attendance"
msgstr "การเข้าร่วม"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__done
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended"
msgstr "เข้าร่วมแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_closed
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Attended Date"
msgstr "วันที่เข้าร่วม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "ผู้เข้าร่วม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__name
msgid "Attendee Name"
msgstr "ชื่อผู้เข้าร่วม"

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.actions.act_window,name:event.event_registration_action
#: model:ir.model.fields,field_description:event.field_event_event__registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Attendees"
msgstr "ผู้เข้าร่วม"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__auto_confirm
msgid ""
"Autoconfirm Registrations. Registrations will automatically be confirmed "
"upon creation."
msgstr ""
"ยืนยันการลงทะเบียนอัตโนมัติ "
"การลงทะเบียนจะได้รับการยืนยันโดยอัตโนมัติเมื่อสร้าง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__auto_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Autoconfirmation"
msgstr "การยืนยันอัตโนมัติ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__auto_confirm
msgid "Automatically Confirm Registrations"
msgstr "ยืนยันการลงทะเบียนโดยอัตโนมัติ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_available
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_available
msgid "Available Seats"
msgstr "ที่นั่งที่ว่าง"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"Bands like Bar Fighters, Led Slippers and Link Floyd will offer you the show"
" of the century during our three day event."
msgstr ""
"วงดนตรีอย่าง Bar Fighters, Led Wristwatch และ Pink Floyd "
"จะนำเสนอการแสดงแห่งศตวรรษในช่วงงานสามวันนี้ของเรา"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_barcode
msgid "Barcode"
msgstr "บาร์โค้ด"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_type__before_event
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_type__before_event
msgid "Before the event"
msgstr "ก่อนอีเวนต์"

#. module: event
#: code:addons/event/models/event_stage.py:0
#: model:event.event,legend_blocked:event.event_0
#: model:event.event,legend_blocked:event.event_1
#: model:event.event,legend_blocked:event.event_2
#: model:event.event,legend_blocked:event.event_3
#: model:event.event,legend_blocked:event.event_4
#: model:event.event,legend_blocked:event.event_5
#: model:event.event,legend_blocked:event.event_6
#: model:event.event,legend_blocked:event.event_7
#: model:event.stage,legend_blocked:event.event_stage_announced
#: model:event.stage,legend_blocked:event.event_stage_booked
#: model:event.stage,legend_blocked:event.event_stage_cancelled
#: model:event.stage,legend_blocked:event.event_stage_done
#: model:event.stage,legend_blocked:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__blocked
#, python-format
msgid "Blocked"
msgstr "บล็อก"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_1
msgid ""
"Bloem brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"Bloem นำความซื่อสัตย์และจริงจังมาสู่อุตสาหกรรมไม้ "
"ในขณะเดียวกันก็ช่วยลูกค้าจัดการกับต้นไม้ ดอกไม้ และเชื้อรา"

#. module: event
#: model:event.stage,name:event.event_stage_booked
msgid "Booked"
msgstr "จอง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Booked By"
msgstr "จองแล้วโดย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__partner_id
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Booked by"
msgstr "จองแล้วโดย"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_booth
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Booth Management"
msgstr "การจัดการบูธ"

#. module: event
#: model:event.event,subtitle:event.event_5
msgid ""
"Bring your outdoor field hockey season to the next level by taking the field"
" at this 9th annual Field Hockey tournament."
msgstr ""
"นำฤดูกาลฮอกกี้สนามกลางแจ้งของคุณไปสู่อีกระดับด้วยการลงสนามในการแข่งขันฮอกกี้สนามประจำปีครั้งที่"
" 9 นี้"

#. module: event
#: model:event.event,name:event.event_4
msgid "Business workshops"
msgstr "การประชุมเชิงปฏิบัติการธุรกิจ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "แคมเปญ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel"
msgstr "ยกเลิก"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Cancel Registration"
msgstr "ยกเลิกการลงทะเบียน"

#. module: event
#: model:event.stage,name:event.event_stage_cancelled
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__cancel
msgid "Cancelled"
msgstr "ยกเลิกแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__category_id
msgid "Category"
msgstr "หมวดหมู่"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"Chamber Works reserves the right to cancel, re-name or re-locate the event "
"or change the dates on which it is held."
msgstr ""
"Chamber Works ขอสงวนสิทธิ์ในการยกเลิก เปลี่ยนชื่อหรือค้นหาสถานที่ใหม่ "
"หรือเปลี่ยนวันที่จัดงาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Come see us live, we hope to meet you !"
msgstr "มาพบกับไลฟ์สดของเรา เราหวังว่าจะได้พบคุณ !"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Communication"
msgstr "การติดต่อสื่อสาร"

#. module: event
#: model:ir.model.fields,help:event.field_event_mail__mail_registration_ids
msgid "Communication related to event registrations"
msgstr "การสื่อสารที่เกี่ยวข้องกับการลงทะเบียนอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Community Chat Rooms"
msgstr "ห้องแชทชุมชน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__company_id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__company_id
#: model:ir.model.fields,field_description:event.field_event_registration__company_id
#: model_terms:event.event,description:event.event_2
msgid "Company"
msgstr "บริษัท"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Compose Email"
msgstr "เขียนอีเมล"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_2
#: model:event.type,name:event.event_type_data_conference
msgid "Conference"
msgstr "การประชุม"

#. module: event
#: model:event.event,name:event.event_2
#: model_terms:event.event,description:event.event_2
msgid "Conference for Architects"
msgstr "การประชุมสำหรับสถาปนิก"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Conferences, workshops and trainings will be organized in 6 rooms:"
msgstr "การประชุม เวิร์คช็อป การและการฝึกอบรมจะจัดขึ้นใน 6 ห้อง:"

#. module: event
#: model:ir.model,name:event.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm"
msgstr "ยืนยัน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Attendance"
msgstr "ยืนยันการเข้าร่วม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_registration_view_kanban
msgid "Confirm Registration"
msgstr "ยืนยันการลงทะเบียน"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__open
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: event
#: model:ir.model,name:event.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__country_id
msgid "Country"
msgstr "ประเทศ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Create Booths and manage their reservations"
msgstr "สร้างบูธและจัดการการจองของพวกเขา"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Create an Event"
msgstr "สร้างอีเวนต์"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid "Create an Event Stage"
msgstr "สร้างขั้นตอนอีเวนต์"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Create an Event Tag Category"
msgstr "สร้างหมวดหมู่แท็กอีเวนต์"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid "Create an Event Template"
msgstr "สร้างเทมเพลตอีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_registration__create_uid
#: model:ir.model.fields,field_description:event.field_event_stage__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag__create_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_uid
#: model:ir.model.fields,field_description:event.field_event_type__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__create_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__create_date
#: model:ir.model.fields,field_description:event.field_event_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_registration__create_date
#: model:ir.model.fields,field_description:event.field_event_stage__create_date
#: model:ir.model.fields,field_description:event.field_event_tag__create_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__create_date
#: model:ir.model.fields,field_description:event.field_event_type__create_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__create_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_1
msgid "Culture"
msgstr "วัฒนธรรม"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer"
msgstr "ลูกค้า"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Customer Email"
msgstr "อีเมลลูกค้า"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Date"
msgstr "วันที่"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__days
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__days
msgid "Days"
msgstr "วัน"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,help:event.field_event_type_ticket__seats_max
msgid ""
"Define the number of available tickets. If you have too many registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""
"กำหนดจำนวนตั๋วที่ใช้ได้ หากคุณมีการลงทะเบียนมากเกินไป "
"คุณจะไม่สามารถขายตั๋วได้อีกต่อไป ตั้งค่า 0 เพื่อละเว้นกฎนี้ตั้งเป็นไม่จำกัด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__description
#: model:ir.model.fields,field_description:event.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event.field_event_type_ticket__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: event
#: model:event.event,name:event.event_0
msgid "Design Fair Los Angeles"
msgstr "งานออกแบบ Los Angeles"

#. module: event
#: model:event.event,subtitle:event.event_4
msgid "Discover how to grow a sustainable business with our experts."
msgstr "ค้นพบวิธีสร้างธุรกิจที่ยั่งยืนกับผู้เชี่ยวชาญของเรา"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Discover more"
msgstr "ค้นพบเพิ่มขึ้น"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_meet
msgid "Discussion Rooms"
msgstr "ห้องสนทนา"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__display_name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__display_name
#: model:ir.model.fields,field_description:event.field_event_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_registration__display_name
#: model:ir.model.fields,field_description:event.field_event_stage__display_name
#: model:ir.model.fields,field_description:event.field_event_tag__display_name
#: model:ir.model.fields,field_description:event.field_event_tag_category__display_name
#: model:ir.model.fields,field_description:event.field_event_type__display_name
#: model:ir.model.fields,field_description:event.field_event_type_mail__display_name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Display Sponsors and Exhibitors on your event pages"
msgstr "แสดงผู้สนับสนุนและผู้จัดแสดงในหน้าอีเวนต์ของคุณ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__sequence
msgid "Display order"
msgstr "แสดงลำดับ"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__done
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""
"ในระหว่างการประชุมนี้ "
"ทีมงานของเราจะนำเสนอภาพรวมโดยละเอียดของการสมัครทางธุรกิจของเรา "
"คุณจะรู้ถึงประโยชน์ทั้งหมดของการใช้งาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__email
msgid "Email"
msgstr "อีเมล"

#. module: event
#: model:ir.model,name:event.model_mail_template
msgid "Email Templates"
msgstr "เทมเพลตอีเมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end
msgid "End Date"
msgstr "วันสิ้นสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_end_located
msgid "End Date Located"
msgstr "วันที่สิ้นสุดตั้งอยู่"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__pipe_end
msgid "End Stage"
msgstr "ขั้นตอนสิ้นสุด"

#. module: event
#: model:event.stage,name:event.event_stage_done
msgid "Ended"
msgstr "สิ้นสุดแล้ว"

#. module: event
#: model:event.event,subtitle:event.event_2
msgid "Enhance your architectural business and improve professional skills."
msgstr "ยกระดับธุรกิจสถาปัตยกรรมของคุณและพัฒนาทักษะทางวิชาชีพ"

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_event__name
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_id
#: model:ir.model.fields,field_description:event.field_event_mail__event_id
#: model:ir.model.fields,field_description:event.field_event_registration__event_id
#: model_terms:ir.ui.view,arch_db:event.event_event_view_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event"
msgstr "อีเวนต์"

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "Event Automated Mailing"
msgstr "การส่งเมลอัตโนมัติของอีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__event_type_id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__event_type_id
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Event Category"
msgstr "หมวดหมู่เอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_form
msgid "Event Category Tag"
msgstr "แท็กหมวดหมู่อีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_end_date
msgid "Event End Date"
msgstr "วันที่สิ้นสุดอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Event Gamification"
msgstr "อีเวนต์ในรูปแบบเกม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr "ข้อมูลอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr "ตัวจัดกำหนดการเมลอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr "ตัวจัดกำหนดการเมลอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr "ชื่ออีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr "อีเวนต์องค์กร"

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr "การลงทะเบียนอีเวนต์"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"Event Scheduler for:\n"
"  - Event: %(event_name)s (%(event_id)s)\n"
"  - Scheduled: %(date)s\n"
"  - Template: %(template_name)s (%(template_id)s)\n"
"\n"
"Failed with error:\n"
"  - %(error)s\n"
"\n"
"You receive this email because you are:\n"
"  - the organizer of the event,\n"
"  - or the responsible of the event,\n"
"  - or the last writer of the template.\n"
msgstr ""
"กำหนดการอีเวนต์สำหรับ:\n"
"  - อีเวนต์: %(event_name)s (%(event_id)s)\n"
"  - กำหนดการ: %(date)s\n"
"  - เทมเพลต: %(template_name)s (%(template_id)s)\n"
"\n"
"ล้มเหลวด้วยข้อผิดพลาด:\n"
"  - %(error)s\n"
"\n"
"คุณได้รับอีเมลนี้เพราะคุณคือ:\n"
"  - ผู้จัดงานอีเวนต์\n"
"  - หรือผู้รับผิดชอบอีเวนต์\n"
"  - หรือผู้เขียนเทมเพลตคนล่าสุด\n"

#. module: event
#: model:ir.model,name:event.model_event_stage
msgid "Event Stage"
msgstr "ขั้นตอนอีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.event_stage_action
#: model:ir.ui.menu,name:event.event_stage_menu
msgid "Event Stages"
msgstr "ขั้นตอนอีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__event_begin_date
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Start Date"
msgstr "วันที่เริ่มต้นอีเวนต์"

#. module: event
#: model:ir.model,name:event.model_event_tag
msgid "Event Tag"
msgstr "แท็กอีเวนต์"

#. module: event
#: model:ir.model,name:event.model_event_tag_category
msgid "Event Tag Category"
msgstr "หมวดหมู่แท็กอีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.event_tag_category_action_tree
#: model:ir.ui.menu,name:event.menu_event_category
#: model_terms:ir.ui.view,arch_db:event.event_tag_view_tree
msgid "Event Tags Categories"
msgstr "หมวดหมู่แท็กอีเวนต์"

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type__name
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
msgid "Event Template"
msgstr "เทมเพลตอีเวนต์"

#. module: event
#: model:ir.model,name:event.model_event_type_ticket
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_form_from_type
msgid "Event Template Ticket"
msgstr "ทิกเก็ตเทมเพลตอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_type_ticket_view_tree_from_type
msgid "Event Template Tickets"
msgstr "ทิกเก็ตเทมเพลตอีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
#: model:ir.ui.menu,name:event.menu_event_type
#: model_terms:ir.ui.view,arch_db:event.event_type_view_search
msgid "Event Templates"
msgstr "เทมเพลตอีเวนต์"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_type
msgid ""
"Event Templates combine configurations you use often and are\n"
"                    usually based on the types of events you organize (e.g. \"Workshop\",\n"
"                    \"Roadshow\", \"Online Webinar\", etc)."
msgstr ""
"เทมเพลตอีเวนต์ได้รวมการกำหนดค่าที่คุณใช้บ่อยและเป็น\n"
"                   มักจะขึ้นอยู่กับประเภทของอีเวนต์ที่คุณจัด (เช่น \"เวิร์กช็อป\"\n"
"                   \"โรดโชว์\" \"การสัมมนาผ่านเว็บออนไลน์\" และอื่นๆ )"

#. module: event
#: model:ir.model,name:event.model_event_event_ticket
#: model:ir.model.fields,field_description:event.field_event_event__event_ticket_ids
#: model:ir.model.fields,field_description:event.field_event_registration__event_ticket_id
msgid "Event Ticket"
msgstr "ทิกเก็ตอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_full_page_ticket
msgid "Event Ticket For"
msgstr "ทิกเก็ตอีเวนต์สำหรับ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__event_type_id
msgid "Event Type"
msgstr "ประเภทอีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.event_registration_action_tree
msgid "Event registrations"
msgstr "การลงทะเบียนอีเวนต์"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_stage_action
msgid ""
"Event stages are used to track the progress of an Event from its origin "
"until its conclusion."
msgstr ""
"ขั้นตอนอีเวนต์ที่ใช้เพื่อติดตามความคืบหน้าของอีเวนต์ตั้งแต่จุดเริ่มต้นจนถึงบทสรุป"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "Event's Ticket"
msgstr "ทิกเก็ตของอีเวนต์"

#. module: event
#: model:ir.actions.server,name:event.event_mail_scheduler_ir_actions_server
#: model:ir.cron,cron_name:event.event_mail_scheduler
#: model:ir.cron,name:event.event_mail_scheduler
msgid "Event: Mail Scheduler"
msgstr "อีเวนต์: ตัวจัดกำหนดการเมล"

#. module: event
#: model:mail.template,name:event.event_subscription
msgid "Event: Registration"
msgstr "อีเวนต์: การลงทะเบียน"

#. module: event
#: model:mail.template,name:event.event_registration_mail_template_badge
msgid "Event: Registration Badge"
msgstr "อีเวนต์: ป้ายการลงทะเบียน"

#. module: event
#: model:mail.template,name:event.event_reminder
msgid "Event: Reminder"
msgstr "อีเวนต์: เตือนความจำ"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_event_menu_pivot_report
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_graph
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.res_partner_view_tree
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr "อีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.event_event_action_pivot
msgid "Events Analysis"
msgstr "การวิเคราะห์อีเวนต์"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr "ตัวจัดกำหนดการเมลอีเวนต์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_tree
msgid "Events Stage"
msgstr "ขั้นตอนอีเวนต์"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__auto_confirm
msgid ""
"Events and registrations will automatically be confirmed upon creation, "
"easing the flow for simple events."
msgstr ""
"อีเวนต์และการลงทะเบียนจะได้รับการยืนยันโดยอัตโนมัติเมื่อสร้าง "
"ทำให้ขั้นตอนสำหรับกิจกรรมง่าย ๆ ง่ายขึ้น"

#. module: event
#: model:ir.model.fields,help:event.field_event_stage__pipe_end
msgid ""
"Events will automatically be moved into this stage when they are finished. "
"The event moved into this stage will automatically be set as green."
msgstr ""
"กิจกรรมจะถูกย้ายไปยังขั้นตอนนี้โดยอัตโนมัติเมื่อเสร็จสิ้น "
"เหตุการณ์ที่ย้ายเข้ามาในสเตจนี้จะถูกตั้งค่าเป็นสีเขียวโดยอัตโนมัติ"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"Every year we invite our community, partners and end-users to come and meet "
"us! It's the ideal event to get together and present new features, roadmap "
"of future versions, achievements of the software, workshops, training "
"sessions, etc...."
msgstr ""
"ทุกปีเราเชิญชุมชน พาร์ทเนอร์ และผู้ใช้ปลายทางมาพบกับเรา! "
"นี้เป็นงานในอุดมคติที่เราจะมารวมตัวกันและนำเสนอฟีเจอร์ใหม่ๆ "
"แผนงานของเวอร์ชันในอนาคต ความสำเร็จของซอฟต์แวร์ เวิร์กช็อป เซสชันการฝึกอบรม"
"  และอื่น ๆ "

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc....\n"
"            This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"ทุกปีเราเชิญชุมชน พันธมิตร และผู้ใช้ปลายทางมาพบกับเรา! เป็นงานในอุดมคติที่จะรวมตัวกันและนำเสนอคุณสมบัติใหม่ แผนงานของเวอร์ชันในอนาคต ความสำเร็จของซอฟต์แวร์ เวิร์กช็อป เซสชันการฝึกอบรม ฯลฯ...\n"
"            งานนี้เป็นโอกาสในการแสดงกรณีศึกษา วิธีการ หรือการพัฒนาของพันธมิตรของเรา อยู่ที่นั่นและดูคุณสมบัติของเวอร์ชันใหม่โดยตรงจากแหล่งที่มา!"

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Exhibition"
msgstr "นิทรรศการ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr "ควาดหวัง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Expected Attendees"
msgstr "ผู้เข้าร่วมที่คาดหวัง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr "ผู้เข้าร่วมที่คาดหวัง"

#. module: event
#: model:event.event,subtitle:event.event_3
msgid "Experience live music, local food and beverages."
msgstr "สัมผัสประสบการณ์ดนตรีสด อาหารท้องถิ่น และเครื่องดื่ม"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_foldable_badge
msgid "Foldable Badge"
msgstr "ป้ายที่พับได้"

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid ""
"Foldable Badge - {{ (object.event_id.name or 'Event').replace('/','_') }}"
msgstr "ป้ายพับได้ - {{ (object.event_id.name or 'Event').replace('/','_') }}"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_foldable_badge
msgid "Foldable Badge Example"
msgstr "ตัวอย่างป้ายพับได้"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__fold
msgid "Folded in Kanban"
msgstr "พับในคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_follower_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_partner_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_type_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
#: model_terms:event.event,description:event.event_7
msgid "For any additional information, please contact us at"
msgstr "สำหรับข้อมูลเพิ่มเติม โปรดติดต่อเราที่"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""
"สำหรับแต่ละเหตุการณ์ คุณสามารถกำหนดการลงทะเบียนที่นั่งสูงสุด "
"(จำนวนผู้เข้าร่วม) เหนือตัวเลขนี้ การลงทะเบียนจะไม่ได้รับการยอมรับ"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_1
msgid "For only 10, you gain access to catering. Yum yum."
msgstr "เพียง 10 คุณสามารถเข้าถึงการจัดเลี้ยงได้ ยัม ยั่ม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid ""
"Foster interactions between attendees by creating virtual conference rooms"
msgstr ""
"ส่งเสริมปฏิสัมพันธ์ระหว่างผู้เข้าร่วมประชุมด้วยการสร้างห้องประชุมเสมือนจริง"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_0
msgid "Free"
msgstr "ฟรี"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_0
msgid "Free entrance, no food !"
msgstr "เข้าฟรี ไม่มีอาหาร !"

#. module: event
#: model:event.stage,description:event.event_stage_new
msgid "Freshly created"
msgstr "สร้างขึ้นใหม่"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_registration
msgid ""
"From this dashboard you can report, analyze and detect trends regarding your"
" event registrations."
msgstr ""
"จากแดชบอร์ดนี้ คุณสามารถรายงาน วิเคราะห์ "
"และตรวจจับแนวโน้มเกี่ยวกับการลงทะเบียนอีเมลของคุณได้"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_registration_full_page_ticket
msgid "Full Page Ticket"
msgstr "หน้าเต็มทิกเก็ต"

#. module: event
#: model:mail.template,report_name:event.event_subscription
msgid ""
"Full Page Ticket - {{ (object.event_id.name or 'Event').replace('/','') }}"
msgstr ""
"หน้าเต็มทิกเก็ต - {{ (object.event_id.name or 'Event').replace('/','') }}"

#. module: event
#: model:ir.actions.report,name:event.action_report_event_event_full_page_ticket
msgid "Full Page Ticket Example"
msgstr "ตัวอย่างหน้าเต็มทิกเก็ต"

#. module: event
#: model:event.stage,description:event.event_stage_done
msgid "Fully ended"
msgstr "สิ้นสุดอย่างสมบูรณ์"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr "โฟลว์การทำงานของแอปพลิเคชันหลัก;"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Future Activities"
msgstr "กิจกรรมในอนาคต"

#. module: event
#: model:event.event.ticket,name:event.event_4_ticket_0
msgid "General Admission"
msgstr "การสมัครทั่วไป"

#. module: event
#: model:event.event,subtitle:event.event_0
msgid "Get Inspired • Stay Connected • Have Fun"
msgstr "รับแรงบันดาลใจ • ไม่พลาดการติดต่อ • ขอให้สนุก"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_state
msgid "Global communication Status"
msgstr "สถานะการสื่อสารทั่วโลก"

#. module: event
#: model:event.event,name:event.event_1
msgid "Great Reno Ballon Race"
msgstr "การแข่งขันบอลลูนรีโนครั้งยิ่งใหญ่"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Great! Now all you have to do is wait for your attendees to show up!"
msgstr "ยอดเยี่ยม! ตอนนี้สิ่งที่คุณต้องทำคือรอให้ผู้เข้าร่วมแสดงตัว!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_done
msgid "Green Kanban Label"
msgstr "ป้ายคัมบังสีเขียว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "ป้ายคัมบังสีเทา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: event
#: model_terms:res.partner,website_description:event.res_partner_event_1
#: model_terms:res.partner,website_description:event.res_partner_event_2
#: model_terms:res.partner,website_description:event.res_partner_event_3
#: model_terms:res.partner,website_description:event.res_partner_event_4
msgid "Happy to be Sponsor"
msgstr "ยินดีที่ได้เป็นผู้สนับสนุน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__has_message
#: model:ir.model.fields,field_description:event.field_event_registration__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr "เมื่อเข้าร่วมการประชุมนี้ ผู้เข้าร่วมควรจะสามารถ:"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Here it is, the 12th edition of our Live Musical Festival !"
msgstr "มาแล้ว Live Musical Festival ครั้งที่ 12 !"

#. module: event
#: model:event.event,name:event.event_5
msgid "Hockey Tournament"
msgstr "การแข่งขันฮอกกี้"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__hours
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__hours
msgid "Hours"
msgstr "ชั่วโมง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (1)"
msgstr "วิธีการพับ (1)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (2)"
msgstr "วิธีการพับ (2)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (3)"
msgstr "วิธีการพับ (3)"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_report_template_foldable_badge
msgid "How to Fold (4)"
msgstr "วิธีการพับ (4)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__id
#: model:ir.model.fields,field_description:event.field_event_event_ticket__id
#: model:ir.model.fields,field_description:event.field_event_mail__id
#: model:ir.model.fields,field_description:event.field_event_mail_registration__id
#: model:ir.model.fields,field_description:event.field_event_registration__id
#: model:ir.model.fields,field_description:event.field_event_stage__id
#: model:ir.model.fields,field_description:event.field_event_tag__id
#: model:ir.model.fields,field_description:event.field_event_tag_category__id
#: model:ir.model.fields,field_description:event.field_event_type__id
#: model:ir.model.fields,field_description:event.field_event_type_mail__id
#: model:ir.model.fields,field_description:event.field_event_type_ticket__id
msgid "ID"
msgstr "ไอดี"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,field_description:event.field_event_registration__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_icon
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุกิจกรรมการยกเว้น"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction
#: model:ir.model.fields,help:event.field_event_event__message_unread
#: model:ir.model.fields,help:event.field_event_registration__message_needaction
#: model:ir.model.fields,help:event.field_event_registration__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error
#: model:ir.model.fields,help:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_error
#: model:ir.model.fields,help:event.field_event_registration__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__start_sale_datetime
msgid ""
"If ticketing is used, contains the earliest starting sale date of tickets."
msgstr "หากมีการใช้ทิกเก็ต จะมีวันที่เริ่มขายทิกเก็ตเร็วที่สุด"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "If you don't have this ticket, you will <b>not</b> be allowed entry!"
msgstr "หากคุณไม่มีทิกเก็ตนี้ คุณจะ <b>ไม่</b> สามารถเข้าได้!"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"If you don't know anything about Hockey, this is a great introduction to this wonderful sport as you will will be able to see some training process and also have some time\n"
"                to chat with experienced players and trainers once the tournament is over !"
msgstr ""
"หากคุณไม่รู้อะไรเกี่ยวกับฮอกกี้เลย นี่เป็นการแนะนำกีฬาที่ยอดเยี่ยมนี้เพราะคุณจะสามารถเห็นกระบวนการฝึกซ้อมและมีเวลาพอสมควร\n"
"                เพื่อสนทนากับผู้เล่นที่มีประสบการณ์และผู้ฝึกสอนเมื่อการแข่งขันจบลง !"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__now
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__now
msgid "Immediately"
msgstr "ทันที"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Important ticket information"
msgstr "ข้อมูลสำคัญของทิกเก็ต"

#. module: event
#: code:addons/event/models/event_stage.py:0
#: model:event.event,legend_normal:event.event_0
#: model:event.event,legend_normal:event.event_1
#: model:event.event,legend_normal:event.event_2
#: model:event.event,legend_normal:event.event_3
#: model:event.event,legend_normal:event.event_4
#: model:event.event,legend_normal:event.event_5
#: model:event.event,legend_normal:event.event_6
#: model:event.event,legend_normal:event.event_7
#: model:event.stage,legend_normal:event.event_stage_announced
#: model:event.stage,legend_normal:event.event_stage_booked
#: model:event.stage,legend_normal:event.event_stage_cancelled
#: model:event.stage,legend_normal:event.event_stage_done
#: model:event.stage,legend_normal:event.event_stage_new
#: model:ir.model.fields.selection,name:event.selection__event_event__kanban_state__normal
#, python-format
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_nbr
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_nbr
msgid "Interval"
msgstr "ช่วงเวลา"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr "บทนำ, CRM, การบริหารการขาย"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "Invalid event / ticket choice"
msgstr "การเลือกอีเวนต์ /ทิกเก็ตไม่ถูกต้อง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__sale_available
msgid "Is Available"
msgstr "พร้อม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__is_expired
msgid "Is Expired"
msgstr "สิ้นสุดแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_finished
msgid "Is Finished"
msgstr "เสร็จแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_is_follower
#: model:ir.model.fields,field_description:event.field_event_registration__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_one_day
msgid "Is One Day"
msgstr "เป็นหนึ่งวัน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "กำลังดำเนินการ"

#. module: event
#: model:ir.model.fields,help:event.field_event_type__seats_max
msgid "It will select this default maximum value when you choose this event"
msgstr "มันจะเลือกค่าสูงสุดเริ่มต้นนี้เมื่อคุณเลือกอีเวนต์นี้"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "John DOE"
msgstr "John DOE"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "Join us for the greatest ballon race of all times !"
msgstr "เข้าร่วมการแข่งขันบอลลูนครั้งยิ่งใหญ่กับเรา!"

#. module: event
#: model_terms:event.event,description:event.event_4
#: model_terms:event.event,description:event.event_6
#: model_terms:ir.ui.view,arch_db:event.event_default_descripton
msgid "Join us for this 24 hours Event"
msgstr "เข้าร่วมกับเราสำหรับอีเวนต์ 24 ชั่วโมงนี้"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "Join us for this 3-day Event"
msgstr "เข้าร่วมอีเวนต์ 3 วันนี้กับเรา"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "คำอธิบายบล็อกคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "คำอธิบายอย่างต่อเนื่องคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state
msgid "Kanban State"
msgstr "สถานะคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__kanban_state_label
msgid "Kanban State Label"
msgstr "ป้ายสถานะคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__legend_done
msgid "Kanban Valid Explanation"
msgstr "คำอธิบายที่ถูกต้องคัมบัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event____last_update
#: model:ir.model.fields,field_description:event.field_event_event_ticket____last_update
#: model:ir.model.fields,field_description:event.field_event_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_registration____last_update
#: model:ir.model.fields,field_description:event.field_event_stage____last_update
#: model:ir.model.fields,field_description:event.field_event_tag____last_update
#: model:ir.model.fields,field_description:event.field_event_tag_category____last_update
#: model:ir.model.fields,field_description:event.field_event_type____last_update
#: model:ir.model.fields,field_description:event.field_event_type_mail____last_update
#: model:ir.model.fields,field_description:event.field_event_type_ticket____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_uid
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_registration__write_uid
#: model:ir.model.fields,field_description:event.field_event_stage__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag__write_uid
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_uid
#: model:ir.model.fields,field_description:event.field_event_type__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_uid
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__write_date
#: model:ir.model.fields,field_description:event.field_event_event_ticket__write_date
#: model:ir.model.fields,field_description:event.field_event_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_registration__write_date
#: model:ir.model.fields,field_description:event.field_event_stage__write_date
#: model:ir.model.fields,field_description:event.field_event_tag__write_date
#: model:ir.model.fields,field_description:event.field_event_tag_category__write_date
#: model:ir.model.fields,field_description:event.field_event_type__write_date
#: model:ir.model.fields,field_description:event.field_event_type_mail__write_date
#: model:ir.model.fields,field_description:event.field_event_type_ticket__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Late Activities"
msgstr "กิจกรรมล่าสุด"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Let's create your first <b>event</b>."
msgstr "มาสร้าง <b>อีเวนต์</b>แรกกันเถอะ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Limit Registrations"
msgstr "จำกัดการลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__has_seats_limitation
msgid "Limited Seats"
msgstr "จำกัดที่นั้ง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Live Broadcast"
msgstr "ถ่ายทอดสด"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_live
msgid "Live Mode"
msgstr "โหมดไลฟ์"

#. module: event
#: model:event.event,name:event.event_3
msgid "Live Music Festival"
msgstr "เทศกาลดนตรีสด"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__notification_type__mail
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__notification_type__mail
msgid "Mail"
msgstr "เมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_registration_ids
msgid "Mail Registration"
msgstr "การลงทะเบียนเมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_mail_ids
#: model:ir.model.fields,field_description:event.field_event_type__event_type_mail_ids
msgid "Mail Schedule"
msgstr "กำหนดการเมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduler_id
msgid "Mail Scheduler"
msgstr "ตัวกำหนดการเมล"

#. module: event
#: model:ir.ui.menu,name:event.menu_event_mail_schedulers
msgid "Mail Schedulers"
msgstr "ตัวกำหนดการเมล"

#. module: event
#: model:ir.model,name:event.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "การกำหนดการเมลบนหมวดหมู่อีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__mail_sent
msgid "Mail Sent"
msgstr "ส่งเมลแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_main_attachment_id
#: model:ir.model.fields,field_description:event.field_event_registration__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Manage &amp; publish a schedule with tracks"
msgstr "จัดการและเผยแพร่กำหนดการพร้อมการติดตาม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Mark as Attending"
msgstr "ทำเครื่องหมายว่าเข้าร่วม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Marketing"
msgstr "การตลาด"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
msgid "Maximum"
msgstr "สูงสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_limited
msgid "Maximum Attendees"
msgstr "ผู้เข้าร่วมสูงสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_max
msgid "Maximum Attendees Number"
msgstr "จำนวนผู้เข้าร่วมสูงสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__seats_max
msgid "Maximum Registrations"
msgstr "การลงทะเบียนสูงสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_max
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_max
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Maximum Seats"
msgstr "ที่นั้งสูงสุด"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "สื่อกลาง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__mobile
msgid "Mobile"
msgstr "มือถือ"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__months
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__months
msgid "Months"
msgstr "เดือน"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_2
msgid "Music"
msgstr "ดนตรี"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__my_activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "อีเวนต์ของฉัน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__name
#: model:ir.model.fields,field_description:event.field_event_tag__name
#: model:ir.model.fields,field_description:event.field_event_tag_category__name
#: model:ir.model.fields,field_description:event.field_event_type_ticket__name
msgid "Name"
msgstr "ชื่อ"

#. module: event
#: model:event.stage,name:event.event_stage_new
msgid "New"
msgstr "ใหม่"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_calendar_event_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_date_deadline
#: model:ir.model.fields,field_description:event.field_event_registration__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_summary
#: model:ir.model.fields,field_description:event.field_event_registration__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_type_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid "No Attendees expected yet!"
msgstr "ยังไม่มีผู้เข้าร่วมที่คาดหวัง!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.action_registration
msgid "No Attendees yet!"
msgstr "ยังไม่มีผู้เข้าร่วม!"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_event_action_pivot
msgid "No Event data yet!"
msgstr "ยังไม่มีข้อมูลอีเวนต์!"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "No more available seats for this ticket"
msgstr "ไม่มีที่นั่งว่างสำหรับทิกเก็ตนี้แล้ว"

#. module: event
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "No more available seats for this ticket."
msgstr "ไม่มีที่นั่งว่างสำหรับทิกเก็ตนี้"

#. module: event
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "No more available seats."
msgstr "ไม่มีที่นั่งว่างแล้ว"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "No more seats available for this event."
msgstr "ไม่มีที่นั่งว่างสำหรับอีเวนต์นี้"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "None"
msgstr "ไม่มี"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__note
#: model:ir.model.fields,field_description:event.field_event_type__note
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Note"
msgstr "โน้ต"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Notes"
msgstr "โน้ต"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid "Nothing Scheduled yet!"
msgstr "ยังไม่มีกำหนดการใด ๆ !"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Now that your event is ready, click here to move it to another stage."
msgstr "เมื่ออีเวนต์ของคุณพร้อมแล้ว คลิกที่นี่เพื่อย้ายไปยังอีกขั้นตอน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_expected
msgid "Number of Expected Attendees"
msgstr "จำนวนผู้เข้าร่วมที่คาดหวัง"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_used
msgid "Number of Participants"
msgstr "จำนวนผู้เข้าร่วม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: event
#: model:ir.model.fields,help:event.field_res_partner__event_count
#: model:ir.model.fields,help:event.field_res_users__event_count
msgid "Number of events the partner has participated."
msgstr "จำนวนอีเวนต์ที่พาร์ทเนอร์ได้เข้าร่วม"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_needaction_counter
#: model:ir.model.fields,help:event.field_event_registration__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_has_error_counter
#: model:ir.model.fields,help:event.field_event_registration__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__message_unread_counter
#: model:ir.model.fields,help:event.field_event_registration__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Objectives"
msgstr "วัตถุประสงค์"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "Once again we assembled the most legendary bands in Rock history."
msgstr "เป็นอีกครั้งที่เรารวบรวมวงดนตรีที่เป็นตำนานที่สุดในประวัติศาสตร์ร็อค"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ongoing Events"
msgstr "อีเวนต์ต่อเนื่อง"

#. module: event
#: model:event.tag,name:event.event_tag_category_3_tag_1
msgid "Online"
msgstr "ออนไลน์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Online Exhibitors"
msgstr "ผู้แสดงสินค้าออนไลน์"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_sale
msgid "Online Ticketing"
msgstr "จองทิกเก็ตออนไลน์"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"OpenElec Applications reserves the right to cancel, re-name or re-locate the"
" event or change the dates on which it is held."
msgstr ""
"แอปพลิเคชัน OpenElec ขอสงวนสิทธิ์ในการยกเลิก เปลี่ยนชื่อ "
"หรือค้นหาสถานที่ใหม่ หรือเปลี่ยนวันที่จัดงาน"

#. module: event
#: model:event.event,name:event.event_7
msgid "OpenWood Collection Online Reveal"
msgstr "OpenWood Collection Online Reveal"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_2
msgid ""
"OpenWood brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"OpenWood นำความซื่อสัตย์และจริงจังมาสู่อุตสาหกรรมไม้ "
"ในขณะเดียวกันก็ช่วยลูกค้าจัดการกับต้นไม้ ดอกไม้ และเชื้อรา"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__organizer_id
msgid "Organizer"
msgstr "ผู้จัด"

#. module: event
#: model:event.event,subtitle:event.event_7
msgid ""
"Our newest collection will be revealed online! Interact with us on our live "
"streams!"
msgstr ""
"คอลเลกชันใหม่ล่าสุดของเราจะถูกเปิดเผยทางออนไลน์! "
"โต้ตอบกับเราในสตรีมสดของเรา!"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_blocked
#: model:ir.model.fields,help:event.field_event_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection."
msgstr "แทนที่ค่าเริ่มต้นที่แสดงของสถานะที่ถูกบล็อกสำหรับการเลือกคัมบัง"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_done
#: model:ir.model.fields,help:event.field_event_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection."
msgstr "แทนที่ค่าเริ่มต้นที่แสดงของสถานะเสร็จสิ้นสำหรับการเลือกคัมบัง"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__legend_normal
#: model:ir.model.fields,help:event.field_event_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection."
msgstr "แทนที่ค่าเริ่มต้นที่แสดงของสถานะปกติสำหรับการเลือกคัมบัง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr "ผู้มีส่วนร่วม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid ""
"Please come <b>at least</b> 30 minutes before the beginning of the event."
msgstr "โปรดมา<b>อย่างน้อย</b> 30 นาทีก่อนเริ่มอีเวนต์"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr "การขายหน้าร้าน (POS) ข้อมูลเบื้องต้นเกี่ยวกับการปรับแต่งรายงาน"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_2
msgid "Program"
msgstr "โปรแกรม"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr "การจัดการโปรเจกต์ ทรัพยากรบุคคล การจัดการสัญญา"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr "การจัดซื้อ การขาย และการบริหารการจัดซื้อ บัญชีการเงิน"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track_quiz
msgid "Quiz on Tracks"
msgstr "แบบทดสอบบนแทร็ก"

#. module: event
#: code:addons/event/models/event_stage.py:0
#: model:event.event,legend_done:event.event_0
#: model:event.event,legend_done:event.event_1
#: model:event.event,legend_done:event.event_2
#: model:event.event,legend_done:event.event_3
#: model:event.event,legend_done:event.event_4
#: model:event.event,legend_done:event.event_5
#: model:event.event,legend_done:event.event_6
#: model:event.event,legend_done:event.event_7
#: model:event.stage,legend_done:event.event_stage_announced
#: model:event.stage,legend_done:event.event_stage_booked
#: model:event.stage,legend_done:event.event_stage_cancelled
#: model:event.stage,legend_done:event.event_stage_done
#: model:event.stage,legend_done:event.event_stage_new
#, python-format
msgid "Ready for Next Stage"
msgstr "เตรียมพร้อมสำหรับขั้นตอนถัดไป"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Ready to <b>organize events</b> in a few minutes? Let's get started!"
msgstr "พร้อม<b>จัดการอีเวนต์</b> ในไม่กี่นาที? มาเริ่มกันเลย!"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "ป้ายคัมบังสีแดง"

#. module: event
#: code:addons/event/models/event_ticket.py:0
#: code:addons/event/models/event_ticket.py:0
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#, python-format
msgid "Registration"
msgstr "การลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__date_open
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Date"
msgstr "วันที่การลงทะเบียน"

#. module: event
#: model:res.groups,name:event.group_event_registration_desk
msgid "Registration Desk"
msgstr "โต๊ะลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__end_sale_datetime
msgid "Registration End"
msgstr "สิ้นสุดการลงทะเบียน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr "ไอดีการลงทะเบียน"

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "ตัวกำหนดเวลาการลงทะเบียนอีเมล"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr "การลงทะเบียนเมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__start_sale_datetime
msgid "Registration Start"
msgstr "เริ่มการลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_questions
msgid "Registration Survey"
msgstr "แบบทดสอบการลงทะเบียน"

#. module: event
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "Registration for %s"
msgstr "การลงทะเบียนสำหรับ %s"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr "เมลการลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_open
msgid "Registration open"
msgstr "เปิดการลงทะเบียน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__registration_ids
msgid "Registrations"
msgstr "การลงทะเบียน"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_open
msgid ""
"Registrations are open if:\n"
"- the event is not ended\n"
"- there are seats available on event\n"
"- the tickets are sellable (if ticketing is used)"
msgstr ""
"เปิดการลงทะเบียนถ้า:\n"
"- อีเวนต์ยังไม่จบ\n"
"- มีที่นั่งว่างในอีเวนต์\n"
"- ทิกเก็ตสามารถขายได้ (หากใช้ทิกเก็ต)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_started
msgid "Registrations started"
msgstr "เริ่มลงทะเบียน"

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reporting"
msgstr "การรายงาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_reserved
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_reserved
msgid "Reserved Seats"
msgstr "สำรแงที่นั้งแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__activity_user_id
#: model:ir.model.fields,field_description:event.field_event_registration__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__running
msgid "Running"
msgstr "กำลังทำงาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_has_sms_error
#: model:ir.model.fields,field_description:event.field_event_registration__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Scan badges to confirm attendances"
msgstr "สแกนป้ายเพื่อยืนยันการเข้าร่วม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Schedule & Tracks"
msgstr "กำหนดการและแทร็ก"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__scheduled_date
msgid "Schedule Date"
msgstr "วันที่ตามกำหนดการ"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Schedule and organize your events: handle registrations, send automated "
"confirmation emails, sell tickets, etc."
msgstr ""
"กำหนดเวลาและจัดการอีเวนต์ของคุณ: จัดการการลงทะเบียน ส่งอีเมลยืนยันอัตโนมัติ "
"ขายทิกเก็ตและอื่น ๆ "

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__scheduled
msgid "Scheduled"
msgstr "กำหนดแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration__scheduled_date
msgid "Scheduled Time"
msgstr "วันที่ตามกำหนดการ"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid "Seasoned Hockey Fans and curious people, this tournament is for you !"
msgstr ""
"แฟนฮอกกี้ผู้มากประสบการณ์และผู้คนที่อยากรู้อยากเห็น "
"ทัวร์นาเมนต์นี้เหมาะสำหรับคุณ !"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_limited
#: model:ir.model.fields,field_description:event.field_event_type_ticket__seats_limited
msgid "Seats Limit"
msgstr "จำกัดที่นั่ง"

#. module: event
#: model:event.type,name:event.event_type_data_ticket
msgid "Sell Online"
msgstr "ขายออนไลน์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets on your website"
msgstr "ขายทิกเก็ตบนเว็บไซต์ของคุณ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.res_config_settings_view_form
msgid "Sell tickets with sales orders"
msgstr "ขายทิกเก็ตพร้อมคำสั่งขาย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event.field_event_type_mail__notification_type
msgid "Send"
msgstr "ส่ง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr "ส่งทางอีเมล"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__mail_done
#: model:ir.model.fields.selection,name:event.selection__event_mail__mail_state__sent
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__sequence
#: model:ir.model.fields,field_description:event.field_event_tag__sequence
#: model:ir.model.fields,field_description:event.field_event_tag_category__sequence
#: model:ir.model.fields,field_description:event.field_event_type__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr "ตั้งค่าเป็นไม่ได้รับการยืนยัน"

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr "การตั้งค่า"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_4
msgid ""
"Shangai Pterocarpus Furniture brings honesty and seriousness to wood "
"industry while helping customers deal with trees, flowers and fungi."
msgstr ""
"Shangai Pterocarpus Furniture นำความจริงใจและความจริงจังมาสู่อุตสาหกรรมไม้ "
"ในขณะเดียวกันก็ช่วยลูกค้าจัดการกับต้นไม้ ดอกไม้ และเชื้อรา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Show all records which has next action date is before today"
msgstr "แสดงระเบียนทั้งหมดที่มีวันที่ดำเนินการถัดไปคือก่อนวันนี้"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_registrations_sold_out
msgid "Sold Out"
msgstr "ขายหมดแล้ว"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__utm_source_id
msgid "Source"
msgstr "แหล่งที่มา"

#. module: event
#: model:event.tag,name:event.event_tag_category_2_tag_3
#: model:event.type,name:event.event_type_2
msgid "Sport"
msgstr "กีฬา"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__stage_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Stage"
msgstr "ขั้นตอน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "คำอธิบายขั้นตอนและทูลทิป"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__name
msgid "Stage Name"
msgstr "ชื่อขั้นตอน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_stage__description
msgid "Stage description"
msgstr "คำอธิบายขั้นตอน"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_1
#: model:event.event.ticket,name:event.event_2_ticket_1
#: model:event.event.ticket,name:event.event_3_ticket_0
#: model:event.event.ticket,name:event.event_7_ticket_1
msgid "Standard"
msgstr "มาตรฐาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_begin_located
msgid "Start Date Located"
msgstr "วันที่เริ่มต้นตั้งอยู่"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__start_sale_datetime
msgid "Start sale date"
msgstr "วันที่เริ่มต้นการขาย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration__state
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "สถานะ"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_state
#: model:ir.model.fields,help:event.field_event_registration__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: event
#: model:ir.model.fields,help:event.field_event_tag__color
msgid ""
"Tag color. No color means no display in kanban or front-end, to distinguish "
"internal tags from public categorization tags."
msgstr ""
"แท็กสี ไม่มีสีหมายความว่าไม่มีการแสดงในคัมบังหรือส่วนหน้า "
"เพื่อแยกแท็กภายในออกจากแท็กการจัดหมวดหมู่สาธารณะ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__tag_ids
#: model:ir.model.fields,field_description:event.field_event_tag_category__tag_ids
#: model:ir.model.fields,field_description:event.field_event_type__tag_ids
#: model_terms:ir.ui.view,arch_db:event.event_tag_category_view_form
msgid "Tags"
msgstr "แท็ก"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "งานอยู่ระหว่างดำเนินการ คลิกเพื่อบล็อกหรือตั้งเป็นเสร็จสิ้น"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "งานถูกบล็อก คลิกเพื่อเลิกบล็อกหรือตั้งเป็นเสร็จสิ้น"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__event_type_id
#: model:ir.model.fields,field_description:event.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_ref
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Template"
msgstr "เทมเพลต"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__template_model_id
#: model:ir.model.fields,field_description:event.field_event_type_mail__template_model_id
msgid "Template Model"
msgstr "โมเดลเทมเพลต"

#. module: event
#: model:event.event,subtitle:event.event_1
msgid ""
"The Great Reno Balloon Race is the world's largest free hot-air ballooning "
"event."
msgstr "Great Reno Balloon Race เป็นงานบอลลูนลมร้อนฟรีที่ใหญ่ที่สุดในโลก"

#. module: event
#: model_terms:event.event,description:event.event_5
msgid ""
"The best Hockey teams of the country will compete for the national Hockey "
"trophy."
msgstr ""
"ทีมฮอกกี้ที่ดีที่สุดของประเทศจะแข่งขันเพื่อชิงถ้วยรางวัลฮอกกี้ระดับชาติ"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"The best aeronauts of the world will gather on this event to offer you the "
"most spectacular show."
msgstr ""
"นักบินที่เก่งที่สุดของโลกจะมารวมตัวกันที่งานนี้เพื่อมอบการแสดงที่น่าตื่นตาตื่นใจที่สุดให้กับคุณ"

#. module: event
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "The closing date cannot be earlier than the beginning date."
msgstr "วันที่ปิดต้องไม่เร็วกว่าวันที่เริ่มต้น"

#. module: event
#: model:event.stage,description:event.event_stage_cancelled
msgid "The event has been cancelled"
msgstr "ยกเลิกการจัดอีเวนต์แล้ว"

#. module: event
#: model:event.stage,description:event.event_stage_announced
msgid "The event has been publicly announced"
msgstr "ประกาศอีเวนต์เรียบร้อยแล้ว"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_sold_out
msgid ""
"The event is sold out if no more seats are available on event. If ticketing "
"is used and all tickets are sold out, the event will be sold out."
msgstr ""
"งานนี้ขายหมดหากไม่มีที่นั่งว่างในงาน หากใช้ตั๋วและบัตรขายหมด งานจะถูกขายหมด"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"The finest OpenWood furnitures are coming to your house in a brand new "
"collection"
msgstr ""
"เฟอร์นิเจอร์ OpenWood ที่ดีที่สุดกำลังมาที่บ้านของคุณในคอลเลกชั่นใหม่ล่าสุด"

#. module: event
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid ""
"The following tickets cannot be deleted while they have one or more registrations linked to them:\n"
"- %s"
msgstr ""
"ทิกเก็ตต่อไปนี้ไม่สามารถลบได้ในขณะที่มีการลงทะเบียนอย่างน้อยหนึ่งรายการที่เชื่อมโยงกับพวกเขา:\n"
"- %s"

#. module: event
#: model:event.stage,description:event.event_stage_booked
msgid "The place has been reserved"
msgstr "ได้จองที่แล้ว"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "The safety of our attendees and our aeronauts comes first !"
msgstr "ความปลอดภัยของผู้เข้าร่วมงานและนักบินอวกาศต้องมาก่อน !"

#. module: event
#: code:addons/event/models/event_ticket.py:0
#, python-format
msgid "The stop date cannot be earlier than the start date."
msgstr "วันที่หยุดต้องไม่เร็วกว่าวันที่เริ่มต้น"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid ""
"The template which is referenced should be coming from %(model_name)s model."
msgstr "เทมเพลตที่อ้างอิงควรมาจากโมเดล %(model_name)s"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid ""
"This event is also an opportunity to showcase our partners' case studies, "
"methodology or developments. Be there and see directly from the source the "
"features of the version 12!"
msgstr ""
"งานนี้เป็นโอกาสในการแสดงกรณีศึกษา วิธีการ หรือการพัฒนาของพันธมิตรของเรา "
"อยู่ที่นั่นและดูโดยตรงจากแหล่งที่มาของคุณสมบัติของเวอร์ชัน 12!"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid ""
"This event is fully online and FREE, if you have paid for tickets, you should get a refund.<br>\n"
"        It will require a good Internet connection to get the best video quality."
msgstr ""
"อีเวนต์นี้ในรูปแบบออนไลน์และฟรี หากคุณได้ชำระค่าทิกเก็ตแล้ว คุณควรจะได้รับเงินคืน<br>\n"
"       จะต้องมีการเชื่อมต่ออินเตอร์เน็ตที่ดีเพื่อให้ได้คุณภาพวิดีโอที่ดีที่สุด"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__ticket_instructions
#: model:ir.model.fields,help:event.field_event_type__ticket_instructions
msgid "This information will be printed on your tickets."
msgstr "ข้อมูลนี้จะถูกพิมพ์ลงบนทิกเก็ตของคุณ"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This is the <b>name</b> your guests will see when registering."
msgstr "นี่คือ <b>ชื่อ</b> ที่แขกของคุณจะเห็นเมื่อลงทะเบียน"

#. module: event
#: model_terms:event.event,description:event.event_1
msgid ""
"This is the perfect place for spending a nice day with your family, we "
"guarantee you will be leaving with beautiful everlasting memories !"
msgstr ""
"นี่คือสถานที่ที่สมบูรณ์แบบสำหรับการใช้เวลาดีๆ กับครอบครัวของคุณ "
"เรารับประกันว่าคุณจะต้องจากไปพร้อมกับความทรงจำที่สวยงามตลอดไป !"

#. module: event
#: model_terms:event.event,description:event.event_3
msgid ""
"This is the perfect place for spending a nice time with your friends while "
"listening to some of the most iconic rock songs of all times!"
msgstr ""
"ที่นี่เป็นสถานที่ที่เหมาะสำหรับการใช้เวลาดีๆ กับเพื่อน ๆ "
"ของคุณในขณะที่ฟังเพลงร็อคที่โด่งดังที่สุดตลอดกาล!"

#. module: event
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "This operator is not supported"
msgstr "ไม่รองรับผู้ปฏิบัติการนี้"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "ขั้นตอนนี้เสร็จสิ้น คลิกเพื่อบล็อกหรือตั้งค่าอยู่ระหว่างดำเนินการ"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_form_from_event
msgid "Ticket"
msgstr "ทิกเก็ต"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Ticket Extra Instructions"
msgstr "คำแนะนำพิเศษทิกเก็ต"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__ticket_instructions
#: model:ir.model.fields,field_description:event.field_event_type__ticket_instructions
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Ticket Instructions"
msgstr "คำแนะนำทิกเก็ต"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Ticket Type"
msgstr "ประเภททิกเก็ต"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Ticket types allow you to distinguish your attendees. Let's <b>create</b> a "
"new one."
msgstr "ประเภททิกเก็ตช่วยให้คุณแยกแยะผู้เข้าร่วมได้ มา<b>สร้าง</b> ใหม่กัน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type__event_type_ticket_ids
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_event_sale
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "Tickets"
msgstr "ทิกเก็ต"

#. module: event
#: model_terms:event.event,ticket_instructions:event.event_0
msgid "Tickets can be printed or scanned directly from your phone."
msgstr "สามารถพิมพ์หรือสแกนทิกเก็ตได้โดยตรงจากโทรศัพท์ของคุณ"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__date_tz
#: model:ir.model.fields,field_description:event.field_event_type__default_timezone
msgid "Timezone"
msgstr "เขตเวลา"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Today Activities"
msgstr "กิจกรรมวันนี้"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Total"
msgstr "รวม"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Total Registrations for this Event"
msgstr "การลงทะเบียนทั้งหมดสำหรับอีเวนต์นี้"

#. module: event
#: model:ir.model.fields,field_description:event.field_res_config_settings__module_website_event_track
msgid "Tracks and Agenda"
msgstr "แทร็กและวาระ"

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Training"
msgstr "การอบรม"

#. module: event
#: model:res.partner,website_short_description:event.res_partner_event_3
msgid ""
"Tree Dealers brings honesty and seriousness to wood industry while helping "
"customers deal with trees, flowers and fungi."
msgstr ""
"ผู้ค้าต้นไม้นำความจริงใจและความจริงจังมาสู่อุตสาหกรรมไม้ "
"ในขณะเดียวกันก็ช่วยลูกค้าจัดการกับต้นไม้ ดอกไม้ และเชื้อรา"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_type
msgid "Trigger"
msgstr "เปิดใช้งาน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_type
msgid "Trigger "
msgstr "เปิดใช้งาน"

#. module: event
#: model:event.tag.category,name:event.event_tag_category_3
msgid "Type"
msgstr "ประเภท"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__activity_exception_decoration
#: model:ir.model.fields,help:event.field_event_registration__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "พิมพ์กิจกรรมข้อยกเว้นบนบันทึก"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_registration__state__draft
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unconfirmed"
msgstr "ไม่มีการยืนยัน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr "การสำรองที่นั่งที่ไม่ได้รับการยืนยัน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_unconfirmed
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Unconfirmed Seats"
msgstr "ที่นั่งที่ไม่ได้รับการยืนยัน"

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_mail
msgid ""
"Under this technical menu you will find all scheduled communication related "
"to your events."
msgstr ""
"ภายใต้เมนูทางเทคนิคนี้ "
"คุณจะพบการสื่อสารตามกำหนดเวลาทั้งหมดที่เกี่ยวข้องกับกิจกรรมของคุณ"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr "ทำความเข้าใจกับโมดูลต่างๆ;"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail__interval_unit
#: model:ir.model.fields,field_description:event.field_event_type_mail__interval_unit
msgid "Unit"
msgstr "หน่วย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__message_unread_counter
#: model:ir.model.fields,field_description:event.field_event_registration__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr "อีเวนต์ที่จะเกิดขึ้นตั้งแต่วันนี้"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming/Running"
msgstr "กำลังจะเกิดขึ้น/กำลังดำเนินการ"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_tag_category_action_tree
msgid "Use Event Tag Categories to classify and organize your event tags."
msgstr "ใช้หมวดหมู่แท็กอีเวนต์เพื่อจัดประเภทและจัดระเบียบแท็กอีเวนต์ของคุณ"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use the <b>breadcrumbs</b> to go back to your kanban overview."
msgstr "ใช้<b>breadcrumbs</b>เพื่อกลับไปที่ภาพรวมคัมบังของคุณ"

#. module: event
#: model_terms:ir.actions.act_window,help:event.event_event_action_pivot
msgid "Use this report to compare or aggregate event performances."
msgstr "ใช้รายงานนี้เพื่อเปรียบเทียบหรือรวมประสิทธิภาพของอีเวนต์"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_ticket__seats_used
msgid "Used Seats"
msgstr "ใช้ที่นั่ง"

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: event
#: model:event.event.ticket,name:event.event_0_ticket_2
#: model:event.event.ticket,name:event.event_2_ticket_2
#: model:event.event.ticket,name:event.event_3_ticket_1
#: model:event.event.ticket,name:event.event_7_ticket_2
msgid "VIP"
msgstr "VIP"

#. module: event
#: code:addons/event/models/event_event.py:0
#: code:addons/event/models/event_event.py:0
#, python-format
msgid "Value should be True or False (not %s)"
msgstr "ค่าควรเป็นจริงหรือเท็จ (ไม่ใช่ %s)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__address_id
msgid "Venue"
msgstr "สถานที่"

#. module: event
#: code:addons/event/models/event_mail.py:0
#, python-format
msgid "WARNING: Event Scheduler Error for event: %s"
msgstr "คำเตือน: ข้อผิดพลาดตัวกำหนดการอีเวนต์สำหรับอีเวนต์: %s"

#. module: event
#: model_terms:ir.actions.act_window,help:event.act_event_registration_from_event
#: model_terms:ir.actions.act_window,help:event.event_registration_action
msgid ""
"Wait until Attendees register to your Event or create their registrations "
"manually."
msgstr ""
"รอจนกว่าผู้เข้าร่วมจะลงทะเบียนเข้าร่วมอีเวนต์ของคุณหรือสร้างการลงทะเบียนด้วยตนเอง"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr "การจัดการคลังสินค้า การผลิต (MRP) และ การจำหน่าย นำเข้า/ส่งออก"

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid ""
"We reserve the right to cancel, re-name or re-locate the event or change the"
" dates on which it is held in case the weather fails us."
msgstr ""
"เราขอสงวนสิทธิ์ในการยกเลิก เปลี่ยนชื่อ หรือค้นหาสถานที่ใหม่ "
"หรือเปลี่ยนวันที่จัดงานในกรณีที่สภาพอากาศไม่เอื้ออำนวย"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event__website_message_ids
#: model:ir.model.fields,field_description:event.field_event_registration__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__website_message_ids
#: model:ir.model.fields,help:event.field_event_registration__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: event
#: model:ir.model.fields.selection,name:event.selection__event_mail__interval_unit__weeks
#: model:ir.model.fields.selection,name:event.selection__event_type_mail__interval_unit__weeks
msgid "Weeks"
msgstr "สัปดาห์"

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "What's new?"
msgstr "มีอะไรใหม่บ้าง?"

#. module: event
#. openerp-web
#: code:addons/event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"When will your event take place? <b>Select</b> the start and end dates "
"<b>and click Apply</b>."
msgstr ""
"อีเวนต์ของคุณจะจัดขึ้นเมื่อไหร่? <b>เลือก</b>วันที่เริ่มต้นและสิ้นสุด "
"แล้วคลิกเพื่อนำไปใช้"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "อีเวนต์ได้เริ่มต้นขึ้นแล้วหรือยัง"

#. module: event
#: model:event.event.ticket,description:event.event_0_ticket_2
msgid "You are truly among the best."
msgstr "คุณเป็นคนที่ดีที่สุดอย่างแท้จริง"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"คุณยังสามารถเพิ่มคำอธิบายเพื่อช่วยให้เพื่อนร่วมงานของคุณเข้าใจความหมายและวัตถุประสงค์ของขั้นตอน"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels in the kanban view."
msgstr ""
"คุณสามารถกำหนดป้ายกำกับที่จะแสดงสำหรับสถานะที่นี่แทน\n"
"                            ของป้ายเริ่มต้นในมุมมองคัมบัง"

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for {{ object.event_id.name }}"
msgstr "ป้ายของคุณสำหรับ {{ object.event_id.name }}"

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your registration at {{ object.event_id.name }}"
msgstr "การลงทะเบียนของคุณที่ {{ object.event_id.name }}"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "e.g. Conference for Architects"
msgstr "เช่น การประชุมสถาปนิก"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "e.g. Online Conferences"
msgstr "เช่น การประชุมออนไลน์"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_form_view
msgid "e.g. VIP Ticket"
msgstr "เช่น ทิกเก็ต VIP"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_7
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
#: model_terms:event.event,description:event.event_5
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "in %d days"
msgstr "ใน %d วัน"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next month"
msgstr "เดือนต่อไป"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "next week"
msgstr "สัปดาห์ต่อไป"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "on %(date)s"
msgstr "เมื่อ %(date)s"

#. module: event
#: model:ir.model.fields,help:event.field_event_event__event_registrations_started
msgid ""
"registrations have started if the current datetime is after the earliest "
"starting date of tickets."
msgstr ""
"การลงทะเบียนเริ่มต้นขึ้นหากวันที่เวลาปัจจุบันอยู่หลังวันที่เริ่มต้นตั๋วเร็วที่สุด"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "reserved +"
msgstr "จองแล้ว +"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
msgid "to"
msgstr "ถึง"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "today"
msgstr "วันนี้"

#. module: event
#: code:addons/event/models/event_registration.py:0
#, python-format
msgid "tomorrow"
msgstr "พรุ่งนี้"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_ticket_view_kanban_from_event
msgid "unconfirmed"
msgstr "ไม่ได้รับการยืนยัน"

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "{{ object.event_id.name }}: {{ object.get_date_range_str() }}"
msgstr "{{ object.event_id.name }}: {{ object.get_date_range_str() }}"
