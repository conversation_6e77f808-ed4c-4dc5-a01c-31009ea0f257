<?xml version="1.0"?>
<odoo>

    <record id="product_supplierinfo_tree_view_inherit" model="ir.ui.view">
        <field name="name">product.template.product.form.inherit</field>
        <field name="model">product.supplierinfo</field>
        <field name="inherit_id" ref="product.product_supplierinfo_tree_view"/>
        <field name="arch" type="xml">
          <xpath expr="//field[@name='product_id']" position="after">
              <field name="purchase_requisition_id" optional="hide"/>
          </xpath>
        </field>
    </record>

    <record id="supplier_info_form_inherit" model="ir.ui.view">
        <field name="name">product.supplierinfo.requisition.view</field>
        <field name="model">product.supplierinfo</field>
        <field name="priority">20</field>
        <field name="inherit_id" ref="product.product_supplierinfo_form_view"/>
        <field name="arch" type="xml">
            <field name="product_code" position="after">
                <field name="purchase_requisition_id"
                    attrs="{'invisible': [('purchase_requisition_id', '=', False)]}"/>
            </field>
        </field>
    </record>

    <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="purchase.view_product_supplier_inherit"/>
        <field name="arch" type="xml">
            <group name="bill" position="before">
                <group string="Reordering" attrs="{'invisible': [('type', '=', 'service')]}">
                    <field name="purchase_requisition" widget="radio"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
