# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_stripe
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2022
# Jonatan Gk, 2022
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 15:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Connect Stripe"
msgstr "Conectar a Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr "No se ha podido establecer la conexión con el API."

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Generate your webhook"
msgstr "Genera tu webhook"

#. module: payment_stripe
#: model_terms:ir.ui.view,arch_db:payment_stripe.payment_acquirer_form
msgid "Get your Secret and Publishable keys"
msgstr "Obtenga sus claves secreta y pública"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid ""
"If a webhook is enabled on your Stripe account, this signing secret must be "
"set to authenticate the messages sent from Stripe to Odoo."
msgstr ""
"Si cuenta con un webhook habilitado en su cuenta de Stripe, debe "
"configurarlo para que autentifique los mensajes enviados desde Stripe a "
"Odoo."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""
"No se ha encontrado ninguna transacción que coincida con la referencia %s."

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Método de pago"

#. module: payment_stripe
#: model:ir.actions.act_window,name:payment_stripe.action_payment_acquirer_onboarding
msgid "Payment Acquirers"
msgstr "Métodos de pago"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_token
msgid "Payment Token"
msgstr "Token de pago"

#. module: payment_stripe
#: model:ir.model,name:payment_stripe.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacción de pago"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveedor"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "Publishable Key"
msgstr "Clave pública"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid intent status: %s"
msgstr "Datos recibidos con estado de intento no válido: %s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing intent status."
msgstr "Datos recibidos sin estado de intento."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr "Datos recibidos en los que falta la referencia del vendedor"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_secret_key
msgid "Secret Key"
msgstr "Clave secreta"

#. module: payment_stripe
#: model:account.payment.method,name:payment_stripe.payment_method_stripe
#: model:ir.model.fields.selection,name:payment_stripe.selection__payment_acquirer__provider__stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_transaction__stripe_payment_intent
msgid "Stripe Payment Intent ID"
msgstr "ID de intento de pago por Stripe"

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_token__stripe_payment_method
msgid "Stripe Payment Method ID"
msgstr "ID del método de pago de Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy error: %(error)s"
msgstr "Stripe proxy error: %(error)s"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: An error occurred when communicating with the proxy."
msgstr "Stripe Proxy: se produjo un error al comunicarse con el proxy."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Stripe Proxy: Could not establish the connection."
msgstr "Stripe Proxy: no se pudo establecer la conexión."

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "El proveedor de servicios de pago a utilizar con este adquirente"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid ""
"The communication with the API failed.\n"
"Stripe gave us the following info about the problem:\n"
"'%s'"
msgstr ""
"La comunicación con la API ha fallado.\n"
"Stripe nos dio la siguiente información sobre el problema:\n"
"'%s'"

#. module: payment_stripe
#: model:ir.model.fields,help:payment_stripe.field_payment_acquirer__stripe_publishable_key
msgid "The key solely used to identify the account with Stripe"
msgstr ""
"La clave que se utiliza exclusivamente para identificar la cuenta con Stripe"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "La transacción no está vinculada a un token."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_token.py:0
#, python-format
msgid "Unable to convert payment token to new API."
msgstr "No se puede convertir el token de pago a la nueva API."

#. module: payment_stripe
#: model:ir.model.fields,field_description:payment_stripe.field_payment_acquirer__stripe_webhook_secret
msgid "Webhook Signing Secret"
msgstr "Secreto de webhook"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "You Stripe Webhook was successfully set up!"
msgstr "¡Tu webhook con Stripe se configuró correctamente!"

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot create a Stripe Webhook if your Stripe Secret Key is not set."
msgstr ""
"No puede crear un webhook de Stripe si su clave secreta de Stripe no está "
"configurada."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer state to Enabled until your onboarding to Stripe"
" is completed."
msgstr ""

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid ""
"You cannot set the acquirer to Test Mode while it is linked with your Stripe"
" account."
msgstr ""
"No puede configurar el método de pago en modo de prueba mientras esté "
"vinculado con su cuenta de Stripe."

#. module: payment_stripe
#: code:addons/payment_stripe/models/payment_acquirer.py:0
#, python-format
msgid "Your Stripe Webhook is already set up."
msgstr "Tu webhook de Stripe ya está configurado."
