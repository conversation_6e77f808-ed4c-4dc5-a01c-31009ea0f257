# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sms
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:35+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Cancel"
msgstr "Odustani"

#. module: sms
#: model:ir.model,name:sms.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api_display_name
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: sms
#: model:ir.model,name:sms.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api_id
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_id
msgid "ID"
msgstr "ID"

#. module: sms
#: code:addons/sms/models/mail_thread.py:53
#, python-format
msgid "Insufficient credit, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_api___last_update
#: model:ir.model.fields,field_description:sms.field_sms_send_sms___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_message
msgid "Message"
msgstr "Poruka"

#. module: sms
#: code:addons/sms/wizard/send_sms.py:48
#, python-format
msgid "Missing mobile number for %s."
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:55
#, python-format
msgid "No mobile number defined, unable to send SMS message: %s"
msgstr ""

#. module: sms
#: model:ir.model.fields,field_description:sms.field_sms_send_sms_recipients
msgid "Recipients"
msgstr ""

#. module: sms
#: code:addons/sms/models/mail_thread.py:48
#, python-format
msgid "SMS message sent: %s"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send"
msgstr "Pošalji"

#. module: sms
#: model:ir.actions.act_window,name:sms.send_sms_action
#: model:ir.actions.act_window,name:sms.send_sms_form_action
msgid "Send SMS"
msgstr ""

#. module: sms
#: model_terms:ir.ui.view,arch_db:sms.send_sms_view_form
msgid "Send an SMS"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_sms_api
msgid "sms.api"
msgstr ""

#. module: sms
#: model:ir.model,name:sms.model_sms_send_sms
msgid "sms.send_sms"
msgstr ""
