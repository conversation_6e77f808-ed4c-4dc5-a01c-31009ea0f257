<?xml version="1.0"?>
<odoo>

    <record id="hr_masarat_car_report_form" model="ir.ui.view">
        <field name="name">hr.masarat.car.wizard.form</field>
        <field name="model">hr.masarat.car.wizard</field>
        <field name="arch" type="xml">
            <form string="تقرير حركة السيارة">
                <group>
                    <group>
                        <field name="employee_id"
                               attrs="{'required':[('all_employee','=',False)],'invisible':[('all_employee','=',True)]}"/>
                        <field name="all_employee"/>
                    </group>
                    <group>
                        <field name="date_start" required="1"/>
                        <field name="date_end" required="1"/>
                    </group>
                </group>
                <footer>
                    <button name="get_report_action" type="object" string="انشاء"
                            class="btn-primary"/>
                    <button string="الغاء" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_hr_masarat_car_report_form" model="ir.actions.act_window">
        <field name="name">تقرير حركة السيارة</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.car.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_masarat_car_report_form"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_hr_car_allawance_report"
            name="تقرير حركة السيارة"
            parent="hr_approvales_masarat.menu_masarat_approvale_report"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_hr_masarat_car_report_form"
            sequence="2"/>


    <template id="car_allawance_report_id_all">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="0"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير حركة السيارة</strong>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <br/>

                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 15%; text-align: center; border: 1px solid;">اجمالي عدد الساعات</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">اجمالي عد التكليفات</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">اسم الموظف</th>
                                <th style="width: 5%; text-align: center; border: 1px solid;">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="str(employees_dict[line]['total_hours'])"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="str(employees_dict[line]['total_count'])"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="employees_dict[line]['name']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <t t-set="index" t-value="index + 1"/>
                                    <span t-esc="index"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="car_allawance_report_e">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="1"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير حركة السيارة</strong>
                        <span t-esc="employee_name"/>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 30%; text-align: center; border: 1px solid;">نوع المهمة</th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">عدد
                                    الساعات
                                </th>
                                <th style="width: 25%; text-align: center; border: 1px solid;">تاريخ الحركة</th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">المكلف من
                                    قبله
                                </th>
                                <th style="width: 5%; text-align: center; border: 1px solid; font-size:12px">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="line['mission_type']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['allowance_hours']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['request_date']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['located_by']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="index"/>
                                    <t t-set="index" t-value="index + 1"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="car_allawance_report_id">
        <t t-if="all_employee">
            <t t-call="hr_approvales_masarat.car_allawance_report_id_all"></t>
        </t>
        <t t-else="">
            <t t-call="hr_approvales_masarat.car_allawance_report_e"></t>
        </t>
    </template>

    <report
            id="car_allawance_report_x1"
            string="Car Allawance Report"
            model="hr.employee"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.car_allawance_report_id"
            file="hr_approvales_masarat.car_allawance_report_id"
            menu="False"/>


</odoo>