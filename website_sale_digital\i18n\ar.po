# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_digital
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:29+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Add attachments for this digital product"
msgstr "إضافة مرفقات إلى هذا المنتج الرقمي "

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_ir_attachment
msgid "Attachment"
msgstr "مرفق"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid "Digital Attachments"
msgstr "المرفقات الرقمية"

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_product_view_form_inherit_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.product_template_view_form_inherit_digital
msgid "Digital Files"
msgstr "الملفات الرقمية "

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_ir_attachment__product_downloadable
msgid "Downloadable from product portal"
msgstr "قابل للتنزيل من بوابة المنتج "

#. module: website_sale_digital
#: model_terms:ir.ui.view,arch_db:website_sale_digital.sale_order_portal_content_inherit_website_sale_digital
msgid "Downloads"
msgstr "التنزيلات "

#. module: website_sale_digital
#: model:ir.model.fields,field_description:website_sale_digital.field_product_product__attachment_count
#: model:ir.model.fields,field_description:website_sale_digital.field_product_template__attachment_count
msgid "File"
msgstr "الملف"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_account_move_line
msgid "Journal Item"
msgstr "عنصر اليومية"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_product
msgid "Product"
msgstr "المنتج"

#. module: website_sale_digital
#: model:ir.model,name:website_sale_digital.model_product_template
msgid "Product Template"
msgstr "قالب المنتج"

#. module: website_sale_digital
#: code:addons/website_sale_digital/models/product.py:0
#: code:addons/website_sale_digital/models/product.py:0
#, python-format
msgid ""
"The attached files are the ones that will be purchased and sent to the "
"customer."
msgstr "الملفات المرفقة هي التي سيتم شراؤها وإرسالها إلى العميل. "

#. module: website_sale_digital
#: model:product.template,uom_name:website_sale_digital.product_1
msgid "Units"
msgstr "الوحدات"

#. module: website_sale_digital
#: model:product.template,name:website_sale_digital.product_1
msgid "eBook: Office Renovation for Dummies"
msgstr "الكتاب الإلكتروني: ترميم المكاتب للمبتدئين "
