# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * phone_validation
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:00+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: phone_validation
#: selection:res.company,phone_international_format:0
msgid "Add international prefix"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_res_company_phone_international_format
msgid ""
"Always encode phone numbers using international format. Otherwise numbers "
"coming from the company's country are nationaly formatted. International "
"numbers are always using international format."
msgstr ""

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin_id
msgid "ID"
msgstr "ID"

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:23
#, python-format
msgid "Impossible number %s: probably invalid number of digits"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:25
#, python-format
msgid "Invalid number %s: probably incorrect prefix"
msgstr ""

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_validation_mixin___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_res_company_phone_international_format
msgid "Local Numbers"
msgstr ""

#. module: phone_validation
#: selection:res.company,phone_international_format:0
msgid "No prefix"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:44
#, python-format
msgid ""
"Unable to format %s:\n"
"%s"
msgstr ""

#. module: phone_validation
#: code:addons/phone_validation/tools/phone_validation.py:20
#, python-format
msgid ""
"Unable to parse %s:\n"
"%s"
msgstr ""

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_validation_mixin
msgid "phone.validation.mixin"
msgstr ""
