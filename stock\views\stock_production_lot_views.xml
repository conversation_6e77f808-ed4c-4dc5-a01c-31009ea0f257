<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_production_lot_form" model="ir.ui.view">
        <field name="name">stock.production.lot.form</field>
        <field name="model">stock.production.lot</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <form string="Lots/Serial Numbers">
                <sheet>
                <field name="display_complete" invisible="1"/>
                <div class="oe_button_box" name="button_box"
                     attrs="{'invisible': [('display_complete', '=', False)]}">
                        <button class="oe_stat_button" name="action_lot_open_transfers"
                                type="object" icon="fa-truck" help="Transfers"
                                attrs="{'invisible': [('delivery_count', '=', 0)]}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="delivery_count" widget="statinfo" nolabel="1" class="mr4"/>
                                </span>
                                <span class="o_stat_text">Transfers</span>
                            </div>
                        </button>                           
                        <button name="action_lot_open_quants" icon="fa-arrows" class="oe_stat_button" string="Location" type="object"/>
                        <button name="%(action_stock_report)d" icon="fa-arrow-up" class="oe_stat_button" string="Traceability" type="action"/>
                </div>
                <div class="oe_title">
                    <label for="name"/>
                    <h1>
                        <field name="name" placeholder="e.g. LOT/0001/20121"/>
                    </h1>
                </div>
                <group name="main_group">
                    <group>
                        <field name="product_id" context="{'default_detailed_type': 'product', 'default_tracking': 'lot'}" readonly="context.get('set_product_readonly', False)" force_save="1" help="Product this lot/serial number contains. You cannot change it anymore if it has already been moved."/>
                        <label for="product_qty" attrs="{'invisible': [('display_complete', '=', False)]}"/>
                        <div class="o_row" attrs="{'invisible': [('display_complete', '=', False)]}">
                            <field name="product_qty"/>
                            <field name="product_uom_id" groups="uom.group_uom"/>
                        </div>
                        <field name="ref"/>
                        <field name="company_id" groups="base.group_multi_company"/>
                    </group>
                    <group></group>
                </group>
                <notebook attrs="{'invisible': [('display_complete', '=', False)]}">
                    <page string="Description" name="description">
                        <field name="note"/>
                    </page>
                </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_production_lot_tree" model="ir.ui.view">
        <field name="name">stock.production.lot.tree</field>
        <field name="model">stock.production.lot</field>
        <field name="arch" type="xml">
            <tree string="Lots/Serial Numbers" multi_edit="1">
                <field name="name" decoration-bf="1"/>
                <field name="ref" optional="show"/>
                <field name="product_id" readonly="1"/>
                <field name="create_date" optional="show"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="last_delivery_partner_id" optional="hide" string="Transfer to"/>
            </tree>
        </field>
    </record>
    <record model="ir.ui.view" id="search_product_lot_filter">
        <field name="name">Production Lots Filter</field>
        <field name="model">stock.production.lot</field>
        <field name="arch" type="xml">
            <search string="Product Lots Filter">
                <field name="name" string="Lot/Serial Number" filter_domain="['|', ('name', 'ilike', self),('ref', 'ilike', self)]"/>
                <field name="product_id"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_product" string="Product" domain="[]" context="{'group_by': 'product_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_production_lot_form" model="ir.actions.act_window">
        <field name="name">Lots/Serial Numbers</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">stock.production.lot</field>
        <field name="view_ids"
                   eval="[(5, 0, 0),
                          (0, 0, {'view_mode': 'tree', 'view_id': ref('view_production_lot_tree')}),
                          (0, 0, {'view_mode': 'form', 'view_id': ref('view_production_lot_form')})]"/>
        <field name="search_view_id" ref="search_product_lot_filter"/>
        <field name="context">{'search_default_group_by_product': 1, 'display_complete': True, 'default_company_id': allowed_company_ids[0]}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Add a lot/serial number
          </p><p>
            Lots/Serial numbers help you tracking the path followed by your products.
            From their traceability report you will see the full history of their use, as well as their composition.
          </p>
        </field>
    </record>
    <menuitem action="action_production_lot_form" id="menu_action_production_lot_form"
        parent="menu_stock_inventory_control" groups="stock.group_production_lot"
        sequence="101"/>
</odoo>
