<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Event CRM Rule 0 -->
        <record id="event_lead_rule_0" model="event.lead.rule">
            <field name="name">Rule on @example.com</field>
            <field name="event_id" ref="event.event_5"/>
            <field name="event_registration_filter">[('email','ilike','@example.com')]</field>
            <field name="lead_user_id" ref="base.user_admin"/>
            <field name="lead_tag_ids" eval="[(6, 0, [ref('sales_team.categ_oppor8')])]"/>
        </record>

        <record id="event_registration_0_rule_0" model="event.registration">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">****** 555 0122</field>
            <field name="event_id" ref="event.event_5"/>
        </record>
        <record id="event_registration_1_rule_0" model="event.registration">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">****** 555 0161</field>
            <field name="event_id" ref="event.event_5"/>
        </record>

    </data>
</odoo>
