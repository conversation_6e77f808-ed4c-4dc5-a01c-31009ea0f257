# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* calendar
# 
# Translators:
# Khwunch<PERSON> Jaen<PERSON>awang <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> Thaidev <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.xml:0
#, python-format
msgid "!activity.calendar_event_id"
msgstr "!activity.calendar_event_id"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_count
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_count
msgid "# Meetings"
msgstr "# ประชุม"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"%(date_start)s at %(time_start)s To\n"
" %(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""
"%(date_start)s เวลา %(time_start)s ถึง\n"
" %(date_end)s เวลา %(time_end)s (%(timezone)s)"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "%(day)s at (%(start)s To %(end)s) (%(timezone)s)"
msgstr "%(day)s เวลา (%(start)s ถึง%(end)s) (%(timezone)s)"

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has accepted invitation"
msgstr "%s ได้ตอบรับคําเชิญ"

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "%s has declined invitation"
msgstr "%s ปฏิเสธคําเชิญ"

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_changedate
msgid ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                The date of your appointment with <t t-out=\"customer.name or ''\">Jesse Brown</t> has been updated.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment has been updated.\n"
"            </t>\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            The date of your appointment with <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> has been updated.\n"
"            The appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            The date of the meeting has been updated.\n"
"            The meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> created by <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> is now scheduled for\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 at (11:00:00 To 11:30:00) (Europe/Brussels)</t>.\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\"object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"     <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        สวัสดี <t t-out=\"object.common_name or ''\">Ready Mat</t>,<br/><br/>\n"
"        <t t-if=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                วันที่นัดหมายกับ <t t-out=\"customer.name or ''\">Jesse Brown</t>ได้รับการอัปเดตแล้ว\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                การนัดหมายได้รับการอัปเดตแล้ว\n"
"            </t>\n"
"            การนัดหมาย <strong t-out=\"object.event_id.appointment_type_id.name or ''\">กำหนดเวลาการสาธิต</strong> ขณะนี้มีกำหนดการวันที่\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_customer\">\n"
"            วันที่นัดหมายกับ<t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> ได้รับการอัปเดตแล้ว\n"
"            การนัดหมาย <strong t-out=\"object.event_id.appointment_type_id.name or ''\"/> ขณะนี้มีกำหนดการวันที่\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"           อัปเดตวันที่ของการประชุมแล้ว\n"
"            การประชุม <strong t-out=\"object.event_id.name or ''\">การติดตามข้อเสนอโครงการ</strong> สร้างโดย <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t>ขณะนี้มีกำหนดการวันที่\n"
"            <t t-out=\"object.event_id.get_display_time_tz(tz=object.partner_id.tz) or ''\">05/04/2021 เวลา (11:00:00 ถึง 11:30:00) (ยุโรป/บรัสเซลส์)</t>\n"
"        </t>\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/meeting/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ยอมรับ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ปฏิเสธ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ดู</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">วันอังคาร</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">พฤษภาคม 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                 <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 น.</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\"> ยุโรป/บรัสเซลส์</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>รายละเอียดของอีเวนต์</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>สถานที่: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">ดูแผนที่</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>เมื่อ: <t t-out=\"object.recurrence_id.name or ''\">ทุก 1 สัปดาห์ 3 อีเวนต์</t>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>ระยะเวลา: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0ช.ม30</t></li>\n"
"                </t>\n"
"                <li>ผู้เข้าร่วม\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background: {{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">แอดมิน Mitchell</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">คุณ</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li> การประชุม URL: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>รายละเอียดของอีเวนต์:\n"
"                    <t t-out=\"object.event_id.description\">ประชุมภายในเพื่อหารือการกำหนดราคาสินค้าและบริการใหม่</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>แอดมิน Mitchell</t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_update
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online = 'appointment_type_id' in object and object.appointment_type_id\"/>\n"
"    <t t-set=\"target_responsible = object.partner_id == object.partner_id\"/>\n"
"    <t t-set=\"target_customer = object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent = object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence'\"/>\n"
"    <t t-set=\"mail_tz\" t-value=\"object._get_mail_tz() or ctx.get('mail_tz')\"/>\n"
"    <div>\n"
"        <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n"
"            <tr>\n"
"                <td width=\"130px;\">\n"
"                    <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='EEEE', lang_code=object.env.lang) \"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='d', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                        <t t-out=\"format_datetime(dt=object.start, tz=mail_tz if not object.allday else None, dt_format='MMMM y', lang_code=object.env.lang)\"/>\n"
"                    </div>\n"
"                    <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                        <t t-if=\"not object.allday\">\n"
"                            <div>\n"
"                                <t t-out=\"format_time(time=object.start, tz=mail_tz, time_format='short', lang_code=object.env.lang)\"/>\n"
"                            </div>\n"
"                            <t t-if=\"mail_tz\">\n"
"                                <div style=\"font-size: 10px; font-weight: normal\">\n"
"                                    (<t t-out=\"mail_tz\"/>)\n"
"                                </div>\n"
"                            </t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td>\n"
"                <td width=\"20px;\"/>\n"
"                <td style=\"padding-top: 5px;\">\n"
"                    <p>\n"
"                        <strong>Details of the event</strong>\n"
"                    </p>\n"
"                    <ul>\n"
"                        <t t-if=\"not is_html_empty(object.description)\">\n"
"                            <li>Description:\n"
"                            <t t-out=\"object.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                        </t>\n"
"                        <t t-if=\"object.videocall_location\">\n"
"                            <li>Meeting URL:\n"
"                                <a t-att-href=\"object.videocall_location\" target=\"_blank\">\n"
"                                    <t t-out=\"object.videocall_location or ''\"/>\n"
"                                </a>\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"object.location\">\n"
"                            <li>Location: <t t-out=\"object.location or ''\"/>\n"
"                                (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.location}}\">View Map</a>)\n"
"                            </li>\n"
"                        </t>\n"
"                        <t t-if=\"recurrent\">\n"
"                            <li>When: <t t-out=\"object.recurrence_id.name or ''\"/></li>\n"
"                        </t>\n"
"                        <t t-if=\"not object.allday and object.duration\">\n"
"                            <li>Duration:\n"
"                                <t t-out=\"('%dH%02d' % (object.duration,round(object.duration*60)%60))\"/>\n"
"                            </li>\n"
"                        </t>\n"
"                    </ul>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div class=\"user_input\">\n"
"        <hr/>\n"
"        <p placeholder=\"Enter your message here\"><br/></p>\n"
"\n"
"    </div>\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_invitation
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"customer\" t-value=\" object.event_id.find_partner_customer()\"/>\n"
"    <t t-set=\"target_responsible\" t-value=\"object.partner_id == object.event_id.partner_id\"/>\n"
"    <t t-set=\"target_customer\" t-value=\"object.partner_id == customer\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Wood Corner</t>,<br/><br/>\n"
"\n"
"        <t t-if=\"is_online and target_customer\">\n"
"            Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> <t t-if=\"object.event_id.appointment_type_id.category != 'custom'\"> with <t t-out=\"object.event_id.user_id.name or ''\">Ready Mat</t></t> has been booked.\n"
"        </t>\n"
"        <t t-elif=\"is_online and target_responsible\">\n"
"            <t t-if=\"customer\">\n"
"                <t t-out=\"customer.name or ''\"/> scheduled the following appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> with you.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Your appointment <strong t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</strong> has been booked.\n"
"            </t>\n"
"        </t>\n"
"        <t t-elif=\"not target_responsible\">\n"
"            <t t-out=\"object.event_id.user_id.partner_id.name or ''\">Colleen Diaz</t> invited you for the <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> meeting.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Your meeting <strong t-out=\"object.event_id.name or ''\">Follow-up for Project proposal</strong> has been booked.\n"
"        </t>\n"
"\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"not is_online or object.state != 'accepted'\">\n"
"            <a t-attf-href=\"/calendar/meeting/accept?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Accept</a>\n"
"            <a t-attf-href=\"/calendar/meeting/decline?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"                Decline</a>\n"
"        </t>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{object.access_token}}&amp;id={{object.event_id.id}}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\"><t t-out=\"'Reschedule' if is_online and target_customer else 'View'\">View</t></a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='EEEE', lang_code=object.env.lang) or ''\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format='MMMM y', lang_code=object.env.lang) or ''\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold ; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format='short', lang_code=object.env.lang) or ''\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"is_online\">\n"
"                    <li>Appointment Type: <t t-out=\"object.event_id.appointment_type_id.name or ''\">Schedule a Demo</t></li>\n"
"                </t>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{object.event_id.location}}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-att-href=\"object.event_id.videocall_location\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""

#. module: calendar
#: model:mail.template,body_html:calendar.calendar_template_meeting_reminder
msgid ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        Hello <t t-out=\"object.common_name or ''\">Gemini Furniture</t>,<br/><br/>\n"
"        This is a reminder for the below event :\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Accept</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            Decline</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            View</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">Tuesday</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">May 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 AM</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">Europe/Brussels</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>Details of the event</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>Location: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">View Map</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>When: <t t-out=\"object.recurrence_id.name or ''\">Every 1 Weeks, for 3 events</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>Duration: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0H30</t></li>\n"
"                </t>\n"
"                <li>Attendees\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">Mitchell Admin</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">You</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>Meeting URL: <a t-attf-href=\"{{ object.event_id.videocall_location }}\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>Description of the event:\n"
"                    <t t-out=\"object.event_id.description\">Internal meeting for discussion for new pricing for product and services.</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"colors\" t-value=\"{'needsAction': 'grey', 'accepted': 'green', 'tentative': '#FFFF00', 'declined': 'red'}\"/>\n"
"    <t t-set=\"is_online\" t-value=\"'appointment_type_id' in object.event_id and object.event_id.appointment_type_id\"/>\n"
"    <t t-set=\"recurrent\" t-value=\"object.recurrence_id and not ctx.get('calendar_template_ignore_recurrence')\"/>\n"
"    <p>\n"
"        สวัสดี <t t-out=\"object.common_name or ''\">Gemini Furniture</t> <br/><br/>\n"
"        นี่คือการเตือนความจำสำหรับอีเวนต์ด้านล่าง :\n"
"    </p>\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/accept?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ยอมรับ</a>\n"
"        <a t-attf-href=\"/calendar/{{ 'recurrence' if recurrent else 'meeting' }}/decline?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ปฏิเสธ</a>\n"
"        <a t-attf-href=\"/calendar/meeting/view?token={{ object.access_token }}&amp;id={{ object.event_id.id }}\" style=\"padding: 5px 10px; color: #FFFFFF; text-decoration: none; background-color: #875A7B; border: 1px solid #875A7B; border-radius: 3px\">\n"
"            ดู</a>\n"
"    </div>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr>\n"
"        <td width=\"130px;\">\n"
"            <div style=\"border-top-left-radius: 3px; border-top-right-radius: 3px; font-size: 12px; border-collapse: separate; text-align: center; font-weight: bold; color: #ffffff; min-height: 18px; background-color: #875A7B; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;EEEE&quot;, lang_code=object.env.lang) or &quot;&quot;\">วันอังคาร</t>\n"
"            </div>\n"
"            <div style=\"font-size: 48px; min-height: auto; font-weight: bold; text-align: center; color: #5F5F5F; background-color: #F8F8F8; border: 1px solid #875A7B;\">\n"
"                <t t-out=\"str(object.event_id.start.day) or ''\">4</t>\n"
"            </div>\n"
"            <div style=\"font-size: 12px; text-align: center; font-weight: bold; color: #ffffff; background-color: #875A7B;\">\n"
"                <t t-out=\"format_datetime(dt=object.event_id.start, tz=object.mail_tz if not object.event_id.allday else None, dt_format=&quot;MMMM y&quot;, lang_code=object.env.lang) or &quot;&quot;\">พฤษภาคม 2021</t>\n"
"            </div>\n"
"            <div style=\"border-collapse: separate; color: #5F5F5F; text-align: center; font-size: 12px; border-bottom-right-radius: 3px; font-weight: bold; border: 1px solid #875A7B; border-bottom-left-radius: 3px;\">\n"
"                <t t-if=\"not object.event_id.allday\">\n"
"                    <div>\n"
"                        <t t-out=\"format_time(time=object.event_id.start, tz=object.mail_tz, time_format=&quot;short&quot;, lang_code=object.env.lang) or &quot;&quot;\">11:00 น.</t>\n"
"                    </div>\n"
"                    <t t-if=\"object.mail_tz\">\n"
"                        <div style=\"font-size: 10px; font-weight: normal\">\n"
"                            (<t t-out=\"object.mail_tz or ''\">ยุโรป/บรัสเซลส์</t>)\n"
"                        </div>\n"
"                    </t>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"        <td width=\"20px;\"/>\n"
"        <td style=\"padding-top: 5px;\">\n"
"            <p><strong>รายละเอียดของอีเวนต์</strong></p>\n"
"            <ul>\n"
"                <t t-if=\"object.event_id.location\">\n"
"                    <li>สถานที่: <t t-out=\"object.event_id.location or ''\">Bruxelles</t>\n"
"                        (<a target=\"_blank\" t-attf-href=\"http://maps.google.com/maps?oi=map&amp;q={{ object.event_id.location }}\">ดูแผนที่</a>)\n"
"                    </li>\n"
"                </t>\n"
"                <t t-if=\"recurrent\">\n"
"                    <li>เมื่อ: <t t-out=\"object.recurrence_id.name or ''\">ทุก 1 สัปดาห์ 3 อีเวนต์</t></li>\n"
"                </t>\n"
"                <t t-if=\"not object.event_id.allday and object.event_id.duration\">\n"
"                    <li>ระยะเวลา: <t t-out=\"('%dH%02d' % (object.event_id.duration,round(object.event_id.duration*60)%60)) or ''\">0ช.ม30</t></li>\n"
"                </t>\n"
"                <li>ผู้เข้าร่วม\n"
"                <ul>\n"
"                    <li t-foreach=\"object.event_id.attendee_ids\" t-as=\"attendee\">\n"
"                        <div t-attf-style=\"display: inline-block; border-radius: 50%; width: 10px; height: 10px; background:{{ colors.get(attendee.state) or 'white' }};\"> </div>\n"
"                        <t t-if=\"attendee.common_name != object.common_name\">\n"
"                            <span style=\"margin-left:5px\" t-out=\"attendee.common_name or ''\">แอดมิน Mitchell</span>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"margin-left:5px\">คุณ</span>\n"
"                        </t>\n"
"                    </li>\n"
"                </ul></li>\n"
"                <t t-if=\"object.event_id.videocall_location\">\n"
"                    <li>การประชุม URL: <a t-attf-href=\"{{ object.event_id.videocall_location }}\" target=\"_blank\" t-out=\"object.event_id.videocall_location or ''\">https://meet.jit.si/odoo-xyz</a></li>\n"
"                </t>\n"
"                <t t-if=\"not is_html_empty(object.event_id.description)\">\n"
"                    <li>รายละเอียดอีเวนต์:\n"
"                    <t t-out=\"object.event_id.description\">ประชุมภายในเพื่อหารือการกำหนดราคาสินค้าและบริการใหม่</t></li>\n"
"                </t>\n"
"            </ul>\n"
"        </td>\n"
"    </tr></table>\n"
"    <br/>\n"
"    ขอบคุณ\n"
"    <t t-if=\"object.event_id.user_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.event_id.user_id.signature or ''\">--<br/>แอดมิน Mitchell </t>\n"
"    </t>\n"
"</div>\n"
"            "

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.res_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"Meetings\" role=\"img\" "
"title=\"Meetings\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-calendar\" aria-label=\"ประชุม\" role=\"img\" "
"title=\"ประชุม\"/>"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "<span> hours</span>"
msgstr "<span>ชั่วโมง</span>"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_filters_user_id_partner_id_unique
msgid "A user cannot have the same contact twice."
msgstr "ผู้ใช้ไม่สามารถมีผู้ติดต่อเดียวกันสองอันได้"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Accept"
msgstr "ยอมรับ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__accepted
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__accepted
msgid "Accepted"
msgstr "ยอมรับแล้ว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_type__category
msgid "Action"
msgstr "การดำเนินการ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction
msgid "Action Needed"
msgstr "ต้องดำเนินการ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"การดำเนินการอาจทำให้เกิดพฤติกรรมเฉพาะเช่นการเปิดมุมมองปฏิทินหรือทำเครื่องหมายโดยอัตโนมัติว่าเสร็จสิ้นเมื่ออัปโหลดเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity
msgid "Activity"
msgstr "กิจกรรม"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "กิจกรรม Mixin"

#. module: calendar
#: model:ir.model,name:calendar.model_mail_activity_type
msgid "Activity Type"
msgstr "ประเภทกิจกรรม"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#, python-format
msgid "Add"
msgstr "เพิ่ม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__body
msgid "Additional Message"
msgstr "ข้อความเพิ่มเติม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__body
msgid ""
"Additional message that would be sent with the notification for the reminder"
msgstr "ข้อความเพิ่มเติมที่จะส่งพร้อมการแจ้งเตือนสำหรับตัวเตือนความจำ"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields,field_description:calendar.field_calendar_event__allday
#, python-format
msgid "All Day"
msgstr "ทั้งวัน"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "All Day, %(day)s"
msgstr "ทั้งวัน, %(day)s"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__all_events
#, python-format
msgid "All events"
msgstr "อีเวนต์ทั้งหมด"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_attachment_count
msgid "Attachment Count"
msgstr "จํานวนสิ่งที่แนบมา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__partner_id
msgid "Attendee"
msgstr "ผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_status
msgid "Attendee Status"
msgstr "สถานะผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_ids
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Attendees"
msgstr "ผู้เข้าร่วม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Availability"
msgstr "ความพร้อม"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__free
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__free
msgid "Available"
msgstr "ว่าง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__availability
msgid "Available/Busy"
msgstr "ว่าง/ไม่ว่าง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__base_event_id
msgid "Base Event"
msgstr "ฐานอีเวนต์"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__availability__busy
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__show_as__busy
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
#, python-format
msgid "Busy"
msgstr "ไม่ว่าง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__byday
msgid "By day"
msgstr "ตามวัน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__byday
msgid "Byday"
msgstr "ตามวัน"

#. module: calendar
#: model:ir.ui.menu,name:calendar.mail_menu_calendar
#: model:ir.ui.menu,name:calendar.menu_calendar_configuration
#: model_terms:ir.ui.view,arch_db:calendar.res_users_view_form
msgid "Calendar"
msgstr "ปฏิทิน"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_alarm
#: model:ir.ui.menu,name:calendar.menu_calendar_alarm
#: model_terms:ir.ui.view,arch_db:calendar.calendar_alarm_view_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_alarm_tree
msgid "Calendar Alarm"
msgstr "เตือนปฏิทิน"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "ข้อมูลผู้เข้าร่วมปฏิทิน"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__calendar_event_ids
msgid "Calendar Event"
msgstr "ปฎิทินอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_filters
msgid "Calendar Filters"
msgstr "ตัวกรองปฏิทิน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Calendar Invitation"
msgstr "คำเชิญปฏิทิน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity__calendar_event_id
msgid "Calendar Meeting"
msgstr "ปฏิทินการประชุม"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_changedate
msgid "Calendar: Date updated"
msgstr "ปฏิทิน: วันที่อัปเดต"

#. module: calendar
#: model:ir.actions.server,name:calendar.ir_cron_scheduler_alarm_ir_actions_server
#: model:ir.cron,cron_name:calendar.ir_cron_scheduler_alarm
#: model:ir.cron,name:calendar.ir_cron_scheduler_alarm
msgid "Calendar: Event Reminder"
msgstr "ปฏิทิน: การแจ้งเตือนอีเวนต์"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_update
msgid "Calendar: Event Update"
msgstr "ปฏิทิน: อัปเดตอีเวนต์"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_invitation
msgid "Calendar: Meeting Invitation"
msgstr "ปฏิทิน: เชิญประชุม"

#. module: calendar
#: model:mail.template,name:calendar.calendar_template_meeting_reminder
msgid "Calendar: Reminder"
msgstr "ปฏิทิน: การแจ้งเตือน"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__is_organizer_alone
msgid ""
"Check if the organizer is alone in the event, i.e. if the organizer is the only one that hasn't declined\n"
"        the event (only if the organizer is not the only attendee)"
msgstr ""
"ตรวจสอบว่าผู้จัดงานอยู่รายเดียวในอีเวนต์หรือไม่ เช่น หากผู้จัดงานเป็นเพียงรายเดียวที่ไม่ปฏิเสธ\n"
"       อีเวนต์ (เฉพาะในกรณีที่ผู้จัดงานไม่ใช่ผู้เข้าร่วมเท่านั้น)"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_checked
msgid "Checked"
msgstr "ตรวจสอบแล้ว"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__recurrence_update
msgid ""
"Choose what to do with other events in the recurrence. Updating All Events "
"is not allowed when dates or time is modified"
msgstr ""
"เลือกว่าจะทำอย่างไรกับอีเวนต์อื่น ๆ ที่เกิดซ้ำ "
"และไม่อนุญาตให้อัปเดตกิจกรรมทั้งหมดในขณะที่มีการแก้ไขวันที่หรือเวลา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__color
msgid "Color"
msgstr "สี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__common_name
msgid "Common name"
msgstr "ชื่อทั่วไป"

#. module: calendar
#: model:ir.ui.menu,name:calendar.calendar_menu_config
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Confirm"
msgstr "ยืนยัน"

#. module: calendar
#: model:ir.model,name:calendar.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Contact Attendees"
msgstr "ติดต่อผู้เข้าร่วม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__count
msgid "Count"
msgstr "จำนวน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__create_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Date"
msgstr "วันที่"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__date
msgid "Date of month"
msgstr "วันที่ของเดือน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__day
msgid "Day"
msgstr "วัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Day of Month"
msgstr "วันที่ของปี"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__month_by__day
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__month_by__day
msgid "Day of month"
msgstr "วันที่ของปี"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__days
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__daily
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__daily
msgid "Days"
msgstr "วัน"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Decline"
msgstr "ปฏิเสธ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__declined
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__declined
msgid "Declined"
msgstr "ปฏิเสธแล้ว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__description
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Description"
msgstr "คำบรรยาย"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Details"
msgstr "รายละเอียด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_description
msgid "Display Description"
msgstr "แสดงรายละเอียด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__display_name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Document"
msgstr "เอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_id
msgid "Document ID"
msgstr "ไอดีเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model_id
msgid "Document Model"
msgstr "โมเดลเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__res_model
msgid "Document Model Name"
msgstr "ชื่อโมเดลเอกสาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__dtstart
msgid "Dtstart"
msgstr "ระยะเริ่ม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__duration
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Duration"
msgstr "ระยะเวลา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration_minutes
#: model:ir.model.fields,help:calendar.field_calendar_alarm__duration_minutes
msgid "Duration in minutes"
msgstr "ระยะเวลาในนาที"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "EMAIL"
msgstr "อีเมล"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/calendar_controller.js:0
#, python-format
msgid "Edit Recurrent event"
msgstr "แก้ไขอีเวนต์ที่เกิดซ้ำ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Edit recurring event"
msgstr "แก้ไขอีเวนต์ที่เกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__email
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__email
msgid "Email"
msgstr "อีเมล"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_1
msgid "Email - 3 Hours"
msgstr "อีเมล - 3 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_mail_2
msgid "Email - 6 Hours"
msgstr "อีเมล - 6 ชั่วโมง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__mail_template_id
msgid "Email Template"
msgstr "เทมเพลตอีเมล"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__email
msgid "Email of Invited Person"
msgstr "อีเมลของผู้ที่ถูกเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__partner_id
msgid "Employee"
msgstr "พนักงาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "End Date"
msgstr "วันสิ้นสุด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__end_type
msgid "End Type"
msgstr "ประเภทการสิ้นสุด"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__end_date
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__end_date
msgid "End date"
msgstr "วันสิ้นสุด"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending At"
msgstr "สิ้นสุดเมื่อ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Ending at"
msgstr "สิ้นสุดเมื่อ"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm
msgid "Event Alarm"
msgstr "เตือนอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_alarm_manager
msgid "Event Alarm Manager"
msgstr "ตัวจัดการการเตือนอีเวนต์"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_event_type
msgid "Event Meeting Type"
msgstr "ประเภทอีเวนต์การประชุม"

#. module: calendar
#: model:ir.model,name:calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "กฎการเกิดซ้ำของอีเวนต์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__display_time
msgid "Event Time"
msgstr "เวลาอีเวนต์"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "Every %(count)s %(period)s"
msgstr "ทุก%(count)s %(period)s"

#. module: calendar
#: code:addons/calendar/models/mail_activity.py:0
#, python-format
msgid "Feedback: %(feedback)s"
msgstr "ผลตอบรับ:%(feedback)s"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__1
msgid "First"
msgstr "อันดับแรก"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "First you have to specify the date of the invitation."
msgstr "ขั้นแรกคุณต้องระบุวันที่ของคำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__follow_recurrence
msgid "Follow Recurrence"
msgstr "ติดตามการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__forever
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__forever
msgid "Forever"
msgstr "ตลอดการ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__4
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__4
msgid "Fourth"
msgstr "อันดับสี่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Free"
msgstr "ฟรี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__fri
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__fri
msgid "Fri"
msgstr "ศุกร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__fri
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__fri
msgid "Friday"
msgstr "วันศุกร์"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Grouping by %s is not allowed on private events."
msgstr "จัดกลุ่มโดย%sไม่ได้รับอนุญาตในอีเวนต์ส่วนตัว"

#. module: calendar
#: model:ir.model,name:calendar.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__hours
msgid "Hours"
msgstr "ชั่วโมง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__id
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__id
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__id
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__id
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__id
msgid "ID"
msgstr "ไอดี"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"หากฟิลด์ที่ใช้งานอยู่ถูกตั้งค่าเป็น \"เท็จ\" "
"จะช่วยให้คุณสามารถซ่อนข้อมูลการเตือนอีเวนต์โดยไม่ต้องลบออก"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__show_as
msgid ""
"If the time is shown as 'busy', this event will be visible to other people with either the full         information or simply 'busy' written depending on its privacy. Use this option to let other people know         that you are unavailable during that period of time. \n"
" If the event is shown as 'free', other users know         that you are available during that period of time."
msgstr ""
"หากเวลาแสดงเป็น 'ไม่ว่าง' กิจกรรมนี้จะมองเห็นได้โดยบุคคลอื่นโดยมีข้อมูลครบถ้วนหรือเขียนว่า 'ไม่ว่าง' ทั้งนี้จะขึ้นอยู่กับความเป็นส่วนตัวของกิจกรรม ใช้ตัวเลือกนี้เพื่อแจ้งให้ผู้อื่นทราบว่าคุณไม่สามารถใช้งานได้ในช่วงเวลาดังกล่าว\n"
"หากกิจกรรมแสดงเป็น 'ว่าง' ผู้ใช้รายอื่นจะรู้ว่าคุณว่างในช่วงเวลานั้น"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__interval
msgid "Interval"
msgstr "ช่วงเวลา"

#. module: calendar
#: model:mail.message.subtype,name:calendar.subtype_invitation
msgid "Invitation"
msgstr "คำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__access_token
msgid "Invitation Token"
msgstr "โทเคนคำเชิญ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitation details"
msgstr "รายละเอียดคำเชิญ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Invitation for"
msgstr "คำเชิญสำหรับ"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_invitation
msgid "Invitation to {{ object.event_id.name }}"
msgstr "คำเชิญถึง {{ object.event_id.name }}"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Invitations"
msgstr "คำเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_highlighted
msgid "Is the Event Highlighted"
msgstr "คือไฮไลท์ของงาน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__is_organizer_alone
msgid "Is the Organizer Alone"
msgstr "คือผู้จัดรายเดียว"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__-1
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__-1
msgid "Last"
msgstr "อันดับสุดท้าย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_filters____last_update
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_uid
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__write_date
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_res_partner__calendar_last_notif_ack
#: model:ir.model.fields,field_description:calendar.field_res_users__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr "การแจ้งเตือนล่าสุดที่ทำเครื่องหมายว่าอ่านแล้วจากฐานปฏิทิน"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__rrule_type
msgid "Let the event automatically repeat at that interval"
msgstr "ให้เหตุการณ์ทําซ้ำโดยอัตโนมัติในช่วงเวลานั้น"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__location
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Location"
msgstr "สถานที่"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__location
msgid "Location of Event"
msgstr "สถานที่จัดงาน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Logo"
msgstr "โลโก้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__mail_tz
msgid "Mail Tz"
msgstr "เมลเขตเวลา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_main_attachment_id
msgid "Main Attachment"
msgstr "เอกสารหลักที่แนบมา"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_filters__user_id
msgid "Me"
msgstr "ฉัน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__mail_activity_type__category__meeting
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Meeting"
msgstr "การประชุม"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"Meeting '%(name)s' starts '%(start_datetime)s' and ends '%(end_datetime)s'"
msgstr ""
"การประชุม '%(name)s' เริ่ม '%(start_datetime)s' และจบ '%(end_datetime)s'"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Meeting Details"
msgstr "รายละเอียดการประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__name
msgid "Meeting Subject"
msgstr "หัวเรื่องในการประชุม"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event_type
#: model:ir.ui.menu,name:calendar.menu_calendar_event_type
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_type_tree
msgid "Meeting Types"
msgstr "ประเภทการประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__videocall_location
msgid "Meeting URL"
msgstr "การประชุม URL"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__event_id
msgid "Meeting linked"
msgstr "ลิงก์การประชุม"

#. module: calendar
#: model:ir.actions.act_window,name:calendar.action_calendar_event
#: model:ir.actions.act_window,name:calendar.action_calendar_event_notify
#: model:ir.model.fields,field_description:calendar.field_res_partner__meeting_ids
#: model:ir.model.fields,field_description:calendar.field_res_users__meeting_ids
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
#: model_terms:ir.ui.view,arch_db:calendar.view_partners_form
msgid "Meetings"
msgstr "การประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error
msgid "Message Delivery error"
msgstr "เกิดการผิดพลาดในการส่งข้อความ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__interval__minutes
msgid "Minutes"
msgstr "นาที"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__mon
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__mon
msgid "Mon"
msgstr "จันทร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__mon
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__mon
msgid "Monday"
msgstr "วันจันทร์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__month_by
msgid "Month By"
msgstr "เดือนโดย"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__monthly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__monthly
msgid "Months"
msgstr "เดือน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "My Meetings"
msgstr "การประชุมของฉัน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__name
#: model:ir.model.fields,field_description:calendar.field_calendar_event_type__name
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__name
msgid "Name"
msgstr "ชื่อ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__needsaction
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__needsaction
msgid "Needs Action"
msgstr "ต้องดำเนินการ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_mail_activity_mixin__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_partner__activity_calendar_event_id
#: model:ir.model.fields,field_description:calendar.field_res_users__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No I'm not going."
msgstr "ไม่ฉันจะไม่ไป"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "No feedback yet"
msgstr "ยังไม่มีผลตอบรับ"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid "No meetings found. Let's schedule one!"
msgstr "ไม่พบการประชุม มากำหนดเวลากัน!"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_alarm__alarm_type__notification
msgid "Notification"
msgstr "การแจ้งเตือน"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_5
msgid "Notification - 1 Days"
msgstr "การแจ้งเตือน - 1 วัน"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_3
msgid "Notification - 1 Hours"
msgstr "การแจ้งเตือน - 1 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_1
msgid "Notification - 15 Minutes"
msgstr "การแจ้งเตือน - 15 นาที"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_4
msgid "Notification - 2 Hours"
msgstr "การแจ้งเตือน - 2 ชั่วโมง"

#. module: calendar
#: model:calendar.alarm,name:calendar.alarm_notif_2
msgid "Notification - 30 Minutes"
msgstr "การแจ้งเตือน - 30 นาที"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__alarm_ids
msgid "Notifications sent to all attendees to remind of the meeting."
msgstr "ส่งการแจ้งเตือนไปยังผู้เข้าร่วมประชุมทั้งหมดเพื่อเตือนการประชุม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "จํานวนข้อความที่ต้องการการดําเนินการ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__end_type__count
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__end_type__count
msgid "Number of repetitions"
msgstr "จํานวนการซ้ำ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__message_unread_counter
msgid "Number of unread messages"
msgstr "จํานวนข้อความที่ยังไม่ได้อ่าน"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "OK"
msgstr "โอเค"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Only Internal Users"
msgstr "เฉพาะผู้ใช้ภายใน"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__confidential
msgid "Only internal users"
msgstr "ผู้ใช้ภายในเท่านั้น"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.mail_activity_view_form_popup
msgid "Open Calendar"
msgstr "เปิดปฏิทิน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__month_by
msgid "Option"
msgstr "ตัวเลือก"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Options"
msgstr "ตัวเลือก"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__user_id
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_calendar
msgid "Organizer"
msgstr "ผู้จัด"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__attendee_ids
msgid "Participant"
msgstr "ผู้มีส่วนร่วม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__partner_id
msgid "Partner-related data of the user"
msgstr "ข้อมูลที่เกี่ยวข้องกับพาร์ทเนอร์ของผู้ใช้"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__privacy
msgid "People to whom this event will be visible."
msgstr "บุคคลที่จะมองเห็นอีเวนต์นี้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__phone
msgid "Phone number of Invited Person"
msgstr "หมายเลขโทรศัพท์ของผู้ได้รับเชิญ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__privacy
msgid "Privacy"
msgstr "ความเป็นส่วนตัว"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__private
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Private"
msgstr "ส่วนตัว"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "Private Event Excluded"
msgstr "ไม่รวมกิจกรรมส่วนตัว"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__privacy__public
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Public"
msgstr "สาธารณะ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule_type
msgid "Recurrence"
msgstr "การเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__recurrence_id
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_id
msgid "Recurrence Rule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__end_type
msgid "Recurrence Termination"
msgstr "การยกเลิกการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrence_update
msgid "Recurrence Update"
msgstr "อัปเดตการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__recurrency
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Recurrent"
msgstr "การเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__rrule
msgid "Recurrent Rule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__duration
msgid "Remind Before"
msgstr "เตือนความจำก่อน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__alarm_ids
#: model:ir.ui.menu,name:calendar.calendar_submenu_reminders
msgid "Reminders"
msgstr "ตัวเตือนความจำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__count
msgid "Repeat"
msgstr "ทำซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__interval
msgid "Repeat Every"
msgstr "ทําซ้ำทุก"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__until
msgid "Repeat Until"
msgstr "ทําซ้ำจนกระทั่ง"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__interval
msgid "Repeat every (Days/Week/Month/Year)"
msgstr "ทําซ้ำทุก (วัน / สัปดาห์ / เดือน / ปี)"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__count
msgid "Repeat x times"
msgstr "ทำซ้ำ x ครั้ง"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.xml:0
#, python-format
msgid "Reschedule"
msgstr "กำหนดการใหม่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule
msgid "Rrule"
msgstr "กฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__rrule_type
msgid "Rrule Type"
msgstr "ประเภทกฎการเกิดซ้ำ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sat
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sat
msgid "Sat"
msgstr "เสาร์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sat
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sat
msgid "Saturday"
msgstr "วันเสาร์"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__partner_id
msgid "Scheduled by"
msgstr "กำหนดโดย"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_search
msgid "Search Meetings"
msgstr "ค้นหาการประชุม"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__2
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__2
msgid "Second"
msgstr "อันดับสอง"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Select attendees..."
msgstr "เลือกผู้เข้าร่วม..."

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Email to attendees"
msgstr "ส่งอีเมลถึงผู้เข้าร่วม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Send Invitations"
msgstr "ส่งคำเชิญ"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Send Mail"
msgstr "ส่งอีเมล"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__show_as
msgid "Show as"
msgstr "แสดงเป็น"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/js/services/calendar_notification_service.js:0
#, python-format
msgid "Snooze"
msgstr "ปิดการเตือนชั่วคราว"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start
msgid "Start"
msgstr "เริ่ม"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__start_date
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__start
msgid "Start date of an event, without time for full days events"
msgstr "วันที่เริ่มต้นของอีเวนต์ โดยไม่มีเวลาสำหรับกิจกรรมเต็มวัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Starting at"
msgstr "เริ่มเมื่อ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_attendee__state
msgid "Status"
msgstr "สถานะ"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__state
msgid "Status of the attendee's participation"
msgstr "สถานะการมีส่วนร่วมของผู้เข้าร่วม"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Status:"
msgstr "สถานะ:"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__stop
msgid "Stop"
msgstr "หยุด"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_event__stop
msgid "Stop date of an event, without time for full days events"
msgstr "หยุดวันที่ของอีเวนต์ ไม่มีเวลาสำหรับอีเวนต์เต็มวัน"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_tree
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__sun
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__sun
msgid "Sun"
msgstr "อาทิตย์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__sun
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__sun
msgid "Sunday"
msgstr "วันอาทิตย์"

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_event_type_name_uniq
msgid "Tag name already exists !"
msgstr "ชื่อแท็กมีอยู่แล้ว!"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__categ_ids
msgid "Tags"
msgstr "แท็ก"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_alarm__mail_template_id
msgid "Template used to render mail reminder content."
msgstr "เทมเพลตที่ใช้แสดงเนื้อหาเตือนความจำเมล"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Tentative"
msgstr "ยังไม่แน่"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "The"
msgstr " "

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/components/activity/activity.js:0
#, python-format
msgid ""
"The activity is linked to a meeting. Deleting it will remove the meeting as "
"well. Do you want to proceed ?"
msgstr ""
"กิจกรรมเชื่อมโยงกับการประชุม การลบจะเป็นการลบการประชุมด้วย "
"คุณต้องการดำเนินการต่อหรือไม่?"

#. module: calendar
#: model_terms:ir.actions.act_window,help:calendar.action_calendar_event
msgid ""
"The calendar is shared between employees and fully integrated with\n"
"            other applications such as the employee leaves or the business\n"
"            opportunities."
msgstr ""
"ปฏิทินมีการแชร์ระหว่างพนักงานมีการผสานการทำงานอย่างเต็มรูปแบบกับ\n"
"          แอปพลิเคชันอื่น ๆ เช่น การลาของพนักงานหรือ\n"
"            โอกาสทางธุรกิจต่าง ๆ "

#. module: calendar
#: model:ir.model.constraint,message:calendar.constraint_calendar_recurrence_month_day
msgid "The day must be between 1 and 31"
msgstr "วันที่ต้องอยู่ระหว่าง 1 ถึง 31"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid ""
"The ending date and time cannot be earlier than the starting date and time."
msgstr "วันที่และเวลาสิ้นสุดต้องไม่อยู่ก่อนวันที่และเวลาเริ่มต้น"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "The ending date cannot be earlier than the starting date."
msgstr "วันที่สิ้นสุดต้องไม่มาก่อนวันที่เริ่มต้น"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The interval cannot be negative."
msgstr "ช่วงเวลาไม่สามารถเป็นค่าลบได้"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "The number of repetitions cannot be negative."
msgstr "จำนวนการซ้ำต้องไม่เป็นค่าลบ"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "There are no attendees on these events"
msgstr "ไม่มีผู้เข้าร่วมในอีเวนต์เหล่านี้"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__byday__3
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__byday__3
msgid "Third"
msgstr "อันดับที่สาม"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__future_events
#, python-format
msgid "This and following events"
msgstr "อีเวนต์นี้และต่อไป"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__recurrence_update__self_only
#, python-format
msgid "This event"
msgstr "อีเวนต์นี้"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_filters__partner_checked
msgid ""
"This field is used to know if the partner is checked in the filter of the "
"calendar view for the user_id."
msgstr ""
"ฟิลด์นี้ใช้เพื่อทราบว่าพาร์ทเนอร์ได้ตรวจสอบตัวกรองของมุมมองปฏิทินสำหรับ "
"user_id หรือไม่"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__thu
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__thu
msgid "Thu"
msgstr "พฤหัส"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__thu
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__thu
msgid "Thursday"
msgstr "วันพฤหัสบดี"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__event_tz
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__event_tz
msgid "Timezone"
msgstr "เขตเวลา"

#. module: calendar
#: model:ir.model.fields,help:calendar.field_calendar_attendee__mail_tz
msgid "Timezone used for displaying time in the mail template"
msgstr "เขตเวลาที่ใช้สำหรับแสดงเวลาในเทมเพลตเมล"

#. module: calendar
#: code:addons/calendar/models/res_users.py:0
#, python-format
msgid "Today's Meetings"
msgstr "การประชุมวันนี้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__tue
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__tue
msgid "Tue"
msgstr "อังคาร"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__tue
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__tue
msgid "Tuesday"
msgstr "วันอังคาร"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__alarm_type
msgid "Type"
msgstr "ประเภท"

#. module: calendar
#. openerp-web
#: code:addons/calendar/static/src/xml/base_calendar.xml:0
#: model:ir.model.fields.selection,name:calendar.selection__calendar_attendee__state__tentative
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__attendee_status__tentative
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
#, python-format
msgid "Uncertain"
msgstr "ไม่แน่นอน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_alarm__interval
msgid "Unit"
msgstr "หน่วย"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread
msgid "Unread Messages"
msgstr "ข้อความที่ยังไม่ได้อ่าน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__message_unread_counter
msgid "Unread Messages Counter"
msgstr "ตัวนับข้อความที่ยังไม่ได้อ่าน"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__until
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "Until"
msgstr "ถึง"

#. module: calendar
#: model:ir.model,name:calendar.model_res_users
msgid "Users"
msgstr "ผู้ใช้"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__wed
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__wed
msgid "Wed"
msgstr "พุธ"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__weekday__wed
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__weekday__wed
msgid "Wednesday"
msgstr "วันพุธ"

#. module: calendar
#: model:ir.model.fields,field_description:calendar.field_calendar_event__weekday
#: model:ir.model.fields,field_description:calendar.field_calendar_recurrence__weekday
msgid "Weekday"
msgstr "วันธรรมดา"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__weekly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__weekly
msgid "Weeks"
msgstr "สัปดาห์"

#. module: calendar
#: model:ir.model.fields.selection,name:calendar.selection__calendar_event__rrule_type__yearly
#: model:ir.model.fields.selection,name:calendar.selection__calendar_recurrence__rrule_type__yearly
msgid "Years"
msgstr "ปี"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.invitation_page_anonymous
msgid "Yes I'm going."
msgstr "ใช่ ฉันกำลังไป"

#. module: calendar
#: code:addons/calendar/models/calendar_event.py:0
#, python-format
msgid "You can't update a recurrence without base event."
msgstr "คุณไม่สามารถอัปเดตการเกิดซ้ำโดยไม่มีฐานอีเวนต์"

#. module: calendar
#: code:addons/calendar/models/calendar_attendee.py:0
#, python-format
msgid "You cannot duplicate a calendar attendee."
msgstr "คุณไม่สามารถทำซ้ำปฏิทินผู้เข้าร่วมได้"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "You have to choose at least one day in the week"
msgstr "คุณต้องเลือกอย่างน้อยหนึ่งวันในหนึ่งสัปดาห์"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "day %s"
msgstr "วัน %s"

#. module: calendar
#: model_terms:ir.ui.view,arch_db:calendar.view_calendar_event_form
msgid "e.g. Business Lunch"
msgstr "เช่น มื้อเที่ยงเพื่อธุรกิจ"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "for %s events"
msgstr "สำหรับ %s อีเวนต์"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on %s"
msgstr "เมื่อ %s"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "on the %(position)s %(weekday)s"
msgstr "ที่ %(position)s %(weekday)s"

#. module: calendar
#: code:addons/calendar/models/calendar_recurrence.py:0
#, python-format
msgid "until %s"
msgstr "จนกระทั้ง %s"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_reminder
msgid "{{ object.event_id.name }} - Reminder"
msgstr "{{ object.event_id.name }} - ตัวเตือนความจำ"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_changedate
msgid "{{ object.event_id.name }}: Date updated"
msgstr "{{ object.event_id.name }}: อัปเดตวันที่"

#. module: calendar
#: model:mail.template,subject:calendar.calendar_template_meeting_update
msgid "{{object.name}}: Event update"
msgstr "{{object.name}}: อัปเดตอีเวนต์"
