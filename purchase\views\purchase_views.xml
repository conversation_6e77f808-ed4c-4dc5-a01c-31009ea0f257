<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- Top menu item -->
        <menuitem name="Purchase"
            id="menu_purchase_root"
            groups="group_purchase_manager,group_purchase_user"
            web_icon="purchase,static/description/icon.png"
            sequence="135"/>

        <menuitem id="menu_procurement_management" name="Orders"
            parent="menu_purchase_root" sequence="1" />

        <!--Supplier menu-->
        <menuitem id="menu_procurement_management_supplier_name" name="Vendors"
            parent="menu_procurement_management"
            action="account.res_partner_action_supplier" sequence="15"/>

        <menuitem id="menu_purchase_config" name="Configuration" parent="menu_purchase_root" sequence="100" groups="group_purchase_manager"/>

        <menuitem
           action="product.product_supplierinfo_type_action" id="menu_product_pricelist_action2_purchase"
           parent="menu_purchase_config" sequence="1"/>

        <menuitem
            id="menu_product_in_config_purchase" name="Products"
            parent="menu_purchase_config" sequence="30"/>

        <record model="ir.ui.menu" id="menu_product_in_config_purchase">
            <field name="groups_id" eval="False"/>
        </record>

        <menuitem id="menu_product_attribute_action"
            action="product.attribute_action" name="Product Attributes"
            parent="purchase.menu_product_in_config_purchase"  groups="product.group_product_variant" sequence="1"/>

        <menuitem
            action="product.product_category_action_form" id="menu_product_category_config_purchase"
            parent="purchase.menu_product_in_config_purchase" sequence="3" />

         <menuitem
            id="menu_unit_of_measure_in_config_purchase" name="Units of Measures"
            parent="menu_purchase_config" sequence="40" groups="uom.group_uom" />

        <menuitem
              action="uom.product_uom_form_action" id="menu_purchase_uom_form_action" active="False"
              parent="purchase.menu_unit_of_measure_in_config_purchase" sequence="5" groups="base.group_no_one"/>

        <menuitem
             action="uom.product_uom_categ_form_action" id="menu_purchase_uom_categ_form_action"
             parent="purchase.menu_unit_of_measure_in_config_purchase" sequence="10" groups="uom.group_uom"/>

    <!-- Products Control Menu -->
    <menuitem id="menu_purchase_products" name="Products" parent="purchase.menu_purchase_root" sequence="5"/>

    <record id="product_normal_action_puchased" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,tree,form,activity</field>
        <field name="context">{"search_default_filter_to_purchase":1, "purchase_product_template": 1}</field>
        <field name="search_view_id" ref="product.product_template_search_view"/>
        <field name="view_id" eval="False"/> <!-- Force empty -->
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No product found. Let's create one!
          </p><p>
            You must define a product for everything you sell or purchase,
            whether it's a storable product, a consumable or a service.
          </p>
        </field>
    </record>

      <!-- Product menu-->
      <menuitem name="Products" id="menu_procurement_partner_contact_form" action="product_normal_action_puchased"
          parent="menu_purchase_products" sequence="20"/>

        <record id="product_product_action" model="ir.actions.act_window">
            <field name="name">Product Variants</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">tree,kanban,form,activity</field>
            <field name="search_view_id" ref="product.product_search_form_view"/>
            <field name="context">{"search_default_filter_to_purchase": 1}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new product variant
              </p><p>
                You must define a product for everything you sell or purchase,
                whether it's a storable product, a consumable or a service.
              </p>
            </field>
        </record>

        <menuitem id="product_product_menu" name="Product Variants" action="product_product_action"
            parent="menu_purchase_products" sequence="21" groups="product.group_product_variant"/>

        <record model="ir.ui.view" id="purchase_order_calendar">
            <field name="name">purchase.order.calendar</field>
            <field name="model">purchase.order</field>
            <field name="priority" eval="2"/>
            <field name="arch" type="xml">
                <calendar string="Calendar View" date_start="date_calendar_start" color="partner_id" hide_time="true" event_limit="5" create="false">
                    <field name="currency_id" invisible="1"/>
                    <field name="partner_ref"/>
                    <field name="amount_total" widget="monetary"/>
                    <field name="partner_id" filters="1"/>
                </calendar>
            </field>
        </record>
        <record model="ir.ui.view" id="purchase_order_pivot">
            <field name="name">purchase.order.pivot</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <pivot string="Purchase Order" display_quantity="1" sample="1">
                    <field name="partner_id" type="row"/>
                    <field name="amount_total" type="measure"/>
                </pivot>
            </field>
        </record>
        <record model="ir.ui.view" id="purchase_order_graph">
            <field name="name">purchase.order.graph</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <graph string="Purchase Order" sample="1">
                    <field name="partner_id"/>
                    <field name="amount_total" type="measure"/>
                </graph>
            </field>
        </record>

        <record id="purchase_order_form" model="ir.ui.view">
            <field name="name">purchase.order.form</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <form string="Purchase Order" class="o_purchase_order">
                <header>
                    <button name="action_rfq_send" states="draft" string="Send by Email" type="object" context="{'send_rfq':True}" class="oe_highlight" data-hotkey="g"/>
                    <button name="print_quotation" string="Print RFQ" type="object" states="draft" class="oe_highlight" groups="base.group_user" data-hotkey="k"/>
                    <button name="button_confirm" type="object" states="sent" string="Confirm Order" class="oe_highlight" id="bid_confirm" data-hotkey="v"/>
                    <button name="button_approve" type="object" states='to approve' string="Approve Order" class="oe_highlight" groups="purchase.group_purchase_manager" data-hotkey="z"/>
                    <button name="action_create_invoice" string="Create Bill" type="object" class="oe_highlight" context="{'create_bill':True}" attrs="{'invisible': ['|', ('state', 'not in', ('purchase', 'done')), ('invoice_status', 'in', ('no', 'invoiced'))]}" data-hotkey="w"/>
                    <button name="action_rfq_send" states="sent" string="Re-Send by Email" type="object" context="{'send_rfq':True}" data-hotkey="g"/>
                    <button name="print_quotation" string="Print RFQ" type="object" states="sent" groups="base.group_user" data-hotkey="k"/>
                    <button name="button_confirm" type="object" states="draft" string="Confirm Order" id="draft_confirm"/>
                    <button name="action_rfq_send" states="purchase" string="Send PO by Email" type="object" context="{'send_rfq':False}" data-hotkey="g"/>
                    <button name="confirm_reminder_mail" string="Confirm Receipt Date" type="object" attrs="{'invisible': ['|','|', ('state', 'not in', ('purchase', 'done')), ('mail_reminder_confirmed', '=', True), ('date_planned', '=', False)]}" groups="base.group_no_one" data-hotkey="o"/>
                    <button name="action_create_invoice" string="Create Bill" type="object" context="{'create_bill':True}" attrs="{'invisible': ['|', '|', ('state', 'not in', ('purchase', 'done')), ('invoice_status', 'not in', ('no', 'invoiced')), ('order_line', '=', [])]}" data-hotkey="w"/>
                    <button name="button_draft" states="cancel" string="Set to Draft" type="object" data-hotkey="o"/>
                    <button name="button_cancel" states="draft,to approve,sent,purchase" string="Cancel" type="object" data-hotkey="x" />
                    <button name="button_done" type="object" string="Lock" states="purchase" data-hotkey="l"/>
                    <button name="button_unlock" type="object" string="Unlock" states="done" groups="purchase.group_purchase_manager" data-hotkey="l"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,sent,purchase" readonly="1"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object"  name="action_view_invoice"
                            class="oe_stat_button"
                            icon="fa-pencil-square-o" attrs="{'invisible':['|', ('invoice_count', '=', 0), ('state', 'in', ('draft','sent','to approve'))]}">
                            <field name="invoice_count" widget="statinfo" string="Vendor Bills"/>
                            <field name='invoice_ids' invisible="1"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <span class="o_form_label" attrs="{'invisible': [('state','not in',('draft','sent'))]}">Request for Quotation </span>
                        <span class="o_form_label" attrs="{'invisible': [('state','in',('draft','sent'))]}">Purchase Order </span>
                        <h1>
                            <field name="priority" widget="priority" class="mr-3"/>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="partner_id" widget="res_partner_many2one" context="{'res_partner_search_mode': 'supplier', 'show_vat': True}"
                                placeholder="Name, TIN, Email, or Reference"
                            />
                            <field name="partner_ref"/>
                            <field name="currency_id" groups="base.group_multi_currency" force_save="1"/>
                            <field name="id" invisible="1"/>
                        </group>
                        <group>
                            <field name="date_order" attrs="{'invisible': [('state','in',('purchase','done'))]}"/>
                            <label for="date_approve" attrs="{'invisible': [('state','not in',('purchase','done'))]}"/>
                            <div name="date_approve" attrs="{'invisible': [('state','not in',('purchase','done'))]}" class="o_row">
                                <field name="date_approve"/>
                                <field name="mail_reception_confirmed" invisible="1"/>
                                <span class="text-muted" attrs="{'invisible': [('mail_reception_confirmed','=', False)]}">(confirmed by vendor)</span>
                            </div>
                            <label for="date_planned"/>
                            <div name="date_planned_div" class="o_row">
                                <field name="date_planned" attrs="{'readonly': [('state', 'not in', ('draft', 'sent', 'to approve', 'purchase'))]}"/>
                                <field name="mail_reminder_confirmed" invisible="1"/>
                                <span class="text-muted" attrs="{'invisible': [('mail_reminder_confirmed', '=', False)]}">(confirmed by vendor)</span>
                            </div>
                            <label for="receipt_reminder_email" invisible='1'/>
                            <div name="reminder" class="o_row" groups='purchase.group_send_reminder' title="Automatically send a confirmation email to the vendor X days before the expected receipt date, asking him to confirm the exact date.">
                                <field name="receipt_reminder_email"/>
                                <span>Ask confirmation</span>
                                <div class="o_row oe_inline" attrs="{'invisible': [('receipt_reminder_email', '=', False)]}">
                                    <field name="reminder_date_before_receipt" class="oe_inline"/>
                                    day(s) before
                                    <widget name='toaster_button' button_name="send_reminder_preview" title="Preview the reminder email by sending it to yourself." attrs="{'invisible': [('id', '=', False)]}"/>
                                </div>
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page string="Products" name="products">
                            <field name="tax_country_id" invisible="1"/>
                            <field name="order_line"
                                widget="section_and_note_one2many"
                                mode="tree,kanban"
                                context="{'default_state': 'draft'}"
                                attrs="{'readonly': [('state', 'in', ('done', 'cancel'))]}">
                                <tree string="Purchase Order Lines" editable="bottom">
                                    <control>
                                        <create name="add_product_control" string="Add a product"/>
                                        <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                        <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                                    </control>
                                    <field name="display_type" invisible="1"/>
                                    <field name="currency_id" invisible="1"/>
                                    <field name="state" invisible="1"/>
                                    <field name="product_type" invisible="1"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="invoice_lines" invisible="1"/>
                                    <field name="sequence" widget="handle"/>
                                    <field
                                        name="product_id"
                                        attrs="{
                                            'readonly': [('state', 'in', ('purchase', 'to approve','done', 'cancel'))],
                                            'required': [('display_type', '=', False)],
                                        }"
                                        context="{'partner_id':parent.partner_id, 'quantity':product_qty, 'company_id': parent.company_id}"
                                        force_save="1" domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                    <field name="name" widget="section_and_note_text"/>
                                    <field name="date_planned" optional="hide" attrs="{'required': [('display_type', '=', False)]}" force_save="1"/>
                                    <field name="account_analytic_id" optional="hide" context="{'default_partner_id':parent.partner_id}" groups="analytic.group_analytic_accounting" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                    <field name="analytic_tag_ids" optional="hide" groups="analytic.group_analytic_tags" widget="many2many_tags" options="{'color_field': 'color'}" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                    <field name="product_qty"/>
                                    <field name="qty_received_manual" invisible="1"/>
                                    <field name="qty_received_method" invisible="1"/>
                                    <field name="qty_received" string="Received" attrs="{'column_invisible': [('parent.state', 'not in', ('purchase', 'done'))], 'readonly': [('qty_received_method', '!=', 'manual')]}" optional="show"/>
                                    <field name="qty_invoiced" string="Billed" attrs="{'column_invisible': [('parent.state', 'not in', ('purchase', 'done'))]}" optional="show"/>
                                    <field name="product_uom" string="UoM" groups="uom.group_uom"
                                        attrs="{
                                            'readonly': [('state', 'in', ('purchase', 'done', 'cancel'))],
                                            'required': [('display_type', '=', False)]
                                        }"
                                        force_save="1" optional="show"/>
                                    <field name="product_packaging_qty" attrs="{'invisible': ['|', ('product_id', '=', False), ('product_packaging_id', '=', False)]}" groups="product.group_stock_packaging" optional="show"/>
                                    <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" optional="show"/>
                                    <field name="price_unit" attrs="{'readonly': [('qty_invoiced', '!=', 0)]}"/>
                                    <field name="taxes_id" widget="many2many_tags" domain="[('type_tax_use','=','purchase'), ('company_id', '=', parent.company_id), ('country_id', '=', parent.tax_country_id)]" context="{'default_type_tax_use': 'purchase', 'search_view_ref': 'account.account_tax_view_search'}" options="{'no_create': True}" optional="show"/>
                                    <field name="price_subtotal" widget="monetary"/>
                                    <field name="price_total" invisible="1"/>
                                    <field name="price_tax" invisible="1"/>
                                </tree>
                                <form string="Purchase Order Line">
                                        <field name="state" invisible="1"/>
                                        <field name="display_type" invisible="1"/>
                                        <group attrs="{'invisible': [('display_type', '!=', False)]}">
                                            <group>
                                                <field name="product_uom_category_id" invisible="1"/>
                                                <field name="product_id"
                                                       context="{'partner_id': parent.partner_id}"
                                                       widget="many2one_barcode"
                                                       domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
                                                />
                                                <label for="product_qty"/>
                                                <div class="o_row">
                                                    <field name="product_qty"/>
                                                    <field name="product_uom" groups="uom.group_uom" attrs="{'required': [('display_type', '=', False)]}"/>
                                                </div>
                                                <field name="qty_received_method" invisible="1"/>
                                                <field name="qty_received" string="Received Quantity" attrs="{'invisible': [('parent.state', 'not in', ('purchase', 'done'))], 'readonly': [('qty_received_method', '!=', 'manual')]}"/>
                                                <field name="qty_invoiced" string="Billed Quantity" attrs="{'invisible': [('parent.state', 'not in', ('purchase', 'done'))]}"/>
                                                <field name="product_packaging_qty" attrs="{'invisible': ['|', ('product_id', '=', False), ('product_packaging_id', '=', False)]}" groups="product.group_stock_packaging"/>
                                                <field name="product_packaging_id" attrs="{'invisible': [('product_id', '=', False)]}" context="{'default_product_id': product_id, 'tree_view_ref':'product.product_packaging_tree_view', 'form_view_ref':'product.product_packaging_form_view'}" groups="product.group_stock_packaging" />
                                                <field name="price_unit"/>
                                                <field name="taxes_id" widget="many2many_tags" domain="[('type_tax_use', '=', 'purchase'), ('company_id', '=', parent.company_id), ('country_id', '=', parent.tax_country_id)]" options="{'no_create': True}"/>
                                            </group>
                                            <group>
                                                <field name="date_planned" widget="date" attrs="{'required': [('display_type', '=', False)]}"/>
                                                <field name="account_analytic_id" colspan="2" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" groups="analytic.group_analytic_accounting"/>
                                                <field name="analytic_tag_ids" groups="analytic.group_analytic_tags" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" widget="many2many_tags" options="{'color_field': 'color'}"/>
                                            </group>
                                            <group colspan="12">
                                            <notebook>
                                                <page string="Notes" name="notes">
                                                    <field name="name"/>
                                                </page>
                                                <page string="Invoices and Incoming Shipments" name="invoices_incoming_shiptments">
                                                    <field name="invoice_lines"/>
                                                </page>
                                            </notebook>
                                            </group>
                                        </group>
                                        <label for="name" string="Section Name (eg. Products, Services)" attrs="{'invisible': [('display_type', '!=', 'line_section')]}"/>
                                        <label for="name" string="Note" attrs="{'invisible': [('display_type', '!=', 'line_note')]}"/>
                                        <field name="name" nolabel="1"  attrs="{'invisible': [('display_type', '=', False)]}"/>
                                 </form>
                                 <kanban class="o_kanban_mobile">
                                     <field name="name"/>
                                     <field name="product_id"/>
                                     <field name="product_qty"/>
                                     <field name="product_uom" groups="uom.group_uom"/>
                                     <field name="price_subtotal"/>
                                     <field name="price_tax" invisible="1"/>
                                     <field name="price_total" invisible="1"/>
                                     <field name="price_unit"/>
                                     <field name="display_type"/>
                                     <field name="taxes_id" invisible="1"/>
                                     <templates>
                                         <t t-name="kanban-box">
                                             <div t-attf-class="oe_kanban_card oe_kanban_global_click {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                                 <t t-if="!record.display_type.raw_value">
                                                     <div class="row">
                                                         <div class="col-8">
                                                             <strong>
                                                                 <span t-esc="record.product_id.value"/>
                                                             </strong>
                                                         </div>
                                                         <div class="col-4">
                                                             <strong>
                                                                 <span t-esc="record.price_subtotal.value" class="float-right text-right"/>
                                                             </strong>
                                                         </div>
                                                     </div>
                                                     <div class="row">
                                                         <div class="col-12 text-muted">
                                                             <span>
                                                                 Quantity:
                                                                 <t t-esc="record.product_qty.value"/>
                                                                 <t t-esc="record.product_uom.value"/>
                                                             </span>
                                                         </div>
                                                     </div>
                                                     <div class="row">
                                                         <div class="col-12 text-muted">
                                                             <span>
                                                                 Unit Price:
                                                                 <t t-esc="record.price_unit.value"/>
                                                             </span>
                                                         </div>
                                                     </div>
                                                 </t>
                                                 <div
                                                     t-elif="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'"
                                                     class="row">
                                                     <div class="col-12">
                                                         <span t-esc="record.name.value"/>
                                                     </div>
                                                 </div>
                                             </div>
                                         </t>
                                     </templates>
                                 </kanban>
                            </field>
                            <group>
                                <group>
                                    <field name="notes" nolabel="1" placeholder="Define your terms and conditions ..."/>
                                </group>
                                <group class="oe_subtotal_footer oe_right">
                                    <field name="tax_totals_json" widget="account-tax-totals-field" nolabel="1" colspan="2"/>
                                </group>
                            </group>
                            <div class="oe_clear"/>
                        </page>
                        <page string="Other Information" name="purchase_delivery_invoice">
                            <group>
                                <group name="other_info">
                                    <field name="user_id" domain="[('share', '=', False)]" widget="many2one_avatar_user"/>
                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                                    <field name="origin"/>
                                </group>
                                <group name="invoice_info">
                                    <field name="invoice_status" attrs="{'invisible': [('state', 'in', ('draft', 'sent', 'to approve', 'cancel'))]}"/>
                                    <field name="payment_term_id" attrs="{'readonly': ['|', ('invoice_status','=', 'invoiced'), ('state', '=', 'done')]}" options="{'no_create': True}"/>
                                    <field name="fiscal_position_id" options="{'no_create': True}" attrs="{'readonly': ['|', ('invoice_status','=', 'invoiced'), ('state', '=', 'done')]}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
                </form>
            </field>
        </record>

       <record id="view_purchase_order_filter" model="ir.ui.view">
            <field name="name">request.quotation.select</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <search string="Search Purchase Order">
                    <field name="name" string="Order"
                        filter_domain="['|', '|', ('name', 'ilike', self), ('partner_ref', 'ilike', self), ('partner_id', 'child_of', self)]"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="user_id"/>
                    <field name="product_id"/>
                    <field name="origin"/>
                    <filter name="my_purchases" string="My Purchases" domain="[('user_id', '=', uid)]"/>
                    <filter string="Starred" name="starred" domain="[('priority', '=', '1')]"/>
                    <separator/>
                    <filter name="draft" string="RFQs" domain="[('state', 'in', ('draft', 'sent', 'to approve'))]"/>
                    <separator/>
                    <filter name="approved" string="Purchase Orders" domain="[('state', 'in', ('purchase', 'done'))]"/>
                    <filter name="to_approve" string="To Approve" domain="[('state', '=', 'to approve')]"/>
                    <separator/>
                    <filter name="order_date" string="Order Date" date="date_order"/>
                    <filter name="draft_rfqs" string="Draft RFQs" domain="[('state', '=', 'draft')]"/>
                    <filter name="waiting_rfqs" string="Waiting RFQs" domain="[('state', '=', 'sent'), ('date_order', '&gt;=', datetime.datetime.now())]"/>
                    <filter name="late_rfqs" string="Late RFQs" domain="[('state', 'in', ['draft', 'sent', 'to approve']),('date_order', '&lt;', datetime.datetime.now())]"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Warnings" name="activities_exception"
                        domain="[('activity_exception_decoration', '!=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Vendor" name="vendor" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Purchase Representative" name="representative" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="Order Date" name="order_date" domain="[]" context="{'group_by': 'date_order'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="purchase_order_view_search" model="ir.ui.view">
            <field name="name">purchase.order.select</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <search string="Search Purchase Order">
                    <field name="name" string="Order"
                        filter_domain="['|', '|', ('name', 'ilike', self), ('partner_ref', 'ilike', self), ('partner_id', 'child_of', self)]"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="user_id"/>
                    <field name="product_id"/>
                    <filter name="my_Orders" string="My Orders" domain="[('user_id', '=', uid)]"/>
                    <filter string="Starred" name="starred" domain="[('priority', '=', '1')]"/>
                    <separator/>
                    <filter name="unconfirmed" string="Not Acknowledged" domain="[('mail_reception_confirmed', '=', False), ('state', '=', 'purchase')]"/>
                    <filter name="not_invoiced" string="Waiting Bills" domain="[('invoice_status', '=', 'to invoice')]" help="Purchase orders that include lines not invoiced."/>
                    <filter name="invoiced" string="Bills Received" domain="[('invoice_status', '=', 'invoiced')]" help="Purchase orders that have been invoiced."/>
                    <separator/>
                    <filter name="order_date" string="Order Date" date="date_order"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Warnings" name="activities_exception"
                        domain="[('activity_exception_decoration', '!=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Vendor" name="vendor" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Purchase Representative" name="representative" domain="[]" context="{'group_by': 'user_id'}"/>
                        <filter string="Order Date" name="order_date" domain="[]" context="{'group_by': 'date_order'}"/>
                    </group>
                </search>
            </field>
        </record>


        <!-- Purchase Orders Kanban View  -->
        <record model="ir.ui.view" id="view_purchase_order_kanban">
            <field name="name">purchase.order.kanban</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" js_class="purchase_kanban_dashboard" sample="1" quick_create="false">
                    <field name="name"/>
                    <field name="partner_id" readonly="1"/>
                    <field name="amount_total"/>
                    <field name="state"/>
                    <field name="date_order"/>
                    <field name="currency_id" readonly="1"/>
                    <field name="activity_state"/>
                    <progressbar field="activity_state" colors='{"planned": "success", "today": "warning", "overdue": "danger"}'/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top mb16">
                                    <field name="priority" widget="priority"/>
                                    <div class="o_kanban_record_headings ml-1">
                                        <strong class="o_kanban_record_title"><span><t t-esc="record.partner_id.value"/></span></strong>
                                    </div>
                                    <strong><field name="amount_total" widget="monetary"/></strong>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span><t t-esc="record.name.value"/> <t t-esc="record.date_order.value and record.date_order.value.split(' ')[0] or False"/></span>
                                        <field name="activity_ids" widget="kanban_activity"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <field name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'cancel': 'default', 'done': 'success', 'approved': 'warning'}}"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="purchase_order_tree" model="ir.ui.view">
            <field name="name">purchase.order.tree</field>
            <field name="model">purchase.order</field>
            <field name="priority" eval="1"/>
            <field name="arch" type="xml">
                <tree string="Purchase Order"
                      decoration-muted="state=='cancel'" sample="1">
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="partner_ref" optional="hide"/>
                    <field name="name" string="Reference" readonly="1" decoration-info="state in ('draft','sent')"/>
                    <field name="date_order" invisible="not context.get('quotation_only', False)" optional="show"/>
                    <field name="date_approve" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="partner_id" readonly="1"/>
                    <field name="company_id" readonly="1" options="{'no_create': True}"
                        groups="base.group_multi_company" optional="show"/>
                    <field name="date_planned" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="user_id" optional="show"/>
                    <field name="origin" optional="show"/>
                    <field name="amount_untaxed" sum="Total Untaxed amount" string="Untaxed" widget="monetary" optional="hide"/>
                    <field name="amount_total" sum="Total amount" widget="monetary" optional="show"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="state" optional="show"/>
                    <field name="invoice_status" optional="hide"/>
                    <field name="activity_exception_decoration" widget="activity_exception"/>
                </tree>
            </field>
        </record>

        <!-- Unfortunately we want the purchase kpis table to only show up in some list views,
             so we have to duplicate code to support both view versions -->
        <record id="purchase_order_kpis_tree" model="ir.ui.view">
            <field name="name">purchase.order.inherit.purchase.order.tree</field>
            <field name="model">purchase.order</field>
            <field name="priority" eval="10"/>
            <field name="arch" type="xml">
                <tree string="Purchase Order" decoration-info="state in ['draft', 'sent']"
                decoration-muted="state == 'cancel'"
                class="o_purchase_order" js_class="purchase_list_dashboard" sample="1">
                    <header>
                        <button name="action_create_invoice" type="object" string="Create Bills"/>
                    </header>
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="partner_ref" optional="hide"/>
                    <field name="name" string="Reference" readonly="1" decoration-bf="1"/>
                    <field name="date_approve" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="partner_id" readonly="1"/>
                    <field name="company_id" readonly="1" options="{'no_create': True}"
                        groups="base.group_multi_company" optional="show"/>
                    <field name="date_planned" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="user_id" optional="show" widget="many2one_avatar_user"/>
                    <field name="date_order" attrs="{'invisible': ['|', '|', ('state', '=', 'purchase'), ('state', '=', 'done'), ('state', '=', 'cancel')]}"
                        invisible="not context.get('quotation_only', False)" widget="remaining_days" optional="show"/>
                    <field name="activity_ids" widget="list_activity" optional="show"/>
                    <field name="origin" optional="show"/>
                    <field name="amount_untaxed" sum="Total Untaxed amount" string="Untaxed" widget="monetary" optional="hide"/>
                    <field name="amount_total" sum="Total amount" widget="monetary" optional="show" decoration-bf="state in ['purchase', 'done']"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="state" optional="show" widget="badge" decoration-success="state == 'purchase' or state == 'done'"
                        decoration-warning="state == 'to approve'" decoration-info="state == 'draft' or state == 'sent'"/>
                    <field name="invoice_status" optional="hide"/>
                </tree>
            </field>
        </record>

        <record id="purchase_order_view_tree" model="ir.ui.view">
            <field name="name">purchase.order.view.tree</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <tree decoration-muted="state == 'cancel'"
                    decoration-info="invoice_status == 'to invoice'"
                    string="Purchase Order"
                    class="o_purchase_order"
                    sample="1">
                    <header>
                        <button name="action_create_invoice" type="object" string="Create Bills"/>
                    </header>
                    <field name="priority" optional="show" widget="priority" nolabel="1"/>
                    <field name="partner_ref" optional="hide"/>
                    <field name="name" string="Reference" readonly="1" decoration-bf="1" decoration-info="state in ('draft','sent')"/>
                    <field name="date_approve" widget="date" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="partner_id"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                    <field name="date_planned" invisible="context.get('quotation_only', False)" optional="show"/>
                    <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                    <field name="date_order" invisible="not context.get('quotation_only', False)" optional="show"/>
                    <field name="activity_ids" widget="list_activity" optional="show"/>
                    <field name="origin" optional="show"/>
                    <field name="amount_untaxed" sum="Total Untaxed amount" string="Untaxed" widget="monetary" optional="hide"/>
                    <field name="amount_total" sum="Total amount" widget="monetary" optional="show" decoration-bf="1"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="state" invisible="1"/>
                    <field name="invoice_status" widget="badge" decoration-success="invoice_status == 'invoiced'" decoration-info="invoice_status == 'to invoice'" optional="show"/>
                </tree>
            </field>
        </record>

        <record id="purchase_order_view_activity" model="ir.ui.view">
            <field name="name">purchase.order.activity</field>
            <field name="model">purchase.order</field>
            <field name="arch" type="xml">
                <activity string="Purchase Order">
                    <templates>
                        <div t-name="activity-box">
                            <div>
                                <field name="name" display="full"/>
                                <field name="partner_id" muted="1" display="full"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="purchase_rfq" model="ir.actions.act_window">
            <field name="name">Requests for Quotation</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.order</field>
            <field name="view_mode">tree,kanban,form,pivot,graph,calendar,activity</field>
            <field name="view_id" ref="purchase_order_kpis_tree"/>
            <field name="domain">[]</field>
            <field name="search_view_id" ref="view_purchase_order_filter"/>
            <field name="context">{'quotation_only': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No request for quotation found. Let's create one!
              </p><p>
                Requests for quotation are documents that will be sent to your suppliers to request prices for different products you consider buying.
                Once an agreement has been found with the supplier, they will be confirmed and turned into purchase orders.
              </p>
            </field>
        </record>
        <menuitem action="purchase_rfq" id="menu_purchase_rfq"
            parent="menu_procurement_management"
            sequence="0"/>

        <record id="purchase_form_action" model="ir.actions.act_window">
            <field name="name">Purchase Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.order</field>
            <field name="view_mode">tree,kanban,form,pivot,graph,calendar,activity</field>
            <field name="view_id" ref="purchase_order_view_tree"/>
            <field name="domain">[('state','in',('purchase', 'done'))]</field>
            <field name="search_view_id" ref="purchase_order_view_search"/>
            <field name="context">{}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No purchase order found. Let's create one!
              </p><p>
                Once you ordered your products to your supplier, confirm your request for quotation and it will turn into a purchase order.
              </p>
            </field>
        </record>
        <menuitem action="purchase_form_action" id="menu_purchase_form_action" parent="menu_procurement_management" sequence="6"/>

        <record id="purchase_order_line_tree" model="ir.ui.view">
            <field name="name">purchase.order.line.tree</field>
            <field name="model">purchase.order.line</field>
            <field name="arch" type="xml">
                <tree string="Purchase Order Lines" create="false">
                    <field name="order_id"/>
                    <field name="name"/>
                    <field name="partner_id" string="Vendor" />
                    <field name="product_id"/>
                    <field name="price_unit"/>
                    <field name="product_qty"/>
                    <field name="product_uom" groups="uom.group_uom"/>
                    <field name="price_subtotal" widget="monetary"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="date_planned"  widget="date"/>
                </tree>
            </field>
        </record>

        <record id="purchase_order_line_form2" model="ir.ui.view">
            <field name="name">purchase.order.line.form2</field>
            <field name="model">purchase.order.line</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <form string="Purchase Order Line" create="false">
                    <sheet>
                        <label for="order_id"/>
                        <h1>
                            <field name="order_id" class="oe_inline"/>
                            <label string="," for="date_order" attrs="{'invisible':[('date_order','=',False)]}"/>
                            <field name="date_order" class="oe_inline"/>
                        </h1>
                        <label for="partner_id"/>
                        <h2><field name="partner_id"/></h2>
                        <group>
                            <group>
                                <field name="product_id" readonly="1"/>
                                <label for="product_qty"/>
                                <div class="o_row">
                                    <field name="product_qty" readonly="1"/>
                                    <field name="product_uom" readonly="1" groups="uom.group_uom"/>
                                </div>
                                <field name="price_unit"/>
                            </group>
                            <group>
                                <field name="taxes_id" widget="many2many_tags"
                                    domain="[('type_tax_use', '=', 'purchase')]"/>
                                <field name="date_planned" widget="date" readonly="1"/>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                                <field name="account_analytic_id" colspan="4" groups="analytic.group_analytic_accounting"/>
                            </group>
                        </group>
                        <field name="name"/>
                        <separator string="Manual Invoices"/>
                        <field name="invoice_lines"/>
                    </sheet>
                </form>
            </field>
        </record>
          <record id="purchase_order_line_search" model="ir.ui.view">
            <field name="name">purchase.order.line.search</field>
            <field name="model">purchase.order.line</field>
            <field name="arch" type="xml">
                <search string="Search Purchase Order">
                    <field name="order_id"/>
                    <field name="product_id"/>
                    <field name="partner_id" string="Vendor" filter_domain="[('partner_id', 'child_of', self)]"/>
                    <filter name="hide_cancelled" string="Hide cancelled lines" domain="[('state', '!=', 'cancel')]"/>
                    <group expand="0" string="Group By">
                        <filter name="groupby_supplier" string="Vendor" domain="[]" context="{'group_by' : 'partner_id'}" />
                        <filter name="groupby_product" string="Product" domain="[]" context="{'group_by' : 'product_id'}" />
                        <filter string="Order Reference" name="order_reference" domain="[]" context="{'group_by' :'order_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by' : 'state'}" />
                    </group>
                </search>
            </field>
        </record>

    <record id="action_purchase_batch_bills" model="ir.actions.server">
        <field name="name">Create Vendor Bills</field>
        <field name="model_id" ref="purchase.model_purchase_order"/>
        <field name="binding_model_id" ref="purchase.model_purchase_order"/>
        <field name='groups_id' eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
            if records:
                action = records.action_create_invoice()
        </field>
    </record>

    <record id="action_purchase_send_reminder" model="ir.actions.server">
        <field name="name">Send Reminder</field>
        <field name="model_id" ref="purchase.model_purchase_order"/>
        <field name="binding_model_id" ref="purchase.model_purchase_order"/>
        <field name='groups_id' eval="[(4, ref('purchase.group_send_reminder'))]"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                action = records._send_reminder_mail(send_single=True)
        </field>
    </record>

    <record id="action_accrued_expense_entry" model="ir.actions.act_window">
        <field name="name">Accrued Expense Entry</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.accrued.orders.wizard</field>
        <field name="view_mode">form</field>
        <field name="binding_model_id" ref="purchase.model_purchase_order"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
        <field name="target">new</field>
    </record>

    <!-- Dashboard action buttons -->
    <!-- Dashboard action buttons: End in List view -->
    <record id="purchase_action_dashboard_list" model="ir.actions.act_window">
        <field name="name">Requests for Quotation</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">form,tree,kanban,pivot,graph,calendar,activity</field>
        <field name="view_id" ref="purchase.purchase_order_kpis_tree"/>
        <field name="search_view_id" ref="view_purchase_order_filter"/>
        <field name="target">main</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No RFQs to display
            </p>
        </field>
    </record>

    <!-- Dashboard action buttons: End in Kanban view-->
    <record id="purchase_action_dashboard_kanban" model="ir.actions.act_window">
        <field name="name">Requests for Quotation</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">form,tree,kanban,pivot,graph,calendar,activity</field>
        <field name="view_id" ref="purchase.view_purchase_order_kanban"/>
        <field name="search_view_id" ref="view_purchase_order_filter"/>
        <field name="target">main</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No RFQs to display
            </p>
        </field>
    </record>

    <record id="action_rfq_form" model="ir.actions.act_window">
        <field name="name">Requests for Quotation</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="purchase.purchase_order_form"/>
        <field name="search_view_id" ref="view_purchase_order_filter"/>
        <field name="target">main</field>
    </record>

</odoo>
