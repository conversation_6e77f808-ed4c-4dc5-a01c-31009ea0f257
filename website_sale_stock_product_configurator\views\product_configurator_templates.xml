<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="website_sale_stock_modal" inherit_id="sale_product_configurator.product_quantity_config" name="Stocks Modal">
        <xpath expr="//input[@type='text'][hasclass('quantity')]" position="attributes">
          <attribute name='t-att-data-max'>max(product.sudo().free_qty - product.cart_qty, 1) if handle_stock and product.type == "product" and not product.allow_out_of_stock_order else None</attribute>
        </xpath>
        <xpath expr="//div[hasclass('css_quantity')]" position="after">
          <t t-if="handle_stock">
            <div class='availability_messages'/>
          </t>
        </xpath>
    </template>
</odoo>