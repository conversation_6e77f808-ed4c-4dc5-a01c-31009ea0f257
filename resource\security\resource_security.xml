<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record model="ir.rule" id="resource_calendar_leaves_rule_group_user_create">
        <field name="name">resource.calendar.leaves: employee reads own or global</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">['|', ('resource_id', '=', False), ('resource_id.user_id', 'in', [False, user.id])]</field>
		<field name="perm_write" eval="False"/>
		<field name="perm_create" eval="False"/>
		<field name="perm_unlink" eval="False"/>
    </record>

    <record model="ir.rule" id="resource_calendar_leaves_rule_group_user_modify">
        <field name="name">resource.calendar.leaves: employee modifies own</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        <field name="domain_force">[('resource_id', '!=', False), ('resource_id.user_id', 'in', [False, user.id])]</field>
		<field name="perm_read" eval="False"/>
    </record>

    <record model="ir.rule" id="resource_calendar_leaves_rule_group_admin_modify">
        <field name="name">resource.calendar.leaves: admin modifies global</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="groups" eval="[(4, ref('base.group_erp_manager'))]"/>
        <field name="domain_force">[('resource_id', '=', False)]</field>
        <field name="perm_read" eval="False"/>
    </record>

    <record id="resource_resource_multi_company" model="ir.rule">
        <field name="name">resource.resource multi-company</field>
        <field name="model_id" ref="model_resource_resource"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>

    <record model="ir.rule" id="resource_calendar_leaves_rule_multi_company">
        <field name="name">resource.calendar.leaves: multi-company rule</field>
        <field name="model_id" ref="model_resource_calendar_leaves"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
    </record>
</odoo>
