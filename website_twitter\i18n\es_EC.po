# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_twitter
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-01-25 18:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.website_twitter_snippet
msgid "<span class=\"oe_snippet_thumbnail_title\">Twitter Scroller</span>"
msgstr "<span class=\"oe_snippet_thumbnail_title\">Twitter Scroller</span>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Callback URL: </strong> leave it blank"
msgstr "<strong>URL de retorno: </strong> déjelo en blanco"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Description: </strong> Odoo Tweet Scroller"
msgstr "<strong>Descripción: </strong> Odoo Tweet Scroller"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Name: </strong> Odoo Tweet Scroller"
msgstr "<strong>Nombre: </strong> Odoo Tweet Scroller"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "<strong>Website: </strong>"
msgstr "<strong>Sitio Web: </strong>"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Accept terms of use and click on the Create button at the bottom"
msgstr ""
"Aceptar términos de uso y pulsar en el botón Crear de la parte inferior"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:38
#, python-format
msgid ""
"Authentication credentials were missing or incorrect. Maybe screen name "
"tweets are protected."
msgstr ""
"Las credenciales de autenticación faltan o son incorrectas. Quizás sus "
"tweets son protegidos."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Copy/Paste API Key and Secret below"
msgstr "Copiar/Pegar clave API y palabra secreta a continuación"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Create a new Twitter application on"
msgstr "Crear una nueva aplicación Twiter en"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_create_date
msgid "Created on"
msgstr "Creado en"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid ""
"Enter the screen name from which you want to load favorite Tweets (does not "
"need to be the same as the API keys)"
msgstr ""
"Introduzca el nombre desde el que quiere cargar sus tuits favoritos (no "
"necesita ser el mismo que el de la clave API)"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_screen_name
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_screen_name
msgid "Get favorites from this screen name"
msgstr "Obtener favoritos desde este nombre"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:49
#, python-format
msgid "HTTP Error: Something is misconfigured"
msgstr "Error HTTP: Algo está mal configurado"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "How to configure the Twitter API access"
msgstr "Cómo configurar el acceso al API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_id
msgid "ID"
msgstr "ID (identificación)"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:62
#, python-format
msgid "Internet connection refused"
msgstr "Conexión a Internet negada"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:64
#: code:addons/website_twitter/models/twitter_config.py:65
#, python-format
msgid "Please double-check your Twitter API Key and Secret!"
msgstr "¡Por favor, compruebe su API Key y Secret de Twitter!"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:25
#, python-format
msgid ""
"Please set a Twitter screen name to load favorites from, in the Website "
"Settings (it does not have to be yours)"
msgstr ""
"Establezca por favor un nombre de Twitter desde el que cargar los favoritos, "
"en la configuración del sitio web (no tiene por qué ser el suyo)"

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:21
#, python-format
msgid "Please set the Twitter API Key and Secret in the Website Settings."
msgstr ""
"Establezca por favor una clave y una palabra secreta del API en la "
"configuración del sitio web."

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:35
#, python-format
msgid "Reload"
msgstr "Recargar"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:40
#, python-format
msgid ""
"Request cannot be served due to the applications rate limit having been "
"exhausted for the resource."
msgstr ""
"La solicitud no se puede transmitir debido al límite de velocidad de las "
"aplicaciones que se han agotado para el recurso."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_screen_name
msgid "Screen Name"
msgstr "Nombre"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_screen_name
msgid ""
"Screen Name of the Twitter Account from which you want to load favorites.It "
"does not have to match the API Key/Secret."
msgstr ""
"Nombre de la cuenta de Twitter desde la que se desean cargar los favoritos. "
"No tiene porque ser la misma que la de la clave de API."

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid ""
"Set your Twitter API access below to be able to use the Twitter Scroller "
"Website snippet.<br/>\n"
"                             You can get your API credentials from"
msgstr ""
"Establezca su acceso a la API de Twitter para utilizar el Twitter Scroller "
"Website. <br/> \n"
"Usted puede obtener sus credenciales de la API desde"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_tutorial
msgid "Show me how to obtain the Twitter API Key and Secret"
msgstr "Muéstreme cómo obtener la clave de API y la palabra secreta de Twitter"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Switch to the API Keys tab: <br/>"
msgstr "Cambiar a la ficha de Claves API: <br/>"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:43
#, python-format
msgid ""
"The Twitter servers are up, but overloaded with requests. Try again later."
msgstr ""
"Los servidores de Twitter están sobrecargados de solicitudes. Inténtelo de "
"nuevo más tarde."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:44
#, python-format
msgid ""
"The Twitter servers are up, but the request could not be serviced due to "
"some failure within our stack. Try again later."
msgstr ""
"Los servidores de Twitter han aumentado, pero la solicitud no podían ser "
"atendidos debido a algún fallo en la pila del servidor. Inténtelo de nuevo "
"más tarde."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:39
#, python-format
msgid ""
"The request is understood, but it has been refused or access is not allowed. "
"Please check your Twitter API Key and Secret."
msgstr ""
"La solicitud se entiende, pero se ha negado o no está permitido el acceso. "
"Por favor, confirmar sus datos de API de Twitter."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:37
#, python-format
msgid ""
"The request was invalid or cannot be otherwise served. Requests without "
"authentication are considered invalid and will yield this response."
msgstr ""
"La solicitud no es válida o no se pueden obtener. Las solicitudes sin "
"autenticación se consideran inválidas y producirán esta respuesta."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:36
#, python-format
msgid "There was no new data to return."
msgstr "No hubo nuevos datos para volver."

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_tweet_id
msgid "Tweet ID"
msgstr "ID del tuit"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_tweet
msgid "Tweets"
msgstr "Tuits"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "Twitter API"
msgstr "API de Twiter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_api_key
#: model:ir.model.fields,help:website_twitter.field_website_twitter_api_key
msgid "Twitter API Key"
msgstr "Clave del API de Twiter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_twitter_api_secret
msgid "Twitter API Secret"
msgstr "Palabra secreta del API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_api_key
msgid "Twitter API key"
msgstr "Clave del API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_api_key
msgid "Twitter API key you can get it from https://apps.twitter.com/app/new"
msgstr ""
"Clave de la API de Twitter que puede obtener de https://apps.twitter.com/app/"
"new"

#. module: website_twitter
#: model:ir.model.fields,field_description:website_twitter.field_website_config_settings_twitter_api_secret
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_api_secret
msgid "Twitter API secret"
msgstr "Palaba secreta de la API de Twitter"

#. module: website_twitter
#: model:ir.model.fields,help:website_twitter.field_website_config_settings_twitter_api_secret
msgid "Twitter API secret you can get it from https://apps.twitter.com/app/new"
msgstr ""
"Palabra secreta de la API de Twitter que puede obtener de https://apps."
"twitter.com/app/new"

#. module: website_twitter
#. openerp-web
#: code:addons/website_twitter/static/src/xml/website.twitter.xml:41
#, python-format
msgid "Twitter Configuration"
msgstr "Configuración de Twitter"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_twitter_tweet
msgid "Twitter Tweets"
msgstr "Tuits de Twitter"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:65
#, python-format
msgid "Twitter authorization error!"
msgstr "Error de autorización de Twitter"

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:42
#, python-format
msgid "Twitter is down or being upgraded."
msgstr "Twitter está caído o se está actualizando."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:41
#, python-format
msgid ""
"Twitter seems broken. Please retry later. You may consider posting an issue "
"on Twitter forums to get help."
msgstr ""
"Twitter parece fuera de servicio. Por favor, vuelva a intentarlo más tarde. "
"Usted puede considerar la publicación de un tema en los foros de Twitter "
"para obtener ayuda."

#. module: website_twitter
#: code:addons/website_twitter/controllers/main.py:36
#, python-format
msgid ""
"Twitter user @%(username)s has less than 12 favorite tweets. Please add more "
"or choose a different screen name."
msgstr ""
"Usuario Twitter @%(username)s tiene menos de 12 tuits favoritos. Añada por "
"favor más o escoja un nombre diferente."

#. module: website_twitter
#: code:addons/website_twitter/models/twitter_config.py:61
#: code:addons/website_twitter/models/twitter_config.py:62
#, python-format
msgid "We failed to reach a twitter server."
msgstr "Pero no hemos podido llegar a un servidor de twitter."

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website
#: model:ir.model.fields,field_description:website_twitter.field_website_twitter_tweet_website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "https://apps.twitter.com/app/new"
msgstr "https://apps.twitter.com/app/new"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website_twitter
#: model:ir.model,name:website_twitter.model_website_config_settings
msgid "website.config.settings"
msgstr "website.config.settings"

#. module: website_twitter
#: model_terms:ir.ui.view,arch_db:website_twitter.view_website_config_settings
msgid "with the following values:"
msgstr "con los siguientes valores:"
