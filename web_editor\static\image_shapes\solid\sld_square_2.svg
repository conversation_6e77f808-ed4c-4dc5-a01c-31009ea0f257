<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="60" id="shape">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M.05 0 1 .113l-.094.6741L.144.9.05 0z"/>
        <g id="animation">
            <animateTransform xlink:href="#shape" attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;0 8;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95"/>
        </g>
    </defs>
    <svg viewBox="0 0 28.3 28.3" preserveAspectRatio="none">
        <path id="shadow" d="M0,2.8L28.3,6l-2.8,19.1L2.8,28.3L0,2.8z" fill="#3AADAA" transform="scale(.97)">
            <animateTransform attributeName="transform" attributeType="XML" type="translate" dur="6s" values="0 0;.3 -.3;0 0" repeatCount="indefinite" calcMode="spline" keySplines=".45 .05 .55 .95;.45 .05 .55 .95" additive="sum"/>
        </path>
    </svg>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
