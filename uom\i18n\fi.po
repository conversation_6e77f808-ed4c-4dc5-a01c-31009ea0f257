# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* uom
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# O<PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(reference unit)=ratio*(this unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    esim: 1*(viiteyksikkö)=suhde*(tämä yksikkö)\n"
"                                </span>"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
msgid ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    e.g: 1*(this unit)=ratio*(reference unit)\n"
"                                </span>"
msgstr ""
"<span class=\"oe_grey oe_inline\">\n"
"                                    esim: 1*(tämä yksikkö)=suhde*(vertailuyksikkö)\n"
"                                </span>"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__active
msgid "Active"
msgstr "Aktiivinen"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid "Add a new unit of measure"
msgstr "Lisää uusi mittayksikkö"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid "Add a new unit of measure category"
msgstr "Lisää uusi mittayksikkö kategoria"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor_inv
msgid "Bigger Ratio"
msgstr "Suurempi suhde"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__bigger
msgid "Bigger than the reference Unit of Measure"
msgstr "Suurempi kuin referenssiyksikkö"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__category_id
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Category"
msgstr "Tuoteryhmä(t)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__color
msgid "Color"
msgstr "Väri"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__ratio
msgid "Combined Ratio"
msgstr "Yhdistetty suhdeluku"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Yksiköiden välinen muunnos onnistuu vain saman ryhmän (kategorian) sisällä. "
"Konversio tehdään käyttäen kertoimena suhdetta."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__create_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__create_date
msgid "Created on"
msgstr "Luotu"

#. module: uom
#: model:uom.uom,name:uom.product_uom_day
msgid "Days"
msgstr "Päivää"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__display_name
#: model:ir.model.fields,field_description:uom.field_uom_uom__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: uom
#: model:uom.uom,name:uom.product_uom_dozen
msgid "Dozens"
msgstr "Tusina"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_category__reference_uom_id
msgid "Dummy field to keep track of reference uom change"
msgstr ""

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: uom
#: model:uom.uom,name:uom.product_uom_hour
msgid "Hours"
msgstr "Tunnit"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr ""
"Kuinka monta kertaa tämä mittayksikkö on suurempi kuin tässä luokassa oleva "
"mittayksikkö: 1 * (tämä yksikkö) = suhde * (vertailuyksikkö)"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr ""
"Kuinka paljon suurempi tai pienempi tämä yksikkö verrattuna tämän luokan "
"mittayksikköön: 1 * (vertailuyksikkö) = suhde * (tämä yksikkö)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__id
#: model:ir.model.fields,field_description:uom.field_uom_uom__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category____last_update
#: model:ir.model.fields,field_description:uom.field_uom_uom____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_uid
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__write_date
#: model:ir.model.fields,field_description:uom.field_uom_uom__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: uom
#: model:uom.category,name:uom.uom_categ_length
msgid "Length / Distance"
msgstr "Pituus / Etäisyys"

#. module: uom
#: model:res.groups,name:uom.group_uom
msgid "Manage Multiple Units of Measure"
msgstr "Hallitse useita (mitta)yksiköitä"

#. module: uom
#: model:ir.model,name:uom.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Tuotteen yksikkö"

#. module: uom
#: model:ir.model,name:uom.model_uom_category
msgid "Product UoM Categories"
msgstr "Tuote UoM Kategoriat"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__factor
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
msgid "Ratio"
msgstr "Suhde"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__reference
msgid "Reference Unit of Measure for this category"
msgstr "Referenssiyksikkö tälle ryhmälle"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__reference_uom_id
msgid "Reference UoM"
msgstr "Viitemittayksikkö (UoM)"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__rounding
msgid "Rounding Precision"
msgstr "Pyöristystarkkuus"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_uom_view_search
msgid "Search UOM"
msgstr "Etsi mittayksikkö"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.uom_categ_view_search
msgid "Search UoM Category"
msgstr "Etsi mittayksikön kategoria"

#. module: uom
#: model:ir.model.fields.selection,name:uom.selection__uom_uom__uom_type__smaller
msgid "Smaller than the reference Unit of Measure"
msgstr "Pienempi kuin referenssiyksikkö"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"Some critical fields have been modified on %s.\n"
"Note that existing data WON'T be updated by this change.\n"
"\n"
"As units of measure impact the whole system, this may cause critical issues.\n"
"E.g. modifying the rounding could disturb your inventory balance.\n"
"\n"
"Therefore, changing core units of measure in a running database is not recommended."
msgstr ""
"Joitakin kriittisiä kenttiä on muutettu kohdassa %s.\n"
"Huomaa, että tämä muutos ei päivitä olemassa olevia tietoja.\n"
"\n"
"Koska mittayksiköt vaikuttavat koko järjestelmään, tämä voi aiheuttaa kriittisiä ongelmia.\n"
"Esimerkiksi pyöristyksen muuttaminen voi häiritä varastotasetta.\n"
"\n"
"Siksi keskeisten mittayksiköiden muuttaminen käynnissä olevassa tietokannassa ei ole suositeltavaa."

#. module: uom
#: model:uom.category,name:uom.uom_categ_surface
msgid "Surface"
msgstr "Pinta"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""
"Laskettu määrä on moninkerta tästä arvosta. Käytä arvoa 1.0 sellaiselle "
"mittayksikölle jota ei voi jakaa, kuten kappale."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_gt_zero
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr "Muuntokerroin mittayksikölle ei voi olla 0 !"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The following units of measure are used by the system and cannot be deleted: %s\n"
"You can archive them instead."
msgstr ""
"Järjestelmä käyttää seuraavia mittayksiköitä, eikä niitä voi poistaa: %s\n"
"Voit arkistoida ne sen sijaan."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_factor_reference_is_one
msgid "The reference unit must have a conversion factor equal to 1."
msgstr "Vertailuyksikön muuntokerroin on oltava 1."

#. module: uom
#: model:ir.model.constraint,message:uom.constraint_uom_uom_rounding_gt_zero
msgid "The rounding precision must be strictly positive."
msgstr "Pyöristystarkkuuden on oltava ehdottomasti positiivinen."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid ""
"The unit of measure %s defined on the order line doesn't belong to the same "
"category as the unit of measure %s defined on the product. Please correct "
"the unit of measure defined on the order line or on the product, they should"
" belong to the same category."
msgstr ""
"Tilausrivillä määritelty mittayksikkö %s ei kuulu samaan luokkaan kuin "
"tuotteessa määritelty mittayksikkö %s. Korjaa tilausrivillä tai tuotteessa "
"määritelty mittayksikkö, niiden pitäisi kuulua samaan luokkaan."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "The value of ratio could not be Zero"
msgstr "Suhdeluvun arvo ei voi olla nolla"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__uom_type
msgid "Type"
msgstr "Tyyppi"

#. module: uom
#: model:ir.model.fields,help:uom.field_uom_uom__active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr ""
"Poista aktiivinen kenttä ottaaksesi pois käytöstä mittayksikön poistamatta "
"sitä."

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_unit
msgid "Unit"
msgstr "Yksikkö"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_uom__name
msgid "Unit of Measure"
msgstr "Mittayksikkö"

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__name
msgid "Unit of Measure Category"
msgstr "Mittayksiköiden ryhmä"

#. module: uom
#: model:uom.uom,name:uom.product_uom_unit
msgid "Units"
msgstr "Yksiköt"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_form_action
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_tree_view
msgid "Units of Measure"
msgstr "Mittayksiköt"

#. module: uom
#: model:ir.actions.act_window,name:uom.product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Mittayksikköjen kategoriat"

#. module: uom
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_form_view
#: model_terms:ir.ui.view,arch_db:uom.product_uom_categ_tree_view
msgid "Units of Measure categories"
msgstr "Yksiköiden ryhmät"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""
"Samaan luokkaan kuuluvat mittayksiköt voidaan\n"
"             muuntaa keskenään. Esimerkiksi luokassa\n"
"             <i> 'Aika' </i>, sinulla on seuraavat mittayksiköt:\n"
"             Tunnit, päivät."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0 code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should have a reference unit of measure."
msgstr "Mittayksikkö-kategorialla %s tulisi olla viitemittayksikkö."

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "UoM category %s should only have one reference unit of measure."
msgstr "UoM-kategoriassa %s tulisi olla vain yksi referenssimittayksikkö."

#. module: uom
#: model:ir.model.fields,field_description:uom.field_uom_category__uom_ids
msgid "Uom"
msgstr "Mittayksikkö"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_vol
msgid "Volume"
msgstr "Tilavuus"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning for %s"
msgstr "Varoitus %s"

#. module: uom
#: code:addons/uom/models/uom_uom.py:0
#, python-format
msgid "Warning!"
msgstr "Varoitus!"

#. module: uom
#: model:uom.category,name:uom.product_uom_categ_kgm
msgid "Weight"
msgstr "Paino"

#. module: uom
#: model:uom.category,name:uom.uom_categ_wtime
msgid "Working Time"
msgstr "Työaika"

#. module: uom
#: model_terms:ir.actions.act_window,help:uom.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr ""
"Sinun on määritettävä muuntokurssi useiden yksiköiden välillä\n"
"              samaan kategoriaan."

#. module: uom
#: model:uom.uom,name:uom.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: uom
#: model:uom.uom,name:uom.product_uom_floz
msgid "fl oz (US)"
msgstr "fl oz (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_foot
msgid "ft"
msgstr "ft"

#. module: uom
#: model:uom.uom,name:uom.uom_square_foot
msgid "ft²"
msgstr "neliöjalkaa (ft²)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_foot
msgid "ft³"
msgstr "ft³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_gal
msgid "gal (US)"
msgstr "gal (US)"

#. module: uom
#: model:uom.uom,name:uom.product_uom_inch
msgid "in"
msgstr "sisään"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_inch
msgid "in³"
msgstr "in³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: uom
#: model:uom.uom,name:uom.product_uom_km
msgid "km"
msgstr "km"

#. module: uom
#: model:uom.uom,name:uom.product_uom_lb
msgid "lb"
msgstr "paunaa"

#. module: uom
#: model:uom.uom,name:uom.product_uom_mile
msgid "mi"
msgstr "mailia"

#. module: uom
#: model:uom.uom,name:uom.product_uom_millimeter
msgid "mm"
msgstr "kk"

#. module: uom
#: model:uom.uom,name:uom.uom_square_meter
msgid "m²"
msgstr "m²"

#. module: uom
#: model:uom.uom,name:uom.product_uom_cubic_meter
msgid "m³"
msgstr "m³"

#. module: uom
#: model:uom.uom,name:uom.product_uom_oz
msgid "oz"
msgstr "oz"

#. module: uom
#: model:uom.uom,name:uom.product_uom_qt
msgid "qt (US)"
msgstr "qt (US)"
