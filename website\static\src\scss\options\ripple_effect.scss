@keyframes o-btn-ripple {
    100% {
        opacity: 0;
        transform: scale(2.5);
    }
}

.o_ripple_item {
    display: none;
    position: absolute;
    z-index: -1;
    border-radius: 100%;
    opacity: .3;
    background: currentColor;
    pointer-events: none;
    transform: scale(0);
}

.o_js_ripple_effect {
    transform-style: preserve-3d;
    position: relative !important;
    overflow: hidden !important;

    .o_ripple_item {
        display: block;
        animation: o-btn-ripple ease-in;
    }
}
