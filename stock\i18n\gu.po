# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock
# 
# Translators:
# <PERSON>, 2018
# Gaura<PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-24 09:34+0000\n"
"PO-Revision-Date: 2018-10-24 09:34+0000\n"
"Last-Translator: Spellbound Soft Solutions <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:341
#, python-format
msgid ""
"\n"
"\n"
"%s --> Product UoM is %s (%s) - Move UoM is %s (%s)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:342
#, python-format
msgid ""
"\n"
"\n"
"Blocking: %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:329
#, python-format
msgid " (%s reserved)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:332
#, python-format
msgid " (reserved)"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: not confirmed yet and will not be scheduled until confirmed.\n"
" * Waiting Another Operation: waiting for another move to proceed before it becomes automatically available (e.g. in Make-To-Order flows).\n"
" * Waiting: if it is not ready to be sent because the required products could not be reserved.\n"
" * Ready: products are reserved and ready to be sent. If the shipping policy is 'As soon as possible' this happens as soon as anything is reserved.\n"
" * Done: has been processed, can't be modified or cancelled anymore.\n"
" * Cancelled: has been cancelled, can't be confirmed anymore."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "#Products"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:559
#, python-format
msgid "%s: Supply Product from %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/res_company.py:24
#, python-format
msgid "%s: Transit Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement resolution is not straight forward. It may need the scheduler to run, a component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Procurement: Virtual location serving as temporary counterpart for procurement operations when the source (vendor or production) is not known yet. This location should be empty when the procurement scheduler has finished running.\n"
"* Production: Virtual counterpart location for production operations: this location consumes the raw material and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Here is your current inventory: </strong>"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:111
#, python-format
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "<span class=\"o_stat_text\">Details</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min :</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>View</span>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<strong>\n"
"                                            Product Barcode</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Date</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Inventory</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Product(s) tracked: </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Production Lot</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_kanban
msgid "<strong>Qty: </strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>State</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Total Quantity</strong>"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products ?</strong>"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:123
#, python-format
msgid "A Pack"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:88
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:80
#, python-format
msgid "A serial number should only be linked to a single product."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__type
#: model:ir.model.fields,help:stock.field_stock_move__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction
msgid "Action Needed"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_location_route__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "સક્રિય"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_ids
msgid "Activities"
msgstr "પ્રવૃત્તિઓ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_state
msgid "Activity State"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "વધારાની માહિતી"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
msgid "Address"
msgstr "સરનામું"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr ""

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Advanced: Apply Procurement Rules"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Alias"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "બધા"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr ""

#. module: stock
#: selection:procurement.group,move_type:0
msgid "All at once"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid ""
"All items couldn't be shipped, the remaining ones will be shipped as soon as"
" they become available."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:113
#, python-format
msgid "All products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"All products could not be reserved. Click on the \"Check Availability\" "
"button to try to reserve products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__putaway_strategy_id
msgid ""
"Allows to suggest the exact location (shelf) where to store the product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_selectable
msgid "Applicable on Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_ids
msgid ""
"Apply specific route(s) for the replenishment instead of product's default "
"routes."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"Are you sure you want to confirm this operation? This may lead to "
"inconsistencies in your inventory."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid "Are you sure you want to validate this picking?"
msgstr ""

#. module: stock
#: selection:stock.picking,move_type:0
msgid "As soon as possible"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Assign Owner"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr ""

#. module: stock
#: selection:stock.quantity.history,compute_at_date:0
msgid "At a Specific Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_variants_action
msgid "Attribute Values"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr ""

#. module: stock
#: selection:stock.rule,auto:0
msgid "Automatic No Step Added"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__string_availability_info
msgid "Availability"
msgstr ""

#. module: stock
#: selection:stock.move,state:0
msgid "Available"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__backorder_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_backorder_confirmation.py:28
#, python-format
msgid "Back order <em>%s</em> <b>cancelled</b>."
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:71
#, python-format
msgid "Backorder exists"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Barcode"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch Pickings"
msgstr ""

#. module: stock
#: selection:res.partner,picking_warn:0
msgid "Blocking Message"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr ""

#. module: stock
#: selection:stock.rule,action:0
msgid "Buy"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "By Lots"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "By Unique Serial Number"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:644
#, python-format
msgid ""
"By changing this quantity here, you accept the new quantity as complete: "
"Odoo will not automatically generate a back order."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""

#. module: stock
#: model:product.product,name:stock.product_cable_management_box
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:520
#, python-format
msgid "Can't find any customer or supplier location."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:256
#, python-format
msgid "Can't find any generic route %s."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "રદ કરો"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Cancel Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.inventory,state:0 selection:stock.move,state:0
#: selection:stock.package_level,state:0 selection:stock.picking,state:0
msgid "Cancelled"
msgstr "રદ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled Moves"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:277
#, python-format
msgid ""
"Cannot set the done quantity from this stock move, work directly with the "
"move lines."
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Cashier"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
msgid "Category"
msgstr "વર્ગ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__move_dest_exists
msgid "Chained Move Exists"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Change must be higher than"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__move_id
msgid "Change to a better name"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Check Availability"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__product_qty
msgid "Checked Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__date
msgid "Choose a date to get the inventory at that date"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:998
#, python-format
msgid "Choose destination location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__compute_at_date
msgid ""
"Choose to analyze the current inventory or from a specific date in the past."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Client"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
msgid "Color"
msgstr "રંગ"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "કંપનીઓ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory__company_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "કંપની"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__compute_at_date
msgid "Compute"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Conditions"
msgstr "શરતો"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
msgid "Configuration"
msgstr "રુપરેખાંકન"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Confirm"
msgstr "ખાતરી"

#. module: stock
#: selection:stock.package_level,state:0
msgid "Confirmed"
msgstr "સમર્થિત"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr ""

#. module: stock
#: selection:product.template,type:0
msgid "Consumable"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_res_partner
msgid "Contact"
msgstr "સંપર્ક"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:24
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:755
#, python-format
msgid "Create Backorder?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_tree_view
msgid "Create Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Create Procurement: A procurement will be created in the source location and the system will try to find a rule to resolve it. The available stock will be ignored.\n"
"             Take from Stock: The products will be taken from the available stock."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                        products later. Do not create a backorder if you will not\n"
"                        process the remaining products."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid "Create a new inventory adjustment"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Create a new product"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "Create a new stock movement"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid "Create a new stock operation"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid "Create a reordering rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__move_ids
msgid "Created Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_putaway__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "સર્જન તારીખ"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Credit Card"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:525
#, python-format
msgid "Cross-Dock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__cumulative_quantity
msgid "Cumulative Quantity"
msgstr ""

#. module: stock
#: selection:stock.quantity.history,compute_at_date:0
msgid "Current Inventory"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:109
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#, python-format
msgid "Customer"
msgstr "ભાગીદાર"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: selection:stock.location,usage:0
msgid "Customer Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr ""

#. module: stock
#: selection:stock.picking.type,code:0
msgid "Customers"
msgstr "ગ્રાહકો"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL USA"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__date
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "તારીખ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
msgid "Date Expected"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr ""

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to get the products"
msgstr ""

#. module: stock
#: selection:stock.warehouse.orderpoint,lead_type:0
msgid "Day(s) to purchase"
msgstr ""

#. module: stock
#: model:res.company,overdue_msg:stock.res_company_1
msgid ""
"Dear Sir/Madam,\n"
"\n"
"Our records indicate that some payments on your account are still due. Please find details below.\n"
"If the amount has already been paid, please disregard this notice. Otherwise, please forward us the total amount stated below.\n"
"If you have any queries regarding your account, Please contact us.\n"
"\n"
"Thank you in advance for your cooperation.\n"
"Best Regards,"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
msgid "Default Destination Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid "Default Owner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
msgid "Default Source Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: stock
#: selection:stock.move,procure_method:0
msgid "Default: Take From Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_form
msgid ""
"Define a minimum stock rule so that Odoo creates automatically requests for "
"quotations or draft manufacturing orders to resupply your stock."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_done_grouped
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Define a new transfer"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) "
"where to take the products from, which lot etc. for this location. This "
"method can be enforced at the product category level, and a fallback is made"
" on the parent locations if none is set here."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Delay"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Deliver goods directly (1 step)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:526
#, python-format
msgid "Deliver in 1 step (ship)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:526
#, python-format
msgid "Deliver in 2 steps (pick + ship)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:527
#, python-format
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:335
#, python-format
msgid "Delivered Qty"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Deliveries"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:734
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
msgid "Delivery Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, MTO, etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__name
msgid "Description"
msgstr "વર્ણન"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Destination"
msgstr "લક્ષ્ય"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:96
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Destination Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:470
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Discounted Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_putaway__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_report_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__qty_done
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.move,state:0 selection:stock.package_level,state:0
#: selection:stock.picking,state:0 selection:stock.scrap,state:0
msgid "Done"
msgstr "પુર્ણ થયુ"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done
msgid "Done Transfers"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_done_grouped
msgid "Done Transfers by Date"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Don’t propagate scheduling changes through chains of operations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.inventory,state:0 selection:stock.package_level,state:0
#: selection:stock.picking,state:0 selection:stock.scrap,state:0
msgid "Draft"
msgstr "ડ્રાફ્ટ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Effective Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_inventory_line__product_tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:13
#, python-format
msgid "Error"
msgstr "ભૂલ"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_expected
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_expected
msgid "Expected Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Filters"
msgstr "ગાળકો"

#. module: stock
#: selection:stock.rule,group_propagation_option:0
msgid "Fixed"
msgstr "ચોક્કસ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway__product_location_ids
msgid "Fixed Locations Per Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway__fixed_location_ids
msgid "Fixed Locations Per Product Category"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_fixed_putaway_strat
msgid "Fixed Putaway Strategy on Location"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:6
#, python-format
msgid "Fold"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_follower_ids
msgid "Followers"
msgstr "અનુયાયીઓ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_channel_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
msgid "Forecast Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:330
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
#, python-format
msgid "Forecasted Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "From"
msgstr "તરફથી"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_reserved_availability
msgid "From Supplier"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:333
#, python-format
msgid "Future Deliveries"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:338
#, python-format
msgid "Future P&L"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:348
#, python-format
msgid "Future Productions"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:343
#, python-format
msgid "Future Qty"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:325
#, python-format
msgid "Future Receipts"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_fixed_putaway_strat__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Graph"
msgstr "આલેખ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_move_lines
msgid "Has Move Lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid ""
"Here you can receive individual products, no matter what\n"
"                purchase order or picking order they come from. You will find\n"
"                the list of all products you are waiting for."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_putaway__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__id
#: model:ir.model.fields,field_description:stock.field_report_stock_report_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ઓળખ"

#. module: stock
#: code:addons/stock/models/stock_inventory.py:431
#, python-format
msgid "INV:"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:88
#, python-format
msgid "INV: %s"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"If a product is not at the right place, set the checked quantity to 0 and "
"create a new line with correct location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread
msgid "If checked new messages require your attention."
msgstr "જો ચેક કરેલા નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction
msgid "If checked, new messages require your attention."
msgstr "જો ચેક કરેલું હોય, તો નવા સંદેશા માટે તમારું ધ્યાન આવશ્યક છે."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "જો ચેક કરેલું હોય, તો કેટલાક મેસેજીસમાં ડિલિવરીની ભૂલ છે."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__date
msgid ""
"If the inventory adjustment is not validated, date at which the theoritical quantities have been checked.\n"
"If the inventory adjustment is validated, date at which the inventory adjustment has been validated."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking)"
" or done quantities (for a done picking)."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_reserved
msgid ""
"If this checkbox is ticked, Odoo will show which products are reserved "
"(lot/serial number, source location, source package)."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__backorder_id
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__filter
msgid ""
"If you do an entire inventory, you can choose 'All Products' and it will "
"prefill the inventory with the current stock.  If you only do some products"
"  (e.g. Cycle Counting) you can choose 'Manual Selection of Products' and "
"the system won't propose anything.  You can also let the system propose for "
"a single product / lot /... "
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_picking__immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:718
#, python-format
msgid "Immediate Transfer?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr ""

#. module: stock
#: selection:res.config.settings,module_procurement_jit:0
msgid "Immediately after sales order confirmation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: selection:stock.inventory,state:0
msgid "In Progress"
msgstr "પ્રગતિમાં છે"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__exhausted
msgid "Include Exhausted Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_receipt_picking_move
msgid "Incoming  Products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Initial Demand"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:444
#, python-format
msgid "Input"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_scrap.py:138
#, python-format
msgid "Insufficient Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: selection:stock.picking.type,code:0
msgid "Internal"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Internal Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__ref
msgid "Internal Reference"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:759
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:254
#, python-format
msgid "Invalid domain left operand %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:256
#, python-format
msgid "Invalid domain operator %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:258
#, python-format
msgid "Invalid domain right operand %s"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__location_id
msgid "Inventoried Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__lot_id
msgid "Inventoried Lot/Serial Number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__partner_id
msgid "Inventoried Owner"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__package_id
msgid "Inventoried Pack"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__product_id
msgid "Inventoried Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__line_ids
msgid "Inventories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventories Date"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.quantsact
#: model:ir.actions.report,name:stock.action_report_inventory
#: model:ir.model,name:stock.model_stock_inventory
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_move__inventory_id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__inventory_id
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustment"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_form
#: model:ir.ui.menu,name:stock.menu_action_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Adjustments"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__date
msgid "Inventory Date"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory Details"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_line
msgid "Inventory Line"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_line_tree
msgid "Inventory Lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__inventory_location_id
msgid "Inventory Location"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Inventory Loss"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__name
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Inventory Reference"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_quantity_history
#: model:ir.ui.menu,name:stock.menu_valuation
msgid "Inventory Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid ""
"Inventory adjustments will be made by comparing the theoretical and the "
"checked quantities."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__date
msgid "Inventory at Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__filter
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Inventory of"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__return_location
msgid "Is a Return Location?"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:261
#, python-format
msgid ""
"It is not possible to reserve more products of %s than you have in stock."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:266
#, python-format
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_packaging
msgid ""
"It specifies attributes of packaging like type, quantity of packaging,etc."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "LN/SN:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__last_done_picking
msgid "Last 10 Done Pickings"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group____last_update
#: model:ir.model.fields,field_description:stock.field_product_putaway____last_update
#: model:ir.model.fields,field_description:stock.field_product_removal____last_update
#: model:ir.model.fields,field_description:stock.field_product_replenish____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_report_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_destination____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_level____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rules_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap____last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap____last_update
msgid "Last Modified on"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_putaway__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_putaway__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:69
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#, python-format
msgid "Late"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days
msgid "Lead Time"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_type
msgid "Lead Type"
msgstr ""

#. module: stock
#: selection:stock.rule,group_propagation_option:0
msgid "Leave Empty"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "List view of lines"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Localization"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Locate"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__fixed_location_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "સ્થળ"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
msgid "Location Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Lot/Serial #"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__prod_lot_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Lot/Serial Number"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_tree
msgid "Lot/Serial Number Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the delivery slip"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided to tracked products"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form_simple
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:288
#: code:addons/stock/models/stock_warehouse.py:629
#: model:stock.location.route,name:stock.route_warehouse0_mto
#, python-format
msgid "Make To Order"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage several warehouses"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Manager"
msgstr "વ્યવસ્થાપક"

#. module: stock
#: selection:stock.rule,auto:0
msgid "Manual Operation"
msgstr ""

#. module: stock
#: selection:res.config.settings,module_procurement_jit:0
msgid "Manually or based on automatic scheduler"
msgstr ""

#. module: stock
#: selection:stock.rule,action:0
msgid "Manufacture"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "ઉત્પાદન"

#. module: stock
#: selection:stock.picking.type,code:0
msgid "Manufacturing Operation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
msgid "Master Data"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Maximum Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
msgid "Message for Stock Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_ids
msgid "Messages"
msgstr "સંદેશાઓ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "રીત"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__propagation_minimum_delta
msgid "Minimum Delta for Propagation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__propagation_minimum_delta
msgid ""
"Minimum Delta for Propagation of a Date Change on moves linked together"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Minimum Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Misc"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree
msgid ""
"Most operations are prepared automatically by Odoo according\n"
"                to your preconfigured logistics rules, but you can also record\n"
"                manual stock movements."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
msgid "Move"
msgstr "ખસેડો"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Move Lines"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Move Supply Method"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
msgid ""
"Move date: scheduled date until move is done, then date of actual move "
"processing"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_product_stock_move_open
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_warehouses
msgid "Multi-Warehouses"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Activities"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_putaway__name
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "નામ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_scrap.py:25
#: code:addons/stock/models/stock_scrap.py:69
#: code:addons/stock/models/stock_scrap.py:70 selection:stock.move,state:0
#: selection:stock.package_level,state:0
#, python-format
msgid "New"
msgstr "નવું"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:169
#: code:addons/stock/models/stock_picking.py:612
#, python-format
msgid "New Move:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_summary
msgid "Next Activity Summary"
msgstr "આગલું પ્રવૃત્તિ સારાંશ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr ""

#. module: stock
#: selection:res.partner,picking_warn:0
msgid "No Message"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__use_propagation_minimum_delta
msgid "No Rescheduling Propagation"
msgstr ""

#. module: stock
#: selection:product.template,tracking:0
msgid "No Tracking"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.quantsact
msgid "No inventory found"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:408
#, python-format
msgid "No negative quantities allowed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:305
#, python-format
msgid ""
"No procurement rule found in location \"%s\" for product \"%s\".\n"
" Check routes configuration."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:59
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:172
#, python-format
msgid "No source location defined on stock rule: %s!"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Normal"
msgstr "સામાન્ય"

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Not urgent"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "નોંધ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__note
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Notes"
msgstr "નોંધ"

#. module: stock
#: code:addons/stock/models/stock_picking.py:562
#, python-format
msgid "Nothing to check the availability for."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__lead_days
msgid ""
"Number of days after the orderpoint is triggered to receive the products or "
"to order to the vendor"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error_counter
msgid "Number of error"
msgstr "ભૂલની સંખ્યા"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "સંદેશાઓની સંખ્યા જે ક્રિયા માટે જરૂરી છે"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "વિતરણ ભૂલ સાથે સંદેશાઓની સંખ્યા"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread_counter
msgid "Number of unread messages"
msgstr "ન વાંચેલા સંદેશાની સંખ્યા"

#. module: stock
#: code:addons/stock/models/stock_picking.py:73
#, python-format
msgid "OK"
msgstr "બરાબર"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "On Hand"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid ""
"Once you receive an order, you can filter based on the name of\n"
"                the vendor or the purchase order reference. Then you can confirm\n"
"                all products received using the buttons on the right of each line."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:121
#, python-format
msgid "One Lot/Serial Number"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:119
#, python-format
msgid "One owner only"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:114
#, python-format
msgid "One product category"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:119
#, python-format
msgid "One product for a specific owner"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:115
#, python-format
msgid "One product only"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:97
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#, python-format
msgid "Operation Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "વિકલ્પો"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Order Date"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Origin"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__original_location_id
msgid "Original Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:446
#, python-format
msgid "Output"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr ""

#. module: stock
#: selection:stock.picking,activity_state:0
#: selection:stock.production.lot,activity_state:0
msgid "Overdue"
msgstr "મુદતવીતી"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__overprocessed_product_name
msgid "Overprocessed Product Name"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_location__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "માલિક"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__partner_id
msgid "Owner of the location if not internal"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid "Owner of the quants"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:340
#, python-format
msgid "P&L Qty"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:6
#, python-format
msgid "PRINT"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:742
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__package_id
#, python-format
msgid "Pack"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Pack goods, send goods in output and then deliver (3 steps)"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Content"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Package Content"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Reference:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__packaging_id
msgid "Package Type"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created by pack operations made on transfers and can "
"contains several different products. You can then reuse a package to move "
"its whole content somewhere else, or to pack it into another bigger package."
" A package can also be unpacked, allowing the disposal of its former content"
" as single units again."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Packaging"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:447
#, python-format
msgid "Packing Zone"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr ""

#. module: stock
#: selection:procurement.group,move_type:0
msgid "Partial"
msgstr ""

#. module: stock
#: selection:stock.move,state:0
msgid "Partially Available"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "ભાગીદાર"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Physical Inventories by Date"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:751
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__pick_ids
#, python-format
msgid "Pick"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_overprocessed_transfer__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking List"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Picking Moves"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Pickings"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Pickings for Groups"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings that are late on scheduled time"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
msgid "Pivot"
msgstr ""

#. module: stock
#: selection:stock.picking,activity_state:0
#: selection:stock.production.lot,activity_state:0
msgid "Planned"
msgstr "આયોજિત"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:690
#, python-format
msgid "Please add some items to move."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:132
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Positive Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging
msgid "Preferred Packaging"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_ids
msgid "Preferred Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Priced Product"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "છાપો"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "છપાયેલ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
msgid "Priority"
msgstr "પ્રાથમિકતા"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Priority for this picking. Setting manually a value here would set it as "
"priority for all the moves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process picking in batch per worker"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid "Processed more than initial demand"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Procurement"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr ""

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:350
#, python-format
msgid "Produced Qty"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__product_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Product"
msgstr "ઉત્પાદન"

#. module: stock
#: code:addons/stock/models/stock_location.py:187
#: model:ir.model.fields,field_description:stock.field_stock_location_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Product Categories"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__category_id
#: model:ir.model.fields,field_description:stock.field_stock_inventory__category_id
msgid "Product Category"
msgstr "ઉત્પાદન વર્ગ"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Product Moves"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr ""

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
msgid "Product Template"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
msgid "Product Tmpl"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__type
#: model:ir.model.fields,field_description:stock.field_stock_move__product_type
msgid "Product Type"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form_simple
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Production"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:177
#: code:addons/stock/wizard/stock_quantity_history.py:29
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#, python-format
msgid "Products"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:313
#, python-format
msgid "Products: "
msgstr ""

#. module: stock
#: selection:stock.rule,group_propagation_option:0
msgid "Propagate"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate
msgid "Propagate cancel and split"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr ""

#. module: stock
#: selection:stock.rule,action:0
msgid "Pull & Push"
msgstr ""

#. module: stock
#: selection:stock.rule,action:0
msgid "Pull From"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr ""

#. module: stock
#: selection:stock.rule,action:0
msgid "Push To"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_fixed_putaway_strat__putaway_id
msgid "Put Away Method"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_putaway
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_strategy_id
msgid "Put Away Strategy"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Put in Pack"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_fixed_putaway_strat_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid "Putaway"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Qty Multiple"
msgstr ""

#. module: stock
#: sql_constraint:stock.warehouse.orderpoint:0
msgid "Qty Multiple must be greater than or equal to zero."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:445
#, python-format
msgid "Quality Control"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_forecast__quantity
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Quantity"
msgstr "જથ્થો"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Quantity On Hand"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Quantity Reserved"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:74
#, python-format
msgid "Quantity cannot be negative."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:643
#, python-format
msgid "Quantity decreased!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.lot_open_quants
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:74
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__rate_picking_backorders
msgid "Rate Picking Backorders"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__rate_picking_late
msgid "Rate Picking Late"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.picking,state:0
msgid "Ready"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Real Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_qty
msgid "Real Reserved Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:726
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods directly (1 step)"
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods in input and then stock (2 steps)"
msgstr ""

#. module: stock
#: selection:stock.warehouse,reception_steps:0
msgid "Receive goods in input, then quality and then stock (3 steps)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:524
#, python-format
msgid "Receive in 1 step (stock)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:524
#, python-format
msgid "Receive in 2 steps (input + stock)"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:525
#, python-format
msgid "Receive in 3 steps (input + quality + stock)"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:327
#, python-format
msgid "Received Qty"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Receptions"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
msgid "Reference"
msgstr "સંદર્ભ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr ""

#. module: stock
#: sql_constraint:stock.picking:0
msgid "Reference must be unique per company!"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_receipt_picking_move
msgid "Register a product receipt"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Register lots, packs, location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:109
#, python-format
msgid "Removal strategy %s not implemented."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_warehouse_2_stock_warehouse_orderpoint
#: model:ir.actions.act_window,name:stock.action_orderpoint_form
#: model:ir.actions.act_window,name:stock.product_open_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr "અહેવાલીકરણ"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__use_propagation_minimum_delta
msgid ""
"Rescheduling applies to any chain of operations (e.g. Make To Order, Pick Pack Ship). In the case of MTO sales, a vendor delay (updated incoming date) impacts the expected delivery date to the customer. \n"
" This option allows to not propagate the rescheduling if the change is not critical."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Rescheduling applies to any chain of operations (e.g. Make To Order, Pick "
"Pack Ship). In the case of MTO sales, a vendor delay (updated incoming date)"
" impacts the expected delivery date to the customer. This option allows to "
"not propagate the rescheduling if the change is not critical."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_procurement_jit
msgid "Reservation"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
#: selection:stock.package_level,state:0
msgid "Reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_procurement_jit
msgid ""
"Reserving products manually in delivery orders or by running the scheduler "
"is advised to better manage priorities in case of long customer lead times "
"or/and frequent stock-outs."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
msgid "Responsible"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_user_id
msgid "Responsible User"
msgstr "જવાબદાર વપરાશકર્તા"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Retrieve the Inventory Quantities"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__location_id
msgid "Return Location"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:103
#, python-format
msgid "Return of %s"
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:153
#, python-format
msgid "Returned Picking"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Rules"
msgstr "નિયમો"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Scheduler"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Run the scheduler"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_expected
msgid "Scheduled date for the processing of this move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:1062
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_id
msgid "Scrap Move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
msgid "Search Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_search
msgid "Search Inventory Lines"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:116
#, python-format
msgid "Select products manually"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr ""

#. module: stock
#: selection:stock.warehouse,delivery_steps:0
msgid "Send goods in output and then deliver (2 steps)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "ક્રમ"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:777
#, python-format
msgid "Sequence in"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:797
#, python-format
msgid "Sequence internal"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:782
#, python-format
msgid "Sequence out"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:787
#, python-format
msgid "Sequence packing"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:792
#, python-format
msgid "Sequence picking"
msgstr ""

#. module: stock
#: selection:product.template,type:0
msgid "Service"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Putaway Strategies on Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Set to Draft"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Settings"
msgstr "સુયોજનો"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
msgid "Shipping Policy"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_mark_as_todo
msgid "Show Mark As Todo"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
msgid "Show Operations"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_reserved
msgid "Show Reserved"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__show_resupply
msgid "Show Resupply"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_validate
msgid "Show Validate"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__string_availability_info
msgid "Show various information on stock availability for this move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Some products of the inventory adjustment are tracked. Are you sure you "
"don't want to specify a serial or lot number for them?"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Source"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
msgid "Source Document"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:95
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_source_location
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Source Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__lot_id
msgid ""
"Specify Lot/Serial Number to focus your inventory on a particular Lot/Serial"
" Number."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__partner_id
msgid "Specify Owner to focus your inventory on a particular Owner."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__package_id
msgid "Specify Pack to focus your inventory on a particular Pack."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__category_id
msgid ""
"Specify Product Category to focus your inventory on a particular Category."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_inventory__product_id
msgid "Specify Product to focus your inventory on a particular Product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Start Inventory"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "અવસ્થા"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__state
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__state
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "સ્થિતિ"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:443
#, python-format
msgid "Stock"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_forecast
msgid "Stock Forecast Report"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Stock Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree
msgid "Stock Inventory Lines"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_product
#: model:ir.actions.act_window,name:stock.action_stock_level_forecast_report_template
msgid "Stock Level Forecast"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_graph
#: model_terms:ir.ui.view,arch_db:stock.view_stock_level_forecast_pivot
msgid "Stock Level forecast"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
msgid "Stock Move"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Stock Moves"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.product_open_quants
#: model:ir.actions.act_window,name:stock.product_template_open_quants
msgid "Stock On Hand"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
msgid "Stock Picking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock moves not in package"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr ""

#. module: stock
#: selection:product.template,type:0
msgid "Storable Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
msgid "Supply Method"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr ""

#. module: stock
#: selection:stock.rule,procure_method:0
msgid "Take From Stock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the check availability button should"
" be shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_mark_as_todo
msgid ""
"Technical field used to compute whether the mark as todo button should be "
"shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_validate
msgid "Technical field used to compute whether the validate should be shown."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_tmpl_id
msgid "Technical: used in views"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:824
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has"
" been created."
msgstr ""

#. module: stock
#: sql_constraint:stock.location:0
msgid "The barcode for a location must be unique per company !"
msgstr ""

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The code of the warehouse must be unique per company!"
msgstr ""

#. module: stock
#: sql_constraint:stock.production.lot:0
msgid "The combination of serial number and product must be unique !"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"delay."
msgstr ""

#. module: stock
#: sql_constraint:stock.warehouse:0
msgid "The name of the warehouse must be unique per company!"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views accordingly.\n"
"                On the operation type you could e.g. specify if packing is needed by default,\n"
"                if it should show the customer."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:377
#, python-format
msgid ""
"The quantity done for the product \"%s\" doesn't respect the rounding "
"precision                                   defined on the unit of measure "
"\"%s\". Please change the quantity done or the"
"                                   rounding precision of your unit of "
"measure."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:284
#: code:addons/stock/models/stock_move_line.py:82
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway
msgid ""
"The rules defined per product will be applied before the rules defined per "
"product category."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:160
#, python-format
msgid ""
"The selected inventory options are not coherent, the package doesn't exist."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:156
#, python-format
msgid "The selected lot number doesn't exist."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:158
#, python-format
msgid "The selected owner doesn't have the proprietary of that product."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:154
#, python-format
msgid "The selected product doesn't belong to that owner.."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"The stock will be reserved for operations waiting for availability and the "
"reordering rules will be triggered."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__theoretical_qty
msgid "Theoretical Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.quantsact
msgid ""
"This analysis gives you a fast overview on the current stock level of your "
"products and their current inventory value."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"This is the list of all the production lots you recorded. When\n"
"            you select a lot, you can get the traceability of the products contained in lot."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_inventory_form
msgid "This is used to correct the product quantities you have in stock."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:83
#, python-format
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:74
#, python-format
msgid "This lot %s is incompatible with this product %s"
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note will show up on delivery orders."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note will show up on internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note will show up on receipt orders (e.g. where to store the product in"
" the warehouse)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which"
" would create duplicated operations)"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "To"
msgstr "પ્રતિ"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "કરવાનું"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr ""

#. module: stock
#: selection:stock.picking,activity_state:0
#: selection:stock.production.lot,activity_state:0
msgid "Today"
msgstr "આજે"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory__total_qty
msgid "Total Quantity"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr ""

#. module: stock
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Traceability Report"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:173
#, python-format
msgid "Tracked Products in Inventory Adjustment"
msgstr ""

#. module: stock
#: selection:stock.track.line,tracking:0
msgid "Tracked by lot"
msgstr ""

#. module: stock
#: selection:stock.track.line,tracking:0
msgid "Tracked by serial number"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_inventory_line__product_tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Transfer"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_partner_id
msgid "Transfer Destination Address"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_overprocessed_transfer
msgid "Transfer Over Processed Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
msgid "Transfer Reference"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.ui.menu,name:stock.all_picking
msgid "Transfers"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "Transit Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
#: selection:stock.rule,procure_method:0
msgid "Trigger Another Rule"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "પ્રકાર"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS"
msgstr ""

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:10
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:36
#, python-format
msgid "Unfold"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__name
msgid "Unique Lot/Serial Number"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_form
msgid "Unit Of Measure"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Unit Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_picking_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking_board
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:885
#, python-format
msgid "Unknow stream."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:348
#, python-format
msgid "Unknown Pack"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:345
#, python-format
msgid "Unplanned Qty"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread
msgid "Unread Messages"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unreserve"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_line_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "UoM"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Qty On Hand"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:563
#, python-format
msgid "Update quantity on hand"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Urgent"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid ""
"Use this assistant to replenish your stock. \n"
"                Depending on your product configuration, launching a replenishment may trigger a request for quotation,\n"
"                a manufacturing order or a transfer."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes and putaway strategies"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr ""

#. module: stock
#: model:res.groups,name:stock.group_stock_user
msgid "User"
msgstr "વપરાશકર્તા"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "Validate Inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_filter
#: selection:stock.inventory,state:0
msgid "Validated"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: selection:stock.location,usage:0
msgid "Vendor Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr ""

#. module: stock
#: selection:stock.picking.type,code:0
msgid "Vendors"
msgstr ""

#. module: stock
#: selection:stock.move,priority:0 selection:stock.picking,priority:0
msgid "Very Urgent"
msgstr ""

#. module: stock
#: selection:stock.location,usage:0
msgid "View"
msgstr "દેખાવ"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: selection:stock.picking,state:0
msgid "Waiting"
msgstr ""

#. module: stock
#: selection:stock.move,state:0
msgid "Waiting Another Move"
msgstr ""

#. module: stock
#: selection:stock.picking,state:0
msgid "Waiting Another Operation"
msgstr ""

#. module: stock
#: selection:stock.move,state:0
msgid "Waiting Availability"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__warehouse_count
msgid "Warehouse Count"
msgstr ""

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "વખાર વ્યવસ્થાપન"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:827
#, python-format
msgid "Warehouse's Routes"
msgstr ""

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:129
#: code:addons/stock/models/stock_move_line.py:141
#: selection:res.partner,picking_warn:0
#, python-format
msgid "Warning"
msgstr "ચેતવણી"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:306
#, python-format
msgid "Warning!"
msgstr "ચેતવણી!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Warnings"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_production_lot__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: stock
#: selection:barcode.rule,type:0
msgid "Weighted Product"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse.  This behaviour"
" can be overridden by the routes on the Product/Product Categories or by the"
" Preferred Routes on the Procurement"
msgstr ""

#. module: stock
#: selection:stock.picking,move_type:0
msgid "When all products are ready"
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form.  It will take priority over the Warehouse route. "
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_categ_selectable
msgid ""
"When checked, the route will be selectable on the Product Category.  It will"
" take priority over the Warehouse route. "
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:113
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> <b>%s</b> are created from "
"<b>%s</b> to fulfill the need."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_rule.py:114
#, python-format
msgid ""
"When products arrive in <b>%s</b>, <br/> <b>%s</b> are created to send them "
"in <b>%s</b>."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate
msgid ""
"When ticked, if the move is splitted or cancelled, the next move will be "
"too."
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:44
#, python-format
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:36
#, python-format
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:668
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "You can delete lines to ignore some products."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:545
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:338
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:147
#, python-format
msgid "You can not enter negative quantities."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:426
#, python-format
msgid "You can only adjust storable products."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1149
#, python-format
msgid "You can only delete draft moves."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:140
#, python-format
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1014
#, python-format
msgid "You cannot cancel a stock move that has been set to 'Done'."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:94
#, python-format
msgid ""
"You cannot change the location type or its use as a scrap location as there "
"are products reserved in this location. Please unreserve the products first."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:647
#, python-format
msgid ""
"You cannot change the ratio of this unit of mesure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:536
#, python-format
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_scrap.py:76
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:105
#, python-format
msgid "You cannot delete a validated inventory adjustement."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:415
#, python-format
msgid ""
"You cannot have two inventory adjustments in state 'In Progress' with the "
"same product (%s), same location, same package, same owner and same lot. "
"Please first validate the first inventory adjustment before creating another"
" one."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1134
#, python-format
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:339
#, python-format
msgid ""
"You cannot perform the move because the unit of measure has a different "
"category as the product unit of measure."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:186
#, python-format
msgid ""
"You cannot set a negative product quantity in an inventory line:\n"
"\t%s - qty: %s"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1180
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:1176
#, python-format
msgid "You cannot split a stock move that has been set to 'Done'."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_quant.py:86
#, python-format
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\"."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move.py:501
#, python-format
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:122
#: code:addons/stock/models/stock_move_line.py:126
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:698
#, python-format
msgid ""
"You cannot validate a transfer if no quantites are reserved nor done. To "
"force the transfer, switch in edit more and encode the done quantities."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:112
#, python-format
msgid ""
"You have manually created product lines, please delete them to proceed."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You have not recorded <i>done</i> quantities yet, by clicking on <i>apply</i>\n"
"                        Odoo will process all the <i>reserved</i> quantities."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_overprocessed_transfer
msgid ""
"You have processed more than what was initially\n"
"                    planned for the product"
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:307
#, python-format
msgid ""
"You have products in stock that have no lot number.  You can assign serial "
"numbers by doing an inventory.  "
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:869
#, python-format
msgid ""
"You have to define a groupby and sorted method and pass them as arguments."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:911
#, python-format
msgid ""
"You have to select a product unit of measure that is in the same category "
"than the default unit of measure of the product"
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_location.py:109
#, python-format
msgid "You have to set a name for this location."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:47
#, python-format
msgid "You may only return Done pickings."
msgstr ""

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:38
#, python-format
msgid "You may only return one picking at a time."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_inventory.py:22
#, python-format
msgid "You must define a warehouse for the company: %s."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_picking.py:1045
#, python-format
msgid "You must first set the quantity you will put in the pack."
msgstr ""

#. module: stock
#: code:addons/stock/models/stock_move_line.py:406
#: code:addons/stock/models/stock_picking.py:712
#, python-format
msgid "You need to supply a Lot/Serial number for product %s."
msgstr ""

#. module: stock
#: code:addons/stock/models/product.py:394
#: code:addons/stock/models/product.py:538
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "_Apply"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "_Cancel"
msgstr ""

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "દિવસો"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "days to be propagated"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "e.g. Annual inventory"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form_simple
msgid "e.g. LOT/0001/20121"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "is not available in sufficient quantity"
msgstr ""

#. module: stock
#: model:product.product,uom_name:stock.product_cable_management_box
#: model:product.product,weight_uom_name:stock.product_cable_management_box
#: model:product.template,uom_name:stock.product_cable_management_box_product_template
#: model:product.template,weight_uom_name:stock.product_cable_management_box_product_template
msgid "kg"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "manually to trigger the reordering rules right now."
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr ""

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_inventory_form
msgid "⇒ Set quantities to 0"
msgstr ""
