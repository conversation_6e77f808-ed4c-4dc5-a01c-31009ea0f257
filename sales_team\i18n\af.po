# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sales_team
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-16 19:35+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Afrikaans (http://www.transifex.com/odoo/odoo-9/language/"
"af/)\n"
"Language: af\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:91
#, python-format
msgid "3 exp. closing"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:137
#, python-format
msgid "35,029.39€"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:124
#, python-format
msgid "78,140.03€"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:169
#, python-format
msgid "80.000€"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>New</span>"
msgstr "<span>Nuut</span>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>Reports</span>"
msgstr "Verslae"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "<span>View</span>"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_active
msgid "Active"
msgstr "Aktief"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:114
#, python-format
msgid "Activities Done"
msgstr ""

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_act
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid "Click here to define a new sales team."
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:148
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:152
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:161
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:165
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:174
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:178
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:182
#, python-format
msgid "Click to set"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_code
msgid "Code"
msgstr "Kode"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_color
msgid "Color Index"
msgstr "Kleurindeks"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_company_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Company"
msgstr "Maatskappy"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:215
#, python-format
msgid "Create a few opportunities to activate your dashboard."
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_create_uid
msgid "Created by"
msgstr "Geskep deur"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_create_date
msgid "Created on"
msgstr "Geskep op"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/js/sales_team_dashboard.js:16
#: model:ir.actions.act_window,name:sales_team.crm_team_salesteams_act
#: model:ir.ui.menu,name:sales_team.menu_sales_team_act
#, python-format
msgid "Dashboard"
msgstr "Kontroleskerm"

#. module: sales_team
#: model:crm.team,name:sales_team.team_sales_department
msgid "Direct Sales"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_display_name
msgid "Display Name"
msgstr "Vertoningsnaam"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid ""
"Follow this salesteam to automatically track the events associated to users "
"of this team."
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:214
#, python-format
msgid "Great sales journeys start with a clean pipeline."
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Group By..."
msgstr "Groepeer op ..."

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:213
#, python-format
msgid "Hi there!"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_id
msgid "ID"
msgstr "ID"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_active
msgid ""
"If the active field is set to false, it will allow you to hide the sales "
"team without removing it."
msgstr ""

#. module: sales_team
#: model:crm.team,name:sales_team.crm_team_1
msgid "Indirect Sales"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:140
#, python-format
msgid "Invoiced"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team___last_update
msgid "Last Modified on"
msgstr "Laas Gewysig op"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:188
#, python-format
msgid "Last Month"
msgstr "Laas Maand"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_write_uid
msgid "Last Updated by"
msgstr "Laas Opgedateer deur"

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_write_date
msgid "Last Updated on"
msgstr "Laas Opgedateer op"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:25
#, python-format
msgid "Meetings"
msgstr "Vergaderings"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "More <i class=\"fa fa-caret-down\"/>"
msgstr "Meer <i class=\"fa fa-caret-down\"/>"

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "More Info"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:42
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:44
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:216
#, python-format
msgid "My Pipeline"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "My Salesteams"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:50
#, python-format
msgid "Next 7 days"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:38
#, python-format
msgid "Next Actions"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/js/sales_team_dashboard.js:84
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_partner
msgid "Partner"
msgstr "Ouer Venoot"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:102
#, python-format
msgid "Performance"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_reply_to
msgid "Reply-To"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_res_users_property_product_pricelist
msgid "Sale Pricelist"
msgstr ""

#. module: sales_team
#: model:ir.model,name:sales_team.model_crm_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_name
#: model:ir.model.fields,field_description:sales_team.field_res_partner_team_id
#: model:ir.model.fields,field_description:sales_team.field_res_users_sale_team_id
#: model:ir.model.fields,field_description:sales_team.field_res_users_team_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_tree
msgid "Sales Team"
msgstr "Verkoopspan"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users_sale_team_id
msgid ""
"Sales Team the user is member of. Used to compute the members of a sales "
"team through the inverse one2many"
msgstr ""

#. module: sales_team
#: model:ir.actions.act_window,name:sales_team.crm_team_act
#: model:ir.actions.act_window,name:sales_team.sales_team_config_action
msgid "Sales Teams"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Sales team"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Salesteam name..."
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Salesteams Search"
msgstr ""

#. module: sales_team
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_view_kanban
msgid "Settings"
msgstr "Stellings"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:146
#, python-format
msgid "Target"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_user_id
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_salesteams_search
msgid "Team Leader"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,field_description:sales_team.field_crm_team_member_ids
#: model_terms:ir.ui.view,arch_db:sales_team.crm_team_view_form
msgid "Team Members"
msgstr ""

#. module: sales_team
#: sql_constraint:crm.team:0
msgid "The code of the sales team must be unique !"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_color
msgid "The color of the team"
msgstr ""

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_crm_team_reply_to
msgid ""
"The email address put in the 'Reply-To' of all emails sent by Odoo about "
"cases in this sales team"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:102
#, python-format
msgid "This Month"
msgstr "Die Maand"

#. module: sales_team
#: model:ir.model.fields,help:sales_team.field_res_users_property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:29
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:61
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:76
#, python-format
msgid "To Activities"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:105
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:189
#, python-format
msgid "To Activity Report"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:16
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:51
#, python-format
msgid "To Calendar"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:131
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:199
#, python-format
msgid "To Invoice Report"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:86
#, python-format
msgid "To Opportunities"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:118
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:194
#, python-format
msgid "To Opportunity Report"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:13
#, python-format
msgid "To do"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:13
#, python-format
msgid "Today"
msgstr "Vandag"

#. module: sales_team
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_act
#: model_terms:ir.actions.act_window,help:sales_team.crm_team_salesteams_act
#: model_terms:ir.actions.act_window,help:sales_team.sales_team_config_action
msgid ""
"Use sales team to organize your different salespersons or\n"
"                    departments into separate teams. Each team will work in\n"
"                    its own list of opportunities."
msgstr ""

#. module: sales_team
#: model:ir.model,name:sales_team.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:127
#, python-format
msgid "Won in Opportunities"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/js/sales_team_dashboard.js:84
#, python-format
msgid "Wrong value entered!"
msgstr "Verkeerde waarde ingevoer!"

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:89
#, python-format
msgid "exp. closing"
msgstr ""

#. module: sales_team
#. openerp-web
#: code:addons/sales_team/static/src/xml/sales_team_dashboard.xml:79
#, python-format
msgid "overdue"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr "Aksie word benodig"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Datum van die laaste boodskap wat op die rekord gepos is."

#~ msgid "Followers"
#~ msgstr "Volgelinge"

#~ msgid "Followers (Channels)"
#~ msgstr "Volgelinge (Kanale)"

#~ msgid "Followers (Partners)"
#~ msgstr "Volgelinge (Venote)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "As dit gekies is vereis nuwe boodskappe jou aandag."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "As dit gekies is, vereis nuwe boodskappe jou aandag."

#~ msgid "Is Follower"
#~ msgstr "Is Volgeling"

#~ msgid "Last Message Date"
#~ msgstr "Laaste Boodskap Datum"

#~ msgid "Messages"
#~ msgstr "Boodskappe"

#~ msgid "Messages and communication history"
#~ msgstr "Boodskap-en kommunikasiegeskiedenis"

#~ msgid "Number of Actions"
#~ msgstr "Hoeveelheid Aksies"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Hoeveelheid boodskappe wat aksie vereis"

#~ msgid "Number of unread messages"
#~ msgstr "Hoeveelheid ongeleesde boodskappe"

#~ msgid "Unread Messages"
#~ msgstr "Ongeleesde Boodskappe"

#~ msgid "Unread Messages Counter"
#~ msgstr "Ongeleesde Boodskappe Teller"
