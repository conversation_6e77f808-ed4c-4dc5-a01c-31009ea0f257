<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_table_of_content" name="Table of Content">
    <section class="s_table_of_content pt24 pb24 o_cc o_cc1">
        <div class="container">
            <div class="row s_nb_column_fixed">
                <div class="col-lg-3 s_table_of_content_navbar_wrap s_table_of_content_navbar_sticky                         s_table_of_content_vertical_navbar d-print-none d-none d-lg-block o_not_editable o_cc o_cc1" data-name="Navbar">
                    <div class="s_table_of_content_navbar list-group o_no_link_popover"/>
                </div>
                <div class="col-lg-9 s_table_of_content_main oe_structure oe_empty" data-name="Content">
                    <section class="pb16">
                        <h1 data-anchor="true">Intuitive system</h1>
                        <h4>What you see is what you get</h4>
                        <p>
                            Insert text styles like headers, bold, italic, lists, and fonts with
                            a simple WYSIWYG editor. Flexible and easy to use.
                        </p>
                        <h4>Customization tool</h4>
                        <p>
                            Click and change content directly from the front-end: no complex back
                            end to deal with.
                        </p>
                        <h4>Building blocks system</h4>
                        <p>
                            Create your page from scratch by dragging and dropping pre-made,
                            fully customizable building blocks.
                        </p>
                    </section>
                    <section class="pb16">
                        <h1 data-anchor="true">Design features</h1>
                        <h4>Bootstrap-based templates</h4>
                        <p>
                            Easily design your own Odoo templates thanks to clean HTML
                            structure and bootstrap CSS.
                        </p>
                        <h4>Professional themes</h4>
                        <p>
                            Change theme in a few clicks, and browse through Odoo's catalog of
                            ready-to-use themes available in our app store.
                        </p>
                    </section>
                </div>
            </div>
        </div>
    </section>
</template>

<template id="s_table_of_content_options" inherit_id="website.snippet_options">
    <xpath expr="//t[@t-call='web_editor.snippet_options_background_options']" position="before">
        <div data-js="MultipleItems" data-selector=".s_table_of_content">
            <we-row string="Content">
                <we-button data-add-item="" data-item=".s_table_of_content_main > section:last" data-select-item="" data-no-preview="true" class="o_we_bg_brand_primary">
                    Add Item
                </we-button>
            </we-row>
        </div>
    </xpath>
    <xpath expr="." position="inside">
        <div data-js="TableOfContent" data-selector=".s_table_of_content"/>
        <div data-js="TableOfContentNavbar" data-selector=".s_table_of_content_navbar_wrap">
            <we-button-group string="Position">
                <we-button class="fa fa-fw fa-long-arrow-left" title="Left" data-navbar-position="left"/>
                <we-button class="fa fa-fw fa-long-arrow-up" title="Top" data-navbar-position="top"/>
                <we-button class="fa fa-fw fa-long-arrow-right" title="Right" data-navbar-position="right"/>
            </we-button-group>
            <we-checkbox string="Sticky" data-select-class="s_table_of_content_navbar_sticky" data-no-preview="true"/>
        </div>
        <div data-js="TableOfContentMainColumns" data-selector=".s_table_of_content_navbar_wrap, .s_table_of_content_main"/>
    </xpath>
</template>

<record id="website.s_table_of_content_000_scss" model="ir.asset">
    <field name="name">Table of content 000 SCSS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_table_of_content/000.scss</field>
</record>

<record id="website.s_table_of_content_000_js" model="ir.asset">
    <field name="name">Table of content 000 JS</field>
    <field name="bundle">web.assets_frontend</field>
    <field name="path">website/static/src/snippets/s_table_of_content/000.js</field>
</record>

</odoo>
