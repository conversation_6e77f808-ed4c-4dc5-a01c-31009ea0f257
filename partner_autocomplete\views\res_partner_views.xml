<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_res_partner_form_inherit_partner_autocomplete" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.partner.autocomplete</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_title')]/h1/field[@id='company']" position="attributes">
                <attribute name="widget">field_partner_autocomplete</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_title')]/h1/field[@id='individual']" position="attributes">
                <attribute name="widget">field_partner_autocomplete</attribute>
            </xpath>
            <xpath expr="//field[@name='vat']" position="attributes">
                <attribute name="widget">field_partner_autocomplete</attribute>
            </xpath>
            <xpath expr="//field[last()]" position="after">
                <field name="partner_gid" invisible="True"/>
                <field name="additional_info" invisible="True"/>
            </xpath>
        </field>
    </record>

    <record id="view_partner_simple_form_inherit_partner_autocomplete" model="ir.ui.view">
        <field name="name">res.partner.simplified.form.inherit.partner.autocomplete</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_simple_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('oe_title')]/h1/field[@id='company']" position="attributes">
                <attribute name="widget">field_partner_autocomplete</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_title')]/h1/field[@id='individual']" position="attributes">
                <attribute name="widget">field_partner_autocomplete</attribute>
            </xpath>
            <xpath expr="//field[last()]" position="after">
                <field name="partner_gid" invisible="True"/>
                <field name="additional_info" invisible="True"/>
            </xpath>
        </field>
    </record>
</odoo>
