# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * utm
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-03-22 15:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium_active
msgid "Active"
msgstr "Activo"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model:ir.model.fields,field_description:utm.field_account_invoice_campaign_id
#: model:ir.model.fields,field_description:utm.field_crm_lead_campaign_id
#: model:ir.model.fields,field_description:utm.field_hr_applicant_campaign_id
#: model:ir.model.fields,field_description:utm.field_link_tracker_campaign_id
#: model:ir.model.fields,field_description:utm.field_sale_order_campaign_id
#: model:ir.model.fields,field_description:utm.field_utm_mixin_campaign_id
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_tree
msgid "Campaign"
msgstr "Campaña"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_name
msgid "Campaign Name"
msgstr "Nombre de la Campaña"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_act
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
msgid "Campaigns"
msgstr "Campañas"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
msgid "Channel"
msgstr "Canal"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium_name
msgid "Channel Name"
msgstr "Nombre de canal"

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "Channels"
msgstr "Canales"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "Especial de Navidad"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Click to define a new medium."
msgstr "Haga clic para definir un nuevo medio."

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "Click to define a new source."
msgstr "Haga clic para definir una nueva fuente."

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium_create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium_create_date
#: model:ir.model.fields,field_description:utm.field_utm_source_create_date
msgid "Created on"
msgstr "Creado en"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium_display_name
#: model:ir.model.fields,field_description:utm.field_utm_mixin_display_name
#: model:ir.model.fields,field_description:utm.field_utm_source_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "Campaña de correo electrónico - Servicios"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP routing"
msgstr "Enrutado HTTP"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_id
#: model:ir.model.fields,field_description:utm.field_utm_medium_id
#: model:ir.model.fields,field_description:utm.field_utm_mixin_id
#: model:ir.model.fields,field_description:utm.field_utm_source_id
msgid "ID"
msgstr "ID"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign___last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium___last_update
#: model:ir.model.fields,field_description:utm.field_utm_mixin___last_update
#: model:ir.model.fields,field_description:utm.field_utm_source___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium_write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign_write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium_write_date
#: model:ir.model.fields,field_description:utm.field_utm_source_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Rastreador de Enlaces"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Mailing Partner"
msgstr "Correo socios"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.model.fields,field_description:utm.field_account_invoice_medium_id
#: model:ir.model.fields,field_description:utm.field_crm_lead_medium_id
#: model:ir.model.fields,field_description:utm.field_hr_applicant_medium_id
#: model:ir.model.fields,field_description:utm.field_link_tracker_medium_id
#: model:ir.model.fields,field_description:utm.field_sale_order_medium_id
#: model:ir.model.fields,field_description:utm.field_utm_mixin_medium_id
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Medium"
msgstr "Media"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "Boletín de noticias"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Venta"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "Motor de búsqueda"

#. module: utm
#: model:ir.model,name:utm.model_utm_source
#: model:ir.model.fields,field_description:utm.field_account_invoice_source_id
#: model:ir.model.fields,field_description:utm.field_crm_lead_source_id
#: model:ir.model.fields,field_description:utm.field_hr_applicant_source_id
#: model:ir.model.fields,field_description:utm.field_link_tracker_source_id
#: model:ir.model.fields,field_description:utm.field_sale_order_source_id
#: model:ir.model.fields,field_description:utm.field_utm_mixin_source_id
msgid "Source"
msgstr "Texto original"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source_name
msgid "Source Name"
msgstr "Nombre de la fuente"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Fuentes"

#. module: utm
#: model:ir.model.fields,help:utm.field_account_invoice_campaign_id
#: model:ir.model.fields,help:utm.field_crm_lead_campaign_id
#: model:ir.model.fields,help:utm.field_hr_applicant_campaign_id
#: model:ir.model.fields,help:utm.field_link_tracker_campaign_id
#: model:ir.model.fields,help:utm.field_sale_order_campaign_id
#: model:ir.model.fields,help:utm.field_utm_mixin_campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts "
"Ex: Fall_Drive, Christmas_Special"
msgstr ""
"Éste es un nombre que facilita la búsqueda de los diferentes esfuerzos de "
"campañas. Por ejemplo: campaña de Navidad, rebajas de otoño"

#. module: utm
#: model:ir.model.fields,help:utm.field_account_invoice_medium_id
#: model:ir.model.fields,help:utm.field_crm_lead_medium_id
#: model:ir.model.fields,help:utm.field_hr_applicant_medium_id
#: model:ir.model.fields,help:utm.field_link_tracker_medium_id
#: model:ir.model.fields,help:utm.field_sale_order_medium_id
#: model:ir.model.fields,help:utm.field_utm_mixin_medium_id
#, fuzzy
msgid "This is the method of delivery.Ex: Postcard, Email, or Banner Ad"
msgstr ""
"Éste es el método de envío. Por ejemplo: carta postal, correo electrónico, "
"publicidad web"

#. module: utm
#: model:ir.model.fields,help:utm.field_account_invoice_source_id
#: model:ir.model.fields,help:utm.field_crm_lead_source_id
#: model:ir.model.fields,help:utm.field_hr_applicant_source_id
#: model:ir.model.fields,help:utm.field_link_tracker_source_id
#: model:ir.model.fields,help:utm.field_sale_order_source_id
#: model:ir.model.fields,help:utm.field_utm_mixin_source_id
#, fuzzy
msgid ""
"This is the source of the link Ex:Search Engine, another domain,or name of "
"email list"
msgstr ""
"Ésta es la fuente del enlace. Por ejemplo: motor de búsqueda, otro dominio o "
"nombre de unos lista de correos"

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTMs"

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "utm.mixin"
msgstr "utm.mixin"

#~ msgid "Email Campaign - Products"
#~ msgstr "Campaña de correo electrónico - Productos"
