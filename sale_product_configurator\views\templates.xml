<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="optional_products_modal" name="Optional Products">
        <main class="modal-body">
            <t t-call="sale_product_configurator.configure_optional_products" />
        </main>
    </template>

    <template id="product_quantity_config">
        <div class="css_quantity input-group">
            <div class="input-group-prepend">
                <button t-attf-href="#" class="btn btn-primary float_left js_add_cart_json d-none d-md-inline-block" aria-label="Remove one" title="Remove one">
                    <i class="fa fa-minus"></i>
                </button>
            </div>
            <input type="text" class="js_quantity form-control quantity" data-min="1" name="add_qty" t-att-value="add_qty or 1"/>
            <div class="input-group-append">
                <button t-attf-href="#" class="btn btn-primary float_left js_add_cart_json d-none d-md-inline-block" aria-label="Add one" title="Add one">
                    <i class="fa fa-plus"></i>
                </button>
            </div>
        </div>
    </template>

    <!-- backend -->
    <template id="configure" name="Configure">
        <div class="js_product main_product">

            <t t-set="combination" t-value="product_combination if product_combination else product._get_first_possible_combination()"/>
            <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1, pricelist=pricelist)"/>
            <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>

            <input type="hidden" class="product_template_id" t-att-value="product.id"/>
            <input type="hidden" class="product_id" t-attf-name="product_id" t-att-value="product_variant.id"/>
            <input type="hidden" class="has_optional_products" t-attf-name="has_optional_products"
                   t-att-value="product_variant.optional_product_ids.filtered(lambda p: p._is_add_to_cart_possible(combination))"/>
            <div class="col-lg-12 text-center mt-5">
                <t t-if="product._is_add_to_cart_possible()">
                    <div class="col-lg-5 d-inline-block text-left">
                        <t t-if="combination" t-call="sale.variants">
                            <t t-set="parent_combination" t-value="None"/>
                        </t>
                        <h2>
                            <span t-attf-class="text-danger oe_default_price oe_striked_price {{'' if combination_info['has_discounted_price'] else 'd-none'}}"
                                t-esc="combination_info['list_price']"
                                t-options='{
                                    "widget": "monetary",
                                    "display_currency": (pricelist or product).currency_id
                                }'/>
                            <span class="oe_price product_id mt-3" style="white-space: nowrap;"
                                t-att-data-product-id="product.id"
                                t-esc="combination_info['price']"
                                t-options='{
                                    "widget": "monetary",
                                    "display_currency": (pricelist or product).currency_id
                                }'/>
                        </h2>
                        <t t-if="product.visible_qty_configurator">
                            <t t-call="sale_product_configurator.product_quantity_config" />
                        </t>
                        <p class="css_not_available_msg alert alert-warning">This combination does not exist.</p>
                    </div>
                    <div class="col-lg-1 d-inline-block"></div>
                    <div class="col-lg-5 d-inline-block align-top text-left">
                        <img t-if="product_variant" t-att-src="'/web/image/product.product/%s/image_1024' % product_variant.id" class="d-block product_detail_img" alt="Product Image"/>
                        <img t-else="" t-att-src="'/web/image/product.template/%s/image_1024' % product.id" class="d-block product_detail_img" alt="Product Image"/>
                    </div>
                </t>
                <t t-else="">
                    <div class="col-lg-5 d-inline-block text-left">
                        <p class="alert alert-warning">This product has no valid combination.</p>
                    </div>
                </t>
            </div>
        </div>
    </template>

    <!-- modal: full table, currenclty selected products at top -->
    <template id="configure_optional_products">
        <table class="table table-striped table-sm">
        <thead>
            <tr>
                <th class="td-img"><span class='label'>Product</span></th>
                <th><span class='label'></span></th>
                <th class="text-center td-qty"><span class='label'>Quantity</span></th>
                <th class="text-center td-price"><span class='label'>Price</span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="js_product in_cart main_product">

                <t t-set="combination_info" t-value="product.product_tmpl_id._get_combination_info(combination, product.id, add_qty or 1, pricelist)"/>
                <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>

                <input type="hidden" class="product_template_id" t-att-value="product.product_tmpl_id.id"/>
                <input type="hidden" class="product_id" t-att-value="product_variant.id"/>
                <td class='td-img'>
                    <img class="product_detail_img" t-if="product_variant" t-att-src="'/web/image/product.product/%s/image_128' % product_variant.id" alt="Product Image"/>
                    <img class="product_detail_img" t-else="" t-att-src="'/web/image/product.template/%s/image_128' % product.id" alt="Product Image"/>
                </td>
                <td class='td-product_name'>
                    <strong class="product-name product_display_name" t-esc="combination_info['display_name']"/>
                    <div class="text-muted small">
                        <div t-field="product.description_sale"/>
                        <div class="js_attributes"/>
                    </div>
                    <div>
                        <t t-if="product.product_tmpl_id and not combination">
                            <t t-set="combination" t-value="product.product_tmpl_id._get_first_possible_combination()"/>
                        </t>
                        <t t-if="combination and not already_configured" t-call="sale.variants">
                            <t t-set="ul_class" t-valuef="flex-column" />
                            <t t-set="product" t-value="product.product_tmpl_id"/>
                        </t>
                        <t t-else="">
                            <ul class="d-none js_add_cart_variants" t-att-data-attribute_exclusions="{'exclusions: []'}"/>
                            <div class="d-none oe_unchanged_value_ids" t-att-data-unchanged_value_ids="variant_values" ></div>
                            <!-- Keep the information to use it later (when leaving the modal window) -->
                            <div class="d-none no-attribute-info" t-att-data-attribute-value='json.dumps(no_attribute)'></div>
                            <div class="d-none custom-attribute-info" t-att-data-attribute-value='json.dumps(custom_attribute)'></div>
                        </t>
                    </div>
                </td>
                <td class="text-center td-qty">
                    <t t-call='sale_product_configurator.product_quantity_config' />
                </td>
                <td class="text-center td-price" name="price">
                    <div t-attf-class="text-danger oe_default_price oe_striked_price {{'' if combination_info['has_discounted_price'] else 'd-none'}}"
                        t-esc="combination_info['list_price']"
                        t-options='{
                            "widget": "monetary",
                            "display_currency": (pricelist or product).currency_id
                        }'
                    />
                    <span class="oe_price product_id" style="white-space: nowrap;"
                        t-att-data-product-id="product.id"
                        t-esc="combination_info['price']"
                        t-options='{
                            "widget": "monetary",
                            "display_currency": (pricelist or product).currency_id
                        }'/>
                    <span class="js_raw_price d-none" t-esc="product.price"/>
                    <p class="css_not_available_msg alert alert-warning">Option not available</p>
                </td>
            </tr>
            <tr class="o_total_row">
                <td colspan="4" class="text-right">
                    <strong>Total:</strong>
                    <span class="js_price_total font-weight-bold" style="white-space: nowrap;"
                        t-att-data-product-id="product.id"
                        t-esc="combination_info['price'] * (add_qty or 1)"
                        t-options='{
                            "widget": "monetary",
                            "display_currency": (pricelist or product).currency_id
                        }'/>
                </td>
            </tr>
            <tr t-if="product.optional_product_ids" class="o_select_options"><td colspan="4"><h4>Available Options:</h4></td></tr>
            <t t-call="sale_product_configurator.optional_product_items">
                <t t-set="parent_combination" t-value="combination"/>
            </t>
        </tbody>
        </table>
    </template>

    <!-- modal: optional products -->
    <template id="optional_product_items">
        <t t-foreach="product.optional_product_ids" t-as="product">
            <t t-set="combination" t-value="product._get_first_possible_combination(parent_combination)"/>
            <t t-if="product._is_add_to_cart_possible(parent_combination)">

                <t t-set="combination_info" t-value="product._get_combination_info(combination, add_qty=add_qty or 1, pricelist=pricelist)"/>
                <t t-set="product_variant" t-value="product.env['product.product'].browse(combination_info['product_id'])"/>

                <tr class="js_product">
                    <td class="td-img">
                        <input type="hidden" class="product_template_id" t-att-value="product.id"/>
                        <input type="hidden" class="product_id" t-attf-name="optional-product-#{product.id}" t-att-value="product_variant.id"/>
                        <img t-if="product_variant" t-att-src="'/web/image/product.product/%s/image_128' % product_variant.id"  class="variant_image" alt="Product Image"/>
                        <img t-else="" t-att-src="'/web/image/product.template/%s/image_128' % product.id"  class="variant_image" alt="Product Image"/>
                    </td>
                    <td class='td-product_name' colspan="2">
                        <div class="mb-3">
                            <strong class="product-name product_display_name" t-esc="combination_info['display_name']"/>
                            <div class="text-muted small" t-field="product.description_sale"/>
                        </div>
                        <t t-call="sale.variants">
                            <t t-set="combination" t-value="product._get_first_possible_combination(parent_combination)"/>
                        </t>
                    </td>
                    <td class="text-center td-qty d-none">
                        <t t-call='sale_product_configurator.product_quantity_config' />
                    </td>
                    <td class="text-center td-price">
                        <div t-attf-class="text-danger oe_default_price oe_optional oe_striked_price {{'' if combination_info['has_discounted_price'] else 'd-none'}}"
                            t-esc="combination_info['list_price']"
                            t-options='{
                                "widget": "monetary",
                                "display_currency": (pricelist or product).currency_id
                            }'/>
                        <div class="oe_price" style="white-space: nowrap;"
                            t-esc="combination_info['price']"
                            t-options='{
                                "widget": "monetary",
                                "display_currency": (pricelist or product).currency_id
                            }'/>
                        <span class="js_raw_price d-none" t-esc="combination_info['price']" />
                        <p class="css_not_available_msg alert alert-warning">Option not available</p>

                        <a role="button" href="#" class="js_add btn btn-primary btn-sm"><i class="fa fa-shopping-cart add-optionnal-item"></i> Add to cart</a>
                        <span class="js_remove d-none">
                            <a role="button" href="#" class="js_remove"><i class="fa fa-trash-o remove-optionnal-item"></i></a>
                        </span>
                    </td>
                </tr>
            </t>
        </t>
    </template>
</odoo>
