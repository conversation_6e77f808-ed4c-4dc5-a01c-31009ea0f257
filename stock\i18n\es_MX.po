# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# Iran Villalobos López, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"%s --> Product UoM is %s (%s) - Move UoM is %s (%s)"
msgstr ""
"\n"
"\n"
"%s--> UdM del producto es %s (%s) - UdM del movimiento es %s (%s)"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"\n"
"Blocking: %s"
msgstr ""
"\n"
"\n"
"Bloqueo: %s"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You cannot validate these transfers if no quantities are reserved nor done. To force these transfers, switch in edit more and encode the done quantities."
msgstr ""
"\n"
"\n"
"Traslados %s: no puede validar estos traslados si no se reservan ni se realizan cantidades. Para forzar estos traslados, cambie a editar más y codifique las cantidades realizadas."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"\n"
"Transfers %s: You need to supply a Lot/Serial number for products %s."
msgstr ""
"\n"
"\n"
"Traslados %s: debe proporcionar un número de lote/de serie para los productos %s."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"\n"
"(%s) exists in location %s"
msgstr ""
"\n"
"(%s) existe en la ubicación %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
"* Borrador: El traslado aún no ha sido confirmado. No se realizan reservas.\n"
" * En espera de otra operación: El traslado espera otra operación antes de estar preparado.\n"
" * En espera: El traslado está en espera de disponibilidad de algunos productos.\n"
"(a) La política de envío es \"Tan pronto como sea posible\": no se pudo reservar ningún producto.\n"
"(b) La política de envío es \"Cuando todos los productos estén listos\": no se pueden reservar todos los productos.\n"
" * Preparado: El traslado está listo para ser procesado.\n"
"(a) La política de envío es \"Tan pronto como sea posible\": por lo menos se reservó un producto.\n"
"(b) La política de envío es \"Cuando todos los productos estén listos\": se reservaron todos los productos.\n"
" * Hecho: se procesó el traslado.\n"
" * Cancelado: se canceló el traslado."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid " - Product: %s, Serial Number: %s"
msgstr " - Producto: %s, número de serie: %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr ""
"Cuando es diferente de 0, la fecha de recuento de inventario para los "
"productos almacenados en esta ubicación se establecerá automáticamente con "
"la frecuencia definida."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s: Suministrar producto de %(supplier)s"

#. module: stock
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"%s use default source or destination locations from warehouse %s that will "
"be archived."
msgstr ""
"%s utilice las ubicaciones de origen o destino predeterminadas del almacén "
"%s que se archivarán."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "&gt;"
msgstr "&gt;"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "\"Hoja de conteos\""

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Recibo de entrega - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'Ubicación- %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'Lote-Serie - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'Tipo de operación - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr ""
"'Operaciones de recolección - %s - %s' % (object.partner_id.name or '', "
"object.name)"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "(copy of) %s"
msgstr "(copia de) %s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: When the stock move is created and not yet confirmed.\n"
"* Waiting Another Move: This state can be seen when a move is waiting for another one, for example in a chained flow.\n"
"* Waiting Availability: This state is reached when the procurement resolution is not straight forward. It may need the scheduler to run, a component to be manufactured...\n"
"* Available: When products are reserved, it is set to 'Available'.\n"
"* Done: When the shipment is processed, the state is 'Done'."
msgstr ""
"*  Nuevo: Cuando se crea el movimiento de existencias y aún no se confirma.\n"
"* En espera de otro movimiento: Este estado aparece cuando un movimiento está en espera de otro, por ejemplo en un flujo encadenado.\n"
"* En espera de disponibilidad: Este estado se alcanza cuando no hay existencias disponibles. Tal vez necesite ejecutar el planificador, un componente a fabricar...\n"
"* Disponible: Cuando los productos están reservados, se establece en 'Disponible'.\n"
"* Hecho: Cuando se procesa el envío, el estado es 'Hecho'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* Ubicación del proveedor: ubicación virtual que representa la ubicación de origen de los productos que provienen de sus proveedores\n"
"* Vista: ubicación virtual que se utiliza para crear estructuras jerárquicas para su almacén, agregan sus ubicaciones secundarias; no puede contener productos directamente\n"
"* Ubicación interna: ubicaciones físicas dentro de sus propios almacenes,\n"
"* Ubicación del cliente: ubicación virtual que representa la ubicación de destino de los productos enviados a sus clientes\n"
"* Pérdida de inventario: ubicación virtual que sirve como contraparte para las operaciones de inventario que se utilizan para corregir los niveles de existencias (inventarios físicos)\n"
"* Producción: ubicación de contraparte virtual para operaciones de producción: esta ubicación consume los componentes y produce productos terminados\n"
"* Ubicación de tránsito: ubicación de contraparte que se debe usar en operaciones entre empresas o entre almacenes"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "+ %d day(s)"
msgstr "+ %d día(s)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", máximo:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            Puede que se necesiten acciones manuales."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 Día"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 Mes"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 Semana"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid ": Insufficient Quantity To Scrap"
msgstr ": Cantidad insuficiente para desechar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>Inventario actual: </strong>"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr ""
"<br>Se crea una necesidad en <b>%s</b> y se activará una regla para "
"cumplirla."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring products in this location."
msgstr ""
"<br>Si los productos no están disponibles en <b>%s</b>, se activará una "
"regla para llevar productos a esta ubicación."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hola <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        Nos complace informarle que se ha enviado su orden.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Su referencia de rastreo es\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Encontrará más detalles en la orden de entrega adjunta.<br/><br/>\n"
"        Gracias,\n"
"        <t t-if=\"user.signature\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" aria-label=\"Periodo\" role=\"img\" title=\"Periodo\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Gestionar\" "
"title=\"Gestionar\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                No se pudieron reservar todos los productos. Haga clic en el botón \"comprobar disponibilidad\" para intentar reservar productos."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Unfold\" "
"title=\"Unfold\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-caret-right\" role=\"img\" aria-label=\"Desplegar\" "
"title=\"Desplegar\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"
msgstr ""
"<span attrs=\"{'invisible': ['|', ('state', '=', 'done'), "
"('from_immediate_transfer', '=', True)]}\"> / </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Label</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">Imprimir etiqueta</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid ""
"<span class=\"d-none d-sm-block o_print_label_text\">Print Labels</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"
msgstr ""
"<span class=\"d-none d-sm-block o_print_label_text\">Imprimir etiquetas</span>\n"
"                                        <span class=\"d-block d-sm-none fa fa-print\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\" groups=\"Los valores establecidos aquí son"
" específicos de la empresa.\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">Pronosticado</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">In:</span>\n"
"                                <span class=\"o_stat_text\">Out:</span>"
msgstr ""
"<span class=\"o_stat_text\">Entrada:</span>\n"
"                                <span class=\"o_stat_text\">Salida:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid ""
"<span class=\"o_stat_text\">Min:</span>\n"
"                                <span class=\"o_stat_text\">Max:</span>"
msgstr ""
"<span class=\"o_stat_text\">Mínimo:</span>\n"
"                                <span class=\"o_stat_text\">Máximo:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">A la mano</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">Operaciones</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Transfers</span>"
msgstr "<span class=\"o_stat_text\">Traslados</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_mrp_line
msgid ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Traceability "
"Report\" aria-label=\"Traceability Report\"><i class=\"fa fa-fw fa-level-up "
"fa-rotate-270\"/></span>"
msgstr ""
"<span role=\"img\" class=\"o_stock_reports_stream\" title=\"Reporte de "
"trazabilidad\" aria-label=\"Reporte de trazabilidad\"><i class=\"fa fa-fw "
"fa-level-up fa-rotate-270\"/></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Customer Address:</strong></span>"
msgstr "<span><strong>Dirección del cliente:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>Dirección de envío:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Vendor Address:</strong></span>"
msgstr "<span><strong>Dirección de proveedor:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Warehouse Address:</strong></span>"
msgstr "<span><strong>Dirección de almacén:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Assign Serial Numbers</span>"
msgstr "<span>Asignar números de serie</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "<span>Clear All</span>"
msgstr "<span>Limpiar todo</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "<span>LN/SN:</span>"
msgstr "<span>Número de serie/de lote:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span>New</span>"
msgstr "<span>Nuevo</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>Tipo de paquete:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>Productos sin paquete asignado</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>Cantidades restantes aún sin entregar:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                La línea de movimiento hecha ha sido corregida.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Available Quantity</strong>"
msgstr "<strong>Cantidad disponible</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Counted Quantity</strong>"
msgstr "<strong>Cantidad contada</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Delivered</strong>"
msgstr "<strong>Entregado</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>Debido a algunos movimientos de existencias realizados entre su "
"actualización de la cantidad inicial y ahora, la diferencia en la cantidad "
"ya no es consistente.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>Desde</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Location</strong>"
msgstr "<strong>Ubicación</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>Número de lote/serie</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty :</strong>"
msgstr "<strong>Cantidad máxima:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty :</strong>"
msgstr "<strong>Cantidad mínima:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>On hand Quantity</strong>"
msgstr "<strong>Cantidad a la mano</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order:</strong>"
msgstr "<strong>Orden:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Ordered</strong>"
msgstr "<strong>Ordenado</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>Tipo de paquete:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "<strong>Package</strong>"
msgstr "<strong>Paquete</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>Código de barras del producto</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>Producto</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>Cantidad</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date:</strong>"
msgstr "<strong>Fecha programada:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date:</strong>"
msgstr "<strong>Fecha de envío:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Firma</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status:</strong>"
msgstr "<strong>Estado:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>Se actualizó la demanda inicial.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>A</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>Productos rastrados:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products ?</strong>"
msgstr "<strong>¿A dónde desea enviar estos productos?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? Esto puede causar inconsistencias en su inventario."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type !"
msgstr "¡Solo se puede asignar un código de barras a un tipo de producto!"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "A done move line should never have a reserved quantity."
msgstr ""
"Una línea de movimiento hecha nunca debe tener una cantidad reservada."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__detailed_type
#: model:ir.model.fields,help:stock.field_product_template__detailed_type
#: model:ir.model.fields,help:stock.field_stock_move__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Un producto almacenable es un producto para el que gestiona existencias. Debe tener instalada la aplicación Inventario.\n"
"Un producto consumible es un producto para el que no gestiona las existencias.\n"
"Un servicio es un producto no material."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "Se puede establecer una advertencia en un contacto (existencias)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "Acción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_location_route__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "Activo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de actividad de excepción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "Agregar un producto"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "Agregar un numero de lote/serie"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "Agregar una nueva ubicación"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "Agregar una nueva ruta"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "Agregar una nueva categoría de almacenamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr ""
"Agregue una nota interna que se imprimirá en la hoja de operaciones de "
"recolección"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Agregue y personalice las operaciones de ruta para procesar movimientos de productos en sus almacenes: por ejemplo, descarga > control de calidad > existencias para productos entrantes, recolección > empaquetado > envío para productos salientes.\n"
" También puede establecer estrategias de almacenamiento en ubicaciones de almacén para enviar productos entrantes a ubicaciones secundarias específicas de inmediato (por ejemplo, contenedores específicos, bastidores)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"Agregue y personalice las operaciones de ruta para procesar movimientos de "
"productos en sus almacenes: por ejemplo, descarga > control de calidad > "
"existencias para productos entrantes, selección > empaquetado > envío para "
"productos salientes. También puede establecer estrategias de almacenamiento "
"en ubicaciones de almacén para enviar productos entrantes a ubicaciones "
"secundarias específicas de inmediato (por ejemplo, contenedores específicos,"
" bastidores)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "Agregue controles de calidad a sus operaciones de traslado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "Información adicional"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "Información adicional"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
msgid "Address"
msgstr "Dirección"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "Dirección a la que se deben entregar los productos. Opcional."

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "Administrador"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "Programación avanzada"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "Avanzado: aplicar reglas de aprovisionamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "Todos"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "Todos los traslados"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "Todo a la vez"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr ""
"Todas las relaciones contractuales se regirán exclusivamente por las leyes "
"de los Estados Unidos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "Todas los movimientos de devolución"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Allocation"
msgstr "Asignación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "Permitir nuevo producto"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "Permitir productos mezlcados "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "Ubicación permitida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "Día y mes del inventario anual"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "Mes del inventario anual"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"Mes del inventario anual para productos que no están en una ubicación con "
"una fecha de inventario cíclica. Establecer como \"sin mes\" si no hay "
"inventario anual automático."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "Aplicabilidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "Aplicable en"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "Aplicable en el empaquetado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_selectable
msgid "Applicable on Product"
msgstr "Aplicable en el producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "Aplicable en la categoria de productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "Aplicable en el almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply"
msgstr "Aplicar"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_ids
msgid ""
"Apply specific route(s) for the replenishment instead of product's default "
"routes."
msgstr ""
"Aplique rutas específicas para el reabastecimiento en lugar de las rutas "
"predeterminadas del producto."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "Abril"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "Archivado"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
msgid "As soon as possible"
msgstr "Lo antes posible"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign"
msgstr "Asignar"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Assign All"
msgstr "Asignar todos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "Asignar propietario"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Assign Serial Numbers"
msgstr "Asignar números de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "Movimientos asignados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "Asignada a"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "En la confirmación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributos"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "Agosto"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "Auto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate Orders"
msgstr "Automatizar órdenes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "Movimiento automático"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "Automático sin agregar paso"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Automatically open reception report when a receipt is validated."
msgstr ""
"Abrir el reporte de recepción de forma automática cuando se valida un "
"recibo."

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#, python-format
msgid "Available"
msgstr "Disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "Productos disponibles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "Cantidad disponible"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Available quantity should be set to zero before changing type"
msgstr ""
"La cantidad disponible debe establecerse en cero antes de cambiar el tipo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "Orden parcial de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Back Orders"
msgstr "Órdenes parciales"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "Confirmación de orden parcial"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "Línea de confirmación de órdenes parciales"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "Líneas de confirmación de órdenes parciales"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "Creación de órdenes parciales"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "Órdenes parciales"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "Código de barras"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "Nomenclaturas de código de barras"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regla de código de barras"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Lector de códigos de barras"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch Transfers"
msgstr "Traslados por lote"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "Antes de la fecha programada"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr ""
"El texto a continuación sirve como sugerencia y no hace responsable a Odoo "
"S.A."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "Mensaje de bloqueo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "Contenido completo"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "Por lotes"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "Por número de serie único"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"De forma predeterminada, el sistema tomará las unidades desde las "
"existencias en la ubicación fuente y esperará pasivamente a su "
"disponibilidad. La otra posibilidad le permite crear un aprovisionamiento en"
" la ubicación de origen (y por lo tanto, ignorar las existencias actuales) "
"para obtener productos. Si desea encadenar movimientos y hacer que éste "
"espere al anterior, debe elegir la segunda opción."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr ""
"Si el campo activo se desmarca, permite ocultar la ubicación sin eliminarla."

#. module: stock
#: model:product.product,name:stock.product_cable_management_box
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "Caja de administración de cable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "Vista de calendario"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any customer or supplier location."
msgstr "No se puede encontrar ninguna ubicación de cliente o de proveedor"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Can't find any generic route %s."
msgstr "No se puede encontrar ninguna ruta genérica %s."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_assign_serial_numbers
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "Cancelar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "Cancelar siguiente movimiento"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled"
msgstr "Cancelado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Cancelled Moves"
msgstr "Movimientos cancelados"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Cannot set the done quantity from this stock move, work directly with the "
"move lines."
msgstr ""
"No se puede establecer la cantidad realizada a partir de este movimiento de "
"existencias, trabaje directamente con las líneas de movimiento."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "Capacidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "Capacidad por paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "Capacidad por producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Categorize your locations for smarter putaway rules"
msgstr ""
"Categorice sus ubicaciones para obtener reglas de almacenamiento más "
"inteligentes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "Categoría"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "Rutas de categoría"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"Algunos países aplican retenciones en origen sobre el importe de las "
"facturas, de acuerdo con su legislación interna. Cualquier retención en "
"origen será pagada por el cliente a las autoridades fiscales. Mi Empresa "
"(Chicago) no puede en ningún caso implicarse en los costos relacionados con "
"la legislación de un país. Por lo tanto, el importe de la factura sera una "
"deuda a Mi Empresa (Chicago) en su totalidad y no incluye ningún costo "
"relacionado con la legislación del país en el que se encuentra el cliente."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__move_dest_exists
msgid "Chained Move Exists"
msgstr "El movimiento encadenado existe"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "Cambiar cantidad de producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__move_id
msgid "Change to a better name"
msgstr "Cambiar a un mejor nombre"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Changing %s is restricted, you can't do this operation."
msgstr "Cambiar %s está restringido, no puede realizar esta operación."

#. module: stock
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_production_lot.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr ""
"En este momento, está prohibido cambiar la empresa de este registro, debe "
"archivarlo y crear uno nuevo."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Changing the operation type of this record is forbidden at this point."
msgstr ""
"En este momento, está prohibido cambiar el tipo de operación de este "
"registro."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "Cambiar el producto solo está permitido en estado 'Borrador'."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "Comprobar disponibilidad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr ""
"Compruebe la existencia de paquetes de destino en líneas de movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "Compruebe la existencia de la operación del paquete en la recolección"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__return_location
msgid "Check this box to allow using this location as a return location."
msgstr ""
"Seleccione esta casilla para permitir el uso de esta ubicación como "
"ubicación de devolución."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr ""
"Seleccione esta opción para permitir utilizar esta ubicación para poner "
"mercancías desechadas/defectuosas."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose Labels Layout"
msgstr "Elegir diseño de etiquetas"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "Elija una fecha para obtener el inventario en esa fecha"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Choose destination location"
msgstr "Elija la ubicación de destino"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "Elija el diseño de la hoja para imprimir etiquetas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "Elija su fecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "Limpiar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Clear Lines"
msgstr "Limpiar líneas"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Code"
msgstr "Código"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
msgid "Color"
msgstr "Color"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Company"
msgstr "Empresa"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "Calcular costos de envío"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Calcular costos de envío y enviar con DHL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Calcular costos de envío y enviar con Easypost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Calcular costos de envío y enviar con FedEx"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Calcular costos de envío y enviar con UPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Calcular costos de envío y enviar con USPS"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Calcular costos de envío y enviar con bpost"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "Configuración"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Confirm"
msgstr "Confirmar"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "Confirmado"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "Conflicto en el inventario"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Conflict in Inventory Adjustment"
msgstr "Conflicto en el ajuste de inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
msgid "Conflicts"
msgstr "Conflictos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "Consigna"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "Línea de consumo"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "Contacto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "Contiene"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "Contenido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "Continuar"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"La conversión entre las unidades de medidas solo puede ocurrir si pertenecen"
" a la misma categoría. La conversión se basará en los cambios establecidos."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "Pasillo (X)"

#. module: stock
#: code:addons/stock/wizard/stock_immediate_transfer.py:0
#, python-format
msgid ""
"Could not reserve all requested products. Please use the 'Mark as Todo' "
"button to handle the reservation manually."
msgstr ""
"No se pudieron reservar todos los productos solicitados. Utilice el botón "
"\"Marcar como por realizar\" para gestionar la reserva manualmente."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "Número"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "Número de recolecciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "Número de recolecciones de órdenes parciales"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "Número de borradores de recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "Número de recolecciones atrasadas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "Número de recolecciones listas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "Número de recolecciones en espera"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "Hoja de conteos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "Cantidades contadas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "Ubicaciones contrapartes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "Crear orden parcial"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Create Backorder?"
msgstr "¿Crear orden parcial?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "Crear nuevos números de lote/de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                        products later. Do not create a backorder if you will not\n"
"                        process the remaining products."
msgstr ""
"Cree una orden parcial si espera procesar los productos\n"
"                        restantes más tarde. No cree una orden parcial si no\n"
"                        procesará los productos restantes."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "Crear un nuevo tipo de operación"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "Crear un nuevo paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "Cree hojas de trabajo personalizables para sus controles de calidad"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr ""
"Cree nuevas reglas de almacenamiento para enviar automáticamente productos "
"específicos a su ubicación de destino adecuada en las recepciones."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__create_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "Creado el"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr ""
"Crear un nuevo almacén activará automáticamente los ajustes de ubicación de "
"almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "Fecha de creación, usualmente la de la orden"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Cross-Dock"
msgstr "Sin almacenaje intermedio (cross dock)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "Ruta sin almacenes intermedios (cross docking)"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "Existencias actuales"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Cantidad actual de los productos.\n"
"En un contexto de una sola ubicación de existencias, esto incluye los artículos que se almacenan en esta ubicación, o cualquier ubicación secundaria.\n"
"En un contexto de un solo almacén, esto incluye los artículos que se almacenan en la ubicación de existencias de ese almacén, o cualquier almacén secundario.\n"
"En cualquier otro caso, esto incluye los artículos que se almacenan en cualquier ubicación de existencias de tipo 'Interna'."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "Personalizar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "Cliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "Plazo de entrega del cliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "Ubicación de cliente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "Ubicaciones de cliente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Inventory"
msgstr "Inventario cíclico"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "Conector de DHL Express"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "Fecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "Procesamiento de fecha"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid "Date Promise to the customer on the top level document (SO/PO)"
msgstr ""
"Promesa de fecha al cliente en el documento de nivel superior (orden de "
"venta/de compra)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "Fecha programada"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "Fecha en la que el reabastecimiento debe ocurrir."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "Fecha en la que se procesó o canceló el traslado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "Fecha para el próximo inventario planeado según un programa cíclico."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "Fecha de traslado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "Fecha del último inventario en esta ubicación."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "Fecha para reservar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "Día y mes en que el conteo de inventario anual debe ocurrir."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "Día del mes"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Día del mes en el que se debe realizar el inventario anual. Si es cero o negativo, se seleccionará el primer día del mes.\n"
"        Si es mayor que el último día de un mes, se seleccionará el último día del mes."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "Días"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "Días cuando se destacó"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "Fecha límite"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "Fecha límite superada y/o por el programado"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Deadline updated due to delay on %s"
msgstr "Se actualizó la fecha límite debido a un retraso el %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "Diciembre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
msgid "Default Destination Location"
msgstr "Ubicación de destino predeterminada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
msgid "Default Source Location"
msgstr "Ubicación de origen predeterminada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "Ruta de entrada a seguir predeterminada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "Ruta de salida a seguir predeterminada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unidad de medida predeterminada que se utiliza para todas las operaciones de"
" existencias."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "Predeterminado: Tomar de las existencias"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "Rutas predeterminadas a través del almacén"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo creates automatically requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr ""
"Defina una regla de existencias mínimas para que Odoo cree solicitudes de "
"cotizaciones u órdenes de fabricación de forma automática para reabastecer "
"sus existencias."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "Defina un nuevo almacén"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"Defina sus ubicaciones para reflejar la estructura de su almacén y de su\n"
"            organización. Odoo es capaz de gestionar ubicaciones físicas\n"
"            (almacenes, estanterías, contenedores, etc.), ubicaciones de partners (clientes,\n"
"            proveedores) y ubicaciones virtuales que se utilizan como contraparte en\n"
"            operaciones de existencias como las órdenes de fabricación,\n"
"            consumos, inventarios, etc."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Define el método predeterminado que se utiliza para sugerir la ubicación específica (estante) de la que se tomarán los productos, qué lote, etc. para esta ubicación. Este método se puede aplicar en el nivel de la categoría de producto, y se hace una alternativa en las ubicaciones padre si no se establece ninguna aquí.\n"
"\n"
"PEPS: los productos o lotes que se almacenaron primero se moverán primero.\n"
"UEPS: los productos o lotes que se almacenaron al último se moverán primero.\n"
"Ubicación más cercana: los productos o lotes más cercanos a la ubicación de destino se moverán primero.\n"
"FEFO: los productos o lotes con la fecha de remoción más cercana se moverán primero (la disponibilidad de este método depende del ajuste de \"Fechas de caducidad\")."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "Fecha de alerta de retraso"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Delay on %s"
msgstr "Retraso en %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver goods directly (1 step)"
msgstr "Entregar artículos directamente (1 paso)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 1 step (ship)"
msgstr "Entregar en 1 paso (envío)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 2 steps (pick + ship)"
msgstr "Entregar en 2 pasos (empaquetado + envío)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "Entregar en 3 pasos (recolección + empaquetado + envío)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Delivered Qty"
msgstr "Cant. entregada"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Delivery"
msgstr "Entrega"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "Dirección de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Métodos de envío"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_out
#: model:stock.picking.type,name:stock.picking_type_out
#, python-format
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "Ruta de entrega"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
msgid "Delivery Slip"
msgstr "Recibo de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "Tipo de entrega"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""
"Plazo de entrega, en días. Es el numero de días, prometidos al cliente, "
"desde la confirmación de la orden de venta hasta la entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_count
msgid "Delivery order count"
msgstr "Número de órdenes de entrega"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "Delivery orders of %s"
msgstr "Órdenes de entrega de %s"

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Delivery: Send by Email"
msgstr "Entrega: Enviar por correo electrónico"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "Demanda"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"Si cuenta con los módulos correspondientes instalados, esto le permitirá "
"definir la ruta del producto en este empaquetado: si se comprará, se "
"fabricará, se reabastecerá bajo pedido, etc."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""
"Si cuenta con los módulos correspondientes instalados, esto le permitirá "
"definir la ruta del producto: si se comprará, se fabricará, se reabastecerá "
"bajo pedido, etc."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__note
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "Descripción"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "Descripción para órdenes de entrega"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "Descripción para traslados internos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "Descripción para recepciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "Descripción de recolecciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Descripción en las órdenes de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "Descripción en recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "Descripción en recepciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "Descripción de recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "Dirección de destino"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Destination Location"
msgstr "Ubicación de destino"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "Ubicación de destino:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "Movimientos de destino"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "Paquete de destino"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package :"
msgstr "Paquete de destino:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "Ubicación de destino"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "Ruta destino"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Detailed Operations"
msgstr "Operaciones detalladas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "Detalles visibles"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "Diferencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "Descartar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "Descartar y resolver el conflicto de forma manual"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "Mostrar asignar serie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_complete
msgid "Display Complete"
msgstr "Mostrar completo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "Mostrar números de lote y de serie en recibos de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__display_name
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: stock
#: model:res.groups,name:stock.group_auto_reception_report
msgid "Display Reception Report at Validation"
msgstr "Mostrar reporte de recepción en la validación"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "Mostrar número de lote/serie en los recibos de entrega"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "Mostrar contenido del paquete"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "Caja desechable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "¿Confirma que desea desechar?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__qty_done
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Done"
msgstr "Hecho"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "Borrador"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "Movimientos en borrador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Conector con Easypost"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Edit Product"
msgstr "Editar producto"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr ""
"La edición de cantidades en una ubicación de ajuste de inventario está "
"prohibida, esas ubicaciones se utilizan como contraparte al corregir las "
"cantidades."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "Fecha efectiva"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "Confirmación de correo electrónico"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "Confirmación por correo electrónico de recolección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "Plantilla de correo electrónico para la confirmación de recolección"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
#: model:ir.model.fields,help:stock.field_res_config_settings__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "Correo electrónico enviado al cliente una vez que se realiza la orden"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Disfrute de una experiencia de ritmo rápido con la aplicación Código de "
"barras de Odoo. Es increíblemente rápida y funciona incluso sin una conexión"
" a internet estable. Es compatible con todos los flujos: ajustes de "
"inventario, recolección por lotes, movimientos por lotes o paletas, "
"controles de inventario bajo, etc. Vaya al menú \"Aplicaciones\" para "
"activar la interfaz de código de barras."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Asegure la trazabilidad de un producto almacenable en su almacén"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Cada operación de existencias en Odoo mueve los productos de una\n"
"            ubicación a otra. Por ejemplo, si recibe productos\n"
"            de un proveedor, Odoo moverá los productos de la ubicación del\n"
"            vendedor a la ubicación de existencias. Se puede analizar reportes\n"
"            por ubicaciones físicas, de terceros o virtuales."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "Ocurrieron excepciones en la recolección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "Excepciones:"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"Existing Serial Numbers (%s). Please correct the serial numbers encoded."
msgstr ""
"Números de serie existentes (%s). Corrija los números de serie codificados."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "Existing Serial numbers. Please correct the serial numbers encoded:"
msgstr ""
"Números de serie existentes. Corrija los números de serie codificados:"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#, python-format
msgid "Exp"
msgstr "Esp"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Exp %s"
msgstr "Esp %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "Esperado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "Entrega esperada:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Fechas de expiración"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "Nota externa..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "PEPS, UEPS..."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "Febrero"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "Conector con FedEx"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "Ubicación filtrada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "Filtros"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_number
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN"
msgstr "Primer NS"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "Fijo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "Grupo de aprovisionamiento fijo"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Fold"
msgstr "Plegar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (partners)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Forzar estrategia de remoción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
msgid "Forecast"
msgstr "Pronóstico"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "Disponibilidad del pronóstico"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Forecast Description"
msgstr "Descripción del pronóstico"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Cantidad pronosticada (calculada como cantidad a la mano - saliente + entrante)\n"
"En un contexto de una sola ubicación de existencias, esto incluye los artículos que se almacenan en esta ubicación, o cualquier ubicación secundaria.\n"
"En un contexto de un solo almacén, esto incluye los artículos que se almacenan en la ubicación de existencias de ese almacén, o cualquier almacén secundario.\n"
"En cualquier otro caso, esto incluye los artículos que se almacenan en cualquier ubicación de existencias de tipo 'Interna'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"Cantidad pronosticada (calculada como cantidad a la mano- cantidad reservada)\n"
"En un contexto con una única ubicación de existencias, esto incluye artículos que se almacenan en esta ubicación, o cualquiera de sus ubicaciones secundarias.\n"
"En un contexto con un único almacén, esto incluye artículos que se almacenan en la ubicación de existencias de este almacén, o cualquiera almacén secundario.\n"
"De lo contrario, esto incluye artículos que se almacenan en cualquier ubicación de existencias con tipo 'interno'."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted"
msgstr "Pronosticado"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Forecasted Date"
msgstr "Fecha pronosticada"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Deliveries"
msgstr "Entregas pronosticadas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "Fecha pronosticada esperada"

#. module: stock
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action
#: model:ir.actions.act_window,name:stock.report_stock_quantity_action_product
#: model:ir.ui.menu,name:stock.menu_forecast_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted Inventory"
msgstr "Inventario pronosticado"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#, python-format
msgid "Forecasted Quantity"
msgstr "Cantidad pronosticada"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Receipts"
msgstr "Recepciones pronosticadas"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_replenishment_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_product_action
#: model:ir.actions.report,name:stock.stock_replenishment_report_product_template_action
#, python-format
msgid "Forecasted Report"
msgstr "Reporte pronosticado"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Forecasted Stock"
msgstr "Existencias pronosticadas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "Peso pronosticado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Forecasted with Pending"
msgstr "Pronosticado con pendiente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "Forecasted<br/>+ Pending"
msgstr "Pronosticado<br/>+ Pendiente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "Formato"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Free Stock"
msgstr "Existencias libres"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "Cantidad de uso libre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "Desde"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "Del propietario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_reserved_availability
msgid "From Supplier"
msgstr "Desde el proveedor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "Nombre completo de la ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Deliveries"
msgstr "Entregas futuras"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future P&L"
msgstr "P&L futuras"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Productions"
msgstr "Producciones futuras"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Future Receipts"
msgstr "Recepciones futuras"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "Obtenga una trazabilidad completa de proveedores a clientes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "Obtenga advertencias informativas o de bloqueo en las empresas"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr ""
"Dele a la categoría más especializada una prioridad más alta para tenerla en"
" lo alto de la lista."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "Da la secuencia de esta línea cuando se muestran los almacenes."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "Agrupar por"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "Agrupar por..."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_picking_wave
msgid "Group your move operations in wave transfer to process them together"
msgstr ""
"Agrupe sus operaciones de movimientos en los traslados por olas para "
"procesarlos juntos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "Tiene operaciones de paquetes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "Tiene paquetes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "Tiene movimientos de desecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "Tiene rastreo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "Tiene variantes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "Tiene categoría"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "Altura"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "Altura (Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "La altura debe ser positiva"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "Oculto hasta el próximo planificador."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__hide_picking_type
msgid "Hide Picking Type"
msgstr "Ocultar tipo de recolección"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#, python-format
msgid "History"
msgstr "Historial"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr ""
"Cómo se deben reservar los productos en traslados para este tipo de "
"operación."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_location_route__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"Si un pago sigue pendiente más de sesenta (60) días después de la fecha de "
"vencimiento, Mi Empresa (Chicago) se reserva el derecho de recurrir a los "
"servicios de una empresa de cobro de deudas. Todos los gastos legales "
"correrán a cargo del cliente."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "Si todos los productos son el mismo"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr ""
"Si se encuentra seleccionado, cuando este movimiento se cancela, también "
"cancela el movimiento relacionado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "Si se establece, las operaciones se empaquetan en este paquete"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr ""
"Si el campo activo se establece a False, permite ocultar la regla de "
"existencias mínimas sin eliminarla."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr ""
"Si el campo activo se establece a False, permitirá ocultar la ruta sin "
"eliminarla."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "Si la ubicación está vacía "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"If the picking is unlocked you can edit initial demand (for a draft picking)"
" or done quantities (for a done picking)."
msgstr ""
"Si la recolección está desbloqueada, puede editar la demanda inicial (para "
"una recolección en borrador) o las cantidades realizadas (para una "
"recolección hecha)."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_reserved
msgid ""
"If this checkbox is ticked, Odoo will automatically pre-fill the detailed "
"operations with the corresponding products, locations and lot/serial "
"numbers."
msgstr ""
"Si esta casilla se encuentra seleccionada, Odoo completará automáticamente "
"las operaciones detalladas con los productos, ubicaciones y números de "
"lote/de serie correspondientes."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid "If this checkbox is ticked, label will be print in this operation."
msgstr ""
"Si esta casilla está seleccionada, la etiqueta se imprimirá en esta "
"operación."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"Si esta casilla se encuentra seleccionada, las líneas de recolección "
"representarán operaciones detalladas de existencias. De lo contrario, las "
"líneas de recolección representarán un agregado de operaciones de "
"recolección detalladas."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr ""
"Solamente si está seleccionado, supondrá que desea crear nuevos números de "
"lote/de serie para que pueda proporcionarlos en un campo de texto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"Si esto se encuentra seleccionado, podrá elegir los números de serie/de "
"lote. También puede decidir no poner lotes en este tipo de operación. Esto "
"significa que creará existencias sin lote o no pondrá una restricción sobre "
"el lote seleccionado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr ""
"Si se dividió el envío, entonces este campo enlaza con el envío que contenga"
" la parte ya procesada."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr ""
"Si se encuentra seleccionado, podrá seleccionar paquetes completos para "
"mover"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr ""
"Si no se encuentra seleccionado, permite ocultar la regla sin eliminarla."

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__immediate_transfer_id
#: model:ir.model.fields,field_description:stock.field_stock_move__from_immediate_transfer
#: model:ir.model.fields,field_description:stock.field_stock_picking__immediate_transfer
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Immediate Transfer"
msgstr "Traslado inmediato"

#. module: stock
#: model:ir.model,name:stock.model_stock_immediate_transfer_line
msgid "Immediate Transfer Line"
msgstr "Línea de traslado inmediato"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__immediate_transfer_line_ids
msgid "Immediate Transfer Lines"
msgstr "Líneas de traslado inmediato"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Immediate Transfer?"
msgstr "¿Traslado inmediato?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid "Immediate transfer?"
msgstr "¿Traslado inmediato?"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Import"
msgstr "Importar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "En tipo"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"Para que sea admisible, cualquier reclamación debe ser notificada a Mi "
"Empresa (Chicago) mediante una carta enviada con acuse de recibo a su "
"oficina registrada en un plazo de 8 días desde la entrega de los productos o"
" la prestación de los servicios."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "Entrada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "Fecha de entrada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Incoming Draft Transfer"
msgstr "Traslado entrante en borrador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "Línea de movimientos entrantes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "Envíos a recibir"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr ""
"Indica la diferencia entre la cantidad teórica del producto y su cantidad "
"contada."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "Demanda inicial"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Input"
msgstr "Entrada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "Ubicación de entrada"

#. module: stock
#: code:addons/stock/models/res_company.py:0
#, python-format
msgid "Inter-warehouse transit"
msgstr "Traslado entre almacenes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal"
msgstr "Interno"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "Ubicación interna"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "Ubicaciones internas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__ref
msgid "Internal Reference"
msgstr "Referencia interna"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "Traslado interno"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_internal
#, python-format
msgid "Internal Transfers"
msgstr "Traslados internos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "Ubicación del tránsito interno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "Tipo interno"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations amoung descendants"
msgstr "Ubicaciones internas entre descendientes"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr ""
"Número de referencia interno en caso de que difiera del número de lote/de "
"serie del fabricante"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain left operand %s"
msgstr "Dominio inválido para el operando izquierdo %s"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain operator %s"
msgstr "Dominio inválido para el operador %s"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "Dominio de operando derecho no válido '%s'. Debe ser Entero/Flotante"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr ""
"La configuración de la regla no es válida, la siguiente regla causa un bucle"
" infinito: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "Cantidad inventariada"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "Inventario"

#. module: stock
#: code:addons/stock/wizard/stock_inventory_adjustment_name.py:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Ajuste de inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Adjustment Name"
msgstr "Nombre del ajuste de inventario"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "Referencia/razón del ajuste de inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "Advertencia del ajuste de inventario"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
#, python-format
msgid "Inventory Adjustments"
msgstr "Ajustes de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "Hoja de recuento de inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "Fecha del inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
msgid "Inventory Frequency (Days)"
msgstr "Frecuencia de inventario (días)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "Ubicación de inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicaciones de inventario"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "Pérdida de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Inventory On Hand"
msgstr "Inventario a la mano"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "Resumen de inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "Conjunto de cantidades de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Inventory Reference / Reason"
msgstr "Referencia/razón de inventario"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_valuation
msgid "Inventory Report"
msgstr "Reporte de inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Rutas de inventario"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
msgid "Inventory Valuation"
msgstr "Valoración del inventario"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_report.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#, python-format
msgid "Inventory at Date"
msgstr "Inventario a la fecha"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "Es un paquete fresco"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "Bloqueado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "Firmado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__return_location
msgid "Is a Return Location?"
msgstr "¿Es una ubicación de devolución?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "¿Es una ubicación de desecho?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "¿Se puede editar la demanda inicial?"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "Atrasado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr ""
"Llega tarde o llegará tarde según la fecha límite y la fecha programada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "¿Se puede editar la cantidad hecha?"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"It is not allowed to import reserved quantity, you have to use the quantity "
"directly."
msgstr ""
"No se permite importar la cantidad reservada, tiene que utilizar la cantidad"
" directamente."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to reserve more products of %s than you have in stock."
msgstr ""
"No es posible reservar más productos que los %s que tiene en existencias."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr ""
"No es posible anular la reserva de más productos de los que %s tiene en "
"existencia."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "Especifica si los artículos se entregan parcialmente o todos a la vez"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "Datos JSON para el widget de ventana emergente"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "Enero"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "JSON de plazo de días"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Historial de reabastecimiento de Json "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "Julio"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "Junio"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "Mantener la cantidad contada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "Mantener diferencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr ""
"Mantener la <strong>cantidad contada</strong> (se actualizará la diferencia)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr ""
"Mantener la <strong>diferencia</strong> (la cantidad contada se actualizará "
"para reflejar la misma diferencia que cuando se contó)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "Últimos 12 meses"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "Últimos 3 meses"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "Últimos 30 días"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "Último partner de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Effective Inventory"
msgstr "Último inventario efectivo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group____last_update
#: model:ir.model.fields,field_description:stock.field_product_removal____last_update
#: model:ir.model.fields,field_description:stock.field_product_replenish____last_update
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer____last_update
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict____last_update
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location____last_update
#: model:ir.model.fields,field_description:stock.field_stock_location_route____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move____last_update
#: model:ir.model.fields,field_description:stock.field_stock_move_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_destination____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_level____last_update
#: model:ir.model.fields,field_description:stock.field_stock_package_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_picking_type____last_update
#: model:ir.model.fields,field_description:stock.field_stock_production_lot____last_update
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quant_package____last_update
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history____last_update
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info____last_update
#: model:ir.model.fields,field_description:stock.field_stock_request_count____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking____last_update
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rule____last_update
#: model:ir.model.fields,field_description:stock.field_stock_rules_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute____last_update
#: model:ir.model.fields,field_description:stock.field_stock_scrap____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category____last_update
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity____last_update
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation____last_update
#: model:ir.model.fields,field_description:stock.field_stock_track_line____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint____last_update
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__write_date
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scheduler_compute__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "Atrasado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "Actividades atrasadas"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "Traslados atrasados"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "Estado de disponibilidad más reciente del producto en la recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "Fecha de plazo de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "Plazo de entrega"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Lead Times"
msgstr "Plazos de entrega"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "Dejar en blanco"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr ""
"Deje este campo vacío si esta ruta se comparte entre todas las empresas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "Leyenda"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "Longitud"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "La longitud debe ser positiva"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "Etiqueta de la medida de largo"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr ""
"Deje vacío este campo si la ubicación se compartirá entre las empresas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "Movimientos vinculados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "Vista de lista de operaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "Ubicación"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "Código de barras de ubicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "Nombre de ubicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "Ubicación de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
msgid "Location Type"
msgstr "Tipo de ubicación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "Ubicación donde el sistema almacenará los productos finalizados."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "Ubicación: almacenar en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "Ubicación: cuando llega a"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "Ubicaciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Lock"
msgstr "Bloquear"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "Logística"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Lote"

#. module: stock
#: model:ir.model,name:stock.model_stock_production_lot
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "Número de lote/de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "Lot/Serial #"
msgstr "# de lote/de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial :"
msgstr "Lote/serie:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Lot/Serial Number"
msgstr "Número de lote/de serie"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "Numero de lote/de serie (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "Número de lote/de serie (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "Nombre del número de lote/de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Lot/Serial Numbers"
msgstr "Números de lote/de serie"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Números de lote y de serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the delivery slip"
msgstr "Los números de lote y de serie aparecerán en el recibo de entrega "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "Lotes visibles"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr ""
"No se proporcionaron números de lote o serie para productos rastreados"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "Números de lote/de serie"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Los números de lote/de serie le ayudan a rastrear la ruta de sus productos.\n"
"            Desde su reporte de trazabilidad verá el historial completo de su uso, así como su composición."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "Regla MTO"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_main_attachment_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Make To Order"
msgstr "Fabricación sobre pedido"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "Gestione diferentes propietarios de existencias"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "Gestionar números de lote / de serie"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "Gestionar múltiples ubicaciones de existencias"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "Gestionar múltiples almacenes"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "Gestionar paquetes"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Gestionar flujos de inventario push y pull"

#. module: stock
#: model:res.groups,name:stock.group_stock_storage_categories
msgid "Manage Storage Categories"
msgstr "Gestionar categorías de almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr ""
"Gestione paquetes de productos (por ejemplo, paquete de 6 botellas, caja de "
"10 unidades)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "Manual"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "Operación manual"

#. module: stock
#: code:addons/stock/wizard/product_replenish.py:0
#: code:addons/stock/wizard/product_replenish.py:0
#, python-format
msgid "Manual Replenishment"
msgstr "Reabastecimiento manual"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "Manualmente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "Fabricación"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "Marzo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "Marcar como por realizar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "Cantidad máxima"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
msgid "Max Weight"
msgstr "Peso máximo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight (kg)"
msgstr "Peso máximo (kg) "

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "El peso máximo debe ser positivo"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "El peso máximo debe ser un número positivo."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr ""
"Número máximo de días antes de la fecha programa en la que se reservarán los"
" productos con recolección con prioridad."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr ""
"Número de días máximo antes de la fecha programada en la que se reservarán "
"los productos."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "Peso máximo a enviar en este paquete"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "Mayo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Mensaje para recolección de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "Método"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "Cantidad mínima"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Reglas de existencias mínimas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__move_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
msgid "Move"
msgstr "Movimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "Detalle de movimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "Mover paquetes completos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "Línea de movimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_nosuggest_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_nosuggest_ids
msgid "Move Line Nosuggest"
msgstr "Linea de movimiento no sugerido"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "Líneas de movimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "Número de líneas de movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "Movimiento que creó el movimiento de devolución"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "Movimientos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"Los movimientos creados a través de este punto de orden se incluirán en este"
" grupo de compras. Si no se da ninguna, los movimientos generados por las "
"reglas de existencias se agruparán en una gran recolección."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "Rutas multietapa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "Cantidad múltiple"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "Múltiples reglas de capacidad para un tipo de paquete."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "Múltiples reglas de capacidad para un producto."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"Mi Empresa (Chicago) se compromete a hacer todo lo posible para prestar los "
"servicios en el momento oportuno y de acuerdo con los plazos acordados. Sin "
"embargo, ninguna de sus obligaciones puede considerarse como una obligación "
"de resultados. Mi Empresa (Chicago) no puede, en ningún caso, ser requerida "
"por el cliente para comparecer como un tercero en el contexto de cualquier "
"reclamo por daños presentada contra el cliente por un consumidor final."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "Mis conteos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "Mis traslados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "Nombre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "Número de movimientos de entrada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "Número de movimientos de salida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "Cantidad prevista negativa"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "Existencias negativas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "Peso neto"

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "New Move:"
msgstr "Nuevo movimiento:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "Nueva cantidad a la mano"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "Nuevo traslado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected Inventory"
msgstr "Siguiente inventario esperado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "La próxima fecha en la que se contará la cantidad a la mano."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "Siguientes traslados impactados:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "Sin orden parcial"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "Sin mensaje"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "No Tracking"
msgstr "Sin seguimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found for incoming products."
msgstr ""
"No se encontró ninguna asignación necesaria para los productos entrantes."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "No negative quantities allowed"
msgstr "No se permiten cantidades negativas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
msgid "No operation made on this lot."
msgstr "Ninguna operación realizada en este lote."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a tranfer !"
msgstr "No se encontraron operaciones. ¡Creemos un traslado!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "No se encontró ningún producto. ¡Creemos uno!"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr ""
"No hay productos por devolver (solo se pueden devolver las líneas en estado "
"hecho y no totalmente devueltas)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "No se encontró ninguna regla de almacenamiento. ¡Creemos una!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "No se encontró ninguna regla de reordenamiento"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"No rule has been found to replenish \"%s\" in \"%s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"No se encontró ninguna regla de reabastecimiento \"%s\" en \"%s\".\n"
"Verifique la configuración de rutas en el producto."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "No source location defined on stock rule: %s!"
msgstr ""
"¡No se ha definido ninguna ubicación de origen en la regla de existencias: "
"%s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "No se encontró ningún movimiento de existencias"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "No Stock On Hand"
msgstr "No hay existencias a la mano"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "No transfer found. Let's create one!"
msgstr "No se encontró ningún traslado. ¡Creemos uno!"

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid "No transfers selected or a delivery order selected"
msgstr "No se seleccionaron traslados u órdenes de entrega"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "Normal"

#. module: stock
#. openerp-web
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/xml/forecast_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#, python-format
msgid "Not Available"
msgstr "No disponible"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "No pospuesto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "Nota"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "Notas"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Nothing to check the availability for."
msgstr "No hay nada para lo que comprobar disponibilidad."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "Noviembre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__next_serial_count
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN"
msgstr "Cantidad de NS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr ""
"Número de movimientos entrantes de existencias en los últimos 12 meses"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr ""
"Número de movimientos salientes de existencias en los últimos 12 meses"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "Octubre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_replenishment_header
msgid "On Hand"
msgstr "A la mano"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "Cantidad a la mano"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr ""
"Cantidad a la mano que no se ha reservado en un traslado, en la unidad de "
"medida predeterminada del producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "A la mano:"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Only a stock manager can validate an inventory adjustment."
msgstr "Solo un gerente de existencias puede validar un ajuste de inventario."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#, python-format
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "Tipo de operación para devoluciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "Tipos de operación"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no permitida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "Tipo de operación"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "Tipo de operación (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "Tipo de operación (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "Operaciones"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "Tipos de operaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "Operaciones sin paquete"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr ""
"Dirección opcional donde se deben entregar las mercancías, se utiliza "
"específicamente para lotes."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "Detalles de ubicación opcionales, solo para fines de información."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr ""
"Opcional: todos los movimientos de devolución creados desde este movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "Opcional: siguiente movimiento de existencias, cuando se encadenan."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "Opcional: movimiento de existencias previo cuando se encadenan"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "Opciones"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Order"
msgstr "Orden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order Once"
msgstr "Ordenar una vez"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed"
msgstr "Orden firmada"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Order signed by %s"
msgstr "Orden firmada por %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "Punto de orden"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "Origen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "Movimientos de origen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "Origen del movimiento de devolución"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__original_location_id
msgid "Original Location"
msgstr "Ubicación original"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "Movimiento original"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "Regla de reordenamiento original"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "Otra información"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"Nuestras facturas se pueden pagar en un plazo de 21 días laborables, a menos"
" que se indique otro plazo de pago en la factura o en la orden. En caso de "
"falta de pago en la fecha de vencimiento, Mi Empresa (Chicago) se reserva el"
" derecho de solicitar un pago de intereses fijo que asciende al 10% de la "
"suma restante adeudada. Mi Empresa (Chicago) estará autorizada a suspender "
"cualquier prestación de servicios sin previo aviso en caso de retraso en el "
"pago."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "Tipo de salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "Saliente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Outgoing Draft Transfer"
msgstr "Traslado saliente en borrador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "Línea de movimiento saliente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "Envíos salientes"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Output"
msgstr "Salida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "Ubicación de salida"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "Información general"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "Propietario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "Propietario "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner :"
msgstr "Propietario:"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "P&L Qty"
msgstr "Cant. P&L"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_backend.xml:0
#, python-format
msgid "PRINT"
msgstr "IMPRIMIR"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Pack"
msgstr "Paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "Tipo de paquete"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pack goods, send goods in output and then deliver (3 steps)"
msgstr ""
"Empaquetar artículos, enviar artículos a ubicación de salida, y enviar (3 "
"pasos)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "Paquete"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "Código de barras del paquete (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "Código de barras del paquete (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Content"
msgstr "Código de barras del paquete con contenido"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "Capacidad de paquete"

#. module: stock
#: code:addons/stock/models/stock_package_level.py:0
#, python-format
msgid "Package Content"
msgstr "Contenido del paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "Nivel de paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "Detalles de nivel de paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "Nombre del paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "Referencia del paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "Traslados de paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "Tipo de paquete"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "Tipos de paquete"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "Uso del paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "Tipo de paquete"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "Paquetes"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"Los paquetes generalmente se crean mediante traslados (durante la operación del paquete) y pueden contener diferentes productos.\n"
"                Una vez creado, el paquete completo se puede mover a la vez, o los productos se pueden desempacar y mover como unidades individuales nuevamente."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "Empaquetado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "Altura del empaquetado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "Longitud del empaquetado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "Ancho del empaquetado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "Empaquetados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "Ubicación de empaquetado"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Packing Zone"
msgstr "Zona de empaquetado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Parameters"
msgstr "Parámetros"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__parent_location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "Ubicación padre"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "Ruta padre"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "Parcial"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "Parcialmente disponible"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "Partner"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "Dirección del partner"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__pick_ids
#, python-format
msgid "Pick"
msgstr "Recolectar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "Tipo de recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "Recolección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "Listas de recolección"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "Operaciones de recolección"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "Código de dominio de tipo de recolección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "Lista de recolección"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Pickings already processed"
msgstr "Recolecciones ya procesadas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Planned Transfer"
msgstr "Traslado planeado"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_rescheduling_popover.js:0
#, python-format
msgid "Planning Issue"
msgstr "Problema de planeación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "Problemas de planeación"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add 'Done' quantities to the picking to create a new pack."
msgstr ""
"Agregue cantidades 'Hecho' a la selección para crear un nuevo paquete."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Please add some items to move."
msgstr "Por favor, agregue algunos artículos por mover."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Please specify at least one non-zero quantity."
msgstr "Por favor, especifique al menos una cantidad diferente de cero."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_reserved
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_reserved
msgid "Pre-fill Detailed Operations"
msgstr "Precompletar operaciones detalladas"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "Preceding operations"
msgstr "Operaciones precedentes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
msgid "Preferred Route"
msgstr "Ruta preferida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_ids
msgid "Preferred Routes"
msgstr "Rutas preferidas"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "Ruta preferida"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr ""
"Haga clic en el botón de CREAR para definir una cantidad para cada producto "
"en sus existencias o impórtelos de una hoja de cálculo a través de Favoritos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "Imprimir"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Print Label"
msgstr "Imprimir etiqueta"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Print Labels"
msgstr "Imprimir etiquetas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "Impreso"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "Prioridad"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "Procesar en esta fecha para llegar a tiempo"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "Procese operaciones más rápido con códigos de barras"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations in wave transfers"
msgstr "Procesar operaciones en traslados por olas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process transfers in batch per worker"
msgstr "Procesar transferencias en lote por trabajador"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "Grupo de aprovisionamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "Grupo de aprovisionamiento"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:stock.ir_cron_scheduler_action
#: model:ir.cron,name:stock.ir_cron_scheduler_action
msgid "Procurement: run scheduler"
msgstr "Aprovisionamiento: ejecutar planificador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "Línea de producción"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Produced Qty"
msgstr "Cant. producida"

#. module: stock
#: model:ir.model,name:stock.model_product_product
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_assign_serial__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "Producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "Disponibilidad del producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "Capacidad de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "Categorías de productos"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "Etiqueta del producto (ZPL)"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "Reporte de etiquetas del producto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "Filtro de lotes de producto"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Product Moves"
msgstr "Movimientos de producto"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (Línea de movimientos de existencias)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "Empaquetado del producto"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "Empaquetado del producto (ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "Empaquetados del producto"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Confirmed"
msgstr "Cantidad de producto confirmada"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Product Quantity Updated"
msgstr "Cantidad de producto actualizada"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#: model:ir.model,name:stock.model_product_replenish
#, python-format
msgid "Product Replenish"
msgstr "Reabastecimiento de producto"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "Reporte de rutas del producto"

#. module: stock
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "Plantilla del producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "Rastreo de productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__detailed_type
#: model:ir.model.fields,field_description:stock.field_product_template__detailed_type
#: model:ir.model.fields,field_description:stock.field_stock_move__product_type
msgid "Product Type"
msgstr "Tipo de producto"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "Variantes de producto"

#. module: stock
#: code:addons/stock/report/product_label_report.py:0
#, python-format
msgid "Product model not defined, Please contact your administrator."
msgstr "El modelo del producto no está definido, contacte a su administrador."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr ""
"Producto que contiene este número de lote/de serie. Ya no puede cambiarlo si"
" ya se movió."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "Etiqueta de unidad de medida del producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "Producto con rastreo"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "Producción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "Ubicación de producción"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "Ubicaciones de producción"

#. module: stock
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_location_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#, python-format
msgid "Products"
msgstr "Productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "Estado de disponibilidad de productos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr ""
"Los productos se reservarán primero para los traslados con las prioridades "
"más altas."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Products: %(location)s"
msgstr "Productos: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "Propagar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "Propagar cancelación y división"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "Propagación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "Propagación del grupo de aprovisionamiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "Propagación del transportista"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "Pull y push"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "Obtener desde"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "Regla pull"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "Regla push"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "Llevar a"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Put in Pack"
msgstr "Incluir en el paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr "Empaquete productos (por ejemplo, partidas, cajas) y rastréelos"

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "Regla de almacenamiento"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#, python-format
msgid "Putaway Rules"
msgstr "Reglas de almacenamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "Almacenamiento:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "Reglas de almacenamiento"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "El múltiplo de la cantidad debe ser mayor o igual a cero."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "Calidad"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Quality Control"
msgstr "Control de calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "Ubicación del control de calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "Hoja de trabajo de calidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr ""
"Los quants se eliminan de forma automática cuando se considera adecuado "
"hacerlo. Si debe eliminarlos de manera manual, solicite a un gerente de "
"existencias que lo haga."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's creation is restricted, you can't do this operation."
msgstr "La creación de Quant está restringida, no puede hacer esta operación."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quant's editing is restricted, you can't do this operation."
msgstr ""
"La edición de Quant está restringida, no puede realizar esta operación."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities Already Set"
msgstr "Cantidades ya establecidas"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quantities To Reset"
msgstr "Cantidades para restablecer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Quantity"
msgstr "Cantidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity :"
msgstr "Cantidad:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity_done
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Quantity Done"
msgstr "Cantidad hecha"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "Múltiplo de la cantidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "Cantidad a la mano"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reserved_availability
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "Cantidad reservada"

#. module: stock
#: code:addons/stock/wizard/stock_change_product_qty.py:0
#, python-format
msgid "Quantity cannot be negative."
msgstr "La cantidad no puede ser negativa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "La cantidad se movió desde el último recuento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr ""
"Cantidad en existencias que aún se puede reservar para este movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "Cantidad en la UdM predeterminada del producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"Cantidad de productos entrantes planeados.\n"
"En un contexto con una única ubicación de existencias, esto incluye los artículos que llegan a esta ubicación o cualquier ubicación secundaria.\n"
"En un contexto con un solo almacén, esto incluye los artículos que llegan a la ubicación de existencias de este almacén, o cualquier ubicación secundaria.\n"
"De lo contrario, esto incluye artículos que llegan a cualquier ubicación de existencias con tipo 'interno'."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"Cantidad de productos salientes planeados.\n"
"En un contexto con una única ubicación de existencias, esto incluye artículos que salen de esta ubicación o cualquier ubicación secundaria.\n"
"En un contexto con un solo almacén, esto incluye los artículos que salen de la ubicación de existencias de este almacén, o cualquier ubicación secundaria.\n"
"De lo contrario, esto incluye artículos que salgan de cualquier ubicación de existencias con tipo 'interno'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr ""
"Cantidad de productos en este quant, en la unidad de medida predeterminada "
"del producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr ""
"Cantidad de productos reservados en este quant, en la unidad de medida "
"predeterminada del producto"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "La cantidad debe ser un número positivo."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reserved_availability
msgid "Quantity that has already been reserved for this move"
msgstr "Cantidad que ya se reservó para este movimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__picking_quantity
msgid "Quantity to print"
msgstr "Cantidad para imprimir"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "Quants"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Quants cannot be created for consumables or services."
msgstr "No se pueden crear quants para consumibles o servicios."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "Listo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "Cantidad real"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_qty
msgid "Real Reserved Quantity"
msgstr "Cantidad reservada real"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Receipt"
msgstr "Recepción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "Ruta de recepción"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.chi_picking_type_in
#: model:stock.picking.type,name:stock.picking_type_in
#, python-format
msgid "Receipts"
msgstr "Recepciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "Recibir de"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive goods directly (1 step)"
msgstr "Recibir artículos directamente (1 paso)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive goods in input and then stock (2 steps)"
msgstr ""
"Recibir artículos en la ubicación de entrada y luego llevar a existencias (2"
" pasos)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive goods in input, then quality and then stock (3 steps)"
msgstr ""
"Recibir artículos en la ubicación de entrada, trasladar a control de "
"calidad, y luego llevar a existencias (3 pasos)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 1 step (stock)"
msgstr "Recibir en 1 paso (existencias)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 2 steps (input + stock)"
msgstr "Recibir en 2 pasos (entrada + existencias)"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "Recibir en 3 pasos (entrada + control de calidad + existencias)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "Received Qty"
msgstr "Cant. recibida"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
msgid "Reception Report"
msgstr "Reporte de recepción"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "Etiqueta de reporte de recepción"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "Referencia"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "Secuencia de la referencia"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "¡La referencia debe ser única por empresa!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "Referencia del documento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "Referencia:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Register lots, packs, location"
msgstr "Registrar lotes, paquetes, ubicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "Movimientos de existencias relacionados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "Partes restantes de recolección parcialmente procesada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "Remoción"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "Estrategia de remoción"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Removal strategy %s not implemented."
msgstr "Estrategia de remoción %s no implementada."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Cantidad máxima de reordenamiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Cantidad mínima de reordenamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "Regla de reordenamiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model:ir.ui.menu,name:stock.menu_reordering_rules_config
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "Reordering Rules"
msgstr "Reglas de reordenamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "Búsqueda de reglas de reordenamiento"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#: model:ir.actions.act_window,name:stock.action_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Replenish"
msgstr "Reabastecer"

#. module: stock
#: model:stock.location.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "Reabastecer sobre pedido (MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "Asistente de reabastecimiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Replenishment"
msgstr "Reabastecimiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
msgid "Replenishment Information"
msgstr "Información de reabastecimiento"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Information for %s in %s"
msgstr "Información de reabastecimiento para %s en %s"

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid "Replenishment Report"
msgstr "Reporte de reabastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "Búsqueda de reportes de reabastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "Report Quantity"
msgstr "Reporte de cantidad"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
msgid "Reporting"
msgstr "Reportes"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "Solicitar un recuento"

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "Requerir una firma en sus órdenes de entrega"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "Método de reservación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "Reservaciones"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserve"
msgstr "Reserva"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "Reservar solo empaquetados completos"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"Reservar solo empaquetados completos: no se reservarán empaquetados parciales. Si el cliente ordena 2 tarimas de 1000 unidades cada una y solo tiene 1600 en existencias, entonces solo se reservarán 1000\n"
"Reservar empaquetados parciales: permite reservar empaquetados parciales. Si el cliente pide 2 tarimas de 1000 unidades cada una y solo tiene 1600, entonces se reservarán 1600"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "Reservar empaquetados"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "Reservar empaquetado parcial"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reserve before scheduled date"
msgstr "Reservar antes de la fecha programada"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_qty
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Reserved"
msgstr "Reservado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "Cantidad reservada"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Reserved from stock"
msgstr "Reservado de existencias"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "Reserving a negative quantity is not allowed."
msgstr "No se permite reservar una cantidad negativa."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "Responsable"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "Reabastecer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "Reabastecer de"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "Rutas de reabastecimiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "Devolver"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__location_id
msgid "Return Location"
msgstr "Ubicación de devolución"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "Recolección de devolución"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Línea de recolección de devolución"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__return_type_id
msgid "Return Type"
msgstr "Tipo de devolución"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Return of %s"
msgstr "Devolución de %s"

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "Returned Picking"
msgstr "Recolección devuelta"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Returns"
msgstr "Devoluciones"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "Caja reutilizable"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"Se utilizan las cajas reutilizables para la preparación de lotes y después se vacían para volver a utilizarse. Al escanear una caja reutilizable mediante la aplicación Código de barras, se agregan los productos a la caja.\n"
"En cambio, las cajas desechables no se vuelven a utilizar y cuando se escanea una caja desechable a través de la aplicación Código de barras, los productos incluidos se agregan al traslado. "

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
msgid "Reverse Transfer"
msgstr "Revertir traslado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "Ruta"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "Ruta de la empresa"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "Secuencia de la ruta"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "Rutas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "Se pueden seleccionar rutas en este producto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr ""
"Las rutas se crearán automáticamente para reabastecer este almacén desde los"
" almacenes marcados"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr ""
"Las rutas se crearán para este almacén de reabastecimiento y podrá "
"seleccionarlas en los productos y las categorías de producto"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "Mensaje de regla"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "Reglas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "Reglas en categorías"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "Reglas en productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "Reglas utilizadas"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_procurement_compute
#: model:ir.ui.menu,name:stock.menu_procurement_compute
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid "Run Scheduler"
msgstr "Ejecutar planificador"

#. module: stock
#: model:ir.model,name:stock.model_stock_scheduler_compute
msgid "Run Scheduler Manually"
msgstr "Ejecutar planificador manualmente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Run the scheduler"
msgstr "Ejecutar el planificador"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "Confirmación por SMS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega de SMS"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "TÉRMINOS Y CONDICIONES DE VENTA ESTÁNDAR"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Sales History"
msgstr "Historial de ventas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr ""
"Fecha programada hasta que el movimiento esté hecho, después fecha real en "
"que el movimiento ha sido procesado."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "Fecha programada o de procesamiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr ""
"Fecha programada para la primera parte del envío que se procesará. "
"Establecer manualmente un valor aquí significará la fecha esperada para "
"todos los movimientos de existencias."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#, python-format
msgid "Scrap"
msgstr "Desechar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "Ubicación de desecho"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_id
msgid "Scrap Move"
msgstr "Desechar movimiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "Ordenes de desecho"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "Desechar productos"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "Desechado"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"Desechar un producto lo removerá de sus existencias. El producto\n"
"                terminará en una ubicación de desecho que se puede utilizar para fines de reportes."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "Desechos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "Buscar aprovisionamiento"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "Buscar desecho"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_category_id
msgid "Select category for the current product"
msgstr "Seleccione la categoría para el producto actual."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "Seleccione los lugares en los que se puede seleccionar esta ruta"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Si selecciona la opción \"advertencia\" se notificará a los usuarios con el "
"mensaje, si selecciona \"mensaje de bloqueo\" se lanzará una excepción con "
"el mensaje y se bloqueará el flujo. El mensaje debe escribirse en el "
"siguiente campo."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Venda y compre productos en diferentes unidades de medida"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr ""
"Enviar un mensaje de texto SMS de confirmación automática cuando se realicen"
" las órdenes de entrega"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr ""
"Envíe un correo electrónico de confirmación automática cuando se realicen "
"las órdenes de entrega"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "Enviar correo electrónico"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Send goods in output and then deliver (2 steps)"
msgstr "Enviar artículos a ubicación de salida y entregar (2 pasos)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "Septiembre"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_location_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#, python-format
msgid "Sequence"
msgstr "Secuencia"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence in"
msgstr "Secuencia de entrada"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence internal"
msgstr "Secuencia interna"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence out"
msgstr "Secuencia de salida"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence packing"
msgstr "Secuencia de empaquetado"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence picking"
msgstr "Secuencia de recolección"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Sequence return"
msgstr "Secuencia de devolución"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "Números de serie"

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"Serial number (%s) already exists in location(s): %s. Please correct the "
"serial number encoded."
msgstr ""
"El número de serie (%s) ya existe en las ubicaciones: %s. Corrija el número "
"de serie codificado."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"El número de serie (%s) no se encuentra en %s, pero se encuentra en las ubicaciones: %s.\n"
"\n"
"Corrija esto para prevenir datos inconsistentes."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"Serial number (%s) is not located in %s, but is located in location(s): %s.\n"
"\n"
"Source location for this move will be changed to %s"
msgstr ""
"El número de serie (%s) no se encuentra en %s, pero se encuentra en las ubicaciones: %s.\n"
"\n"
"Se cambiará la ubicación de origen para este movimiento a %s"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "Establecer"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "Establecer valor actual"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "Establecer rutas de almacén"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"Establezca una estrategia de remoción específica que se utilizará sin importar la ubicación de origen para esta categoría de producto.\n"
"\n"
"PEPS: los productos o lotes que se almacenaron primero se moverán primero.\n"
"UEPS: los productos o lotes que se almacenaron al último se moverán primero.\n"
"Ubicación más cercana: los productos o lotes más cercanos a la ubicación de destino se moverán primero.\n"
"FEFO: los productos o lotes con la fecha de remoción más cercana se moverán primero (la disponibilidad de este método depende del ajuste de \"Fechas de caducidad\")."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots &amp; serial numbers"
msgstr "Establecer fechas de caducidad en números de lote y serie"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "Establecer propietario en productos almacenados"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr ""
"Establecer los atributos del producto (por ejemplo, color, tamaño) para "
"administrar las variantes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Set quantities"
msgstr "Establecer cantidades"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid ""
"Sets a location if you produce at a fixed location. This can be a partner "
"location if you subcontract the manufacturing operations."
msgstr ""
"Establece una ubicación si se producen en una ubicación fija. Puede ser una "
"ubicación de partner si subcontrata las operaciones de fabricación."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "Ajustes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "Estantería (Y)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "Envíos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "Envío"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "Conectores de envío"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
msgid "Shipping Policy"
msgstr "Política de entrega"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"Los conectores de envíos permiten calcular costos de envío precisos, "
"imprimir etiquetas de envío y solicitar la recolección del transportista en "
"su almacén para enviar al cliente. Aplique el conector de envío desde los "
"métodos de entrega."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "Nombre corto"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "Nombre corto que se utiliza para identificar su almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "Mostrar asignación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "Mostrar verificar disponibilidad"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "Mostrar operaciones detalladas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "Mostrar botón de estado de cantidad "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "Mostrar Lotes M2O"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "Mostrar texto de lotes"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_mark_as_todo
msgid "Show Mark As Todo"
msgstr "Mostrar Marcar por realizar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "Mostrar botón de estado de cantidad a la mano"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
msgid "Show Operations"
msgstr "Mostrar operaciones"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_auto_reception_report
msgid "Show Reception Report at Validation"
msgstr "Mostrar reporte de recepción en la validación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer__show_transfers
msgid "Show Transfers"
msgstr "Mostrar traslados"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_validate
msgid "Show Validate"
msgstr "Mostrar validar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "Mostrar las rutas que aplican en los almacenes seleccionados."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__show_info
msgid "Show warning"
msgstr "Mostrar advertencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "Firmar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "Firma"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "Firmado"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "Tamaño"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "Tamaño: Longitud × Ancho × Altura"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#, python-format
msgid "Snooze"
msgstr "Posponer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "Fecha pospuesta"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "Posponer punto de orden"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "Posponer para"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "Pospuesto"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Some products of the inventory adjustment are tracked. Are you sure you "
"don't want to specify a serial or lot number for them?"
msgstr ""
"Algunos productos del ajuste de inventario tienen rastreo por número de "
"lote/de serie. ¿Está seguro de que no desea especificar un número de lote/de"
" serie para ellos?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr ""
"Algunas líneas seleccionadas ya tienen cantidades establecidas, se "
"ignorarán."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid ""
"Some selected lines don't have any quantities set, they will be ignored."
msgstr ""
"Algunas líneas seleccionadas no tienen cantidades establecidas, se "
"ignorarán."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "Origen"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "Documento de origen"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#, python-format
msgid "Source Location"
msgstr "Ubicación de origen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "Ubicación de origen:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "Paquete de origen"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package :"
msgstr "Paquete de origen:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "Destacado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_search
msgid "State"
msgstr "Estado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "Estado"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya pasó\n"
"Hoy: la fecha límite es hoy\n"
"Planeada: futuras actividades."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Stock"
msgstr "Existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_assign_serial
msgid "Stock Assign Serial Numbers"
msgstr "Asignar números de serie a existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "Ubicación de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "Ubicaciones de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_lines
#: model:ir.ui.menu,name:stock.stock_move_menu
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "Movimientos de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "Análisis de los movimientos de existencias"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#, python-format
msgid "Stock On Hand"
msgstr "Existencias a la mano"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "Operación de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "Destino del paquete de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "Nivel de paquete de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "Recolección de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "Quant de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historial de cantidad de existencias "

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "Reporte de cantidad de existencias"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "Reporte de recepción de existencias"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_product_product_replenishment
#: model:ir.model,name:stock.model_report_stock_report_product_template_replenishment
msgid "Stock Replenishment Report"
msgstr "Reporte de reabastecimiento de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Solicitar un recuento de inventario"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "Regla de existencias"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "Reporte de reglas de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Reporte de reglas de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "Confirmación de seguimiento de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "Línea de seguimiento de existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock moves not in package"
msgstr "Movimientos de existencias sin paquete"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr ""
"Movimientos de existencias que están disponibles (listos para ser "
"procesados)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr ""
"Movimientos de existencias que están confirmados, disponibles o en espera."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "Movimientos de existencias que han sido procesados"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "Tipo de paquete de existencias"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Reporte de regla de existencias"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "Información de reabastecimiento del proveedor de existencias"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__detailed_type__product
#: model:ir.model.fields.selection,name:stock.selection__product_template__type__product
msgid "Storable Product"
msgstr "Producto almacenable"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"Storable products are physical items for which you manage the inventory "
"level."
msgstr ""
"Los productos almacenables son artículos físicos para los que se gestiona el"
" nivel de inventario."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Storage Capacities"
msgstr "Capacidades de almacenamiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_storage_categories
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "Categorías de almacenamiento"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "Categoría de almacenamiento"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model:ir.ui.menu,name:stock.menu_storage_categoty_capacity_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "Capacidad de la categoría de almacenamiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "Ubicaciones de almacenamiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr ""
"Almacene productos en ubicaciones específicas de su almacén (por ejemplo, "
"contenedores, bastidores) y lleve un seguimiento del inventario en "
"consecuencia."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "Almacenar en sububicación"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "Almacén suministrado"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "Método de suministro"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "Almacén de suministros"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "Tomar de las existencias"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "Tomar de las existencias, si no hay disponibles, activar otra regla"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"Tomar de las existencias: los productos se tomarán de las existencias disponibles de la ubicación de origen.\n"
"Activar otra regla: el sistema intentará encontrar una regla de existencias para llevar los productos a la ubicación de origen. Se ignorarán las existencias disponibles.\n"
"Tomar de las existencias, si no está disponible, active otra regla: los productos se tomarán de las existencias disponible de la ubicación de origen. Si no hay existencias disponibles, el sistema intentará encontrar una regla para llevar los productos a la ubicación de origen."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr ""
"Campo técnico que se utiliza para decidir si se debe mostrar el botón "
"\"Asignación\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "Información técnica"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"Technical field depicting the warehouse to consider for the route selection "
"on the next procurement (if any)."
msgstr ""
"Campo técnico que indica el almacén a considerar para la selección de ruta "
"en el siguiente aprovisionamiento (si lo hay)."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__internal_transit_location_id
msgid ""
"Technical field used for resupply routes between warehouses that belong to "
"this company"
msgstr ""
"Campo técnico que se utiliza para reabastecer rutas entre almacenes que "
"pertenecen a esta empresa"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr ""
"Campo técnico que se utiliza para calcular si se debe mostrar el botón "
"\"Verificar disponibilidad\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_mark_as_todo
msgid ""
"Technical field used to compute whether the button \"Mark as Todo\" should "
"be displayed."
msgstr ""
"Campo técnico que se utiliza para calcular si se debe mostrar el botón "
"\"Marcar por realizar\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_validate
msgid ""
"Technical field used to decide whether the button \"Validate\" should be "
"displayed."
msgstr ""
"Campo técnico que se utiliza para decidir si se debe mostrar el botón "
"\"Validar\"."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__restrict_partner_id
msgid ""
"Technical field used to depict a restriction on the ownership of quants to "
"consider when marking this move as 'done'"
msgstr ""
"Campo técnico que muestra la restricción en el propietario de los quants a "
"considerar cuando se marque este movimiento como hecho."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__price_unit
msgid ""
"Technical field used to record the product cost set by the user during a "
"picking confirmation (when costing method used is 'average price' or "
"'real'). Value given in company currency and in product uom."
msgstr ""
"Campo técnico que se utiliza para registrar el costo de un producto "
"establecido por el usuario durante la confirmación de recolección (cuando el"
" método de costo que se utiliza es 'precio promedio' o 'real'). El valor se "
"expresa en la divisa de la empresa y en la UdM del producto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__produce_line_ids
msgid "Technical link to see which line was produced with this. "
msgstr "Enlace técnico para ver qué línea se produjo con esto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__consume_line_ids
msgid "Technical link to see who consumed what. "
msgstr "Enlace técnico para ver quién consumió qué."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_tmpl_id
msgid "Technical: used in views"
msgstr "Técnico: se utiliza en vistas"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__stock_move_ids
#: model:ir.model.fields,help:stock.field_product_product__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "Técnico: se utiliza para calcular las cantidades."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__incoming_move_line_ids
#: model:ir.model.fields,help:stock.field_stock_location__outgoing_move_line_ids
msgid "Technical: used to compute weight."
msgstr "Técnico: se utiliza para calcular el peso."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "Plantilla"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"El valor 'Operación manual' creará un movimiento de existencias después del "
"actual. Con 'Automático sin paso agregado', la ubicación se reemplaza en el "
"movimiento original."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The Serial Number (%s) is already used in these location(s): %s.\n"
"\n"
"Is this expected? For example this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"El número de serie (%s) ya está en uso en estas ubicaciones: %s.\n"
"\n"
"¿Es esto lo que espera? Por ejemplo, esto puede ocurrir si se valida una operación antes de que se valide su operación de recepción correspondiente. En este caso, el problema se resolverá automáticamente una vez que se completen todos los pasos. De lo contrario, debe corregir el número de serie para prevenir datos inconsistentes."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"The backorder <a href=# data-oe-model=stock.picking data-oe-id=%d>%s</a> has"
" been created."
msgstr ""
"Se creó la orden parcial <a href=# data-oe-model=stock.picking data-oe-"
"id=%d>%s</a>."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company !"
msgstr "¡El código de barras de una ubicación debe ser único por empresa!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"El cliente renuncia explícitamente a sus propios términos y condiciones "
"estándar, incluso si éstas se redactaron después de los presentes términos y"
" condiciones estándar de venta. Para que sea válida, se debe acordar "
"cualquier derogación expresamente por escrito y con antelación."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "The combination of product and location must be unique."
msgstr "La combinación de producto y ubicación debe ser única."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"The combination of serial number and product must be unique across a company.\n"
"Following combination contains duplicates:\n"
msgstr ""
"La combinación de número de serie y producto debe ser única en toda la empresa.\n"
"La siguiente combinación contiene duplicados:\n"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr ""
"La empresa se establece automáticamente de acuerdo a sus preferencias de "
"usuario."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"The deadline has been automatically updated due to a delay on <a href='#' "
"data-oe-model='%s' data-oe-id='%s'>%s</a>."
msgstr ""
"Se actualizó la fecha límite automáticamente debido a un retraso el <a "
"href='#' data-oe-model='%s' data-oe-id='%s'>%s</a> ."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr ""
"La fecha esperada del traslado creado se calculará en función de este plazo "
"de entrega."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "El primero en la secuencia es el aplicado de forma predeterminada."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "The forecasted stock on the"
msgstr "Las existencias pronosticadas en el"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr ""
"La frecuencia de inventario (días) para una ubicación no debe ser negativa"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid "The lot name must contain at least one digit."
msgstr "El número de lote debe contener al menos un dígito."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "¡El nombre del almacén debe ser único por empresa!"

#. module: stock
#: code:addons/stock/wizard/stock_assign_serial_numbers.py:0
#, python-format
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "La cantidad de números de serie por generar debe ser mayor a cero."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"                operation a specific type which will alter its views accordingly.\n"
"                On the operation type you could e.g. specify if packing is needed by default,\n"
"                if it should show the customer."
msgstr ""
"El sistema de tipos de operación le permite asignar a cada operación de\n"
"                existencias un tipo específico que alterará sus vistas en consecuencia.\n"
"                En el tipo de operación puede, por ejemplo, especificar si el empaquetado es necesario de forma predeterminada,\n"
"                si debe mostrar el cliente."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "El paquete que contiene este quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr ""
"La ubicación principal que incluye esta ubicación. Ejemplo: La 'Zona de "
"despacho' es la ubicación principal 'Puerta 1'."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to this multiple.  If it is 0, "
"the exact quantity will be used."
msgstr ""
"La cantidad de aprovisionamiento se redondeará a este múltiplo. Si es 0, se "
"utilizará la cantidad exacta."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "El producto no está disponible en cantidad suficiente"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "La cantidad contada del producto."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The quantity done for the product \"%s\" doesn't respect the rounding "
"precision defined on the unit of measure \"%s\". Please change the quantity "
"done or the rounding precision of your unit of measure."
msgstr ""
"La cantidad hecha para el producto \"%s\" no respeta la precisión de "
"redondeo que se definió en la unidad de medida  \"%s\". Cambie la cantidad "
"hecha o la precisión de redondeo de su unidad de medida."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"No se puede procesar la operación solicitada debido a un error de "
"programación que establece el campo 'product_qty' en lugar del campo "
"'product_uom_qty'."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr ""
"La frecuencia de inventario (días) seleccionada crea una fecha demasiado "
"lejana en el futuro."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"The serial number has already been assigned: \n"
" Product: %s, Serial Number: %s"
msgstr ""
"Ya se asignó el número de serie:\n"
" Producto: %s, número de serie: %s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "¡El nombre corto del almacén debe ser único para cada empresa!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"La ubicación de existencias que se utiliza como destino al enviar artículos "
"a este contacto."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"La ubicación de existencias que se utiliza como origen al recibir productos "
"de este contacto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "La operación de existencias en la que se creó el paquete"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "La regla de existencias que creó este movimiento de existencias"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_procurement_compute_wizard
msgid ""
"The stock will be reserved for operations waiting for availability and the "
"reordering rules will be triggered."
msgstr ""
"Se reservarán las existencias para operaciones que están esperando "
"disponibilidad y se activarán las reglas de reabastecimiento."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"El almacén a propagar en el movimiento/aprovisionamiento creado, que puede "
"ser diferente del almacén para el que es esta regla (por ejemplo, para "
"reglas de reabastecimiento desde otro almacén)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "No hay movimiento de producto todavía"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr ""
"Este análisis le da un resumen del nivel de existencias actual de sus "
"productos."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr ""
"Este campo rellenará el paquete de origen y el nombre de sus movimientos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid ""
"This is a technical field for calculating when a move should be reserved"
msgstr ""
"Este es un campo técnico para calcular cuándo se debe reservar un movimiento"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when you create a picking manually "
"with this operation type. It is possible however to change it or that the "
"routes put another location. If it is empty, it will check for the customer "
"location on the partner. "
msgstr ""
"Esta es la ubicación de destino predeterminada cuando crea una recolección "
"de manera manual con este tipo de operación. Sin embargo, es posible "
"cambiarla o que las rutas establezcan otra ubicación. Si está vacía, "
"verificará la ubicación del cliente en el contacto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when you create a picking manually with "
"this operation type. It is possible however to change it or that the routes "
"put another location. If it is empty, it will check for the supplier "
"location on the partner. "
msgstr ""
"Esta es la ubicación de origen predeterminada cuando crea una recolección de"
" manera manual con este tipo de operación. Sin embargo, es posible cambiarla"
" o que las rutas establezcan otra ubicación. Si está vacía, verificará la "
"ubicación del proveedor en el contacto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "Este es el propietario del quant"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of products from an inventory point of view. For moves "
"in the state 'done', this is the quantity of products that were actually "
"moved. For other moves, this is the quantity of product that is planned to "
"be moved. Lowering this quantity does not generate a backorder. Changing "
"this quantity on assigned moves affects the product reservation, and should "
"be done with care."
msgstr ""
"Esta es la cantidad de productos desde un punto de vista de inventario. Para"
" movimientos en el estado 'hecho', esta es la cantidad de productos que se "
"movieron realmente. Para otros movimientos, esta es la cantidad de producto "
"que planea mover. Disminuir esta cantidad no genera una orden parcial. "
"Cambiar esta cantidad en movimientos asignados, afecta la reserva de "
"producto, y se debe realizar con cuidado."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr ""
"Esta ubicación (si es interna) y todos sus descendientes se filtran con "
"type=internal."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr ""
"El uso de esta ubicación no puede cambiarse a \"vista\" puesto que contiene "
"productos."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr ""
"Este lote %(lot_name)s no es compatible con este producto %(product_name)s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"Este menú le ofrece la trazabilidad completa de las operaciones de inventario \n"
"                de un producto específico. Puede filtrar el producto\n"
"                para ver todos los movimientos pasados o futuros del mismo."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"Este menú le brinda la trazabilidad completa de las operaciones de inventario en un producto específico.\n"
"                    Puede filtrar el producto para ver todos los movimientos pasados del producto."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "Esta nota se agrega a las órdenes de entrega."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr ""
"Esta nota se agrega a las órdenes de traslados internos (por ejemplo, dónde "
"recolectar el producto en el almacén)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr ""
"Esta nota se agrega a las órdenes de recepción (por ejemplo, dónde almacenar"
" el producto en el almacén)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid ""
"This picking appears to be chained with another operation. Later, if you "
"receive the goods you are returning now, make sure to <b>reverse</b> the "
"returned picking in order to avoid logistic rules to be applied again (which"
" would create duplicated operations)"
msgstr ""
"Esta recolección aparece ligada a otra operación. Más adelante, si recibe "
"los artículos que está devolviendo ahora, asegúrese de <b>anular</b> la "
"recolección devuelta para evitar que se apliquen de nuevo las reglas de "
"logística (lo que crearía operaciones duplicadas)"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"Este producto se utilizó en al menos un movimiento de inventario. No se "
"recomienda cambiar el tipo de producto, ya que puede provocar "
"inconsistencias. Una mejor solución podría ser archivar el producto y crear "
"uno nuevo."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr ""
"La empresa de este producto no se puede cambiar mientras tenga cantidades "
"que pertenecen a otra empresa."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr ""
"No puede cambiar la empresa de este producto si hay movimientos de "
"existencias que pertenecen a otra empresa."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr ""
"Esta cantidad está expresada en la unidad de medida predeterminada del "
"producto."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid "This record already exists."
msgstr "Este registro ya existe."

#. module: stock
#: code:addons/stock/report/report_stock_reception.py:0
#, python-format
msgid ""
"This report cannot be used for done and not done transfers at the same time"
msgstr ""
"No se puede utilizar este reporte para traslados realizados y no realizados "
"al mismo tiempo."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""
"Se usará esta ubicación de existencias en lugar de la predeterminada, como "
"la ubicación origen para los movimientos de existencias generados por las "
"órdenes de fabricación."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""
"Se usará esta ubicación de existencias, en lugar de la predeterminada, como "
"la ubicación origen para los movimientos de existencias generados cuando se "
"realizan inventarios."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""
"Este usuario será responsable de las próximas actividades relacionadas con "
"las operaciones logísticas de este producto."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr "Esto descartará todos los recuentos no aplicados, ¿desea continuar?"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "Consejo: acelere las operaciones de inventario con códigos de barras"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "Por aplicar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "Para hacer una orden parcial"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "Para contar"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "To Do"
msgstr "Por hacer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "Por ordenar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__to_immediate
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "Por procesar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "Por reordenar"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "Todas las rutas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "Trazabilidad"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/stock_traceability_report_widgets.js:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.report_stock_inventory
#, python-format
msgid "Traceability Report"
msgstr "Reporte de trazabilidad"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"Haga un seguimiento de las fechas de seguimiento de los números de lote y serie: consúmase antes de, remoción, fin de vida, alerta.\n"
" Dichas fechas se establecen automáticamente en la creación del número de lote/de serie en función de los valores establecidos en el producto (en días)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"Haga un seguimiento de las fechas de seguimiento de los números de lotes y "
"de serie: consúmase antes de, remoción, fin de vida, alerta. Dichas fechas "
"se establecen automáticamente en la creación del número de lote y de serie "
"en función de los valores establecidos en el producto (en días)."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "Lleve el seguimiento de la ubicación del producto en su almacén"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "Rastree sus existencias al crear productos almacenables."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Tracked Products in Inventory Adjustment"
msgstr "Productos rastreados en ajuste de inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "Seguimiento"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "Línea de seguimiento"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_immediate_transfer_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "Traslado"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__picking_quantity__picking
msgid "Transfer Quantities"
msgstr "Trasladar cantidades"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "Trasladar a"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__delivery_ids
#: model:ir.ui.menu,name:stock.all_picking
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "Traslados"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Transfers %s: Please add some items to move."
msgstr "Traslados %s: agregue algunos elementos para mover."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_form
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_all
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_backorder
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_late
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_ready
#: model_terms:ir.actions.act_window,help:stock.action_picking_tree_waiting
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.actions.act_window,help:stock.stock_picking_action_picking_type
msgid "Transfers allow you to move products from one location to another."
msgstr "Los traslados le permiten mover productos de una ubicación a otra."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "Traslados por grupos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr ""
"Los traslados que llegan tarde a la hora programada o una de las "
"recolecciones llegarán tarde"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "Ubicación de tránsito"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "Ubicaciones de tránsito"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
msgid "Trigger"
msgstr "Activar"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "Activar otra regla"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "Activar otra regla si no hay existencias"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_forecasted.js:0
#, python-format
msgid "Try to add some incoming or outgoing transfers."
msgstr "Intente agregar algunos traslados entrantes o salientes."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
#: model:ir.model.fields,field_description:stock.field_product_product__type
#: model:ir.model.fields,field_description:stock.field_product_template__type
msgid "Type"
msgstr "Tipo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "Tipo de operación"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_production_lot__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "Conector con UPS"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "Conector con USPS"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/report_stock_reception.js:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
#, python-format
msgid "Unassign"
msgstr "Desasignar"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#: code:addons/stock/static/src/xml/stock_traceability_report_line.xml:0
#, python-format
msgid "Unfold"
msgstr "Desplegar"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_production_lot__name
msgid "Unique Lot/Serial Number"
msgstr "Número de lote/de serie único"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "Precio unitario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: stock
#: model:product.product,uom_name:stock.product_cable_management_box
#: model:product.template,uom_name:stock.product_cable_management_box_product_template
msgid "Units"
msgstr "Unidades"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "Unidades de medida"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "Unidades de medida"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unit of measure"
msgstr "Unidad de medida"

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Unknown Pack"
msgstr "Paquete desconocido"

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "Unknown stream."
msgstr "Secuencia desconocida."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Unlock"
msgstr "Desbloquear"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "Desempaquetar"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__message_unread_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Número de mensajes sin leer"

#. module: stock
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "Anular reserva"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable_config
msgid "UoM"
msgstr "UdM"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "Categorías de UdM"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "Actualizar la cantidad de productos"

#. module: stock
#: code:addons/stock/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#, python-format
msgid "Update Quantity"
msgstr "Actualizar la cantidad "

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "Urgente"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "Utilizar números de lotes/de serie existentes"

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "Utilizar reporte de recepción"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid ""
"Use this assistant to replenish your stock.\n"
"                Depending on your product configuration, launching a replenishment may trigger a request for quotation,\n"
"                a manufacturing order or a transfer."
msgstr ""
"Utilice este asistente para reabastecer sus existencias.\n"
"                Dependiendo de la configuración de su producto, iniciar un reabastecimiento puede activar una solicitud de cotización,\n"
"                una orden de fabricación o un traslado."

#. module: stock
#: model:res.groups,name:stock.group_stock_picking_wave
msgid "Use wave pickings"
msgstr "Utilizar recolección por olas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "Use sus propias rutas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_product_product_replenishment
msgid "Used by"
msgstr "Usado por"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "Se utiliza para ordenar la vista de kanban de 'Todas la operaciones'"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "Usuario"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "Usuario asignado para realizar un recuento de productos."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "Validar"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
#, python-format
msgid "Validate Inventory"
msgstr "Validar inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "Número de variantes"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "Proveedor"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "Ubicación de proveedor"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "Ubicaciones de proveedor"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "Ver"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "Ver diagrama"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "Ver ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "Ver y asignar cantidades recibidas."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "En espera"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "En espera de otro movimiento"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "En espera de otra operación"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "En espera de disponibilidad"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "En espera de movimientos"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "En espera de traslados"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "Almacén"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "Configuración del almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "Dominio de almacén"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "Gestión de almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "Vista de almacén"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "Almacén por propagar"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "Ubicación de vista del almacén"

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "Warehouse's Routes"
msgstr "Rutas del almacén"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/report_stock_forecasted.xml:0
#, python-format
msgid "Warehouse:"
msgstr "Almacén:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_location_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Warehouses"
msgstr "Almacenes"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "Advertir de cantidad insuficiente"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "Advertir de cantidad de desecho insuficiente"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
#, python-format
msgid "Warning"
msgstr "Advertencia"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "Advertencia en la recolección"

#. module: stock
#: code:addons/stock/models/product.py:0 code:addons/stock/models/product.py:0
#, python-format
msgid "Warning!"
msgstr "¡Advertencia!"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "Advertencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Advertencias para existencias"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_picking_wave
msgid "Wave Transfers"
msgstr "Traslados con recolección por olas"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_production_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Etiqueta de la unidad de medida de peso"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Producto con peso medido"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr ""
"Cuando se selecciona un almacén para esta ruta, esta ruta debe considerarse "
"como la ruta predeterminada cuando los productos pasan a través de este "
"almacén."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
msgid "When all products are ready"
msgstr "Cuando todos los productos estén listos"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr ""
"Cuando se selecciona, la ruta se podrá seleccionar en la pestaña de "
"inventario del formulario de producto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr ""
"Cuando se selecciona, la ruta se podrá seleccionar en las categorías de "
"producto."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr ""
"Cuando se selecciona, la ruta será seleccionable en el empaquetado de "
"producto. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "Cuando llega el producto"

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products are needed in <b>%s</b>, <br/> <b>%s</b> are created from "
"<b>%s</b> to fulfill the need."
msgstr ""
"Cuando se requieren productos en <b>%s</b>, <br/> <b>%s</b> se crea desde "
"<b>%s</b> para cumplir la necesidad."

#. module: stock
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid ""
"When products arrive in <b>%s</b>, <br/> <b>%s</b> are created to send them "
"in <b>%s</b>."
msgstr ""
"Cuando llegan productos en <b>%s</b>, <br/> <b>%s</b> se crea para enviarlos"
" a <b>%s</b>."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr ""
"Cuando la recolección no está hecha, esto permite cambiar la demanda "
"inicial. Cuando se realiza la operación, esto permite cambiar las cantidades"
" hechas."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity to the Max "
"Quantity."
msgstr ""
"Cuando las existencias virtuales estén por debajo de la cantidad mínima "
"especificada para este campo, Odoo genera un aprovisionamiento para llevar "
"la cantidad pronosticada a la cantidad máxima."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity to the Quantity specified as "
"Max Quantity."
msgstr ""
"Cuando las existencias virtuales estén por debajo de la cantidad mínima, "
"Odoo generará un aprovisionamiento para llevar la cantidad pronosticada a la"
" cantidad especificada como cantidad máxima."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propgated."
msgstr "Cuando se selecciona, se propagará el transportista del envío."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr ""
"Cuando se encuentra seleccionado, si el movimiento creado por esta regla se "
"cancela, el siguiente movimiento también se cancelará."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr ""
"Al validar el traslado, los productos se asignarán a este propietario."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "Al validar el traslado, los productos se tomarán de este propietario."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr ""
"Si el movimiento se agregó después de la confirmación de la recolección"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "Ancho"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "El ancho debe ser positivo"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "Asistente"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Write your SN/LN one by one or copy paste a list."
msgstr ""
"Escriba su número de serie/ de lote uno por uno o copie y pegue una lista."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "¡Usted es bueno, no hay reabastecimiento que realizar!"

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"No se permite cambiar el producto relacionado a un número de lote/de serie "
"si ya se generaron movimientos de existencias con ese número. Eso llevaría a"
" inconsistencias en su inventario."

#. module: stock
#: code:addons/stock/models/stock_production_lot.py:0
#, python-format
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"No está permitido crear un número de lote/de serie en este tipo de "
"operación. Para cambiar este comportamiento, vaya al tipo de operación y "
"active la opción \"Crear nuevos números de lote/de serie\"."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr ""
"Está intentando poner productos de diferentes ubicaciones en el mismo "
"paquete "

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"Está utilizando una unidad de medida más pequeña que la que utiliza para "
"almacenar su producto. Esto puede provocar un problema de redondeo en la "
"cantidad reservada. Debe usar la unidad de medida más pequeña posible para "
"valorar sus existencias o cambiar su precisión de redondeo a un valor más "
"pequeño (ejemplo: 0.00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"Puede definir aquí las principales rutas que se ejecutan en\n"
"                    sus almacenes y que definen los flujos de sus productos. Estas\n"
"                    rutas se pueden asignar a un producto, a una categoría de producto o\n"
"                   establecerse para las órdenes de compra o de venta."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "Puede:"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You can not change the type of a product that is currently reserved on a "
"stock move. If you need to change the type, you should first unreserve the "
"stock move."
msgstr ""
"No puede cambiar el tipo de producto que está actualmente reservado en un "
"movimiento de existencias. Si necesita cambiar el tipo, primero debe "
"cancelar el movimiento de existencias."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid "You can not change the type of a product that was already used."
msgstr "No puede cambiar el tipo de un producto que ya se utilizó."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr ""
"No puede eliminar movimientos del producto si ya realizó la recolección. "
"Solo puede corregir las cantidades hechas."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can not enter negative quantities."
msgstr "No puede ingresar cantidades negativas."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You can only delete draft moves."
msgstr "Solo puede eliminar movimientos en estado de borrador."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr ""
"Solo puede procesar 1.0 %s para productos con un número de serie único."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You can't desactivate the multi-location if you have more than once "
"warehouse by company"
msgstr ""
"No puede desactivar las ubicaciones múltiples si tiene más de un almacén por"
" empresa"

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You cannot archive the location %s as it is used by your warehouse %s"
msgstr "No puede archivar la ubicación %s ya que el almacén %s la utiliza"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr ""
"No puede cancelar un movimiento de existencias que se haya configurado como "
"‘Hecho’. Cree una devolución para revertir los movimientos que se "
"realizaron."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr ""
"No puede cambiar la fecha programada en un traslado realizado o cancelado."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr ""
"No puede cambiar la UdM de un movimiento de existencias establecido como "
"\"Hecho\"."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid ""
"You cannot change the location type or its use as a scrap location as there "
"are products reserved in this location. Please unreserve the products first."
msgstr ""
"No puede cambiar el tipo de ubicación o su uso como ubicación de desecho ya "
"que aun hay productos reservados en esta ubicación. Por favor,  anule la "
"reserva primero."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr ""
"No puede cambiar la proporción de esta unidad de medida, ya que algunos "
"productos con esta unidad de medida ya se han movido o están reservados "
"actualmente."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"No puede cambiar la unidad de medida de un producto que ya se utilizó en un "
"movimientos de existencias. Si necesita cambiar la unidad de medida, puede "
"archivar este producto y crear uno nuevo."

#. module: stock
#: code:addons/stock/models/stock_scrap.py:0
#, python-format
msgid "You cannot delete a scrap which is done."
msgstr "No se puede eliminar un desecho que está hecho."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "You cannot modify inventory loss quantity"
msgstr "No puede modificar la cantidad de pérdida de inventario"

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr ""
"No puede mover el mismo contenido del paquete más de una vez en el mismo "
"traslado o dividir el mismo paquete en dos ubicaciones."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid ""
"You cannot perform the move because the unit of measure has a different "
"category as the product unit of measure."
msgstr ""
"No puede realizar el movimiento porque la unidad de medida tiene una "
"categoría diferente a la unidad de medida del producto."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr ""
"No puede dividir un movimiento en borrador. Necesita confirmarlo primero."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr ""
"No puede dividir un movimiento de existencias que se haya establecido como "
"'Realizado' o 'Cancelado'."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr ""
"No puede tomar productos ni entregar productos en una ubicación de tipo "
"\"vista\" (%s)."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr "No puede cancelar un movimiento de existencias que está como 'hecho'."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr ""
"No puede cambiar un movimiento de existencias cancelado, cree una nueva "
"línea en su lugar."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr ""
"No puede usar el mismo número de serie dos veces. Por favor, corrija los "
"números de serie codificados."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. To "
"force the transfer, switch in edit mode and encode the done quantities."
msgstr ""
"No se puede validar una transferencia si no se reservan ni se hacen "
"cantidades. Para forzar el traslado, cambie al modo de edición y codifique "
"las cantidades hechas."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid ""
"You have manually created product lines, please delete them to proceed."
msgstr "Creó líneas de productos manualmente, elimínelas para continuar."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_immediate_transfer
msgid ""
"You have not recorded <i>done</i> quantities yet, by clicking on "
"<i>apply</i> Odoo will process all the quantities."
msgstr ""
"Aún no ha registrado las cantidades <i>hechas</i> , al hacer clic en "
"<i>aplicar,</i> Odoo procesará todas las cantidades."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "Ha procesado menos productos que la demanda inicial."

#. module: stock
#: code:addons/stock/models/res_config_settings.py:0
#, python-format
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"Algunos productos en sus existencias tienen el número de lote o serie de seguimiento habilitado. \n"
"Deshabilite el seguimiento de todos los productos antes de desactivar este ajuste."

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Tiene productos en existencia que no tienen número de serie/de lote. Puede "
"asignar números de lote/de serie al hacer un ajuste de inventario."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You have to define a groupby and sorted method and pass them as arguments."
msgstr "Debe definir un método groupby y sorted y pasarlos como argumentos."

#. module: stock
#: code:addons/stock/models/stock_orderpoint.py:0
#, python-format
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr ""
"Debe seleccionar una unidad de medida de producto que esté en la misma "
"categoría que la unidad de medida predeterminada del producto"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/js/inventory_singleton_list_controller.js:0
#, python-format
msgid ""
"You have tried to create a record which already exists. The existing record "
"has been modified instead."
msgstr ""
"Intentó crear un registro que ya existe. El registro existente se ha "
"modificado en su lugar."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return Done pickings."
msgstr "Solo puede devolver las recolecciones hechas."

#. module: stock
#: code:addons/stock/wizard/stock_picking_return.py:0
#, python-format
msgid "You may only return one picking at a time."
msgstr "Solo puede devolver una recolección a la vez."

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr ""
"Debe activar ubicaciones de almacenamiento para poder realizar tipos de "
"operaciones internas."

#. module: stock
#: code:addons/stock/models/stock_move.py:0
#, python-format
msgid "You need to set a Serial Number before generating more."
msgstr "Debe establecer un número de serie antes de generar más."

#. module: stock
#: code:addons/stock/models/stock_move_line.py:0
#, python-format
msgid ""
"You need to supply a Lot/Serial Number for product: \n"
" - "
msgstr ""
"Debe proporcionar un número de lote/de serie para el producto:\n"
" - "

#. module: stock
#: code:addons/stock/models/stock_picking.py:0
#, python-format
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "Debe proporcionar un número de lote/de serie para los productos %s."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr ""
"Debe actualizar este documento para que refleje sus términos y condiciones."

#. module: stock
#: code:addons/stock/models/stock_warehouse.py:0
#, python-format
msgid "You still have ongoing operations for picking types %s in warehouse %s"
msgstr ""
"Todavía tiene operaciones en curso para los tipos de recolección %s en el "
"almacén %s"

#. module: stock
#: code:addons/stock/models/product.py:0
#, python-format
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr ""
"Aún tiene alguna regla de reordenamiento activa en este producto. Archívelas"
" o elimínelas primero."

#. module: stock
#: code:addons/stock/models/stock_location.py:0
#, python-format
msgid "You still have some product in locations %s"
msgstr "Aún tiene productos en las ubicaciones %s"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"Aquí encontrará propuestas de reabastecimiento inteligentes con base en pronósticos de inventario.\n"
"            Elija la cantidad a comprar o fabricar y lance órdenes con un clic.\n"
"            Para ahorrar tiempo en el futuro, establezca las reglas como \"automatizadas\"."

#. module: stock
#: code:addons/stock/models/stock_quant.py:0
#, python-format
msgid "Your stock is currently empty"
msgstr "Sus existencias están actualmente vacías"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
msgid "ZPL Labels"
msgstr "Etiquetas ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
msgid "ZPL Labels with price"
msgstr "Etiquetas con precio ZPL"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>min:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "_Cancel"
msgstr "_Cancelar"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "below the inventory"
msgstr "por debajo del inventario"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "Conector bpost"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "días"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "días antes de que se destacó"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "días antes/"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "Por ejemplo, Almacén principal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "Por ejemplo, Almacén principal"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "Por ejemplo, LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "Por ejemplo, PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "Por ejemplo, PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "Por ejemplo, Ubicaciones físicas"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "Por ejemplo, Existencias de reserva"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "Por ejemplo, Recepción en dos pasos"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "desde la ubicación"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "en"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "is"
msgstr "es"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "manually to trigger the reordering rules right now."
msgstr ""
"manualmente para activar las reglas de reordenamiento en este momento."

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "minimum of"
msgstr "valor mínimo de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "de"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/popover_widget.xml:0
#, python-format
msgid "planned on"
msgstr "planeado en"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "procesado en lugar de"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "should be replenished"
msgstr "debe reabastecerse"

#. module: stock
#. openerp-web
#: code:addons/stock/static/src/xml/stock_orderpoint.xml:0
#, python-format
msgid "to reach the maximum of"
msgstr "para alcanzar un valor máximo de"

#. module: stock
#: model:mail.template,report_name:stock.mail_template_data_delivery_confirmation
msgid "{{ (object.name or '').replace('/','_') }}"
msgstr "{{ (object.name or '').replace('/','_') }}"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} Orden de entrega (Ref {{ object.name or 'n/a' "
"}})"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#, python-format
msgid "Global Visibility Days"
msgstr "Días de visibilidad global"
