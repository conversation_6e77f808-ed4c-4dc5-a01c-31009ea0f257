# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e-20200204\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-16 17:01+0000\n"
"PO-Revision-Date: 2020-03-16 17:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fi
#: model:ir.model.constraint,message:l10n_fi.constraint_res_partner_operator_einvoice_operator_identifier_uniq
msgid "\"Identifier\" should be unique!"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__active
msgid "Active"
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_search
msgid "Archived"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__res_partner_operator_einvoice__ttype__bank
msgid "Bank with Finvoice brokerage service"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner__business_code
#: model:ir.model.fields,field_description:l10n_fi.field_res_users__business_code
#: model_terms:ir.ui.view,arch_db:l10n_fi.view_partner_form_l10n_fi
msgid "Business ID"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__res_partner_operator_einvoice__ttype__broker
msgid "Carrier broker"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__create_date
msgid "Created on"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fi
#: model:ir.ui.menu,name:l10n_fi.account_einvoicing_menu
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_config_settings_view_form
msgid "E-Invoicing"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_company__edicode
#: model:ir.model.fields,field_description:l10n_fi.field_res_config_settings__edicode
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner__edicode
#: model:ir.model.fields,field_description:l10n_fi.field_res_users__edicode
msgid "Edicode"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_res_config_settings__edicode
msgid "Edicode for eInvoice documents"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__account_journal__invoice_reference_model__finnish_rf
msgid "Finnish Creditor Reference (RF)"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields.selection,name:l10n_fi.selection__account_journal__invoice_reference_model__finnish
msgid "Finnish Standard Reference"
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_search
msgid "Group by..."
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__id
msgid "ID"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__identifier
msgid "Identifier"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_res_company__einvoice_operator_id
#: model:ir.model.fields,help:l10n_fi.field_res_config_settings__einvoice_operator_id
#: model:ir.model.fields,help:l10n_fi.field_res_partner__einvoice_operator_id
#: model:ir.model.fields,help:l10n_fi.field_res_users__einvoice_operator_id
msgid "Intermediator for eInvoice documents"
msgstr ""

#. module: l10n_fi
#: code:addons/l10n_fi/models/account_move.py:0
#, python-format
msgid "Invoice number must contain numeric characters"
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_account_move
msgid "Journal Entries"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_res_partner_operator_einvoice__identifier
msgid "Monetary Institution Identifier (see https://tieke.fi)"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__name
msgid "Operator"
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_form
msgid "Operator Name"
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_search
msgid "Operator Type"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_res_company__edicode
msgid "Our Company's Edicode for eInvoice documents"
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_config_settings_view_form
msgid "Our company's Edicode for eInvoice documents."
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_config_settings_view_form
msgid "Our company's provider for eInvoice documents."
msgstr ""

#. module: l10n_fi
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_search
msgid "Search for eInvoice Operator"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__sequence
msgid "Sequence"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_res_partner__business_code
#: model:ir.model.fields,help:l10n_fi.field_res_users__business_code
msgid "The unique business registry identifier"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner_operator_einvoice__ttype
msgid "Type"
msgstr ""

#. module: l10n_fi
#: model:ir.model.fields,help:l10n_fi.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""

#. module: l10n_fi
#: model:ir.model,name:l10n_fi.model_res_partner_operator_einvoice
#: model:ir.model.fields,field_description:l10n_fi.field_res_company__einvoice_operator_id
#: model:ir.model.fields,field_description:l10n_fi.field_res_config_settings__einvoice_operator_id
#: model:ir.model.fields,field_description:l10n_fi.field_res_partner__einvoice_operator_id
#: model:ir.model.fields,field_description:l10n_fi.field_res_users__einvoice_operator_id
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_form
msgid "eInvoice Operator"
msgstr ""

#. module: l10n_fi
#: model:ir.actions.act_window,name:l10n_fi.res_partner_operator_einvoice_action
#: model:ir.ui.menu,name:l10n_fi.res_partner_operator_einvoice_menu
#: model_terms:ir.ui.view,arch_db:l10n_fi.res_partner_operator_einvoice_view_tree
msgid "eInvoice Operators"
msgstr ""
