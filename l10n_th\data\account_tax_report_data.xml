<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="tax_report" model="account.tax.report">
        <field name="name">Tax Report</field>
        <field name="country_id" ref="base.th"/>
    </record>

    <record id="tax_report_out_tax_title" model="account.tax.report.line">
        <field name="name">Output Tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
    </record>

    <record id="tax_report_out_tax_sale" model="account.tax.report.line">
        <field name="name">1. Sales amount</field>
        <field name="tag_name">1. Sales amount</field>
        <field name="code">OUTPUTTAX_SALEAMOUNT</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_out_tax_title"/>
    </record>

    <record id="tax_report_out_tax_less_sales_0_rate" model="account.tax.report.line">
        <field name="name">2. Less sales subject to 0% tax rate </field>
        <field name="tag_name">2. Less sales subject to 0% tax rate </field>
        <field name="code">OUTPUTTAX_SALE_ZERO</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_out_tax_title"/>
    </record>

    <record id="tax_report_out_tax_less_exempted_sales" model="account.tax.report.line">
        <field name="name">3. Less exempted sales</field>
        <field name="tag_name">3. Less exempted sales</field>
        <field name="code">OUTPUTTAX_EXEMPTED</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_out_tax_title"/>
    </record>

    <record id="tax_report_out_tax_taxable_sales_amount" model="account.tax.report.line">
        <field name="name">4. Taxable sales amount(1. -2. -3.)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
        <field name="parent_id" ref="tax_report_out_tax_title"/>
        <field name="formula">OUTPUTTAX_SALEAMOUNT-(OUTPUTTAX_SALE_ZERO+OUTPUTTAX_EXEMPTED)</field>
    </record>

    <record id="tax_report_out_tax" model="account.tax.report.line">
        <field name="name">5. Output tax</field>
        <field name="tag_name">5. Output tax</field>
        <field name="code">OUTPUTTAX_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="5"/>
        <field name="parent_id" ref="tax_report_out_tax_title"/>
    </record>

    <record id="tax_report_input_tax_title" model="account.tax.report.line">
        <field name="name">Input Tax</field>
        <field name="code">INPUTTAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
    </record>

    <record id="tax_report_input_tax_purchase_from_out_tax" model="account.tax.report.line">
        <field name="name">6. Purchase amount that is entitled to deduction of input tax from output tax in tax computation</field>
        <field name="tag_name">6. Purchase amount that is entitled to deduction of input tax from output tax in tax computation</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_input_tax_title"/>
    </record>

    <record id="tax_report_input_tax" model="account.tax.report.line">
        <field name="name">7. Input tax (according to invoice of purchase amount in 6.)</field>
        <field name="tag_name">7. Input tax (according to invoice of purchase amount in 6.)</field>
        <field name="code">INPUTTAX_TAX</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_input_tax_title"/>
    </record>

    <record id="tax_report_vat" model="account.tax.report.line">
        <field name="name">Value Added Tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
    </record>

    <record id="tax_report_vat_payable" model="account.tax.report.line">
        <field name="name">8. Tax payable (5. minus 7. (if 5. is greater than 7.))</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_vat"/>
        <field name="formula">OUTPUTTAX_TAX - INPUTTAX_TAX if (OUTPUTTAX_TAX > INPUTTAX_TAX) else 0</field>
    </record>

    <record id="tax_report_vat_excess" model="account.tax.report.line">
        <field name="name">9. Excess tax payable (7. minus 5. (if 5. is less than 7.))</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_vat"/>
        <field name="formula">INPUTTAX_TAX - OUTPUTTAX_TAX if (INPUTTAX_TAX > OUTPUTTAX_TAX) else 0</field>
    </record>

    <record id="tax_report_vat_payment_last_period" model="account.tax.report.line">
        <field name="name">10. Excess tax payment carried forward from last period</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="3"/>
        <field name="parent_id" ref="tax_report_vat"/>
    </record>

    <record id="tax_report_net_vat" model="account.tax.report.line">
        <field name="name">Net Tax</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="4"/>
    </record>

    <record id="tax_report_net_vat_payable" model="account.tax.report.line">
        <field name="name">11. Net tax payable (if 8. is greater than 10.)</field>
        <field name="tag_name">11. Net tax payable (if 8. is greater than 10.)</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="1"/>
        <field name="parent_id" ref="tax_report_net_vat"/>
    </record>

    <record id="tax_report_net_vat_excess" model="account.tax.report.line">
        <field name="name">12. Net excess tax payable ((if 10. is greater than 8.) or (9. plus 10.))</field>
        <field name="tag_name">12. Net excess tax payable ((if 10. is greater than 8.) or (9. plus 10.))</field>
        <field name="report_id" ref="tax_report"/>
        <field name="sequence" eval="2"/>
        <field name="parent_id" ref="tax_report_net_vat"/>
    </record>

</odoo>
