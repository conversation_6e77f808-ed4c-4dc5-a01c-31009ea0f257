<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="ir_rule_crm_reveal_rule_all" model="ir.rule">
        <field name="name">CRM Reveal Rules: All Rules</field>
        <field name="model_id" ref="model_crm_reveal_rule"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
    </record>
    <record id="ir_rule_crm_reveal_rule_salesman" model="ir.rule">
        <field name="name">CRM Reveal Rules: Personal / Global Rules</field>
        <field name="model_id" ref="model_crm_reveal_rule"/>
        <field name="domain_force">['|', ('user_id', '=', user.id), ('user_id', '=', False)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>

    <record id="ir_rule_crm_reveal_view_all" model="ir.rule">
        <field name="name">CRM Reveal Views: All Views</field>
        <field name="model_id" ref="model_crm_reveal_view"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
    </record>
    <record id="ir_rule_crm_reveal_view_salesman" model="ir.rule">
        <field name="name">CRM Reveal Views: Personal / Global Views</field>
        <field name="model_id" ref="model_crm_reveal_view"/>
        <field name="domain_force">['|', ('reveal_rule_id.user_id', '=', user.id), ('reveal_rule_id.user_id', '=', False)]</field>
        <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman'))]"/>
    </record>
</odoo>
