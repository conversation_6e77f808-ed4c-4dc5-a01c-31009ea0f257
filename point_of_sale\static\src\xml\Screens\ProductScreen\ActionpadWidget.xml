<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="ActionpadWidget" owl="1">
        <div class="actionpad">
            <button class="button set-customer" t-att-class="{'decentered': isLongName}"
                    t-on-click="trigger('click-customer')">
                <t t-if="!env.isMobile"><i class="fa fa-user" role="img" aria-label="Customer" title="Customer" /></t>
                <t t-if="client">
                    <t t-esc="client.name" />
                </t>
                <t t-else="">
                    Customer
                </t>
            </button>
            <button class="button pay" t-on-click="trigger(props.actionToTrigger or 'click-pay')">
                <div class="pay-circle" t-att-class="{ 'highlight': props.isActionButtonHighlighted }">
                    <i class="fa fa-chevron-right" role="img" aria-label="Pay" title="Pay" />
                </div>
                <t t-esc="props.actionName" />
            </button>
            <button t-if="env.isMobile" class="button back-button" t-on-click="trigger('switchpane')">
                <i class="fa fa-angle-double-left"/>
                Back
            </button>
        </div>
    </t>

</templates>
