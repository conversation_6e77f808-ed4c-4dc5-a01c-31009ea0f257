# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_live
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_content
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"pl-2\">Loading Video...</span>"
msgstr ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"pl-2\">Video lädt ...</span>"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_aside
msgid "Chat"
msgstr "Chat"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__is_youtube_replay
msgid ""
"Check this option if the video is already available on Youtube to avoid "
"showing 'Direct' options (Chat, ...)"
msgstr ""
"Aktivieren Sie diese Option, wenn das Video bereits auf YouTube verfügbar "
"ist, um zu vermeiden, dass „direkte“ Optionen (Chat ...) angezeigt werden."

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_url
msgid ""
"Configure this URL so that event attendees can see your Track in video!"
msgstr ""
"Konfigurieren Sie diese URL so, dass die Teilnehmer der Veranstaltung Ihren "
"Beitrag als Video sehen können!"

#. module: website_event_track_live
#: model:ir.model,name:website_event_track_live.model_event_track
msgid "Event Track"
msgstr "Veranstaltungsbeitrag"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_id
msgid ""
"Extracted from the video URL and used to infer various links "
"(embed/thumbnail/...)"
msgstr ""
"Wird aus der Video-URL extrahiert und verwendet, um verschiedene Links "
"abzuleiten (Einbettung/Vorschaubild/...)"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_chat_available
msgid "Is Chat Available"
msgstr "Ist Chat verfügbar"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_replay
msgid "Is Youtube Replay"
msgstr "Ist Youtube-Wiedergabe"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.tracks_display_list
msgid "Replay"
msgstr "Abspielen"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Replay Video"
msgstr "Video abspielen"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Starts in"
msgstr "Beginnt in"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Up Next:"
msgstr "Als nächstes:"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "You just watched:"
msgstr "Sie haben eben angesehen:"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_url
msgid "Youtube Video URL"
msgstr "Youtube-Video-URL"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_id
msgid "Youtube video ID"
msgstr "YouTube-Video-ID"
