# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta, date
from odoo.exceptions import ValidationError
from dateutil.relativedelta import relativedelta

class HrMAsaratCrons(models.Model):
    _name = "hr.masarat.crons"



    def action_send_notification_finger_forget(self,recorde_id, ddate):
        email_from = self.env['hr.employee'].search([('id', '=', recorde_id)])
        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك نسيان بصمة بتاريخ : </font></p>
        <p><font style="font-size: 14px;"><strong> """+str(ddate)+""" </strong></font></p>
        <p><font style="font-size: 14px;">.يرجى مراجعة الموارد البشرية</font></p>
        </div>"""
        template_id = self.env['mail.mail'].create({
            'subject':'اشعار نسيان بصمة',
            'email_from':email_from.work_email,
            'email_to': email_from.work_email,
            'body_html':body
        })
        #### freaa
        template_id.sudo().send()

    def action_send_notification_latency(self,recorde_id, ddate):
        email_from = self.env['hr.employee'].search([('id', '=', recorde_id)])
        body = """
        <div dir="rtl">
        <p><font style="font-size: 14px;"> لديك تأخير بتاريخ : </font></p>
        <p><font style="font-size: 14px;"><strong> """+str(ddate)+""" </strong></font></p>
        <p><font style="font-size: 14px;">.يرجى مراجعة الموارد البشرية</font></p>
        </div>"""
        template_id = self.env['mail.mail'].create({
            'subject':'اشعار تأخير',
            'email_from':email_from.work_email,
            'email_to': email_from.work_email,
            'body_html':body
        })
        #### freaa
        template_id.sudo().send()


    def get_all_employee_finger_print_forget(self):
        forget_finger_list = []
        latency_list = []
        yesterday = datetime.now()- timedelta(days=1)
        calendars = self.env['resource.calendar'].search([('there_is_letancy','=',True)])
        all_contract = self.env['hr.contract'].search([('resource_calendar_id','in',calendars.ids)])
        for elem in all_contract:
            employee = elem.employee_id
            attendance = self.env['hr.attendance'].search([('employee_id','=',employee.id),('attendance_date','=',str(yesterday.date()))],limit=1)
            attendance.get_computed_latency()
            if attendance.computed_latency_note == ' نسيان بصمة ':
                forget_finger_list.append(elem.employee_id.id)

            if attendance.computed_latency and not attendance.computed_latency_note:
                latency_list.append(elem.employee_id.id)

        for elem in latency_list:
            self.sudo().action_send_notification_latency(elem, str(yesterday.date()))
        for elem in forget_finger_list:
            self.sudo().action_send_notification_finger_forget(elem, str(yesterday.date()))



