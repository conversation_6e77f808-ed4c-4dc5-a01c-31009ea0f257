# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_restaurant
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "Utilizați o imprimantă conectată la caseta IoT "

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "!props.isBill"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-cutlery\" title=\"For bars and restaurants\" "
"role=\"img\" aria-label=\"For bars and restaurants\"/>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr "<strong>Nume Etaj: </strong>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr "<strong>Punct de vânzare: </strong>"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""
"Un etaj de restaurant reprezintă locul în care sunt serviți clienții, acesta este locul unde puteți \n"
"                defini și poziționa mesele."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__active
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Activ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Add"
msgstr "Adaugă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.js:0
#, python-format
msgid "Add Internal Note"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr "Adăugați un nou etaj de restaurant"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr "Adăugați o nouă imprimantă pentru comenzi la restaurant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Add a tip"
msgstr "Adăugați un bacșiș"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Add button"
msgstr "Adăugare Buton"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add internal notes on order lines"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add tip after payment (North America specific)"
msgstr "Adăugați bacșiș după plată (specific America de Nord)"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Adjust Amount"
msgstr "Ajustați suma"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__set_tip_after_payment
msgid ""
"Adjust the amount authorized by payment terminals to add a tip after the "
"customers left or at the end of the day."
msgstr ""
"Ajustați suma autorizată de terminalele de plată pentru a adăuga un bacșiș "
"după plecarea clienților sau la sfârșitul zilei."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom internal notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Allow to print bill before payment"
msgstr "Permite să imprimați factura înainte de efectuarea plății"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr "Permite să imprimați factura înainte de efectuarea plății"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Amount"
msgstr "Valoare"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr "O identificare internă a unei mese"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr "O identificare internă a imprimantei"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__name
msgid "An internal identification of the restaurant floor"
msgstr "O identificare internă a etajului de restaurant"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr "Aspect"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_search
msgid "Archived"
msgstr "Arhivat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Are you sure ?"
msgstr "Ești sigur ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Back"
msgstr "Înapoi"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#: code:addons/pos_restaurant/static/src/xml/ChromeWidgets/BackToFloorButton.xml:0
#, python-format
msgid "Back to floor"
msgstr "Înapoi la etaj"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr "Culoare fundal"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr "Imagine fundal"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bacon
#: model:product.template,name:pos_restaurant.pos_food_bacon_product_template
msgid "Bacon Burger"
msgstr "Burger Bacon"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/PrintBillButton.xml:0
#, python-format
msgid "Bill"
msgstr "Factură achiziție"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr "Tipărire Nota de plată"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr "Divizare Nota de plată"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Blocked action"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Blue"
msgstr "Albastru"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "CANCELLED"
msgstr "ANULAT"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_cheeseburger
#: model:product.template,name:pos_restaurant.pos_food_cheeseburger_product_template
msgid "Cheese Burger"
msgstr "Cheese Burger"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chicken
#: model:product.template,name:pos_restaurant.pos_food_chicken_product_template
msgid "Chicken Curry Sandwich"
msgstr "Sandwich de pui cu curry"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Close"
msgstr "Închide"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Close Tab"
msgstr "Închideți fila"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_club
#: model:product.template,name:pos_restaurant.pos_food_club_product_template
msgid "Club Sandwich"
msgstr "Sandwich de Club"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.coke
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr "Coca-Cola"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Color"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Connection Error"
msgstr "Eroare conexiune"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Creat în"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Delete"
msgstr "Șterge"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Design floors and assign orders to tables"
msgstr "Proiectați etajele și atribuiți comenzi tabelelor"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr "Băuturi"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/ChromeWidgets/TicketButton.js:0
#, python-format
msgid "Due to a connection error, the orders are not synchronized."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Duplicate"
msgstr "Duplicare"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "Edit"
msgstr "Editare"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr "Activare divizare factura în punctul de vânzare."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr "Etaj"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr "Nume Etaj"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr "Planuri Etaj"

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid "Floor: %s - PoS Config: %s \n"
msgstr "Etaj: %s - Configurare Punct de Vânzare: %s \n"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Floors"
msgstr "Etaje"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Floors & Tables"
msgstr "Etaje și mese"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.food
msgid "Food"
msgstr "Mâncare"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "For convenience, we are providing the following gratuity calculations:"
msgstr "Pentru comoditate, oferim următoarele calcule de gratuitate:"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_funghi
#: model:product.template,name:pos_restaurant.pos_food_funghi_product_template
msgid "Funghi"
msgstr "Funghi"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Green"
msgstr "Verde"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Grey"
msgstr "Gri"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TableGuestsButton.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr "Vizitatori"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "Guests ?"
msgstr "Vizitatori ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Guests:"
msgstr "Vizitatori:"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr "Înălțime"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr "Pozitie orizontală"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "ID"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""
"Dacă este fals, masa este dezactivată  și nu va fi disponibilă  în punctul "
"de vânzare"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineNoteButton.xml:0
#, python-format
msgid "Internal Note"
msgstr "Nota interna"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__note
msgid "Internal Note added by the waiter."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Internal Notes"
msgstr "Note Interne"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Este un bar / restaurant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Keep Open"
msgstr "Ține deschis"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Light grey"
msgstr "Gri Deschis"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Sigla"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_maki
#: model:product.template,name:pos_restaurant.pos_food_maki_product_template
msgid "Lunch Maki 18pc"
msgstr "Prânz Maki 18 bucăți"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_salmon
#: model:product.template,name:pos_restaurant.pos_food_salmon_product_template
msgid "Lunch Salmon 20pc"
msgstr "Prânz Somon 20 buc"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_temaki
#: model:product.template,name:pos_restaurant.pos_food_temaki_product_template
msgid "Lunch Temaki mix 3pc"
msgstr "Prânz Mix Temaki 3 buc"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_margherita
#: model:product.template,name:pos_restaurant.pos_food_margherita_product_template
msgid "Margherita"
msgstr "Margherita"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.minute_maid
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr "Minute Maid"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_mozza
#: model:product.template,name:pos_restaurant.pos_food_mozza_product_template
msgid "Mozzarella Sandwich"
msgstr "Sandwich cu Mozzarella"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_dirty
msgid "Mp Dirty"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__multiprint_resume
msgid "Multiprint Resume"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NEW"
msgstr "NOU"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:0
#, python-format
msgid "NOTE"
msgstr "NOTĂ"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "No Tip"
msgstr "Fără Bacșiș"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Note"
msgstr "Notă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "Nothing to Print"
msgstr "Nimic de tipărit"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Number of Seats ?"
msgstr "Nr. de Locuri ?"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Offline"
msgstr "Deconectat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Open"
msgstr "Afișare"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Orange"
msgstr "Portocaliu"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SubmitOrderButton.xml:0
#, python-format
msgid "Order"
msgstr "Comandă"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "Printare comandă"

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr "Imprimante pentru Note de plată"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"Comenzile Imprimante sunt utilizate de restaurante și baruri pentru a tipări \n"
"               actualizările comenzii în bucătărie / bar atunci când ospătarul actualizează comanda."

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr "Imprimante pentru POS"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "PRO FORMA"
msgstr "PRO FORMA"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_4formaggi
#: model:product.template,name:pos_restaurant.pos_food_4formaggi_product_template
msgid "Pasta 4 formaggi "
msgstr "Paste 4 formagii"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_bolo
#: model:product.template,name:pos_restaurant.pos_food_bolo_product_template
msgid "Pasta Bolognese"
msgstr "Paste Bolognese"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitBillScreen.xml:0
#, python-format
msgid "Payment"
msgstr "Plată"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Punct de vânzare"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configurarea Punctului de Vânzare"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Liniile Punctului de vânzare"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comenzile Punctului de vânzare"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Plăți Punct de Vânzare"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/BillScreen.xml:0
#, python-format
msgid "Print"
msgstr "Tipăriți"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "Tipăriți comenzile la bucătărie, la bar etc."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "Imprimate Categorii de produse"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr "Nume Imprimantă"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "Tip Imprimantă"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Printers"
msgstr "Imprimante"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/SubmitOrderButton.js:0
#, python-format
msgid "Printing failed"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "Imprimarea nu este acceptată pe unele browsere"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"Imprimarea nu este acceptată în unele browsere, deoarece nu este disponibil "
"un protocol de imprimare implicit. Este posibil să vă imprimați biletele "
"utilizând o casetă IoT."

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "Adresa Proxy IP"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Purple"
msgstr "Mov"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Red"
msgstr "Rosu"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Removing a table cannot be undone"
msgstr "Eliminarea unei mese nu poate fi anulată"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Rename"
msgstr "Redenumire"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Reprint receipts"
msgstr "Reimprimă chitanțe"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr "Etaj Restaurant"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr "Etaje Restaurant"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr "Imprimante pentru Note de plată în Restaurnat"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Imprimantă Restaurant"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr "Masă Restaurant"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Inversare"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Plată Inversă"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__round
msgid "Round"
msgstr "Rotund"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Round Shape"
msgstr "Formă Rotundă"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_chirashi
#: model:product.template,name:pos_restaurant.pos_food_chirashi_product_template
msgid "Salmon and Avocado"
msgstr "Somon și Avocado"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr "Locuri"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Servit de"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__set_tip_after_payment
msgid "Set Tip After Payment"
msgstr "Trimite bacșiș după plată"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TipScreen.xml:0
#, python-format
msgid "Settle"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr "Formă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Signature"
msgstr "Semnătură"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order_line__mp_skip
msgid "Skip line when sending ticket to kitchen printers."
msgstr ""
"Săriți peste linie când trimiteți bilet către imprimantele din bucătărie."

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_tuna
#: model:product.template,name:pos_restaurant.pos_food_tuna_product_template
msgid "Spicy Tuna Sandwich"
msgstr "Sandwich cu Tuna Iute"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/SplitBillButton.xml:0
#, python-format
msgid "Split"
msgstr "Separă"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Split total or order lines"
msgstr "Divizare total sau linii de comandă"

#. module: pos_restaurant
#: model:ir.model.fields.selection,name:pos_restaurant.selection__restaurant_table__shape__square
msgid "Square"
msgstr "Pătrat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Square Shape"
msgstr "Formă Pătrată"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Subtotal"
msgstr "Subtotal"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
#, python-format
msgid "Table"
msgstr "Tabel"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr "Nume masă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Table Name ?"
msgstr "Nume tabel ?"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr "Tabele"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Tel:"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "Adresa IP sau numele de gazdă al proxy hardware-ului imprimantei"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr "Valoare nr. clienți care au fost prestați prin această comandă."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr "Numărul implicit de client servit la această masă"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__table_ids
msgid "The list of tables in this floor"
msgstr "Lista de mese din acest etaj"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr "Etejele restaurantului deservite de acest punct de vânzare."

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr "Masa la care a fost servită această comandă"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""
"Culoarea tabelului, exprimată ca valoare validă a proprietății CSS „de "
"fundal”"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr "Înălțimea tabelului în pixeli"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""
"Poziția orizontală a tabelului de la partea stângă spre centru, în pixeli"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""
"Poziția verticală a tabelului de la partea de sus spre centru, în pixeli"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr "Lățimea tabelului în pixeli"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/PrintBillButton.js:0
#, python-format
msgid "There are no order lines"
msgstr "Nu există linii de comandă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "This floor has no tables yet, use the"
msgstr "Acest etaj nu are încă mese, folosiți"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Tint"
msgstr "Tentă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/TicketScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Sfat"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Tip:"
msgstr "Bacșiș:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TicketScreen.js:0
#, python-format
msgid "Tipping"
msgstr "Bacșiș:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "Total:"
msgstr "Total:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ProductScreen/ControlButtons/TransferOrderButton.xml:0
#, python-format
msgid "Transfer"
msgstr "Transfer"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Turquoise"
msgstr "Turcoaz"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to change background color"
msgstr "Nu se poate schimba culoarea de fundal"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to create table because you are offline."
msgstr "Imposibil de creat tabelul deoarece sunteți offline."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to delete table"
msgstr "Nu se poate șterge tabelul"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/FloorScreen/FloorScreen.js:0
#, python-format
msgid "Unable to get orders count"
msgstr ""

#. module: pos_restaurant
#: model:product.product,uom_name:pos_restaurant.coke
#: model:product.product,uom_name:pos_restaurant.minute_maid
#: model:product.product,uom_name:pos_restaurant.pos_food_4formaggi
#: model:product.product,uom_name:pos_restaurant.pos_food_bacon
#: model:product.product,uom_name:pos_restaurant.pos_food_bolo
#: model:product.product,uom_name:pos_restaurant.pos_food_cheeseburger
#: model:product.product,uom_name:pos_restaurant.pos_food_chicken
#: model:product.product,uom_name:pos_restaurant.pos_food_chirashi
#: model:product.product,uom_name:pos_restaurant.pos_food_club
#: model:product.product,uom_name:pos_restaurant.pos_food_funghi
#: model:product.product,uom_name:pos_restaurant.pos_food_maki
#: model:product.product,uom_name:pos_restaurant.pos_food_margherita
#: model:product.product,uom_name:pos_restaurant.pos_food_mozza
#: model:product.product,uom_name:pos_restaurant.pos_food_salmon
#: model:product.product,uom_name:pos_restaurant.pos_food_temaki
#: model:product.product,uom_name:pos_restaurant.pos_food_tuna
#: model:product.product,uom_name:pos_restaurant.pos_food_vege
#: model:product.product,uom_name:pos_restaurant.water
#: model:product.template,uom_name:pos_restaurant.coke_product_template
#: model:product.template,uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_4formaggi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bacon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_bolo_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_cheeseburger_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chicken_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_chirashi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_club_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_funghi_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_maki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_margherita_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_mozza_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_salmon_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_temaki_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_tuna_product_template
#: model:product.template,uom_name:pos_restaurant.pos_food_vege_product_template
#: model:product.template,uom_name:pos_restaurant.water_product_template
msgid "Units"
msgstr "buc"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/TipScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__sequence
msgid "Used to sort Floors"
msgstr "Folosit pentru sortare Etaje"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "TVA:"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.pos_food_vege
#: model:product.template,name:pos_restaurant.pos_food_vege_product_template
msgid "Vegetarian"
msgstr "Vegetarian"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr "Poziție verticală"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.water
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Apa"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr "Lățime"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "With a"
msgstr "Cu"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/EditBar.xml:0
#, python-format
msgid "Yellow"
msgstr "Galben"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/Screens/ProductScreen/ControlButtons/TableGuestsButton.js:0
#, python-format
msgid "You cannot put a number that exceeds %s "
msgstr ""

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a floor that is used in a PoS session, close the "
"session(s) first: \n"
msgstr ""

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_restaurant.py:0
#, python-format
msgid ""
"You cannot remove a table that is used in a PoS session, close the "
"session(s) first."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "________________________"
msgstr "________________________"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/TipReceipt.xml:0
#, python-format
msgid "______________________________________________"
msgstr "______________________________________________"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "at"
msgstr "la"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "at table"
msgstr "la masă"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/FloorScreen/FloorScreen.xml:0
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr "din bara de instrumente de editare pentru a crea tabele noi."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/SplitBillScreen/SplitOrderline.xml:0
#, python-format
msgid "discount"
msgstr "reducere"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "env.pos.config.set_tip_after_payment and !currentOrder.is_paid()"
msgstr ""

#. module: pos_restaurant
#: code:addons/pos_restaurant/models/pos_config.py:0
#, python-format
msgid "Cash Bar"
msgstr ""
