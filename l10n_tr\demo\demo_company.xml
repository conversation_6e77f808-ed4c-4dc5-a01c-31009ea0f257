<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_tr" model="res.partner">
        <field name="name">TR Company</field>
        <field name="vat">3297552117</field>
        <field name="street">3281. Cadde</field>
        <field name="city">İç Anadolu Bölgesi</field>
        <field name="country_id" ref="base.tr"/>
        <field name="state_id" ref="base.state_tr_81"/>
        <field name="zip">06810</field>
        <field name="phone">+90 501 234 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.trexample.com</field>
    </record>

    <record id="demo_company_tr" model="res.company">
        <field name="name">TR Company</field>
        <field name="partner_id" ref="partner_demo_company_tr"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_tr')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_tr.demo_company_tr'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_tr.l10ntr_tek_duzen_hesap')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_tr.demo_company_tr')"/>
    </function>
</odoo>
