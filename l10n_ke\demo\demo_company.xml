<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ke" model="res.partner">
        <field name="name">KE Company</field>
        <field name="vat"></field>
        <field name="street"><PERSON>, 3rd Flr <PERSON>le Selassie Ave, 48505-00100 GPO</field>
        <field name="city">Nairobi</field>
        <field name="country_id" ref="base.ke"/>
        <field name="zip"></field>
        <field name="phone">+*********** 919</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.keexample.com</field>
    </record>

    <record id="demo_company_ke" model="res.company">
        <field name="name">KE Company</field>
        <field name="partner_id" ref="partner_demo_company_ke"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ke')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ke.demo_company_ke'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ke.l10nke_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ke.demo_company_ke')"/>
    </function>
</odoo>
