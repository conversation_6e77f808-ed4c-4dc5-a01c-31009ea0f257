<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_inherit_purchase" model="ir.ui.view">
        <field name="name">account.move.inherit.purchase</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="groups_id" eval="[(4, ref('purchase.group_purchase_user'))]"/>
        <field name="arch" type="xml">
            <!-- Auto-complete could be done from either a bill either a purchase order -->
            <field name="invoice_vendor_bill_id" position="after">
                <field name="purchase_id" invisible="1"/>
                <label for="purchase_vendor_bill_id" string="Auto-Complete" class="oe_edit_only"
                       attrs="{'invisible': ['|', ('state','!=','draft'), ('move_type', '!=', 'in_invoice')]}" />
                <field name="purchase_vendor_bill_id" nolabel="1"
                       attrs="{'invisible': ['|', ('state','!=','draft'), ('move_type', '!=', 'in_invoice')]}"
                       class="oe_edit_only"
                       domain="partner_id and [('company_id', '=', company_id), ('partner_id.commercial_partner_id', '=', commercial_partner_id)] or [('company_id', '=', company_id)]"
                       placeholder="Select a purchase order or an old bill"
                       context="{'show_total_amount': True}"
                       options="{'no_create': True, 'no_open': True}"/>
            </field>
            <label name="invoice_vendor_bill_id_label" position="attributes">
                <attribute name="invisible">1</attribute>
            </label>
            <field name="invoice_vendor_bill_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <!-- Add link to purchase_line_id to account.move.line -->
            <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='company_id']" position="after">
                <field name="purchase_line_id" invisible="1"/>
                <field name="purchase_order_id" attrs="{'column_invisible': [('parent.move_type', '!=', 'in_invoice')]}" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/tree/field[@name='company_id']" position="after">
                <field name="purchase_line_id" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>
