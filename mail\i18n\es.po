# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid " This channel is private. People must be invited to join it."
msgstr "El canal es privado. Hay que tener una invitación para unirse."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires \"%s\" access"
msgstr "\"%s\" requiere \"%s\" acceso"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "\"%s\" requires microphone access"
msgstr "\"%s\" requiere acceso al micrófono"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "%(activity_name)s: %(summary)s assigned to you"
msgstr "%(activity_name)s: %(summary)s asignado a usted"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s no se reconoce como un correo electrónico válido. Esto es "
"necesario para crear un nuevo cliente."

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s le invitó a seguir %(document)s el documento: %(title)s"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s le invitó a seguir un nuevo documento."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Message"
msgstr "%d Mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%d Messages"
msgstr "%d mensajes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "%d days overdue"
msgstr "%d días de retraso"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "%d days overdue:"
msgstr "%d días de retraso:"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s y %s están escribiendo.."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s y %s reaccionaron con %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s connected"
msgstr "%s conectado"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s creado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "%s from %s"
msgstr "%s de %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "%s has a request"
msgstr "%s tiene una solicitud"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s reaccionó con %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s esta escribiendo..."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s inició una conferencia en vivo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s y más están escribiendo.."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s, %s, %s y %s otras personas reaccionaron con  %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s, %s, %s y 1 persona más reaccionaron con %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message_reaction_group/message_reaction_group.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s reaccionaron con %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "(from"
msgstr "(de"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(originalmente asignado a"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid ", "
msgstr ", "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid ", enter to"
msgstr ", Intro para"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "-&gt;"
msgstr "-&gt;"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". Limite su búsqueda para ver más opciones."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ".<br/>"
msgstr ".<br/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Future"
msgstr "0 futuro"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Late"
msgstr "0 tarde"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "0 Today"
msgstr "0 hoy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">No record for this model</b>"
msgstr ""
"<b attrs=\"{'invisible': [('no_record', '=', False)]}\" class=\"text-"
"warning\">Sin registro para este modelo</b>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<br><br>\n"
"            Type <b>@username</b> to mention someone, and grab his attention.<br>\n"
"            Type <b>#channel</b> to mention a channel.<br>\n"
"            Type <b>/command</b> to execute a command.<br>"
msgstr ""
"<br><br>\n"
"            Escriba <b>@nombredeusuario</b> para mencionar a alguien y llamar su atención.<br>\n"
"            Escriba <b>#canal</b> para mencionar un canal.<br>\n"
"            Escriba <b>/comando</b>para ejecutar un comando.<br>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">created <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"
msgstr ""
"<div class=\"o_mail_notification\">creado <a href=\"#\" "
"class=\"o_channel_redirect\" data-oe-id=\"%s\">#%s</a></div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"<div class=\"o_mail_notification\">invited <a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> to the channel</div>"
msgstr ""
"<div class=\"o_mail_notification\">invitó<a href=\"#\" data-oe-"
"model=\"res.partner\" data-oe-"
"id=\"%(new_partner_id)d\">%(new_partner_name)s</a> al canal</div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">joined the channel</div>"
msgstr "<div class=\"o_mail_notification\">se unió al canal</div>"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "<div class=\"o_mail_notification\">left the channel</div>"
msgstr "<div class=\"o_mail_notification\">abandonó el canal</div>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Chat with coworkers</b> in real-time using direct "
"messages.</p><p><i>You might need to invite users from the Settings app "
"first.</i></p>"
msgstr ""
"<p><b>Chatee con sus compañeros</b> en tiempo real usando mensajes "
"directos.</p><p><i>Puede que necesite invitar a usuarios de la aplicación de"
" configuración primero.</i></p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p><b>Write a message</b> to the members of the channel here.</p> <p>You can"
" notify someone with <i>'@'</i> or link another channel with <i>'#'</i>. "
"Start your message with <i>'/'</i> to get the list of possible commands.</p>"
msgstr ""
"<p><b>Escribe un mensaje</b> a los miembros del canal aquí.</p> <p>Puedes "
"notificar a alguien con <i>'@'</i> o enlazar con otro canal con <i>'#'</i>. "
"Empieza tus mensajes con <i>'/'</i> para obtener la lista de comandos.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"<p>Channels make it easy to organize information across different topics and"
" groups.</p> <p>Try to <b>create your first channel</b> (e.g. sales, "
"marketing, product XYZ, after work party, etc).</p>"
msgstr ""
"<p>Los canales hacen fácil de organizar la información entre diferentes "
"tópicos y grupos.</p> <p>Prueba de <b>crear tu primer canal</b> (ej. ventas,"
" marketing, producto XYZ, después de la fiesta del trabajo, etc).</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a channel here.</p>"
msgstr "<p>Crear un canal aquí.</p>"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "<p>Create a public or private channel.</p>"
msgstr "<p>Crea un canal público o privado.</p>"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"<p>Dear Sender,<br /><br />\n"
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"Please make sure you are using the correct address or contact us at %(default_email)s instead.<br /><br />\n"
"Kind Regards,</p>"
msgstr ""
"<p>Estimado remitente,<br /><br />\n"
"El siguiente mensaje no pudo entregarse a la dirección%(alias_display_name)s.\n"
"Solo %(contact_description)s están autorizados a ponerse en contacto.<br /><br />\n"
"Asegúrese de utilizar la dirección correcta o póngase en contacto con nosotros en %(default_email)s.<br /><br />\n"
"Saludos cordiales,</p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Email mass mailing</strong> on\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">the selected records</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">the current search filter</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Followers of the document and</span>"
msgstr ""
"<span attrs=\"{'invisible': [('composition_mode', '!=', 'mass_mail')]}\">\n"
"                                <strong>Envio de correo electrónico masivo</strong> en\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">los registros seleccionados</span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">el filtro de búsqueda</span>.\n"
"                            </span>\n"
"                            <span name=\"document_followers_text\" attrs=\"{'invisible':['|', ('model', '=', False), ('composition_mode', '=', 'mass_mail')]}\">Seguidores del documento y</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    If you want to send it for all the records matching your search criterion, check this box :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    If you want to use only selected records please uncheck this selection box :\n"
"                                </span>"
msgstr ""
"<span attrs=\"{'invisible': [('use_active_domain', '=', True)]}\">\n"
"                                    Si quiere enviarlo a todos los registros que coincidan con los criterios de la búsqueda, marque esta casilla :\n"
"                                </span>\n"
"                                <span attrs=\"{'invisible': [('use_active_domain', '=', False)]}\">\n"
"                                    Si quiere utilizar solo los registros seleccionados desmarque esta casilla :\n"
"                                </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Force a language: </span>"
msgstr "<span class=\"col-md-5 col-lg-4 col-sm-12 pl-0\">Forzar un idioma:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"<span class=\"fa fa-info-circle\"/> Caution: It won't be possible to send "
"this mail again to the recipients you did not select."
msgstr ""
"<span class=\"fa fa-info-circle\"/> Precaución: no será posible enviar este "
"correo nuevamente a los destinatarios que no seleccionó."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Activities</span>"
msgstr "<span class=\"o_form_label\">Actividades</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Custom ICE server list</span>"
msgstr "<span class=\"o_form_label\">Lista de servidores ICE personalizada</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Add</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Añadir</span>\n"
"                                    <span class=\"o_stat_text\">Acción del contexto</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"<span class=\"o_stat_text\">Remove</span>\n"
"                                    <span class=\"o_stat_text\">Context Action</span>"
msgstr ""
"<span class=\"o_stat_text\">Eliminar</span>\n"
"                                    <span class=\"o_stat_text\">Acción del contexto</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>\n"
"                                    All records matching your current search filter will be mailed,\n"
"                                    not only the ids selected in the list view.\n"
"                                </strong><br/>\n"
"                                The email will be sent for all the records selected in the list.<br/>\n"
"                                Confirming this wizard will probably take a few minutes blocking your browser."
msgstr ""
"<strong>Todos los registros que coincidan con su filtro de búsqueda actual\n"
"se enviarán por correo, no solo los ids seleccionados en la vista de lista. </strong> <br/>\n"
"El email se enviará a todos los registros seleccionados en la lista. <br/>\n"
"Confirmar este asistente puede bloquear durante algunos minutos su navegador."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Comunicación interna</strong>: Respondiendo a este mensaje se "
"publicará una nota interna. Los seguidores externos no recibirán ninguna "
"notificación por correo electrónico."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<strong>Only records checked in list view will be used.</strong><br/>\n"
"                                The email will be sent for all the records selected in the list."
msgstr ""
"<strong>Sólo los registros marcados en la vista de lista serán usados.</strong><br/>\n"
"                                El email se enviará a todos los registros seleccionados en la lista."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Nota original:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "<strong>Recommended Activities</strong>"
msgstr "<strong>Actividades recomendadas</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_channel__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Diccionario Python a evaluar para proporcionar valores por defecto cuando un"
" nuevo registro se cree para este seudónimo."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "La presencia del bus debe tener un usuario o invitado."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_partner_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "El miembro de un canal debe ser partner o invitado."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "Un canal del tipo 'chat' no puede tener más de dos usuarios."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "No se debe crear un chat con más de 2 personas. Mejor cree un grupo."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "La reacción a un mensaje debe ser de un partner o de un invitado."

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use the chatter"
msgstr ""
"La siguiente actividad solo se puede planificar en modelos que utilizan los "
"comentarios"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"A shortcode is a keyboard shortcut. For instance, you type #gm and it will "
"be transformed into \"Good Morning\"."
msgstr ""
"Un código corto es un atajo de teclado. Por ejemplo, si teclas #gm se "
"transformara en \"Buenos días\"."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Un ajuste de volumen debe tener un partner o un invitado."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Accept"
msgstr "Aceptar"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Grupos de acceso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Token de acceso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Acción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
msgid "Action To Do"
msgstr "Acción a realizar"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Vista de acción de ventana"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Las acciones pueden desencadenar comportamientos específicos como abrir la "
"vista de calendario o marcar automáticamente como hecho cuando un documento "
"es subido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Activado por defecto en la suscripción."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_channel__active
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Active"
msgstr "Activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__active_domain
msgid "Active domain"
msgstr "Dominio activo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#, python-format
msgid "Activities"
msgstr "Actividades"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_view.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
#, python-format
msgid "Activity"
msgstr "Actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad  de Excepción"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Mezcla de actividades"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Configuración de la actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Tipo de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ícono de tipo de actvidad"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Tipos de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "Activity User Type"
msgstr "Tipo de actividad de usuario"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Tipo de actividad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Añadir lista negra de correo electrónico"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Añadir seguidores"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Add Followers can only be done on a mail thread model"
msgstr ""
"Añadir seguidores solo puede hacerse en un modelo de hilos de correo "
"electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_mail__add_sign
#: model:ir.model.fields,field_description:mail.field_mail_message__add_sign
msgid "Add Sign"
msgstr "Agregar Signo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.js:0
#, python-format
msgid "Add a Reaction"
msgstr "Añadir una reacción"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add a description"
msgstr "Añadir una descripción"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""
"Agregar un nuevo %(document)s o envíe un correo electrónico a %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Añadir una dirección de correo electrónico a la lista negra"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/text_emojis.xml:0
#, python-format
msgid "Add an emoji"
msgstr "Agrega un emoji"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Add attachment"
msgstr "Añadir adjunto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#, python-format
msgid "Add attachments"
msgstr "Añadir adjuntos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Añadir contactos a notificar..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Add or join a channel"
msgstr "Agregar o unirse a un canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Add users"
msgstr "Añadir usuarios"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "Añada sus credenciales de twilio a los servidores ICE"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"No es posible añadir seguidores en los canales. En su lugar, considere "
"añadir miembros."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Contactos adicionales"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Avanzado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Advanced Settings"
msgstr "Configuración avanzada"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "Alerta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_contact
msgid "Alias Contact Security"
msgstr "Seudónimo del contacto de seguridad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Seudónimo del dominio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_name
msgid "Alias Name"
msgstr "Seudónimo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_domain
msgid "Alias domain"
msgstr "Seudónimo del dominio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_model_id
msgid "Aliased Model"
msgstr "Modelo con seudónimo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_alias
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Seudónimos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Todos"

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Se debe proporcionar un código de acceso para cada archivo adjunto."

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "Se requiere un correo electrónico para que find_or_create funcione"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending an email."
msgstr "Se ha producido un error al enviar el e-mail."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "Se ha producido un error al recuperar los mensajes."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "Ocurrió un error inesperado durante la creación del chat."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "Y"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "Y 1 miembro más."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Anonymous"
msgstr "Anónimo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Aplica a"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Apply"
msgstr "Aplicar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Archived"
msgstr "Archivado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "¿Está seguro de que desea eliminar este mensaje?"

#. module: mail
#: code:addons/mail/wizard/mail_resend_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s mail delivery failures? You won't be "
"able to re-send these mails later!"
msgstr ""
"¿Está seguro de que desea descartar %s errores en la entrega de correo? ¡No "
"podrá volver a enviar estos correos más tarde!"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""
"¿Está seguro de que quiere eliminar la lista negra de esta dirección de "
"correo electrónico?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign to ..."
msgstr "Asignar a..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Assign/unassign to me"
msgstr "Asignar/desasignar a mí"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#, python-format
msgid "Assigned to"
msgstr "Asignada a"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"El usuario asignado %s no tiene acceso a este documento y no puede ejecutar "
"esta actividad."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Adjuntar un archivo"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Adjunto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "Cargando contador de adjuntos..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_box/attachment_box.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Adjuntos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,help:mail.field_mail_message__attachment_ids
msgid ""
"Attachments are linked to a document through model / res_id and to the "
"message through this field."
msgstr ""
"Los adjuntos se asocian a un documento mediante el modelo / res_id y al "
"mensaje mediante este campo."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Empresas autenticadas"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor del mensaje. Si no se establece, email_from puede contener una "
"dirección de correo que no coincida con la de ninguna empresa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar del autor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_public_id
msgid "Authorized Group"
msgstr "Grupo autorizado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Auto eliminar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Grupos de suscripción automática"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__group_ids
msgid "Auto Subscription"
msgstr "Auto suscripción"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Auto suscripción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Actividad automatizada"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Programar automáticamente esta actividad una vez que la actual se marque "
"como hecha."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Avatar of OdooBot"
msgstr "Avatar de OdooBot"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of guest"
msgstr "Avatar del invitado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "Avatar del usuario"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Ausente"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Base"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Best regards,"
msgstr "Saludos cordiales,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Lista de denegación"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Fecha de alta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Direcciones incluidas en la lista de denegación"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Direcciones de correo electrónico incluidas en la lista de denegación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Contenido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Bot"
msgstr "Bot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
msgid "Bounce"
msgstr "Rebote"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Devuelto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Browser default"
msgstr "Navegador predeterminado"

#. module: mail
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "Correo electrónico cc"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Llamada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Camera is off"
msgstr "La cámara está desactivada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
msgid "Can Write"
msgstr "Puede escribir"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""
"No se puede actualizar el mensaje o el destinatario de una notificación."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Cancelar correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Cancel notification in failure"
msgstr "Cancelar notificación en caso de fallo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Cancelada"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Respuesta preparada / código corto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__canned_response_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__canned_response_ids
msgid "Canned Responses"
msgstr "Respuestas preparadas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Destinatarios en Copia Carbón del mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "Destinatarios de copia carbón"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr ""
"Destinatarios para copia carbón (se pueden utilizar aquí expresiones de "
"campos)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Correo electrónico de Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Tipo de encadenamiento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Change Layout"
msgstr "Cambiar diseño"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Change layout"
msgstr "Cambiar diseño"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr ""
"Cambie el color de fondo de las actividades relacionadas de este tipo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Changed"
msgstr "Cambiado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__channel_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Canal"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(guest_names)s"
msgstr ""
"El canal \"%(channel_name)s\" solo acepta miembros del grupo "
"\"%(group_name)s\". Prohibido para: %(guest_names)s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Channel \"%(channel_name)s\" only accepts members of group "
"\"%(group_name)s\". Forbidden for: %(partner_names)s"
msgstr ""
"El canal \"%(channel_name)s\" solo acepta miembros del grupo "
"\"%(group_name)s\". Prohibido para:%(partner_names)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__channel_partner_id
msgid "Channel Partner"
msgstr "Partner del canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_type
msgid "Channel Type"
msgstr "Tipo de canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Channel settings"
msgstr "Configuración de canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.mail_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_partner_view_tree
#, python-format
msgid "Channels"
msgstr "Canales"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_partner_action
#: model:ir.ui.menu,name:mail.mail_channel_partner_menu
msgid "Channels/Partner"
msgstr "Canales/Socios"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Charla"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Chat Shortcode"
msgstr "Código corto de chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"El chat es privado y único entre 2 personas. El grupo es privado entre las "
"personas invitadas. El acceso al canal es libre (dependiendo de su "
"configuración)."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Mensajes hijos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Choose an example"
msgstr "Escoja un ejemplo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#, python-format
msgid "Click here to retry"
msgstr "Haga clic aquí para volver a intentarlo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Click on your message"
msgstr "Haga clic en su mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "Haga clic para ver los archivos adjuntos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Close (Esc)"
msgstr "Cerrar (Esc)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close chat window"
msgstr "Ajuste de canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Close conversation"
msgstr "Cerrar conversación"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__closed
msgid "Closed"
msgstr "Cerrado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Direcciones CC separadas por comas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "IDs de destinatarios separados por comas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "Ids de destinatarios separados por comas (pude usar \"marcadores\")"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Direcciones de destinatarios separadas por comas"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Destinatarios separados por comas (se pueden utilizar aquí expresiones de "
"campos)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Comentario"

#. module: mail
#: model:ir.model,name:mail.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/mail_template/mail_template.js:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Componer correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Modo de composición"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "Configurar la lista de servidores ICE para webRTC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Configurar sus tipos de actividad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Configure sus propios servidores de correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Confirm"
msgstr "Confirmar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Confirmación"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "Felicidades, ha terminado sus actividades."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "¡Enhorabuena! Su bandeja de entrada está vacía."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/widgets/discuss/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "¡Enhorabuena! Su bandeja de entrada está vacía."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Error de conexión (problema del servidor de correo saliente)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Considere las respuestas como un nuevo hilo"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Contactos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel_partner__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""
"Incluya la fecha y hora del último evento interesante que ha ocurrido en "
"este canal para este partner. Esto incluye: crear, unirse, fijar, y nuevo "
"mensaje publicado."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Contenido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Contenidos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fold_state
msgid "Conversation Fold State"
msgstr "Conversación plegada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_minimized
msgid "Conversation is minimized"
msgstr "Conversación minimizada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Conversations"
msgstr "Conversaciones"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""
"Contador del número de correos electrónicos rebotados de este contacto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "País"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Create"
msgstr "Crear"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Next Activity"
msgstr "Crear nueva actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Crear Uid"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Create group chat"
msgstr "Crear un chat grupal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "Crear nuevos %(document)s"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr ""
"Cree un nuevo %(document)s enviando un correo electrónico a %(email_link)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Create or search channel..."
msgstr "Crear o buscar canal..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Created"
msgstr "Creada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Creado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Creado el"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Creating a new record..."
msgstr "Creando un nuevo registro..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Fecha de Creación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Credencial"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"El usuario actual tiene una notificación destacada asociada a este mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mensaje rebotado personalizado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__custom_channel_name
msgid "Custom channel name"
msgstr "Nombre de canal personalizado"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr ""
"Se requiere al cliente para recibir notificaciones por correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Fecha"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Días"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Deadline"
msgstr "Fecha límite"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Deafen"
msgstr "Ensordecer"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Querido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Tipo de decoración"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Modo de visualización predeterminado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Nota predeterminada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Resumen predeterminado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Usuario predeterminado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,field_description:mail.field_mail_template__null_value
msgid "Default Value"
msgstr "Valor por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_defaults
msgid "Default Values"
msgstr "Valores por defecto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Destinatarios por defecto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Destinatarios por defecto del registro:\n"
"- Empresa (usando el id de la empresa o del campo partner_id) O\n"
"- Correo electrónico (usando los campos email_from o email)"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Define a new chat shortcode"
msgstr "Definir un nuevo shortcode de chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Etiqueta de retraso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Tipo de retraso"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "Retraso después de soltar la función pulsar para hablar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Unidades de retraso"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: code:addons/mail/static/src/xml/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Suprimir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Borrar correos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_message
msgid "Delete Message Copy"
msgstr "Borrar copia del mensaje"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Entrega fallida"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "Fallo en la entrega"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Descripción"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Descripción que se añadirá en el mensaje mandado para este subtipo. Si no se"
" rellena, se pondrá el nombre en su lugar."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Determina cómo se mostrará el canal de forma predeterminada al abrirlo desde"
" el enlace de invitación. Ningún valor significa que se muestra texto (sin "
"voz/video)."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Direct Messages"
msgstr "Mensajes directos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Discard delivery failures"
msgstr "Descartar fallos de entrega"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_cancel_action
msgid "Discard mail delivery failures"
msgstr "Descartar fallos en la entrega de correo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Discard message delivery failures"
msgstr "Descartar errores en la entrega de mensajes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "Desconectar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "Desconectado de la llamada RTC por el servidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#: code:addons/mail/static/src/models/discuss/discuss.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Conversaciones"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Barra lateral de Conversaciones"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel
msgid "Discussion Channel"
msgstr "Canal de conversaciones"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Conversaciones"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_cancel
msgid "Dismiss notification for resend by model"
msgstr "Descartar notificación para reenviar por modelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Mostrar una opción en el documento relacionado para abrir un asistente de "
"composición con esta plantilla"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__res_name
msgid "Display name of the related document."
msgstr "Mostrar el nombre del documento relacionado."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_message
msgid ""
"Do not keep a copy of the email in the document communication history (mass "
"mailing only)"
msgstr ""
"No mantengas una copia del correo en el histórico de comunicación del "
"documento (solo correo masivo)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "¿Realmente desea eliminar \"%s\"?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Documento"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Seguidores del documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
msgid "Document Model"
msgstr "Modelo de documento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Nombre del documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Documentation"
msgstr "Documentación"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Done"
msgstr "Hecho"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Launch Next"
msgstr "Listo y lanzar siguiente"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.js:0
#: code:addons/mail/static/src/xml/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "¡Hecho! Prepare el próximo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Download"
msgstr "Descargar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Download logs"
msgstr "Descargar registros"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/drop_zone/drop_zone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "Arrastre los archivos aquí"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Dropdown menu"
msgstr "Menú desplegable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
msgid "Due Date"
msgstr "Fecha de vencimiento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Fecha de vencimiento en"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Due in %d days"
msgstr "Vence en %d días"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Due in %d days:"
msgstr "Vence en %d días:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Due on"
msgstr "Vence el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Tipo de vencimiento"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Correo electrónico duplicado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Duración de la actividad vocal en ms"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Dynamic Placeholder Generator"
msgstr "Generador dinámico de expresiones con marcadores de posición"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Editar contactos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_subtype_list/follower_subtype_list.xml:0
#, python-format
msgid "Edit Subscription of"
msgstr "Editar suscripción de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Editar subscripción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Dirección de Email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Email Alias"
msgstr "Pseudónimo de correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Seudónimos de correos"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "Alias de correo electrónico Mixin"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Lista negra de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "Gestión de correo electrónico CC"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Configuración del correo electrónico"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "Envío masivo de correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "Vista previa del correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Buscar correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Plantilla de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Vista preliminar de la plantilla del correo electrónico"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
msgid "Email Templates"
msgstr "Plantillas de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Hilo de mensajes"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "¡Esta dirección de correo electrónico ya existe!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Dirección de correo del remitente. Este campo se establece cuando no se "
"encuentra ningún socio coincidente y reemplaza el campo author_id en la "
"charla."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Email address to which replies will be redirected"
msgstr ""
"Dirección de correo electrónico a la que se redireccionarán las respuestas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"Dirección de correo electrónico a la que se redireccionarán las respuestas "
"cuando se envíen correos electrónicos masivos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"Dirección de correo electrónico a la que se redireccionarán las respuestas "
"cuando se envíen correos electrónicos masivos; solo se utiliza cuando la "
"respuesta no está registrada en el hilo de conversación original."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""
"Las direcciones de correo electrónico que estén en la lista negra no "
"recibirán más correos electrónicos"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email alias %(alias_name)s cannot be used on %(count)d records at the same "
"time. Please update records one by one."
msgstr ""
"El seudónimo de correo electrónico %(alias_name)s no se puede usar en los "
"registros %(count)dal mismo tiempo. Actualice los registros uno por uno "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "Correo electrónico cc"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Mensaje de correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Asistente de reenvío de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "Plantillas de correo electrónico"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Correos electrónicos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Emojis"
msgstr "Emojis"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Empleado solamente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Habilitar rastreo ordenado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.xml:0
#, python-format
msgid "Enable desktop notifications to chat."
msgstr "Habilite las notificaciones de escritorio para chatear."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid "Envelope Example"
msgstr "Ejemplo de sobre"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Error"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Mensaje de error"

#. module: mail
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr ""
"Error durante la comunicación con el servidor de garantía del editor del "
"publicador."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Error sin excepción. Probablemente debido a la actualización de acceso "
"concurrente de los registros de notificación. Por favor, consulte con un "
"administrador."

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due do sending an email without computed "
"recipients."
msgstr ""
"Error sin excepción. Probablemente debido al enviar un correo sin los "
"recipientes procesados."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Error, una empresa no puede seguir dos veces el mismo objeto."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__public
msgid "Everyone"
msgstr "Todos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Excepción"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__member_count
msgid "Excluding guests from count."
msgstr "Excluir a los invitados del recuento."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Exit full screen"
msgstr "Salir de pantalla completa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Filtros extendidos..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Correo Fallido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Fallido"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render QWeb template : %s)"
msgstr "No se pudo visualizar en la plantilla QWeb: %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template : %s)"
msgstr "No se pudo visualizar en la plantilla inline_template : %s)"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template : %(xml_id)s (%(view_id)d)"
msgstr "No se pudo visualizar la plantilla:  %(xml_id)s (%(view_id)d)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Razón del fallo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Razón del fallo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Razón del fallo. Esto suele ser la excepción lanzada por el servidor de "
"correo electrónico, almacenado para facilitar la depuración de los problemas"
" de correo."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Tipo de fallo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Hecho Favorito por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Feedback"
msgstr "Comentarios"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field
msgid "Field"
msgstr "Campo"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "El campo \"Actividad de correo\" no se puede cambiar a \"Falso\"."

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "El campo \"Lista negra de correo\" no se puede cambiar a \"Falso\"."

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "El campo \"Hilo de mensajes\" no puede cambiarse a \"Falso\"."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_desc
msgid "Field Description"
msgstr "Descripción campo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "Grupos del campo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_type
msgid "Field Type"
msgstr "Tipo de campo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Detalles del campo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Campo usado para enlazar el modelo relacionado al modelo de subtipo cuando "
"se utiliza suscripción automática en el documento relacionado. El campo se "
"usa para calcular getattr(relacionado.campo_relacionado)."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,help:mail.field_mail_template__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Expresión de campos final, a ser copia-pegada en el campo de la plantilla "
"deseado."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or create a channel..."
msgstr "Encuentra o crea un canal ..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Find or start a conversation..."
msgstr "Busque o inicie una conversación..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__folded
msgid "Folded"
msgstr "Replegado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Follow"
msgstr "Seguir"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
#, python-format
msgid "Followers"
msgstr "Seguidores"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Formulario de seguidores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Followers of"
msgstr "Seguidores de"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Sólo seguidores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Following"
msgstr "Siguiendo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Email formateado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Desde"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Compositor completo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#, python-format
msgid "Full screen"
msgstr "Pantalla completa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Video en pantalla completa"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Future"
msgstr "Futuro"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Pasarela"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Generic User From Record"
msgstr "Usuario genérico del registro"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Go to the configuration panel"
msgstr "Ir al panel de configuración"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__channel_type__group
msgid "Group"
msgstr "Grupo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Agrupar por"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "Chat grupal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_tree
msgid "Groups"
msgstr "Grupos"

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
#, python-format
msgid "Guest"
msgstr "Invitado"

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "El campo de nombre de invitado no puede estar vacío."

#. module: mail
#: code:addons/mail/models/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "El nombre del invitado es demasiado largo."

#. module: mail
#: model:ir.ui.menu,name:mail.mail_guest_menu
msgid "Guests"
msgstr "Clientes"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "Ruta HTTP "

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Manejar por correos electrónicos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Manejar en Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__has_cancel
msgid "Has Cancel"
msgstr "Ha cancelado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Tiene menciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "Ha desactivado el sonido de entrada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
#: model:ir.model.fields,help:mail.field_mail_mail__has_error
#: model:ir.model.fields,help:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Tiene un error"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Cabeceras"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Hola"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Hola, "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__help_message
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Help message"
msgstr "Mensaje de ayuda"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Oculto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Hide Member List"
msgstr "Ocultar lista de miembros"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Ocultar el subtipo en las opciones del seguidor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Ocultar a los usuarios públicos / del portal, independientemente de la "
"configuración del subtipo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "History"
msgstr "Historial"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Cuánto tiempo permanecerá activa la emisión de audio tras superar el límite "
"de volumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "Servidores ICE"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "Servidor ICE"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.mail_channel_ice_servers_menu
msgid "ICE servers"
msgstr "Servidores ICE"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_channel__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID del registro padre que tiene el seudónimo. (ejemplo: el proyecto que "
"contiene el seudónimo para la creación de tareas)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id del recurso seguido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "Identity"
msgstr "Identidad"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Idle"
msgstr "Inactivo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_unread
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado hay nuevos mensajes que requieren su atención."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__send_mail
msgid ""
"If checked, the partners will receive an email warning they have been added "
"in the document's followers."
msgstr ""
"Si está marcado, las empresas recibirán un correo electrónico advirtiéndoles"
" de que han sido añadidos como seguidores del  documento."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked in the chatter. "
"Value is used to order tracking values."
msgstr ""
"Si se marca, cualquier modificación del campo será registrado en los "
"comentarios."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Si esta opción está activada, el administrador de tareas enviará el correo "
"electrónico después de la fecha. Si no está activada, el correo electrónico "
"se enviará lo antes posible. A menos que se especifique una zona horaria, se"
" considerará que está en la zona horaria UTC."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expressions "
"expression."
msgstr ""
"Si está opción está activada, el administrador de espera enviará el correo "
"electrónico después de la fecha. Si no se activada, el correo electrónico se"
" enviará lo antes posible. Puede utilizar la expresión de expresiones "
"dinámicas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
#: model:ir.model.fields,help:mail.field_mail_channel__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Si se configura, este contenido se enviará automáticamente a usuarios no "
"autorizados en lugar del mensaje predeterminado."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Si la dirección de email esta en la lista negra, el contacto ya no recibirá "
"correo masivo de cualquier lista."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Si está marcado como verdadero, las respuestas no se mostrarán en el hilo de"
" conversación del documento original. En su lugar, se comprobará el reply_to"
" en el seguimiento del message-id y se redireccionará como corresponde. Esto"
" tiene un impacto en el message-id generado."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Si tiene un dominio de correo electrónico general redireccionado a un "
"servidor de Odoo, ponga el nombre del dominio aquí."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_cancel_view_form
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red envelope next"
" to each message."
msgstr ""
"Si desea reenviarlos, haga clic en Cancelar ahora, luego haga clic en la "
"notificación y revíselos uno por uno haciendo clic en el sobre rojo junto a "
"cada mensaje."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "Si desea utilizar twilio como proveedor de servidores TURN/STUN"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all failures"
msgstr "Ignorar todas las fallas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#, python-format
msgid "Image"
msgstr "Imagen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "La imagen es un enlace"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Seudónimo inactivo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Bandeja de entrada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "Llamada entrante..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Indica que esta actividad ha sido creada automáticamente y no por ningún "
"usuario."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Info"
msgstr "Información"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Modelo inicial"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,help:mail.field_mail_mail__parent_id
#: model:ir.model.fields,help:mail.field_mail_message__parent_id
msgid "Initial thread message."
msgstr "Mensaje inicial del hilo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Input device"
msgstr "Dispositivo de entrada"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Integrations"
msgstr "Integraciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Solo interno"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Dirección de correo electrónico no válida"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Dirección de correo electrónico no válida %r"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Expresión invalida, debe ser un diccionario python, por ejemplo \"{'field': "
"'value'}\""

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "Campo de correo electrónico primario no válido en el modelo %s"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Valor no válido al crear un canal con miembros, solo se permiten 4 o 6"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "Valor no válido al crear un canal con membresías, solo se permite 0."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invitation Link"
msgstr "Enlace de invitación"

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Invitación para seguir %(document_model)s: %(document_name)s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Invite Follower"
msgstr "Invitar seguidor"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Invite people"
msgstr "Invitar gente"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to Channel"
msgstr "Invitar a canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/channel_invitation_form/channel_invitation_form.js:0
#, python-format
msgid "Invite to group chat"
msgstr "Invitar a chat grupal"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Asistente de invitación"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__private
msgid "Invited people only"
msgstr "Personas invitadas solamente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "Está Activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "Es un usuario o autor invitado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_member
msgid "Is Member"
msgstr "Es miembro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Es leído"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__is_chat
msgid "Is a chat"
msgstr "Es un chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr ""
"¿Está abierto el canal de la categoría de la barra lateral de "
"conversaciones?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr ""
"¿Está abierto el chat de la categoría de la barra lateral de conversaciones?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Está silenciado el micrófono"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__is_pinned
msgid "Is pinned on the interface"
msgstr "Está fijada en la interfaz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "Está enviando un video de usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "Está compartiendo pantalla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "Issue with audio"
msgstr "Problema con el audio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
msgid "Join"
msgstr "Unirse"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Call"
msgstr "Unirse a llamada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Join Channel"
msgstr "Unirse al canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Join Video Call"
msgstr "Unirse a videollamada"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_action_view
msgid "Join a group"
msgstr "Unirse a un grupo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "EN VIVO"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Idioma"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__fetched_message_id
msgid "Last Fetched"
msgstr "Última vez recuperado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__last_interest_dt
msgid "Last Interest"
msgstr "Último interés"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity____last_update
#: model:ir.model.fields,field_description:mail.field_mail_activity_type____last_update
#: model:ir.model.fields,field_description:mail.field_mail_alias____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist____last_update
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session____last_update
#: model:ir.model.fields,field_description:mail.field_mail_compose_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_followers____last_update
#: model:ir.model.fields,field_description:mail.field_mail_guest____last_update
#: model:ir.model.fields,field_description:mail.field_mail_ice_server____last_update
#: model:ir.model.fields,field_description:mail.field_mail_mail____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction____last_update
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype____last_update
#: model:ir.model.fields,field_description:mail.field_mail_notification____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_message____last_update
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner____last_update
#: model:ir.model.fields,field_description:mail.field_mail_shortcode____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template____last_update
#: model:ir.model.fields,field_description:mail.field_mail_template_preview____last_update
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value____last_update
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings____last_update
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_last_seen_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__seen_message_id
msgid "Last Seen"
msgstr "Ultima vez visto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Última actualización el"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/systray.xml:0
#, python-format
msgid "Late"
msgstr "Retrasado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Actividades tardías"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__layout
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Diseño"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_kanban
#, python-format
msgid "Leave"
msgstr "Ausencia"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Leave this channel"
msgstr "Abandonar este canal"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_cc__email_cc
msgid "List of cc from incoming emails."
msgstr "Listado de cc en emails entrantes."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__partner_ids
msgid ""
"List of partners that will be added as follower of the current document."
msgstr ""
"Lista de empresas que serán añadidas como seguidoras del documento actual."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Listado actual de usuarios en este canal"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_partner
msgid "Listeners of a Channel"
msgstr "Seguidores de un canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Load more"
msgstr "Cargar mas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/components/thread_view/thread_view.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Loading..."
msgstr "Cargando…"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Registro"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log a note"
msgstr "Registrar una nota"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Log a note..."
msgstr "Registrar una nota..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Log an Activity"
msgstr "Registrar una actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_log
msgid "Log an Internal Note"
msgstr "Registrar una nota interna"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Log an internal note..."
msgstr "Registrar una nota interna…"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Log in the original discussion thread"
msgstr "Registrar en el hilo de la conversación original"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Log note"
msgstr "Poner una nota"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.js:0
#, python-format
msgid "Logged in as %s"
msgstr "Iniciada sesión como %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Actividad de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Tipo de actividad del correo electrónico"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Lista negra de correo "

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Mail Blacklist mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Mail Channel Form"
msgstr "Formulario de canal de correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Mixin del redactor de correo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Mail Failures"
msgstr "Fallos de correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_channel_rtc_session
msgid "Mail RTC session"
msgstr "Sesión de comunicación en tiempo real por correo"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mail Render Mixin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Editor de plantillas de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Hilo de mensajes"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Valor de seguimiento de correo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Se ha creado un correo electrónico para notificar a la gente de un mensaje "
"existente"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_mail_scheduler_action
#: model:ir.cron,name:mail.ir_cron_mail_scheduler_action
msgid "Mail: Email Queue Manager"
msgstr "Mail: Gestor de colas de email"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Bandeja no disponible - %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#, python-format
msgid "Mailboxes"
msgstr "Buzones de correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Correos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_partner__message_main_attachment_id
#: model:ir.model.fields,field_description:mail.field_res_users__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Administrar mensajes"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Gestione respuestas como correos electrónicos entrantes nuevos en lugar de "
"respuestas al mismo hilo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#: code:addons/mail/static/src/components/activity/activity.xml:0
#, python-format
msgid "Mark Done"
msgstr "Marcar como hecho"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Mark all read"
msgstr "Marcar todo como leido"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Mark as Done"
msgstr "Marcar como Hecho"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Mark as Read"
msgstr "Marcar como leido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#, python-format
msgid "Mark as Todo"
msgstr "Marcar 'Por realizar'"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Mark as done"
msgstr "Marcar como hecho"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Reunión"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__member_count
msgid "Member Count"
msgstr "Número de miembros"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__channel_partner_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Members"
msgstr "Miembros"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Los miembros de estos grupos se añadirán automáticamente como seguidores. "
"Tenga en cuenta que podrán gestionar su suscripción manualmente si es "
"necesario."

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Asistente de fusionado de empresas"

#. module: mail
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Combinada con las siguientes empresas:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message #%s..."
msgstr "Mensaje #%s..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Message %s..."
msgstr "Mensaje %s..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID del mensaje"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Notificaciones de mensajes"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Reacción de mensaje"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Reacciones de mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Nombre de registro del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Tipo de mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "Message delivery failure image"
msgstr "Imagen de error de entrega de mensajes"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr ""
"Descripción del mensaje: o bien el asunto, o el comienzo del cuerpo del "
"mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "Mensaje publicado en \"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Destinatarios del mensaje (correos electrónicos)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr ""
"Referencias del mensaje, tales como identificadores de mensajes anteriores"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "El mensaje debe ser una instancia de EmailMessage válida"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"El subtipo de mensaje da un tipo más preciso en el mensaje, especialmente "
"para los sistema de notificación. Por ejemplo, puede ser una notificación "
"relativa a un nuevo registro (Nuevo), o a un cambio de etapa en un proceso "
"(Cambio de etapa). Los subtipos de mensaje permiten afinar las "
"notificaciones que los usuarios quieren recibir en su muro."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Subtipos de mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Subtipos de mensaje seguidos. Serán los subtipos que serán llevados al muro "
"del usuario."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Tipo de mensaje: email para mensajes de correo, notification para mensajes "
"de sistema, comment para otros tipos de mensaje como respuestas de usuarios."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Identificador único del mensaje"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Id del mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Mensajes"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Buscar mensajes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Messages can be <b>starred</b> to remind you to check back later."
msgstr ""
"Los mensajes pueden ser <b>destacados</b> para que recuerde revisarlos más "
"tarde."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "Los mensajes marcados como leídos aparecerán en el historial."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Mensajes con subtipos internos solo visibles por los empleados, conocidos "
"también como a miembros del grupo base_user"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "No se pueden modificar los mensajes con valores de seguimiento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Minimum activity for voice detection"
msgstr "Actividad mínima para la detección de voz"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Correo faltante"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email addresss"
msgstr "Dirección de correo electrónico faltante"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model:ir.model.fields,field_description:mail.field_mail_resend_cancel__model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Model"
msgstr "Modelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "El modelo ha cambiado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Modelo del recurso seguido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Modelo al que aplica el subtipo. Si no está establecido, este subtipo aplica"
" a todos los modelos."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modelos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"La modificación del modelo puede tener un impacto en las actividades "
"existentes que utilizan este tipo de actividad, tenga cuidado."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Desinstalar módulo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Meses"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "More"
msgstr "Más"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Mute"
msgstr "Silenciar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mi fecha límite de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_channel__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
msgid "Name"
msgstr "Nombre"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__record_name
#: model:ir.model.fields,help:mail.field_mail_mail__record_name
#: model:ir.model.fields,help:mail.field_mail_message__record_name
msgid "Name get of the related document."
msgstr "Nombre obtenido del documento relacionado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__report_name
msgid ""
"Name to use for the generated report file (may contain placeholders)\n"
"The extension can be omitted and will then come from the report type."
msgstr ""
"Nombre a usar para el archivo del informe generado (se pueden utilizar aquí expresiones de campos)\n"
"La extensión puede ser omitida y se rellenará automáticamente desde el tipo de informe."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model:ir.model.fields,help:mail.field_mail_mail__needaction
#: model:ir.model.fields,help:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Requiere acción"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "New Channel"
msgstr "Nuevo canal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Nuevo valor de carácter"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nuevo valor de fecha"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nuevo valor de coma flotante"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nuevo valor entero"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_monetary
msgid "New Value Monetary"
msgstr "Nuevo valor monetario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Nuevo valor de texto"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#: code:addons/mail/static/src/models/chat_window/chat_window.js:0
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "New message"
msgstr "Nuevo mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages"
msgstr "Nuevo mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Los mensajes nuevos se muestran aquí."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Nuevos valores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Next (Right-Arrow)"
msgstr "Siguiente (flecha derecha)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Siguientes actividades"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#, python-format
msgid "Next Activity"
msgstr "Actividad siguiente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente plazo de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Siguientes actividades disponibles"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "No hay error"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "No IM status available"
msgstr "No hay estado de IM disponible"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "No hay registro"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No channel found"
msgstr "No se encontró el canal"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "No se seleccionó ninguna conversación."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_list/notification_list.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Ninguna conversación aún..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No history messages"
msgstr "No hay mensajes de historial"

#. module: mail
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "No se encontró message_id en contexto"

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "No se encontró destinatario."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "No starred messages"
msgstr "No hay mensajes destacados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "No hay un hilo para las respuestas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/webclient/commands/mail_providers.js:0
#, python-format
msgid "No user found"
msgstr "No se encontró el usuario"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "No se encontró un usuario que no sea miembro de este canal."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "No users found"
msgstr "No se encontraron usuarios"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
msgid "None"
msgstr "Ninguno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Correo electrónico normalizado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Nota"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Correo electrónico de notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Tipo de notificación"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_delete_notification
#: model:ir.cron,name:mail.ir_cron_delete_notification
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "Notificación: Eliminar notificaciones de más de 6 meses"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Notificaciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__notify
msgid "Notify followers"
msgstr "Notificar a los seguidores"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__notify
msgid "Notify followers of the document (mass post only)"
msgstr "Notificar a los seguidores del documento (sólo para envíos masivos)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Número de días/semana/mes antes de ejecutar la acción. Permite planificar el"
" plazo de actuación."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,help:mail.field_res_users__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_alert/notification_alert.xml:0
#, python-format
msgid ""
"Odoo Push notifications have been blocked. Go to your browser settings to "
"allow them."
msgstr ""
"Se han bloqueado las notificaciones push de Odoo. Vaya a la configuración de"
" su navegador para permitirlos."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid ""
"Odoo will not have the permission to send native notifications on this "
"device."
msgstr ""
"Odoo no tendrá permisos para enviar notificaciones nativas en este "
"dispositivo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Offline"
msgstr "Desconectado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_delete_confirm_dialog/attachment_delete_confirm_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Aceptar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Valor de carácter antiguo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Valor de fecha y hora antiguo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Valor decimal antiguo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Valor antiguo del entero"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_monetary
msgid "Old Value Monetary"
msgstr "Valor monetario antiguo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Texto de valor antiguo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Valores antiguos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid ""
"Once a message has been starred, you can come back and review it at any time"
" here."
msgstr ""
"Una vez que un mensaje ha sido destacado, puede volver y revisarlo en "
"cualquier momento aquí."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_unique_user_id
msgid "One user should only have one mail user settings."
msgstr "Un usuario solo debe tener una configuración de usuario de correo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "Online"
msgstr "En línea"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "Solo los administradores pueden exportar mensajes de correo"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to use grouped read on message model"
msgstr ""
"Solo los administradores pueden utilizar la función de confirmación de "
"lectura grupal en los mensajes"

#. module: mail
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Solo los modelos personalizados pueden ser modificados."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only logged notes can have their content updated on model '%s'"
msgstr ""
"Solo las notas registradas pueden tener su contenido actualizado en el "
"modelo'%s'"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr ""
"Solo los mensajes de tipo comentario pueden tener su contenido actualizado"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'mail.channel'"
msgstr ""
"Solo los mensajes de tipo comentario pueden tener su contenido actualizado "
"en el modelo 'mail.channel'"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Only users belonging to the \"%s\" group can modify dynamic templates."
msgstr ""
"Solo los usuarios pertenecientes al grupo \"%s\" pueden modificar las "
"plantillas dinámicas."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_channel_partner__fold_state__open
msgid "Open"
msgstr "Abierto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Document"
msgstr "Abrir documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_form
msgid "Open Parent Document"
msgstr "Abrir el documento padre"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "Open chat"
msgstr "Abrir chat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_activity_notice/rtc_activity_notice.xml:0
#, python-format
msgid "Open conference:"
msgstr " Abrir conferencia:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Open in Discuss"
msgstr "Abrir en Conversaciones"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Open profile"
msgstr "Abrir perfil"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Canceló"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Id. opcional de un hilo (registro) al que todos los mensajes entrantes serán"
" adjuntados, incluso si no fueron respuestas del mismo. Si se establece, se "
"deshabilitará completamente la creación de nuevos registros."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""
"ID de mail_mail opcional. Se utiliza principalmente para optimizar "
"búsquedas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Servidor de salida preferido (opcional). Si no está establecido, se usará el"
" de mayor prioridad."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template
msgid "Optional report to print and attach"
msgstr "Informe opcional a generar y adjuntar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__null_value
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_render_mixin__null_value
#: model:ir.model.fields,help:mail.field_mail_template__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Valor opcional a usar si el campo objetivo está vacío"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Discusión original: Las respuestas se dirigen al hilo de discusión del documento original.\n"
"Otra dirección de correo electrónico: Las respuestas se dirigen a la dirección de correo electrónico mencionada en el mensaje de seguimiento en lugar de al hilo de discusión del documento original.\n"
"Esto tiene un impacto en el mensaje generado."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_in_reply_to_view/message_in_reply_to_view.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "Se eliminó el mensaje original"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Saliente"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Servidor de correo saliente"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Correos salientes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Servidor de correos salientes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Vencidas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Sobreescribir correo electrónico del autor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_user_id
msgid "Owner"
msgstr "Propietario"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "PDF file"
msgstr "Archivo PDF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Principal"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Mensaje padre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_model_id
msgid "Parent Model"
msgstr "Modelo padre"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID del hilo del registro padre"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Modelo padre que contiene el alias. El modelo que contiene la referencia "
"alias no es necesariamente el modelo dado por alias_model_id (example: "
"project (parent_model) and task (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Subtipo padre, utilizado para la subscripción automática. Este campo no esta"
" correctamente nombrado. Por ejemplo en un proyecto, el parent_id del "
"subtipo de proyecto se refiere a los subtipos de tareas relacionadas."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: mail
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Perfil de la empresa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
msgid "Partner Readonly"
msgstr "Partner Solo Lectura "

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "Asóciese con información adicional para reenviar correo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Asociados con acción requerida"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/delete_message_confirm_dialog/delete_message_confirm_dialog.xml:0
#, python-format
msgid ""
"Pay attention: The followers of this document who were notified by email "
"will still be able to read the content of this message and reply to it."
msgstr ""
"Ponga atención: Los seguidores de este documento a los que se les notificó "
"por correo electrónico todavía pueden leer el contenido de este mensaje y "
"responder."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_request/notification_request.js:0
#, python-format
msgid "Permission denied"
msgstr "Permiso denegado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Teléfono"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Llamada telefónica"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__copyvalue
#: model:ir.model.fields,field_description:mail.field_mail_template__copyvalue
msgid "Placeholder Expression"
msgstr "Expresión de marcador"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Planificado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_box/activity_box.xml:0
#, python-format
msgid "Planned activities"
msgstr "Actividades planificadas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Planificado en"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient/composer_suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "Por favor complete la información del cliente"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Por favor contáctenos en su lugar usando"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.js:0
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "Espere mientras se carga el archivo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_container/chatter_container.xml:0
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.xml:0
#, python-format
msgid "Please wait..."
msgstr "Por favor espere... "

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Política de gestión de las notificaciones:\n"
"- Manejar por correos electrónicos: las notificaciones son enviadas a su dirección de correo\n"
"- Manejar en Odoo: las notificaciones aparecen en su bandeja de entrada de Odoo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
#: model:ir.model.fields,help:mail.field_mail_channel__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Política para publicar un mensaje en el documento utilizando el servidor de correo.\n"
"- todo el mundo: todos pueden publicar\n"
"- socios: sólo socios autenticados\n"
"- seguidores: sólo seguidores del documento relacionado o miembros de los siguientes canales\n"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "Acceso al portal otorgado"

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "Acceso al portal revocado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_post
msgid "Post on Multiple Documents"
msgstr "Publicar en varios documentos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "Publicar en un documento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/tours/mail.js:0
#, python-format
msgid "Post your message on the thread"
msgstr "Publica tu mensaje en el hilo"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"La publicación de un mensaje debe hacerse en un documento comercial. Utilice"
" message_notify para enviar una notificación a un usuario."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message with channels as listeners is not supported since Odoo "
"14.3+. Please update code accordingly."
msgstr ""
"Publicar un mensaje con canales como oyentes no se puede hacer desde Odoo "
"14.3+. Actualice el código como corresponde."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_borders
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Powered by"
msgstr "Con tecnología de"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Actividad precedente"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Dirección de respuesta preferida"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut"
msgstr "Presione una clave para registrar como atajo para pulsar para hablar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Previsualizar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Vista previa de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Previous (Left-Arrow)"
msgstr "Anterior (flecha izquierda)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Tipo de actividad previo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Print"
msgstr "Imprimir"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__public
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Privacy"
msgstr "Privacidad"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Private channel"
msgstr "Canal privado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category/discuss_sidebar_category.js:0
#, python-format
msgid "Public Channels"
msgstr "Canales públicos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#, python-format
msgid "Public channel"
msgstr "Canal público"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Contrato de garantía del editor"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
#: model:ir.cron,cron_name:mail.ir_cron_module_update_notification
#: model:ir.cron,name:mail.ir_cron_module_update_notification
msgid "Publisher: Update Notification"
msgstr "Editor: Actualizar la notificación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Atajo de la función pulsar para hablar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "Clave de la función pulsar para hablar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Quick search..."
msgstr "Búsqueda rápida..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "Sesión de comunicación en tiempo real"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_session_ids
msgid "RTC Sessions"
msgstr "Sesiones de comunicación en tiempo real"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.mail_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "Sesiones de comunicación de tiempo real"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""
"Rangos entre 0.0 y 1.0, la escala depende de la implementación del "
"navegador."

#. module: mail
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Re:"
msgstr "Re:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Reacción del invitado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Reacción del partner"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reaction"
msgstr "Reacción"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
msgid "Reactions"
msgstr "Reacciones"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Fecha de lectura"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#, python-format
msgid "Ready"
msgstr "Preparado"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Listo para enviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Motivo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Recibido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s"
msgstr "Recibido por %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s and %s"
msgstr "Recibido por %s y %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by %s, %s and more"
msgstr "Recibido por %s, %s y más"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Received by Everyone"
msgstr "Recibido por todos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Destinatario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Destinatarios"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Tipo recomendado de actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Registro"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
#: model:ir.model.fields,field_description:mail.field_mail_channel__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Id. del hijo de registro"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Registros:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Redirect to another email address"
msgstr "Redireccionar a otra dirección de correo electrónico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referencias"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#: code:addons/mail/static/src/components/rtc_invitation_card/rtc_invitation_card.xml:0
#, python-format
msgid "Refuse"
msgstr "Rechazar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Saludos, "

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Register new key"
msgstr "Registrar nueva clave"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Reject"
msgstr "Rechazado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Empresa relacionada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "ID del documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Modelo de documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Nombre del modelo del documento relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Plantilla de correo relacionada"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Mensaje relacionado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Contacto relacionada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Campo relación"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Remove"
msgstr "Eliminar"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Asistente para eliminar correo electrónico de la lista negra"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Borrar la acción conceptual para usar esta plantilla en documentos "
"relacionados."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower/follower.xml:0
#: code:addons/mail/static/src/components/follower/follower.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Eliminar este seguidor"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "Modelo de renderización"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Respuestas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: code:addons/mail/static/src/components/message_action_list/message_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Responder"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Responder A"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Dirección de respuesta de los correos. Estableciendo este campo, se saltará "
"la creación automática de hilos."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Responder a"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "Respondiendo a"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_name
msgid "Report Filename"
msgstr "Fichero del informe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Socio solicitante"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users__res_users_settings_ids
msgid "Res Users Settings"
msgstr "Ajustes del recurso del usuario"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Resend mail"
msgstr "Reenviar correo electrónico"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Resend to selected"
msgstr "Reenviar a seleccionados"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Asistente de reenvio"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Reset Zoom"
msgstr "Restablecer zoom"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Reset Zoom (0)"
msgstr "Restablecer zoom (0)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Limitar la creación de plantillas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates and Jinja rendering."
msgstr "Limitar las plantillas de correo y la creación por Jinja."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""
"Restringir la edición de plantillas de correo y el uso de marcadores de la "
"posición QWEB."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Reintentar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Rich-text Contents"
msgstr "Contenido con texto enriquecido"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Mensaje en Texto enriquecido/HTML"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel_partner__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Sesión de llamadas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Rotate"
msgstr "Rotar"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Rotate (r)"
msgstr "Girar (r)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Sesión de comunicación en tiempo real"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "Servidor SMTP"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Comercial"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Save"
msgstr "Guardar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Guardar como plantilla nueva"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as new template"
msgstr "Grabar como nueva plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Planificacion"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/activity/activity.js:0
#: code:addons/mail/static/src/models/chatter/chatter.js:0
#, python-format
msgid "Schedule Activity"
msgstr "Planificación de actividad"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "Programe actividades que le ayuden a hacer las cosas."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Planificar actividad"

#. module: mail
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Planificar una actividad"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Planificar una actividad"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Fecha prevista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Fecha planeada de envió"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
msgid "Search Alias"
msgstr "Buscar seudónimo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_search
msgid "Search Groups"
msgstr "Grupos de busqueda"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "Buscar sesión de comunicación en tiempo real"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.js:0
#: code:addons/mail/static/src/components/discuss/discuss.js:0
#: code:addons/mail/static/src/components/messaging_menu/messaging_menu.js:0
#, python-format
msgid "Search user..."
msgstr "Buscar usuario..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/views/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "Buscar: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s"
msgstr "Visto por %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s and %s"
msgstr "Visto por %s y %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by %s, %s and more"
msgstr "Visto por %s, %s y más"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_seen_indicator/message_seen_indicator.js:0
#, python-format
msgid "Seen by Everyone"
msgstr "Visto por todos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#: code:addons/mail/static/src/js/m2x_avatar_user.js:0
#, python-format
msgid "Select a user..."
msgstr "Seleccionar usuario"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Seleccione el campo objetivo del modelo de documentado relacionado.\n"
"Si es un campo de asociación, podrá seleccionar un campo objetivo en el destino de la asociación."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid ""
"Select the action to do on each mail and correct the email address if "
"needed. The modified address will be saved on the corresponding contact."
msgstr ""
"Seleccione la acción a realizar en cada correo y corrija la dirección de "
"correo electrónico si es necesario. La dirección modificada se guardará en "
"el contacto correspondiente."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_icon/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_channel__public__groups
#, python-format
msgid "Selected group of users"
msgstr "Grupo de usuarios seleccionado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Selected users:"
msgstr "Usuarios seleccionados:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/composer_view/composer_view.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Enviar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Send Again"
msgstr "Enviar de nuevo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__send_mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__email
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Enviar mail (%s)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Enviar ahora"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send a message"
msgstr "Enviar un mensaje"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_text_input/composer_text_input.js:0
#, python-format
msgid "Send a message to followers..."
msgstr "Enviar un mensaje a los seguidores ..."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Enviar correo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chatter_topbar/chatter_topbar.xml:0
#, python-format
msgid "Send message"
msgstr "Enviar mensaje"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Dirección del remitente"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Dirección del remitente (se pueden usar expresiones de campo aquí). Si no se"
" establece, el valor por defecto será el alias de correo del autor si está "
"configurado, o la dirección de correo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/notification_popover/notification_popover.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Enviado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Acción de servidor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "Establezca activo en falso para ocultar el canal sin eliminarlo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_option_list/rtc_option_list.xml:0
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "Settings"
msgstr "Ajustes"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Share screen"
msgstr "Compartir pantalla"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift left"
msgstr "Desplazar a la izquierda"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.js:0
#, python-format
msgid "Shift right"
msgstr "Cambiar a la derecha"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "Descripción corta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Codigos cortos"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Acceso rápido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show All"
msgstr "Mostrar todo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follower_list_menu/follower_list_menu.xml:0
#, python-format
msgid "Show Followers"
msgstr "Mostrar seguidores"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Show Member List"
msgstr "Mostrar lista de miembros"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Show a helper message"
msgstr "Mostrar un mensaje de ayuda"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "Mostrar menos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer_suggested_recipient_list/composer_suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "Mostrar más"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Show only video"
msgstr "Mostrar solo video"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Showing"
msgstr "Mostrando"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Sidebar"
msgstr "Barra lateral"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Acción barra lateral"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Acción de la barra lateral para hacer disponible esta plantilla en los "
"registros del modelo de documento relacionado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Origen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Usuario específico"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Especificar un modelo si la actividad debería ser específica para un modelo "
"y no estar disponible cuando se gestionan actividades para otros modelos."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Spotlight"
msgstr "Foco de atención"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Destacado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Call"
msgstr "Iniciar llamada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Start a Video Call"
msgstr "Iniciar videollamada"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss/discuss.xml:0
#: code:addons/mail/static/src/models/messaging_initializer/messaging_initializer.js:0
#, python-format
msgid "Start a conversation"
msgstr "Iniciar una conversación"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#: code:addons/mail/static/src/components/discuss_sidebar/discuss_sidebar.xml:0
#, python-format
msgid "Start a meeting"
msgstr "Iniciar reunión"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_rtc_session_view_form
msgid "State"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Estado"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window_header/chat_window_header.xml:0
#, python-format
msgid "Stop adding users"
msgstr "Dejar de añadir usuarios"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop camera"
msgstr "Desactivar cámara"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "Dejar de responder"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Stop screen sharing"
msgstr "Dejar de compartir pantalla"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Cadena con formato que representa una tecla con modificadores que siguen "
"este patrón: shift.ctrl.alt.key, por ejemplo: truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_model_object_field
msgid "Sub-field"
msgstr "Sub-campo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,field_description:mail.field_mail_template__sub_object
msgid "Sub-model"
msgstr "Sub-modelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Asunto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Subject (placeholders may be used here)"
msgstr "Asunto (se pueden utilizar aquí expresiones de campos)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Subject..."
msgstr "Asunto..."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Asunto:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Sustitución"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Subtipo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Subtipo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Sugerir"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Sugerir siguiente actividad"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""
"Sugiera estas actividades una vez que la actual esté marcada como hecha."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
msgid "Summary"
msgstr "Resumen"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Parámetro del sistema"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/message/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Notificación del sistema"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Modelo dirigido"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "Identificación fiscal"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__has_recommended_activities
msgid "Technical field for UX purpose"
msgstr "Campo técnico para fines UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model_change
msgid "Technical field for UX related behaviour"
msgstr "Campo técnico para el comportamiento relacionado con UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__can_write
msgid "Technical field to hide buttons if the current user has no access."
msgstr ""
"Campo técnico para ocultar botones si el usuario actual no tiene acceso."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Campo técnico para realizar un seguimiento del modelo al inicio de la "
"edición para respaldar el comportamiento relacionado con UX"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_field_name
msgid "Technical name of the user on the record"
msgstr "Nombre técnico del usuario en el registro"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Vista previa plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Idioma de vista previa de plantilla"

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering for language should be called with a list of IDs."
msgstr ""
"La representación de la plantilla para el idioma debe llamarse con una lista"
" de ID."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called on a valid record IDs."
msgstr "La creación de una plantilla tiene que hacerse en un registro válido."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Template rendering should be called only using on a list of IDs."
msgstr ""
"La representación de la plantilla debe invocarse solo con una lista de ID."

#. module: mail
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw)."
msgstr ""
"La creación de plantillas solo admite inline_template, qweb o qweb_view "
"(vista o en línea)."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Plantillas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Text file"
msgstr "Archivo de texto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "El"

#. module: mail
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "El valor 'Fecha de vencimiento' no puede ser negativo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc_call_viewer/rtc_call_viewer.js:0
#, python-format
msgid "The FullScreen mode was denied by the browser"
msgstr "El navegador denegó el modo de pantalla completa"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"El número de identificación de impuesto. Complételo si el contacto está "
"sujeto a los impuestos del gobierno. Utilizado en algunas declaraciones "
"legales."

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr ""
"El archivo adjunto %s no existe o usted no tiene los derechos para acceder a"
" él."

#. module: mail
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "El adjunto %s no existe."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "El UUID del canal debe ser único"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already linked with "
"%(alias_model_name)s. Choose another alias or change it on the linked model."
msgstr ""
"El seudónimo %(matching_alias_name)s del correo electrónico ya está "
"vinculado con %(alias_model_name)s. Escoja otro seudónimo o cámbielo en el "
"modelo vinculado."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used as "
"%(alias_duplicate)s alias. Please choose another alias."
msgstr ""
"%(alias_duplicate)s ya está usando el seudónimo %(matching_alias_name)s de "
"correo electrónico. Escoja otro."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The e-mail alias %(matching_alias_name)s is already used by the "
"%(document_name)s %(model_name)s. Choose another alias or change it on the "
"other document."
msgstr ""
"El seudónimo %(matching_alias_name)s  de correo electrónico ya lo está "
"usando  %(document_name)s %(model_name)s. Escoja otro o cambie el del otro "
"documento."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "El correo electrónico enviado a"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid "The escaped html code replacing the shortcut"
msgstr "El código html escapado que reemplaza el acceso directo"

#. module: mail
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid "The field %s does not exist on the model %s"
msgstr "El campo %s  no existe en el modelo %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "El usuario interno a cargo de este contacto."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"El modelo (Tipo de documento de Odoo) al que corresponde este seudónimo. "
"Cualquier correo entrante que no sea respuesta a un registro existente, "
"causará la creación de un nuevo registro de este modelo (por ejemplo, una "
"tarea de proyecto)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_channel__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"El nombre de este seudónimo de correo electrónico. Por ejemplo, "
"\"trabajos\",  si lo que quiere es obtener los correos para "
"<<EMAIL>>."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_user_id
#: model:ir.model.fields,help:mail.field_mail_channel__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"El propietario de los registros creados al recibir correos electrónicos en "
"este seudónimo. Si el campo no está establecido, el sistema tratará de "
"encontrar el propietario adecuado basado en la dirección del emisor (De), o "
"usará la cuenta de administrador si no se encuentra un usuario para esa "
"dirección."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr ""
"El administrador de colas enviará el correo electrónico después de la fecha"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"No se ha podido completar la operación por restricciones de seguridad. Contacte por favor con su administrador de sistema.\n"
"\n"
"(Tipo de documento: %s, Operación: %s)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid "The shortcut which must be replaced in the Chat Messages"
msgstr "El atajo que debe ser reemplazado en los mensajes de chat"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/follower/follower.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "Se han aplicado correctamente las preferencias de su suscripción."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__model_id
#: model:ir.model.fields,help:mail.field_mail_template_preview__model_id
msgid "The type of document this template can be used with"
msgstr "El tipo de plantilla de este documento puede ser usado con"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "No hay mensajes en esta conversación."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_channel_rtc_session_channel_partner_unique
msgid "There can only be one rtc session per channel partner"
msgstr ""
"Solo puede haber una sesión de comunicación en tiempo real por canal del "
"partner"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Este"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/activity_view.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Esta acción enviará un correo electrónico."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Este correo electrónico está en la lista de denegación para envíos masivos. "
"Haga clic para eliminarlo de ella."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Este campo no distingue entre mayúsculas y minúsculas."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Este campo se utiliza para buscar en la dirección de correo electrónico, ya "
"que el campo de correo electrónico principal puede contener más de una "
"dirección de correo electrónico."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_channel__public
msgid ""
"This group is visible by non members. Invisible groups can add members "
"through the invite button."
msgstr ""
"Este grupo es visible para los no miembros. Los grupos invisibles puede "
"añadir miembros a través del botón invitar."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "This is their first connection. Wish them luck."
msgstr "Esta es su primera conexión. Deséeles suerte."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Esta opción borra cualquier rastro del envío del correo, incluido el menú "
"Técnico en los Ajustes, para preservar el espacio de almacenamiento de su "
"base de datos Odoo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "This record has an exception activity."
msgstr "Este registro tiene una actividad de excepción."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Save the record before scheduling an activity!"
msgstr "¡Por favor guarde el registro antes de planificar una actividad!"

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "This user can not be added in this channel"
msgstr "No se puede añadir al usuario en este canal"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Hilo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#: code:addons/mail/static/src/components/thread_needaction_preview/thread_needaction_preview.xml:0
#: code:addons/mail/static/src/components/thread_preview/thread_preview.xml:0
#, python-format
msgid "Thread Image"
msgstr "Imagen de hilo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_layout_menu/rtc_layout_menu.xml:0
#, python-format
msgid "Tiled"
msgstr "Mosaico"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Zona horaria"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "Hasta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Para (correos electrónicos)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "A (empresas)"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To Do"
msgstr "Por hacer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/chat_window/chat_window.xml:0
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "To:"
msgstr "Para:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/js/views/activity/activity_renderer.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#: code:addons/mail/static/src/xml/systray.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Actividades de Hoy"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Today:"
msgstr "Hoy:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Tomorrow"
msgstr "Mañana"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Tomorrow:"
msgstr "Mañana:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Temas discutidos en este grupo..."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Los valores seguidos están almacenados en un modelo separado. Este campo "
"permite reconstruir el seguimiento y generar estadísticas en el modelo."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Seguimiento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Valor de seguimiento"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Valores de seguimiento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__tracking_sequence
msgid "Tracking field sequence"
msgstr "Seguimiento de secuencia de campo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Valores de seguimiento"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Activador"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Activar la siguiente actividad"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Turn camera on"
msgstr "Encender cámara"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Token de autentificación de cuenta de Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "SID de cuenta de Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tipo"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Tipo de retraso"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Execute Python Code': a block of python code that will be executed\n"
"- 'Create': create a new record with new values\n"
"- 'Update a Record': update the values of a record\n"
"- 'Execute several actions': define an action that triggers several other server actions\n"
"- 'Send Email': automatically send an email (Discuss)\n"
"- 'Add Followers': add followers to a record (Discuss)\n"
"- 'Create Next Activity': create an activity (Discuss)"
msgstr ""
"Tipo de acción de servidor. Están disponibles los siguientes valores:\n"
"- 'Ejecutar código Python': un bloque de código Python que será ejecutado\n"
"- 'Crear o duplicar un nuevo registro': crea un nuevo registro con nuevos valores, o duplica uno existente en la base de datos\n"
"- 'Actualizar un registro': actualiza los valores de un registro\n"
"- 'Ejecutar varias acciones': define una acción que lanza varias otras acciones de servidor\n"
"- 'Enviar un correo electrónico': envía automáticamente un correo electrónico (Conversaciones)\n"
"- 'Añadir seguidores': añade seguidores a un registro (Conversaciones)\n"
"- 'Crear próxima actividad': crea una actividad (Conversaciones)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "Escriba el nombre de la persona"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Imposible conectar con el servidor SMTP"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to log message, please configure the sender's email address."
msgstr ""
"No se ha podido registrar el mensaje, configure la dirección de correo "
"electrónico del remitente."

#. module: mail
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"No se puede publicar el mensaje, configure la dirección de correo "
"electrónico del remitente."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Quitar de la lista negra"

#. module: mail
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Unblacklisting Reason: %s"
msgstr "Motivo de la eliminación de la lista negra: %s"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Undeafen"
msgstr "Desactivar ensordecimiento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/follow_button/follow_button.xml:0
#, python-format
msgid "Unfollow"
msgstr "Dejar de seguir"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_alias_unique
msgid ""
"Unfortunately this email alias is already used, please choose a unique one"
msgstr ""
"Desafortunadamente, este seudónimo de correo ya está en uso. Por favor, "
"elija otro."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Unidad de retraso"

#. module: mail
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Error desconocido"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_controller/rtc_controller.xml:0
#, python-format
msgid "Unmute"
msgstr "Desactivar silencio"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category_item/discuss_sidebar_category_item.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "Desanclar conversación"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin leer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_channel__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_unread_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nº de mensajes sin leer"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Mensajes pendientes de leer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/thread_view_topbar/thread_view_topbar.xml:0
#, python-format
msgid "Unstar all"
msgstr "Desmarcar todos"

#. module: mail
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Tipo de informe desconocido %s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
#, python-format
msgid "Upload Document"
msgstr "Subir documento"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "Upload file"
msgstr "Subir archivo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#, python-format
msgid "Uploaded"
msgstr "Cargado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_card/attachment_card.xml:0
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "Uploading"
msgstr "Subiendo"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Generic User From Record' to specify the field name of the user to choose "
"on the record."
msgstr ""
"Use 'Usuario específico' para asignar siempre el mismo usuario en la "
"siguiente actividad. Use 'Usuario genérico del registro' para especificar el"
" nombre del campo del usuario para elegir en el registro."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "Use Push-to-talk"
msgstr "Usar pulsar para hablar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Usar los servicios ICE de Twilio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_active_domain
msgid "Use active domain"
msgstr "Usar el dominio activo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Usar plantilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Usar la función pulsar para hablar"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr ""
"Se utiliza para mostrar la divisa cuando se hace un seguimiento de los "
"valores monetarios"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Usado para ordenar los subtipos."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_alias_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Usuario"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Usuario conectado"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Ajustes de usuario"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Ajustes de usuario"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Ajustes de volumen del usuario"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Notificación específica del usuario"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User field name"
msgstr "Nombre del campo de usuario"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is a bot"
msgstr "El usuario es un bot"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is idle"
msgstr "Usuario está inactivo"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is offline"
msgstr "Usuario está desconectado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/partner_im_status_icon/partner_im_status_icon.xml:0
#: code:addons/mail/static/src/widgets/common.xml:0
#, python-format
msgid "User is online"
msgstr "Usuario está conectado"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Usuario:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Nombre de usuario"

#. module: mail
#: model:ir.model,name:mail.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "Usuarios en este canal: %(members)s %(dots)s y tú."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Los usuarios aún podrán visualizar las plantillas.Sin embargo, solo los "
"editores de plantillas de correo podrán crear nuevas plantillas dinámicas o "
"modificar las existentes."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"El uso de un servidor propio de correo electrónico es necesario para "
"enviar/recibir correos en las versiones Community y Enterprise. Los usuarios"
" de la versión Online si tienen disponible un servidor de correo listo para "
"usar (@mycompany.odoo.com)."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"No es posible validar el valor de `mail.catchall.domain.allowed`.\n"
"Debe ser una lista de dominios separada por comas, por ejemplo: ejemplo.com,ejemplo.org."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Video"
msgstr "Video"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
#, python-format
msgid "View"
msgstr "Ver"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Ver %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Tipo de vista"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_image/attachment_image.xml:0
#, python-format
msgid "View image"
msgstr "Ver imagen"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/discuss_sidebar_category/discuss_sidebar_category.xml:0
#, python-format
msgid "View or join channels"
msgstr "Ver o unirse a canales"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Viewer"
msgstr "Visor"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Voz"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Volumen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "Volumen por partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Volúmenes de otros partners"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#, python-format
msgid "Warning"
msgstr "Alerta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Semanas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "What's your name?"
msgstr "¿Cuál es su nombre?"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_model_object_field
#: model:ir.model.fields,help:mail.field_mail_template__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Cuando se selecciona un campo de relación como primer campo, este campo "
"permite establecer el campo objetivo del modelo de documento destino (sub-"
"modelo)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__sub_object
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_render_mixin__sub_object
#: model:ir.model.fields,help:mail.field_mail_template__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Cuando un campo de asociación se selecciona como primer campo, este campo "
"muestra el modelo de documento al que apunta la relación."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__is_log
msgid "Whether the message is an internal note (comment mode only)"
msgstr "Si el mensaje es una nota interna (sólo en modo de comentario)"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_activity
msgid "Whether this model supports activities."
msgstr "Si este modelo soporta actividades."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_blacklist
msgid "Whether this model supports blacklist."
msgstr "Si este modelo es compatible con la lista negra."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model__is_mail_thread
msgid "Whether this model supports messages and notifications."
msgstr "Si este modelo soporta mensajería y notificaciones."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "Who can follow the group's activities?"
msgstr "Quien puede seguir las actividades del grupo?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity_mark_done_popover/activity_mark_done_popover.xml:0
#: code:addons/mail/static/src/xml/activity.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Escribir comentarios"

#. module: mail
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "Nombre de operación erróneo (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "AAAA-MM-DD HH:MM:SS"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/activity.js:0
#: code:addons/mail/static/src/models/message/message.js:0
#, python-format
msgid "Yesterday"
msgstr "Ayer"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "Yesterday:"
msgstr "Ayer:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Está a punto de dejar esta conversación grupal y no volverá a tener acceso a"
" menos que lo vuelvan a invitar. ¿Está seguro de que desea continuar?"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Está solo en este canal."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in a private conversation with <b>@%s</b>."
msgstr "Esta en una conversación privada con <b>@%s</b>."

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "You are in channel <b>#%s</b>."
msgstr "Esta en el canal <b>#%s</b>."

#. module: mail
#: code:addons/mail/controllers/discuss.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "No está permitido para usted subir un archivo adjunto aquí."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/discuss_sidebar_category_item/discuss_sidebar_category_item.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "Eres el administrador de este canal. ¿Estás seguro que quieres irte?"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_list/message_list.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Puede marcar cualquier mensaje como 'destacado', y se muestra en este buzón."

#. module: mail
#: code:addons/mail/models/mail_channel_partner.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "No puede escribir en %(field_name)s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "Solo puede chatear con usuarios existentes."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/partner/partner.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr "Solo puede chatear con socios que tengan un usuario dedicado."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging/messaging.js:0
#, python-format
msgid "You can only open the profile of existing channels."
msgstr "Solo puede abrir el perfil de los canales existentes."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/user/user.js:0
#, python-format
msgid "You can only open the profile of existing users."
msgstr "Solo puede abrir el perfil de usuarios existentes."

#. module: mail
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"You cannot create a new user from here.\n"
" To create new user please go to configuration panel."
msgstr ""
"No puede crear un nuevo usuario desde aquí.\n"
"Para crear un nuevo usuario, vaya al panel de configuración"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"No puede borrar estos grupos, ya que el grupo \"Toda la compañía\" es "
"requerido por otros módulos."

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address (%s)."
msgstr ""
"No puede usar otra cosa más que caracteres latinos sin acento en la "
"dirección del seudónimo  (%s)."

#. module: mail
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"No tiene derecho a acceder a la lista negra de correos electrónicos. Por "
"favor, póngase en contacto con su administrador."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Ha sido asignado a %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Has sido asignado al"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "Lo han invitado a #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Puede adjuntar archivos a esta plantilla, para ser añadidos a todos los "
"e-mails creados desde ella."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unpinned your conversation with %s."
msgstr "Desancló su conversación con %s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/messaging_notification_handler/messaging_notification_handler.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "Canceló su suscripción a %s."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "¡Lo han invitado a un chat!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "¡Lo han invitado a una reunión!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message_author_prefix/message_author_prefix.xml:0
#, python-format
msgid "You:"
msgstr "Usted:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_paynow
msgid "Your"
msgstr "Su"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/media_preview/media_preview.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "Su navegador no permite las videoconferencias."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/rtc/rtc.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "Su navegador no permite la activación por voz"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/models/thread/thread.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "Su navegador no permite webRTC"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/welcome_view/welcome_view.xml:0
#, python-format
msgid "Your name"
msgstr "Su nombre"

#. module: mail
#: code:addons/mail/controllers/home.py:0
#, python-format
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"¡Tu contraseña es la predeterminada (admin)! Si este sistema está expuesto a"
" usuarios no confiables, es importante cambiarlo de inmediato por razones de"
" seguridad. ¡Te seguiré molestando al respecto!"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom In"
msgstr "Acercarse"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom In (+)"
msgstr "Acercar (+)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/thread.xml:0
#: code:addons/mail/static/src/xml/thread.xml:0
#, python-format
msgid "Zoom Out"
msgstr "Alejarse"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/attachment_viewer/attachment_viewer.xml:0
#, python-format
msgid "Zoom Out (-)"
msgstr "Disminuir el zoom (-)"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "direcciones vinculadas a partners registrados"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "después de la fecha de finalización"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "después de la fecha límite de actividad anterior"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "assigned you an activity"
msgstr "le asignó una actividad"

#. module: mail
#: model:mail.channel,name:mail.channel_2
msgid "board-meetings"
msgstr "tablero de citas"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "bounce"
msgstr "devolver"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "by"
msgstr "por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "cancel"
msgstr "cancelar"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"no puede ser procesado. Esta dirección\n"
"    se utiliza para recopilar respuestas y no se debe utilizar para contactar directamente"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "catchall"
msgstr "catchall"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "channel"
msgstr "canal"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "días"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "ensordecido"

#. module: mail
#. openerp-web
#: code:addons/mail/models/mail_thread.py:0
#: code:addons/mail/static/src/components/message/message.xml:0
#, python-format
msgid "document"
msgstr "documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "done"
msgstr "hecho"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. \"mycompany.com\""
msgstr "ej. \"miempresa.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "ej. 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "ej. ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Calendar: Reminder"
msgstr "ej. Calendario: Recordatorio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "Por ejemplo, conversación de una propuesta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "ej. agendar una reunión"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Users"
msgstr "ej. usuarios"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_channel_view_form
msgid "e.g. support"
msgstr "ej. soporte"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "ej. true.true..f"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "escape to"
msgstr "Esc para"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/activity/activity.js:0
#, python-format
msgid "for %s"
msgstr "para %s"

#. module: mail
#: model:mail.channel,name:mail.channel_all_employees
msgid "general"
msgstr "general"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "ha sido creado a partir de:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "ha sido modificado de:"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "alias configurado incorrectamente"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "alias configurado incorrectamente (registro de referencia incorrecto)"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "en vivo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support model and res_id parameters anymore. Please "
"call message_post on record."
msgstr ""
"message_post ya no admite los parámetros model y res_id. Solicite "
"message_post en el registro."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"message_post does not support subtype parameter anymore. Please give a valid"
" subtype_id or subtype_xmlid value instead."
msgstr ""
"message_post ya no admite el parámetro subtype. Indique un valor subtype_id "
"o subtype_xmlid válido para sustituirlo."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "message_post partner_ids and must be integer list, not commands."
msgstr ""
"message_post partner_ids y debe ser una lista de enteros, no de comandos."

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "modelo %s no acepta la creación de documentos"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "meses"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#: code:addons/mail/static/src/components/rtc_call_participant_card/rtc_call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "silenciado"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/js/utils.js:0
#, python-format
msgid "now"
msgstr "ahora"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "de"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "on:"
msgstr "en:"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/mail_template/mail_template.xml:0
#: code:addons/mail/static/src/xml/web_kanban_activity.xml:0
#, python-format
msgid "or"
msgstr "o"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_member_list/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "otros miembros."

#. module: mail
#: model:mail.channel,name:mail.channel_3
msgid "rd"
msgstr "rd"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read less"
msgstr "leer menos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/message/message.js:0
#, python-format
msgid "read more"
msgstr "leer más"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "record:"
msgstr "registro:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"responder al documento faltante (%(model)s,%(thread)s), recurrir a la "
"creación del documento"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"responder al modelo %s que no acepta la actualización del documento, "
"retroceda en la creación de documentos"

#. module: mail
#: code:addons/mail/models/mail_channel.py:0
#, python-format
msgid "restricted to channel members"
msgstr "Restringido a miembros del canal"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "Restringido a seguidores"

#. module: mail
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "restringido a autores conocidos"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/channel_invitation_form/channel_invitation_form.xml:0
#, python-format
msgid "results out of"
msgstr "resultados de"

#. module: mail
#: model:mail.channel,name:mail.channel_1
msgid "sales"
msgstr "ventas"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "save"
msgstr "guardar"

#. module: mail
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "algunas direcciones específicas"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "inmovilizar:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "modelo de destino no especificado"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "equipo."

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/composer/composer.xml:0
#, python-format
msgid "this document"
msgstr "este documento"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "to close for"
msgstr "cerrar por"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#: code:addons/mail/static/src/components/rtc_configuration_menu/rtc_configuration_menu.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "alternar pulsar para hablar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "girar:"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "error desconocido"

#. module: mail
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "modelo de destino desconocido %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_email
msgid "using"
msgstr "usando"

#. module: mail
#. openerp-web
#: code:addons/mail/static/src/xml/many2one_avatar_user.xml:0
#, python-format
msgid "value"
msgstr "valor"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "semanas"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"
