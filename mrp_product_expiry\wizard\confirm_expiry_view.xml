<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="confirm_expiry_view_mrp_inherit" model="ir.ui.view">
        <field name="name">Confirm</field>
        <field name="model">expiry.picking.confirmation</field>
        <field name="inherit_id" ref="product_expiry.confirm_expiry_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='description']" position="after">
                <field name="picking_ids" invisible="1"/>
                <field name="production_ids" invisible="1"/>
                <field name="workorder_id" invisible="1"/>
            </xpath>
            <xpath expr="//button[@name='process']" position="attributes">
                <attribute name="attrs">{'invisible': [('picking_ids', '=', [])]}</attribute>
            </xpath>
            <xpath expr="//button[@name='process_no_expired']" position="attributes">
                <attribute name="attrs">{'invisible': [('picking_ids', '=', [])]}</attribute>
            </xpath>
            <xpath expr="//button[@special='cancel']" position="attributes">
                <attribute name="attrs">{'invisible': [('production_ids', '!=', [])]}</attribute>
            </xpath>
            <xpath expr="//button[@name='process']" position="after">
                <!-- From Produce Product wizard -->
                <button name="confirm_produce"
                    string="Confirm"
                    type="object"
                    attrs="{'invisible': [('production_ids', '=', [])]}"
                    class="btn-primary"/>
                <button special="cancel" data-hotkey="z"
                    string="Discard"
                    attrs="{'invisible': [('production_ids', '=', [])]}"
                    class="btn-secondary"/>
                <!-- From a Workorder -->
                <button name="confirm_workorder"
                    string="Confirm"
                    type="object"
                    attrs="{'invisible': [('workorder_id', '=', False)]}"
                    class="btn-primary"/>
            </xpath>
        </field>
    </record>
</odoo>
