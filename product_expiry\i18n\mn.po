# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_expiry
# 
# Translators:
# Bayarkhuu Bataa, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Baskhu<PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Expiration Alert</span>"
msgstr ""
"<span class=\"badge badge-danger\" attrs=\"{'invisible': "
"[('product_expiry_alert', '=', False)]}\">Дуусах хугацааны "
"анхааруулга</span>"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "<span> days</span>"
msgstr "<span> өдрүүд</span>"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__alert_date
msgid "Alert Date"
msgstr "Анхааруулах огноо"

#. module: product_expiry
#: model:mail.activity.type,name:product_expiry.mail_activity_type_alert_date_reached
msgid "Alert Date Reached"
msgstr "Барааны дуусах хугацааг анхаарна уу"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__alert_time
msgid "Alert Time"
msgstr "Анхааруулах хугацаа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_time
msgid "Best Before Time"
msgstr "Хадгалалтын хугацаа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_date
msgid "Best before Date"
msgstr "Хадгалалтын хугацаа"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Best before:"
msgstr "Аюулгүй хугацаа:"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Confirm"
msgstr "Батлах"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Хугацаа дууссаныг баталгаажуулах"

#. module: product_expiry
#: code:addons/product_expiry/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
#, python-format
msgid "Confirmation"
msgstr "Баталгаа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__alert_date
msgid ""
"Date to determine the expired lots and serial numbers using the filter "
"\"Expiration Alerts\"."
msgstr ""
"\"Дуусах хугацааны анхааруулга\" гэдэг шүүлтүүр ашиглан хугацаа нь дууссан "
"цуврал болон сериал дугааруудыг харна."

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_product_form_expiry
msgid "Dates"
msgstr "Огноо"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__description
msgid "Description"
msgstr "Тайлбар"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Discard"
msgstr "Үл хэрэгсэх"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_res_config_settings__group_expiry_date_on_delivery_slip
msgid "Display Expiration Dates on Delivery Slips"
msgstr "Хүргэлтийн тасалбар дээр хугацааны мэдээлэл харуулах"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.quant_search_view_inherit_product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.search_product_lot_filter_inherit_product_expiry
msgid "Expiration Alerts"
msgstr "Дуусах хугацааны анхааруулга"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__use_expiration_date
#: model_terms:ir.ui.view,arch_db:product_expiry.stock_report_delivery_document_inherit_product_expiry
msgid "Expiration Date"
msgstr "Хугацаа дуусах Огноо"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__expiration_time
msgid "Expiration Time"
msgstr "Хугацаа дуусах огноо"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.res_config_settings_view_form_stock
msgid "Expiration dates will appear on the delivery slip"
msgstr "Хүргэлтийн тасалбар дээр дуусах хугацааны мэдээлэл харагдана"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Expired Lot(s)"
msgstr "Хугацаа дууссан сер(үүд)"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_reminded
msgid "Expiry has been reminded"
msgstr "Дуусах хугацаа сануулга хадгалагдсан"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__id
msgid "ID"
msgstr "ID"

#. module: product_expiry
#: model:res.groups,name:product_expiry.group_expiry_date_on_delivery_slip
msgid "Include expiration dates on delivery slip"
msgstr "Хүргэлтийн тасалбар дээр дуусах хугацааг оруулах"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__lot_ids
msgid "Lot"
msgstr "Сери"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Серийн дугаар"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__expiration_time
#: model:ir.model.fields,help:product_expiry.field_product_template__expiration_time
msgid ""
"Number of days after the receipt of the products (from the vendor or in "
"stock after production) after which the goods may become dangerous and must "
"not be consumed. It will be computed on the lot/serial number."
msgstr ""
"Барааг хүлээж авснаас хойшхи (нийлүүлэгчээс хүлээн авах эсвэл үйлдвэрлэлээс "
"орлогод авах) хэд хоногийн дараа бүтээгдэхүүнийг хэрэглэхэд аюултай буюу "
"хэрэглэхгүй байх заалттай тухай. Энэ хоногийг серийн дугаар бүрт хөтөлдөг."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__alert_time
#: model:ir.model.fields,help:product_expiry.field_product_template__alert_time
msgid ""
"Number of days before the Expiration Date after which an alert should be "
"raised on the lot/serial number. It will be computed on the lot/serial "
"number."
msgstr ""
"Серийн дугаар дээр бүртгэгдсэн дуусах хугацаанаас хэд хоногийн өмнө "
"анхааруулга өгөх тухай. Энэ хугацаань серийн дугаар дээрээ хөтлөгддөг."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,help:product_expiry.field_product_template__removal_time
msgid ""
"Number of days before the Expiration Date after which the goods should be "
"removed from the stock. It will be computed on the lot/serial number."
msgstr ""
"Серийн дугаар дээр тодорхойлсон дуусах хугацаа өнгөрсний дараа хэд хоногийн "
"дотор нөөцөөс хассан байх ёстойг заах хугацаа. Энэ нь серийн дугаар дээрээ "
"хөтлөгддөг."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_time
#: model:ir.model.fields,help:product_expiry.field_product_template__use_time
msgid ""
"Number of days before the Expiration Date after which the goods starts "
"deteriorating, without being dangerous yet. It will be computed on the "
"lot/serial number."
msgstr ""
"Серийн дугаарын дуусах хугацаа болохоос хэд хоногийн өмнө барааны чанар "
"алдагдаж эхлэх тухай, гэхдээ хараахан аюулт хэмжээнд хүрээгүй байх. Энэ нь "
"серийн дугаар дээрээ хөтлөгддөг."

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__picking_ids
msgid "Picking"
msgstr "Бэлтгэх баримт"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.confirm_expiry_view
msgid "Proceed except for expired one"
msgstr "Хугацаа дууссанаас бусад барааг боловсруулах"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_procurement_group
msgid "Procurement Group"
msgstr "Нөөц хангалтын бүлэг"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_product
msgid "Product"
msgstr "Бараа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "Product Expiry Alert"
msgstr "Барааны дуусах хугацааны анхааруулга"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Барааны хөдөлгөөн (Дэлгэрэнгүй)"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Нөөцийн бүртгэл"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant__removal_date
msgid "Removal Date"
msgstr "Дуусах хугацаа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_product__removal_time
#: model:ir.model.fields,field_description:product_expiry.field_product_template__removal_time
msgid "Removal Time"
msgstr "Устгах хугацаа"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_expiry_picking_confirmation__show_lots
msgid "Show Lots"
msgstr "Серийн дугаар харуулах"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_move
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__product_expiry_alert
msgid "The Expiration Date has been reached."
msgstr "Дуусах хугацаанд хүрсэн байна."

#. module: product_expiry
#: code:addons/product_expiry/models/production_lot.py:0
#, python-format
msgid "The alert date has been reached for this lot/serial number"
msgstr "Энэ цуврал/сериал дугаарын анхааруулга огноо хэтэрсэн."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_move_line__expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__expiration_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""
"Энэ огноо нь энэ серийн дугаарын бараанууд аюултай болж ашиглах хийж "
"болохгүй болох огноо юм."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__removal_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock. This date will be used in FEFO removal strategy."
msgstr ""
"Энэхүү серийн дугаар бүхий бараа нь аль хугацаанд хүрэхэд нөөцөөс бүрэн "
"хассан байвал зохилтойг илэрхийлэх огноо. Энэ огноо нь FEFO гаргах зарчимд "
"ашиглагдана."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""
"Энэ огноо нь энэ серийн дугаарын бараанууд аюултай биш боловч муудаж эхлэх "
"огноо юм."

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_picking
msgid "Transfer"
msgstr "Шилжүүлэг"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot__use_expiration_date
msgid "Use Expiration Date"
msgstr "Дуусах хугацаа мөрдөх"

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.report_lot_label_expiry
msgid "Use by:"
msgstr "Хэрэглэх:"

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_product__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_product_template__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_move__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot__use_expiration_date
#: model:ir.model.fields,help:product_expiry.field_stock_quant__use_expiration_date
msgid ""
"When this box is ticked, you have the possibility to specify dates to manage"
" product expiration, on the product and on the corresponding lot/serial "
"numbers"
msgstr ""
"Үүнийг сонговол, та бараа болон харгалзах серийн дугааруудад дуусах хугацааг"
" удирдах огнооны мэдээллүүдийг оруулах болно."

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver some product expired lots.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Та зарим нэг хугацаа нь дууссан серийн дугаар бүхий шилжүүлэг хийх гэж "
"байна. Та энэ үйлдлийг боловсруулахдаа итгэлтэй байна уу?"

#. module: product_expiry
#: code:addons/product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to deliver the product %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed ?"
msgstr ""
"Таны боловсруулах гэж буй %(product_name)s бараа, %(lot_name)s серийн дугаар нь хугацаа дууссан байна.\n"
"Батлахдаа итгэлтэй байна уу ?"
