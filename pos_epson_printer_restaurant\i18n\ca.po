# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer_restaurant
# 
# Translators:
# ma<PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,field_description:pos_epson_printer_restaurant.field_restaurant_printer__epson_printer_ip
msgid "Epson Receipt Printer IP Address"
msgstr "Adreça IP de la impressora de rebuts Epson"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,help:pos_epson_printer_restaurant.field_restaurant_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Adreça IP local d'una impressora de recepció d'Epson."

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields,field_description:pos_epson_printer_restaurant.field_restaurant_printer__printer_type
msgid "Printer Type"
msgstr "Tipus d'impressora"

#. module: pos_epson_printer_restaurant
#: model:ir.model,name:pos_epson_printer_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr "Impressora del restaurant"

#. module: pos_epson_printer_restaurant
#: model:ir.model.fields.selection,name:pos_epson_printer_restaurant.selection__restaurant_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Utilitza una impressora Epson"
