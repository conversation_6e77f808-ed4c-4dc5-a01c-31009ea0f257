# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-12 11:32+0000\n"
"PO-Revision-Date: 2019-08-26 09:09+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr ""

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""

#. module: auth_password_policy
#: code:addons/auth_password_policy/models/res_users.py:30
#, python-format
msgid "Passwords must have at least %d characters, got %d."
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:123
#, python-format
msgid ""
"Required: %s.\n"
"\n"
"Hint: increase length, use multiple words and use non-letter characters to increase your password's strength."
msgstr ""

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "Users"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:28
#, python-format
msgid "at least %d character classes"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:22
#, python-format
msgid "at least %d characters"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:25
#, python-format
msgid "at least %d words"
msgstr ""

#. module: auth_password_policy
#. openerp-web
#: code:addons/auth_password_policy/static/src/js/password_gauge.js:125
#, python-format
msgid "no requirements"
msgstr ""
