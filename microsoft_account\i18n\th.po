# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_account
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""
"ไม่รองรับวิธีการ [%s] ไม่ได้อยู่ใน [GET, POST, PUT, PATCH หรือ DELETE]!"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_rtoken
msgid "Microsoft Refresh Token"
msgstr "โทเค็นการรีเฟรชของ Microsoft"

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_microsoft_service
msgid "Microsoft Service"
msgstr "บริการของ Microsoft"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token_validity
msgid "Microsoft Token Validity"
msgstr "ความถูกต้องของโทเค็นของ Microsoft"

#. module: microsoft_account
#: model:ir.model.fields,field_description:microsoft_account.field_res_users__microsoft_calendar_token
msgid "Microsoft User token"
msgstr "โทเค็นผู้ใช้ Microsoft"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""
"มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างโทเค็นของคุณ "
"รหัสอนุญาตของคุณอาจไม่ถูกต้อง"

#. module: microsoft_account
#: code:addons/microsoft_account/models/microsoft_service.py:0
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""
"มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างโทเค็นของคุณ "
"บางทีรหัสอนุญาตของคุณอาจไม่ถูกต้องหรือหมดอายุแล้ว"

#. module: microsoft_account
#: model:ir.model,name:microsoft_account.model_res_users
msgid "Users"
msgstr "ผู้ใช้งาน"
