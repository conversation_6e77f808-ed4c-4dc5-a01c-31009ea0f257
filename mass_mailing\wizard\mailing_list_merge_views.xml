<?xml version="1.0"?>
<odoo>
	<!-- Merge Mailing List  -->
    <record id="mailing_list_merge_view_form" model="ir.ui.view">
        <field name="name">mailing.list.merge.form</field>
        <field name="model">mailing.list.merge</field>
        <field name="arch" type="xml">
            <form string="Merge Mass Mailing List">
                <group>
                    <field name="merge_options" widget="selection"/>
                    <field name="new_list_name" attrs="{'invisible': [('merge_options', '=', 'existing')], 'required': [('merge_options', '=', 'new')]}"/>
                    <field name="dest_list_id" attrs="{'invisible': [('merge_options', '=', 'new')], 'required': [('merge_options', '=', 'existing')]}"/>
                    <field name="archive_src_lists"/>
                </group>
                <field name="src_list_ids">
                    <tree>
                        <field name="name"/>
                        <field name="contact_count" string="Number of Recipients"/>
                    </tree>
                </field>
                <footer>
                    <button name="action_mailing_lists_merge" type="object" string="Merge" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="mailing_list_merge_action" model="ir.actions.act_window">
        <field name="name">Merge</field>
        <field name="res_model">mailing.list.merge</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="model_mailing_list"/>
        <field name="binding_view_types">list</field>
    </record>
</odoo>
