<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <record id="dep_administration" model="hr.department">
          <field name="name">Administration</field>
        </record>

        <record id="dep_sales" model="hr.department">
          <field name="name">Sales</field>
        </record>

        <record id="res_partner_admin_private_address" model="res.partner">
            <field name="name">Administrator</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="email"><EMAIL></field>
            <field name="type">private</field>
        </record>

        <record id="employee_admin" model="hr.employee">
            <field name="name" eval="obj(ref('base.partner_admin')).name" model="res.partner"/>
            <field name="department_id" ref="dep_administration"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="address_id" ref="base.main_partner"/>
            <field name="address_home_id" ref="res_partner_admin_private_address"/>
            <field name="image_1920" eval="obj(ref('base.partner_admin')).image_1920" model="res.partner"/>
        </record>

        <record id="onboarding_setup_it_materials" model="hr.plan.activity.type">
            <field name="summary">Setup IT Materials</field>
            <field name="responsible">manager</field>
        </record>

        <record id="onboarding_plan_training" model="hr.plan.activity.type">
            <field name="summary">Plan Training</field>
            <field name="responsible">manager</field>
        </record>

        <record id="onboarding_training" model="hr.plan.activity.type">
            <field name="summary">Training</field>
            <field name="responsible">employee</field>
        </record>

        <record id="offboarding_setup_compute_out_delais" model="hr.plan.activity.type">
            <field name="summary">Compute Out Delais</field>
            <field name="responsible">manager</field>
        </record>

        <record id="offboarding_take_back_hr_materials" model="hr.plan.activity.type">
            <field name="summary">Take Back HR Materials</field>
            <field name="responsible">manager</field>
        </record>

        <record id="onboarding_plan" model='hr.plan'>
            <field name="name">Onboarding</field>
            <field name="plan_activity_type_ids" eval="[(6, 0, [
                ref('hr.onboarding_setup_it_materials'),
                ref('hr.onboarding_plan_training'),
                ref('hr.onboarding_training'),
                ])]"/>
        </record>

        <record id="offboarding_plan" model='hr.plan'>
            <field name="name">Offboarding</field>
            <field name="plan_activity_type_ids" eval="[(6, 0, [
                ref('hr.offboarding_setup_compute_out_delais'),
                ref('hr.offboarding_take_back_hr_materials'),
                ])]"/>
        </record>

        <record model="ir.config_parameter" id="hr_presence_control_login" forcecreate="False">
            <field name="key">hr.hr_presence_control_login</field>
            <field name="value">True</field>
        </record>

        <!-- Departure Reasons -->
        <record id="departure_fired" model="hr.departure.reason">
            <field name="sequence">0</field>
            <field name="name">Fired</field>
        </record>

        <record id="departure_resigned" model="hr.departure.reason">
            <field name="sequence">1</field>
            <field name="name">Resigned</field>
        </record>

        <record id="departure_retired" model="hr.departure.reason">
            <field name="sequence">2</field>
            <field name="name">Retired</field>
        </record>

        <!-- Work permit expires Soon -->
        <record id="ir_cron_data_check_work_permit_validity" model="ir.cron">
            <field name="name">HR Employee: check work permit validity</field>
            <field name="model_id" ref="model_hr_employee"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">model._cron_check_work_permit_validity()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>

    </data>
</odoo>
