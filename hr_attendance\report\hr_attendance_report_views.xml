<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_attendance_report_view_search" model="ir.ui.view">
        <field name="name">hr.attendance.report.view.search</field>
        <field name="model">hr.attendance.report</field>
        <field name="arch" type="xml">
            <search string="HR Attendance Search">
                <field name="employee_id"/>
                <field name="department_id" operator="child_of"/>
                <filter name="check_in" string="Check In" date="check_in" default_period="last_month"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="groupby_employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Check In" name="groupby_check_in" context="{'group_by': 'check_in'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_attendance_report_view_pivot" model="ir.ui.view">
        <field name="name">hr.attendance.report.view.pivot</field>
        <field name="model">hr.attendance.report</field>
        <field name="arch" type="xml">
            <pivot string="Attendance" disable_linking="1">
                <field name="employee_id" type="row"/>
                <field name="check_in" type="col"/>
                <field name="worked_hours" type="measure" widget="float_time"/>
                <field name="overtime_hours" type="measure" widget="float_time"/>
            </pivot>
        </field>
    </record>

    <record id="hr_attendance_report_view_graph" model="ir.ui.view">
        <field name="name">hr.attendance.report.view.graph</field>
        <field name="model">hr.attendance.report</field>
        <field name="arch" type="xml">
            <graph string="Attendance Statistics" stacked="0" disable_linking="1">
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="overtime_hours" type="measure" />
                <field name="worked_hours" type="measure" />
            </graph>
        </field>
    </record>

    <record id="hr_attendance_report_action" model="ir.actions.act_window">
        <field name="name">Attendance Analysis</field>
        <field name="res_model">hr.attendance.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_attendance_report_view_search"/>
        <field name="context">{'group_by': ['check_in:day', 'employee_id'], 'search_default_check_in': '1'}</field>
    </record>

    <record id="hr_attendance_report_action_filtered" model="ir.actions.act_window">
        <field name="name">Attendance Analysis</field>
        <field name="res_model">hr.attendance.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_attendance_report_view_search"/>
        <field name="context">{
            'group_by': ['check_in:day', 'employee_id'],
            'search_default_department_id': [active_id]}
        </field>
    </record>

    <menuitem
        id="menu_hr_attendance_report"
        name="Reporting"
        sequence="30"
        parent="hr_attendance.menu_hr_attendance_root"
        action="hr_attendance_report_action"
        groups="hr_attendance.group_hr_attendance_user"/>
</odoo>
