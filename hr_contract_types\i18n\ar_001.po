# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_types
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-27 09:25+0000\n"
"PO-Revision-Date: 2022-06-27 09:25+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hr_contract_types
#: model:ir.model,name:hr_contract_types.model_hr_contract
msgid "Contract"
msgstr "عقد"

#. module: hr_contract_types
#: model:ir.model,name:hr_contract_types.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract_types.hr_contract_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_types.hr_contract_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract_types.hr_contract_type_view_tree
msgid "Contract Type"
msgstr "نوع العقد"

#. module: hr_contract_types
#: model:ir.actions.act_window,name:hr_contract_types.action_hr_contract_type
#: model:ir.ui.menu,name:hr_contract_types.hr_menu_contract_type
msgid "Contract Types"
msgstr "أنواع العقود"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract__type_id
msgid "Employee Category"
msgstr "فئة الموظف"

#. module: hr_contract_types
#: model:ir.model.fields,help:hr_contract_types.field_hr_contract__type_id
msgid "Employee category"
msgstr "فئة الموظف"

#. module: hr_contract_types
#: model:ir.model.fields,help:hr_contract_types.field_hr_contract_type__sequence
msgid "Gives the sequence when displaying a list of Contract."
msgstr ".يعطي التسلسل عند عرض قائمة العقد"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__id
msgid "ID"
msgstr "المُعرف"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: hr_contract_types
#: model:ir.model.fields,help:hr_contract_types.field_hr_contract_type__name
msgid "Name"
msgstr "اسم"

#. module: hr_contract_types
#: model_terms:ir.ui.view,arch_db:hr_contract_types.hr_contract_type_view_search
msgid "Search Contract Type"
msgstr "بحث نوع العقد"

#. module: hr_contract_types
#: model:ir.model.fields,field_description:hr_contract_types.field_hr_contract_type__sequence
#: model_terms:ir.ui.view,arch_db:hr_contract_types.hr_contract_type_view_search
msgid "Sequence"
msgstr "تسلسل"