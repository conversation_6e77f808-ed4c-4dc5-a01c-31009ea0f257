/* Input style used in cash control popups */
.pos .popup .pos-input {
    text-align: center;
    font-size: 18px;
    color: #555555;
    background: none;
    min-height: 0;
    border-radius: unset;
    box-shadow: none;
    padding: 0;
    cursor: pointer;
    width: 50px;
    margin-right: 15px;
    border-bottom: solid 2px;
}

.pos .popup .pos-input:focus {
    box-shadow: none ;
    border-color: blue;
    font-weight: bold;
}

.pos .popup .invalid-cash-input {
    color: red;
    animation: blink 0.5s linear;
    animation-iteration-count: 2;
    border: 1px solid red;
    box-shadow: none !important;
}

.pos .popup .invalid-cash-input:focus {
    border-color: red;
}

@keyframes blink {
    0%, 100% {
        border-color: red;
    }
    50% {
        border-color: transparent;
    }
}
