<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.web.recaptcha</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='website_recaptcha_settings']/div[hasclass('text-muted')]" position="inside">
                <p>If no keys are provided, no checks will be done.</p>
            </xpath>
            <div id="recaptcha_warning" position="replace">
                <div class="content-group" id="reacaptcha_configuration_settings" attrs="{'invisible': [('module_google_recaptcha', '=', False)]}">
                    <div class="mt16 row">
                        <label for="recaptcha_public_key" class="col-3 o_light_label"/>
                        <field name="recaptcha_public_key"/>
                        <label for="recaptcha_private_key" class="col-3 o_light_label"/>
                        <field name="recaptcha_private_key"/>
                        <label for="recaptcha_min_score" class="col-3 o_light_label"/>
                        <field name="recaptcha_min_score"/>
                    </div>
                    <div>
                        <a href="https://www.google.com/recaptcha/admin/create" class="oe_link" target="_blank">
                            <i class="fa fa-arrow-right"/> Generate reCAPTCHA v3 keys
                        </a>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
