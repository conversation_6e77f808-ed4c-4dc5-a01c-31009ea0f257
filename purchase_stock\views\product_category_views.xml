<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="product_category_view_form" model="ir.ui.view">
        <field name="name">product.category.view.form.inherit.purchase.stock</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="purchase.view_category_property_form"/>
        <field name="arch" type="xml">
            <field name="property_account_creditor_price_difference_categ" position="attributes">
                <attribute name="attrs">{'invisible':[('property_valuation', '=', 'manual_periodic')]}</attribute>
            </field>
        </field>
    </record>
</odoo>
