<?xml version="1.0"?>
<odoo>

    <record id="hr_manager_attendance_report_form_2" model="ir.ui.view">
        <field name="name">hr.manager.attendance.report.form</field>
        <field name="model">hr.manager.attendance.report</field>
        <field name="arch" type="xml">
            <form string="تقرير احتساب الحضور و الغياب">
                <div>
                    <h4>
                        My Employees
                    </h4>
                </div>
                <group>
                    <group>
                        <field name="employee_id"
                               attrs="{'required':[('all_absence','=',False)],'invisible':[('all_absence','=',True)]}"/>
                        <field name="all_absence"/>
                        <field name="type" attrs="{'invisible':[('all_absence','!=',True)], 'required':[('all_absence','=',True)]}"/>

                        <field name="employee_with_absance" attrs="{'invisible':[('all_absence','=',True)]}"/>
                        <field name="detail_type" attrs="{'required':[('all_absence','=',True)],'invisible':[('all_absence','!=',True)]}"/>

                    </group>
                    <group>
                        <field name="date_start" required="1"/>
                        <field name="date_end" required="1"/>
                    </group>
                </group>
                <footer>
                    <button name="get_employee_attendancy_report_action" type="object" string="انشاء"
                            class="btn-primary"/>
                    <button string="الغاء" class="btn-secondary" special="cancel"/>
                </footer>

            </form>
        </field>
    </record>


    <record id="action_report_manager_attendance_report_2" model="ir.actions.act_window">
        <field name="name">تقرير احتساب الحضور و الغياب</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.manager.attendance.report</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_manager_attendance_report_form_2"/>
        <field name="target">new</field>
    </record>


    <menuitem
            id="menu_hr_attendance_admin_report"
            name="تقرير حضور وانصراف"
            parent="hr_approvales_masarat.menu_hr_attendance_admin_masarat"
            groups="hr_approvales_masarat.group_hr_approvales_masarat"
            action="action_report_manager_attendance_report_2"
            sequence="2"/>


    <template id="employee_attendancy_report">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="0"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong>تقرير حركة الحضور والانصراف</strong>
                    </h4>
                    <br/>
                    <h5 style="text-align: right;">
                        <strong>
                            <span t-esc="name"/>
                            -
                            <span t-esc="department"/>
                        </strong>
                    </h5>
                    <br/>
                    <br/>
                    <p style="text-align: right; font-size: 14">
                        نوع الدوام :
                        <span t-esc="attendance_type"/>
                    </p>
                    <p style="text-align: right; font-size: 14">
                        عدد الغياب :
                        <span t-esc="absence_count"/>
                    </p>

                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 10%; text-align: center; border: 1px solid; font-size:12px">دقائق
                                    التأخير المحتسبة
                                </th>
                                <th style="width: 25%; text-align: center; border: 1px solid;">الحالة</th>
                                <th style="width: 15%; text-align: center; border: 1px solid; font-size:12px">مجموع
                                    ساعات العمل
                                </th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">إنصارف</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">حضور</th>
                                <th style="width: 15%; text-align: center; border: 1px solid;">التاريخ</th>
                                <th style="width: 5%; text-align: center; border: 1px solid;">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="atten_obj" t-as="line" style="text-align: center;">
                                <td style="border: 1px solid;">
                                    <span t-esc="line['computed_latency_minutes']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['state']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['worked_hours']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['check_out']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['check_in']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <span t-esc="line['attendance_date']"/>
                                </td>
                                <td style="border: 1px solid;">
                                    <t t-set="index" t-value="index + 1"/>
                                    <span t-esc="index"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="all_employee_absence_report">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="index" t-value="0"/>
                <div class="page">
                    <h4 style="text-align: center;">
                        <strong> تقرير</strong>
                        <span t-esc="type"/>
                        <span t-esc="detail_type_name"/>
                    </h4>
                    <ul dir="rtl" class="nav" style="display: flex; justify-content: center;">
                        <li>
                            <h5 style="text-align: center;">للفترة من :
                                <span t-esc="start_date"/>
                            </h5>
                        </li>
                        <li>
                            <h5 style="text-align: center;">الى :
                                <span t-esc="end_date"/>
                            </h5>
                        </li>
                    </ul>
                    <br/>
                    <br/>
                    <table style="font-size:16px; width: 100%; border-collapse: collapse; border-style: solid; border: 1px solid;">
                        <thead>
                            <tr style="text-align: center; height: 20px; font-weight:bold;">
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px"><span t-esc="type_colomn"/>
                                </th>
                                <th style="width: 20%; text-align: center; border: 1px solid; font-size:12px">نوع
                                    الدوام
                                </th>
                                <th style="width: 25%; text-align: center; border: 1px solid;">القسم</th>
                                <th style="width: 30%; text-align: center; border: 1px solid; font-size:12px">الموظف
                                </th>
                                <th style="width: 5%; text-align: center; border: 1px solid;">#</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr t-foreach="employees_dict.keys()" t-as="employee" style="text-align: center;">
                                <tr t-foreach="employees_dict[employee]['absence_days']" t-as="line"
                                    style="text-align: center;">
                                    <td style="border: 1px solid; text-align: center; font-size:12px">
                                        <span t-esc="line['note2']"/> :: <span t-esc="line['note1']"/> :: <span t-esc="line['date']"/>
                                    </td>
                                    <td style="border: 1px solid;">
                                        <span t-esc="employees_dict[employee]['attendancy_type']"/>
                                    </td>
                                    <td style="border: 1px solid;">
                                        <span t-esc="employees_dict[employee]['department']"/>
                                    </td>
                                    <td style="border: 1px solid;">
                                        <span t-esc="employees_dict[employee]['name']"/>
                                    </td>
                                    <td style="border: 1px solid;">
                                        <t t-set="index" t-value="index + 1"/>
                                        <span t-esc="index"/>
                                    </td>
                                </tr>
                                <tr style="height:10px">
                                    <td colspan="5" style="background-color: #F3F3F3">
                                    </td>
                                </tr>
                            </tr>
                        </tbody>
                    </table>
                    <br/>
                </div>
            </t>
        </t>
    </template>

    <template id="employee_attendancy_report_id">
        <t t-if="all_absence">
            <t t-call="hr_approvales_masarat.all_employee_absence_report"></t>
        </t>
        <t t-else="">
            <t t-call="hr_approvales_masarat.employee_attendancy_report"></t>
        </t>
    </template>

    <report
            id="employee_attendancy_report_x1"
            string="Employee Attendance Report"
            model="hr.employee"
            report_type="qweb-pdf"
            name="hr_approvales_masarat.employee_attendancy_report_id"
            file="hr_approvales_masarat.employee_attendancy_report_id"
            menu="False"/>


</odoo>