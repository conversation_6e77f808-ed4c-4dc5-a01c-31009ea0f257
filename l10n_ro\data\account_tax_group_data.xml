<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="tax_group_tva_0" model="account.tax.group">
            <field name="name">TVA 0%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_5" model="account.tax.group">
            <field name="name">TVA 5%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_9" model="account.tax.group">
            <field name="name">TVA 9%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_19" model="account.tax.group">
            <field name="name">TVA 19%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_20" model="account.tax.group">
            <field name="name">TVA 20%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_24" model="account.tax.group">
            <field name="name">TVA 24%</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_0_eu" model="account.tax.group">
            <field name="name">TVA 0% EU</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_5_eu" model="account.tax.group">
            <field name="name">TVA 5% EU</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_9_eu" model="account.tax.group">
            <field name="name">TVA 9% EU</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_19_eu" model="account.tax.group">
            <field name="name">TVA 19% EU</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_ned" model="account.tax.group">
            <field name="name">TVA Nedeductibil</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_ti" model="account.tax.group">
            <field name="name">TVA Taxare Inversa</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_scutit" model="account.tax.group">
            <field name="name">TVA Scutit</field>
            <field name="country_id" ref="base.ro"/>
        </record>
        <record id="tax_group_tva_neimp" model="account.tax.group">
            <field name="name">TVA Neimpozabil</field>
            <field name="country_id" ref="base.ro"/>
        </record>
    </data>
</odoo>
