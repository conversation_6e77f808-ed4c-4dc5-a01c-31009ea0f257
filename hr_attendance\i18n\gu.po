# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_attendance
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: Spellbound Soft Solutions <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: gu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:52
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:27
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:32
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:125
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Employee PIN</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attendance</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:140
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:129
#, python-format
msgid "Another good day's work! See you soon!"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph_filtered
msgid "Attendance Analysis"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
msgid "Attendance Status"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:4
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#, python-format
msgid "Available"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__barcode
msgid "Badge ID"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:82
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:69
#: code:addons/hr_attendance/models/hr_attendance.py:95
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:109
#, python-format
msgid "Cannot perform check in or check out on multiple employees."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:123
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
msgid "Check In"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In Date"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
msgid "Check Out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out Date"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,attendance_state:0
msgid "Checked in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:124
#, python-format
msgid "Checked in at"
msgstr ""

#. module: hr_attendance
#: selection:hr.employee,attendance_state:0
msgid "Checked out"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:115
#, python-format
msgid "Checked out at"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:47
#: code:addons/hr_attendance/static/src/xml/attendance.xml:75
#, python-format
msgid "Click to"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:14
#: model_terms:ir.ui.view,arch_db:hr_attendance.print_employee_badge
#, python-format
msgid "Company Logo"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "રુપરેખાંકન"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
msgid "Created by"
msgstr "બનાવનાર"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
msgid "Created on"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
msgid "Department"
msgstr "વિભાગ"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:146
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:138
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "કર્મચારી"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.print_employee_badge
msgid "Employee Image"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_kanban_inherit_hr_attendance
msgid "Employee's Name"
msgstr "કર્મચારી નું નામ"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_employees_kanban
msgid "Employees"
msgstr "કર્મચારીઓ"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:96
#, python-format
msgid "Error: could not find corresponding employee."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:93
#, python-format
msgid "First come, first served"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:108
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:63
#: code:addons/hr_attendance/static/src/xml/attendance.xml:64
#: code:addons/hr_attendance/static/src/xml/attendance.xml:137
#, python-format
msgid "Go back"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:99
#, python-format
msgid "Good afternoon"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:101
#, python-format
msgid "Good evening"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:96
#, python-format
msgid "Good morning"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:87
#: code:addons/hr_attendance/static/src/js/greeting_message.js:103
#, python-format
msgid "Good night"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:113
#: code:addons/hr_attendance/static/src/xml/attendance.xml:131
#, python-format
msgid "Goodbye"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:143
#, python-format
msgid "Have a good afternoon"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:134
#, python-format
msgid "Have a good day!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:148
#, python-format
msgid "Have a good evening"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:136
#, python-format
msgid "Have a nice lunch!"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
msgid "ID"
msgstr "ઓળખ"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__barcode
msgid "ID used for employee identification."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:111
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:136
#, python-format
msgid "Invalid request, please return to the main menu."
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_mode
msgid "Kiosk Mode"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
msgid "Last Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_manage_attendances
msgid "Manage Attendances"
msgstr ""

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Manager"
msgstr "વ્યવસ્થાપક"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__manual_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "Manual Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "સંદેશ"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "No attendance records to display"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:73
#, python-format
msgid "No employee corresponding to barcode %(barcode)s"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Not available"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:132
#, python-format
msgid "OK"
msgstr "બરાબર"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer"
msgstr "અધિકારી"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__pin
msgid "PIN"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__pin
msgid "PIN used to Check In/Out in Kiosk Mode (if enabled in Configuration)."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:78
#, python-format
msgid "Please enter your PIN to"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:96
#, python-format
msgid "Please return to the main menu."
msgstr ""

#. module: hr_attendance
#: model:ir.actions.report,name:hr_attendance.hr_employee_print_badge
msgid "Print Badge"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "અહેવાલીકરણ"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:18
#, python-format
msgid "Scan your badge"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:25
#, python-format
msgid "Select Employee"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "સુયોજનો"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:46
#: code:addons/hr_attendance/static/src/xml/attendance.xml:74
#, python-format
msgid "Sign in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:45
#: code:addons/hr_attendance/static/src/xml/attendance.xml:73
#: code:addons/hr_attendance/static/src/xml/attendance.xml:98
#, python-format
msgid "Sign out"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_filter_inherit_hr_attendance
msgid "Signed In"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_filter_inherit_hr_attendance
msgid "Signed Out"
msgstr ""

#. module: hr_attendance
#: sql_constraint:hr.employee:0
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:64
#, python-format
msgid "The PIN must be a sequence of digits."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:91
#, python-format
msgid "The early bird catches the worm"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__manual_attendance
msgid ""
"The employee will have access to the \"My Attendances\" menu to check in and"
" out from his session"
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:44
#: code:addons/hr_attendance/static/src/xml/attendance.xml:71
#, python-format
msgid "Want to check out?"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:49
#, python-format
msgid ""
"Warning : Your user should be linked to an employee to use attendance. "
"Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:122
#, python-format
msgid "Welcome"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:13
#, python-format
msgid "Welcome to"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:44
#: code:addons/hr_attendance/static/src/xml/attendance.xml:71
#, python-format
msgid "Welcome!"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:80
#, python-format
msgid "Wrong PIN"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:103
#, python-format
msgid "You cannot duplicate an attendance."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.print_employee_badge
msgid "barcode"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:47
#: code:addons/hr_attendance/static/src/xml/attendance.xml:75
#: code:addons/hr_attendance/static/src/xml/attendance.xml:78
#, python-format
msgid "check in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:47
#: code:addons/hr_attendance/static/src/xml/attendance.xml:75
#: code:addons/hr_attendance/static/src/xml/attendance.xml:78
#, python-format
msgid "check out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_ids
msgid "list of attendances for the employee"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:21
#, python-format
msgid "or"
msgstr "અથવા"
