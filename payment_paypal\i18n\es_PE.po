# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * payment_paypal
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Language-Team: Spanish (Peru) (https://www.transifex.com/odoo/teams/41243/es_PE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_api_access_token
msgid "Access Token"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_api_access_token_validity
msgid "Access Token Validity"
msgstr ""

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.acquirer_form_paypal
msgid "How to configure your paypal account?"
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer_paypal_pdt_token
msgid ""
"Payment Data Transfer allows you to receive notification of successful "
"payments as they are made."
msgstr ""

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_email_account
msgid "Paypal Email ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer_paypal_use_ipn
msgid "Paypal Instant Payment Notification"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_seller_account
msgid "Paypal Merchant ID"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_pdt_token
msgid "Paypal PDT Token"
msgstr ""

#. module: payment_paypal
#: code:addons/payment_paypal/models/payment.py:138
#, python-format
msgid "Paypal: received data with missing reference (%s) or txn_id (%s)"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_api_password
msgid "Rest API Password"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_api_username
msgid "Rest API Username"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_acquirer_paypal_seller_account
msgid ""
"The Merchant ID is used to ensure communications coming from Paypal are "
"valid and secured."
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction_paypal_txn_type
msgid "Transaction type"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_use_ipn
msgid "Use IPN"
msgstr ""

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_acquirer_paypal_api_enabled
msgid "Use Rest API"
msgstr ""
