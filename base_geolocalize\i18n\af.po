# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_geolocalize
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span class=\"oe_inline\"> ( On  </span>"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> : Lat : </span>"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "<span> ;  Long:  </span>"
msgstr ""

#. module: base_geolocalize
#: code:addons/base_geolocalize/models/res_partner.py:18
#, python-format
msgid ""
"Cannot contact geolocation servers. Please make sure that your Internet "
"connection is up and running (%s)."
msgstr ""

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner_partner_latitude
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users_partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner_partner_longitude
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users_partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocate"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr ""

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner_date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users_date_localization
msgid "Geolocation Date"
msgstr ""

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignation"
msgstr ""
