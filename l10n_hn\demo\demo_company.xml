<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_hn" model="res.partner">
        <field name="name">HN Company</field>
        <field name="vat"></field>
        <field name="street">CA-1</field>
        <field name="city">Nacaome</field>
        <field name="country_id" ref="base.hn"/>
        
        <field name="zip"></field>
        <field name="phone">+504 9123-4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.hnexample.com</field>
    </record>

    <record id="demo_company_hn" model="res.company">
        <field name="name">HN Company</field>
        <field name="partner_id" ref="partner_demo_company_hn"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_hn')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_hn.demo_company_hn'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_hn.cuentas_plantilla')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_hn.demo_company_hn')"/>
    </function>
</odoo>
