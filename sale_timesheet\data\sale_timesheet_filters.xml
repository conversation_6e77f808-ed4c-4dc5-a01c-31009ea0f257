<?xml version="1.0" encoding="utf-8"?>
<odoo>

	<record id="ir_filter_project_profitability_report_costs_and_revenues" model="ir.filters">
        <field name="name">Costs and Revenues</field>
        <field name="model_id">project.profitability.report</field>
        <field name="user_id" eval="False"/>
        <field name="is_default" eval="True"/>
        <field name="context">{
            'pivot_measures': ['amount_untaxed_to_invoice', 'amount_untaxed_invoiced', 'timesheet_cost', 'margin']
        }</field>
    </record>

</odoo>
