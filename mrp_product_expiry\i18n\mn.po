# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_product_expiry
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_product_expiry
#: model_terms:ir.ui.view,arch_db:mrp_product_expiry.confirm_expiry_view_mrp_inherit
msgid "Confirm"
msgstr "Батлах"

#. module: mrp_product_expiry
#: model:ir.model,name:mrp_product_expiry.model_expiry_picking_confirmation
msgid "Confirm Expiry"
msgstr "Хугацаа дууссаныг баталгаажуулах"

#. module: mrp_product_expiry
#: code:addons/mrp_product_expiry/models/mrp_production.py:0
#, python-format
msgid "Confirmation"
msgstr "Баталгаа"

#. module: mrp_product_expiry
#: model_terms:ir.ui.view,arch_db:mrp_product_expiry.confirm_expiry_view_mrp_inherit
msgid "Discard"
msgstr "Үл хэрэгсэх"

#. module: mrp_product_expiry
#: model:ir.model.fields,field_description:mrp_product_expiry.field_expiry_picking_confirmation__production_ids
msgid "Production"
msgstr "Үйлдвэрлэл"

#. module: mrp_product_expiry
#: model:ir.model,name:mrp_product_expiry.model_mrp_production
msgid "Production Order"
msgstr "Үйлдвэрлэлийн захиалга"

#. module: mrp_product_expiry
#: model:ir.model.fields,field_description:mrp_product_expiry.field_expiry_picking_confirmation__workorder_id
msgid "Workorder"
msgstr ""

#. module: mrp_product_expiry
#: code:addons/mrp_product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to use some expired components.\n"
"Do you confirm you want to proceed ?"
msgstr ""

#. module: mrp_product_expiry
#: code:addons/mrp_product_expiry/wizard/confirm_expiry.py:0
#, python-format
msgid ""
"You are going to use the component %(product_name)s, %(lot_name)s which is expired.\n"
"Do you confirm you want to proceed ?"
msgstr ""
