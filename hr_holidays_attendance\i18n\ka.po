# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# <PERSON><PERSON><PERSON> <david.machakhel<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# Temur, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: G<PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Georgian (https://app.transifex.com/odoo/teams/41243/ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "ხელმისაწვდომია"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_company
msgid "Companies"
msgstr "კომპანიები"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr ""

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "გაუქმება"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr ""

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr ""

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "HOURS AVAILABLE"
msgstr ""

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Hours"
msgstr "საათი"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__hr_attendance_overtime
msgid "Hr Attendance Overtime"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr ""

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "შენახვა"

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to request this allocation."
msgstr ""

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to request this leave."
msgstr ""

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "Users"
msgstr "მომხმარებლები"

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "You do not have enough extra hours to request this leave"
msgstr ""

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "requires_allocation"
msgstr ""
