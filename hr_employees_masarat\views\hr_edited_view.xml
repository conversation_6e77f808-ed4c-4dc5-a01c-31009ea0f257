<?xml version="1.0"?>
<odoo>
    <!---->
    <record id="view_employee_form_inherited_x1" model="ir.ui.view">
        <field name="name">hr.employee.form.x1</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='passport_id']" position="replace">
                <field name="national_number" groups="hr.group_hr_user"/>
                <field name="passport_id" groups="hr.group_hr_user"/>
                <field name="passport_issue_location" groups="hr.group_hr_user"/>
                <field name="passport_issue_date" groups="hr.group_hr_user"/>
                <field name="passport_end_date" groups="hr.group_hr_user"/>
            </xpath>

            <xpath expr="//field[@name='country_id']" position="before">
                <field name="join_date" groups="hr.group_hr_user" required="True"/>
                <field name="hiring_date" groups="hr.group_hr_user"/>
                <field name="connected_with_comp" groups="hr.group_hr_user" required="True"/>
                <label for="class_id" id="label_dates" attrs="{'invisible': [('connected_with_comp', '!=', '3')]}"/>
                <div>
                    <div class="o_row">
                        <span class="oe_inline" attrs="{'invisible': [('connected_with_comp', '!=', '3')]}"></span>
                        <field name="class_id" class="oe_inline" attrs="{'invisible': [('connected_with_comp', '!=', '3')]}"/>
                        <span class="oe_inline" attrs="{'invisible': [('connected_with_comp', '!=', '3')]}"></span>
                        <field name="selection_type" class="oe_inline" attrs="{'invisible': [('connected_with_comp', '!=', '3')]}"/>
                    </div>
                </div>
            </xpath>
                
            <xpath expr="//field[@name='job_title']" position="before">
               <div>
                <h1><field name="english_name" placeholder="English Name" required="True"/></h1>
               </div>
               
               <div>
                <field name="int_id" placeholder="Employee ID" required="True"/>
               </div>
            </xpath>
            
            <xpath expr="//field[@name='department_id']" position="replace">
                <field name="department_id" options='{"no_open": True}'/>
            </xpath>
            
            
            <xpath expr="//field[@name='gender']" position="after">
                <field name="bloodtype"/>
            </xpath>

            <xpath expr="//field[@name='department_id']" position="after">
                <field name="referral_date"/>
            </xpath>

            <xpath expr="//field[@name='country_of_birth']" position="replace">
                <field name="country_of_birth" groups="hr.group_hr_user"/>
                <field name="residence_place" groups="hr.group_hr_user"/>
                <field name="city" groups="hr.group_hr_user"/>
                <field name="neighborhood" groups="hr.group_hr_user"/>

                <field name="street_name" groups="hr.group_hr_user"/>
                <field name="closest_point" groups="hr.group_hr_user"/>

            </xpath>
            <xpath expr="//field[@name='place_of_birth']" position="replace">
                <field name="place_of_birth" invisible="1" groups="hr.group_hr_user"/>
            </xpath>
            <xpath expr="//field[@name='study_field']" position="replace">
                <!--<field name="study_field"/>-->
                <field name="study_field_edited"/>
            </xpath>
            <xpath expr="//field[@name='barcode']" position="replace">
                <field name="barcode" placeholder="yy-xxxx"/>
            </xpath>
            <xpath expr="//field[@name='coach_id']" position="replace">
                <field name="coach_id" string="supervisor"/>
            </xpath>
            <!--            <xpath expr="//group[@name='identification_group']" position="after">-->
            <!--                <group>-->
            <!--                    <field name="start_work" placeholder="hh:mm:ss"/>-->
            <!--                    <field name="end_work" placeholder="hh:mm:ss"/>-->
            <!--                </group>-->
            <!--            </xpath>-->
            <xpath expr="//field[@name='emergency_contact']" position="replace">
                <field name="name_emergency1"/>
                <field name="phone_emergency1"/>
                <field name="relation_emergency1"/>
                <field name="address_emergency1"/>
                <field name="name_emergency2"/>
                <field name="phone_emergency2"/>
                <field name="relation_emergency2"/>
                <field name="address_emergency2"/>
            </xpath>
            <xpath expr="//field[@name='emergency_phone']" position="replace">
                <field name="emergency_phone" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='visa_expire']" position="after">
                <field name="health_certificate"/>
                <label for="health_certificate_date" id="label_dates"/>
                <div>
                    <div class="o_row">
                        <span class="oe_inline">From</span>
                        <field name="health_certificate_date" class="oe_inline" nolabel="1"/>
                        <span class="oe_inline">To</span>
                        <field name="health_certificate_expiry" class="oe_inline"/>
                    </div>
                </div>
                <field name="criminal_certificate"/>
                <label for="criminal_certificate_date" id="label_dates"/>
                <div>
                    <div class="o_row">
                        <span class="oe_inline">From</span>
                        <field name="criminal_certificate_date" class="oe_inline" nolabel="1"/>
                        <span class="oe_inline">To</span>
                        <field name="criminal_certificate_expiry" class="oe_inline"/>
                    </div>
                </div>
            </xpath>
            <xpath expr="//field[@name='study_school']" position="after">
                <field name="certificate_issue_date"/>
                <field name="certificate_issue_place"/>
            </xpath>
            <xpath expr="/form/sheet/notebook/page[3]" position="after">
                <page string="Asset">
                    <field name="grants_ids">
                        <tree editable="bottom">
                            <field name="name"/>
                            <field name="category_id" string="Asset Category"/>
                            <field name="date"/>
                            <field name="value_residual" widget="monetary"/>
                            <field name="state"/>
                        </tree>
                    </field>
                </page>
                <page string="Document">
                    <group string="Document">

                        <field name="hiring_decision"/>
                        <field name="annual_contract_document"/>
                        <field name="graduation_certificate_document"/>
                        <field name="employment_requist_document"/>
                        <field name="last_financial_allowance"/>
                        <field name="last_annual_dromotion"/>
                         
                        <field name="id_document"/>
                        <field name="family_state_certificate"/>
                        <field name="last_secondment_decision"/>
                        <field name="non_duality_certificate_document"/>
                        <field name="resignation_letter_document"/>
                        <field name="criminal_certificate_document"/>
                        <field name="personal_photo_document"/>
                        <field name="non_employment_affidavit_document"/>
                        <field name="kye_document"/>


                        <!--<field name="passport_document"/>-->

                        
                        <!--<field name="residence_doc"/>-->
                        
                        
                        
                        <!--<field name="criminal_certificate_document"/>-->
                        <!--<field name="health_certificate_document"/>-->
                        <!--<field name="national_number_document"/>-->
                        <!--<field name="blood_type_doc"/>-->
                        
                        <!--<field name="previos_experiance_document"/>-->
                        <!--<field name="cv_document"/>-->
                        <!--<field name="bank_account_document"/>-->
                        <!--<field name="job_title_document"/>-->
                        
                        <!--<field name="computer_logs_document"/>-->
                        <!--<field name="nda"/>-->
                        <!--<field name="attendance_finger_print_document" widget="checkbox"/>-->
                        <!--<field name="coworkers_demonstration_document" widget="checkbox"/>-->
                        <!--<field name="trail_contract_document"/>-->
                       
                        <!--<field name="drive_lisence_document"/>-->

                        <!--<field name="company_car_lisence_document"/>-->

                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <record id="inherit_partner_bank_form" model="ir.ui.view">
        <field name="name">inherit_partner_bank_form</field>
        <field name="model">res.partner.bank</field>
        <field name="inherit_id" ref="base.view_partner_bank_form"/>
        <field name="arch" type="xml">
            <field name="bank_id" position="attributes">
                <attribute name="options">{'no_create_edit': True, 'no_quick_create': True,'no_open':True}</attribute>
            </field>
            <xpath expr="//field[@name='bank_id']" position="after">
                <field name="bank_branch" options="{'no_create_edit': True, 'no_quick_create': True,'no_open':True}"/>
            </xpath>
            <xpath expr="//field[@name='acc_holder_name']" position="replace">
                <field name="acc_holder_name" invisible="1"/>
            </xpath>

            <xpath expr="/form/sheet/group" position="after">
                <div>
                    <h4>
                        في حالة الحساب ليس باسم الموظف, يرجى إدخال المعلومات التالية
                    </h4>
                </div>
                <group>
                    <group>
                        <field name="acc_holder_name" string="اسم صاحب الحساب" placeholder="اسم صاحب الحساب"/>
                    </group>
                    <group>
                        <field name="acc_holder_NN" string="الرقم الوطني لصاحب الحساب" placeholder="الرقم الوطني"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <record id="hr_hr_employee_view_form2_inherit" model="ir.ui.view">
        <field name="name">hr.hr.employee.view.form2.x1</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr_contract.hr_hr_employee_view_form2"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='bank_account_id']" position="replace">
                <field name="bank_account_id"/>
            </xpath>
        </field>
    </record>


</odoo>

