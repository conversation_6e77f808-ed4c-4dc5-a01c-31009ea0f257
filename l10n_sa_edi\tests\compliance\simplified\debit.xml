<Invoice xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2">
  <cbc:UBLVersionID>2.1</cbc:UBLVersionID>
  <cbc:ProfileID>reporting:1.0</cbc:ProfileID>
  <cbc:ID>INV/2023/00035</cbc:ID>
  <cbc:UUID>47fdc8c6-2346-460a-8231-c28bf3bab44c</cbc:UUID>
  <cbc:IssueDate>2023-03-10</cbc:IssueDate>
  <cbc:IssueTime>15:01:46</cbc:IssueTime>
  <cbc:InvoiceTypeCode name="0200000">383</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>SAR</cbc:DocumentCurrencyCode>
  <cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>
  <cbc:BuyerReference>Mohammed Ali</cbc:BuyerReference>
  <cac:OrderReference>
    <cbc:ID>Test</cbc:ID>
  </cac:OrderReference>
  <cac:BillingReference>
    <cac:InvoiceDocumentReference>
      <cbc:ID>INV/2023/00034</cbc:ID>
    </cac:InvoiceDocumentReference>
  </cac:BillingReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>QR</cbc:ID>
    <cac:Attachment>
      <cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">N/A</cbc:EmbeddedDocumentBinaryObject>
    </cac:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>PIH</cbc:ID>
    <cac:Attachment>
      <cbc:EmbeddedDocumentBinaryObject mimeCode="text/plain">NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ==</cbc:EmbeddedDocumentBinaryObject>
    </cac:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>ICV</cbc:ID>
    <cbc:UUID>0</cbc:UUID>
  </cac:AdditionalDocumentReference>
  <cac:Signature>
    <cbc:ID>urn:oasis:names:specification:ubl:signature:Invoice</cbc:ID>
    <cbc:SignatureMethod>urn:oasis:names:specification:ubl:dsig:enveloped:xades</cbc:SignatureMethod>
  </cac:Signature>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="CRN">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>SA Company Test</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
        <cbc:BuildingNumber>1234</cbc:BuildingNumber>
        <cbc:PlotIdentification>1234</cbc:PlotIdentification>
        <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
        <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
        <cbc:PostalZone>42317</cbc:PostalZone>
        <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
        <cac:Country>
          <cbc:IdentificationCode>SA</cbc:IdentificationCode>
          <cbc:Name>Saudi Arabia</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>SA Company Test</cbc:RegistrationName>
        <cbc:CompanyID>311111111111113</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
          <cbc:BuildingNumber>1234</cbc:BuildingNumber>
          <cbc:PlotIdentification>1234</cbc:PlotIdentification>
          <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
          <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
          <cbc:PostalZone>42317</cbc:PostalZone>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>SA Company Test</cbc:RegistrationName>
        <cbc:CompanyID>311111111111113</cbc:CompanyID>
        <cac:RegistrationAddress>
          <cbc:StreetName>Al Amir Mohammed Bin Abdul Aziz Street</cbc:StreetName>
          <cbc:BuildingNumber>1234</cbc:BuildingNumber>
          <cbc:PlotIdentification>1234</cbc:PlotIdentification>
          <cbc:CitySubdivisionName>Testomania</cbc:CitySubdivisionName>
          <cbc:CityName>&#1575;&#1604;&#1605;&#1583;&#1610;&#1606;&#1577; &#1575;&#1604;&#1605;&#1606;&#1608;&#1585;&#1577;</cbc:CityName>
          <cbc:PostalZone>42317</cbc:PostalZone>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>1</cbc:ID>
        <cbc:Name>SA Company Test</cbc:Name>
        <cbc:Telephone>+966 51 234 5678</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="MOM">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Mohammed Ali</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
        <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
        <cac:Country>
          <cbc:IdentificationCode>SA</cbc:IdentificationCode>
          <cbc:Name>Saudi Arabia</cbc:Name>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>Mohammed Ali</cbc:RegistrationName>
        <cac:RegistrationAddress>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Mohammed Ali</cbc:RegistrationName>
        <cac:RegistrationAddress>
          <cbc:CountrySubentity>Riyadh</cbc:CountrySubentity>
          <cbc:CountrySubentityCode>RYA</cbc:CountrySubentityCode>
          <cac:Country>
            <cbc:IdentificationCode>SA</cbc:IdentificationCode>
            <cbc:Name>Saudi Arabia</cbc:Name>
          </cac:Country>
        </cac:RegistrationAddress>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:ID>7</cbc:ID>
        <cbc:Name>Mohammed Ali</cbc:Name>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cbc:ActualDeliveryDate>2023-03-10</cbc:ActualDeliveryDate>
  </cac:Delivery>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode listID="UN/ECE 4461">1</cbc:PaymentMeansCode>
    <cbc:PaymentDueDate>2023-03-10</cbc:PaymentDueDate>
    <cbc:InstructionID>INV/2023/00035</cbc:InstructionID>
    <cbc:InstructionNote>More Burgers</cbc:InstructionNote>
    <cbc:PaymentID>INV/2023/00035</cbc:PaymentID>
  </cac:PaymentMeans>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="SAR">79.50</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="SAR">530.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="SAR">79.50</cbc:TaxAmount>
      <cbc:Percent>15.0</cbc:Percent>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>15.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="SAR">79.50</cbc:TaxAmount>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="SAR">530.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="SAR">530.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="SAR">609.50</cbc:TaxInclusiveAmount>
    <cbc:PrepaidAmount currencyID="SAR">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="SAR">609.50</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>170</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">2.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="SAR">530.00</cbc:LineExtensionAmount>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="SAR">79.50</cbc:TaxAmount>
      <cbc:RoundingAmount currencyID="SAR">609.50</cbc:RoundingAmount>
      <cac:TaxSubtotal>
        <cbc:TaxableAmount currencyID="SAR">530.00</cbc:TaxableAmount>
        <cbc:TaxAmount currencyID="SAR">79.50</cbc:TaxAmount>
        <cbc:Percent>15.0</cbc:Percent>
        <cac:TaxCategory>
          <cbc:ID>S</cbc:ID>
          <cbc:Percent>15.0</cbc:Percent>
          <cac:TaxScheme>
            <cbc:ID>VAT</cbc:ID>
          </cac:TaxScheme>
        </cac:TaxCategory>
      </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:Item>
      <cbc:Description>Burger</cbc:Description>
      <cbc:Name>Burger</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>15.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="SAR">265.0</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
</Invoice>
