<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="state_in_ot" model="res.country.state">
        <field name="name">Other Territory</field>
        <field name="code">IN_OT</field>
        <field name="country_id" ref="base.in"/>
        <field name="l10n_in_tin">97</field>
    </record>

    <record id="base.state_in_an" model="res.country.state">
        <field name="l10n_in_tin">35</field>
    </record>
    <record id="base.state_in_ap" model="res.country.state">
        <field name="l10n_in_tin">37</field>
    </record>
    <record id="base.state_in_ar" model="res.country.state">
        <field name="l10n_in_tin">12</field>
    </record>
    <record id="base.state_in_as" model="res.country.state">
        <field name="l10n_in_tin">18</field>
    </record>
    <record id="base.state_in_br" model="res.country.state">
        <field name="l10n_in_tin">10</field>
    </record>
    <record id="base.state_in_ch" model="res.country.state">
        <field name="l10n_in_tin">04</field>
    </record>
    <record id="base.state_in_cg" model="res.country.state">
        <field name="l10n_in_tin">22</field>
    </record>
    <record id="base.state_in_dn" model="res.country.state">
        <field name="l10n_in_tin">26</field>
    </record>
    <record id="base.state_in_dd" model="res.country.state">
        <field name="l10n_in_tin">25</field>
    </record>
    <record id="base.state_in_dl" model="res.country.state">
        <field name="l10n_in_tin">07</field>
    </record>
    <record id="base.state_in_ga" model="res.country.state">
        <field name="l10n_in_tin">30</field>
    </record>
    <record id="base.state_in_gj" model="res.country.state">
        <field name="l10n_in_tin">24</field>
    </record>
    <record id="base.state_in_hr" model="res.country.state">
        <field name="l10n_in_tin">06</field>
    </record>
    <record id="base.state_in_hp" model="res.country.state">
        <field name="l10n_in_tin">02</field>
    </record>
    <record id="base.state_in_jk" model="res.country.state">
        <field name="l10n_in_tin">01</field>
    </record>
    <record id="base.state_in_jh" model="res.country.state">
        <field name="l10n_in_tin">20</field>
    </record>
    <record id="base.state_in_ka" model="res.country.state">
        <field name="l10n_in_tin">29</field>
    </record>
    <record id="base.state_in_kl" model="res.country.state">
        <field name="l10n_in_tin">32</field>
    </record>
    <record id="base.state_in_ld" model="res.country.state">
        <field name="l10n_in_tin">31</field>
    </record>
    <record id="base.state_in_mp" model="res.country.state">
        <field name="l10n_in_tin">23</field>
    </record>
    <record id="base.state_in_mh" model="res.country.state">
        <field name="l10n_in_tin">27</field>
    </record>
    <record id="base.state_in_mn" model="res.country.state">
        <field name="l10n_in_tin">14</field>
    </record>
    <record id="base.state_in_ml" model="res.country.state">
        <field name="l10n_in_tin">17</field>
    </record>
    <record id="base.state_in_mz" model="res.country.state">
        <field name="l10n_in_tin">15</field>
    </record>
    <record id="base.state_in_nl" model="res.country.state">
        <field name="l10n_in_tin">13</field>
    </record>
    <record id="base.state_in_or" model="res.country.state">
        <field name="l10n_in_tin">21</field>
    </record>
    <record id="base.state_in_py" model="res.country.state">
        <field name="l10n_in_tin">34</field>
    </record>
    <record id="base.state_in_pb" model="res.country.state">
        <field name="l10n_in_tin">03</field>
    </record>
    <record id="base.state_in_rj" model="res.country.state">
        <field name="l10n_in_tin">08</field>
    </record>
    <record id="base.state_in_sk" model="res.country.state">
        <field name="l10n_in_tin">11</field>
    </record>
    <record id="base.state_in_tn" model="res.country.state">
        <field name="l10n_in_tin">33</field>
    </record>
    <record id="base.state_in_ts" model="res.country.state">
        <field name="l10n_in_tin">36</field>
    </record>
    <record id="base.state_in_tr" model="res.country.state">
        <field name="l10n_in_tin">16</field>
    </record>
    <record id="base.state_in_up" model="res.country.state">
        <field name="l10n_in_tin">09</field>
    </record>
    <record id="base.state_in_uk" model="res.country.state">
        <field name="l10n_in_tin">05</field>
    </record>
    <record id="base.state_in_wb" model="res.country.state">
        <field name="l10n_in_tin">19</field>
    </record>

</odoo>
