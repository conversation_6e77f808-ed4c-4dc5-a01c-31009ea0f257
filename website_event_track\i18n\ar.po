# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "\"Events App Name\" field is required."
msgstr "حقل \"اسم تطبيق الفعاليات\" مطلوب. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "عدد العناصر في قائمة الأمنيات "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s from %(company)s"
msgstr "%(name)s من %(company)s "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s، %(function)s في %(company)s "

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "%s Events"
msgstr "%s الفعاليات "

#. module: website_event_track
#: code:addons/website_event_track/controllers/webmanifest.py:0
#, python-format
msgid "%s Online Events Application"
msgstr "%s تطبيق الفعاليات عبر الإنترنت "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "&amp;bull;"
msgstr "&amp;bull;"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "10 أفكار أثاث يمكنك القيام بها بنفسك للمبتدئين "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "6 نصائح وطرق للأعمال الخشبية للمبتدئين "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "<b>Contact Information</b>"
msgstr "<b>معلومات التواصل</b> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>البريد</b>: "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>الهاتف</b>: "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>مقترح من قِبَل</b>: "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>سيرة المتحدث الذاتية</b>: "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Speaker Profile</b>"
msgstr "<b>ملف المتحدث التعريفي</b> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Talk Intro</b>"
msgstr "<b>مقدمة الحوار</b> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>مقدمة الحوار</b>: "

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    عزيزي <t t-out=\"object.partner_id.name or object.partner_name or ''\">براندن فريمان</t><br/>\n"
"    يسعدنا إخبارك بأن مقترحك <t t-out=\"object.name or ''\">محور هذه الفعالية</t> قد تم قبوله وتأكيده للفعالية <t t-out=\"object.event_id.name or ''\">الكشف عن مجموعة OpenWood عبر الإنترنت</t>.\n"
"    <br/>\n"
"    ستجد المزيد من التفاصيل هنا:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            عرض الحوار\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    شكراً لك،\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>ميتشل آدمن</t>\n"
"    </t>\n"
"</div>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>غير منشور "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mr-2 text-muted\"/> Favorite Talks"
msgstr "<i class=\"fa fa-bell mr-2 text-muted\"/> الحوارات المفضلة "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "<i class=\"fa fa-folder-open\"/> Favorites"
msgstr "<i class=\"fa fa-folder-open\"/> المفضلة "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block ml-2\">&amp;bull;</span>"
msgstr "<span class=\"d-none d-md-block ml-2\">&amp;bull;</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"القيم التي تعين هنا تحدد حسب "
"الموقع.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"
msgstr ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"خطأ \"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">مقدمة الحوار</span>\n"
"                                            <span>*</span> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">عنوان الحوار</span>\n"
"                                            <span>*</span> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""
"<span for=\"contact_name\">الاسم</span>\n"
"                    <span>*</span> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"mr-1\">0</span>Results"
msgstr "<span id=\"search_number\" class=\"mr-1\">0</span> نتائج "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span>&amp;nbsp;at&amp;nbsp;</span>"
msgstr "<span>&amp;nbsp;at&amp;nbsp;</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr "<span>مقترح مسار جديد</span> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "<span>hours</span>"
msgstr "<span>ساعات</span> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""
"<strong>الحوارات السريعة</strong>. إنها حوارات مدتها 30 دقيقة تتمحور حول الكثير من\n"
"                                    المواضيع المختلفة. معظم المواضيع تصلح لأن تكون إحدى الحوارات السريعة."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>الموقع:</strong> "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<strong>الحوارات العادية</strong>. إنها الحوارات العادية مع العروض التقديمية،\n"
"                                    مخصصة للحوارات التي تستغرق أكثر من 60 دقيقة. "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr "تفسير تقني لكيفية استخدام تطبيقات التصميم على الكمبيوتر "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"تشير حالة كانبان لمسار ما إلى الأوضاع التي تؤثر فيه:\n"
" * الرمادي هو الوضع الافتراضي\n"
" * يشير الأحمر إلى وجود شيء يعيق تقدم هذا المسار\n"
" * يشير الأخضر إلى أن المسار جاهز ليتم سحبه إلى المرحلة التالية "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "يتطلب اتخاذ إجراء "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "نشط"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""
"أضف وصفاً لمساعدة زملائك في العمل على فهم المعنى والمغزى من تلك المرحلة. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "إضافة وصف..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "إضافة مرحلة جديدة في مخطط سير عمل المهمة "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr ""
"قم بإضافة علامات تصنيف لمساراتك لتساعد حاضريك على تصفح صفحات الويب "
"لفعالياتك. "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr "إدارة العملاء المهتمين المتقدمة: النصائح والطرق من الحقول "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "إعداد التقارير المتقدمة "

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Agenda"
msgstr "جدول أعمال"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "كافة الحوارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr "السماح بمقترحات المسارات "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Allow push notifications?"
msgstr "السماح بالإشعارات المنبثقة؟ "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""
"السماح بتسجيل مقاطع الفيديو والصوت لعرضهم\n"
"                                    التقديمي، لنشره على موقعنا الإلكتروني. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "مدرج دائماً في قائمة الأمنيات "

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "معلن عنه "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Application"
msgstr "التطبيق "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "مؤرشف"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""
"بما أن المسار الرئيسي لا يمكن إزالته من المفضلة، يخزن هذا الحقل اختيار "
"الشريك لإزالة التذكير للمسارات الرئيسية. "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""
"كما سمعت سابقاً، فإن صنع أثاثك بنفسك ليس صعباً أو معقداً كما كنت تظن.\n"
"    في الحقيقة، بعض قطع الأثاث سهلة جداً لدرجة أن أياً كان بإمكانه صنعها. على سبيل المثال، صنع مقعد لطيف من\n"
"    إطار قديم أسهل مما تتصور، وإذا كنت تحتاج إلى طاولة قهوة، بإمكانك صنع واحدة بكل سهولة باستخدام\n"
"    صناديق الخشب. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "الجمهور "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "علامات تصنيف المسار المتاحة "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr "حيلة Bandy clamp "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "السيرة الذاتية"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_blocked:website_event_track.event_7_track_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_12
#: model:event.track,legend_blocked:website_event_track.event_7_track_13
#: model:event.track,legend_blocked:website_event_track.event_7_track_14
#: model:event.track,legend_blocked:website_event_track.event_7_track_15
#: model:event.track,legend_blocked:website_event_track.event_7_track_16
#: model:event.track,legend_blocked:website_event_track.event_7_track_17
#: model:event.track,legend_blocked:website_event_track.event_7_track_18
#: model:event.track,legend_blocked:website_event_track.event_7_track_19
#: model:event.track,legend_blocked:website_event_track.event_7_track_2
#: model:event.track,legend_blocked:website_event_track.event_7_track_20
#: model:event.track,legend_blocked:website_event_track.event_7_track_21
#: model:event.track,legend_blocked:website_event_track.event_7_track_22
#: model:event.track,legend_blocked:website_event_track.event_7_track_23
#: model:event.track,legend_blocked:website_event_track.event_7_track_24
#: model:event.track,legend_blocked:website_event_track.event_7_track_25
#: model:event.track,legend_blocked:website_event_track.event_7_track_26
#: model:event.track,legend_blocked:website_event_track.event_7_track_3
#: model:event.track,legend_blocked:website_event_track.event_7_track_4
#: model:event.track,legend_blocked:website_event_track.event_7_track_5
#: model:event.track,legend_blocked:website_event_track.event_7_track_6
#: model:event.track,legend_blocked:website_event_track.event_7_track_7
#: model:event.track,legend_blocked:website_event_track.event_7_track_8
#: model:event.track,legend_blocked:website_event_track.event_7_track_9
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_2
#: model:event.track,legend_blocked:website_event_track.event_track1
#: model:event.track,legend_blocked:website_event_track.event_track10
#: model:event.track,legend_blocked:website_event_track.event_track11
#: model:event.track,legend_blocked:website_event_track.event_track12
#: model:event.track,legend_blocked:website_event_track.event_track13
#: model:event.track,legend_blocked:website_event_track.event_track14
#: model:event.track,legend_blocked:website_event_track.event_track15
#: model:event.track,legend_blocked:website_event_track.event_track16
#: model:event.track,legend_blocked:website_event_track.event_track17
#: model:event.track,legend_blocked:website_event_track.event_track18
#: model:event.track,legend_blocked:website_event_track.event_track19
#: model:event.track,legend_blocked:website_event_track.event_track2
#: model:event.track,legend_blocked:website_event_track.event_track20
#: model:event.track,legend_blocked:website_event_track.event_track21
#: model:event.track,legend_blocked:website_event_track.event_track22
#: model:event.track,legend_blocked:website_event_track.event_track23
#: model:event.track,legend_blocked:website_event_track.event_track24
#: model:event.track,legend_blocked:website_event_track.event_track25
#: model:event.track,legend_blocked:website_event_track.event_track27
#: model:event.track,legend_blocked:website_event_track.event_track28
#: model:event.track,legend_blocked:website_event_track.event_track29
#: model:event.track,legend_blocked:website_event_track.event_track3
#: model:event.track,legend_blocked:website_event_track.event_track30
#: model:event.track,legend_blocked:website_event_track.event_track31
#: model:event.track,legend_blocked:website_event_track.event_track4
#: model:event.track,legend_blocked:website_event_track.event_track5
#: model:event.track,legend_blocked:website_event_track.event_track6
#: model:event.track,legend_blocked:website_event_track.event_track7
#: model:event.track,legend_blocked:website_event_track.event_track8
#: model:event.track,legend_blocked:website_event_track.event_track9
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
#, python-format
msgid "Blocked"
msgstr "محجوب"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "احجز مقاعدك لأفضل الحوارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "احجز حواراتك "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "قم ببناء خزانة بنفسك من البداية إلى النهاية "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "زر رابط URL المستهدف "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "عنوان الزر "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Button appears"
msgstr "يظهر الزر "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "زر الدعوة إلى اتخاذ إجراء متاح "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "الدعوة إلى المقترحات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr "المرحلة الملغية "

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "ملغي"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "الفئات"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "الفئة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "المناخ الإيجابي "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "اللون"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: website_event_track
#: code:addons/website_event_track/controllers/event_track.py:0
#, python-format
msgid "Coming soon"
msgstr "قريباً "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "قريباً... "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "الشركة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "اسم الشركة "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr "تأكيد {{ object.name }} "

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "تم التأكيد "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"أتفكر في بناء منزل خشبي؟ شاهد هذا الفيديو لمعرفة المزيد من المعلومات عن "
"عملية البناء والنتائج النهايئة. شرح بسيط خطوة بخطوة! أأنت مهتم؟ "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
#, python-format
msgid "Contact"
msgstr "جهة الاتصال"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "تفاصيل جهة الاتصال"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
#, python-format
msgid "Contact Email"
msgstr "البريد الإلكتروني لجهة الاتصال"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "رقم الهاتف "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_email
msgid "Contact email is private and used internally"
msgstr "البريد الإلكتروني لجهة الاتصال خاص ويُستخدم داخلياً فقط "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "تواصلوا معي عن طريق بريد إلكتروني/رقم هاتف آخر "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_id
msgid "Contact of the track, may be different from speaker."
msgstr "يمكن أن تختلف جهة الاتصال للمسار عن المتحدث. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_phone
msgid "Contact phone is private and used internally"
msgstr "رقم هاتف جهة الاتصال خاص ويُستخدم داخلياً فقط "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr "إنشاء مسار "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr "إنشاء موقع المسار "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr "إنشاء علامة تصنيف للمسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "العميل"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "مشروع كسوة الخشب يمكنك القيام به بنفسك "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "التاريخ"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "نهاية اليوم الثاني "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "التعامل مع خشب OpenWood "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "قم بتحديد علامات التصنيف التي تشرح إدارة حالة كانبان. "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"قم بتحديد الخطوات التي سيتم استخدامها في\n"
"            الفعالية منذ إنشاء المسار، وحتى إغلاقه.\n"
"            ستستخدم هذه المراحل لتتبع التقدم في \n"
"            حل مسار فعالية. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "حذف"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "الوصف"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "وصف الشريك (الاسم، الوظيفة، واسم الشركة) "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "مسابقة التصميم (فترة ما بعد الظهيرة بأكملها) "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "مسابقة التصميم (اليوم بأكمله) "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "خريطة الطريق المفصلة لمنتجاتنا الجديدة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "استكشف فريق التصميم الجديد لدينا "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr "قم بعرض زر الدعوة إلى اتخاذ إجراء لحاضريك بينما يشاهدون مساراتك. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "منتهي "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr "حيلة العمود "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr "القائمة المنسدلة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "المدة"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "طريقة سهلة لبناء منزل خشبي "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "تحرير المسارات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "قالب البريد الإلكتروني"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Error"
msgstr "خطأ"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "الفعالية"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "موقع الفعالية"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "مواقع الفعاليات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "قوائم مقترحات الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "قالب الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "مسار الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "موقع مسار الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "مرحلة مسار الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "علامة تصنيف مسار الفعالية "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "فئة علامة تصنيف مسار الفعالية "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "مسارات الفعالية "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "قوائم مسارات الفعالية "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "نهاية الفعالية "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr "اسم تطبيق الفعاليات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr "تطبيق الويب التقدمي للفعاليات "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Favorite On"
msgstr "مفضلة في "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "المفضلات"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks..."
msgstr "تصفية المسارات... "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "مُنتهي"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "نهاية اليوم الأول "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "مطوي في عرض كانبان"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة Font awesome مثال fa-tasks "

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "التنسيق "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "يمكن الوصول إليه بالكامل "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "تجهّز و"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr "نصيحة لاستخدام الغراء "

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "أخضر"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "بطاقة عنوان كانبان خضراء"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "رمادي"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "بطاقة عنوان كانبان رمادية"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr "سعيد مع OpenWood "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "مرتفع"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "الأعلى "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "الصفحة الرئيسية "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "بيت ثقافات العالم "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "كيف لفريقنا التواصل معك؟ "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr "كيفية بناء استراتيجيتك للتسويق في بيئة تنافسية "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "كيفية التواصل مع مجتمعك "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "كيفية تصميم قطع أثاث جديدة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "كيفية تطوير العمليات المؤتمتة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "كيفية متابعتنا على مواقع التواصل الاجتماعي "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "كيفية تحسين عمليات الجودة لديك "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "كيفية دمج المواد الحديدية في قطع أثاثك "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr "كيفية تحسين مبيعاتك، من العملاء المهتمين إلى أوامر البيع "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "المُعرف"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى استثناء النشاط"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr ""
"إذا كان محدداً، قم بنشر المسارات تلقائياً حتى يتم تقديم روابط الوصول إلى "
"العملاء. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr "إذا كان محدداً، ستكون المسارات ذات الصلة مرئية في الواجهة الأمامية. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""
"إذا كان محدداً، سيتم إرسال رسالة بريد الكتروني إلى العميل عند وصول المسار "
"إلى هذه الخطوة. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr ""
"إذا كان محدداً، سيتم تعيين الحوار كالحوار المفضل لكل حاضر مسجل في الفعالية. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "رابط URL للصورة "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr "في"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_normal:website_event_track.event_7_track_1
#: model:event.track,legend_normal:website_event_track.event_7_track_10
#: model:event.track,legend_normal:website_event_track.event_7_track_11
#: model:event.track,legend_normal:website_event_track.event_7_track_12
#: model:event.track,legend_normal:website_event_track.event_7_track_13
#: model:event.track,legend_normal:website_event_track.event_7_track_14
#: model:event.track,legend_normal:website_event_track.event_7_track_15
#: model:event.track,legend_normal:website_event_track.event_7_track_16
#: model:event.track,legend_normal:website_event_track.event_7_track_17
#: model:event.track,legend_normal:website_event_track.event_7_track_18
#: model:event.track,legend_normal:website_event_track.event_7_track_19
#: model:event.track,legend_normal:website_event_track.event_7_track_2
#: model:event.track,legend_normal:website_event_track.event_7_track_20
#: model:event.track,legend_normal:website_event_track.event_7_track_21
#: model:event.track,legend_normal:website_event_track.event_7_track_22
#: model:event.track,legend_normal:website_event_track.event_7_track_23
#: model:event.track,legend_normal:website_event_track.event_7_track_24
#: model:event.track,legend_normal:website_event_track.event_7_track_25
#: model:event.track,legend_normal:website_event_track.event_7_track_26
#: model:event.track,legend_normal:website_event_track.event_7_track_3
#: model:event.track,legend_normal:website_event_track.event_7_track_4
#: model:event.track,legend_normal:website_event_track.event_7_track_5
#: model:event.track,legend_normal:website_event_track.event_7_track_6
#: model:event.track,legend_normal:website_event_track.event_7_track_7
#: model:event.track,legend_normal:website_event_track.event_7_track_8
#: model:event.track,legend_normal:website_event_track.event_7_track_9
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_1
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_10
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_11
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_2
#: model:event.track,legend_normal:website_event_track.event_track1
#: model:event.track,legend_normal:website_event_track.event_track10
#: model:event.track,legend_normal:website_event_track.event_track11
#: model:event.track,legend_normal:website_event_track.event_track12
#: model:event.track,legend_normal:website_event_track.event_track13
#: model:event.track,legend_normal:website_event_track.event_track14
#: model:event.track,legend_normal:website_event_track.event_track15
#: model:event.track,legend_normal:website_event_track.event_track16
#: model:event.track,legend_normal:website_event_track.event_track17
#: model:event.track,legend_normal:website_event_track.event_track18
#: model:event.track,legend_normal:website_event_track.event_track19
#: model:event.track,legend_normal:website_event_track.event_track2
#: model:event.track,legend_normal:website_event_track.event_track20
#: model:event.track,legend_normal:website_event_track.event_track21
#: model:event.track,legend_normal:website_event_track.event_track22
#: model:event.track,legend_normal:website_event_track.event_track23
#: model:event.track,legend_normal:website_event_track.event_track24
#: model:event.track,legend_normal:website_event_track.event_track25
#: model:event.track,legend_normal:website_event_track.event_track27
#: model:event.track,legend_normal:website_event_track.event_track28
#: model:event.track,legend_normal:website_event_track.event_track29
#: model:event.track,legend_normal:website_event_track.event_track3
#: model:event.track,legend_normal:website_event_track.event_track30
#: model:event.track,legend_normal:website_event_track.event_track31
#: model:event.track,legend_normal:website_event_track.event_track4
#: model:event.track,legend_normal:website_event_track.event_track5
#: model:event.track,legend_normal:website_event_track.event_track6
#: model:event.track,legend_normal:website_event_track.event_track7
#: model:event.track,legend_normal:website_event_track.event_track8
#: model:event.track,legend_normal:website_event_track.event_track9
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
#, python-format
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr "في هذا الفيديو، سنرى طريقة صنع الخشب في مصنع النشارة. "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr "في هذا الفيديو، قمت بتغطية 6 نصائح وطرق لمساعدة المبتدئين: "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install"
msgstr "تثبيت"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install Application"
msgstr "تثبيت التطبيق "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "التفاعلية "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "المقدمة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr "الدعوة إلى اتخاذ إجراء نشطة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "تم نشره "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr "تم تشغيل التذكير "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr "انتهى المسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr "المسار يتم بثه مباشرة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr "سيتم التشغيل قريباً "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr "سيتم التشغيل اليوم "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr "المسار القادم "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "مدرج في قائمة الأمنيات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr "تم إغلاق التذكير "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "المسمى الوظيفي"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "تفسير حالة محجوب في واجهة كانبان "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "تفسير حالة \"جاري\" في واجهة كانبان "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "حالة كانبان"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "بطاقة عنوان حالة كانبان"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "تفسير حالة \"صالح\" في واجهة كانبان "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "عوامل النجاح الأساسية عند بيع أثاثنا "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "مطابخ للمستقبل "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "أحدث الاتجاهات "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "البساطة هي الخيار الأمثل دوماً "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "الحياة في المنزل في شتى أنحاء العالم: قصة ويليام "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""
"إليكم الرابط إلى الأسئلة والأجوبة! حان الوقت لمحو جدران الطابوق القديمة تلك."
" أعشق مشاريع التحويل البسيطة كهذه كثيراً! :)- "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "مباشر "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr "ينقل بثاً مباشراً الآن "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr "التزكية المباشرة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr "التزكيات المباشرة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "الموقع "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "بناء المنزل من الأجذاع الخشبية "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "من الأجذاع الخشبية وإلى قطع الخشب "

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "منخفض"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "الغداء"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "الزر السحري "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr "صنع مقياس لتحديد الوسط "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr ""
"قم بإدارة الأماكن التي تقوم بتنظيم المسارات فيها من هنا (مثال: الغرف، "
"القنوات،...). "

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "وسط"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "نوع القائمة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "تصميم بسيط ولكن عملي "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "الدقائق قبل بدء الدعوة إلى اتخاذ إجراء "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr "دقائق قبل أن يبدأ المسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr "المقارنة دقائق من بدء المسار "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "نصيحة لاستخدام المنشار المتري "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "استراحة الصباح "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "نهاية الوقت المعين للنشاط"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "العرض التقديمي الدولي لشركتي "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "مساراتي "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "الاسم"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr "اسم تطبيق الويب المتطور لفعاليات موقعك الإلكتروني "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "برنامج شهادة جديد "

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "مسار جديد "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr "لا يوجد زوار لمسارك بعد! "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "No results found"
msgstr "لم يتم العثور على نتائج "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr "لم يتم وضع أي مسارات في المفضلة بواسطة هذا الزائر "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr "لم يتم العثور على مسار. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr ""
"يرجى العلم بأن علامات التصنيف بلا لون لن تكون متاحة في الموقع الإلكتروني. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل غير المقروءة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "القديم جديد "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together !"
msgstr "آخر يوم لنا معاً! "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "برامج الشراكة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "رقم الهاتف"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "صورة"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr "قم بالتخطيط لتجربتك عن طريق إضافة حواراتك المفضلة إلى قائمة أمنياتك "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please enter either a contact email address or a contact phone number."
msgstr "يرجى إدخال عنوان بريد إلكتروني أو رقم هاتف للتواصل. "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "يرجى تعبئة الاستمارة بشكل صحيح. "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "العرض التقديمي للحافظة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "جميل. قبيح. رائع. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "الصفحة السابقة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "الأولوية"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""
"قد يكون أحد أكثر الأسئلة سؤالاً حتى الآن هو كيف بدأت بتعلم الصناعات الخشبية!"
" في هذا الفيديو سأشارك معكم كيف ولماذا بدأت بصنع الأثاث! "

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "المقترح"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr "المقترحات مغلقة! "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "المقترحات في الموقع الإلكتروني "

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
msgid "Published"
msgstr "تم النشر "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "جمع وجهات نظر قيّمة من عملائك "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_done:website_event_track.event_7_track_1
#: model:event.track,legend_done:website_event_track.event_7_track_10
#: model:event.track,legend_done:website_event_track.event_7_track_11
#: model:event.track,legend_done:website_event_track.event_7_track_12
#: model:event.track,legend_done:website_event_track.event_7_track_13
#: model:event.track,legend_done:website_event_track.event_7_track_14
#: model:event.track,legend_done:website_event_track.event_7_track_15
#: model:event.track,legend_done:website_event_track.event_7_track_16
#: model:event.track,legend_done:website_event_track.event_7_track_17
#: model:event.track,legend_done:website_event_track.event_7_track_18
#: model:event.track,legend_done:website_event_track.event_7_track_19
#: model:event.track,legend_done:website_event_track.event_7_track_2
#: model:event.track,legend_done:website_event_track.event_7_track_20
#: model:event.track,legend_done:website_event_track.event_7_track_21
#: model:event.track,legend_done:website_event_track.event_7_track_22
#: model:event.track,legend_done:website_event_track.event_7_track_23
#: model:event.track,legend_done:website_event_track.event_7_track_24
#: model:event.track,legend_done:website_event_track.event_7_track_25
#: model:event.track,legend_done:website_event_track.event_7_track_26
#: model:event.track,legend_done:website_event_track.event_7_track_3
#: model:event.track,legend_done:website_event_track.event_7_track_4
#: model:event.track,legend_done:website_event_track.event_7_track_5
#: model:event.track,legend_done:website_event_track.event_7_track_6
#: model:event.track,legend_done:website_event_track.event_7_track_7
#: model:event.track,legend_done:website_event_track.event_7_track_8
#: model:event.track,legend_done:website_event_track.event_7_track_9
#: model:event.track,legend_done:website_event_track.event_7_track_l3_1
#: model:event.track,legend_done:website_event_track.event_7_track_l3_10
#: model:event.track,legend_done:website_event_track.event_7_track_l3_11
#: model:event.track,legend_done:website_event_track.event_7_track_l3_2
#: model:event.track,legend_done:website_event_track.event_track1
#: model:event.track,legend_done:website_event_track.event_track10
#: model:event.track,legend_done:website_event_track.event_track11
#: model:event.track,legend_done:website_event_track.event_track12
#: model:event.track,legend_done:website_event_track.event_track13
#: model:event.track,legend_done:website_event_track.event_track14
#: model:event.track,legend_done:website_event_track.event_track15
#: model:event.track,legend_done:website_event_track.event_track16
#: model:event.track,legend_done:website_event_track.event_track17
#: model:event.track,legend_done:website_event_track.event_track18
#: model:event.track,legend_done:website_event_track.event_track19
#: model:event.track,legend_done:website_event_track.event_track2
#: model:event.track,legend_done:website_event_track.event_track20
#: model:event.track,legend_done:website_event_track.event_track21
#: model:event.track,legend_done:website_event_track.event_track22
#: model:event.track,legend_done:website_event_track.event_track23
#: model:event.track,legend_done:website_event_track.event_track24
#: model:event.track,legend_done:website_event_track.event_track25
#: model:event.track,legend_done:website_event_track.event_track27
#: model:event.track,legend_done:website_event_track.event_track28
#: model:event.track,legend_done:website_event_track.event_track29
#: model:event.track,legend_done:website_event_track.event_track3
#: model:event.track,legend_done:website_event_track.event_track30
#: model:event.track,legend_done:website_event_track.event_track31
#: model:event.track,legend_done:website_event_track.event_track4
#: model:event.track,legend_done:website_event_track.event_track5
#: model:event.track,legend_done:website_event_track.event_track6
#: model:event.track,legend_done:website_event_track.event_track7
#: model:event.track,legend_done:website_event_track.event_track8
#: model:event.track,legend_done:website_event_track.event_track9
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
#, python-format
msgid "Ready for Next Stage"
msgstr "جاهزة للمرحلة المقبلة"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "أحمر"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "بطاقة عنوان كانبان حمراء"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "تم الرفض "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr "الوقت النسبي مقارنة ببدء المسار (بالثواني) "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr "تالوقت المتبقي قبل بدء الدعوة إلى اتخاذ إجراء (بالثواني) "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr "الوقت المتبقي قبل بدء المسار (بالثواني) "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "المسؤول "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr "ترميم أدوات الحرف الخشبية القديمة "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr "ترميم أدوات الحرف الخشبية القديمة "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr "مشبك الزاوية اليمنى "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "Schedule some tracks to get started"
msgstr "قم بجدولة بعض المسارات للبدء "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "وضع الخشب بشكل آمن أثناء النقل "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "Select categories"
msgstr "تحديد الفئات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "التسلسل "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Set Favorite"
msgstr "تعيين المفضلة "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr "عرض كافة السجلات المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr "عرض المسارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker"
msgstr "المتحدث "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "النبذة التعريفية للمتحدث "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speaker Email"
msgstr "البريد الإلكتروني للمتحدث "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "صورة المتحدث "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_email
msgid ""
"Speaker email is used for public display and may vary from contact email"
msgstr ""
"يُستخدم البريد الإلكتروني للمتحدث للعرض العام وقد يختلف عن البريد الإلكتروني"
" للتواصل "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_name
msgid "Speaker name is used for public display and may vary from contact name"
msgstr "يُستخدم اسم المتحدث للعرض العام وقد يختلف عن اسم التواصل "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_phone
msgid ""
"Speaker phone is used for public display and may vary from contact phone"
msgstr ""
"يُستخدم رقم الهاتف للمتحدث للعرض العام وقد يختلف عن رقم الهاتف للتواصل "

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speakers"
msgstr "المتحدثون "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "المرحلة"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "وصف وتلميحات المرحلة"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "اسم المرحلة"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "الحالة والاستراتيجية "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "اتفاقية التسليم "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "تسليم المقترح "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr "عبارة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "اسم علامة التصنيف "

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists !"
msgstr "اسم علامة التصنيف موجود بالفعل! "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "علامات التصنيف "

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Talk Proposals"
msgstr "مقترحات الحوارات "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk added to your Favorites"
msgstr "تمت إضافة الحوار إلى مفضلتك "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk already in your Favorites"
msgstr "الحوار موجود في مفضلتك بالفعل "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk removed from your Favorites"
msgstr "تمت إزالة الحوار من مفضلتك "

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#, python-format
msgid "Talks"
msgstr "الحوارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "أنواع الحوارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr "المهمة قيد التنفيذ. اضغط لحظرها أو تعيينها كمكتملة."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr "المهمة محظورة. اضغط لإلغاء حظرها أو تعيينها كمكتملة."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Thank you for your proposal."
msgstr "نشكرك على مقترحك. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "رابطURL الكامل للوصول إلى المستند من خلال الموقع الإلكتروني. "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "استراتيجية التسويق الجديدة "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "الطريقة الجديدة للترويج لاختراعاتك "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""
"توجد الكثير من الأفكار التي يجدر استكشافها عن طريق 10 أفكار لأثاث يمكنك صنعه"
" بنفسك للمبتدئين للغاية. "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""
"توجد العديد من الأصناف المتاحة من الخشب في العالم، ولكننا نتحدث عن الأصناف الأغلى\n"
"    في العالم، ولذلك قمنا بوضع أغلى عشر أصناف من الخشب. "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr "سيتم إنشاؤهم تلقائياً بمجرد أن يبدأ الحاضرون بتصفح فعالياتك. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr "لا تقبل هذه الفعالية المقترحات. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""
"يحتوي هذا الحقل على الصورة المستخدمة لعرض أيقونة تطبيق الهاتف المحمول في "
"الموقع الإلكتروني (صيغة PNG). "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr "يحتوي هذا الحقل على اسم تطبيق الويب المتطور لفعاليتك. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""
"لم يتم حفظ هذه الصفحة لقراءتها دون الاتصال بالإنترنت بعد.<br/> يرجى التحقق "
"من اتصالك بالإنترنت. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"يتم طي هذه المرحلة عند استخدام طريقة كانبان للعرض عندما لا توجد سجلات يمكن "
"عرضها في هذه المرحلة."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "هذه الخطوة مكتملة. اضغط لحجبها أو تعيينها كقيد التنفيذ. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""
"إصدار مواد العروض التقديمية في الوقت المناسب (الشرائح)\n"
"                                    لنشرها على موقعنا الإلكتروني. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "العنوان"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr "الأدوات للحرف الخشبية للمبتدئين "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr "أغلى 10 أنواع من الخشب في العالم "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""
"أغلى أنواع الأخشاب في العالم هو موضوع مثير للاهتمام حقاً وقد يتفاجأ الكثيرون من أنه\n"
"    توجد المئات من أنواع الأخشاب حول العالم ذات خواص واستخدامات مختلفة. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "المسار "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "المسار/رابط الزائر "

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr "تم حجب المسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr "عدد المسارات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "تاريخ المسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr "تاريخ انتهاء المسار "

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr "مواقع المسارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr "عنصر قائمة مقترحات المسارات "

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr "المسار جاهز "

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr "المسار جاهز للمرحلة التالية "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr "مرحلة المسار "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "مراحل المسار "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr "فئات علامات تصنيف المسار "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr "فئة علامة تصنيف المسار "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "علامات تصنيف المسارات "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr "فئة علامات تصنيف المسار "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "زائر المسار "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr "زائري المسار "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr ""
"يقوم زوار المسارات بتخزين الإحصائيات في فعالياتك، كعدد المرات التي تم وضع "
"المسارات فيها في قائمة الأمنيات. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_soon
msgid "Track begins soon"
msgstr "سيبدأ المسار قريباً "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_today
msgid "Track begins today"
msgstr "يبدأ المسار اليوم "

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr "المسار محجوب "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__duration
msgid "Track duration in hours."
msgstr "مدة المسار بالساعات. "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_live
msgid "Track has started and is ongoing"
msgstr "لقد بدأ المسار وهو جارٍ الآن "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_done
msgid "Track is finished"
msgstr "انتهى المسار "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_upcoming
msgid "Track is not yet started"
msgstr "لم يبدأ المسار بعد "

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Track: Confirmation"
msgstr "المسار: التأكيد "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "المسارات"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr "عنصر قائمة المسارات "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr "مقترحات المسارات في الموقع الإلكتروني "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr ""
"تحدد المسارات جدول فعاليتك. قد تكون حوارات، ورش عمل، أو أي نشاط مماثل. "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "المسارات في الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط الاستثنائي المسجل."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "غير منشور"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عداد الرسائل غير المقروءة "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""
"استخدم هذه الخطوات البسيطة لنقل قطع الأخشاب الطويلة في شاحنة نقل قصيرة. القليل من براعة\n"
"    النجار مع بعض الألواح الخشبية، أربطة متينة، والقليل من البراغي هي كل ما أستخدمه لنقل الألواح الطويلة\n"
"    من ساحة الخشب إلى المستوى التالي لمتجر النجارة أو موقع العمل. "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""
"استخدام طريقة ربط فريدة للأربطة (ليس الأربطة المطاطية!!!) يتيح لقطع الخشب أن تكون\n"
"    مربوطة بإحكام دون الحاجة إلى ربط وفك عقد صعبة أو معقدة. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "عرض المسار "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr "مرئي في جدول الأعمال "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "ظاهرة في الموقع الإلكتروني الحالي "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "الزائر"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "قائمة أمنيات الزائر "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "قائمة أمنيات الزوار "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "الصوت من العميل "

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr "انتظر أن يقوم الزوار بإضافة هذا المسار إلى قوائم المفضلة لديهم "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We did not find any track matching your"
msgstr "لم نتمكن من العثور على أي مسار مطابق لـ"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr "نحن نلزم المتحدثين بقبول اتفاقية يلتزمون فيها بـ: "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                            presentations, from reports on academic and\n"
"                            commercial projects to tutorials and case\n"
"                            studies. As long as the presentation is\n"
"                            interesting and potentially useful to the\n"
"                            audience, it will be considered for\n"
"                            inclusion in the programme."
msgstr ""
"سنقبل مجالاً واسعاً من \n"
"                            العروض التقديمية والتقارير الأكاديمية\n"
"                            والمشاريع التجارية للدروس التعليمية ودراسات\n"
"                            الحالات. طالما أن العرض التقديمي مثير\n"
"                            للاهتمام ولديه احتمالية أن يكون ذا فائدة\n"
"                            للجمهور، سيتم اعتباره ليتم\n"
"                            ضمه في البرنامج. "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "سنقوم بتقييم مقترحك والعودة إليك قريباً. "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "أيقونة تطبيق الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "قائمة فعالية الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "صورة الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "قائمة الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "رابط URL للموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "زائر الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية بالموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي بالموقع الإلكتروني "

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح للموقع الإلكتروني "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr "مرحباً بك في اليوم 2 "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "محور هذه الفعالية "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr "ما موضوع الحوار؟ "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr "من الذي سيلقي هذا الحوار؟ سنري ذلك لحاضريك لعرض حوارك. "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway ?"
msgstr "من هو OpenWood بأي حال؟ "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "مدرج في قائمة الأمنيات بواسطة "

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr "المسارات المدرجة في قائمة الأمنيات "

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr "الأعمال الخشبية: بدايتي! "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "You cannot access this page."
msgstr "لا يمكنك الوصول إلى هذه الصفحة. "

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr "عليك تمكين الإشعارات المنبثقة للحصول على التذكيرات لمساراتك المفضلة. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "أنت غير متصل بالإنترنت! "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "مضت "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now !"
msgstr "مثال: احصل على خاصتك الآن! "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "مثال: حوارات الأعمال الملهمة "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "مثال: http://www.example.com "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "ساعات"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after track starts"
msgstr "دقائق بعد بدء المسار "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr "قم بالتسجيل لحوارتك المفضلة الآن. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "search."
msgstr "البحث. "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "يبدأ في "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "يبدأ في "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "tracks"
msgstr "المسارات "
