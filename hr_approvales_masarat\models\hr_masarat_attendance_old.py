# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from pytz import timezone
from odoo import models, fields, api, exceptions, _
from odoo.tools import format_datetime


class HrAttendance(models.Model):
    _inherit = "hr.attendance"
    _description = "Attendance"

    attendance_date = fields.Date(string='Attendance Date', compute='_get_attendance_date', store=True)
    attendance_type_id = fields.Many2one('resource.calendar', related='employee_id.contract_id.resource_calendar_id',store=True, readonly=True)
    attendance_state= fields.Selection([('latency','Latency'),('approved','Approved Latency')], compute='_get_if_letancy', string='State', store=True)
    latency_minutes = fields.Float(string='Latency Min', compute='_get_if_letancy', default=0.0, store=True)
    computed_latency_minutes = fields.Float(string='Computed Latency Min', compute='_get_if_letancy', default=0.0)

    @api.depends('check_in')
    def _get_attendance_date(self):
        for elem in self:
            if elem.check_in:
                elem.attendance_date=datetime.strptime(str(elem.check_in)[:10], '%Y-%m-%d')

    def convert_TZ_UTC(self, TZ_datetime):
        fmt = "%Y-%m-%d %H:%M:%S"
        now_utc = datetime.now(timezone('UTC'))
        now_timezone = now_utc.astimezone(timezone(self.env.user.tz))
        UTC_OFFSET_TIMEDELTA = datetime.strptime(now_utc.strftime(fmt), fmt) - datetime.strptime(now_timezone.strftime(fmt), fmt)
        local_datetime = datetime.strptime(str(TZ_datetime), fmt)
        result_utc_datetime = local_datetime - UTC_OFFSET_TIMEDELTA

        return result_utc_datetime

    @api.depends('employee_id','check_in')
    def _get_if_letancy(self):
        for elem in self:
            elem.attendance_state=False
            elem.latency_minutes=0
            elem.computed_latency_minutes=0
            gender = elem.employee_id.gender
            today = elem.check_in.weekday()
            check_for_aprovale = self.env['hr.masarat.latency'].search([('check_in_attendacy', '=', elem.id)]).state

            check_for_aprovale_exit = self.env['hr.masarat.exit.permission'].search([
                ('employee_id', '=', elem.employee_id.id),
                ('start_date', '=', str(elem.check_in)[:10]),
                ('state', 'in', ['hr_approval','manager_approval'])], limit=1)


            check_for_halfday_holiday = self.env['hr.leave'].search([('employee_id', '=', elem.employee_id.id),('request_unit_half', '=', True) , ('request_date_from', '=', str(elem.check_in.date()))],limit=1)
            halfday_holiday = False

            check_for_work_assignment = self.env['hr.masarat.work.assignment'].search_count([('employee_id', '=', elem.employee_id.id),('state','in',('hr_approval','manager_approval')), ('start_date', '<=', elem.attendance_date),('end_date', '>=', elem.attendance_date)])

            check_for_holiday_leave = self.env['hr.leave'].search([('state','in',('validate1','validate')),('employee_id', '=', elem.employee_id.id),('date_from','<=',str(elem.check_in.date())),('date_to','>=',str(elem.check_in.date()))])
            #,('hr_icon_display','in',('presence_holiday_absent','presence_holiday_present'))

            ############## Get Latency
            calender = self.env['resource.calendar.attendance'].search([('calendar_id', '=', elem.attendance_type_id.id), ('dayofweek', '=', str(today))])
            start_work_day = '{0:02.0f}:{1:02.0f}'.format(*divmod(calender.hour_from * 60, 60)) + ':00'
            actul_date = datetime.strptime(str(elem.check_in)[:10] + ' ' + start_work_day, '%Y-%m-%d %H:%M:%S')
            ll = (self.convert_TZ_UTC(elem.check_in) - actul_date).total_seconds() / 60
            ##################


            if today in (4, 5) and check_for_holiday_leave:## pass in case of weekend
                continue

            elif check_for_halfday_holiday and (check_for_halfday_holiday.state).startswith('validate'):
                if check_for_halfday_holiday.request_date_from_period == 'am':
                    actul_date = datetime.strptime(str(elem.check_in)[:10] + ' ' + '12:00:00', '%Y-%m-%d %H:%M:%S')
                    ll = (self.convert_TZ_UTC(elem.check_in) - actul_date).total_seconds() / 60
                    if (ll > 0):
                        elem.attendance_state = 'latency'
                        elem.latency_minutes = ll
                        elem.computed_latency_minutes= ll*2

                elif check_for_halfday_holiday.request_date_from_period == 'pm':
                    actul_date = datetime.strptime(str(elem.check_in)[:10] + ' ' + '08:00:00', '%Y-%m-%d %H:%M:%S')
                    ll = (self.convert_TZ_UTC(elem.check_in) - actul_date).total_seconds() / 60
                    if (ll > 15):
                        elem.attendance_state = 'latency'
                        elem.latency_minutes = ll+15
                        elem.computed_latency_minutes= (ll+15)*2

            elif elem.attendance_type_id.name == 'توقيت الدوام 1 - التوقيت الرسمي' or elem.attendance_type_id.name == 'توقيت الدوام 1':
                # calender = self.env['resource.calendar.attendance'].search([('calendar_id', '=', elem.attendance_type_id.id), ('dayofweek', '=', str(today))])
                # start_work_day = '{0:02.0f}:{1:02.0f}'.format(*divmod(calender.hour_from * 60, 60)) + ':00'
                # actul_date = datetime.strptime(str(elem.check_in)[:10] + ' ' + start_work_day, '%Y-%m-%d %H:%M:%S')
                # ll = (self.convert_TZ_UTC(elem.check_in) - actul_date).total_seconds()/60
                if (ll > 0):
                    if str(elem.attendance_date) >= '2022-12-01': ## added for woman to give thim latency for up to 45 min
                        if (check_for_aprovale in ('manager_approval', 'hr_approval')) or check_for_work_assignment:
                            elem.attendance_state = 'approved'
                            elem.computed_latency_minutes = 0.0
                        elif (gender != 'female') and (ll <= 15) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                            elem.attendance_state = 'latency'
                            elem.latency_minutes = ll + 15
                            elem.computed_latency_minutes = (15 + ll) * 2
                        elif (gender != 'female') and (ll > 15) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                            elem.attendance_state = 'latency'
                            elem.latency_minutes = ll + 15
                            elem.computed_latency_minutes = (15 + ll) * 2
                        elif (ll > 30) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                            elem.attendance_state = 'latency'
                            elem.latency_minutes = ll + 15
                            elem.computed_latency_minutes = (15 + ll) * 2

                    else: ### regular latency
                        elem.attendance_state = 'latency'
                        elem.latency_minutes = ll+15
                        if (check_for_aprovale in ('manager_approval', 'hr_approval')) or check_for_work_assignment:
                            elem.attendance_state = 'approved'
                            elem.computed_latency_minutes = 0.0
                        elif (ll <= 15) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                            elem.computed_latency_minutes= (15+ll)*2
                        elif (ll > 15) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                            elem.computed_latency_minutes= (15+ll)*2

                if elem.check_out and (str(self.convert_TZ_UTC(elem.check_out))[11:] < '16:00:00'):
                    actul_date = datetime.strptime(str(elem.check_out)[:10] + ' ' + '16:00:00', '%Y-%m-%d %H:%M:%S')
                    ll = (actul_date - self.convert_TZ_UTC(elem.check_out)).total_seconds() / 60
                    elem.attendance_state = 'latency'
                    elem.latency_minutes += ll
                    elem.computed_latency_minutes += ll
                    if check_for_aprovale_exit.total_leave_minutes:
                        elem.latency_minutes = 0
                        elem.attendance_state = 'approved'
                        elem.computed_latency_minutes = elem.latency_minutes


                if elem.check_in == elem.check_out: ### Check_in == Check_out
                    if (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                        elem.attendance_state = 'latency'
                        elem.latency_minutes = 120
                        elem.computed_latency_minutes = 240

            elif elem.attendance_type_id.name == 'توقيت الدوام 2' or elem.attendance_type_id.name == 'توقيت الدوام 2 - الادارة الفنية':
                if ll > 75 :
                    elem.attendance_state = 'latency'
                    elem.latency_minutes = ll
                    if (check_for_aprovale in ('manager_approval', 'hr_approval')) or check_for_work_assignment:
                        elem.attendance_state = 'approved'
                        elem.computed_latency_minutes = 0.0
                    elif (ll >= 75) and (ll <= 90) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                        # elem.computed_latency_minutes+= ((ll-60)*2)+60
                        elem.computed_latency_minutes+= (ll*2)
                    elif (ll > 90) and (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                        # elem.computed_latency_minutes+= ((ll-60)*2)+60
                        elem.computed_latency_minutes+= (ll*2)

                if (elem.worked_hours < 8) :
                    elem.attendance_state = 'latency'
                    if (check_for_aprovale in ('manager_approval', 'hr_approval')) or check_for_work_assignment:
                        elem.attendance_state = 'approved'
                        elem.computed_latency_minutes = 0.0
                    else:
                        elem.latency_minutes += (8*60)- (elem.worked_hours*60)
                        # elem.computed_latency_minutes+=(8*60)- (elem.worked_hours*60)
                        elem.computed_latency_minutes = elem.latency_minutes*2

                if elem.check_in == elem.check_out: ### Check_in == Check_out
                    if (check_for_aprovale not in ('manager_approval', 'hr_approval')):
                        elem.attendance_state = 'latency'
                        elem.latency_minutes = 120
                        elem.computed_latency_minutes = 240

class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    unapproved_latency_minutes = fields.Integer(string='Un-Approved Latency', compute='get_unapproved_latency_minutes', store=True)

    work_hour_day = fields.Float(related='contract_id.resource_calendar_id.hours_per_day',string='Work Hour Day')

    approved_absence_days = fields.Integer(string='Approved Absence Days', compute='get_absence_days', store=True, default = 0)
    non_approved_absence_days = fields.Integer(string='Absence Days', compute='get_absence_days', store=True, default = 0)

    over_time_hours_at_home = fields.Float(help='Total Computed OverTime work hours', string='من السبت الى الخميس من المنزل', compute='_get_total_overtime_hours', store=True)
    over_time_hours_at_work = fields.Float(help='Total Computed OverTime work hours', string='من السبت الى الخميس من العمل', compute='_get_total_overtime_hours', store=True)
    over_time_hours_at_holiday = fields.Float(help='Total Computed OverTime work hours', string='العطلة /الجمعة', compute='_get_total_overtime_hours', store=True)

    car_allowance_total_hours = fields.Float(help='Total Computed Car Allowance hours', string='حركة السيارة', compute='_get_car_allowance_total_hours', store=True)

    @api.depends('employee_id', 'date_to', 'date_from')
    def _get_car_allowance_total_hours(self):
        for payslip in self:
            payslip.car_allowance_total_hours = 0
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01' ## to make sure there is no requists un collected from previus month
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt)).date()
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=2, day=1,days=-1))  ## to make sure there is no requists un collected from previus month
                    car_allowance_requists = self.env['hr.masarat.car'].search([('employee_id', '=', payslip.employee_id.id), ('state', '=', 'hr_approval'), ('request_date', '>=', attendance_date_from), ('request_date', '<=', attendance_date_to)])
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=1, day=1,days=-1))  ## geting previus month only
                    for elem in car_allowance_requists:
                        for each_requists in elem.car_line_ids:
                            if (each_requists.request_date >= attendance_date_from) and (each_requists.request_date <= attendance_date_to):
                                payslip.car_allowance_total_hours+=(each_requists.allowance_hours)
                            else:
                                pass

    @api.depends('employee_id', 'date_to', 'date_from')
    def _get_total_overtime_hours(self):
        for payslip in self:
            payslip.over_time_hours_at_home = 0
            payslip.over_time_hours_at_work = 0
            payslip.over_time_hours_at_holiday = 0
            if payslip.contract_id.resource_calendar_id.name not in ('توقيت الدوام 2' , 'توقيت الدوام 2 - الادارة الفنية', 'توقيت الدوام 1 - التوقيت الرسمي', 'توقيت الدوام 1'):
                continue
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01' ## to make sure there is no requists un collected from previus month
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt)).date()
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=2, day=1, days=-1)) ## to make sure there is no requists un collected from previus month
                    over_times_requists = self.env['hr.masarat.overtime'].search([('employee_id', '=', payslip.employee_id.id), ('state', '=', 'hr_approval'),('request_date', '>=', attendance_date_from), ('request_date', '<=', attendance_date_to)])
                    attendance_date_to = (datetime.strptime(pre_date, fmt).date() + relativedelta(months=1, day=1, days=-1)) ## geting previus month only
                    for elem in over_times_requists:
                        # print('requist', elem.request_date)
                        for each_requists in elem.overtime_line_ids:
                            if (each_requists.overtime_date >= attendance_date_from) and (each_requists.overtime_date <= attendance_date_to):
                                # print('intended month',each_requists.overtime_date)
                                if each_requists.overtime_type == 'at_home':
                                    payslip.over_time_hours_at_home += each_requists.overtime_hours
                                elif each_requists.overtime_type == 'at_work':
                                    payslip.over_time_hours_at_work+=(each_requists.overtime_hours)
                                elif each_requists.overtime_type == 'holidays':
                                    payslip.over_time_hours_at_holiday+=(each_requists.overtime_hours)
                            else:
                                pass

    @api.depends('employee_id','date_to','date_from')
    def get_unapproved_latency_minutes(self):
        for payslip in self:
            payslip.unapproved_latency_minutes=0
            if payslip.contract_id.resource_calendar_id.name not in ('توقيت الدوام 2' , 'توقيت الدوام 2 - الادارة الفنية', 'توقيت الدوام 1 - التوقيت الرسمي', 'توقيت الدوام 1'):
                continue
            if payslip.date_from and payslip.date_to:
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
                if pre_date:
                    attendance_date_from = (datetime.strptime(pre_date, fmt))
                    attendance_date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1)).strftime(fmt)
                latencies = self.env['hr.attendance'].search([('employee_id', '=', payslip.employee_id.id),('attendance_state','=','latency'), ('attendance_date','>=',attendance_date_from), ('attendance_date','<=',attendance_date_to)])
                for elem in latencies:
                    payslip.unapproved_latency_minutes+=int(elem.computed_latency_minutes)

    @api.depends('employee_id','date_to','date_from')
    def get_absence_days(self):
        for payslip in self:
            if payslip.contract_id.resource_calendar_id.name not in ('توقيت الدوام 2' , 'توقيت الدوام 2 - الادارة الفنية', 'توقيت الدوام 1 - التوقيت الرسمي', 'توقيت الدوام 1'):
                continue
            if payslip.date_from and payslip.date_to:
                ########## Last Month Date
                fmt = '%Y-%m-%d'
                pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
                attendance_date_from = (datetime.strptime(pre_date, fmt))
                attendance_date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1))
                ##############

                payslip.approved_absence_days = 0 ## init
                payslip.non_approved_absence_days = 0 ## init

                skip_list = []
                # Attended Days
                attended = self.env['hr.attendance'].search([('employee_id', '=', payslip.employee_id.id), ('attendance_date','>=',attendance_date_from), ('attendance_date','<=',attendance_date_to)])
                for days in attended:
                    skip_list.append(str(days.attendance_date)[:10])

                # Global Leave Days
                global_leave_days = self.env['resource.calendar'].search([('name', '=', payslip.employee_id.contract_id.resource_calendar_id.name)]).global_leave_ids
                for gld in global_leave_days:
                    sd = gld.date_from.date()
                    ed = gld.date_to.date()
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                # Leave Days
                leave_obj = self.env['hr.leave.report'].search([('employee_id', '=', payslip.employee_id.id), ('state', '=', 'validate'), ('leave_type', '=', 'request'),'|', ('date_to', '>=', attendance_date_from), ('date_from', '>=', attendance_date_from),'|', ('date_to', '<=', attendance_date_to), ('date_from', '<=', attendance_date_to)])
                for ee in leave_obj:
                    sd = ee.date_from.date()
                    ed = ee.date_to.date()
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                ## Work Assignment
                work_assignment_obj = self.env['hr.masarat.work.assignment'].search([('employee_id', '=', payslip.employee_id.id), ('state', 'in', ('hr_approval','manager_approval')),'|', ('end_date', '>=', attendance_date_from), ('start_date', '>=', attendance_date_from),'|', ('end_date', '<=', attendance_date_to), ('start_date', '<=', attendance_date_to)])
                for ee in work_assignment_obj:
                    sd = ee.start_date
                    ed = ee.end_date
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)


                # Approved work_outside Absence Days
                approved_absence_list=[]
                approved_absence = self.env['hr.masarat.absence'].search(
                            [('absence_type', '=', 'work_outside'),
                             ('employee_id', '=', payslip.employee_id.id),
                             ('state', '!=', 'draft'), '|',
                             ('absence_start_at', '>=', attendance_date_from),
                             ('absence_end_at', '>=', attendance_date_from), '|',
                             ('absence_start_at', '<=', attendance_date_to),
                             ('absence_end_at', '<=', attendance_date_to)])
                for days in approved_absence:
                    sd = days.absence_start_at
                    ed = days.absence_end_at
                    while sd <= ed:
                        skip_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                # Approved Absence Days
                approved_absence_list=[]
                approved_absence = self.env['hr.masarat.absence'].search(
                            [('absence_type', '!=', 'work_outside'),
                             ('employee_id', '=', payslip.employee_id.id),
                             ('state', '!=', 'draft'), '|',
                             ('absence_start_at', '>=', attendance_date_from),
                             ('absence_end_at', '>=', attendance_date_from), '|',
                             ('absence_start_at', '<=', attendance_date_to),
                             ('absence_end_at', '<=', attendance_date_to)])
                for days in approved_absence:
                    sd = days.absence_start_at
                    ed = days.absence_end_at
                    while sd <= ed:
                        approved_absence_list.append(str(sd)[:10])
                        sd = sd + timedelta(days=1)

                ###################
                #############
                abs_days = []
                approv_abs_days = []
                start_day = attendance_date_from
                end_day = attendance_date_to
                while start_day <= end_day:
                    if start_day.weekday() in (4, 5):
                        start_day = start_day + timedelta(days=1)
                        continue
                    if str(start_day)[:10] in skip_list:
                        start_day = start_day + timedelta(days=1)
                        continue
                    if str(start_day)[:10] in approved_absence_list:
                        approv_abs_days.append(start_day) ## Approved Absence Days
                        start_day = start_day + timedelta(days=1)
                        continue
                    abs_days.append(start_day)
                    start_day = start_day + timedelta(days=1)

                payslip.approved_absence_days=len(approv_abs_days)
                payslip.non_approved_absence_days=len(abs_days)