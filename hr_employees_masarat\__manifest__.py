# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

{
    'name': 'Masarat Employees Modification',
    'category': 'Masarat Employees Modification',
    'summary': 'Masarat Employees Modification',
    'sequence': 10,
    'version': '1.0',
    'description': """Investor Module""",
    'depends': ['hr_holidays','hr_attendance','hr','hr_contract','hr_recruitment','base_accounting_kit','base'],
    'data': [
        'security/security_assissment.xml',
        'security/ir.model.access.csv',
        'data/hr_payroll_data.xml',
        'data/cron_activity.xml',
        'views/hr_edited_view.xml',
        'views/hr_recruitment_edited_view.xml',
        'views/assets_edited_view.xml',
        'views/hr_assessment_view.xml',
        'views/hr_contract_edited.xml',
        'views/hr_assessment_masarat.xml',
        'views/hr_annual_assessment.xml',
        'views/bank_branch.xml',
        'views/hr_payslip_edited.xml',
        'views/hr_allocation_topaid_view.xml',
        'views/hr_leave_view.xml'
    ],
    'installable': True,
    'application': True,
}

#'views/hr_annual_assessment.xml',
#'views/hr_year_assessment_view.xml',
#'views/hr_sittings_assessment.xml',
#access_purchase_investor_search,access.purchase.investor.search,model_purchase_investor_search,,1,1,1,1
#'views/search_investor_view.xml',
#'views/sale_view.xml'5
#'views/hr_loan_edited_view.xml'

#access_hr_contract_assessment_element,access.hr.contract.assessment.element,model_hr_contract_assessment_element,,1,1,1,1
#access_hr_contract_assessment_annual_element,access.hr.contract.assessment.annual.element,model_hr_contract_assessment_annual_element,,1,1,1,1


# access_hr_contract_assessment_annual,access.hr.contract.assessment.annual,model_hr_contract_assessment_annual,,1,1,1,1
# access_hr_contract_assessment_annual_items,access.hr.contract.assessment.annual.items,model_hr_contract_assessment_annual_items,,1,1,1,1
# access_hr_assessment_year,access.hr.assessment.year,model_hr_assessment_year,,1,1,1,1
