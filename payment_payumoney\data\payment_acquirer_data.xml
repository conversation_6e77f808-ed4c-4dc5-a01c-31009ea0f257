<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="payment.payment_acquirer_payumoney" model="payment.acquirer">
        <field name="provider">payumoney</field>
        <field name="redirect_form_view_id" ref="redirect_form"/>
        <field name="support_authorization">False</field>
        <field name="support_fees_computation">False</field>
        <field name="support_refund"></field>
        <field name="support_tokenization">False</field>
    </record>

    <record id="payment_method_payumoney" model="account.payment.method">
        <field name="name">PayUmoney</field>
        <field name="code">payumoney</field>
        <field name="payment_type">inbound</field>
    </record>

</odoo>
