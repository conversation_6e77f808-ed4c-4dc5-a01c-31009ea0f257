# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_portal
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: E<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid " Copy"
msgstr " Kopya"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "(Disable two-factor authentication)"
msgstr "(İki faktörlü kimlik doğrulamayı devre dışı bırakın)"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        Two-factor authentication not enabled"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                          İki faktörlü kimlik doğrulama etkinleştirilmedi"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid ""
"<span class=\"text-success\">\n"
"                        <i class=\"fa fa-check-circle\"/>\n"
"                        Two-factor authentication enabled\n"
"                    </span>"
msgstr ""
"<span class=\"text-success\">\n"
"                          <i class=\"fa fa-check-circle\"/>\n"
"                         İki faktörlü kimlik doğrulama etkinleştirildi\n"
"                           </span>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Added On</strong>"
msgstr "<strong>Eklendi</strong>"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "<strong>Trusted Device</strong>"
msgstr "<strong>Güvenilir Cihaz</strong>"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Copied !"
msgstr "Kopyalandı !"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Enable two-factor authentication"
msgstr "İki faktörlü kimlik doğrulamayı etkinleştirin"

#. module: auth_totp_portal
#. openerp-web
#: code:addons/auth_totp_portal/static/src/js/totp_frontend.js:0
#, python-format
msgid "Operation failed for unknown reason."
msgstr "Bilinmeyen bir nedenle işlem başarısız oldu."

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Revoke All"
msgstr "Hepsini Geriye Al"

#. module: auth_totp_portal
#: model_terms:ir.ui.view,arch_db:auth_totp_portal.totp_portal_hook
msgid "Two-factor authentication"
msgstr "İki Faktörlü Kimlik Doğrulama"

#. module: auth_totp_portal
#: model:ir.model,name:auth_totp_portal.model_res_users
msgid "Users"
msgstr "Kullanıcılar"
