# -*- coding: utf-8 -*-
from odoo import tools, api, fields, models
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from odoo.exceptions import ValidationError


class HrAnnualContractAssisment(models.Model):
    _name = "hr.contract.assessment.annual"
    _rec_name = 'assessment_name'

    _sql_constraints = [('assessment_unique', 'UNIQUE(year,month,employee_id)', 'Assessment already exists!')]

    #### Date period
    @api.model
    def year_selection(self):
        year = 2010
        year_list = []
        while year != 2050:
            year_list.append((str(year), str(year)))
            year += 1
        return year_list


    state = fields.Selection([('draft','مسودة'),('done','مؤكدة')], default='draft')

    year = fields.Selection(year_selection, string="السنة", required=True)
    month = fields.Selection([("Jan", "يناير"), ("Feb", "فبراير"), ("Mar", "مارس"), ("Apr", "ابريل"),
                                        ("May", "مايو"), ("Jun", "يونيو"), ("Jul", "يوليو"), ("Aug", "أغسطس"),
                                        ("Sep", "سبتمبر"), ("Oct", "أكتوبر"), ("Nov", "نوفمبر"), ("Dec", "ديسمبر")],string='الشهر',required=True)
    ###############################################
    current_user_id = fields.Many2one('res.users', 'معد التقييم', default=lambda self: self.env.user)## the one how cuducted the evaluation
    employee_id = fields.Many2one('hr.employee', string='اسم الموظف', domain="[('parent_id.user_id', '=', current_user_id)]", required=True)
    department_id = fields.Many2one('hr.department', compute='_compute_employee_contract',string='الادارة',readonly=False)
    #contract_id = fields.Many2one('hr.contract')
    job_id = fields.Many2one('hr.job', compute='_compute_employee_contract',string='الوظيفة', store=True, readonly=False)
    assessment_name = fields.Char(compute='_compute_assessment_name')
    #annual_id = fields.Many2one('hr.contract.byyear.assessment', string='سنة التقييم')

    final_result = fields.Float(string="نتيجة القسم", compute='_compute_final_result', default = 0)

    time_attendance_ids = fields.One2many('hr.contract.assessment.annual.items', 'contract_assessment_time_attendance_id')
    time_attendance_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=9,required=True)
    time_attendance_result = fields.Float(string="نتيجة القسم", compute='_compute_time_attendance_result',default=0)

    performance_ids = fields.One2many('hr.contract.assessment.annual.items', 'contract_assessment_performance_id')
    performance_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=72,required=True)
    performance_result = fields.Float(string="نتيجة القسم", compute='_compute_performance_result',default=0)

    behaviors_ids = fields.One2many('hr.contract.assessment.annual.items', 'contract_assessment_behaviors_id')
    behaviors_wight = fields.Integer(string="وزن القسم (نسبة مئوية)", default=19,required=True)
    behaviors_result = fields.Float(string="نتيجة القسم", compute='_compute_behaviors_result',default=0)

    is_hr_group = fields.Boolean(compute='call_with_sudo_is_hr_group')
    def get_if_hr_group(self):
        hr_group = self.env.user.has_group('hr_employees_masarat.group_hr_masarat_assessment_annual')
        for rec in self:
            if hr_group:
                rec.is_hr_group = True
            else:
                rec.is_hr_group = False

    @api.depends('is_hr_group')
    def call_with_sudo_is_hr_group(self):
        self.sudo().get_if_hr_group()

    def make_done(self):
        self.state = 'done'

    def make_draft(self):
        self.state = 'draft'

    @api.depends('time_attendance_ids')
    def _compute_time_attendance_result(self):
        for elem in self:
            elem.time_attendance_result = 0
            if elem.time_attendance_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.time_attendance_ids:
                    total_item_value+=e.item_value
                    total_item_scores+=e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.time_attendance_result = (float(total_item_scores)/float(total_item_value))*(elem.time_attendance_wight)

    @api.depends('performance_ids')
    def _compute_performance_result(self):
        for elem in self:
            elem.performance_result = 0
            if elem.performance_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.performance_ids:
                    total_item_value += e.item_value
                    total_item_scores += e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.performance_result = (float(total_item_scores) / float(total_item_value)) * (elem.performance_wight)

    @api.depends('behaviors_ids')
    def _compute_behaviors_result(self):
        for elem in self:
            elem.behaviors_result = 0
            if elem.behaviors_ids:
                total_item_value = 0
                total_item_scores = 0
                for e in elem.behaviors_ids:
                    total_item_value += e.item_value
                    total_item_scores += e.item_score
                ## constrains
                if total_item_scores > total_item_value:
                    raise ValidationError('There is an Error With Your Assessments Scores !')
                ####
                elem.behaviors_result = (float(total_item_scores) / float(total_item_value)) * (
                            elem.behaviors_wight)

    @api.depends('time_attendance_result','performance_result','behaviors_result','state')
    def _compute_final_result(self):
        for elem in self:
            elem.final_result = 0
            if elem.time_attendance_result and elem.performance_result and elem.behaviors_result and (elem.state=='done'):
                elem.final_result = elem.time_attendance_result +elem.performance_result+elem.behaviors_result

    @api.depends('employee_id')
    def _compute_assessment_name(self):
        for elem in self:
            elem.assessment_name = str(elem.employee_id.name)+'-Annual-Assessment-'+str(elem.month)+'-'+str(elem.year)

    @api.depends('employee_id')
    def _compute_employee_contract(self):
        for contract in self.filtered('employee_id'):
            contract.job_id = contract.employee_id.job_id
            contract.department_id = contract.employee_id.department_id
#
    @api.model
    def default_get(self, fields):
        res = super(HrAnnualContractAssisment, self).default_get(fields)
        #employ_id = self._context.get('active_id')
        #res['contract_id'] = self._context.get('active_id')
        #res['employee_id'] = self.env['hr.contract'].search([('id', '=', employ_id)]).employee_id.id

        #### time_attendance #####################
        llist = self.env['hr.assessment.annual.element'].search([('item_type','=','time_attendance')])
        default_list = []
        i=1
        for ele in llist:
            default_list.append((0,0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i+=1

        res['time_attendance_ids'] = default_list
        ##############################################
        #### performance #############################
        llist = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'performance')])
        default_list = []
        i = 1
        for ele in llist:
            default_list.append((0, 0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i += 1

        res['performance_ids'] = default_list
        ##############################################
        #### behaviors #############################
        llist = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'behaviors')])
        default_list = []
        i = 1
        for ele in llist:
            default_list.append((0, 0, {
                'seq_number': i,
                'item_name': ele.item_name,
                'item_value': ele.item_value,
                'item_score': 0,
            }))
            i += 1
        res['behaviors_ids'] = default_list
        return res


    def unlink(self):
        for elem in self:
            if not elem.is_hr_group:
                raise ValidationError('ليس لديك صلاحية حذف التقييم'+' '+str(elem.assessment_name))
            if elem.current_user_id.id != elem.env.user.id:
                raise ValidationError('ليس لديك صلاحية حذف التقييم'+' '+str(elem.assessment_name))
            if elem.state != 'draft':
                raise ValidationError('لا يمكن حذف التقييم الا في حالة كان مسودة'+' '+str(elem.assessment_name))
        return super(HrAnnualContractAssisment, self).unlink()

    @api.model
    def create(self, vals):
        default_list = self.env['hr.assessment.annual.element'].search([('item_type','=','time_attendance')])
        i=0
        for elem in default_list:
            vals['time_attendance_ids'][i][2].setdefault('seq_number',i+1)
            vals['time_attendance_ids'][i][2]['item_value']=elem.item_value
            vals['time_attendance_ids'][i][2]['item_name']=elem.item_name
            i+=1

        default_list = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'performance')])
        i = 0
        for elem in default_list:
            vals['performance_ids'][i][2].setdefault('seq_number', i + 1)
            vals['performance_ids'][i][2]['item_value'] = elem.item_value
            vals['performance_ids'][i][2]['item_name'] = elem.item_name
            i += 1

        default_list = self.env['hr.assessment.annual.element'].search([('item_type', '=', 'behaviors')])
        i = 0
        for elem in default_list:
            vals['behaviors_ids'][i][2].setdefault('seq_number', i + 1)
            vals['behaviors_ids'][i][2]['item_value'] = elem.item_value
            vals['behaviors_ids'][i][2]['item_name'] = elem.item_name
            i += 1
        return super(HrAnnualContractAssisment, self).create(vals)
#

class HrAnnualContractAssismentItems(models.Model):
    _name = 'hr.contract.assessment.annual.items'
    contract_assessment_id = fields.Many2one('hr.contract.assessment.annual')

    contract_assessment_time_attendance_id = fields.Many2one('hr.contract.assessment.annually')
    contract_assessment_performance_id = fields.Many2one('hr.contract.assessment.annually')
    contract_assessment_behaviors_id = fields.Many2one('hr.contract.assessment.annually')

    seq_number = fields.Integer(string='الرقم')
    item_name = fields.Char(string='اسم العنصر')
    item_value = fields.Float(string='قيمة العنصر')
    item_score = fields.Float(string='النتيجة')

    @api.constrains('item_score')
    def check_item_score(self):
        for elem in self:
            if (elem.item_score > 5.0) or (elem.item_score < 0.0):
                raise ValidationError('نتيجة العنصر يجب أن تكون بين 0 و 5')


class HrPayslipX(models.Model):
    _inherit = 'hr.payslip'

    annually_assessment_result = fields.Float(string="Annually Assessment", compute='_get_annually_assessment', default = 0)

    @api.depends('employee_id', 'date_to', 'date_from')
    def _get_annually_assessment(self):
        for payslip in self:
            payslip.annually_assessment_result = 0
            if payslip.date_from and payslip.employee_id:
                self.env.cr.execute("select id, year, month from hr_contract_assessment_annual where state = 'done' and employee_id = %s", (payslip.employee_id.id,))
                assissment = [x for x in self.env.cr.fetchall()]
                for res in assissment:
                    if (payslip.date_from) and (res[1]+'-'+res[2] == str((payslip.date_from - relativedelta(months=1)).strftime('%Y-%b'))): ## looking for previous assissment that match current date
                        assiss_final_result = self.env['hr.contract.assessment.annual'].search([('id','=',res[0])]).final_result # getting assiss result
                        #self.env.cr.execute("select coalesce((select coalesce(id, 0) from hr_payslip where state = 'done' and employee_id = %s order by date_from desc limit 1),0);", (payslip.employee_id.id,)) ## getting last payslip
                        #pre_payslip = self.env.cr.fetchone()[0] ## gitting payslip
                        #self.env.cr.execute("select coalesce((select coalesce(total, 0) as total from hr_payslip_line where code = 'NET' and slip_id = %s),0);",(pre_payslip,))  ## getting last payslip
                        total_pre_payslip = payslip.employee_id.contract_id.wage ## gitting pre_payslip_netsalary
                        payslip.annually_assessment_result = total_pre_payslip * (assiss_final_result/100)

            # pre_date = str((datetime.strptime(str(payslip.date_from), fmt) - relativedelta(months=1)).strftime(fmt))[:8] + '01'
            # date_from = (datetime.strptime(pre_date, fmt))
            # date_to = (datetime.strptime(pre_date, fmt) + relativedelta(months=1, day=1, days=-1))
            # assessment = self.env['hr.contract.assessment.monthly'].search([('employee_id','=',payslip.employee_id.id),('date_from','=',date_from),('date_to','=',date_to)], limit=1)
            # payslip.monthly_assessment_result= assessment.final_result

#
# class EvaluationElement(models.Model):
    # عناصر تقييم الأداء السنوي
#     _name = 'hr.assessment.annual.element'
#     contract_assessment_id = fields.Many2one('hr.contract.assessment.annually')
#
#     item_name = fields.Char(string='أسم العنصر', required=True)
#     item_value = fields.Integer(string='وزن العنصر', required=True)
#     item_type = fields.Selection(([
#         ('time_attendance','Time and Attendance'),
#         ('performance','Performance'),
#         ('behaviors','Behaviors')]),string='نوع العنصر', required=True)
#
#
#
# # class ByYaerAssessment(models.Model):
# #     _name = 'hr.contract.byyear.assessment'
# #
# #     name = fields.Char(sting='سنة التقييم')
# #     assessment_ids = field.One2many("hr.contract.assessment.annual",'annual_id')
