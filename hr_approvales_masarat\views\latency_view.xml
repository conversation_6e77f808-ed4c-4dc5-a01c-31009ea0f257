<?xml version="1.0"?>
<odoo>

    <record id="latency_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.latency.form</field>
        <field name="model">hr.masarat.latency</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
<!--                    <button string="Cancel" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','=','draft')]}" name="make_cancel_approval" type="object"/>-->
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval" type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee"  attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>
                            <field name="manager_id"  options='{"no_open": True}'/>
                            <field name="request_date"/>
                            <field name="request_date_3days" invisible="1"/>
                        </group>
                        <group string="Employee Info">
                            <field name="absence_type" attrs="{'readonly': [('state','!=','draft')]}"/>
<!--                            <field name="choosen_day" attrs="{'readonly': [('state','!=','draft')]}"/>-->
                            <field name="check_in_attendacy" attrs="{'readonly': [('state','!=','draft')]}"/>
<!--                            <field name="start_work_day" attrs="{'readonly': [('state','!=','draft')]}"/>-->
                            <field name="start_work_day"/>
                        </group>
                        <group string="Latency Info">
                            <field name="check_in" options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="latency"/>
<!--                            <field name="approved_latency_by_manager" attrs="{'readonly': [('is_manager','!=','1')]}"/>-->
                        </group>
                        <group>
                            <field name="Note" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="latency_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.latency.tree</field>
        <field name="model">hr.masarat.latency</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="check_in"/>
                <field name="latency"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_latency_view" model="ir.actions.act_window">
        <field name="name">طلبات تبرير التأخير</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.latency</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="hr_masarat_approvals"
              name="طلبات"
              parent="hr.menu_hr_root"
              groups="hr_approvales_masarat.group_employee_approvales_masarat,hr_approvales_masarat.group_hr_approvales_masarat"
              sequence="15"/>

    <menuitem
            id="menu_latency"
            name="طلبات تبرير التأخير"
            parent="hr_masarat_approvals"
            action="action_latency_view"
            sequence="1"/>


</odoo>

