# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# xu aaron, 2021
# 深圳市耀影科技有限公司_QQ1006399173, 2021
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON> CHEN <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: 张应兵 <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_count
msgid "# Leads/Opps assigned this month"
msgstr "# 本月分配的线索/操作"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_count
msgid "# Meetings"
msgstr "# 会议"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__number_of_months
msgid "# Months"
msgstr "# 月"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "# Opportunities"
msgstr "# 商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_count
msgid "# Overdue Opportunities"
msgstr "# 逾期商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_unassigned_count
msgid "# Unassigned Leads"
msgstr "# 未分派线索"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(assigned)s leads allocated among %(team_count)s teams."
msgstr "在%(team_count)s个团队中分配%(assigned)s个线索。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(assigned)s leads allocated to %(team_name)s team."
msgstr "%(assigned)s条线索分配给%(team_name)s队。"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "%(attach_name)s (from %(lead_name)s)"
msgstr "%(attach_name)s（来自%(lead_name)s）"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(duplicates)s duplicates leads have been merged."
msgstr "%(duplicates)s重复的线索已被合并。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"%(members_assigned)s leads assigned among %(member_count)s salespersons."
msgstr "在%(member_count)s名销售人员中分配%(members_assigned)s条线索。"

#. module: crm
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid "%s and %s"
msgstr "%s 和 %s"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "%s's opportunity"
msgstr "%s's 商机"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "<b>Create your first opportunity.</b>"
msgstr "<b>创建您的第一个商机</b>"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr "在您的销售循环中<b>拖拽商机</b> 到您的进展列。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "<b>Predictive Lead Scoring</b>"
msgstr "<b>预测线索评分</b>"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "<b>Write a few letters</b> to look for a company, or create a new one."
msgstr "<b>写几封信</b> 寻找公司，或创建一个新的公司。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-star\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"
msgstr "<i class=\"fa fa-fw fa-star\" aria-label=“商机” role=\"img\" title=“商机”/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"
msgstr "<i class=\"fa fa-gear\" role=\"img\" title=\"切换到自动概率\" aria-label=\"切换到自动概率\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr "<i title=\"现在更新\" role=\"img\" aria-label=\"现在更新\" class=\"fa fa-fw fa-refresh\"/>"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"<p class='o_view_nocontent_smiling_face'>Add new opportunities</p><p>\n"
"    Looks like you are not a member of a Sales Team. You should add yourself\n"
"    as a member of one of the Sales Team.\n"
"</p>"
msgstr ""
"<p class='o_view_nocontent_smiling_face'>添加新商机</p><p>\n"
"您还不是当前销售团队的成员\n"
"请先将自己添加到当前销售团队中\n"
"</p>"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"<p>As you don't belong to any Sales Team, Odoo opens the first one by "
"default.</p>"
msgstr "<p>由于您不属于任何销售团队，Odoo默认打开第一个团队。</p>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "<span class=\"bg-danger\">Lost</span>"
msgstr "<span class=\"bg-danger\">丢失</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"attrs=\"{'invisible': [('partner_email_update', '=', False)]}\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=“通过保存此更改，客户电子邮件也将得到更新.” attrs=\"{'invisible': "
"[('partner_email_update', '=', False)]}\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" attrs=\"{'invisible': [('partner_phone_update', '=', False)]}\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=“通过保存此更改，客户电话号码也将被更新.” attrs=\"{'invisible': [('partner_phone_update',"
" '=', False)]}\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\">会议</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\">会议</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&lt;', 2)]}\">Similar Leads</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&gt;', 1)]}\">Similar Lead</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&lt;', 2)]}\">类似信息</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&gt;', 1)]}\">类似的线索</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', False)]}\">Leads</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', True)]}\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', False)]}\">线索</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', True)]}\">商机</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\">线索</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> at </span>"
msgstr ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> 在 </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2\"> at </span>"
msgstr "<span class=\"oe_grey p-2\"> 在 </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey\"> %</span>"
msgstr "<span class=\"oe_grey\">% </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> (max) </span>"
msgstr "<span class=\"oe_inline\">(最大) </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> / </span>"
msgstr "<span class=\"oe_inline\">/ </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<span>Periodically assign leads based on rules</span><br/>\n"
"                                    <span attrs=\"{'invisible': [('crm_use_auto_assignment', '=', False)]}\">\n"
"                                        All sales teams will use this setting by default unless\n"
"                                        specified otherwise.\n"
"                                    </span>"
msgstr ""
"<span>根据规则定期分配潜在客户线索</span><br/>\n"
"                                    <span attrs=\"{'invisible': [('crm_use_auto_assignment', '=', False)]}\">\n"
"                                       除非另有说明，否则所有销售团队都将默认使用此设置。\n"
"                                        \n"
"                                    </span>"

#. module: crm
#: model:mail.template,body_html:crm.mail_template_demo_crm_lead
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Lead/Opportunity</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Interest in your products</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hi <t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>,<br/><br/>\n"
"                            Welcome to <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>.\n"
"                            It's great to meet you! Now that you're on board, you'll discover what <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t> has to offer. My name is <t t-out=\"object.user_id.name or ''\">Marc Demo</t> and I'll help you get the most out of Odoo. Could we plan a quick demo soon?<br/>\n"
"                            Feel free to reach out at any time!<br/><br/>\n"
"                            Best,<br/>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br/>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br/>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\"/>\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br/>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">您的线索/机会</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">对您的产品感兴趣</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            嗨，<t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>。<br/><br/>\n"
"                            欢迎来到<t t-out=\"object.company_id.name or ''\">我的公司（旧金山）</t>。\n"
"                            很高兴见到您!现在您已经加入了，您会发现<t t-out=\"object.company_id.name or ''\">我的公司（旧金山）</t>所能提供的东西。我的名字是<t t-out=\"object.user_id.name or ''\">Marc Demo</t>，我将帮助您从Odoo中获得最大的收益。我们能不能尽快计划一次快速演示？<br/>\n"
"                            请随时联系我们!<br/><br/>\n"
"                            最好。<br/>\n"
"                           <t t-if=\"object.user_id\">\n"
"                               <b><t t-out=\"object.user_id.name or ''\">马克演示</t></b>\n"
"                                <br/> 电子邮件:<t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br/> 电话。<t t-out=\"object.user_id.phone or ''\">******-123-4567</t> </t>\n"
"                            \n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">我的公司 (旧金山)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\"/>\n"
"            <b t-out=\"object.company_id.name or ''\">我的公司 (旧金山)</b><br/>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    |<a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    |<a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    由<a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>提供技术提供\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "将为此别名创建新记录时计算以提供默认值的 Python 字典。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid ""
"A great tip to boost sales efficiency is to always define a next step on "
"each opportunity. To manage ongoing activities, click on any status of the "
"progress bar to filter opportunities based on their next activities' status."
" Click on the grey area of the progress bar to see all opportunities that "
"have no next activity."
msgstr ""
"提高销售效率的一个很好的技巧是始终确定每个商机的下一步。若要管理正在进行的活动，请单击进度栏的任何状态，以根据其下一个活动的状态筛选商机。单击进度栏的灰色区域以查看没有下一个活动的所有商机。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr "接收邮件来自"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr "需要采取行动"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__active
msgid "Active"
msgstr "启用"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_tomerge_ids
msgid "Active Leads"
msgstr "活动潜在顾客"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Activities"
msgstr "活动"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activities Analysis"
msgstr "活动分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr "活动完成目标"

#. module: crm
#: model:ir.model,name:crm.model_mail_activity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity"
msgstr "活动"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__body
msgid "Activity Description"
msgstr "活动描述"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动例外勋章"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr "活动类型"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr "活动类型"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Activity by"
msgstr "活动由"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Add a description..."
msgstr "添加描述。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before the creation of an opportunity"
msgstr "在新建一个新的商机之前，先添加一个资格认证步骤"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/forecast/forecast_kanban_column_quick_create.js:0
#, python-format
msgid "Add next %s"
msgstr "添加下一个%s。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_domain
msgid ""
"Additional filter domain when fetching unassigned leads to allocate to the "
"team."
msgstr "在获取未分配的线索以分配给团队时，增加了过滤条件。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Address"
msgstr "地址"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_tree
msgid "Alias"
msgstr "别名"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_contact
msgid "Alias Contact Security"
msgstr "别名联系人安全"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_name
msgid "Alias Name"
msgstr "别名名称"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain
msgid "Alias domain"
msgstr "别名域"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_model_id
msgid "Aliased Model"
msgstr "别名模型"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "All set. Let’s <b>Schedule</b> it."
msgstr "全部设置。让我们安排它<b>附表</b>。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Analysis"
msgstr "分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr "删除重复内容"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_recurring_plan_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Archived"
msgstr "已存档"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign Documentation"
msgstr "分配文件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assign Leads"
msgstr "分配线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr "分配商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign salespersons into multiple Sales Teams."
msgstr "将销售人员分配到多个销售团队。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr "批量分配商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr "分配商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assigned Leads Count"
msgstr "已分配线索数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Assigned To"
msgstr "分派给"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignment Date"
msgstr "分配日期"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_domain
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_domain
msgid "Assignment Domain"
msgstr "分配域"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assignment Rules"
msgstr "分配规则"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Assignment domain for team %(team)s is incorrectly formatted"
msgstr "团队%(team)s的分配条件的格式不正确"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_id
msgid "Associated Lead"
msgstr "关联潜在顾客"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_auto_enabled
msgid "Auto Assignment"
msgstr "自动分配"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_action
msgid "Auto Assignment Action"
msgstr "自动分配动作"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Auto Assignment Interval Unit"
msgstr "自动分配间隔单位"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_run_datetime
msgid "Auto Assignment Next Execution Date"
msgstr "自动分配 下次执行日期"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__automated_probability
msgid "Automated Probability"
msgstr "自动概率"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_max
msgid "Average Leads Capacity (on 30 days)"
msgstr "平均信息容量（30天内）"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr "黑名单"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "列入黑名单的手机是移动的"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "列入黑名单的电话是电话"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Boom! Team record for the past 30 days."
msgstr "哇！新的月度记录诞生啦呀。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr "退回"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr "CRM"

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "CRM活动分析"

#. module: crm
#: model:ir.model,name:crm.model_crm_recurring_plan
msgid "CRM Recurring revenue plans"
msgstr "CRM 周期收入计划"

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr "CRM 阶段"

#. module: crm
#: model:ir.actions.server,name:crm.ir_cron_crm_lead_assign_ir_actions_server
#: model:ir.cron,cron_name:crm.ir_cron_crm_lead_assign
#: model:ir.cron,name:crm.ir_cron_crm_lead_assign
msgid "CRM: Lead Assignment"
msgstr "CRM:线索分配"

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Calendar Event"
msgstr "日历事件"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr "呼叫演示"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr "活动"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "取消"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_tree
msgid "Channel"
msgstr "频道"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr "选中此框以筛选传入的请求并作为线索，然后再将其转换为商机并将其分配给销售人员。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr "选中此框可管理带有商机的预售流程。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr "城市"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr "归类分析线索/商机的类别， 例如：培训，服务"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Click on the breadcrumb to go back to the Pipeline."
msgstr "单击面包屑可返回管道。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_closed
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr "关闭日期"

#. module: crm
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:0
#, python-format
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr "关闭不能转化为商机的线索。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
msgid "Color Index"
msgstr "颜色索引"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "公司"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Company Name"
msgstr "公司名称"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completion Date"
msgstr "完成日期"

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "配置"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Configure a custom domain"
msgstr "配置自定义域名"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Confirm"
msgstr "确认"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Congrats, best of luck catching such big fish! :)"
msgstr "恭喜， 祝你好运抓这么大的鱼! :)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Consider leads created as of the:"
msgstr "考虑到截至的线索创建。"

#. module: crm
#: model:ir.model,name:crm.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Contact Information"
msgstr "联系信息"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr "联系人姓名"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr "转换动作"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr "转换日期"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr "从线索转化为商机的时间"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr "转换选项"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr "转化线索为商机（批量）"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr "转化线索为商机（单个）"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr "转为商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr "转换为商机"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr "转为商机"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__convert
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__convert
#, python-format
msgid "Convert to opportunity"
msgstr "转换为商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads and perform data enrichment "
"based on their IP address"
msgstr "假如填了联系我们那个页面上的表单，则访客所填写的表单信息，将被odoo自动转成一条线索，出现在CRM线索列表中。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__correct
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__correct
msgid "Correct"
msgstr "正确的"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "此联系人退回邮件数量计数器"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_country_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr "国家/地区"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_website_crm_iap_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr "从您的网站流量创建线索/商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr "创建商机"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid "Create a Lead"
msgstr "创建一个线索"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Create a Lost Reason"
msgstr "创造一个丢失的理由"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid "Create a Recurring Plan"
msgstr "创建周期计划"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr "直接从商机管道创建一个线索挖掘需求。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__create
msgid "Create a new customer"
msgstr "创建客户"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
#, python-format
msgid "Create a new lead"
msgstr "创建线索"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
#, python-format
msgid "Create an opportunity to start playing with your pipeline."
msgstr "在您的渠道中创造商机。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr "创建人"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr "创建时间"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_create_date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr "创建时间"

#. module: crm
#: model:ir.actions.server,name:crm.action_opportunity_forecast
msgid "Crm: Forecast"
msgstr "Crm:预测"

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr "Crm：我的渠道"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr "币种"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自定义退回消息"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#, python-format
msgid "Customer"
msgstr "客户"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Customer Email"
msgstr "客户邮件"

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr "客户"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr "关闭日期"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__days
msgid "Days"
msgstr "天数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr "分配天数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr "关闭日期"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_defaults
msgid "Default Values"
msgstr "默认值"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Define recurring plans and revenues on Opportunities"
msgstr "定义机会的周期计划和收入"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr "刪除"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Description"
msgstr "说明"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Did you know emails sent to  generate opportunities in your pipeline?<br>"
msgstr "您知道为在您的漏斗中产生机会而发送的电子邮件吗？<br>"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Did you know emails sent to a Sales Team alias generate opportunities in "
"your pipeline?"
msgstr "发送到 <strong>${email}</strong>  该邮箱的邮件会在您的渠道里自动生成商机.<br>？"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid ""
"Did you know you can search a company by name or VAT number to instantly "
"fill in all its data? Odoo autocompletes everything for you: logo, address, "
"company size, business information, social media accounts, etc."
msgstr "您是否知道您可以按名称或增值税号搜索公司，以立即填写所有数据？Odoo 自动完成您的一切：徽标、地址、公司规模、业务信息、社交媒体帐户等。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid ""
"Did you know you can turn a list of opportunities into a map view, using the"
" top-right map icon? A lot of screens in Odoo can be turned into a map: "
"tasks, contacts, delivery orders, etc."
msgstr "您是否知道可以使用右上角的地图图标将商机列表转换为地图视图？Odoo 中的许多屏幕可以转换为地图：任务、联系人、交货订单等。"

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr "摘要"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: crm
#: code:addons/crm/models/digest.py:0 code:addons/crm/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "没有权限，用户电子邮件中忽略此数据"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__nothing
msgid "Do not link to a customer"
msgstr "不要链接到某个客户"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Documentation"
msgstr "文档"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Domain"
msgstr "域"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Drag your opportunity to <b>Won</b> when you get the deal. Congrats !"
msgstr "获得交易后，将您<b>赢得</b>的机会拖后而去。恭喜！"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "E.g. Monthly"
msgstr "例如每月"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "Easily set expected closing dates and overview your revenue streams."
msgstr "轻松地设置预期结束日期，并概述您的收入流。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr "编辑"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Email"
msgstr "电子邮件"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr "电子邮件别名"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_email_state
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_state
msgid "Email Quality"
msgstr "邮件质量"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Email cc"
msgstr "邮件抄送"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_type_demo_email_with_template
msgid "Email: Welcome Demo"
msgstr "电子邮件：欢迎演示"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__auto
msgid "Enrich all leads automatically"
msgstr "自动丰富所有线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr "自动丰富潜线索"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__manual
msgid "Enrich leads on demand only"
msgstr "仅丰富需要的线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Enrich your leads automatically with company data based on their email "
"address"
msgstr "使用基于邮件地址的公司数据来自动丰富您的线索。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr "根据公司电子邮件地址自动使用公司数据丰富您的潜在客户。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr "在这里输入此阶段的内部需求（例如：发给客户的报价）。它将会显示于阶段名称上面的工具箱。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr "预计商机落单日期."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr "预期结束"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected MRR"
msgstr "预期 MRR"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Expected Revenue"
msgstr "预期收益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr "预期收益"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr "扩展筛选"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Info"
msgstr "额外信息"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Information"
msgstr "额外信息"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Extra fields..."
msgstr "额外的字段..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__field_id
msgid "Field"
msgstr "字段"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__name
msgid "Field Label"
msgstr "字段标签"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "用于存储已消毒的电话号码的字段。有助于加快搜索和比较。"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency_field
msgid "Fields that can be used for predictive lead scoring computation"
msgstr "用来计算预测线索评分值的字段"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr "在渠道中折叠"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr "跟进报价"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "字体真棒图标e.g. fa-tasks"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid ""
"For a sales team, there is nothing worse than being dry on leads. "
"Fortunately, in just a few clicks, you can generate leads specifically "
"targeted to your needs: company size, industry, etc. To help you test the "
"feature, we offer you 200 credits for free."
msgstr ""
"对于销售团队来说，没有什么比对潜在客户保持干性更糟糕的了。幸运的是，只需点击几下，您就可以生成专门针对您需求的潜在客户：公司规模、行业等。为了帮助您测试该功能，我们为您提供"
" 200 个免费积分。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid "Force assignment"
msgstr "强制分配"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_forecast
#: model:ir.ui.menu,name:crm.crm_menu_forecast
msgid "Forecast"
msgstr "预测"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot_forecast
msgid "Forecast Analysis"
msgstr "预测分析"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "From %(source_name)s"
msgstr "来自 %(source_name)s"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "From %(source_name)s : %(source_subject)s"
msgstr "从 %(source_name)s : %(source_subject)s"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr "未来活动"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_mine
msgid "Generate new leads based on their country, industries, size, etc."
msgstr "基于他们的国家，行业，规模等生成新的线索。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Generate new leads based on their country, industry, size, etc."
msgstr "基于他们的国家，行业，规模等生成新的线索。"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr "获取丢失原因"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr "告诉您的团队移动商机到这个阶段的条件。"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0 code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Go, go, go! Congrats for your first deal."
msgstr "祝贺您获得第一笔交易订单。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "分组"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__has_message
msgid "Has Message"
msgstr "有信息"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__2
msgid "High"
msgstr "高"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__hours
msgid "Hours"
msgstr "小时"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr "ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "保存别名的父记录的 ID（例如：保存任务创建别名的项目）"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "表示一个例外活动的图标。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid ""
"If checked, forces salesman to be updated on updated opportunities even if "
"already set."
msgstr "如果选中，将强制销售员更新更新的商机，即使已设置。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_partner__team_id
#: model:ir.model.fields,help:crm.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr "如果设置，此销售团队将用于与此合作伙伴相关的销售和分配"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "如果设置，此内容将自动发送给未经授权的用户，而不是默认消息。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "存在于黑名单的电子邮件表示收件者不会再收到任何列表的群发邮件"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "存在于黑名单的电子邮件表示收件者不会再收到任何列表的群发邮件。"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr "导入并同步"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Import Template for Leads & Opportunities"
msgstr "导入线索或商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
msgid "Include archived"
msgstr "包含存档"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__incorrect
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__incorrect
msgid "Incorrect"
msgstr "不正确"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名单的已消毒电话号码是否为手机号码。帮助区分在模型中同时存在手机和手机字段时列入黑名单的号码。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名单的已消毒电话号码是否为电话号码。帮助区分在模型中同时存在手机和手机字段时列入黑名单的号码。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Internal Notes"
msgstr "内部备注"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Interval type between each cron run (e.g. each 2 days or each 2 hours)"
msgstr "每个cron运行之间的间隔类型（例如，每2天或每2小时）。"

#. module: crm
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid ""
"Invalid repeat frequency. Consider changing frequency type instead of using "
"large numbers."
msgstr "无效的重复频率。考虑改变频率类型，而不是使用大数字。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__is_won
msgid "Is Won Stage?"
msgstr "是赢得阶段？"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_automated_probability
msgid "Is automated probability?"
msgstr "是否自动概率？"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr "工作岗位"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__kanban_state
msgid "Kanban State"
msgstr "看板状态"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr "KPI CRM预期收益"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr "KPI CRM签单金额"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_lang_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_id
msgid "Language"
msgstr "语言"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_action_last
msgid "Last Action"
msgstr "上一步"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason____last_update
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity____last_update
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan____last_update
#: model:ir.model.fields,field_description:crm.field_crm_stage____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr "最后阶段更新"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr "最近的活动"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__lead
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__lead
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead"
msgstr "线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_enabled
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_enabled
msgid "Lead Assign"
msgstr "线索指定"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Lead Assignment requested by %(user_name)s"
msgstr "由%(user_name)s要求的领导任务"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_max
msgid "Lead Average Capacity"
msgstr "线索平均容量"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Enrichment"
msgstr "线索丰富"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Generation"
msgstr "线索生成"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Mining"
msgstr "线索挖掘"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency
msgid "Lead Scoring Frequency"
msgstr "线索评分重复率"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr "线索评分重复率字段"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr "字符串中的线索评分重复率字段"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr "线索评分开始日期"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr "字符串中线索评分开始日期"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_month_count
msgid "Lead assigned to this member those last 30 days"
msgstr "‎过去 30 天分配给此成员的领引‎"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Lead or Opportunity"
msgstr "线索/商机"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"Lead/Opportunities automatic assignment is limited to managers or "
"administrators"
msgstr "领导/机会的自动分配仅限于管理人员或行政人员"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "线索/商机"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Lead/Opportunity created"
msgstr "已创建潜在顾客/商机"

#. module: crm
#: code:addons/crm/models/crm_lost_reason.py:0
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#, python-format
msgid "Leads"
msgstr "线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_month_count
msgid "Leads (30 days)"
msgstr "线索（30天）"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "线索分析"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr "线索分析允许您检查CRM中关联的不同信息，例如延迟或每个状态的线索的数量。您可以使用不同的分组对线索分析进行整理以获得精确细致的分析结果。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Leads Assigned"
msgstr "线索分配"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__leads_count
msgid "Leads Count"
msgstr "线索数量"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr "生成线索"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Leads are the qualification step before the creation of an opportunity."
msgstr "线索是创造机会之前的资格认证步骤。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_activity
msgid "Leads or Opportunities"
msgstr "线索/商机"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#: code:addons/crm/models/crm_team_member.py:0
#, python-format
msgid ""
"Leads team allocation should be done for at least 0.2 or maximum 30 work "
"days, not %.2f."
msgstr "领导团队的分配应至少进行0.2或最多30个工作日，而不是%.2f。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr "分配给我的线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are not assigned"
msgstr "未分配的线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr "您选择的线索有重复。如果列表为空，表明没有发现重复线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr "存在的重复线索（信息）"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr "线索/商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "线索/商机 数量"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Let's <b>Schedule an Activity.</b>"
msgstr "让我们<b>安排一个活动。</b>"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Let’s have a look at an Opportunity."
msgstr "让我们来看看机会。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__exist
msgid "Link to an existing customer"
msgstr "链接到现有客户"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr "连接的业务伙伴（可选）。通常在转化线索时创建。您能通过它的名称、TIN，邮件和内部引用查找到业务伙伴。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_cc
msgid "List of cc from incoming emails."
msgstr "收到的邮件里的抄送列表。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Looks like nothing is planned."
msgstr "看起来没有什么计划。"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Looks like nothing is planned. :(<br><br><i>Tip : Schedule activities to "
"keep track of everything you have to do!</i>"
msgstr "看起来没有计划。:(<br><br><i>提示： 安排活动，跟踪您必须做的一切！</i>"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Lost"
msgstr "丢失"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__lost_count
msgid "Lost Count"
msgstr "丢失数量"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr "失去原因"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr "失去原因"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__0
msgid "Low"
msgstr "低"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_main_attachment_id
msgid "Main Attachment"
msgstr "附件"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr "制作报价"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manage Recurring Plans"
msgstr "管理周期计划"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_action
msgid ""
"Manual assign allow to trigger assignment from team form view using an "
"action button. Automatic configures a cron running repeatedly assignment in "
"all teams."
msgstr "手动分配允许使用一个行动按钮从团队表格视图中触发分配。自动配置一个在所有团队中重复运行的cron分配。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__manual
msgid "Manually"
msgstr "手工"

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_as_lost
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as lost"
msgstr "标记为丢失"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as won"
msgstr "标记为获取"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Marketing"
msgstr "市场营销"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__1
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr "媒介"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Meeting scheduled at '%s'<br> Subject: %s <br> Duration: %s hours"
msgstr "会议安排在 '%s'<br>   主题: %s <br> 时长: %s 小时"

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_ids
msgid "Meetings"
msgstr "会议"

#. module: crm
#: code:addons/crm/models/crm_team_member.py:0
#, python-format
msgid ""
"Member assignment domain for user %(user)s and team %(team)s is incorrectly "
"formatted"
msgstr "用户%(user)s和团队%(team)s的成员分配条件格式不正确"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr "合并"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr "合并线索/商机"

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr "合并商机"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr "将不同的业务伙伴已存在的线索或商机合并"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__merge
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__merge
msgid "Merge with existing opportunities"
msgstr "与已存在的商机合并"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged lead"
msgstr "合并的线索"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged leads"
msgstr "合并的线索"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged opportunities"
msgstr "合并的商机"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Merged opportunity"
msgstr "合并的商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr "消息传递错误"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr "消息"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__minutes
msgid "Minutes"
msgstr "分钟"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Misc"
msgstr "杂项"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mobile"
msgstr "手机"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_monthly
msgid "Monthly"
msgstr "每月"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_max
msgid "Monthly average leads capacity for all salesmen belonging to the team"
msgstr "属于团队的所有销售人员的月平均线索能力"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__is_membership_multi
msgid "Multi Teams"
msgstr "多团队"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_my_activities
#: model:ir.ui.menu,name:crm.crm_lead_menu_my_activities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
msgid "My Activities"
msgstr "我的活动"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "My Deadline"
msgstr "我的截止活动"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr "我的线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "My Opportunities"
msgstr "我的商机"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr "我的渠道"

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr "新建"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_open_lead_form
msgid "New Lead"
msgstr "新线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads/Opportunities"
msgstr "新的线索/商机"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "New Opportunities"
msgstr "新商机"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr "新商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一个活动日历事件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Next Run"
msgstr "下次运行"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__green
msgid "Next activity is planned"
msgstr "下一个活动已计划"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__red
msgid "Next activity late"
msgstr "下一个活动迟到"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "No Subject"
msgstr "无主题"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to %(team_name)s team and its salespersons because no "
"unassigned lead matches its domain."
msgstr "没有分配给%(team_name)s团队及其销售人员的线索，因为没有未分配的线索与它的条件相匹配。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to %(team_name)s team because it has no capacity. Add "
"capacity to its salespersons."
msgstr "没有分配给%(team_name)s团队的线索，因为它没有能力。为其销售人员增加能力。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to any team or salesperson. Check your Sales Teams and "
"Salespersons configuration as well as unassigned leads."
msgstr "没有分配给任何团队或销售人员的线索。检查您的销售团队和销售人员配置以及未分配的线索。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "No data found!"
msgstr "没有数据!"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "No data yet!"
msgstr "还没有数据耶！"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No lead assigned to salespersons because no unassigned lead matches their "
"domains."
msgstr "没有分配给销售人员的线索，因为没有未分配的线索符合他们的条件。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No new lead allocated to %(team_name)s team because no unassigned lead "
"matches its domain."
msgstr "没有新的线索分配给%(team_name)s团队，因为没有未分配的线索符合其条件。"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No new lead allocated to the teams because no lead match their domains."
msgstr "没有新的线索分配给各小组，因为没有符合他们条件的线索。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__grey
msgid "No next activity planned"
msgstr "没有安排下一个活动"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "No opportunity to display!"
msgstr "没有商机!"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr "没有销售员"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_normalized
msgid "Normalized Email"
msgstr "规范化邮件"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr "没足够的库存"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr "备注"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Now, <b>add your Opportunity</b> to your Pipeline."
msgstr "点击这里<b>添加商机</b>， 并且加入到您的商机渠道."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of errors"
msgstr "错误数"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_number
msgid ""
"Number of interval type between each cron run (e.g. each 2 days or each 4 "
"days)"
msgstr "每个cron运行之间的间隔类型的数量（例如，每2天或每4天）。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_count
msgid "Number of leads and opportunities assigned this last month."
msgstr "上个月分配的潜在客户和商机数。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要操作消息数量"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "递送错误消息数量"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_unread_counter
msgid "Number of unread messages"
msgstr "未读消息数量"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr "Odoo帮助您追踪销售渠道，关注潜在销售机会并更好的预测未来收入。"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid ""
"Odoo's artificial intelligence engine predicts the success rate of each "
"opportunity based on your history. You can always update the success rate "
"manually, but if you let Odoo do the job the score is updated while the "
"opportunity moves forward in your sales cycle."
msgstr ""
"Odoo 的人工智能引擎根据您的历史预测每个机会的成功率。您始终可以手动更新成功率，但如果您让 Odoo "
"完成工作，则在销售循环中机会向前移动时，分数将更新。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Open Opportunities"
msgstr "打开商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Open Opportunity"
msgstr "打开商机"

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr "丢单原因"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr "商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr "商机分析"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""
"商机分析让您立即有机会获取预计收入、计划成本、错过的期限或每次机会的交流次数等信息。此报告主要由销售经理使用，以便对销售渠道的渠道进行周期性评估。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph_forecast
msgid "Opportunities Forecast"
msgstr "机会预测"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr "商机收入"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr "已签单商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr "分配给我的商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_count
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__opportunity
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunity"
msgstr "商机"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr "商机已创建"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr "失去商机"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_restored
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_restored
msgid "Opportunity Restored"
msgstr "商机已恢复"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr "商机状态已改变"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr "赢得的商机"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr "失去的商机"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_restored
msgid "Opportunity restored"
msgstr "恢复的商机"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr "赢得的商机"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "线程（记录）的可选 ID，所有传入消息都将附加到该 ID，即使它们未回复该线程（记录）。如果设置，这将完全禁用新记录的创建。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Organization / Contact"
msgstr "组织/联系人"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_3_years
msgid "Over 3 years"
msgstr "3 年以上"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_5_years
msgid "Over 5 years "
msgstr "5 年以上"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_team_overdue_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr "逾期商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_amount
msgid "Overdue Opportunities Revenues"
msgstr "逾期商机收入"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Overdue Opportunity"
msgstr "逾期商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_user_id
msgid "Owner"
msgstr "所有者"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_model_id
msgid "Parent Model"
msgstr "父模型"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "父记录线程 ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"持有别名的父模型。持有别名引用的模型不一定是用户提供(example: project (parent_model) and task (model))"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_email_update
msgid "Partner Email will Update"
msgstr "合作伙伴的电子邮件将被更新"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_phone_update
msgid "Partner Phone will Update"
msgstr "伙伴电话将更新"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr "合作伙伴黑名单"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Phone"
msgstr "电话"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "电话黑名单"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_phone_state
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_state
msgid "Phone Quality"
msgstr "电话质量"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_mobile_search
msgid "Phone/Mobile"
msgstr "电话/手机"

#. module: crm
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
#, python-format
msgid "Pipeline"
msgstr "渠道"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
msgid "Pipeline Activities"
msgstr "管道活动"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr "渠道分析"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__name
msgid "Plan Name"
msgstr "计划名称"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"Please select more than one element (lead or opportunity) from the list "
"view."
msgstr "请从列表视图中选择一个以上的线索或商机。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_fields
msgid "Pls Fields"
msgstr "Pis 字段"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_start_date
msgid "Pls Start Date"
msgstr "Pis 开始日期"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"使用邮件门方式在文档上发布邮件策略。\n"
"- 每个人： 每个人都可以发布\n"
"- 合作伙伴：仅经过身份验证的合作伙伴\n"
"- 关注者：仅关注相关文档或以下渠道的成员\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_ids
msgid "Potential Duplicate Lead"
msgstr "潜在的重复线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_count
msgid "Potential Duplicate Lead Count"
msgstr "潜在的重复线索数量"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_field_labels
msgid "Predictive Lead Scoring Field Labels"
msgstr "预测性线索评分字段标签"

#. module: crm
#: model:ir.actions.server,name:crm.website_crm_score_cron_ir_actions_server
#: model:ir.cron,cron_name:crm.website_crm_score_cron
#: model:ir.cron,name:crm.website_crm_score_cron
msgid "Predictive Lead Scoring: Recompute Automated Probabilities"
msgstr "预测潜在顾客评分：重新计算自动概率"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr "优先级"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr "概率"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Probability (%)"
msgstr "概率(%)"

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr "建议"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly_prorated
msgid "Prorated MRR"
msgstr "按比例分配的维修与保养"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__prorated_revenue
msgid "Prorated Revenue"
msgstr "按比例分摊收入"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_forecast
msgid "Prorated Revenues"
msgstr "按比例计算的收入"

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr "验证"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Ready to boost your sales? Let's have a look at your <b>Pipeline</b>."
msgstr "准备好提高您的销售额了吗？让我们来看看您的<b>渠道</b>。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "记录线程 ID"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_plan
msgid "Recurring Plan"
msgstr "周期计划"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_recurring_plan_action
#: model:ir.ui.menu,name:crm.crm_recurring_plan_menu_config
msgid "Recurring Plans"
msgstr "周期计划"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Recurring Revenue"
msgstr "周期收益"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_recurring_revenues
msgid "Recurring Revenues"
msgstr "周期收益"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr "引荐于"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
msgid "Related Customer"
msgstr "相关客户"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_number
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Repeat every"
msgstr "重复间隔为每"

#. module: crm
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid "Repeat frequency should be positive."
msgstr "重复频率应该是积极的。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__auto
msgid "Repeatedly"
msgstr "循环"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr "报告"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr "要求"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Reschedule"
msgstr "重新安排"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Restore"
msgstr "还原"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_use_auto_assignment
msgid "Rule-Based Assignment"
msgstr "基于规则的分配"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Running"
msgstr "运行中"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr "销售"

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model:ir.model.fields,field_description:crm.field_res_partner__team_id
#: model:ir.model.fields,field_description:crm.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr "销售团队"

#. module: crm
#: model:ir.model,name:crm.model_crm_team_member
msgid "Sales Team Member"
msgstr "销售团队成员"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Sales Team Settings"
msgstr "销售团队设置"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr "销售团队"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr "销售员"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salespersons"
msgstr "销售员"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized
msgid "Sanitized Number"
msgstr "去除数字"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Schedule activities to keep track of everything you have to do."
msgstr "安排活动，以记录您要做的一切。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr "搜索线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr "搜索商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Select Leads/Opportunities"
msgstr "选择商机/线索"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mail_compose
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send email"
msgstr "发送邮件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__sequence
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr "序号"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid ""
"Set Recurring Plans on Opportunities to display the contracts' renewal "
"periodicity<br>(e.g: Monthly, Yearly)."
msgstr "设置机会周期计划以显示合同的续订周期<br>(e.g: Monthly, Yearly)."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr "在您的商机渠道中设置一个新的节点"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr "设置"

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr "显示线索菜单"

#. module: crm
#: model:res.groups,name:crm.group_use_recurring_revenues
msgid "Show Recurring Revenues Menu"
msgstr "显示周期收入菜单"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr "显示下一个操作日期在今天之前的所有商机"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only lead"
msgstr "仅显示线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunity"
msgstr "仅显示商机"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_optout
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_optout
msgid "Skip auto assignment"
msgstr "跳过自动分配"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Snooze 7d"
msgstr "延后7天"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_source_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr "来源"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr "特定团队使用这个节点后，其他团队将无法查看或使用此节点。"

#. module: crm
#: code:addons/crm/models/res_config_settings.py:0
#: code:addons/crm/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Stage"
msgstr "阶段"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr "阶段已改变"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr "阶段名称"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr "阶段搜索"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr "阶段已改变"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr "阶段"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""
"节点让销售人员能够轻松地跟踪一个特定的机会，\n"
"可在销售过程中定位。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "Start scheduling activities on your opportunities"
msgstr "开始为您商机计划活动"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_state_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "State"
msgstr "省份"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态 \n"
" 逾期：已经超过截止日期 \n"
" 现今：活动日期是当天 \n"
" 计划：未来活动。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr "街道"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street 2..."
msgstr "详细地址..."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street..."
msgstr "街道..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr "街道 2"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Submit"
msgstr "提交"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr "子类型"

#. module: crm
#: model:ir.model,name:crm.model_ir_config_parameter
msgid "System Parameter"
msgstr " 系统参数"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "标签"

#. module: crm
#: model:crm.lead.scoring.frequency.field,name:crm.frequency_field_tag_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "标签"

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Teams"
msgstr "团队"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_member_config
msgid "Teams Members"
msgstr "团队成员"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr "与此渠道相关联的电子邮件地址。收到的新电子邮件会自动创建分配给该渠道的新线索。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr "这个别名对应的模型(Odoo文档类型。 任何未回复现有记录的传入电子邮件都将导致创建此模型的新记录(例如。 a项目任务)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "电子邮件别名的名称，例如， 'jobs'如果您想要捕获 <<EMAIL>>"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr "在销售线索转化为商机时对应创建业务伙伴的公司名称。"

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_recurring_plan_check_number_of_months
msgid "The number of month can't be negative."
msgstr "月份数不能为负数。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"基于此别名接收到的邮件，创建记录的所有人。若此字段没有设置，系统将试图根据发送者(From)地址来查找正确的所有者，如果对于那个地址没有系统用户被发现，将使用系统管理员账户。"

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_check_probability
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr "这笔交易达成的概率应在0%到100%之间！"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "The success rate is computed based on"
msgstr "成功率的计算基于"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid ""
"The success rate is computed based on the stage, but you can add more fields"
" in the statistical analysis."
msgstr "成功率是根据阶段计算的，但您可以在统计分析中添加更多字段。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This analysis shows you how many leads have been created per month."
msgstr "此分析显示每月创建了多少潜在客户。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "这个工具条允许你基于安排的事件来过滤商机。"

#. module: crm
#. openerp-web
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "This bar also allows you to switch stage."
msgstr "此栏还允许您切换舞台。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"This can be used to automatically assign leads to sales persons based on "
"rules"
msgstr "这可用于根据规则自动将潜在客户分配给销售人员"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "This can be used to compute statistical probability to close a lead"
msgstr "这个用来计算关闭一条线索的统计学概率"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "此电子邮件已列入群发邮件黑名单。单击以取消黑名单。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "这个字段用来在邮件地址上搜索，因为初始的邮件字段会包含严格来说至少两个邮件地址。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr "名称会帮助您追踪不同营销活动，例如，节能减排、圣诞特辑"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "这是传达方法，例如，明信片、电子邮件或横幅广告"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "这是链接源，例如，搜索引擎、其他域名或电子邮件列表名称"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "此电话号码被列入短信营销黑名单。单击以取消黑名单。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "当此阶段中没有任何记录要呈现时，将在看板视图中被收藏。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "This will assign leads to all members. Do you want to proceed?"
msgstr "这会将线索分配给所有成员。您想继续吗？"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_0
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Tip: Convert incoming emails into opportunities"
msgstr "提示：转换收到的邮件到商机"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_1
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid "Tip: Did you know Odoo has built-in lead mining?"
msgstr "提示：您知道 Odoo 有内置引领前沿技术吗？"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_4
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid "Tip: Do not waste time recording customers' data"
msgstr "提示：不要浪费时间记录客户数据"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_3
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid "Tip: Manage your pipeline"
msgstr "提示：管理渠道"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_2
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid "Tip: Opportunity win rate is predicted with AI"
msgstr "提示：通过 AI 预测机会赢率"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_5
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid "Tip: Turn a selection of opportunities into a map"
msgstr "提示：将所选商机转换为地图"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Title"
msgstr "标题"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"To prevent data loss, Leads and Opportunities can only be merged by groups "
"of %(max_length)s."
msgstr "为防止数据丢失，线索和机会只能按 %(max_length)s 组进行合并。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr "今天的活动"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr "太昂贵"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Tracking"
msgstr "追踪"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Trailing 12 months"
msgstr "跟踪 12 个月"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try sending an email"
msgstr "尝试发送一个邮件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Type"
msgstr "类型"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
msgid "Type is used to separate Leads and Opportunities"
msgstr "类型用于区分销售线索和商机"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的例外活动类型。"

#. module: crm
#: model:ir.model,name:crm.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM 营销活动"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_company_ids
msgid "UX: Limit to lead company or all if no company"
msgstr "UX：如果没有公司，则限制为领导公司或全部"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr "未分派"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Lead"
msgstr "未分派线索"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Leads"
msgstr "未分派线索"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unread Messages"
msgstr "未读消息"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息计数器"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
msgid "Upcoming Closings"
msgstr "即将结束"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_pls_update_action
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Update Probabilities"
msgstr "更新概率"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_pls_update
msgid "Update the probabilities"
msgstr "更新概率"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__use_leads
msgid "Use Leads"
msgstr "使用线索"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Use Lost Reasons to report on why opportunities are lost (e.g.\"Undercut by "
"competitors\")."
msgstr "使用 丢失原因 报告失去机会的原因（例如“被竞争对手削弱”）。"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__each_exist_or_create
msgid "Use existing partner or create"
msgstr "使用已有的业务伙伴或新建"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr "在创建机会或客户之前，如果需要确认的步骤，请使用线索。它可以是您收到的名片、您网站上填写的联系表、或您输入的未确认的潜在客户的文件等。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""
"在创建机会或客户之前，如果需要确认的步骤，请使用线索。它可以是您收到的名片、您网站上填写的联系表、或您输入的未确认的潜在客户的文件等。一旦合格，线索就可以转换成您的通讯录中的商业机会和/或新客户。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Use the top left <i>Create</i> button, or send an email to"
msgstr "使用左上角的 <i>创建</i> 按钮，或发送电子邮件至"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"Use the top left <i>Create</i> button, or send an email to %s to test the "
"email gateway."
msgstr "使用左上<i>创建</i>按钮，或发送电子邮件到 %s 以测试电子邮件网关。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid ""
"Use the top left Create button, or configure an email alias to test the "
"email gateway."
msgstr "使用左上角的创建按钮，或配置电子邮件别名来测试电子邮件网关。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid "Use this menu to have an overview of your Pipeline."
msgstr "使用此菜单可以概览您的漏斗。"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_login
msgid "Used to log into the system"
msgstr "用户登录入系统"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr "用于阶段的排序，数字越小越好。"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_company_ids
msgid "User Company"
msgstr "用户公司"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_email
msgid "User Email"
msgstr "用户电子邮件"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_login
msgid "User Login"
msgstr "用户 登录"

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "Users"
msgstr "用户"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__value
msgid "Value"
msgstr "值"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__variable
msgid "Variable"
msgstr "可变"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__3
msgid "Very High"
msgstr "非常高"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Visits to Leads"
msgstr "网站访问线索"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr "我们没有人/能力"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr "网站"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr "网上沟通记录"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr "联系人网站"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__weeks
msgid "Weeks"
msgstr "周"

#. module: crm
#: model:mail.template,name:crm.mail_template_demo_crm_lead
msgid "Welcome Demo"
msgstr "欢迎演示"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0 model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Won"
msgstr "赢得"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__won_count
msgid "Won Count"
msgstr "赢得个数"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr "签单是商机的最终目标"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Yeah! Deal of the last 7 days for the team."
msgstr "团队最近 7 天的交易。"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_yearly
msgid "Yearly"
msgstr "每年"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You don't have the access needed to run this cron."
msgstr "您没有运行这个定时任务的权限。"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 30 days."
msgstr "您在过去的 30 天里打破了您的个人记录。"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 7 days."
msgstr "您在过去的 7 天里打破了您的个人记录。"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""
"您将能够计划来自机会的会议和电话，\n"
"将其转换为报价，附加相关文档，\n"
"跟踪所有讨论等等。"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "ZIP"
msgstr "邮编"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr "邮政编码"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "e.g. Negotiation"
msgstr "例如谈判"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. Product Pricing"
msgstr "例如, 产品定价"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "e.g. Too expensive"
msgstr "例如太贵了"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. https://www.odoo.com"
msgstr "例如：https://www.odoo.com"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "for the leads created as of the"
msgstr "对于目前创建的潜在客户日期截至"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr "team_count"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "to test the email gateway."
msgstr "测试邮件网关"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "to your CRM. This email address is configurable by sales team members."
msgstr "到您的 CRM。此电子邮件地址可由销售团队成员配置。"

#. module: crm
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "unknown"
msgstr "未知的"
