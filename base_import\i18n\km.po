# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON>g <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:05+0000\n"
"PO-Revision-Date: 2018-10-02 10:05+0000\n"
"Last-Translator: <PERSON><PERSON>nn Seang <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:612
#, python-format
msgid "%d records were successfully imported"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:647
#, python-format
msgid "(%d more)"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:378
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:582
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:102
#, python-format
msgid "Cancel"
msgstr "លុបចោល"

#. module: base_import
#: code:addons/base_import/models/base_import.py:693
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:788
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:211
#: code:addons/base_import/static/src/js/import_action.js:228
#, python-format
msgid "Comma"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:825
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:108
#, python-format
msgid "Create if doesn't exist"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:107
#, python-format
msgid ""
"Creates new records if they can't be found (instead of failing to import). "
"Note that the value in the column will be used as the new record's 'name', "
"and assumes this is sufficient to create the record."
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__currency_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__currency_id
msgid "Currency"
msgstr "រូបិយវត្ថុ"

#. module: base_import
#: code:addons/base_import/models/base_import.py:207
#: code:addons/base_import/models/base_import.py:212
#, python-format
msgid "Database ID"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:83
#, python-format
msgid "Date Format:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:84
#, python-format
msgid "Datetime Format:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:88
#, python-format
msgid "Decimal Separator:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:56
#, python-format
msgid "Defer parent/child computation"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:445
#, python-format
msgid "Don't import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:229
#, python-format
msgid "Dot"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:87
#, python-format
msgid "Download"
msgstr "ទាញយក"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__dt
msgid "Dt"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:78
#, python-format
msgid "Encoding:"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:790
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:289
#, python-format
msgid "Error cell found while reading XLS/XLSX file: %s"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:624
#, python-format
msgid "Everything seems valid."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:82
#, python-format
msgid "Excel files are recommended as fields formatting is automatic."
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:169
#: code:addons/base_import/models/base_import.py:206
#, python-format
msgid "External ID"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:808
#: code:addons/base_import/models/base_import.py:814
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr ""

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:137
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:13
#, python-format
msgid "Formatting Options…"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:671
#, python-format
msgid "Get all possible values"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:658
#, python-format
msgid "Here are the possible values:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:138
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:67
#, python-format
msgid ""
"If the file contains\n"
"                the column names, Odoo can try auto-detecting the\n"
"                field corresponding to the column. This makes imports\n"
"                simpler especially when the file has many columns."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:45
#, python-format
msgid ""
"If the model uses openchatter, history tracking                             "
"will set up subscriptions and send notifications"
"                             during the import, but lead to a slower import."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:54
#, python-format
msgid ""
"If the model uses parent/child relations, computing the                     "
"parent / child relation occurs on every line, and lead to a slower import."
"                     Defering it can speed up import."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:98
#: code:addons/base_import/static/src/xml/base_import.xml:169
#, python-format
msgid "Import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:91
#, python-format
msgid "Import FAQ"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:140
#, python-format
msgid "Import a File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:612
#, python-format
msgid "Import completed"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:136
#, python-format
msgid "Import preview failed due to:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:579
#, python-format
msgid ""
"Import timed out. Please retry. If you still encounter this issue, the file "
"may be too big for the system's configuration, try to split it (import less "
"records per file)."
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:101
#, python-format
msgid "Load File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:364
#, python-format
msgid "Load New File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:41
#, python-format
msgid "Map your columns to import"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:85
#, python-format
msgid "Need Help?"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:231
#, python-format
msgid "No Separator"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:513
#, python-format
msgid "Normal Fields"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__othervalue
msgid "Other Variable"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__parent_id
msgid "Parent"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:514
#, python-format
msgid "Relation Fields"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:100
#, python-format
msgid "Reload File"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:79
#, python-format
msgid "Select a CSV or Excel file to import."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:212
#, python-format
msgid "Semicolon"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:79
#, python-format
msgid "Separator:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:66
#, python-format
msgid "Show fields of relation fields (advanced)"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__somevalue
msgid "Some Value"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:214
#, python-format
msgid "Space"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:213
#, python-format
msgid "Tab"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:99
#, python-format
msgid "Test Import"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_preview
msgid "Tests : Base Import Model Preview"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char
msgid "Tests : Base Import Model, Character"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_noreadonly
msgid "Tests : Base Import Model, Character No readonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_readonly
msgid "Tests : Base Import Model, Character readonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_required
msgid "Tests : Base Import Model, Character required"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_states
msgid "Tests : Base Import Model, Character states"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_stillreadonly
msgid "Tests : Base Import Model, Character still readonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o
msgid "Tests : Base Import Model, Many to One"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_related
msgid "Tests : Base Import Model, Many to One related"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required
msgid "Tests : Base Import Model, Many to One required"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required_related
msgid "Tests : Base Import Model, Many to One required related"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m
msgid "Tests : Base Import Model, One to Many"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m_child
msgid "Tests : Base Import Model, One to Many child"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_complex
msgid "Tests: Base Import Model Complex"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_float
msgid "Tests: Base Import Model Float"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:80
#, python-format
msgid "Text Delimiter:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:62
#, python-format
msgid ""
"The first row\n"
"                 contains the label of the column"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:87
#, python-format
msgid "Thousands Separator:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:47
#, python-format
msgid "Track history during import"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:254
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:255
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "Users"
msgstr "អ្នកប្រើ"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__value
msgid "Value"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value2
msgid "Value2"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:760
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:631
#, python-format
msgid "You must configure at least one field to import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:641
#, python-format
msgid "at row %d"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:643
#, python-format
msgid "between rows %d and %d"
msgstr ""
