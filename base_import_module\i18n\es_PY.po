# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import_module
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:00+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Paraguay) (https://www.transifex.com/odoo/teams/41243/es_PY/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_PY\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:31
#, python-format
msgid "Could not select database '%s'"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_create_date
msgid "Created on"
msgstr "Creado en"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_display_name
msgid "Display Name"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:112
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:104
#, python-format
msgid "File is not a zip file!"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_force
msgid "Force init"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module_force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_import_message
msgid "Import Message"
msgstr ""

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module_imported
msgid "Imported Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module___last_update
msgid "Last Modified on"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_module_file
msgid "Module .ZIP file"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:102
#, python-format
msgid "No file sent."
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:25
#, python-format
msgid "Only administrators can upload a module"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module_state
msgid "Status"
msgstr "Estado"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:40
#: code:addons/base_import_module/models/ir_module.py:47
#, python-format
msgid "Studio customizations require Studio"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:42
#, python-format
msgid "Unmet module dependencies: %s"
msgstr ""

#. module: base_import_module
#: selection:base.import.module,state:0
msgid "done"
msgstr ""

#. module: base_import_module
#: selection:base.import.module,state:0
msgid "init"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "ir.ui.view"
msgstr ""
