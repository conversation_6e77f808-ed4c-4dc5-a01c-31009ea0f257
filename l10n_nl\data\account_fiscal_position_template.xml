<?xml version="1.0" encoding="utf-8"?>
<openerp>
    <data>

        <!-- Fiscal Position Templates -->
        <record id="fiscal_position_template_national" model="account.fiscal.position.template">
            <field name="sequence">1</field>
            <field name="name">Binnenland</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_id" ref="base.nl"/>
        </record>
        <record id="fiscal_position_template_transferred" model="account.fiscal.position.template">
            <field name="name">BTW verlegd</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
        </record>
        <record id="fiscal_position_template_eu_private" model="account.fiscal.position.template">
            <field name="sequence">2</field>
            <field name="name">EU landen privaat</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
            <field name="auto_apply" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>
        <record id="fiscal_position_template_eu" model="account.fiscal.position.template">
            <field name="sequence">3</field>
            <field name="name">EU landen</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>
        <record id="fiscal_position_template_non_eu" model="account.fiscal.position.template">
            <field name="sequence">4</field>
            <field name="name">Niet-EU landen</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
            <field name="auto_apply" eval="True"/>
        </record>

        <!-- Fiscal Position for customers within the EU who don't have to report taxes -->
        <record id="fiscal_position_template_eu_no_taxes_report" model="account.fiscal.position.template">
            <field name="name">Installatie en Afstandsverkopen</field>
            <field name="chart_template_id" ref="l10nnl_chart_template" />
        </record>
        <record id="position_tax_eu_no_taxes_report_1" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_0"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_2" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_verk_0"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_3" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_6"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_4" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_9"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_5" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_21"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_6" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_overig"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_7" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_0_d"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_8" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_6_d"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_9" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_9_d"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_10" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_21_d"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
        <record id="position_tax_eu_no_taxes_report_11" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_eu_no_taxes_report"/>
            <field name="tax_src_id" ref="btw_overig_d"/>
            <field name="tax_dest_id" ref="btw_X2"/>
        </record>
    </data>
</openerp>
