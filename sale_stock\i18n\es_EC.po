# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_stock
#
# Translators:
# <PERSON> <aju<PERSON><EMAIL>>, 2015
# <PERSON>, 2015
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-01 04:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterms:</strong>"
msgstr "<strong>Incoterms:</strong>"

#. module: sale_stock
#: selection:sale.config.settings,module_delivery:0
msgid "Allow adding shipping costs"
msgstr "Permitir añadir gastos de envío"

#. module: sale_stock
#: selection:sale.config.settings,group_mrp_properties:0
msgid "Allow setting manufacturing order properties per order line (advanced)"
msgstr ""
"Permitir establecer prioridades de orden de fabricación por línea de orden "
"(avanzado)"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_config_settings_group_mrp_properties
msgid "Allows you to tag sales order lines with properties."
msgstr "Permite etiquetar las líneas de pedido de venta con propiedades."

#. module: sale_stock
#: selection:sale.config.settings,group_route_so_lines:0
msgid "Choose specific routes on sales order lines (advanced)"
msgstr "Elegir rutas específicas por línea de pedidos de venta (avanzado)"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_default_picking_policy
msgid "Default Shipping Policy"
msgstr "Política de envío predeterminado"

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver all products at once"
msgstr "Entregar todos los productos a la vez"

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver each product when available"
msgstr "Entregar cada producto cuando esté disponible"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Entrega"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_delivery_count
msgid "Delivery Orders"
msgstr "Órdenes de entrega"

#. module: sale_stock
#: selection:sale.config.settings,group_mrp_properties:0
msgid "Don't use manufacturing properties (recommended as its easier)"
msgstr "No use propiedades de fabricación (recomendado porque es más fácil)"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_route_so_lines
msgid "Enable Route on Sales Order Line"
msgstr "Habilitar rutas en las líneas de los pedidos de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_product_product_expense_policy
msgid "Expense Invoice Policy"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_product_product_expense_policy
msgid ""
"If you invoice at cost, the expense will be invoiced on the sale order at "
"the cost of the analytic line;if you invoice at sales price, the price of "
"the product will be used instead."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_account_invoice_incoterms_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_incoterm
msgid "Incoterms"
msgstr "Incoterms"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_account_invoice_incoterms_id
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-the-"
"art transportation practices."
msgstr ""
"Los incoterms son una serie de términos de venta. Se usan para dividir los "
"costes de la transacción y las responsabilidades entre el comprador y el "
"vendedor y reflejan las últimas prácticas en el transporte"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Los términos de comercio internacional son una serie de condiciones "
"comerciales usadas en las transacciones internacionales."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Rutas de inventario"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice_line
msgid "Invoice Line"
msgstr "Linea de Factura"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised "
"date, to cope with unexpected delays in the supply chain."
msgstr ""
"Margen de error para las fechas prometidas a los clientes. Los productos "
"serán planificados para el abastecimiento y entrega días antes de la fecha "
"prometida actual, para lidiar con posibles retrasos inesperados en la cadena "
"de suministro."

#. module: sale_stock
#: selection:sale.config.settings,group_route_so_lines:0
msgid "No order specific routes like MTO or drop shipping"
msgstr ""
"Sin direcciones especificas como un MTO (Orden personalizada para el "
"cliente) o entrega directa desde el proveedor."

#. module: sale_stock
#: selection:sale.config.settings,module_delivery:0
msgid "No shipping costs on sales orders"
msgstr "Sin costos de envío en los pedidos de venta"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:156
#, python-format
msgid "Not enough inventory!"
msgstr "¡No hay suficiente inventario!"

#. module: sale_stock
#: code:addons/sale_stock/res_config.py:42
#, python-format
msgid "Only administrators can change the settings"
msgstr "Sólo los administradores pueden cambiar esta configuración"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_group_route_so_lines
msgid "Order Routing"
msgstr "Ruteado de Ordenes"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:167
#, python-format
msgid "Ordered quantity decreased!"
msgstr "¡La cantidad pedida ha sido reducida!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_product_packaging
msgid "Packaging"
msgstr "Empaquetado"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_ids
msgid "Picking associated to this sale"
msgstr "Movimiento de inventario asociado a esta venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_order
msgid "Procurement"
msgstr "Abastecimiento"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_product
#, fuzzy
msgid "Product"
msgstr "Plantilla producto"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla producto"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_group_mrp_properties
msgid "Properties on SO Lines"
msgstr "Propiedades por línea de pedido de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_route_id
msgid "Route"
msgstr "Ruta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking_sale_id
msgid "Sale Order"
msgstr "Pedido de venta"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_form_view_inherit_sale_stock
msgid "Sale Order Lines"
msgstr "Líneas de pedido de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Aviso para pedido de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea pedido de venta"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Estadísticas pedidos de venta"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company_security_lead
msgid "Sales Safety Days"
msgstr "Días de seguridad para ventas"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route_sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Seleccionable en las líneas de los pedidos de venta"

#. module: sale_stock
#: selection:sale.config.settings,default_picking_policy:0
msgid "Ship all products at once, without back orders"
msgstr "Enviar todos los productos a la vez, sin entregas retrasadas"

#. module: sale_stock
#: selection:sale.config.settings,default_picking_policy:0
msgid "Ship products when some are available, and allow back orders"
msgstr ""
"Enviar productos cuando según disponibilidad y permitir entregas retrasadas"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_module_delivery
#: model_terms:ir.ui.view,arch_db:sale_stock.view_sales_config_inherit_sale_stock
msgid "Shipping"
msgstr "Envío"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Shipping Information"
msgstr "Información de envio"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_policy
msgid "Shipping Policy"
msgstr "Política de facturación"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:213
#, fuzzy, python-format
msgid "This product is packaged by %s %s. You should sell %s %s."
msgstr "Este producto es empaquetado por %s %s. Debería vender %s %s."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Transferir"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report_warehouse_id
msgid "Warehouse"
msgstr "Bodega"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:212
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:168
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"¡Está reduciendo la cantidad pedida! No olvide actualizar manualmente el "
"orden de entrega si es necesario."

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:157
#, python-format
msgid ""
"You plan to sell %.2f %s but you only have %.2f %s available!\n"
"The stock on hand is %.2f %s."
msgstr ""
"¡Pretende vender %.2f %s, pero solo tiene %.2f %s disponibles!\n"
"El stock disponible es de %.2f %s"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_config_settings
msgid "sale.config.settings"
msgstr "sale.config.settings"
