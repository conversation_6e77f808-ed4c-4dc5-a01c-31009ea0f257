<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <function model="l10n_latam.document.type" name="write">
            <value model="l10n_latam.document.type" eval="obj().search([('country_id.code', '=', 'AR'), ('code', 'in', ['5','10','14','16','22','30','31','32','34','35','36','37','38','50','55','56','57','58','59','60','61','65','67','68','70','71','73','74','75','80','84','85','86','87','88','89','90','91','92','93','94','95','96','97','101','102','103','104','94','23','24','25','26','33','331','332','150','151','157','158','159','160','161','162','163','164','165','166','167','168','169','170','171','172','180','182','183','185','186','188','189','190','191'])]).ids"/>
            <value eval="{'active': False}"/>
        </function>
    </data>
</odoo>
