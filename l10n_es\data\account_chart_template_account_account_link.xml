<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!--We complete the declaration of the chart of account here.
        These data are to be created once the accounts have been created in database,
        they finish "linking" the CoA to them.-->
        <record id="account_chart_template_common" model="account.chart.template">
            <field name="property_account_receivable_id" ref="account_common_4300"/>
            <field name="property_account_payable_id" ref="account_common_4100"/>
            <field name="property_account_expense_categ_id" ref="account_common_600"/>
            <field name="property_account_income_categ_id" ref="account_common_7000"/>
            <field name="income_currency_exchange_account_id" ref="account_common_768"/>
            <field name="expense_currency_exchange_account_id" ref="account_common_668"/>
            <field name="default_cash_difference_income_account_id" ref="account_common_778"/>
            <field name="default_cash_difference_expense_account_id" ref="account_common_678"/>
            <field name="default_pos_receivable_account_id" ref="account_common_4301" />
            <field name="account_journal_suspense_account_id" ref="account_common_572998" />
            <field name="account_journal_payment_debit_account_id" ref="account_common_4312" />
            <field name="account_journal_payment_credit_account_id" ref="account_common_411" />
        </record>

    </data>
</odoo>
