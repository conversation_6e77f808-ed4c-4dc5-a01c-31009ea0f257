<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="digest_tip_purchase_0" model="digest.tip">
            <field name="name">Tip: How to keep late receipts under control?</field>
            <field name="sequence">100</field>
            <field name="group_id" ref="purchase.group_purchase_user" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: How to keep late receipts under control?</p>
    <p class="tip_content">When creating a purchase order, have a look at the vendor's <i>On Time Delivery</i> rate: the percentage of products shipped on time. If it is too low, activate the <i>automated reminders</i>. A few days before the due shipment, Odoo will send the vendor an email to ask confirmation of shipment dates and keep you informed in case of any delays. To get the vendor's performance statistics, click on the OTD rate.</p>
    <img src="/purchase/static/src/img/OTDPurchase.gif" class="illustration_border" />
</div>
            </field>
        </record>
        <record id="digest_tip_purchase_1" model="digest.tip">
            <field name="name">Tip: Never miss a purchase order</field>
            <field name="sequence">2000</field>
            <field name="group_id" ref="purchase.group_purchase_user" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Never miss a purchase order</p>
    <p class="tip_content">When sending a purchase order by email, Odoo asks the vendor to acknowledge the reception of the order. When the vendor acknowledges the order by clicking on a button in the email, the information is added on the purchase order. Use filters to track orders that have not been acknowledged.</p>
</div>
            </field>
        </record>
    </data>
</odoo>
