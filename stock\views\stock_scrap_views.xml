<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="stock_scrap_search_view" model="ir.ui.view">
            <field name="name">stock.scrap.search</field>
            <field name="model">stock.scrap</field>
            <field name="arch" type="xml">
                <search string="Search Scrap">
                    <field name="name" string="Reference"/>
                    <field name="product_id"/>
                    <field name="location_id"/>
                    <field name="scrap_location_id"/>
                    <field name="create_date"/>
                    <group expand="0" string="Group By">
                        <filter string="Product" name="product" domain="[]" context="{'group_by':'product_id'}"/>
                        <filter string="Location" name="location" domain="[]" context="{'group_by':'location_id'}"/>
                        <filter string="Scrap Location" name="scrap_location" domain="[]" context="{'group_by':'scrap_location_id'}"/>
                        <filter string="Transfer" name="transfer" domain="[]" context="{'group_by':'picking_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="stock_scrap_form_view" model="ir.ui.view">
            <field name="name">stock.scrap.form</field>
            <field name="model">stock.scrap</field>
            <field name="arch" type="xml">
                <form string="Scrap">
                    <header>
                        <button name="action_validate" states="draft" string="Validate" type="object" class="oe_highlight" context="{'not_unlink_on_discard': True}" data-hotkey="v"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,done" />
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" name="action_get_stock_picking"
                                    string="Stock Operation" type="object"
                                    attrs="{'invisible':[('picking_id','=',False)]}" icon="fa-cogs"/>
                            <field name="picking_id" invisible="1"/>
                            <button class="oe_stat_button" name="action_get_stock_move_lines"
                                    string="Product Moves" type="object"
                                    attrs="{'invisible':[('move_id','=',False)]}" icon="fa-exchange"/>
                            <field name="move_id" invisible="1"/>
                        </div>
                        <div class="oe_title">
                            <h1><field name="name" nolabel="1"/></h1>
                        </div>
                        <group>
                            <group>
                                <field name="product_id" context="{'default_detailed_type': 'product'}"/>
                                <label for="scrap_qty"/>
                                <div class="o_row">
                                    <field name="scrap_qty"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="product_uom_id" groups="uom.group_uom" force_save="1"/>
                                </div>
                            </group>
                            <group>
                                <field name="lot_id" context="{'default_product_id': product_id, 'default_company_id': company_id}" attrs="{'invisible': ['|',('product_id', '=', False),('tracking', '=', 'none')], 'required': [('tracking', '!=', 'none')]}" groups="stock.group_production_lot"/>
                                <field name="tracking" invisible="1"/>
                                <field name="package_id" groups="stock.group_tracking_lot"/>
                                <field name="owner_id" groups="stock.group_tracking_owner"/>
                                <field name="location_id" options="{'no_create': True, 'no_open': True}" groups="stock.group_stock_multi_locations" force_save="1"/>
                                <field name="scrap_location_id" options="{'no_create': True, 'no_open': True}" groups="stock.group_stock_multi_locations" force_save="1"/>
                                <field name="origin"/>
                                <field name="date_done" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                                <field name="picking_id" attrs="{'invisible': [('picking_id', '=', False)]}"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <record id="stock_scrap_view_kanban" model="ir.ui.view">
            <field name="name">stock.scrap.kanban</field>
            <field name="model">stock.scrap</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1">
                    <field name="name"/>
                    <field name="product_id"/>
                    <field name="date_done"/>
                    <field name="scrap_qty"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_global_click">
                                <div class="row mb4">
                                    <strong class="col-6">
                                        <span t-esc="record.name.value"/>
                                    </strong>
                                    <strong t-if="record.date_done.value" class="col-6 text-right">
                                        <i class="fa fa-clock-o" role="img" aria-label="Date" title="Date"/><span t-esc="record.date_done.value"/>
                                    </strong>
                                    <div class="col-12">
                                        <span t-esc="record.product_id.value"/>
                                    </div>
                                    <div class="col-6">
                                        <span t-esc="record.scrap_qty.value"/>
                                    </div>
                                    <div class="col-6">
                                        <span class="float-right badge badge-secondary">
                                            <field name="state"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="stock_scrap_tree_view" model="ir.ui.view">
            <field name="name">stock.scrap.tree</field>
            <field name="model">stock.scrap</field>
            <field name="arch" type="xml">
                <tree multi_edit="1" sample="1" decoration-info="state == 'draft'">
                    <field name="name" decoration-bf="1"/>
                    <field name="date_done"/>
                    <field name="product_id" readonly="1"/>
                    <field name="scrap_qty"/>
                    <field name="product_uom_category_id" invisible="1"/>
                    <field name="product_uom_id" groups="uom.group_uom"/>
                    <field name="location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                    <field name="scrap_location_id" options="{'no_create': True}" groups="stock.group_stock_multi_locations"/>
                    <field name="company_id" readonly="1" groups="base.group_multi_company"/>
                    <field name="state" widget="badge" decoration-success="state == 'done'" decoration-info="state == 'draft'"/>
                </tree>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_stock_scrap">
            <field name="name">Scrap Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">stock.scrap</field>
            <field name="view_mode">tree,form,kanban,pivot,graph</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Scrap products
              </p><p>
                Scrapping a product will remove it from your stock. The product will
                end up in a scrap location that can be used for reporting purpose.
              </p>
            </field>
        </record>

        <record id="stock_scrap_form_view2" model="ir.ui.view">
            <field name="name">stock.scrap.form2</field>
            <field name="model">stock.scrap</field>
            <field name="arch" type="xml">
                <form string="Scrap">
                    <group>
                        <group>
                            <field name="state" invisible="1"/>
                            <field name="product_id" options="{'no_create': True}" domain="[('id', 'in', context.get('product_ids', []))]"/>
                            <label for="scrap_qty"/>
                            <div class="o_row">
                                <field name="scrap_qty"
                                    attrs="{'readonly': [('tracking', '=', 'serial')]}"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="product_uom_id" groups="uom.group_uom"/>
                            </div>
                        </group>
                        <group>
                            <field name="picking_id" invisible="1"/>
                            <field name="tracking" invisible="1"/>
                            <field name="lot_id" groups="stock.group_production_lot"
                                context="{'default_company_id': company_id, 'default_product_id': product_id}"
                                attrs="{'invisible': ['|',('product_id', '=', False),('tracking', '=', 'none')], 'required': [('tracking', '!=', 'none')]}"/>
                            <field name="package_id" groups="stock.group_tracking_lot"/>
                            <field name="owner_id" groups="stock.group_tracking_owner"/>
                            <field name="company_id" invisible="1"/>
                            <field name="location_id" groups="stock.group_stock_multi_locations" options="{'no_open': True, 'no_create': True}"/>
                            <field name="scrap_location_id" groups="stock.group_stock_multi_locations" options="{'no_open': True, 'no_create': True}" />
                        </group>
                    </group>
                    <footer>
                        <button name="action_validate" string="Done" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                    </footer>
                </form>
            </field>
        </record>

    <menuitem
        id="menu_stock_scrap"
        name="Scrap"
        parent="menu_stock_warehouse_mgmt"
        action="action_stock_scrap"
        sequence="99"/>

</odoo>
