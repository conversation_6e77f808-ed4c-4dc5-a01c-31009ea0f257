# -*- coding: utf-8 -*-

from odoo import fields, models


class HrEmployeePublic(models.Model):
    """
    إصلاح مشاكل الحقول المفقودة في hr.employee.public
    بإضافة الحقول كحقول فارغة لتجنب أخطاء "Invalid field"
    """
    _inherit = "hr.employee.public"

    # الحقول المتعلقة بجواز السفر
    passport_issue_location = fields.Char(
        string='Passport Issue Location',
        readonly=True,
        help="مكان إصدار جواز السفر"
    )
    
    passport_issue_date = fields.Date(
        string="Passport Issue Date",
        readonly=True,
        help="تاريخ إصدار جواز السفر"
    )
    
    passport_end_date = fields.Date(
        string="Passport End Date",
        readonly=True,
        help="تاريخ انتهاء جواز السفر"
    )
    
    referral_date = fields.Date(
        string="Referral Date",
        readonly=True,
        help="تاريخ الإحالة"
    )
    
    national_number = fields.Char(
        string="National Number",
        readonly=True,
        help="الرقم الوطني"
    )
    
    # حقول العنوان
    residence_place = fields.Char(
        string="Residence Place",
        readonly=True,
        help="مكان الإقامة"
    )
    
    city = fields.Char(
        string="City",
        readonly=True,
        help="المدينة"
    )
    
    neighborhood = fields.Char(
        string="Neighborhood",
        readonly=True,
        help="الحي"
    )

    # حقول التوظيف
    connected_with_comp = fields.Selection(
        string="Type of Contract",
        readonly=True,
        help="نوع العقد مع الشركة"
    )
    
    class_id = fields.Selection(
        string="Class ID",
        readonly=True,
        help="الدرجة الوظيفية"
    )
    
    selection_type = fields.Selection(
        string="selection type",
        readonly=True,
        help="نوع التعين"
    )

    join_date = fields.Date(
        string="Join Date",
        readonly=True,
        help="تاريخ الانضمام"
    )

    hiring_date = fields.Date(
        string="Hiring Date",
        readonly=True,
        help="تاريخ التوظيف"
    )

    english_name = fields.Char(
        string="English Name",
        readonly=True,
        help="الاسم بالإنجليزية"
    )

    int_id = fields.Char(
        string="Employee ID",
        readonly=True,
        help="رقم الموظف"
    )

    # حقول شخصية
    bloodtype = fields.Selection(
        string="Blood Type",
        readonly=True,
        help="فصيلة الدم"
    )

    street_name = fields.Char(
        string="Street Name",
        readonly=True,
        help="اسم الشارع"
    )

    closest_point = fields.Char(
        string="Closest Point",
        readonly=True,
        help="أقرب نقطة دالة"
    )
    
    # حقول الطوارئ
    name_emergency1 = fields.Char(
        string="Name of closest relative 1",
        readonly=True,
        help="اسم اقرب الاقارب"
    )
    
    phone_emergency1 = fields.Char(
        string="Phone of closest relative 1",
        readonly=True,
        help="هاتف اقرب الاقارب"
    )
    
    relation_emergency1 = fields.Char(
        string="Relation of closest relative 1",
        readonly=True,
        help="علاقة اقرب الاقارب"
    )
    
    address_emergency1 = fields.Char(
        string="Address of closest relative 1",
        readonly=True,
        help="عنوان اقرب الاقارب"
    )
    
    name_emergency2 = fields.Char(
        string="Name of closest relative 2",
        readonly=True,
        help="اسم اقرب الاقارب"
    )
    
    phone_emergency2 = fields.Char(
        string="Phone of closest relative 2",
        readonly=True,
        help="هاتف اقرب الاقارب"
    )
    
    relation_emergency2 = fields.Char(
        string="Relation of closest relative 2",
        readonly=True,
        help="علاقة اقرب الاقارب"
    )
    
    address_emergency2 = fields.Char(
        string="Address of closest relative 2",
        readonly=True,
        help="عنوان اقرب الاقارب"
    )
    
    # حقول الشهادات
    health_certificate = fields.Char(
        string="Health certificate",
        readonly=True,
        help="شهادة صحية"
    )
    
    health_certificate_date = fields.Date(
        string="Health certificate date",
        readonly=True,
        help="تاريخ البدء"
    )
    
    health_certificate_expiry = fields.Date(
        string="Health certificate expiry",
        readonly=True,
        help="تاريخ الانتهاء"
    )
    
    criminal_certificate = fields.Char(
        string="Criminal certificate",
        readonly=True,
        help="شهادة جنائية"
    )
    
    criminal_certificate_date = fields.Date(
        string="Criminal certificate date",
        readonly=True,
        help="تاريخ البدء"
    )
    
    criminal_certificate_expiry = fields.Date(
        string="Criminal certificate expiry",
        readonly=True,
        help="تاريخ الانتهاء"
    )
    
    study_field_edited = fields.Char(
        string="Study field",
        readonly=True,
        help="مجال الدراسة"
    )
    
    certificate_issue_date = fields.Date(
        string="Certificate issue date",
        readonly=True,
        help="تاريخ اصدار الشهادة"
    )
    
    certificate_issue_place = fields.Char(
        string="Certificate issue place",
        readonly=True,
        help="مكان اصدار الشهادة"
    )
    
    # حقول إضافية
    overtime_hour_rate = fields.Float(
        string="Over Time Hour Rate",
        readonly=True,
        help="معدل ساعة العمل الإضافي"
    )
    
    supporting_family = fields.Boolean(
        string="Supporting Family",
        readonly=True,
        help="دعم الأسرة"
    )
