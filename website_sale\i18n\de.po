# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# Friederi<PERSON> Fast<PERSON>ling-Nesselbosch, 2022
# jans23, 2022
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-26 11:51+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " Abgerufene Daten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "“-Kategorie."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.option_collapse_categories_recursive
msgid "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"
msgstr "#{'Unfold' if c.id in category.parents_and_self.ids else 'Fold'}"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s Rezension"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "%s Rezensionen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Shipping"
msgstr "&amp; Versand"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "“. Es werden Ergebnisse angezeigt für „"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_1
msgid "1 year"
msgstr "1 Jahr"

#. module: website_sale
#: model:product.template.attribute.value,name:website_sale.product_1_attribute_3_value_2
msgid "2 year"
msgstr "2 Jahre"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>Mitteilungen: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Shipping: </b>"
msgstr "<b>Versand: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "<b>Your order: </b>"
msgstr "<b>Ihre Bestellung: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                30-Tage-Geld-zurück-Garantie<br/>\n"
"                Versand: 2-3 Geschäftstage"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "<i class=\"fa fa-arrow-right\"/> Add payment acquirers"
msgstr "<i class=\"fa fa-arrow-right\"/> Zahlungsanbieter hinzufügen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid "<i class=\"fa fa-bolt mr-2\"/>BUY NOW"
msgstr "<i class=\"fa fa-bolt mr-2\"/>JETZT KAUFEN"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "<i class=\"fa fa-check\"/> Ship to this address"
msgstr "<i class=\"fa fa-check\"/> Versand an diese Adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Back</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                            <span>Zurück</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Return to Cart</span>"
msgstr ""
"<i class=\"fa fa-chevron-left\"/>\n"
"                                    <span>Zurück zum Warenkorb</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_footer
msgid "<i class=\"fa fa-chevron-left\"/> Return to Cart"
msgstr "<i class=\"fa fa-chevron-left\"/> Zurück zum Warenkorb"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-edit\"/> Edit"
msgstr "<i class=\"fa fa-edit\"/> Bearbeiten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "<i class=\"fa fa-eraser mr-1\"/><u>Clear filters</u>"
msgstr "<i class=\"fa fa-eraser mr-1\"/><u>Filter zurücksetzen</u>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Add an address</span>"
msgstr ""
"<i class=\"fa fa-plus-square\"/>\n"
"                                                        <span>Eine Adresse hinzufügen</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Drucken"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"fa fa-shopping-cart mr-2\"/>ADD TO CART"
msgstr "<i class=\"fa fa-shopping-cart mr-2\"/>ZUM WARENKORB HINZUFÜGEN"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">Land ...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">Bundesland ...</option>"

#. module: website_sale
#: code:addons/website_sale/models/crm_team.py:0
#, python-format
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Hier finden Sie alle verlassenen Warenkörbe, d. h. die von den Besuchern Ihrer Website vor über einer Stunde erstellten Warenkörbe, die noch nicht bestätigt wurden.</p>\n"
"                        <p>Sie sollten eine E-Mail an die Kunden senden, um sie zu ermutigen!</p>\n"
"                    "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. Please "
"contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">Eine Änderung des Unternehmensnamens "
"oder der MwSt.-Nummer ist nicht mehr möglich, sobald Dokumente für Ihr Konto"
" ausgestellt wurden. Bitte wenden Sie sich für diesen Vorgang direkt an "
"uns.</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">Zur Kasse</span>\n"
"                                        <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"\">Process Checkout</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span class=\"\">Zur Kasse</span>\n"
"                                    <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Sort "
"By:</span>"
msgstr ""
"<span class=\"d-none d-lg-inline font-weight-bold text-muted\">Sortieren "
"nach:</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"
msgstr ""
"<span class=\"fa fa-chevron-down fa-border float-right\" role=\"img\" aria-"
"label=\"Details\" title=\"Details\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Previous\"/>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x\" role=\"img\" aria-"
"label=\"Previous\" title=\"Zurück\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Continue Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                        <span class=\"\">Weiter Einkaufen</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Continue<span class=\"d-none d-md-inline\"> Shopping</span>"
msgstr ""
"<span class=\"fa fa-chevron-left\"/>\n"
"                                    Weiter<span class=\"d-none d-md-inline\"> Einkaufen</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"fa fa-chevron-left\"/> Previous"
msgstr "<span class=\"fa fa-chevron-left\"/> Vorherige"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x\" role=\"img\" aria-label=\"Next\" "
"title=\"Next\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Abandoned Carts</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Verlassene Einkaufswagen</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Assignment</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Aufgabe</span>\n"
"                            <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Confirmation Email</span>"
msgstr "<span class=\"o_form_label\">Bestätigungs-E-Mail</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Invoicing Policy</span>"
msgstr "<span class=\"o_form_label\">Fakturierungsregel</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Geben Sie uns Feedback</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Ein Dokument hochladen</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">Ihre Referenz</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Based on variants</span>"
msgstr ""
"<span class=\"text-muted\" attrs=\"{'invisible': [('product_variant_count', "
"'&lt;=', 1)]}\">Basierend auf Varianten</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid ""
"<span>Confirm</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>Bestätigen</span>\n"
"                                    <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Confirmed</span>"
msgstr "<span>Bestätigt</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<span>Next</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"
msgstr ""
"<span>Nächste</span>\n"
"                                            <i class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>Bestellung</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "<span>Process Checkout</span>"
msgstr "<span>Zur Kasse</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>Leider kann Ihre Bestellung nicht bestätigt werden, da der Betrag Ihrer Zahlung nicht mit dem Betrag in Ihrem Warenkorb übereinstimmt.\n"
"                        Für weitere Informationen wenden Sie sich bitte an den Verantwortlichen des Shops.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>Video-Vorschau</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<span>Your payment has been authorized.</span>"
msgstr "<span>Ihre Zahlung wurde autorisiert.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "<strong class=\"align-top\">URL: </strong>"
msgstr "<strong class=\"align-top\">URL: </strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Categories</strong>"
msgstr ""
"<strong class=\"o_categories_collapse_title text-"
"uppercase\">Kategorien</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "<strong>Add to Cart</strong>"
msgstr "<strong>In den Warenkorb</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                    If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Es konnte keine geeignete Zahlungsmethode gefunden werden.</strong><br/>\n"
"                                Wenn Sie glauben, dass es sich um einen Fehler handelt, wenden Sie sich bitte an den Administrator der Website."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Payment Information:</strong>"
msgstr "<strong>Zahlungsinformationen:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total:</strong>"
msgstr "<strong>Gesamt:</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your Cart</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- KOPFZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Ihr Warenkorb</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">S00060</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- INHALT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">ES BEFINDET SICH ETWAS IN IHREM WARENKORB.</h1>\n"
"                    Möchten Sie Ihre Einkäufe abschließen?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Schreibtisch-Kombination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Schreibtisch-Kombination, schwarz-braun: Stuhl + Schreibtisch + Schublade.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Einheiten</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Bestellung fortsetzen\n"
"                        </a>\n"
"                    </div>\n"
"                    <div style=\"text-align: center;\"><strong>Vielen Dank für Ihren Einkauf bei <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FUßZEILE -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\" t-out=\"company.name or ''\">My Company (San Francisco)</td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"company.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"company.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__terms_url
msgid "A preview will be available at this URL."
msgstr "Eine Vorschau wird über diese URL verfügbar sein."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"Ein Produkt kann entweder ein physisches Produkt oder eine Dienstleistung "
"sein, die Sie an Ihre Kunden verkaufen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "A short description that will also appear on documents."
msgstr "Eine kurze Beschreibung, die auch auf Dokumenten erscheint."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "AT A GLANCE"
msgstr "AUF EINEN BLICK"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "Verlassen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "Verlassener Warenkorb"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/models/crm_team.py:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
#, python-format
msgid "Abandoned Carts"
msgstr "Verlassene Warenkörbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "Verlassene Warenkörbe zum Wiederherstellen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "Frist für verlassene Warenkörbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Abandoned carts are all carts left unconfirmed by website visitors. You can "
"find them in *Website > Orders > Abandoned Carts*. From there you can send "
"recovery emails to visitors who entered their contact details."
msgstr ""
"Verlassene Einkaufswagen sind alle Einkaufswagen, die von den Besuchern der "
"Website nicht bestätigt wurden. Sie finden sie auf der *Website > "
"Bestellungen > Verlassene Einkaufswagen*. Von dort aus können Sie Recovery-"
"E-Mails an Besucher senden, die ihre Kontaktdaten hinterlassen haben."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "Über Cross-Selling-Produkte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "Zulässige Dateigröße"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "Zubehör für Produkt"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"Zubehör wird angezeigt, wenn der Kunde den Warenkorb vor der Bezahlung "
"überprüft (Cross-Selling-Strategie)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "Zubehörprodukte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Kontonummer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add a Media"
msgstr "Medien hinzufügen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "Hinzufügen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add price per base unit of measure on your Products"
msgstr "Fügen Sie Ihren Produkten den Preis pro Basismengeneinheit hinzu"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
msgid "Add to Cart"
msgstr "In den Warenkorb"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
msgid "Add to Cart <i class=\"fa fa-fw fa-shopping-cart\"/>"
msgstr "Zu Einkaufswagen hinzufügen <i class=\"fa fa-fw fa-shopping-cart\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Address"
msgstr "Adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "Alle Produkte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "Alle Preislisten"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "All results"
msgstr "Alle Ergebnisse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr ""
"Ermöglichen Sie es Kunden, Produkte anhand von Attributen zu vergleichen."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "Dem Endnutzer erlauben, diese Preisliste auszuwählen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
msgid "Alternative Products"
msgstr "Alternative Produkte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.recommended_products
msgid "Alternative Products:"
msgstr "Alternative Produkte:"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "Anzahl verlassener Warenkörbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "Anwenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Manuelle Rabatte auf Angebotslinien oder durch Preislisten kalkulierte "
"Rabatte (Option die auf Preislisten aktiviert werden kann)."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Apply specific prices per country, discounts, etc."
msgstr "Wenden Sie spezifische Preise für Länder, Rabatte usw. an."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "Zuweisung von Online-Bestellungen"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Attribute"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Average Order"
msgstr "Durchschnittlicher Auftrag"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Bankname"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "Anzahl Grundeinheiten"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "Name der Grundeinheit"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "Grundpreis pro Einheit"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
#: model:ir.ui.menu,name:website_sale.website_base_unit_menu
msgid "Base Units"
msgstr "Grundeinheiten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "Achtung!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "BeNeLux"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "Benelux"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Best Sellers"
msgstr "Bestseller"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "Abrechnung"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Billing Address"
msgstr "Rechnungsadresse"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Bin"
msgstr "Papierkorb"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "Eimer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""
"Steigern Sie Verkaufszahlen mit zwei Rabattprogrammen: Promo- und Coupon-"
"Codes. Spezifische Bedingungen können festgelegt werden (Produkte, Kunden, "
"Mindestbestellmenge, Zeitraum). Prämien können Rabatte sein (Prozent oder "
"Betrag) oder kostenlose Produkte."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "Unten"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Box"
msgstr "Box"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "Boxen"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "Marke"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "Schränke"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Campaigns"
msgstr "Kampagnen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "Kann Bild 1024 gezoomt werden"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "Kann veröffentlichen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Can be used as payment toward future orders."
msgstr "Kann als Zahlungsmittel für zukünftige Bestellungen verwendet werden."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Capture order payments when the delivery is completed."
msgstr "Erfassen Sie Zahlungen von Aufträgen bei Abschluss der Lieferung."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "Anzahl der Artikel im Warenkorb"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "E-Mail zur Warenkorbwiederherstellung"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Cart is abandoned after"
msgstr "Einkaufswagen wird aufgegeben nach "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "E-Mail zur Warenkorbwiederherstellung bereits gesendet."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Carts"
msgstr "Warenkörbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "Warenkörbe werden nach dieser Frist als verlassen markiert."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Categories"
msgstr "Kategorien"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"            touchscreen interface."
msgstr ""
"Kategorien werden verwendet, um Ihre Produkte mittels\n"
"Touchscreen-Benutzerschnittstelle zu durchsuchen."

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Categories:"
msgstr "Kategorien:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "Kategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "Kategoriebeschreibung"

#. module: website_sale
#: code:addons/website_sale/models/product_template.py:0
#, python-format
msgid "Category:"
msgstr "Kategorie:"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Chair"
msgstr "Stuhl"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "Stühle"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Das Ändern der MwSt.-Nummer ist nicht zulässig, sobald Dokumente für Ihr "
"Konto ausgestellt wurden. Bitte kontaktieren Sie uns direkt für diesen "
"Vorgang."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"Eine Änderung des Namens des Unternehmens ist nicht mehr möglich, sobald "
"Dokumente für Ihr Konto ausgestellt wurden. Bitte wenden Sie sich für diesen"
" Vorgang direkt an uns."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once documents have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Eine Änderung Ihres Namen ist nicht mehr möglich, sobald Dokumente für Ihr "
"Konto ausgestellt wurden. Bitte wenden Sie sich für diesen Vorgang direkt an"
" uns."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "Unterkategorien"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"Der Name darf nicht geändert werden, sobald eine Rechnung für Sie erzeugt "
"wurde. Bitte wenden Sie sich direkt an uns."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_open_website_sale_onboarding_payment_acquirer_wizard
msgid "Choose a payment method"
msgstr "Wählen Sie eine Zahlungsmethode"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "Weihnachten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "Stadt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"Klicken Sie auf die Schaltfläche „<i>Neu</i>“ rechts oben, um Ihr erstes "
"Produkt zu erstellen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "Hier klicken"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on <em>Continue</em> to create the product."
msgstr "Klicken Sie auf <em> Weiter</em>, um das Produkt zu erstellen."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Click on this button so your customers can see it."
msgstr ""
"Klicken Sie auf diese Schaltfläche, damit es für Ihre Kunden sichtbar ist."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Comma-separated list of parts of product names"
msgstr "Kommagetrennte Liste der Teile von Produktnamen"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
#, python-format
msgid "Company Name"
msgstr "Unternehmensname"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "Komponenten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "Versandkosten für Easypost berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Versandkosten für DHL berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Versandkosten für FedEx berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Versandkosten für UPS berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Versandkosten für USPS berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Versandkosten für bpost berechnen und versenden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Versandkosten für Aufträge berechnen"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Confirm Order"
msgstr "Bestellung bestätigen"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Confirm orders when you get paid."
msgstr "Bestätigen Sie Aufträge bei Zahlung."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "Bestätigt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "Bestätigte Aufträge"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Conversion"
msgstr "Konversion"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "Sofas"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_country
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "Land"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "Neues Produkt erstellen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "Erstellungsdatum"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "Aktuelle Kategorie oder alle"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "Benutzerdefinierte Maßeinheit"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "Kunde"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "Kundenland"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "Kundenrezensionen"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
msgid "Customers"
msgstr "Kunden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "DHL-Express-Konnektor"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr "BAUSTEINE HIER PLATZIEREN, DAMIT SIE FÜR ALLE PRODUKTE VERFÜGBAR SIND"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "Standardwährung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist"
msgstr "Standardpreisliste"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"Definieren Sie eine benutzerdefinierte Einheit, die im Feld „Preis pro "
"Maßeinheit“ angezeigt wird."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "Eine neue Kategorie definieren"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete"
msgstr "Löschen"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__internal
msgid ""
"Delivery methods are only used internally: the customer doesn't pay for "
"shipping costs"
msgstr ""
"Liefermethoden werden nur intern verwendet: Kunden zahlen keine "
"Versandkosten."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__website
msgid ""
"Delivery methods are selectable on the website: the customer pays for "
"shipping costs"
msgstr ""
"Liefermethoden können auf der Website gewählt werden: Kunden zahlen für "
"Versandkosten."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "Beschreibung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "Beschreibung für die Website"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "Schreibtische"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "Legt die Reihenfolge auf der E-Commerce-Website fest"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "Übersicht"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "Digitaler Inhalt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display a prompt with optional products when adding to cart"
msgstr "Anzeige optionaler Produkte beim Hinzufügen zum Warenkorb"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"Anzeige des Grundpreises pro Einheit auf Ihren E-Commerce-Seiten. Setzen Sie"
" ihn auf 0, um ihn für dieses Produkt auszublenden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "Wird im unteren Bereich der Produktseiten angezeigt"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"Zeigt die benutzerdefinierte Einheit für die Produkte an, falls definiert, "
"oder andernfalls die ausgewählte Maßeinheit."

#. module: website_sale
#: code:addons/website_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Kein Zugriff, überspringen Sie diese Daten für die E-Mail des Benutzer"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__done
msgid "Done"
msgstr "Erledigt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Double click here to set an image describing your product."
msgstr ""
"Doppelklicken Sie hier, um ein Bild zu erstellen, das Ihr Produkt "
"beschreibt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Drag building blocks here to customize the header for \""
msgstr "Ziehen Sie die Bausteine hierher, um die Kopfzeile anzupassen für „"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Drag this website block and drop it in your page."
msgstr "Verschieben Sie diesen Website-Block in Ihre Seite."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Drawer"
msgstr "Schublade"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "Schubladen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "Zusätzliche Felder für E-Commerce"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "Zusätzliche Infos für E-Commerce auf der Produktseite angezeigt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "E-Commerce-Gutscheincode"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "EUR"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Edit"
msgstr "Bearbeiten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form_web_terms
msgid "Edit in Website Builder"
msgstr "Im Website-Builder bearbeiten"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Edit the price of this product by clicking on the amount."
msgstr ""
"Bearbeiten Sie den Preis des Produktes, indem Sie auf den Betrag klicken."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "Diese Adresse bearbeiten"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_email_account
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Email"
msgstr "E-Mail"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email Template"
msgstr "E-Mail-Vorlage"

#. module: website_sale
#: model:ir.model,name:website_sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr "E-Mail-Assistent"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Email sent to the customer after the checkout"
msgstr "Nach Zahlung an Kunden gesendete E-Mail"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "Code einbetten"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Enter a name for your new product"
msgstr "Geben Sie einen Namen für Ihr neues Produkt ein"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Fehler! Rekursive Kategorien sind nicht zulässig"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info_option
msgid "Extra Info"
msgstr "Weitere Informationen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Extra Product Media"
msgstr "Zusätzliche Produktmedien"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "Zusätzliche Variantenbilder"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "Zusätzliche Variantenmedien"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Featured"
msgstr "Beliebteste"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "Feld"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "Feldbezeichnung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "Feldname"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "Von Website"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "Möbel"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Generate an invoice from orders ready for invoicing."
msgstr "Generieren Sie eine Rechnung aus fakturierbaren Aufträgen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""
"Generieren Sie die Rechnung automatisch, wenn die Online-Zahlung bestätigt "
"wird"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_gift_card
msgid "Gift Card"
msgstr "Geschenkkarte"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_public_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Gibt die Reihenfolge beim Anzeigen der Liste der Produktkategorien an"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to cart"
msgstr "Zum Warenkorb gehen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Gewährung von Rabatten auf Verkaufsauftragszeilen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "Grid"
msgstr "Raster"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "Gruppieren nach"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
msgid "Hidden"
msgstr "Verborgen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "Sehr große Datei. Das Bild sollte optimiert/verkleinert werden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "I agree to the"
msgstr "Ich akzeptiere die "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.reduction_code
msgid "I have a promo code"
msgstr "Ich habe einen Gutscheincode"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__id
msgid "ID"
msgstr "ID"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "Bild"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "Bild 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "Bild 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "Bild 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "Bildname"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"Wenn Sie für eine externe Person bestellen, geben Sie Ihre Bestellung bitte "
"über das Backend auf. Wenn Sie Ihren Namen oder Ihre E-Mail-Adresse ändern "
"möchten, tun Sie dies bitte in den Kontoeinstellungen oder kontaktieren Sie "
"Ihren Administrator."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr ""
"Ungültige E-Mail-Adresse! Bitte geben Sie eine gültige E-mail E-Mail-"
"Adresse."

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_invoices_ecommerce
msgid "Invoices"
msgstr "Rechnungen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Rechnungsstellung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "Ist veröffentlicht"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "Rechnungen an Kunden senden"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr ""
"Es ist verboten, einen Verkaufsauftrag zu ändern, der sich nicht im Status "
"Entwurf befindet."

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__just_done
msgid "Just done"
msgstr "Gerade erledigt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "KPI Website Verkauf Gesamtwert"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lamp"
msgstr "Lampe"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "Lampen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_public_category____last_update
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field____last_update
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "Letzter Monat"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "Letzter Online-Verkaufsauftrag"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "Letzte Woche"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "Letztes Jahr"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "Links"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let returning shoppers save products in a wishlist"
msgstr ""
"Lassen Sie wiederkehrende Kunden Produkte in einer Wunschliste speichern."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a shipping address"
msgstr "Lassen Sie Kunden eine Versandadresse eingeben."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Let's create your first product."
msgstr "Los geht’s: Erstellen Sie Ihr erstes Produkt."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Let's now take a look at your administration dashboard to get your eCommerce"
" website ready in no time."
msgstr ""
"Schauen wir uns jetzt Ihr Admin-Dashboard an, damit Ihre E-Commerce-Website "
"im Handumdrehen bereitsteht."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Lightbulb sold separately"
msgstr "Glühbirne separat zu kaufen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__linked_line_id
msgid "Linked Order Line"
msgstr "Verknüpfte Auftragsposition"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
msgid "List"
msgstr "Liste"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Log In"
msgstr "Anmelden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Werbe- und Gutscheinkampagnen verwalten"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Medium"
msgstr "Medium"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "Verkäufer-Konto-ID"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Methode"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "Multimedia"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
#, python-format
msgid "My Cart"
msgstr "Mein Warenkorb"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Name"
msgstr "Name"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "Kurzname"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/controllers/main.py:0
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#, python-format
msgid "New Product"
msgstr "Neues Produkt"

#. module: website_sale
#: model:product.ribbon,html:website_sale.new_ribbon
msgid "New!"
msgstr "Neu!"

#. module: website_sale
#: model:ir.filters,name:website_sale.dynamic_snippet_newest_products_filter
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "Neueste Produkte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Newest arrivals"
msgstr "Neu eingetroffen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Next <span class=\"fa fa-chevron-right\"/>"
msgstr "Nächste <span class=\"fa fa-chevron-right\"/>"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "Keine verlassenen Warenkörbe gefunden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "Kein Produkt definiert"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in category \""
msgstr "Kein Produkt definiert in der Kategorie „"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "Noch keine Produktansichten für diesen Besucher"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "No redirect when the user adds a product to cart."
msgstr ""
"Kein Weiterleiten, wenn der Benutzer ein Produkt in den Warenkorb legt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "Keine Treffer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "Keine Treffer für „"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "Kein Treffer gefunden für „"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_utils.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "Kein Treffer, versuchen Sie es mit einem anderen Stichwort."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__sale_delivery_settings__none
msgid "No shipping management on website"
msgstr "Kein Versandmanagement auf der Website."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "Keine"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_company__website_sale_onboarding_payment_acquirer_state__not_done
msgid "Not done"
msgstr "Nicht erledigt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "Anzahl verlassener Warenkörbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of Columns"
msgstr "Spaltenanzahl"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "Anzahl der Gitterspalten im Shop"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_res_config_settings__cart_abandoned_delay
msgid "Number of hours after which the cart is considered abandoned."
msgstr "Anzahl Stunden, nach denen ein Einkaufskorb als verlassen gilt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Number of products"
msgstr "Anzahl der Produkte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "Anzahl der Produkte im Raster des Shops"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "On wheels"
msgstr "Auf Rädern"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr ""
"Sobald Sie auf <b>Speichern</b> klicken, wird Ihr Produkt aktualisiert."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "Online-Verkäufe"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "Online-Verkaufsanalyse"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "Nur Dienstleistungen"

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"Es sind nur die Websites des Unternehmens zugelassen.\n"
"Lassen Sie das Feld Unternehmen leer oder wählen Sie eine Website dieses Unternehmens."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "Open-Source-E-Commerce"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Open your website app here."
msgstr "Öffnen Sie Ihre Website-App hier."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"Optimierung erforderlich! Verringern Sie die Bildgröße oder erhöhen Sie Ihre"
" Komprimierungseinstellungen."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option for: %s"
msgstr "Option für: %s"

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "Option: %s"
msgstr "Option: %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Optional Products"
msgstr "Optionale Produkte"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__option_line_ids
msgid "Options Linked"
msgstr "Verlinkte Optionen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "Oder scannen Sie mich mit Ihrer Banking-App."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "Bestelldatum"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "Auf der Website angezeigte Auftragspositionen"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_order_line
msgid ""
"Order Lines to be displayed on the website. They should not be used for "
"computation purpose."
msgstr ""
"Angebotspositionen zur Anzeige auf der Website. Sie sollten nicht für "
"Berechnung eingesetzt werden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.short_cart_summary
msgid "Order Total"
msgstr "Auftrag insgesamt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
#, python-format
msgid "Orders"
msgstr "Aufträge"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Orders Followup"
msgstr "Aufträge Follow-up"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "Abzurechnende Aufträge"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders to Invoice"
msgstr "Abzurechnende Aufträge"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Orders/Day"
msgstr "Aufträge/Tag"

#. module: website_sale
#: model:product.ribbon,html:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "Nicht vorrätig"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT-Identitäts-Token"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_page
msgid "Page"
msgstr "Seite"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "Oberkategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "Übergeordneter Pfad"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "Übergeordnete und Selbst"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay Now"
msgstr "Jetzt bezahlen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Pay with"
msgstr "Bezahlen mit"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Zahlungsanbieter"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_acquirers
msgid "Payment Acquirers"
msgstr "Zahlungsdienstleister"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_icons
msgid "Payment Icons"
msgstr "Zahlungssymbole"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Zahlungsanweisungen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Zahlungsmethode"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "Zahlungstoken"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "Zahlungstransaktionen"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Payments to Capture"
msgstr "Zu erfassende Zahlungen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Paypal-Benutzertyp"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Pedal-based opening system"
msgstr "Pedalgestütztes Öffnungssystem"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "Telefon"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "Telefonnummer"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "Bitte geben Sie eine gültige Video-URL ein."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "Bitte mit dem aktuellen Warenkorb fortfahren."

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Month"
msgstr "Vorheriger Monat"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Week"
msgstr "Vorherige Woche"

#. module: website_sale
#: code:addons/website_sale/controllers/backend.py:0
#, python-format
msgid "Previous Year"
msgstr "Vorheriges Jahr"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "Preis"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (high to low)"
msgstr "Preis (absteigend)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price (low to high)"
msgstr "Preis (aufsteigend)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - High to Low"
msgstr "Preis - Absteigend"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "Price - Low to High"
msgstr "Preis - Aufsteigend"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "Einzelpreis"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "Preisliste für diese E-Commerce/Website verfügbar"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "Preisliste"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_product_pricelist3
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Preislisten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Positionen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "Drucken"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "Bearbeiten Sie den Auftrag, sobald die Zahlung eingegangen ist."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#, python-format
msgid "Product"
msgstr "Produkt"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "Produktzubehör"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "Produktattribut"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "Produktkategorie"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
msgid "Product Comparison Tool"
msgstr "Produktvergleichstool"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "Produktbild"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "Produktbilder"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.product
#, python-format
msgid "Product Name"
msgstr "Produktname"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "Zusatzfelder der Produktseite"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "Produktpreise"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "Öffentliche Produktkategorien"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "Produktvorlage"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "Attributzeile der Produktvorlage"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "Produktvorlage"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "Produktvariante"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_variants
msgid "Product Variants"
msgstr "Produktvarianten"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "Produktansichten"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "Historie der Produktansichten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product names"
msgstr "Produktnamen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product prices displaying in web catalog"
msgstr "Anzeigte Produktpreise im Webkatalog"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "Produktband"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_settings
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "Produkte"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "Kürzlich verkaufte Produkte mit"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "Kürzlich verkaufte Produkte mit Produkt"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "Produktansichten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Provide customers with product-specific links or downloadable content in the"
" confirmation page of the checkout process if the payment gets through. To "
"do so, attach some files to a product using the new Files button and publish"
" them."
msgstr ""
"Stellen Sie Kunden auf der Bestätigungsseite des Zahlungsvorgangs "
"produktspezifische Links oder Downloads bereit, wenn die Zahlung erfolgt "
"ist. Dazu fügen Sie die Dateien mit der neuen Schaltfläche „Dateien“ zu "
"einem Produkt hinzu und veröffentlichen sie."

#. module: website_sale
#: code:addons/website_sale/models/product_image.py:0
#, python-format
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"Die angegebene Video-URL für „%s“ ist nicht gültig. Bitte geben Sie eine "
"gültige Video-URL ein."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
msgid "Published"
msgstr "Veröffentlicht"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "Nach unten schieben"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "Ganz nach unten schieben"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "Ganz nach oben schieben"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "Nach oben schieben"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "Qty:"
msgstr "Menge:"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
#, python-format
msgid "Quantity"
msgstr "Menge"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "REVENUE BY"
msgstr "EINNAHMEN VON"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Rating"
msgstr "Bewertung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Rating Average"
msgstr "Durchschnittliche Bewertung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Bewertung letztes Feedback"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "Bewertung letztes Bild"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "Bewertung letzter Wert"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "Anzahl Bewertungen"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__rating_last_feedback
msgid "Reason of the rating"
msgstr "Begründung der Bewertung"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "Kürzlich verkaufte Produkte"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "Kürzlich angesehene Produkte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "Wiederherstellungs-E-Mail versendet"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "Zu sendende Wiederherstellungs-E-Mail"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Reinforced for heavy loads"
msgstr "Verstärkt für schwere Ladungen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "Aus dem Warenkorb entfernen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "Eins entfernen"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_reporting
msgid "Reporting"
msgstr "Berichtswesen"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict publishing to this website."
msgstr "Beschränken Sie die Veröffentlichung auf dieser Website."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.wizard_checkout
msgid "Review Order"
msgstr "Bestellung überprüfen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "Band"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Ribbon background color"
msgstr "Band-Hintergrundfarbe"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html_class
msgid "Ribbon class"
msgstr "Bandklasse"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__html
msgid "Ribbon html"
msgstr "Band-HTML"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Ribbon text color"
msgstr "Band-Textfarbe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "Rechts"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO-optimiert"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sale_ribbon
msgid "Sale"
msgstr "Sale"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "Verkaufsanalyse"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#, python-format
msgid "Sales"
msgstr "Verkäufe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "Verkaufsanalyse"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkaufsanalysebericht"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Sales Order: Cart Recovery Email"
msgstr "Verkaufsauftrag: Warenkorb-Wiederherstellungs-E-Mail"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Month"
msgstr "Verkäufe seit letztem Monat"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Week"
msgstr "Verkäufe seit letzter Woche"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sales Since Last Year"
msgstr "Verkäufe seit letztem Jahr"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Verkaufsteam"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "Vertriebsmitarbeiter"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "Beispiel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "Abgebrochene Verkaufsaufträge suchen"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"Zur Umsatzsteigerung wählen Sie <b>Neues Produkt</b>, um es zu erstellen und"
" seine Eigenschaften zu verwalten."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Select this address"
msgstr "Diese Adresse auswählen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "Auswählbar"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell content to download or URL links"
msgstr "Inhalte zum Download oder URL-Links verkaufen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""
"Verkaufen Sie Produktvarianten basierend auf Attributen (Größe, Farbe, etc.)"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_actions_server_sale_cart_recovery_email
msgid "Send a Cart Recovery Email"
msgstr "E-Mail zur Warenkorbwiederherstellung senden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "Wiederherstellungs-E-Mail senden"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Send a recovery email to visitors who haven't completed their order."
msgstr ""
"Senden Sie eine Wiederherstellungs-E-Mail an Besucher, die ihre Bestellung "
"noch nicht abgeschlossen haben."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send a recovery email when a cart is abandoned"
msgstr ""
"Senden Sie eine Wiederherstellungs-E-Mail, wenn ein Einkaufswagen verlassen "
"wird"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "Seo-Name"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Sequence"
msgstr "Sequenz"

#. module: website_sale
#: model:product.public.category,name:website_sale.services
msgid "Services"
msgstr "Dienstleistungen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Ship to the same address\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Your shipping address will be requested later) </i></span>"
msgstr ""
"Versand an die gleiche Adresse\n"
"                                                    <span class=\"ship_to_other text-muted\" style=\"display: none\">&amp;nbsp;(<i>Ihre Lieferadresse wird später abgefragt) </i></span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping"
msgstr "Versand"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shipping Address"
msgstr "Lieferadresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "Versandkosten"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__sale_delivery_settings
msgid "Shipping Management"
msgstr "Versand-Management"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "Shop"
msgstr "Shop"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "Shop - Kassiervorgang"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "Shop - Bestätigt"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Acquirer"
msgstr "Shop – Zahlungsanbieter auswählen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "Warenkorb"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty Cart"
msgstr "Leeren Warenkorb anzeigen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories
msgid "Show categories"
msgstr "Kategorien anzeigen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Show options"
msgstr "Optionen anzeigen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "Registrieren"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Sit comfortably"
msgstr "Lehnen Sie sich zurück"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "Größe"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "X-Größe"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "Y-Größe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Slanted"
msgstr "Geneigt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sold"
msgstr "Verkauft"

#. module: website_sale
#: model:product.ribbon,html:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "Vergriffen"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "Einige erforderliche Felder sind leer."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Sources"
msgstr "Quellen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "Bundesland"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_company__website_sale_onboarding_payment_acquirer_state
msgid "State of the website sale onboarding payment acquirer step"
msgstr ""
"Status des Einführungsschritts für neue Zahlungsanbieter für Website-"
"Verkäufe"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "Status"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_add_on_page
#: model:ir.model.fields,field_description:website_sale.field_website__cart_add_on_page
msgid "Stay on page after adding to cart"
msgstr "Nach dem Hinzufügen zum Warenkorb auf der Seite bleiben"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street 2"
msgstr "Straße 2"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street <span class=\"d-none d-md-inline\"> and Number</span>"
msgstr "Straße <span class=\"d-none d-md-inline\"> und Hausnummer</span>"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe Veröffentlichbarer Schlüssel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe Geheimer Schlüssel"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal:"
msgstr "Zwischensumme:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"Schlagen Sie Ihrem Kunden Alternativen vor (Upselling-Strategie). Diese "
"werden auf der Produktseite angezeigt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested Accessories:"
msgstr "Vorgeschlagenes Zubehör:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "Vorgeschlagenes Zubehör im Warenkorb des E-Commerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_b2b
msgid "VAT"
msgstr "MwSt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tag"
msgstr "Stichwort"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes:"
msgstr "Steuern:"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website__all_pricelist_ids
msgid "Technical: Used to recompute pricelist_ids"
msgstr "Technisch: Für die Neuberechnung von pricelist_ids"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "Terms and Conditions"
msgstr "Bedingungen und Konditionen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "Vielen Dank für Ihre Bestellung!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "Die #1"

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Das Zugriffstoken ist ungültig."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The cart has been updated. Please refresh the page."
msgstr "Der Warenkorb wurde aktualisiert. Bitte aktualisieren Sie die Seite."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"Die vollständige URL, um über die Website auf das Dokument zuzugreifen."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr ""
"Die angegebene Kombination existiert nicht und kann daher nicht in den "
"Warenkorb gelegt werden."

#. module: website_sale
#: code:addons/website_sale/models/sale_order.py:0
#, python-format
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr ""
"Das angegebene Produkt existiert nicht, daher kann es nicht in den Warenkorb"
" gelegt werden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"Der hier ausgewählte Modus gilt als Abrechnungspolitik für alle neu "
"erstellten Produkte, jedoch nicht für bestehende Produkte."

#. module: website_sale
#: code:addons/website_sale/controllers/main.py:0
#, python-format
msgid "The order has been canceled."
msgstr "Die Bestellung wurde storniert."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Customize and enable 'eCommerce categories' to view all eCommerce "
"categories."
msgstr ""
"Das Produkt wird in jeder genannten eCommerce-Kategorie verfügbar sein. "
"Gehen Sie zu Shop > Anpassen und aktivieren Sie \"eCommerce-Kategorien\", um"
" alle eCommerce-Kategorien anzuzeigen."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"Die Zeit, um einen Warenkorb als verlassen zu markieren, kann in den "
"Einstellungen geändert werden."

#. module: website_sale
#: code:addons/website_sale/models/product_product.py:0
#, python-format
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"Der Wert der Basisstückzahl muss größer als 0 sein. Verwenden Sie 0, um den "
"Einzelpreis für dieses Produkt auszublenden."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "Es gibt keinen bestätigten Auftrag von der Website"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There is no recent confirmed order."
msgstr "Es gibt keinen kürzlich bestätigten Auftrag."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "Es liegt noch keine unbezahlte Bestellung auf der Website vor"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "There isn't any UTM tag detected in orders"
msgstr "In Bestellungen wurde kein UTM-Stichwort gefunden"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "Diese Kombination existiert nicht."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"This email template is suggested by default when you send a recovery email."
msgstr ""
"Diese E-Mail-Vorlage wird standardmäßig vorgeschlagen, wenn Sie eine "
"Wiederherstellungs-E-Mail senden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "Dies ist Ihr aktueller Warenkorb."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "Dieses Produkt hat keine gültige Kombination."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "Dieses Produkt ist nicht länger verfügbar."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "Dieses Produkt ist unveröffentlicht."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "Dieser Aktionscode ist nicht verfügbar."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails Position"
msgstr "Thumbnails Position"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "Gesamtzahl der angesehenen Produkte"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "Gesamtzahl der Produktansichten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "True"
msgstr "Wahr"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"Wahr nur für Produktfilter, die eine product_id erfordern, weil sie sich auf"
" Cross-Selling beziehen"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "Maßeinheit-Preis-Anzeige für E-Commerce"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "UPS"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__terms_url
msgid "URL"
msgstr "URL"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "URL eines Videos, in dem Ihr Produkt vorgestellt wird."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "USPS"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "Maßeinheit für den Einzelpreis bei E-Commerce-Produkten."

#. module: website_sale
#: model:product.product,uom_name:website_sale.product_product_1
#: model:product.product,uom_name:website_sale.product_product_1b
#: model:product.template,uom_name:website_sale.product_product_1_product_template
msgid "Units"
msgstr "Einheiten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "Unbezahlt"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
#, python-format
msgid "Unpaid Orders"
msgstr "Unbezahlte Aufträge"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "Unveröffentlicht"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/xml/website_sale_dashboard.xml:0
#, python-format
msgid "Untaxed Total Sold"
msgstr "Gesamte Nettoverkäufe"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
#, python-format
msgid "Upload a file from your local library."
msgstr "Laden Sie eine Datei aus Ihrer Bibliothek hoch."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Validate"
msgstr "Bestätigen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "Video-URL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "View Cart ("
msgstr "Warenkorb ansehen ("

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "Produkt ansehen"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "Sichtbarkeit"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "Sichtbar"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "Auf der aktuellen Website sichtbar"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "Besuchte Seiten"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "Angesehene Produkte"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "Produktansichten von Besuchern"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "Historie der Produktansichten von Besuchern"

#. module: website_sale
#: model:product.product,name:website_sale.product_product_1
#: model:product.product,name:website_sale.product_product_1b
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "Garantie"

#. module: website_sale
#: model:product.product,description_sale:website_sale.product_product_1
#: model:product.product,description_sale:website_sale.product_product_1b
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"Garantie, die der Hersteller dem Käufer eines Artikels gewährt, indem er "
"sich verpflichtet, den Artikel innerhalb eines bestimmten Zeitraums zu "
"reparieren oder zu ersetzen, falls erforderlich."

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_validate.js:0
#, python-format
msgid "We are waiting for confirmation from the bank or the payment provider"
msgstr ""
"Wir warten auf die Bestätigung von der Bank oder dem Zahlungsanbieter."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_payment__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Website"
msgstr "Website"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_payment_acquirer_onboarding_wizard
msgid "Website Payment acquire onboarding wizard"
msgstr "Website Zahlungsanbieter Onboarding-Assistent"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "Website-Produktkategorien"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "Website Öffentliche Kategorien"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "Website-Sequenz"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "Website-Shop"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Website-Snippet-Filter"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "Website-URL"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "Website-Besucher"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "Website-Meta-Beschreibung"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website-Meta-Schlagwörter"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "Website-Meta-Titel"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "Opengraph-Bild der Website"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
#: model:ir.model.fields,help:website_sale.field_account_payment__website_id
msgid "Website through which this invoice was created."
msgstr "Website, über die diese Rechnung erstellt wurde."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed."
msgstr "Website, über die diese Bestellung aufgegeben wurde."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "Websites"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_crm_team__website_ids
msgid "Websites using this Sales Team"
msgstr "Websites, die dieses Verkaufsteam verwenden"

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "Whiteboard"
msgstr "Whiteboard"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "Wunschlisten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"With the first mode you can set several prices in the product config form "
"(from Sales tab). With the second one, you set prices and computation rules "
"from Pricelists."
msgstr ""
"Mit dem ersten Modus können Sie im Produktkonfigurationsformular (im "
"Verkaufsreiter) verschiedene Preise festlegen. Mit dem zweiten legen Sie "
"Preise und Berechnungsregeln von Preislisten fest."

#. module: website_sale
#: code:addons/website_sale/models/product_misc.py:0
#, python-format
msgid "With this action, '%s' website would not have any pricelist available."
msgstr ""
"Mit dieser Aktion, hätte die '%s' Website keine Preisliste zur Verfügung."

#. module: website_sale
#: code:addons/website_sale/models/website_snippet_filter.py:0
#, python-format
msgid "With three feet"
msgstr "Mit drei Füßen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>billing and shipping</b> addresses at the same time!<br/>\n"
"                                            If you want to modify your shipping address, create a"
msgstr ""
"Sie bearbeiten Ihre <b>Rechnungs- und Lieferadressen</b> zur gleichen Zeit!<br/>\n"
"                                          Wenn Sie Ihre Lieferadresse ändern möchten, erstellen Sie eine"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "Sie haben keine Bestellung von der Website"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "Sie haben keine Bestellung zur Rechnungsstellung von der Website"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "Sie haben Artikel in Ihrem Warenkorb!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"Hier finden Sie alle Warenkörbe, die Ihre Besucher verlassen haben.\n"
"               Wenn sie ihre Adresse vervollständigt haben, sollten Sie ihnen eine Wiederherstellungs-E-Mail senden!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Your Address"
msgstr "Ihre Adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Your Address\n"
"                                        <small> or </small>"
msgstr ""
"Ihre Adresse\n"
"                                        <small> oder </small>"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Email"
msgstr "Ihre E-Mail"

#. module: website_sale
#. openerp-web
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#, python-format
msgid "Your Name"
msgstr "Ihr Name"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
#: model_terms:ir.ui.view,arch_db:website_sale.cart_summary
msgid "Your cart is empty!"
msgstr "Ihr Warenkorb ist noch leer!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "Ihr vorheriger Warenkorb wurde bereits abgeschlossen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "PLZ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "code..."
msgstr "Code ..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "z. B. Lampe, Abfalleimer"

#. module: website_sale
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
#, python-format
msgid "eCommerce"
msgstr "E-Commerce"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
msgid "eCommerce Categories"
msgstr "E-Commerce-Kategorien"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter Visibility"
msgstr "Filter-Sichtbarkeit im E-Commerce"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "E-Commerce-Verkäufe"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_delivery
msgid "eCommerce Shipping Costs"
msgstr "eCommerce Versandkosten"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Shop"
msgstr "E-Commerce-Shop"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.search_count_box
msgid "found)"
msgstr "Gefunden)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "hours."
msgstr "Stunden."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr ""
"wenn Sie Ihren vorherigen Warenkorb mit dem aktuellen zusammenführen "
"möchten."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"wenn Sie Ihren vorherigen Warenkorb wiederherstellen möchten. Ihr aktueller "
"Warenkorb wird durch Ihren vorherigen Warenkorb ersetzt."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "in der Kategorie „"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_popover
msgid "item(s))"
msgstr "Artikel)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "neue Adresse"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_sale_note
msgid "terms &amp; conditions"
msgstr "Geschäftsbedingungen"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr ", um Ihre Bestellung zu verfolgen."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Background"
msgstr "Hintergrundbild"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Mode"
msgstr "⌙ Modus"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Position"
msgstr "â Position"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "⌙ Text"
msgstr "⌙ Text"
