# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <ondrej.j<PERSON><PERSON>@alarex.cz>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2022
# Jak<PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Aleš Fiala <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""
"%(user)s potvrzuje, že tento výdaj není duplikátem s podobným výdajem."

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""
"'Výdaje - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr "1 %(exp_cur)s = %(rate)s %(comp_cur)s"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use this reference as a subject prefix "
"when submitting by email.</i>"
msgstr ""
"<i class=\"text-muted oe_edit_only\">Pri odosielaní e-mailom použite túto "
"referenciu ako predponu predmetu.</i>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Approve the report here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""
"<p>Schválte správu tu.</p><p>Tip: ak odmietnete, nezabudnite uviesť dôvod "
"vďaka nižšie uvedenému nástroju na odosielanie správ</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Click on <b> Action Create Report </b> to submit selected expenses to "
"your manager</p>"
msgstr ""
"<p>Kliknutím na <b> Zpráva o vytvoření události </b> předložíte vybrané "
"výdaje vašemu manažerovi</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Click on <b> Create Report </b> to create the report.</p>"
msgstr "<p>Kliknutím na <b> Vytvořit zprávu </b> vytvoříte zprávu.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Once your <b> Expense </b> is ready, you can save it.</p>"
msgstr "<p>Když je váš <b> výdaj </b> připraven, můžete si ho uložit.</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr "<p>Vyberte výdavky, ktoré chcete odoslať svojmu manažérovi</p>"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""
"<p>Účetní dostáva schválené výkazy výdajů.</p><p>Může správně zaúčtovat "
"denníkové zápisy, pokud jsou daně a účty správně.</p>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr "<span>@</span>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr "<strong>Datum:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr "<strong>Popis:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr "<strong>Zaměstnanec:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr "<strong>Platba od:</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr "<strong>Celkem</strong>"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr "<strong>Ověřeno od:</strong>"

#. module: hr_expense
#: model:product.product,name:hr_expense.accomodation_expense_product
#: model:product.template,name:hr_expense.accomodation_expense_product_product_template
msgid "Accomodation"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr "Účet"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr "Účetnictví"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr "Datum účtování"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorace výjimky aktivity"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr "Typy aktivit"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr "Zástupce"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr "Všichni schvalovatelé"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr "Všechny výkazy výdajů"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
msgid "All My Expenses"
msgstr "Všetky moje výdavky"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all
msgid "All Reports"
msgstr "Všetky správy"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_residual
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr "Částka splatná"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/upload_mixin.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr "Počas nahrávania sa vyskytla chyba"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr "Očekává se výdajový účet"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr "Výdaj se stejnou kategorií, částkou a datumem již existuje."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr "Výkaz výdavkov musí obsahovať iba riadky od tej istej spoločnosti."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Analytic Account"
msgstr "Analytický účet"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Střediska"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Apple App Store"
msgstr "Apple App Store"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr "Datum schválení"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr "Schválit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Approve Report"
msgstr "Schváliť správu"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
msgid "Approve the new expense reports submitted by the employees you manage."
msgstr ""
"Schvaľujte nové výkazy výdavkov predložené zamestnancami, ktorých riadite."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr "Scháleno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr "Schváleno od"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Approved Expenses"
msgstr "Schválené výdavky"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr "Schváleno dne"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr "Archivováno"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr "Priložte potvrdenie"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach your receipt here."
msgstr "Přiložte svůj doklad."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr "Bankovní deník"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr "Referenčné číslo faktury"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr "Může Schválit"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr "Možno resetovat"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr "Může byť zaúčtovaný"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#, python-format
msgid "Cancel"
msgstr "Zrušit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr "Kategorie"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr "Kategorie:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr "Kategorie: nenalezeno"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr "Certifikovaný čestný a konformný,<br/>(Dátum a podpis).<br/><br/>"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr "Firma"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr "Konfigurace"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Confirmed Expenses"
msgstr "Potvrzené výdaje"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Převod mezi měrnými jednotkami lze uskutečnit jen když patří do stejné "
"kategorie. Převod bude proveden na základě vzájemných poměrů."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create"
msgstr "Vytvořit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr "Vytvořit zprávu"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr "Vytvořit nový přehled výdajů"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr "Vytvářejte výdaje z příchozích e-mailů"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr "Vytvořte nové výdaje a získejte statistiky."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Create record"
msgstr "Vytvořit záznam"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
msgid "Currency"
msgstr "Měna"

#. module: hr_expense
#: model:product.product,name:hr_expense.allowance_expense_product
#: model:product.template,name:hr_expense.allowance_expense_product_product_template
msgid "Daily Allowance"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr "Datum"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.allowance_expense_product
#: model:product.template,uom_name:hr_expense.allowance_expense_product_product_template
msgid "Days"
msgstr "Dny"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr "Vážený"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr "Výchozí název zástupce pro výdaje"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr "Oddělení"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
msgid "Description"
msgstr "Popis"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Did you try the mobile app?"
msgstr "Vyzkoušeli jste mobilní aplikaci?"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr "Digitalizujte své příjmy pomocí OCR a umělé inteligence"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""
"Už si nenechávejte své výdajové lístky v kapsách. Stačí pořídit fotografii "
"dokladu a nechat Odoo digitalizovat za vás. OCR a umělá inteligence vyplní "
"data automaticky."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr "Dokumenty"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr "Hotovo"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "Draft"
msgstr "Návrh"

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr "Duplicitní výdaj"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr "Zaměstnanec"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr "Zaměstnanec (k úhradě)"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr "Náklady na zaměstnance"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr "Adresa bydliště zaměstnance"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr "Výdaje"

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr "Schválení výdajů"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr "Duplikát při schvalování výdaje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr "Schvalovatel výdajů"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr "Datum nákladu"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr "Digitalizace nákladů (OCR)"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr "Nákladový deník"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr "Řádky výdajů"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_editable
msgid "Expense Lines Are Editable By Current User"
msgstr "Položky výdajů lze upravovat aktuálním uživatelem"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr "Správce výdajů"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr "Nákladové produkty"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr "Průvodce důvodem odmítnutí výdajů"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr "Zpráva o výdajích"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__state
msgid "Expense Report State"
msgstr "Stav hlášení výdajů"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr "Souhrnný přehled výdajů"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr "Zprávy o výdajích"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr "Analýza výkazů výdajů"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr "Zprávy o výdajích k zaplacení"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr "Zprávy o výdajích za zveřejnění"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr "Zprávy o výdajích ke schválení"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr "Ověřit duplikát výdajů"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense products can be reinvoiced to your customers."
msgstr "Nákladové produkty mohou být vašim zákazníkům přefakturovány."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr "Důvod odmítnutí výdajů"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr "Zpráva o výdajích schválena"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr "Zpráva o výdajích zaplacena"

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr "Zpráva o výdajích byla odmítnuta"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""
"Výkazy výdajů přeskupují všechny výdaje vzniklé během konkrétní události."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line__expense_id
msgid "Expense where the move line come from"
msgstr "Náklady, odkud tahová čára pochází"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expenses"
msgstr "Výdaje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr "Analýza výdajů"

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr "Zpráva o výdajích"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr "Výkazy výdajů ke schválení"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr "Výdaje podle data"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses must be paid by the same entity (Company or employee)."
msgstr "Výdaje musí platit stejný subjekt (společnost nebo zaměstnanec)."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr "Výdaje člena týmu"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses to Invoice"
msgstr "Náklady na fakturu"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr "Náklady na zpracování"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitly Refused by manager or accountant"
msgstr "Správce nebo účetní výslovně odmítl"

#. module: hr_expense
#: model:product.product,name:hr_expense.trans_expense_product
#: model:product.template,name:hr_expense.trans_expense_product_product_template
msgid "Flights, train, bus, taxi, parking"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skvělá ikona písma, např. fa-úkoly"

#. module: hr_expense
#: model:product.product,name:hr_expense.food_expense_product
#: model:product.template,name:hr_expense.food_expense_product_product_template
msgid "Food & beverages"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr "Bývalí zaměstnanci"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr "Budoucí činnosti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr "Obecná informace"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generated Expenses"
msgstr "Generované výdaje"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Google Play Store"
msgstr "Google Play Store"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr "Zpracovat řádky s různými měnami"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr "HR výdajů"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr "HR výdajový list"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
msgid "ID"
msgstr "ID"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona označuje vyjímečnou aktivitu."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr "V platbě"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Included in price taxes"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr "Příchozí e-maily"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr "Neplatné přílohy!"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__invoicing_legacy
msgid "Invoicing App Legacy"
msgstr "Fakturační dědictví aplikace"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr "Lze upravovat aktuálním uživatelem"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_currency
msgid "Is currency_id different from the company_currency_id"
msgstr "Je currency_id rozdílné od company_currency_id"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Is product with non zero cost selected"
msgstr "Je vybrán produkt s nenulovými náklady"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr "Deník"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Journal Entry"
msgstr "Položka deníku"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr "Položka deníku"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_convert_rate
msgid "Label Convert Rate"
msgstr "Míra konverze štítků"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_total_amount_company
msgid "Label Total Amount Company"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr "Zpožděné činnosti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
msgid "Let your employees record expenses by email"
msgstr "Nechte své zaměstnance zaznamenat výdaje emailem"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr "Manažer"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr "Manažeři mohou z této nabídky získat všechny přehledy ke schválení."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: hr_expense
#: model:product.product,name:hr_expense.mileage_expense_product
#: model:product.template,name:hr_expense.mileage_expense_product_product_template
msgid "Mileage"
msgstr "Ujeté kilometry"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mé aktivity"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr "Moje výdaje"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
msgid "My Expenses to Report"
msgstr "Moje výdaje k hlášení"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr "Moje zprávy"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr "Můj tým"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr "Jméno"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report"
msgstr "Nová zpráva o výdajích"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termín další aktivity"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr "Souhrn další aktivity"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr "Další typ aktivity"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""
"Pro produkt nebyl nalezen žádný účet výdajů %s (nebo pro jeho kategorii), "
"prosím nakonfigurujte jeden."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr ""
"Pro zaměstnance nebyla nalezena žádná domácí adresa %s, nakonfigurujte si "
"prosím jeden."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr "Nebyla poskytnuta žádná příloha"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense products found. Let's create one!"
msgstr "Nebyly nalezeny žádné výdajové produkty. Vytvořme si jeden!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr "Nebyl nalezen žádný výkaz výdajů. Vytvořme si jeden!"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid "No expense reports found"
msgstr "Nebyly nalezeny žádné výkazy výdajů"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr "Nebyly nalezeny žádné výkazy výdajů. Vytvořme si jeden!"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__not_paid
msgid "Not Paid"
msgstr "Nezaplaceno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr "Poznámky ..."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr "Počet příloh"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet zpráv, které vyžadují akci"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet nepřečtených zpráv"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr "Odoo"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""
"Jakmile vytvoříte své výdaje, odešlete je svému nadřízenému, který je ověří."

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense report</b> is ready, you can submit it to your manager "
"and wait for the approval from your manager."
msgstr ""
"Jakmile je váš<b>report výdavku</b> je připraven, můžete jej odeslat svému "
"nadřízenému a počkat na jeho schválení."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr "Pouze pracovníci HR nebo dotyčný zaměstnanec mohou obnovit návrh."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr "Pouze manažeři a HR pracovníci mohou schvalovat výdaje"

#. module: hr_expense
#: model:product.product,name:hr_expense.other_expense_product
#: model:product.template,name:hr_expense.other_expense_product_product_template
msgid "Other expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__paid
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr "Zaplaceno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr "Hradí"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr "Částečný"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__partial
msgid "Partially Paid"
msgstr "Částečně zaplaceno"

#. module: hr_expense
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Payment Cancelled"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr "stav platby"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Please configure Default Expense account for Category expense: "
"`property_account_expense_categ_id`."
msgstr ""
"Nastavte prosím výchozí výdajový účet pro kategorii výdajů: "
"`property_account_expense_categ_id`."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Post Entries"
msgstr "Příspěvky"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr "Zaúčtovat vnitřní účetní doklady"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid ""
"Post the journal entries of the new expense reports approved by the "
"employees' manager."
msgstr "Zaúčtujte vnitřní účetní doklady nových výdajů schválených manažerem."

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr "Schváleno"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr "Běží na"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr "Cena"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr "Cena v měně společnosti"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr "Cena:"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
msgid "Product"
msgstr "Produkt"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr "Název výrobku"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr "Šablona produktu"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr "Varianty výrobku"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr "Veřejný zaměstnanec"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr "Množství"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr "Množství"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr "Důvod"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr "Důvod:"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr "Důvod odmítnutí výdajů"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Receipts"
msgstr "Účtenky"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr "Odk."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_ref_editable
msgid "Reference Is Editable By Current User"
msgstr "Lze upravovat aktuálním uživatelem"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr "Odmítnout"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr "Důvod odmítnutí výdajů"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr "Odmítnuto"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr "Odmítnuté výdaje"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
#, python-format
msgid "Register Payment"
msgstr "Zaznamenat platbu"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr "Hrazené výdaje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr "Hrazené výdaje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr "Hrazené výdaje"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
msgid ""
"Reimburse the employees who incurred these costs or simply register the "
"corresponding payments."
msgstr ""
"Odměňte zaměstnance, kterým tyto náklady vznikly, nebo jednoduše "
"zaregistrujte odpovídající platby."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Report"
msgstr "Sestava"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr "Měna společnosti"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr "Přehledy"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
msgid "Reports to Approve"
msgstr "Zprávy ke schválení"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
msgid "Reports to Pay"
msgstr "Zprávy o výdajích k zaplacení"

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
msgid "Reports to Post"
msgstr "Zprávy o výdajích za zveřejnění"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr "Obnovit návrh"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr "Zodpovědný uživatel"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__reversed
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reversed"
msgstr "Obráceně"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sample
msgid "Sample"
msgstr "Vzorek"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "Save Anyways"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Scan"
msgstr "Skenování"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/expense_qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr "Naskenováním tohoto QR kódu získáte aplikaci Odoo:"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""
"Vyberte uživatele odpovědného za schválení „Výdaje“ tohoto zaměstnance.\n"
"Pokud je prázdné, schválení provede administrátor nebo schvalovatel (určeno v nastavení / uživatelé)."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Selected Unit of Measure for expense %(expense)s does not belong to the same"
" category as the Unit of Measure of product %(product)s."
msgstr ""
"Zvolená měrná jednotka výdaje %(expense)s nepatří do stejné kategorie jako "
"měrná jednotka produktu %(product)s."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""
"Odesláním e-mailu na tento e-mailový alias s účtenkou v příloze vytvoříte "
"výdaj jedním kliknutím. Pokud první slovo předmětu e-mailu obsahuje interní "
"odkaz na kategorii nebo název kategorie, automaticky se nastaví odpovídající"
" kategorie. Zadáním částky výdaje do předmětu e-mailu ji nastavíte i na "
"výdaj."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr "Odesílejte přijaté faktury OCR za účelem generování nákladů"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr "Nastavení"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr "Nastavte alias domény"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
msgid "Sheet"
msgstr "List"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Zobrazit všechny záznamy, které mají následující datum akce před dneškem"

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""
"Pořiďte fotografie svých účtenek a nechte Odoo<br> automaticky za vás "
"vytvoří výdaje."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Specify expense journal to generate accounting entries."
msgstr "Pro generování účetních záznamů zadejte deník výdajů."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr "Zvolte, zda může být výrobek vybrán na řádku potvrzení objednávky."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr "Stav"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stav na základě aktivit\n"
"Vypršeno: Datum již uplynulo\n"
"Dnes: Datum aktivity je dnes\n"
"Plánováno: Budoucí aktivity."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__state
msgid "Status of the expense."
msgstr "Stav výdajů"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr "Předložit manažerovi"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr "Odesláno"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Subtotal"
msgstr "Mezisoučet"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr "Daně"

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr "Schvalovatel týmu"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr "Účetní může zaregistrovat platbu, která zaměstnanci přímo proplatí."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr ""
"Aktuální uživatel nemá žádného souvisejícího zaměstnance. Vytvořte si prosím"
" jednu."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The expense reports were successfully approved."
msgstr "Výkazy výdajů byly úspěšně schváleny."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""
"První slovo předmětu e-mailu neodpovídalo žádnému kódu kategorie. Kategorii "
"budete muset na výdajích nastavit ručně."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""
"Následující schválené výdaje mají podobné zaměstnance, částku a kategorii "
"jako některé výdaje tohoto výkazu. Ověřte prosím, že tento výkaz neobsahuje "
"duplikáty."

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr "Deník musí být nastaven na zaúčtované výdaje"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr "Deník použitý po dokončení výdajů."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr "Způsob platby v případě, když společnost zaplatí výdaj."

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__tax_ids
msgid "The taxes should be \"Included In Price\""
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "There are no expense reports to approve."
msgstr "Nejsou zde žádné výkazy výdajů ke schválení."

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr "Tip: pořizujte snímky účtenek pomocí vzdálené aplikace"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Approve"
msgstr "Ke schválení"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Pay"
msgstr "K platbě"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Post"
msgstr "Zveřejnit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr "Ohlásit"

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
msgid "To Submit"
msgstr "Předložit"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr "Dnešní činnosti"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total"
msgstr "Celkem"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total %s"
msgstr "Celkový %s"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr "Celková částka"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Company Currency"
msgstr "Celková měna společnosti"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
msgid "Total In Currency"
msgstr "Celkem v měně"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ zaznamenané výjimečné aktivity."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr "Jednotková cena"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr "Měrná jednotka"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.accomodation_expense_product
#: model:product.product,uom_name:hr_expense.food_expense_product
#: model:product.product,uom_name:hr_expense.other_expense_product
#: model:product.product,uom_name:hr_expense.product_product_fixed_cost
#: model:product.product,uom_name:hr_expense.trans_expense_product
#: model:product.template,uom_name:hr_expense.accomodation_expense_product_product_template
#: model:product.template,uom_name:hr_expense.food_expense_product_product_template
#: model:product.template,uom_name:hr_expense.other_expense_product_product_template
#: model:product.template,uom_name:hr_expense.product_product_fixed_cost_product_template
#: model:product.template,uom_name:hr_expense.trans_expense_product_product_template
msgid "Units"
msgstr "Jednotky"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo nepřečtených zpráv"

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr "Kategorie MJ"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr "Nahrát"

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "Users"
msgstr "Uživatelé"

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr "Ověření duplicitních výdajů"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr "Zobrazit přílohy"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr "Zobrazit výdaje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr "Zobrazit hlášení"

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Want to manage your expenses? It starts here."
msgstr "Chcete spravovat své výdaje? Začíná to tady."

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr "Zprávy webstránky"

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr "Historie komunikace webstránky"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user using"
" this product won't be able to change the amount of the expense, only the "
"quantity. Use a cost different than 0 for expense categories funded by the "
"company at fixed cost like allowances for mileage, per diem, accomodation or"
" meal."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit the reference of this expense report."
msgstr "Nejste oprávněni upravovat odkaz na tento výkaz výdajů."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr "Nemáte oprávnění upravovat tento výkaz výdajů."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr "Bez kategorie nelze hlášení vytvořit."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr "Nyní jej můžete odeslat správci pomocí následujícího odkazu."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only approve your department expenses"
msgstr "Schválit můžete pouze výdaje vašeho oddělení"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr "Účetní záznam můžete vygenerovat pouze pro schválené výdaje."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only refuse your department expenses"
msgstr "Můžete odmítnout jen náklady vašeho oddělení "

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can't mix sample expenses and regular ones"
msgstr "Nemůžete kombinovat výdaje vzorku a běžné"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr "Nemůžete přidat výdaje jiného zaměstnance."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot approve your own expenses"
msgstr "Nemůžete schválit vlastní výdaje"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr "Vyúčtovaný nebo schválený výdaj nemůžete smazat."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr "Nemůžete odstranit účtované nebo placené náklady."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot refuse your own expenses"
msgstr "Nemůžete odmítnout své vlastní náklady"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different companies in the same report."
msgstr "V téže zprávě nemůžete vykázat výdaje za různé společnosti."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr "V téže zprávě nemůžete vykázat výdaje za různé zaměstnance."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr "Nelze nahlásit dvakrát stejný řádek.."

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""
"Abyste mohli pokračovat, musíte mít ve své databázi alespoň jednu kategorii,"
" kterou lze zařadit do nákladů!"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr "Vaše výdaje"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr "Váš výdaj byl úspěšně zaregistrován."

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr "např. Oběd"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr "např. oběd se zákazníkem"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr "např. Výlet do New Yorku"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr "bylo odmítnuto"

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.mileage_expense_product
#: model:product.template,uom_name:hr_expense.mileage_expense_product_product_template
msgid "km"
msgstr "km"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr "k úhradě"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to report"
msgstr "ohlásit"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr "probíhá validace"

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr "použijte OCR k vyplnění údajů z obrázku účtu"
