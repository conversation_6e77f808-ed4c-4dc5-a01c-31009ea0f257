# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# Wichanon Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr "ช่วยให้เราสามารถกรองแคมเปญที่เกี่ยวข้อง"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approval-based Flow"
msgstr "โฟลว์ตามการอนุมัติ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Approved"
msgstr "อนุมัติแล้ว"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr "กำหนดแท็กให้กับแคมเปญของคุณเพื่อจัดระเบียบ กรอง และติดตาม"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Audience-driven Flow"
msgstr "โฟลว์ที่ขับเคลื่อนโดยผู้ชม"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "แคมเปญที่สร้างขึ้นโดยอัตโนมัติ"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "แคมเปญ"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "ชื่อแคมเปญ"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr "ขั้นตอนแคมเปญ"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "แท็กแคมเปญ"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "แคมเปญ"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr "แคมเปญใช้เพื่อรวมศูนย์การตลาดของคุณและติดตามผลลัพธ์"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr "คริสต์มาสสเปเชียล"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr "รวบรวมไอเดีย ออกแบบเนื้อหาที่สร้างสรรค์ และเผยแพร่เมื่อตรวจสอบแล้ว"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "ดัชนีสี"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Copywriting"
msgstr "การเขียนโฆษณา"

#. module: utm
#: model:utm.source,name:utm.utm_source_craigslist
msgid "Craigslist"
msgstr "Craigslist"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr "สร้างสื่อกลาง"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr "สร้างแท็ก"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr "สร้างแคมเปญ"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr "สร้างขั้นตอนสำหรับแคมเปญของคุณ"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Creative Flow"
msgstr "โฟลว์ความคิดสร้างสรรค์"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "ลบ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deploy"
msgstr "ปรับใช้"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Deployed"
msgstr "ปรับใช้"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
#, python-format
msgid "Design"
msgstr "ออกแบบ"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Dropdown menu"
msgstr "เมนูแบบดรอปดาว์น"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "แก้ไข"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "อีเมลแคมเปญ - สินค้า"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "อีเมลแคมเปญ - บริการ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Event-driven Flow"
msgstr "โฟลว์ที่ขับเคลื่อนด้วยอีเวนต์"

#. module: utm
#: model:utm.source,name:utm.utm_source_facebook
msgid "Facebook"
msgstr "Facebook"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Gather Data"
msgstr "รวบรวมข้อมูล"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""
"รวบรวมข้อมูล สร้างรายชื่อผู้รับ "
"และเขียนเนื้อหาอิงตามเป้าหมายทางการตลาดของคุณ"

#. module: utm
#: model:utm.source,name:utm.utm_source_glassdoor
msgid "Glassdoor"
msgstr "ประตูกระจก"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ไอดี"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Ideas"
msgstr "แนวคิด"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign____last_update
#: model:ir.model.fields,field_description:utm.field_utm_medium____last_update
#: model:ir.model.fields,field_description:utm.field_utm_source____last_update
#: model:ir.model.fields,field_description:utm.field_utm_stage____last_update
#: model:ir.model.fields,field_description:utm.field_utm_tag____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Later"
msgstr "ภายหลัง"

#. module: utm
#: model:utm.source,name:utm.utm_source_mailing
msgid "Lead Recall"
msgstr "การเรียกคืนลูกค้าเป้าหมาย"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "ตัวติดตามลิงก์"

#. module: utm
#: model:utm.source,name:utm.utm_source_linkedin
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "List-Building"
msgstr "ตัวสร้าง-รายการ"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "การตลาด"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "สื่อกลาง"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "ชื่อกลาง"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "สื่อกลาง"

#. module: utm
#: model:utm.source,name:utm.utm_source_monster
msgid "Monster"
msgstr "มอนสเตอร์"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
msgid "Name"
msgstr "ชื่อ"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "ใหม่"

#. module: utm
#: model:utm.source,name:utm.utm_source_newsletter
msgid "Newsletter"
msgstr "ข่าวสาร"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr "ยังไม่มีแหล่งที่มา!"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Pre-Launch"
msgstr "ก่อนการเปิดตัว"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr "เตรียมแคมเปญและได้รับการอนุมัติก่อนที่จะเผยแพร่"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""
"เตรียมแคมเปญของคุณ ทดสอบกับผู้ชมบางส่วนของคุณ "
"และปรับใช้อย่างเต็มที่ในภายหลัง"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Report"
msgstr "รายงาน"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "รับผิดชอบ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Review"
msgstr "รีวิว"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Running"
msgstr "กำลังทำงาน"

#. module: utm
#: model:utm.campaign,name:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "การขาย"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "กำหนดเวลา"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr "ค้นหาสื่อกลาง UTM"

#. module: utm
#: model:utm.source,name:utm.utm_source_search_engine
msgid "Search engine"
msgstr "เครื่องมือค้นหา"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Send"
msgstr "ส่ง"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
#, python-format
msgid "Sent"
msgstr "ส่งแล้ว"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch"
msgstr "Soft-Launch"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Soft-Launch Flow"
msgstr "โฟลว์ Soft-Launch"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "แหล่งที่มา"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "ชื่อแหล่งที่มา"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "แหล่งที่มา"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "ขั้นตอน"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "ขั้นตอน"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. : plan, design, in "
"progress,  done, …)."
msgstr ""
"ขั้นตอนช่วยให้คุณจัดระเบียบเวิร์กโฟลว์ของคุณ (เช่น : วางแผน การออกแบบ "
"อยู่ระหว่างดำเนินการ เสร็จสิ้น …)"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""
"แท็กสี "
"ไม่มีสีหมายความว่าไม่มีการแสดงในคัมบังเพื่อแยกแท็กภายในออกจากแท็กการจัดหมวดหมู่สาธารณะ"

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists !"
msgstr "ชื่อแท็กนี้มีอยู่แล้ว!"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
msgid "Tags"
msgstr "แท็ก"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Month"
msgstr "เดือนนี้"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "This Week"
msgstr "สัปดาห์นี้"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"ชื่อนี้ช่วยให้คุณติดตามความพยายามของแคมเปญต่างๆ เช่น Fall_Drive, "
"Christmas_Special"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "นี้เป็นวิธีการจัดส่งเช่น โปสการ์ด อีเมล หรือแบนเนอร์โฆษณา"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"นี่เป็นที่มาของลิงก์ เช่น เครื่องมือค้นหา โดเมนอื่นๆ หรือชื่อรายการอีเมล"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "To be Approved"
msgstr "ได้รับการอนุมัติ"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid ""
"Track incoming events (e.g. : Christmas, Black Friday, ...) and publish "
"timely content."
msgstr ""
"ติดตามอีเวนต์ที่เข้ามา (เช่น คริสต์มาส แบล็กฟรายเดย์ ...) "
"และเผยแพร่เนื้อหาในเวลาที่เหมาะสม"

#. module: utm
#: model:utm.source,name:utm.utm_source_twitter
msgid "Twitter"
msgstr "Twitter"

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "แคมเปญ UTM "

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr "แคมเปญ UTM "

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "สื่อกลาง UTM"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"Twitter\", ...)."
msgstr ""
"สื่อกลาง UTM ติดตามค่าเฉลี่ยที่ใช้ในการดึงดูดการเข้าชม (เช่น \"เว็บไซต์\", "
"\"Twitter\", ...)"

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr "UTM Mixin"

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "แหล่งที่มา UTM"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""
"แหล่งที่มา UTM ติดตามที่มาของการเข้าชม (เช่น \"จดหมายข่าวพฤษภาคม\", \"\", "
"...)"

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr "ขั้นตอน UTM"

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr "แท็ก UTM"

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr "UTMs"

#. module: utm
#. openerp-web
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#, python-format
msgid "Use This For My Campaigns"
msgstr "ใช้สิ่งนี้สำหรับแคมเปญของฉัน"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr "เช่น แบล็คฟรายเดย์"
