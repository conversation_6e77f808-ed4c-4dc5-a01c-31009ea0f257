<?xml version="1.0" encoding="utf-8"?>
<!-- © 2009-2011 <PERSON><PERSON> - Zikzakmedia
     © 2011 <PERSON> A<PERSON>s
     © 2014 <PERSON> - Aserti Global Solutions
     © 2014 <PERSON> - Domatix
     © 2017 <PERSON> - PESOL
     © 2015 <PERSON> - Factor Libre
     © 2015 <PERSON> GAFIC consultores
     © 2015 Vicent Cubells
     © 2013-2020 Pedro M<PERSON> Baeza
     © 2020 Harald <PERSON> - Sygel Technology
     © 2022 Omar <PERSON> - Comunitea
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>

    <record id="mod_111_02" model="account.account.tag">
        <field name="name">mod111[02]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_03" model="account.account.tag">
        <field name="name">mod111[03]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_05" model="account.account.tag">
        <field name="name">mod111[05]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_06" model="account.account.tag">
        <field name="name">mod111[06]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_08" model="account.account.tag">
        <field name="name">mod111[08]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_09" model="account.account.tag">
        <field name="name">mod111[09]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_11" model="account.account.tag">
        <field name="name">mod111[11]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_111_12" model="account.account.tag">
        <field name="name">mod111[12]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>

    <record id="mod_115_02" model="account.account.tag">
        <field name="name">mod115[02]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_115_03" model="account.account.tag">
        <field name="name">mod115[03]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>

    <record id="mod_303_150" model="account.account.tag">
        <field name="name">mod303[150]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_152" model="account.account.tag">
        <field name="name">mod303[152]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_01" model="account.account.tag">
        <field name="name">mod303[01]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_03" model="account.account.tag">
        <field name="name">mod303[03]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_153" model="account.account.tag">
        <field name="name">mod303[153]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_155" model="account.account.tag">
        <field name="name">mod303[155]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_04" model="account.account.tag">
        <field name="name">mod303[04]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_06" model="account.account.tag">
        <field name="name">mod303[06]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_07" model="account.account.tag">
        <field name="name">mod303[07]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_09" model="account.account.tag">
        <field name="name">mod303[09]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_10" model="account.account.tag">
        <field name="name">mod303[10]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_11" model="account.account.tag">
        <field name="name">mod303[11]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_12" model="account.account.tag">
        <field name="name">mod303[12]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_13" model="account.account.tag">
        <field name="name">mod303[13]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_14_sale" model="account.account.tag">
        <field name="name">mod303[14]sale</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_14_purchase" model="account.account.tag">
        <field name="name">mod303[14]purchase</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_15" model="account.account.tag">
        <field name="name">mod303[15]purchase</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_156" model="account.account.tag">
        <field name="name">mod303[156]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_158" model="account.account.tag">
        <field name="name">mod303[158]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_16" model="account.account.tag">
        <field name="name">mod303[16]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_18" model="account.account.tag">
        <field name="name">mod303[18]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_19" model="account.account.tag">
        <field name="name">mod303[19]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_21" model="account.account.tag">
        <field name="name">mod303[21]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_22" model="account.account.tag">
        <field name="name">mod303[22]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_24" model="account.account.tag">
        <field name="name">mod303[24]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_25" model="account.account.tag">
        <field name="name">mod303[25]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_26" model="account.account.tag">
        <field name="name">mod303[26]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_28" model="account.account.tag">
        <field name="name">mod303[28]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_29" model="account.account.tag">
        <field name="name">mod303[29]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_30" model="account.account.tag">
        <field name="name">mod303[30]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_31" model="account.account.tag">
        <field name="name">mod303[31]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_32" model="account.account.tag">
        <field name="name">mod303[32]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_33" model="account.account.tag">
        <field name="name">mod303[33]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_34" model="account.account.tag">
        <field name="name">mod303[34]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_35" model="account.account.tag">
        <field name="name">mod303[35]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_36" model="account.account.tag">
        <field name="name">mod303[36]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_37" model="account.account.tag">
        <field name="name">mod303[37]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_38" model="account.account.tag">
        <field name="name">mod303[38]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_39" model="account.account.tag">
        <field name="name">mod303[39]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_40" model="account.account.tag">
        <field name="name">mod303[40]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_41" model="account.account.tag">
        <field name="name">mod303[41]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_42" model="account.account.tag">
        <field name="name">mod303[42]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_59" model="account.account.tag">
        <field name="name">mod303[59]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_60" model="account.account.tag">
        <field name="name">mod303[60]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_61" model="account.account.tag">
        <!--Not used anymore since Q3 2021; replaced by grids 120, 122, 123 and 124-->
        <field name="name">mod303[61]</field>
        <field name="applicability">taxes</field>
        <field name="active" eval="False"/>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_120" model="account.account.tag">
        <field name="name">mod303[120]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_122" model="account.account.tag">
        <field name="name">mod303[122]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_123" model="account.account.tag">
        <field name="name">mod303[123]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>
    <record id="mod_303_124" model="account.account.tag">
        <field name="name">mod303[124]</field>
        <field name="applicability">taxes</field>
        <field name="country_id" ref="base.es"/>
    </record>

    <record id="account_tax_template_s_iva21b" model="account.tax.template">
        <field name="sequence" eval="0"/> <!-- Para que sea el impuesto por defecto de ventas -->
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 21% (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_07')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva21s" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 21% (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_07')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva21isp" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 21% (ISP)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_07')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_bc" model="account.tax.template">
        <field name="sequence" eval="0"/> <!-- Para que sea el impuesto por defecto de compras -->
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">21% IVA soportado (bienes corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_sc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">21% IVA soportado (servicios corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_sp_in" model="account.tax.template">
        <field name="name">IVA 21% Adquisición de servicios intracomunitarios</field>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="amount" eval="21"/>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_10'), ref('mod_303_36')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_purchase'), ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_ic_bc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="amount_type">percent</field>
        <field name="amount" eval="21"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 21% Adquisición Intracomunitaria. Bienes corrientes</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_ic_bi" model="account.tax.template">
        <field name="name">IVA 21% Adquisición Intracomunitaria. Bienes de inversión</field>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="21"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_10'), ref('mod_303_38')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_39')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_ibc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 21% Importaciones bienes corrientes</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_32')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_33')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_ibi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 21% Importaciones bienes de inversión</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_34')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_35')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf21td" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF (Trabajadores) dinerarios</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <!-- Obtained from https://www.agenciatributaria.es/AEAT.internet/Inicio/La_Agencia_Tributaria/Campanas/Retenciones/Cuadro_informativo_tipos_de_retencion_aplicables__2020_.shtml -->
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_sp_ex" model="account.tax.template">
        <field name="amount" eval="4"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Adquisición de servicios extracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_12'), ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_purchase'), ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_sp_ex" model="account.tax.template">
        <field name="amount" eval="10"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Adquisición de servicios extracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_sp_ex" model="account.tax.template">
        <field name="name">IVA 21% Adquisición de servicios extracomunitarios</field>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="amount" eval="21"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_ic_bc" model="account.tax.template">
        <field name="amount" eval="4"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Adquisición Intracomunitario. Bienes corrientes</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_ic_bi" model="account.tax.template">
        <field name="amount" eval="4"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Adquisición Intracomunitario. Bienes de inversión</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_38'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_39')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_ic_bc" model="account.tax.template">
        <field name="amount" eval="10"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Adquisición Intracomunitario. Bienes corrientes</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_ic_bi" model="account.tax.template">
        <field name="amount" eval="10"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Adquisición Intracomunitario. Bienes de inversión</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_38'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_39')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_ic_bc" model="account.tax.template">
        <field name="amount" eval="5"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 5% Adquisición Intracomunitario. Bienes corrientes</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_ic_sc" model="account.tax.template">
        <field name="amount" eval="5"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 5% Adquisición de servicios intracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_ic_bc" model="account.tax.template">
        <field name="amount" eval="0"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 0% Adquisición Intracomunitario. Bienes corrientes</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_ic_sc" model="account.tax.template">
        <field name="amount" eval="0"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 0% Adquisición de servicios intracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0_sp_i" model="account.tax.template">
        <field name="description">Intracomunitario exento (Servicios)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% Prestación de servicios intracomunitario</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_59')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_59')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva_ns" model="account.tax.template">
        <field name="description">No sujeto (Servicios)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">No sujeto Repercutido (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_120')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_120')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_oss_s_iva_ns" model="account.tax.template">
        <field name="description">No sujeto y acogidas a la OSS (Servicios)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">No sujeto y acogidas a la OSS (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_123')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_123')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva_ns_b" model="account.tax.template">
        <field name="description">No sujeto (Bienes)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">No sujeto Repercutido (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_120')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_120')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_oss_s_iva_ns_b" model="account.tax.template">
        <field name="description">No sujeto y acogidas a la OSS (Bienes)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">No sujeto y acogidas a la OSS (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_123')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_123')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva_e" model="account.tax.template">
        <field name="description">Extracomunitario (Servicios)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% Prestación de servicios extracomunitaria</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_122')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_122')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_ibc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Importaciones bienes corrientes</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_32')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_33')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_ibi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Importaciones bienes de inversión</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_34')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_35')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_ibc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Importaciones bienes corrientes</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_32')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_33')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_ibi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Importaciones bienes de inversión</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_34')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_35')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_ibc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 5% Importaciones bienes corrientes</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_32')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_33')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_isc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 5% Adquisición de servicios extracomunitarios</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_12'), ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_purchase'), ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_ibc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 0% Importaciones bienes corrientes</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_32')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_isc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 0% Adquisición de servicios extracomunitarios</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_12'), ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_purchase'), ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_s_bc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">0% IVA soportado (bienes corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_s_sc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">0% IVA soportado (servicios corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_bi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">4% IVA Soportado (bienes de inversión)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_31')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_sc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">4% IVA soportado (servicios corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_bc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">5% IVA soportado (bienes corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_sc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">5% IVA soportado (servicios corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_bi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">10% IVA Soportado (bienes de inversión)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_31')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_bi" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">21% IVA Soportado (bienes de inversión)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_31')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_bc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">10% IVA soportado (bienes corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_bc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">4% IVA soportado (bienes corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_29')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_sc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">10% IVA soportado (servicios corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0" model="account.tax.template">
        <field name="description">IVA Exento</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA Exento Repercutido Sujeto</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0_ns" model="account.tax.template">
        <field name="description">IVA Exento No Sujeto</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA Exento Repercutido No Sujeto</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_req0" model="account.tax.template">
        <field name="description">0% Rec. Eq.</field>
        <field name="type_tax_use">sale</field>
        <field name="name">0% Recargo Equivalencia Ventas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_16')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_25')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_req05" model="account.tax.template">
        <field name="description">0.50% Rec. Eq.</field>
        <field name="type_tax_use">sale</field>
        <field name="name">0.50% Recargo Equivalencia Ventas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0-5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_16')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_18')],
                'account_id': ref('l10n_es.account_common_477'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_25')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_26')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_req062" model="account.tax.template">
        <field name="description">0.62% Rec. Eq.</field>
        <field name="type_tax_use">sale</field>
        <field name="name">0.62% Recargo Equivalencia Ventas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0.62"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0-62"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_16')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_18')],
                'account_id': ref('l10n_es.account_common_477'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_25')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_26')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva4b" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 4% (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_01')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_03')],
                'account_id': ref('l10n_es.account_common_477'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_15')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva10b" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 10% (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_04')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_06')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_nd" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">21% IVA Soportado no deducible</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="21"/>
        <field name="amount_type">percent</field>
        <field name="analytic" eval="True"/>
        <field name="tax_group_id" ref="tax_group_iva_nd"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_nd" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">10% IVA Soportado no deducible</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="analytic" eval="True"/>
        <field name="tax_group_id" ref="tax_group_iva_nd"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva5_nd" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">5% IVA Soportado no deducible</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="analytic" eval="True"/>
        <field name="tax_group_id" ref="tax_group_iva_nd"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_nd" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">4% IVA Soportado no deducible</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="analytic" eval="True"/>
        <field name="tax_group_id" ref="tax_group_iva_nd"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0s" model="account.tax.template">
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_150')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0b" model="account.tax.template">
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_150')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva4s" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 4% (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_01')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_03')],
                'account_id': ref('l10n_es.account_common_477'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_15')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva5s" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 5% (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_153')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_155')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_15')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva5b" model="account.tax.template">
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 5% (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_153')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_155')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_15')],
                'account_id': ref('l10n_es.account_common_477'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva10s" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 10% (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_04')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_06')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_14_sale')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_req014" model="account.tax.template">
        <field name="description">1.4% Rec. Eq.</field>
        <field name="type_tax_use">sale</field>
        <field name="name">1.4% Recargo Equivalencia Ventas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="1.4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_1-4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_19')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_21')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_25')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_26')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_req52" model="account.tax.template">
        <field name="description">5.2% Rec. Eq.</field>
        <field name="type_tax_use">sale</field>
        <field name="name">5.2% Recargo Equivalencia Ventas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5.2"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_5-2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_22')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_24')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_25')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_26')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_bc" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA Soportado exento (operaciones corrientes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_ns" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA Soportado no sujeto (Servicios)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva0_ns_b" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA Soportado no sujeto (Bienes)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf9" model="account.tax.template">
        <field name="description">Retención 9%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 9%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-9"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf18" model="account.tax.template">
        <field name="description">Retención 18%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 18%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-18"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_18"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf19" model="account.tax.template">
        <field name="description">Retención 19%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 19%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf19a" model="account.tax.template">
        <field name="description">Retención 19% (Arrend.)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta 19% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf195a" model="account.tax.template">
        <field name="description">Retención 19,5% (Arrend.)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta 19,5% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19-5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf19" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 19%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf20a" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones 20% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-20"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf18" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 18%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-18"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_18"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf19a" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones 19% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf195a" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones 19,5% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19-5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf7" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 7%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-7"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf9" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 9%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-9"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_9"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf24" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 24%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-24"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_24"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf24_rdc" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 24% (Rendimientos del capital)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-24"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_24"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf20" model="account.tax.template">
        <field name="description">Retención 20%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 20%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-20"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf20a" model="account.tax.template">
        <field name="description">Retención 20% (Arrend.)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta 20% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-20"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf24" model="account.tax.template">
        <field name="description">Retención 24%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 24%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-24"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_24"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf24_rdc" model="account.tax.template">
        <field name="description">Retención 24% (Rendimientos del capital)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 24% (Rendimientos del capital)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-24"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_24"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva12_agr" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">12% IVA Soportado régimen agricultura</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="12"/>
        <field name="amount_type">percent</field>
        <field name="include_base_amount" eval="1"/>
        <field name="tax_group_id" ref="tax_group_iva_12"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_42')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_42')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva105_gan" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">10,5% IVA Soportado régimen ganadero o pesca</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="10.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_10-5"/>
        <field name="include_base_amount" eval="1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_42')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_42')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0_e" model="account.tax.template">
        <field name="description">Exportación (Bienes)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% Exportaciones</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_60')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_60')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0_ic" model="account.tax.template">
        <field name="description">Intracomunitario exento (Bienes)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">IVA 0% Entregas Intracomunitarias exentas</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_59')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_59')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_req014" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">1.4% Recargo Equivalencia Compras</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="1.4"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_1-4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_req0" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">0% Recargo Equivalencia Compras</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_req05" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">0.50% Recargo Equivalencia Compras</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0.5"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0-5"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_req062" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="name">0.62% Recargo Equivalencia Compras</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="0.62"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_0-62"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_req52" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">5.2% Recargo Equivalencia Compras</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="5.2"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_recargo_5-2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf1" model="account.tax.template">
        <field name="description">Retención 1%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 1%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-1"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf2" model="account.tax.template">
        <field name="description">Retención 2%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 2%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-2"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf21" model="account.tax.template">
        <field name="description">Retención 21%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 21%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf21a" model="account.tax.template">
        <field name="description">Retención 21% (Arrend.)</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta 21% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf7" model="account.tax.template">
        <field name="description">Retención 7%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 7%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-7"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_irpf15" model="account.tax.template">
        <field name="description">Retención 15%</field>
        <field name="type_tax_use">sale</field>
        <field name="name">Retenciones a cuenta IRPF 15%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_473'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf1" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 1%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-1"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_1"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf15" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 15%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf21t" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF (Trabajadores)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <!-- Obtained from https://www.agenciatributaria.es/AEAT.internet/Inicio/La_Agencia_Tributaria/Campanas/Retenciones/Cuadro_informativo_tipos_de_retencion_aplicables__2020_.shtml -->
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_sp_in" model="account.tax.template">
        <field name="amount" eval="10"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 10% Adquisición de servicios intracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_37')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_sp_in" model="account.tax.template">
        <field name="amount" eval="4"/>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount_type">percent</field>
        <field name="type_tax_use">purchase</field>
        <field name="name">IVA 4% Adquisición de servicios intracomunitarios</field>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_36'), ref('mod_303_10')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_37')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_11')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'tag_ids': [ref('mod_303_41')],
                'account_id': ref('l10n_es.account_common_472'),
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf21te" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF (Trabajadores) en especie</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <!-- Obtained from https://www.agenciatributaria.es/AEAT.internet/Inicio/La_Agencia_Tributaria/Campanas/Retenciones/Cuadro_informativo_tipos_de_retencion_aplicables__2020_.shtml -->
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_05')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_06')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_05')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_06')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf15e" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 15% en especie</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-15"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_15"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_11')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_12')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_11')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_12')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf7e" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 7% en especie</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-7"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_7"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_11')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_12')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_11')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_12')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf20" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 20%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-20"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_20"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf21a" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones 21% (Arrendamientos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_115_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_115_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf21p" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 21%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-21"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf2" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 2%</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-2"/>
        <field name="sequence" eval="2"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_2"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_08')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_09')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_s_iva0_isp" model="account.tax.template">
        <field name="description">IVA 0% ISP</field>
        <field name="name">IVA 0% Venta con Inversión del Sujeto Pasivo</field>
        <field name="type_tax_use">sale</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="0"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_0"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_122')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_122')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_isp" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="name">IVA 4% Compra con Inversión del Sujeto Pasivo Nacional</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="4"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_isp" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="name">IVA 10% Compra con Inversión del Sujeto Pasivo Nacional</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="10"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_isp" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="name">IVA 21% Compra con Inversión del Sujeto Pasivo Nacional</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="21"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_28'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_29')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva4_isp_bi" model="account.tax.template">
        <field name="name">IVA 4% ISP (bienes de inversión)</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="4"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_4"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_31')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva10_isp_bi" model="account.tax.template">
        <field name="name">IVA 10% ISP (bienes de inversión)</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="10"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_10"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_31')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_iva21_isp_bi" model="account.tax.template">
        <field name="name">IVA 21% ISP (bienes de inversión)</field>
        <field name="type_tax_use">purchase</field>
        <field name="amount_type">percent</field>
        <field name="amount" eval="21"/>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="tax_group_id" ref="tax_group_iva_21"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_30'), ref('mod_303_12')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_31')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_13')],
            }),
        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_303_40'), ref('mod_303_14_purchase')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_472'),
                'tag_ids': [ref('mod_303_41')],
            }),

            (0,0, {
                'factor_percent': -100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_477'),
                'tag_ids': [ref('mod_303_15')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_rp19" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="name">Retenciones 19% (préstamos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_rrD19" model="account.tax.template">
        <field name="type_tax_use">purchase</field>
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="name">Retenciones 19% (reparto de dividendos)</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
            }),
        ]"/>
    </record>
     <record id="account_tax_template_p_irpf19ca" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 19% Consejeros y administradores</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-19"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_19"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),
        ]"/>
    </record>
    <record id="account_tax_template_p_irpf35cya" model="account.tax.template">
        <field name="description"/> <!-- for resetting the value on existing DBs -->
        <field name="type_tax_use">purchase</field>
        <field name="name">Retenciones IRPF 35% Consejeros y administradores</field>
        <field name="chart_template_id" ref="l10n_es.account_chart_template_common"/>
        <field name="amount" eval="-35"/>
        <field name="amount_type">percent</field>
        <field name="tax_group_id" ref="tax_group_retenciones_35"/>
        <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),

        ]"/>
        <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'base',
                'tag_ids': [ref('mod_111_02')],
            }),

            (0,0, {
                'factor_percent': 100,
                'repartition_type': 'tax',
                'account_id': ref('l10n_es.account_common_4751'),
                'tag_ids': [ref('mod_111_03')],
            }),
        ]"/>
    </record>
</odoo>
