# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>ari <PERSON> <<EMAIL>>, 2022
# <PERSON> <lorenz<PERSON><EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>dr <<EMAIL>>, 2022
# whenwesober, 2022
# <PERSON>to The <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"<PERSON>-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "\"Events App Name\" field is required."
msgstr "Field \"Nama Aplikasi Acara\" diperlukan."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_count
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_count
msgid "# Wishlisted"
msgstr "# Wishlisht"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s from %(company)s"
msgstr "%(name)s dari %(company)s"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "%(name)s, %(function)s at %(company)s"
msgstr "%(name)s, %(function)s pada %(company)s"

#. module: website_event_track
#: code:addons/website_event_track/models/website.py:0
#, python-format
msgid "%s Events"
msgstr "%s Acara"

#. module: website_event_track
#: code:addons/website_event_track/controllers/webmanifest.py:0
#, python-format
msgid "%s Online Events Application"
msgstr "%s Lamaran Acara Online"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "&amp;bull;"
msgstr "&amp;bull;"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_17
msgid "10 DIY Furniture Ideas For Absolute Beginners"
msgstr "10 Ide Furnitur DIY Untuk Pemula"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_18
msgid "6 Woodworking tips and tricks for beginners"
msgstr "6 tips & tricks Pengerjaan Kayu untuk pemula"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "<b>Contact Information</b>"
msgstr "<b>Informasi Kontak</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Mail</b>:"
msgstr "<b>Email</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Phone</b>:"
msgstr "<b>Telepon</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Proposed By</b>:"
msgstr "<b>Diajukan Oleh</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Speaker Biography</b>:"
msgstr "<b>Biografi Pembicara</b>:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Speaker Profile</b>"
msgstr "<b>Profil Pembicara</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "<b>Talk Intro</b>"
msgstr "<b>Pengantar Talkshow</b>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<b>Talk Introduction</b>:"
msgstr "<b>Talk Introduction</b>:"

#. module: website_event_track
#: model:mail.template,body_html:website_event_track.mail_template_data_track_confirmation
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    We are pleased to inform you that your proposal <t t-out=\"object.name or ''\">What This Event Is All About</t> has been accepted and confirmed for the event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    You will find more details here:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            View Talk\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"
msgstr ""
"<div>\n"
"    Yth <t t-out=\"object.partner_id.name or object.partner_name or ''\">Brandon Freeman</t><br/>\n"
"    Kami senang untuk mengabarkan Anda bahwa proposal Anda <t t-out=\"object.name or ''\">What This Event Is All About</t> telah diterima dan dikonfirmasi untuk event <t t-out=\"object.event_id.name or ''\">OpenWood Collection Online Reveal</t>.\n"
"    <br/>\n"
"    Anda akan menemukan lebih banyak detail di sini:\n"
"    <div style=\"margin: 16px 0px 16px 0px;\">\n"
"        <a t-attf-href=\"/event/{{ object.event_id.id }}/track/{{ object.id }}\" style=\"padding: 8px 16px 8px 16px; font-size: 14px; color: #FFFFFF; text-decoration: none !important; background-color: #875A7B; border: 0px solid #875A7B; border-radius:3px\">\n"
"            Lihat Talkshow\n"
"        </a>\n"
"    </div>\n"
"    <br/><br/>\n"
"    Terima kasih,\n"
"    <t t-if=\"user.signature\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_search
msgid "<i class=\"fa fa-bell mr-2 text-muted\"/> Favorite Talks"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "<i class=\"fa fa-folder-open\"/> Favorites"
msgstr "<i class=\"fa fa-folder-open\"/> Favorit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "<span class=\"d-none d-md-block ml-2\">&amp;bull;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"
msgstr ""
"<span class=\"o_wetrack_proposal_error_section text-danger d-none ml8\">\n"
"                                        <i class=\"fa fa-close mr4\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                                        <span class=\"o_wetrack_proposal_error_message\"/>\n"
"                                    </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Introduction</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">Pengantar Talkshow</span>\n"
"                                            <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<span class=\"o_wetrack_proposal_label_content\">Talk Title</span>\n"
"                                            <span>*</span>"
msgstr ""
"<span class=\"o_wetrack_proposal_label_content\">Judui Talkshow</span>\n"
"                                            <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid ""
"<span for=\"contact_name\">Name</span>\n"
"                    <span>*</span>"
msgstr ""
"<span for=\"contact_name\">Nama</span>\n"
"                    <span>*</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "<span id=\"search_number\" class=\"mr-1\">0</span>Results"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<span>&amp;nbsp;at&amp;nbsp;</span>"
msgstr ""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_template_new
msgid "<span>New track proposal</span>"
msgstr "<span>Proposal track baru</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "<span>hours</span>"
msgstr "<span>jam</span>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Lightning Talks</strong>. These are 30 minutes talks on many\n"
"                                    different topics. Most topics are accepted in lightning talks."
msgstr ""
"<strong>Talkshow Petir</strong>. Ini adalah talkshow 30 menit mengenai banyak\n"
"                                    topik yang berbega. Hampir semua topik diterima di talkshow petir."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "<strong>Location:</strong>"
msgstr "<strong>Lokasi:</strong>"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"<strong>Regular Talks</strong>. These are standard talks with slides,\n"
"                                    alocated in slots of 60 minutes."
msgstr ""
"<strong>Talkshow Reguler</strong>. Ini adalah talkshow standar dengan slide,\n"
"                                    dialokasikan pada slot selama 60 menit."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track7
msgid "A technical explanation of how to use computer design apps"
msgstr ""
"Penjelasan teknis tentang bagaimana cara menggunakan aplikasi desain "
"komputer"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__kanban_state
msgid ""
"A track's kanban state indicates special situations affecting it:\n"
" * Grey is the default situation\n"
" * Red indicates something is preventing the progress of this track\n"
" * Green indicates the track is ready to be pulled to the next stage"
msgstr ""
"Status kanban untuk track mengindikasikan situasi spesial yang mempengaruhinya:\n"
" * Abu-abu adalah situasi default\n"
" * Merah mengindikasikan ada sesuatu yang mencegah progress track ini\n"
" * Hijau mengindikasikan track ini siap untuk ditarik ke tahap berikutnya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__active
msgid "Active"
msgstr "Aktif"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid ""
"Add a description to help your coworkers understand the meaning and purpose "
"of the stage."
msgstr ""
"Tambahkan keterangan untuk membantu rekan kerja Anda memahami arti dan "
"tujuan tahap ini."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Add a description..."
msgstr "Tambahkan keterangan..."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid "Add a new stage in the task pipeline"
msgstr "Tambahkan tahap baru di pipeline tugas"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid ""
"Add tags to your tracks to help your attendees browse your event web pages."
msgstr ""
"Tambahkan tag ke track Anda untuk membantu peserta browse halaman website "
"acara Anda."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track19
msgid "Advanced lead management : tips and tricks from the fields"
msgstr "Advanced lead management: tips dan tricks dari lapangan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track13
msgid "Advanced reporting"
msgstr "Pelaporan advanced"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Agenda"
msgstr "Agenda"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "All Talks"
msgstr "Semua Talkshow"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Allow Track Proposals"
msgstr "Semua Proposal Track"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Allow push notifications?"
msgstr "Izi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Allow video and audio recording of their\n"
"                                    presentation, for publishing on our website."
msgstr ""
"Izinkan rekaman video dan audio presentasi\n"
"                                    mereka, untuk dipublikasikan pada website kami."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlisted_by_default
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Always Wishlisted"
msgstr "Selalu di-Wishlist"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage2
msgid "Announced"
msgstr "Mengumumkan"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Application"
msgstr "Lamaran"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_visitor__is_blacklisted
msgid ""
"As key track cannot be un-favorited, this field store the partner choice to "
"remove the reminder for key tracks."
msgstr ""
"Karena key track tidak dapat dihapus dari favorit, field store ini adalah "
"pilihan mitra untuk menghapus pengingat untuk key track."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"As you may have heard before, making your own furniture is actually not as difficult or as complicated as you think.\n"
"    In fact, some projects are so easy anyone could successfully complete them. For example, making a cute stool out of\n"
"    a old tire is a real piece of cake and if you’re in need of a coffee table you can easily put one together using\n"
"    wood crates."
msgstr ""
"Seperti yang Anda pernah dengar sebelumnya, membuat furnitur Anda sendiri tidak sesusah atau serumit yang Anda pikirkan.\n"
"    Bahkan, beberapa proyek sangat gampang dibuat siapapun dapat berhasil menyelesaikan mereka. Contohnya, membuat bangku yang lucu dari\n"
"    ban bekas itu sangat mudah dan bila Anda membutuhkan meja kopi Anda dapat dengan mudah membuat satu menggunakan\n"
"    peti kayu."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_1
msgid "Audience"
msgstr "Audiens"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__allowed_track_tag_ids
msgid "Available Track Tags"
msgstr "Track Tag yang Tersedia"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Bandy clamp hack"
msgstr "Bandy clamp hack"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_biography
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Biography"
msgstr "Biografi"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_blocked:website_event_track.event_7_track_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_12
#: model:event.track,legend_blocked:website_event_track.event_7_track_13
#: model:event.track,legend_blocked:website_event_track.event_7_track_14
#: model:event.track,legend_blocked:website_event_track.event_7_track_15
#: model:event.track,legend_blocked:website_event_track.event_7_track_16
#: model:event.track,legend_blocked:website_event_track.event_7_track_17
#: model:event.track,legend_blocked:website_event_track.event_7_track_18
#: model:event.track,legend_blocked:website_event_track.event_7_track_19
#: model:event.track,legend_blocked:website_event_track.event_7_track_2
#: model:event.track,legend_blocked:website_event_track.event_7_track_20
#: model:event.track,legend_blocked:website_event_track.event_7_track_21
#: model:event.track,legend_blocked:website_event_track.event_7_track_22
#: model:event.track,legend_blocked:website_event_track.event_7_track_23
#: model:event.track,legend_blocked:website_event_track.event_7_track_24
#: model:event.track,legend_blocked:website_event_track.event_7_track_25
#: model:event.track,legend_blocked:website_event_track.event_7_track_26
#: model:event.track,legend_blocked:website_event_track.event_7_track_3
#: model:event.track,legend_blocked:website_event_track.event_7_track_4
#: model:event.track,legend_blocked:website_event_track.event_7_track_5
#: model:event.track,legend_blocked:website_event_track.event_7_track_6
#: model:event.track,legend_blocked:website_event_track.event_7_track_7
#: model:event.track,legend_blocked:website_event_track.event_7_track_8
#: model:event.track,legend_blocked:website_event_track.event_7_track_9
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_1
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_10
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_11
#: model:event.track,legend_blocked:website_event_track.event_7_track_l3_2
#: model:event.track,legend_blocked:website_event_track.event_track1
#: model:event.track,legend_blocked:website_event_track.event_track10
#: model:event.track,legend_blocked:website_event_track.event_track11
#: model:event.track,legend_blocked:website_event_track.event_track12
#: model:event.track,legend_blocked:website_event_track.event_track13
#: model:event.track,legend_blocked:website_event_track.event_track14
#: model:event.track,legend_blocked:website_event_track.event_track15
#: model:event.track,legend_blocked:website_event_track.event_track16
#: model:event.track,legend_blocked:website_event_track.event_track17
#: model:event.track,legend_blocked:website_event_track.event_track18
#: model:event.track,legend_blocked:website_event_track.event_track19
#: model:event.track,legend_blocked:website_event_track.event_track2
#: model:event.track,legend_blocked:website_event_track.event_track20
#: model:event.track,legend_blocked:website_event_track.event_track21
#: model:event.track,legend_blocked:website_event_track.event_track22
#: model:event.track,legend_blocked:website_event_track.event_track23
#: model:event.track,legend_blocked:website_event_track.event_track24
#: model:event.track,legend_blocked:website_event_track.event_track25
#: model:event.track,legend_blocked:website_event_track.event_track27
#: model:event.track,legend_blocked:website_event_track.event_track28
#: model:event.track,legend_blocked:website_event_track.event_track29
#: model:event.track,legend_blocked:website_event_track.event_track3
#: model:event.track,legend_blocked:website_event_track.event_track30
#: model:event.track,legend_blocked:website_event_track.event_track31
#: model:event.track,legend_blocked:website_event_track.event_track4
#: model:event.track,legend_blocked:website_event_track.event_track5
#: model:event.track,legend_blocked:website_event_track.event_track6
#: model:event.track,legend_blocked:website_event_track.event_track7
#: model:event.track,legend_blocked:website_event_track.event_track8
#: model:event.track,legend_blocked:website_event_track.event_track9
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage0
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage1
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage2
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage3
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage4
#: model:event.track.stage,legend_blocked:website_event_track.event_track_stage5
#, python-format
msgid "Blocked"
msgstr "Diblokir"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Book your seats to the best talks"
msgstr "Booking tempat duduk Anda untuk talkshow-talkshow terbaik"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Book your talks"
msgstr "Booking talkshow-talkshow Anda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_14
msgid "Building a DIY cabin from the ground up"
msgstr "Buat pondok DIY dari bawah ke atas"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_url
msgid "Button Target URL"
msgstr "Tombol URL Sasaran"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_title
msgid "Button Title"
msgstr "Judul Tombol"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_delay
msgid "Button appears"
msgstr "Tombol muncul"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_website_cta_live
msgid "CTA button is available"
msgstr "Tombol CTA tersedia"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Call for Proposals"
msgstr "Call for proposal"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__can_publish
msgid "Can Publish"
msgstr "Dapat Dipublikasikan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_cancel
msgid "Canceled Stage"
msgstr "Tahap Batal"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage5
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Categories"
msgstr "Kategori"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__category_id
msgid "Category"
msgstr "Kategori"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_12
msgid "Climate positive"
msgstr "Iklim positif"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__color
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__color
msgid "Color"
msgstr "Warna"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: website_event_track
#: code:addons/website_event_track/controllers/event_track.py:0
#, python-format
msgid "Coming soon"
msgstr "Coming soon"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Coming soon ..."
msgstr "Coming soon ..."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_company_name
msgid "Company Name"
msgstr "Nama Perusahaan"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: website_event_track
#: model:mail.template,subject:website_event_track.mail_template_data_track_confirmation
msgid "Confirmation of {{ object.name }}"
msgstr "Konfirmasi untuk {{ object.name }}"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage1
msgid "Confirmed"
msgstr "Dikonfirmasi"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_13
#: model_terms:event.track,description:website_event_track.event_7_track_3
msgid ""
"Considering to build a wooden house? Watch this video to find out more "
"information about a construction process and final result. Step by step "
"simple explanation! Interested?"
msgstr ""
"Mempertimbangkan untuk membangun rumah kayu? Buat video ini untuk mencari "
"informasi lebih lanjut mengenai proses konstruksi dan hasil akhir. "
"Penjelasan sederhana langkah demi langkah! Tertarik?"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_id
#, python-format
msgid "Contact"
msgstr "Kontak"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Contact Details"
msgstr "Informasi  Kontak"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_email
#, python-format
msgid "Contact Email"
msgstr "Kontak Email"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__contact_phone
msgid "Contact Phone"
msgstr "Nomor Telepon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_email
msgid "Contact email is private and used internally"
msgstr "Email kontak bersifat pribadi dan digunakan secara internal"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Contact me through a different email/phone"
msgstr "Hubungi saya melalui email/telepon yang berbeda"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_id
msgid "Contact of the track, may be different from speaker."
msgstr "Kontak untuk track, dapat berbeda dari pembicara."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__contact_phone
msgid "Contact phone is private and used internally"
msgstr "Nomor telepon bersifat pribad dan digunakan secara internal"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid "Create a Track"
msgstr "Buat Track"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid "Create a Track Location"
msgstr "Buat Lokasi Track"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_tag
msgid "Create a Track Tag"
msgstr "Buat Tag Track"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__create_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Customer"
msgstr "Pelanggan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_19
msgid "DIY Timber Cladding Project"
msgstr "Project DIY Timber Cladding"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Date"
msgstr "Tanggal"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_11
msgid "Day 2 Wrapup"
msgstr "Wrapup Day 2"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_8
msgid "Dealing with OpenWood Furniture"
msgstr "Berbisnis dengan OpenWood Furniture"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Define labels explaining kanban state management."
msgstr "Definisikan label menjelaskan manajemen status kanban."

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_stage_action
msgid ""
"Define the steps that will be used in the event from the\n"
"            creation of the track, up to the closing of the track.\n"
"            You will use these stages in order to track the progress in\n"
"            solving an event track."
msgstr ""
"Definisikan langkah-langkah yang akan digunakan di acara dari\n"
"            pembuatan track, sampai penutupan track.\n"
"            Anda akan menggunakan tahap-tahap ini untuk melacak proses dalam\n"
"            menyelesaikan track acara."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Delete"
msgstr "Hapus"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__description
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__description
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Description"
msgstr "Deskripsi"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_tag_line
msgid "Description of the partner (name, function and company name)"
msgstr "Keterangan mitra (nama, fungsi dan nama perusahaan)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track25
msgid "Design contest (entire afternoon)"
msgstr "Kontes desain (seluruh sore hari)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track24
msgid "Design contest (entire day)"
msgstr "Kontes desain (seluruh hari)"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track6
msgid "Detailed roadmap of our new products"
msgstr "Roadmap detail mengenai produk-produk baru kami"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track11
msgid "Discover our new design team"
msgstr "Temukan tim desain baru kami"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__display_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta
msgid ""
"Display a Call to Action button to your Attendees while they watch your "
"Track."
msgstr ""
"Tampilkan tombol Call to Action (CTA) untuk Peserta-Peserta Anda selagi "
"mereka melihat Track Anda."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
msgid "Done"
msgstr "Selesai"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Dowel Hack"
msgstr "Dowel Hack"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Dropdown menu"
msgstr "Menu dropdown"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__duration
msgid "Duration"
msgstr "Durasi"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_3
msgid "Easy Way To Build a Wooden House"
msgstr "Cara Gampang Membuat Rumah Kayu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "Edit Track"
msgstr "Mengedit jalur"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Email"
msgstr "Email"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__mail_template_id
msgid "Email Template"
msgstr "Template Email"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Error"
msgstr "Eror!"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_event
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event"
msgstr "Acara"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_tree
msgid "Event Location"
msgstr "Lokasi acara"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_location
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_location_form
msgid "Event Locations"
msgstr "Lokasi acara"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_proposal_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track_proposal
msgid "Event Proposals Menus"
msgstr "Menu Proposal Acara"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_type
msgid "Event Template"
msgstr "Templat Acara"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tree
msgid "Event Track"
msgstr "Track acara"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_location
msgid "Event Track Location"
msgstr "Lokasi Track Acara"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_stage
msgid "Event Track Stage"
msgstr "Tahap Track Acara"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_tag_tree
msgid "Event Track Tag"
msgstr "Acara Track Tag"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_tag_category
msgid "Event Track Tag Category"
msgstr "Kategori Tag Track Acara"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_from_event
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_calendar
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Event Tracks"
msgstr "Track acara"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_menu_ids
#: model:ir.model.fields.selection,name:website_event_track.selection__website_event_menu__menu_type__track
msgid "Event Tracks Menus"
msgstr "Menu Track Acara"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_21
msgid "Event Wrapup"
msgstr "Wrapup Acara"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,field_description:website_event_track.field_website__events_app_name
msgid "Events App Name"
msgstr "Nama Aplikasi Acara"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Events PWA"
msgstr "PWA Acara"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Favorite On"
msgstr "Favorit On"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.session_topbar
msgid "Favorites"
msgstr "Favorit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_topbar
msgid "Filter Tracks..."
msgstr "Menyaring trek..."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Finished"
msgstr "Selesai"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_2
msgid "First Day Wrapup"
msgstr "Wrapup Hari Pertama"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__fold
msgid "Folded in Kanban"
msgstr "Sembunyikan di Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: website_event_track
#: model:event.track.tag.category,name:website_event_track.event_track_tag_category_2
msgid "Format"
msgstr "Format"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_fully_accessible
msgid "Fully accessible"
msgstr "Dapat diakses sepenuhnya"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "Get prepared and"
msgstr "Siap-siap dan"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Glue tip"
msgstr "Glue tip"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__done
msgid "Green"
msgstr "Hijau"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_done
msgid "Green Kanban Label"
msgstr "Label Kanban Hijau"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__normal
msgid "Grey"
msgstr "Abu-Abu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Label Kanban Abu-Abu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_11
msgid "Happy with OpenWood"
msgstr "Senang dengan OpenWood"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__2
msgid "High"
msgstr "Tinggi"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__3
msgid "Highest"
msgstr "Tertinggi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Home page"
msgstr "Beranda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track21
msgid "House of World Cultures"
msgstr "Kultur House of World"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "How can our team get in touch with you?"
msgstr "Bagaimana tim kami dapat menghubungi Anda?"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track18
msgid "How to build your marketing strategy within a competitive environment"
msgstr ""
"Bagaimana membangun strategi marketing Anda di dalam lingkungan yang "
"kompetitif"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track15
msgid "How to communicate with your community"
msgstr "Cara berkomunikasi dengan komunitas Anda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track1
msgid "How to design a new piece of furniture"
msgstr "Cara merancang furniture baru"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track4
msgid "How to develop automated processes"
msgstr "Cara mengembangkan proses yang otomatis"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track16
msgid "How to follow us on the social media"
msgstr "Cara mengikuti kami di sosial media"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track9
msgid "How to improve your quality processes"
msgstr "Cara meningkatkan proses berkualitas Anda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track2
msgid "How to integrate hardware materials in your pieces of furniture"
msgstr "Cara mengintegrasikan material hardware di furnitur-furniture Anda"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track8
msgid "How to optimize your sales, from leads to sales orders"
msgstr "Cara mengoptimalkan sales Anda, dari lead sampai sales order"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__id
msgid "ID"
msgstr "ID"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_fully_accessible
msgid ""
"If checked, automatically publish tracks so that access links to customers "
"are provided."
msgstr ""
"Jika dicentang, secara otomatis publikasikan track untuk menyediakan link "
"akses ke pelanggan."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "If checked, the related tracks will be visible in the frontend."
msgstr "Jika dicentang, track yang terkait akan terlihat di frontend."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__mail_template_id
msgid ""
"If set an email will be sent to the customer when the track reaches this "
"step."
msgstr ""
"Jika dinyalakan email akan dikirim ke pelanggan saat track mencapai langkah "
"ini."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__wishlisted_by_default
msgid ""
"If set, the talk will be set as favorite for each attendee registered to the"
" event."
msgstr ""
"Jika dinyalakan, talkshow akan ditetapkan sebagai favorit untuk setiap "
"peserta yang didaftarkan untuk acara."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image_url
msgid "Image URL"
msgstr "URL Gambar"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "In"
msgstr "Masuk"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_normal:website_event_track.event_7_track_1
#: model:event.track,legend_normal:website_event_track.event_7_track_10
#: model:event.track,legend_normal:website_event_track.event_7_track_11
#: model:event.track,legend_normal:website_event_track.event_7_track_12
#: model:event.track,legend_normal:website_event_track.event_7_track_13
#: model:event.track,legend_normal:website_event_track.event_7_track_14
#: model:event.track,legend_normal:website_event_track.event_7_track_15
#: model:event.track,legend_normal:website_event_track.event_7_track_16
#: model:event.track,legend_normal:website_event_track.event_7_track_17
#: model:event.track,legend_normal:website_event_track.event_7_track_18
#: model:event.track,legend_normal:website_event_track.event_7_track_19
#: model:event.track,legend_normal:website_event_track.event_7_track_2
#: model:event.track,legend_normal:website_event_track.event_7_track_20
#: model:event.track,legend_normal:website_event_track.event_7_track_21
#: model:event.track,legend_normal:website_event_track.event_7_track_22
#: model:event.track,legend_normal:website_event_track.event_7_track_23
#: model:event.track,legend_normal:website_event_track.event_7_track_24
#: model:event.track,legend_normal:website_event_track.event_7_track_25
#: model:event.track,legend_normal:website_event_track.event_7_track_26
#: model:event.track,legend_normal:website_event_track.event_7_track_3
#: model:event.track,legend_normal:website_event_track.event_7_track_4
#: model:event.track,legend_normal:website_event_track.event_7_track_5
#: model:event.track,legend_normal:website_event_track.event_7_track_6
#: model:event.track,legend_normal:website_event_track.event_7_track_7
#: model:event.track,legend_normal:website_event_track.event_7_track_8
#: model:event.track,legend_normal:website_event_track.event_7_track_9
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_1
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_10
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_11
#: model:event.track,legend_normal:website_event_track.event_7_track_l3_2
#: model:event.track,legend_normal:website_event_track.event_track1
#: model:event.track,legend_normal:website_event_track.event_track10
#: model:event.track,legend_normal:website_event_track.event_track11
#: model:event.track,legend_normal:website_event_track.event_track12
#: model:event.track,legend_normal:website_event_track.event_track13
#: model:event.track,legend_normal:website_event_track.event_track14
#: model:event.track,legend_normal:website_event_track.event_track15
#: model:event.track,legend_normal:website_event_track.event_track16
#: model:event.track,legend_normal:website_event_track.event_track17
#: model:event.track,legend_normal:website_event_track.event_track18
#: model:event.track,legend_normal:website_event_track.event_track19
#: model:event.track,legend_normal:website_event_track.event_track2
#: model:event.track,legend_normal:website_event_track.event_track20
#: model:event.track,legend_normal:website_event_track.event_track21
#: model:event.track,legend_normal:website_event_track.event_track22
#: model:event.track,legend_normal:website_event_track.event_track23
#: model:event.track,legend_normal:website_event_track.event_track24
#: model:event.track,legend_normal:website_event_track.event_track25
#: model:event.track,legend_normal:website_event_track.event_track27
#: model:event.track,legend_normal:website_event_track.event_track28
#: model:event.track,legend_normal:website_event_track.event_track29
#: model:event.track,legend_normal:website_event_track.event_track3
#: model:event.track,legend_normal:website_event_track.event_track30
#: model:event.track,legend_normal:website_event_track.event_track31
#: model:event.track,legend_normal:website_event_track.event_track4
#: model:event.track,legend_normal:website_event_track.event_track5
#: model:event.track,legend_normal:website_event_track.event_track6
#: model:event.track,legend_normal:website_event_track.event_track7
#: model:event.track,legend_normal:website_event_track.event_track8
#: model:event.track,legend_normal:website_event_track.event_track9
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage0
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage1
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage2
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage3
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage4
#: model:event.track.stage,legend_normal:website_event_track.event_track_stage5
#, python-format
msgid "In Progress"
msgstr "Dalam Proses"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_15
msgid "In this video we will see how lumber is made in a sawmill factory."
msgstr ""
"Di video ini kami akan melihat bagaimana kayu akan dibuat di pabrik "
"penggergajian"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "In this video, I covered 6 tips and tricks to help out beginners:"
msgstr "Di video ini, saya menjelaskan 6 tip dan trick yang membantu pemula."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install"
msgstr "Pasang"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/website_event_pwa.xml:0
#, python-format
msgid "Install Application"
msgstr "Instal Aplikasi"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Interactivity"
msgstr "Interaktivitas"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Introduction"
msgstr "Pengantar"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_website_cta_live
msgid "Is CTA Live"
msgstr "Apakah CTA Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_published
msgid "Is Published"
msgstr "Dipublikasikan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_reminder_on
msgid "Is Reminder On"
msgstr "Adalah Pengingat Pada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_done
msgid "Is Track Done"
msgstr "Apakah Track Selesai"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_live
msgid "Is Track Live"
msgstr "Apakah Track Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_soon
msgid "Is Track Soon"
msgstr "Apakah Track Sebentar Lagi"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_today
msgid "Is Track Today"
msgstr "Apakah Track Hari Ini"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_track_upcoming
msgid "Is Track Upcoming"
msgstr "Apakah Track Akan Datang"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_wishlisted
msgid "Is Wishlisted"
msgstr "Apakah di-Wishlist"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__is_blacklisted
msgid "Is reminder off"
msgstr "Apakah pengingat mati"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_function
msgid "Job Position"
msgstr "Posisi Kerja"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Job Title"
msgstr "Jabatan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Kanban diblokir penjelasan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanban berkelanjutan penjelasan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state
msgid "Kanban State"
msgstr "Status Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__kanban_state_label
msgid "Kanban State Label"
msgstr "Label Status Kanban"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban berlaku penjelasan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track23
msgid "Key Success factors selling our furniture"
msgstr "Faktor Kunci Sukses dalam menjual furnitur kami"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_9
msgid "Kitchens for the Future"
msgstr "Dapur-Dapur untuk Masa Depan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category____last_update
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_uid
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__write_date
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track12
msgid "Latest trends"
msgstr "Trend-trend terkini"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_26
msgid "Less Furniture is More Furniture"
msgstr "Less Furniture is More Furniture"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_4
msgid "Life at Home Around the World: William’s Story"
msgstr "Hidup di Rumah Mengelili Dunia: Cerita William"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_19
msgid ""
"Link to Q&amp;A here! The time has come to hide those old block walls. Love "
"simple and transformation type projects like this! :)-"
msgstr ""
"Link ke Q&amp;A di sini! Waktunya telah datang untuk menyembunyikan blok "
"dinding tersebut. Saya cinta projek transformasi yang simpel seperti ini! "
":)-"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Live"
msgstr "Live"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_cards
msgid "Live Now"
msgstr "Live Sekarang"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_10
msgid "Live Testimonial"
msgstr "Testimoni Live"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_25
msgid "Live Testimonials"
msgstr "Testimoni Live"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__location_id
#: model:ir.model.fields,field_description:website_event_track.field_event_track_location__name
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Location"
msgstr "Lokasi"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_13
msgid "Log House Building"
msgstr "Bangunan Rumah Kayu"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_15
msgid "Logs to lumber"
msgstr "Batang Pohon menjadi kayu"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__0
msgid "Low"
msgstr "Rendah"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track31
msgid "Lunch"
msgstr "Makan siang"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta
msgid "Magic Button"
msgstr "Magic Button"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Making a center marking gauge"
msgstr "Membuat center marking gauge"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_location
msgid ""
"Manage from here the places where you organize your tracks (e.g. Rooms, "
"Channels, ...)."
msgstr ""
"Kelola di sini lokasi di mana Anda mengorganisir track Anda (contohnya "
"Ruangan, Channel, ...)."

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__priority__1
msgid "Medium"
msgstr "Media"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Jenis Menu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track22
msgid "Minimal but efficient design"
msgstr "Desain minimal tapi efisien"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_cta_start_remaining
msgid "Minutes before CTA starts"
msgstr "Hitungan menit sebelum CTA dimulai"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_remaining
msgid "Minutes before track starts"
msgstr "Hitungan menit sebelum track dimulai"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__track_start_relative
msgid "Minutes compare to track start"
msgstr "Hitungan menit untuk membandingkan ke pemulaian track"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Miter saw tip"
msgstr "Miter saw tip"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track30
msgid "Morning break"
msgstr "Istirahat"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track27
msgid "My Company global presentation"
msgstr "Presentasi global Perusahaan Saya"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "My Tracks"
msgstr "Track-Track Saya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_name
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__name
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name"
msgstr "Nama"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.res_config_settings_view_form
msgid "Name of your website's Events Progressive Web Application"
msgstr "Name of your website's Events Progressive Web Application"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track20
msgid "New Certification Program"
msgstr "Program Sertifikasi Baru"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_event_track
msgid "New Track"
msgstr "Track Baru"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid "No Track Visitors yet!"
msgstr "Belum ada Pengunjung Track!"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "No results found"
msgstr "Tidak ada hasil yang ditemukan"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_action_from_visitor
msgid "No track favorited by this visitor"
msgstr "Tidak ada track yang difavorit oleh pengunjung ini"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "No track found."
msgstr "Tidak ada track yang ditemukan."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_tag__color
msgid "Note that colorless tags won't be available on the website."
msgstr "Catat bahwa tag tanpa warna tidak akan tersedia pada website."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_24
msgid "Old is New"
msgstr "Old is New"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_20
msgid "Our Last Day Together !"
msgstr "Hari Terakhir Kami Bersama-Sama!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__partner_id
msgid "Partner"
msgstr "Rekanan"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track14
msgid "Partnership programs"
msgstr "Program kemitraan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal_contact_details
msgid "Phone"
msgstr "Telepon"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Picture"
msgstr "Gambar"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Plan your experience by adding your favorites talks to your wishlist"
msgstr ""
"Rencanakan pengalaman Anda dengan menambahkan talkshow favorit Anda ke "
"wishlist Anda"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please enter either a contact email address or a contact phone number."
msgstr "Mohon masukkan baik alamat email kontak atau nomor telepon."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "Please fill out the form correctly."
msgstr "Mohon isi formulir dengan benar."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track3
msgid "Portfolio presentation"
msgstr "Presentasi portfolio"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_16
msgid "Pretty. Ugly. Lovely."
msgstr "Cantik. Jelek. Indah."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "Previous page"
msgstr "Halaman sebelumnya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__priority
msgid "Priority"
msgstr "Prioritas"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_7
msgid ""
"Probably one of the most asked questions I've gotten is how I got started "
"woodworking! In this video I share with you how/why I started building "
"furniture!"
msgstr ""
"Biasanya salah satu pertanyaan yang paling sering saya dapatkan adalah "
"bagaimana saya memulai woodworking! Di video ini saya akan membagikan ke "
"Anda bagaimana/kenapa saya mulai membuat furnitur!"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage0
msgid "Proposal"
msgstr "Proposal"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Proposals are closed!"
msgstr "Proposal ditutup!"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track_proposal
msgid "Proposals on Website"
msgstr "Proposal pada Website"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage3
msgid "Published"
msgstr "Terpublikasi"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track10
msgid "Raising qualitive insights from your customers"
msgstr "Tingkatkan wawasan kualitatif dari pelanggan-pelanggan Anda"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track_stage.py:0
#: model:event.track,legend_done:website_event_track.event_7_track_1
#: model:event.track,legend_done:website_event_track.event_7_track_10
#: model:event.track,legend_done:website_event_track.event_7_track_11
#: model:event.track,legend_done:website_event_track.event_7_track_12
#: model:event.track,legend_done:website_event_track.event_7_track_13
#: model:event.track,legend_done:website_event_track.event_7_track_14
#: model:event.track,legend_done:website_event_track.event_7_track_15
#: model:event.track,legend_done:website_event_track.event_7_track_16
#: model:event.track,legend_done:website_event_track.event_7_track_17
#: model:event.track,legend_done:website_event_track.event_7_track_18
#: model:event.track,legend_done:website_event_track.event_7_track_19
#: model:event.track,legend_done:website_event_track.event_7_track_2
#: model:event.track,legend_done:website_event_track.event_7_track_20
#: model:event.track,legend_done:website_event_track.event_7_track_21
#: model:event.track,legend_done:website_event_track.event_7_track_22
#: model:event.track,legend_done:website_event_track.event_7_track_23
#: model:event.track,legend_done:website_event_track.event_7_track_24
#: model:event.track,legend_done:website_event_track.event_7_track_25
#: model:event.track,legend_done:website_event_track.event_7_track_26
#: model:event.track,legend_done:website_event_track.event_7_track_3
#: model:event.track,legend_done:website_event_track.event_7_track_4
#: model:event.track,legend_done:website_event_track.event_7_track_5
#: model:event.track,legend_done:website_event_track.event_7_track_6
#: model:event.track,legend_done:website_event_track.event_7_track_7
#: model:event.track,legend_done:website_event_track.event_7_track_8
#: model:event.track,legend_done:website_event_track.event_7_track_9
#: model:event.track,legend_done:website_event_track.event_7_track_l3_1
#: model:event.track,legend_done:website_event_track.event_7_track_l3_10
#: model:event.track,legend_done:website_event_track.event_7_track_l3_11
#: model:event.track,legend_done:website_event_track.event_7_track_l3_2
#: model:event.track,legend_done:website_event_track.event_track1
#: model:event.track,legend_done:website_event_track.event_track10
#: model:event.track,legend_done:website_event_track.event_track11
#: model:event.track,legend_done:website_event_track.event_track12
#: model:event.track,legend_done:website_event_track.event_track13
#: model:event.track,legend_done:website_event_track.event_track14
#: model:event.track,legend_done:website_event_track.event_track15
#: model:event.track,legend_done:website_event_track.event_track16
#: model:event.track,legend_done:website_event_track.event_track17
#: model:event.track,legend_done:website_event_track.event_track18
#: model:event.track,legend_done:website_event_track.event_track19
#: model:event.track,legend_done:website_event_track.event_track2
#: model:event.track,legend_done:website_event_track.event_track20
#: model:event.track,legend_done:website_event_track.event_track21
#: model:event.track,legend_done:website_event_track.event_track22
#: model:event.track,legend_done:website_event_track.event_track23
#: model:event.track,legend_done:website_event_track.event_track24
#: model:event.track,legend_done:website_event_track.event_track25
#: model:event.track,legend_done:website_event_track.event_track27
#: model:event.track,legend_done:website_event_track.event_track28
#: model:event.track,legend_done:website_event_track.event_track29
#: model:event.track,legend_done:website_event_track.event_track3
#: model:event.track,legend_done:website_event_track.event_track30
#: model:event.track,legend_done:website_event_track.event_track31
#: model:event.track,legend_done:website_event_track.event_track4
#: model:event.track,legend_done:website_event_track.event_track5
#: model:event.track,legend_done:website_event_track.event_track6
#: model:event.track,legend_done:website_event_track.event_track7
#: model:event.track,legend_done:website_event_track.event_track8
#: model:event.track,legend_done:website_event_track.event_track9
#: model:event.track.stage,legend_done:website_event_track.event_track_stage0
#: model:event.track.stage,legend_done:website_event_track.event_track_stage1
#: model:event.track.stage,legend_done:website_event_track.event_track_stage2
#: model:event.track.stage,legend_done:website_event_track.event_track_stage3
#: model:event.track.stage,legend_done:website_event_track.event_track_stage4
#: model:event.track.stage,legend_done:website_event_track.event_track_stage5
#, python-format
msgid "Ready for Next Stage"
msgstr "Siap untuk Tahap Berikutnya"

#. module: website_event_track
#: model:ir.model.fields.selection,name:website_event_track.selection__event_track__kanban_state__blocked
msgid "Red"
msgstr "Merah"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Label Kanban Merah"

#. module: website_event_track
#: model:event.track.stage,name:website_event_track.event_track_stage4
msgid "Refused"
msgstr "Ditolak"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_relative
msgid "Relative time compared to track start (seconds)"
msgstr "Waktu relatif dibandingkan dengan mulainya track (dalam detik)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_cta_start_remaining
msgid "Remaining time before CTA starts (seconds)"
msgstr "waktu tersisa sebelum CTA dimulai (dalam detik)"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__track_start_remaining
msgid "Remaining time before track starts (seconds)"
msgstr "Waktu tersisa sebelum track dimulai (dalam detik)"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__user_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_23
msgid "Restoring Old Woodworking Tools"
msgstr "Memulihkan Alat Woodworking "

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_23
msgid "Restoring old woodworking tools"
msgstr "Memulihkan alat woodworking"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_18
msgid "Right angle clamp jig"
msgstr "Right angle clamp jig"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__is_seo_optimized
msgid "SEO optimized"
msgstr "Dioptimalkan SEO"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "Schedule some tracks to get started"
msgstr "Jadwalkan track untuk memulai"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_6
msgid "Securing your Lumber during transport"
msgstr "Mengamankan Kayu selama dijalan"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form_tags.js:0
#, python-format
msgid "Select categories"
msgstr "Pilih kategori"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__seo_name
msgid "Seo name"
msgstr "Nama SEO"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__sequence
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#: model_terms:ir.ui.view,arch_db:website_event_track.track_widget_reminder
#, python-format
msgid "Set Favorite"
msgstr "Tetapkan sebagai Favorit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
msgid "Showcase Tracks"
msgstr "Showcase Track"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker"
msgstr "Speaker"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Speaker Bio"
msgstr "Profil Speaker"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speaker Email"
msgstr "Email Speaker"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__image
msgid "Speaker Photo"
msgstr "Foto Pembicara"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_email
msgid ""
"Speaker email is used for public display and may vary from contact email"
msgstr ""
"Email pembicara ditampilkan secara umum dan dapat berbeda dari email kontak"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_name
msgid "Speaker name is used for public display and may vary from contact name"
msgstr ""
"Nama pembicara ditampilkan secara umum dan dapat berbeda dari nama kontak"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__partner_phone
msgid ""
"Speaker phone is used for public display and may vary from contact phone"
msgstr ""
"Nomor telefon pembicara ditampilkan secara umum dan dapat berbeda dari nomor"
" kontak"

#. module: website_event_track
#: code:addons/website_event_track/models/event_track.py:0
#, python-format
msgid "Speakers"
msgstr "saya"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__stage_id
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Stage"
msgstr "Tahapan"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Tahap Deskripsi dan Tooltips"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__name
msgid "Stage Name"
msgstr "Nama Tahap"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track28
msgid "Status & Strategy"
msgstr "Status & Strategi"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submission Agreement"
msgstr "Perjanjian penyerahan"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Submit Proposal"
msgstr "Mengajukan Proposal"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__partner_tag_line
msgid "Tag Line"
msgstr "Tag Line"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__name
msgid "Tag Name"
msgstr "Nama Tag"

#. module: website_event_track
#: model:ir.model.constraint,message:website_event_track.constraint_event_track_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nama tag sudah ada !"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__tag_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag_category__tag_ids
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Tags"
msgstr "Label"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#, python-format
msgid "Talk Proposals"
msgstr "Berbicara proposal"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk added to your Favorites"
msgstr "Talk ditambahkan ke Favorit Anda"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk already in your Favorites"
msgstr "Talk sudah ada di Favorit Anda"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid "Talk removed from your Favorites"
msgstr "Talk dihapus dari Favorit Anda"

#. module: website_event_track
#: code:addons/website_event_track/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside
#, python-format
msgid "Talks"
msgstr "Pembicaraan"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "Talks Types"
msgstr "Pembicaraan jenis"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""
"Tugas sedang dalam progres. Klik untuk blok atau tetapkan sebagai selesai."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"Tugas diblok. Klik untuk menghilangkan blokir atau tetapkan sebagai selesai."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "Thank you for your proposal."
msgstr "Terima kasih atas usulan Anda."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_url
msgid "The full URL to access the document through the website."
msgstr "URL lengkap untuk mengakses dokumen melalui website."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track17
#: model:event.track,name:website_event_track.event_track29
msgid "The new marketing strategy"
msgstr "Strategi marketing baru"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_track5
msgid "The new way to promote your creations"
msgstr "Cara baru untuk mempromosikan karya Anda"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_17
msgid ""
"There are a lot of ideas worth exploring so start with the 10 DIY furniture "
"ideas for absolute beginners."
msgstr ""
"Terdapat banyak ide yang layak dikembangkan jadi mulai dengan 10 ide "
"furnitur DIY untuk pemula."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"There are several variants of wood is available in the world but we are talking about most expensive\n"
"    ones in the world and keeping to the point we have arranged ten most expensive wood."
msgstr ""
"Terdapat beberapa varian kayu yang tersedia di dunia tapi kita sedang berbicara tentang kayu paling\n"
"    mahal di dunia dan tetap mengenai poin tersebut kami telah menyiapkan sepuluh kayu paling mahal"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"They will be created automatically once attendees start browsing your "
"events."
msgstr "Akan dibuat secara otomatis setelah peserta mulai browse acara Anda."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "This event does not accept proposals."
msgstr "Acara ini tidak menerima proposal."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_website__app_icon
msgid ""
"This field holds the image used as mobile app icon on the website (PNG "
"format)."
msgstr ""
"Field ini memegang gambar yang digunakan sebagai ikon aplikasi mobile pada "
"website (format PNG)."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_res_config_settings__events_app_name
#: model:ir.model.fields,help:website_event_track.field_website__events_app_name
msgid "This fields holds the Event's Progressive Web App name."
msgstr "Field ini memegang nama untuk Aplikasi Acara Website Progresif "

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid ""
"This page hasn't been saved for offline reading yet.<br/>Please check your "
"network connection."
msgstr ""
"Halaman ini belum disimpan untuk dibaca secara offline.<br/>Silakan periksa "
"koneksi internet Anda."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Tahap ini disembunyikan pada tampilan kanban ketika tidak ada catatan dalam "
"tahap itu untuk ditampilkan."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr "Langkah ini sudah selesai. Klik untuk blok atau mulai progres."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Timely release of presentation material (slides),\n"
"                                    for publishing on our website."
msgstr ""
"Tepat waktu rilis bahan presentasi (slide),\n"
"                                    untuk dipublikasikan pada website kami."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__name
msgid "Title"
msgstr "Judul"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_22
msgid "Tools for the Woodworking Beginner"
msgstr "Alat untuk Pemula Woodworking"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_5
msgid "Top 10 Most Expensive Wood in the World"
msgstr "10 Kayu Paling Mahal di Dunia"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_5
msgid ""
"Top most expensive wood in the world is quite interesting topic and several people may be surprised\n"
"    that there are hundreds of wood types exist around the globe following different properties and use."
msgstr ""
"Kayu-kayu paling mahal di dunia merupakan topik yang cukup menarik dan beberapa orang mungkin akan terkejut\n"
"    mengetahui bahwa terdapat ratusan tipe kayu yang ada di dunia ini berdasarkan sifat dan penggunaan yang berbeda."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__track_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Track"
msgstr "Lacak"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Lacak / Link Pengunjung"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_blocked
msgid "Track Blocked"
msgstr "Track Diblokir"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_count
msgid "Track Count"
msgstr "Jumlah Track"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date
msgid "Track Date"
msgstr "Melacak tanggal"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__date_end
msgid "Track End Date"
msgstr "Tanggal Akhir Track"

#. module: website_event_track
#: model:ir.ui.menu,name:website_event_track.menu_event_track_location
msgid "Track Locations"
msgstr "Lokasi Track"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Track Proposals Menu Item"
msgstr "Lacak Item Menu Proposal"

#. module: website_event_track
#: model:mail.message.subtype,name:website_event_track.mt_track_ready
msgid "Track Ready"
msgstr "Lacak Kesiapan"

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_ready
msgid "Track Ready for Next Stage"
msgstr "Lacak Kesiapan untuk Stage Berikutnya"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_tree
msgid "Track Stage"
msgstr "Lacak Stage"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_stage_action
#: model:ir.ui.menu,name:website_event_track.event_track_stage_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_stage_view_search
msgid "Track Stages"
msgstr "Lacak Stages"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_tag_category_action
#: model:ir.ui.menu,name:website_event_track.event_track_tag_category_menu
msgid "Track Tag Categories"
msgstr "Lacak Kategori Tag"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_form
msgid "Track Tag Category"
msgstr "Lacak Kategori Tag"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.action_event_track_tag
#: model:ir.model.fields,field_description:website_event_track.field_event_event__tracks_tag_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track_tag
msgid "Track Tags"
msgstr "Melacak Tag"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_tag_category_view_list
msgid "Track Tags Category"
msgstr "Lacak Kategori Tag"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_form
msgid "Track Visitor"
msgstr "Lacak Pengunjung"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_visitor_action
#: model:ir.model.fields,field_description:website_event_track.field_event_track__event_track_visitor_ids
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_visitor_ids
#: model:ir.ui.menu,name:website_event_track.event_track_visitor_menu
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_list
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Track Visitors"
msgstr "Lacak Pengunjung-pengunjung"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.event_track_visitor_action
msgid ""
"Track Visitors store statistics on your events, including how many times "
"tracks have been wishlisted."
msgstr "Lacak Pengunjung-Pengunjung "

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_soon
msgid "Track begins soon"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_today
msgid "Track begins today"
msgstr ""

#. module: website_event_track
#: model:mail.message.subtype,description:website_event_track.mt_track_blocked
msgid "Track blocked"
msgstr "Lacak yang diblokir"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__duration
msgid "Track duration in hours."
msgstr "Lacak durasi dalam jam."

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_live
msgid "Track has started and is ongoing"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_done
msgid "Track is finished"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__is_track_upcoming
msgid "Track is not yet started"
msgstr ""

#. module: website_event_track
#: model:mail.template,name:website_event_track.mail_template_data_track_confirmation
msgid "Track: Confirmation"
msgstr ""

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__track_ids
#: model:ir.model.fields,field_description:website_event_track.field_event_track_tag__track_ids
#: model:ir.ui.menu,name:website_event_track.menu_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_graph
#: model_terms:ir.ui.view,arch_db:website_event_track.website_visitor_view_form
msgid "Tracks"
msgstr "Trek"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_type_view_form_inherit_track
msgid "Tracks Menu Item"
msgstr "Lacak Item Menu"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track_proposal
msgid "Tracks Proposals on Website"
msgstr "Lacak Proposal pada Website"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.action_event_track_from_event
msgid ""
"Tracks define your event schedule. They can be talks, workshops or any "
"similar activity."
msgstr ""
"Track mendefinisikan jadwal acara Anda. Ini dapat merupakan talk, workshop "
"atau kegiatan mirip lainnya."

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_event__website_track
#: model:ir.model.fields,field_description:website_event_track.field_event_type__website_track
msgid "Tracks on Website"
msgstr "Track pada Website"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_aside_other_track
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_display_list
msgid "Unpublished"
msgstr "Belum dipublikasikan"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_search
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Use these simple steps to easily haul LONG lumber in a short box pickup truck.  A dose of carpenter's\n"
"    ingenuity along with a couple boards, a sturdy strap and a few screws are all I use to easily haul\n"
"    long boards from the lumberyard to the Next Level Carpentry shop or jobsite."
msgstr ""
"Gunakan langkah-langkah sederhana ini untuk dengan mudah mengangkut kayu PANJANG menggunakan truk pickup yang kecil. Dengan sedikit kecerdikan tukang\n"
"    kayu ditambah beberapa papan, tali yang kuat dan sekrup saya dapat dengan mudah\n"
"    mengangkut papan-papan panjang dari tempat penebangan kayu ke toko Next Level Carpentry atau situs kerja."

#. module: website_event_track
#: model_terms:event.track,description:website_event_track.event_7_track_6
msgid ""
"Using a unique wrapping method for a tie down strap (NOT Bungee cords!!!) allows lumber to be\n"
"    cinched securely WITHOUT the need to tie and untie tricky or complicated knots."
msgstr ""
"Menggunakan metode pembungkusan yang unik untuk mengikat erat tali (BUKAN tali Bungee!!!) memungkinkan kayu\n"
"    terikat aman TANPA harus mengikat atau mengurai simpul yang rumit."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "View Track"
msgstr "Lihat Track"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_stage__is_visible_in_agenda
msgid "Visible in agenda"
msgstr "Terlihat di Agenda"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_published
msgid "Visible on current website"
msgstr "Terlihat pada website saat ini"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track_visitor__visitor_id
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_visitor_view_search
msgid "Visitor"
msgstr "Pengunjung"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__wishlist_visitor_ids
msgid "Visitor Wishlist"
msgstr "Wishlist Pengunjung"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.website_visitor_action_from_track
msgid "Visitors Wishlist"
msgstr "Wishlist Pengunjung"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_1
msgid "Voice from Customer"
msgstr "Suara dari Pengunjung"

#. module: website_event_track
#: model_terms:ir.actions.act_window,help:website_event_track.website_visitor_action_from_track
msgid "Wait for visitors to add this track to their list of favorites"
msgstr "Tunggu pengunjung menambahkan track ini ke daftar favorit mereka"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "We did not find any track matching your"
msgstr "Kami tidak menemukan track yang cocok sesuai dengan"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "We require speakers to accept an agreement in which they commit to:"
msgstr ""
"Kami memerlukan speaker untuk menerima kesepakatan di mana mereka "
"berkomitmen untuk:"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"We will accept a broad range of\n"
"                            presentations, from reports on academic and\n"
"                            commercial projects to tutorials and case\n"
"                            studies. As long as the presentation is\n"
"                            interesting and potentially useful to the\n"
"                            audience, it will be considered for\n"
"                            inclusion in the programme."
msgstr ""
"Kami akan menerima berbagai macam\n"
"                            presentasi, dari laporan akademis dan\n"
"                            project-project komersil dan studi\n"
"                            kasus. Selama presentasi menarik\n"
"                            dan berpotensi untuk berguna bagi\n"
"                            audiens, presentasi akan dianggap dipertimbangkan\n"
"                            untuk disertakan dalam program."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/xml/event_track_proposal_templates.xml:0
#, python-format
msgid "We will evaluate your proposition and get back to you shortly."
msgstr "Kami akan mengevaluasi proposisi dan kembali kepada Anda segera."

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website
msgid "Website"
msgstr "Website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_website__app_icon
msgid "Website App Icon"
msgstr "Ikon Aplikasi Website"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_event_menu
msgid "Website Event Menu"
msgstr "Menu Acara Website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_image
msgid "Website Image"
msgstr "Gambar Website"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_menu
msgid "Website Menu"
msgstr "Menu Website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_message_ids
msgid "Website Messages"
msgstr "Pesan Website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_url
msgid "Website URL"
msgstr "URL Website"

#. module: website_event_track
#: model:ir.model,name:website_event_track.model_website_visitor
msgid "Website Visitor"
msgstr "Pengunjung Website"

#. module: website_event_track
#: model:ir.model.fields,help:website_event_track.field_event_track__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_description
msgid "Website meta description"
msgstr "Deskripsi meta website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_keywords
msgid "Website meta keywords"
msgstr "Kata kunci meta website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_title
msgid "Website meta title"
msgstr "Judul meta website"

#. module: website_event_track
#: model:ir.model.fields,field_description:website_event_track.field_event_track__website_meta_og_img
msgid "Website opengraph image"
msgstr "Gambar opengraph Website"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_10
msgid "Welcome to Day 2"
msgstr "Selamat datang di Hari Ke-2"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_1
msgid "What This Event Is All About"
msgstr "Acara ini Mengenai Apa Saja"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid "What is your talk about?"
msgstr "Talk Anda mengenai apa?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_proposal
msgid ""
"Who will give this talk? We will show this to attendees to showcase your "
"talk."
msgstr ""
"Siapa yang akan memberikan talk ini? Kami akan menunjukkan ini ke peserta "
"untuk menunjukkan talk Anda."

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_l3_2
msgid "Who's OpenWood anyway ?"
msgstr "Siapa itu OpenWood ?"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "Wishlisted By"
msgstr "Diwishlist Oleh"

#. module: website_event_track
#: model:ir.actions.act_window,name:website_event_track.event_track_action_from_visitor
#: model:ir.model.fields,field_description:website_event_track.field_website_visitor__event_track_wishlisted_ids
msgid "Wishlisted Tracks"
msgstr "Track yang di-Wishlist"

#. module: website_event_track
#: model:event.track,name:website_event_track.event_7_track_7
msgid "Woodworking: How I got started!"
msgstr "Woodworking: Bagaimana saya memulai!"

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/website_event_track_proposal_form.js:0
#, python-format
msgid "You cannot access this page."
msgstr "Anda tidak dapat mengakses halaman ini."

#. module: website_event_track
#. openerp-web
#: code:addons/website_event_track/static/src/js/event_track_reminder.js:0
#, python-format
msgid ""
"You have to enable push notifications to get reminders for your favorite "
"tracks."
msgstr ""
"Anda harus mengaktifkan push notification untuk mendapatkan pengingat untuk "
"track favorit Anda."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.pwa_offline
msgid "You're offline!"
msgstr "Anda offline!"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.track_card
msgid "ago"
msgstr "lalu"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Get Yours Now !"
msgstr "contohnya, \"Dapatkan Milik Anda Sekarang !\""

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. Inspiring Business Talk"
msgstr "contohnya, Talk Bisnis Inspiratif"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "e.g. http://www.example.com"
msgstr "contohnya, http://www.contoh.com"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_kanban
msgid "hours"
msgstr "jam"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.view_event_track_form
msgid "minutes after track starts"
msgstr "menit"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.registration_complete
msgid "register to your favorites talks now."
msgstr "daftar ke talk favorit Anda sekarang."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
#: model_terms:ir.ui.view,arch_db:website_event_track.tracks_main
msgid "search."
msgstr "cari."

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts in"
msgstr "dimulai di"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.event_track_content
msgid "starts on"
msgstr "dimulai pada"

#. module: website_event_track
#: model_terms:ir.ui.view,arch_db:website_event_track.agenda_main
msgid "tracks"
msgstr "track"
