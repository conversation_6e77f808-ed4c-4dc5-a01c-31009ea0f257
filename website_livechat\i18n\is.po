# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_livechat
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:06+0000\n"
"PO-Revision-Date: 2018-08-24 09:34+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Live Chat</span>\n"
"                        <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "<span>Livechat Channel</span>"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Channel"
msgstr "farvegur"

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,help:website_livechat.field_im_livechat_channel__website_description
msgid "Description of the channel displayed on the website page"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Happy face"
msgstr ""

#. module: website_livechat
#: model:website.menu,name:website_livechat.menu_livechat
msgid "Live Support"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.res_config_settings_view_form
msgid "Live chat channel of your website"
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_im_livechat_channel
msgid "Livechat Channel"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "Livechat Support Channels"
msgstr ""

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:30
#, python-format
msgid "Name"
msgstr "Nafn"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Neutral face"
msgstr ""

#. module: website_livechat
#. openerp-web
#: code:addons/website_livechat/static/src/js/website_livechat.editor.js:29
#, python-format
msgid "New Channel"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Sad face"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "Statistics"
msgstr "Statistics"

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "The Team"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_list_page
msgid "There is no public livechat channel to show."
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "There is no rating for this channel for now."
msgstr ""

#. module: website_livechat
#: model:ir.model,name:website_livechat.model_website
msgid "Website"
msgstr "Vefsíða"

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_res_config_settings__channel_id
msgid "Website Live Channel"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_website__channel_id
msgid "Website Live Chat Channel"
msgstr ""

#. module: website_livechat
#: model:ir.model.fields,field_description:website_livechat.field_im_livechat_channel__website_description
msgid "Website description"
msgstr ""

#. module: website_livechat
#: model_terms:ir.ui.view,arch_db:website_livechat.channel_page
msgid "last feedbacks"
msgstr ""
