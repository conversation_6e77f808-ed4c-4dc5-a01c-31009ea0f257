# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pad
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: pad
#: model:ir.model,name:pad.model_res_company
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: pad
#: model:ir.model,name:pad.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company__pad_key
#: model:ir.model.fields,help:pad.field_res_config_settings__pad_key
msgid "Etherpad lite api key."
msgstr ""

#. module: pad
#: model:ir.model.fields,help:pad.field_res_company__pad_server
#: model:ir.model.fields,help:pad.field_res_config_settings__pad_server
msgid "Etherpad lite server. Example: beta.primarypad.com"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common__id
msgid "ID"
msgstr "ID"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_pad_common____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:147
#, python-format
msgid "Loading"
msgstr "ដំណើរការ"

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company__pad_key
msgid "Pad Api Key"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings__pad_key
msgid "Pad Api Key *"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_company__pad_server
msgid "Pad Server"
msgstr ""

#. module: pad
#: model:ir.model.fields,field_description:pad.field_res_config_settings__pad_server
msgid "Pad Server *"
msgstr ""

#. module: pad
#: code:addons/pad/models/pad.py:57
#, python-format
msgid ""
"Pad creation failed, either there is a problem with your pad server URL or "
"with your connection."
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/xml/pad.xml:6
#, python-format
msgid ""
"Please enter your Etherpad credentials through the menu 'Settings > General "
"Settings'."
msgstr ""

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "Server"
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/xml/pad.xml:14
#, python-format
msgid "Switch pad"
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:166
#, python-format
msgid "This pad will be initialized on first edit"
msgstr ""

#. module: pad
#. openerp-web
#: code:addons/pad/static/src/js/pad.js:160
#, python-format
msgid "Unable to load pad"
msgstr ""

#. module: pad
#: model_terms:ir.ui.view,arch_db:pad.res_config_settings_view_form
msgid "e.g. beta.primarypad.com"
msgstr ""

#. module: pad
#: model:ir.model,name:pad.model_pad_common
msgid "pad.common"
msgstr ""
