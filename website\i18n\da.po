# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# j<PERSON><PERSON> jensen <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-10 06:07+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "Tilføj billeder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr "\" alarmér med en"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"URL fra\" kan ikke være tom."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "\"URL til\" kan ikke være tom."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "\"URL til\" kan ikke indeholde parametre %s som ikke anvendes i \"URL fra\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"URL til\" er ugyldig: %s"

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "\"URL til\" skal indeholde parametre %s anvendt i \"URL fra\"."

#. module: website
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "\"URL til\" skal starte med skråstreg. "

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# Besøgte sider"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "# Besøg"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10,50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12,00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15,50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7,50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9,00"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "%s (id:%s)"
msgstr "%s (id:%s)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;/body&amp;gt;"
msgstr "&amp;lt;/body&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt;"
msgstr "&amp;lt;head&amp;gt;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&gt;"
msgstr "&gt;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "' matchede ikke nogen sider."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "' matchede ikke med noget."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"' matchede ikke med noget.\n"
"            Resultater er vist for '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "' for at linke til et anker."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr "' for at søge på en side."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' er ikke en gyldig dato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' er ikke en valid dato/klokkeslæt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. Viser resultater for '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "(could be used in"
msgstr "(kan anvendes i"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"(see e.g. Opinion\n"
"            04/2012 on Cookie Consent Exemption by the EU Art.29 WP)."
msgstr ""
"(Se f.eks. Udtalelse\n"
"           04/2012 om Fritagelse fra Cookie Samtykke under EU Art.29 WP)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ Felt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_2
msgid "+1 (650) 555-0111"
msgstr "+1 (650) 555-0111"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid ", .s_searchbar_input"
msgstr ", .s_searchbar_input"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid ", .s_website_form"
msgstr ", .s_website_form"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", forfatter:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                               Hjemmesiden vil stadig virke hvis du afviser eller kasserer disse cookies."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""
".\n"
"            Ændring af navnet vil ødelægge disse kald."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr ".. og skift tidslinje indholdet så de passer til dine behov."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/kontaktos"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr "2 <span class=\"sr-only\">(nuværende)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "24x7 gratis support"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 Flyttet permanent"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 Flyttet midlertidigt"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 Omdiriger / Omskriv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 trin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 m"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 Ikke fundet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 km"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 m"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "50.000+ virksomheder bruger Odoo til at vokse deres virksomhed."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 km"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% Udvikling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>50.000+ virksomheder</b> bruger Odoo til at udvide deres forretning."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>Tilføj</b> det valgte billede."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "<b>Klik rediger</b> for at starte med at designe din hjemmeside."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>Klik på en snippet</b> for at tilgå dens indstillinger menu."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>Klik på en tekst</b> for at redigere den."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>Klik</b> på denne kolonne for at tilgå dens indstillinger."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>Klik</b> på denne overskrift for at konfigurere den. "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>Klik</b> på denne valgmulighed for at ændre blokkens %s."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr ""
"<b>Tilpas</b> enhver blok via denne menu. Prøv at ændre baggrundsfarven på "
"denne blok."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr ""
"<b>Tilpas</b> enhver blok via denne menu. Prøv at ændre baggrundsbilledet "
"for denne blok."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>Designet</b> <br/>til Virksomheder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>Designet</b> til virksomheder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr "<b>Dobbelt klik på et ikon</b> for at skifte den ud med en du vælger."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr ""
"<b>Dobbelt klik på et billede</b> for at skifte den ud med et du vælger."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>Min Virksomhed</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>Vælg</b> en %s."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>Vælg</b> en Farvepalet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>Træk</b> denne knap for at ændre %s polstringen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>Træk</b> denne knap for at ændre kolonne størrelsen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\">God skrivning er "
"simpel, men ikke simplistisk.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<font style=\"font-size: 14px;\">Created in 2021, the company is young and "
"dynamic. Discover the composition of the team and their skills.</font>"
msgstr ""
"<font style=\"font-size: 14px;\">Grundlagt i 2021, virksomheden er ung og "
"dynamisk. Se kompositionen af holdet og deres færdigheder.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, "
"255);\">Rediger denne titel</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "<font style=\"font-size: 62px; font-weight: bold;\">Catchy Headline</font>"
msgstr ""
"<font style=\"font-size: 62px; font-weight: bold;\">Fængende "
"Overskrift</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr "<font style=\"font-size: 62px;\">En fængende overskrift</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\">Sell Online. Easily.</font>"
msgstr "<font style=\"font-size: 62px;\">Sælg Online. Nemt.</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr "<font style=\"font-size: 62px;\">Dias titel</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "<font style=\"font-size: 62px;\">Win $20</font>"
msgstr "<font style=\"font-size: 62px;\">Vind $20</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr "<font style=\"font-size: 62px;\">Din sides titel</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 dage siden</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"mr-2\"/><span><EMAIL></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker mr-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                               Lav et Google Projekt og få en nøgle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Aktivér fakturering på dit Google Projekt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Hvordan får jeg mit klient ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Measurement ID"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Hvordan jeg får mit Målings ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Go to View\"/>"
msgstr "<i class=\"fa fa-eye ml-2 text-muted\" title=\"Gå til Visning\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Show Arch Diff\"/>"
msgstr "<i class=\"fa fa-files-o ml-2 text-muted\" title=\"Vis Arch Diff\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> Cirkler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> Hjerter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh mr-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh mr-1\"/> Erstat Ikon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> Firkanter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> Stjerner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> Tomler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-danger\"/>\n"
"                            <span>Offline</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Connected</span>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\"/>\n"
"                            <span>Forbundet</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/>Rediger indholdet under denne linje, for at "
"tilpasse den normale <strong>Side ikke fundet</strong> side."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Et kodeord er påkrævet for at tilgå denne side.</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Forkert kodeord</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw mr-2\"/><span "
"class=\"o_force_ltr\">Chaussée de Namur 40</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw mr-2\"/><span class=\"o_force_ltr\">+ 32 81 81 "
"37 00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_add_language
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Add a language...</span>"
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"        <span>Tilføj et sprog...</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large mr-2\"/> WEBSITE"
msgstr "<i class=\"fa fa-th-large mr-2\"/> HJEMMESIDE"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Events</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw mr-2\"/>\n"
"                            <b>Begivenheder</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>About us</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw mr-2\"/>\n"
"                            <b>Om os</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw mr-2\"/>\n"
"                            <b>Partnere</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Services</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw mr-2\"/>\n"
"                            <b>Ydelser</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw mr-2\"/>\n"
"                            <b>Hjælpe center</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw mr-2\"/>\n"
"                            <b>Vejledninger</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw mr-2\"/>\n"
"                            <b>Vores blog</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw mr-2\"/>\n"
"                            <b>Kunder</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Products</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw mr-2\"/>\n"
"                            <b>Produkter</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Contact us"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments mr-2\"/> Kontakt os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Free returns"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-cube mr-2\"/> Gratis "
"returneringer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Pickup"
" in store"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket mr-2\"/> Afhent"
" i butik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Express delivery"
msgstr ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck mr-2\"/> Ekspres levering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"
msgstr "<i title=\"Is the page SEO optimized?\" class=\"fa fa-search\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"
msgstr ""
"<i title=\"Is the page included in the main menu?\" class=\"fa fa-thumb-"
"tack\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"
msgstr "<i title=\"Is the page indexed by search engines?\" class=\"fa fa-globe\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"
msgstr "<i title=\"Is the page published?\" class=\"fa fa-eye\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>Øjeblikkeligt setup, tilfreds eller refunderet.</i>"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files : </p>"
msgstr "<p>Vedhæftede filer: </p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small class=\"s_share_title d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title d-none\"><b>Følg os</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
msgid "<small class=\"s_share_title text-muted d-none\"><b>Follow us</b></small>"
msgstr "<small class=\"s_share_title text-muted d-none\"><b>Følg os</b></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                        </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                            <i class=\"fa fa-info\"/>: det er nok at skrive nogle af de første tegn efter 'google', vi klarer resten.\n"
"                                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form felt hjælpe "
"tekst</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Vi vil aldrig dele "
"din mail med nogen anden.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ måned</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "<small>TABS</small>"
msgstr "<small>FANER</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_magazine_oe_structure_header_magazine_1
msgid "<small>We help you grow your business</small>"
msgstr "<small>Vi hjælper dig med at vokse din virksomhed</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2015</b></span>"
msgstr "<span class=\"bg-white\"><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2018</b></span>"
msgstr "<span class=\"bg-white\"><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span class=\"bg-white\"><b>2019</b></span>"
msgstr "<span class=\"bg-white\"><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"sr-only\">Næste</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"sr-only\">Forrige</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Discover our new products</font></b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2\">\n"
"                            <b><font style=\"font-size:14px;\">Se vores nye produkter</font></b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Din besked er blevet <b>sendt</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Previous</span>"
msgstr ""
"<span class=\"fa fa-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Forrige</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Next</span>"
msgstr ""
"<span class=\"fa fa-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"sr-only\">Næste</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil mr-2\"/>Edit"
msgstr "<span class=\"fa fa-pencil mr-2\"/>Redigér"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus mr-2\"/>New"
msgstr "<span class=\"fa fa-plus mr-2\"/>Ny"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"
msgstr "<span class=\"fa fa-sort fa-lg\" role=\"img\" aria-label=\"Sort\" title=\"Sort\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "<span class=\"fa-2x\">×</span>"
msgstr "<span class=\"fa-2x\">×</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"
msgstr ""
"<span class=\"o_add_language list-inline-item\" "
"groups=\"website.group_website_publisher\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name mr-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr ""
"<span class=\"o_footer_copyright_name mr-2\">Ophavsret &amp;copy; "
"Virksomhedsnavn</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Robots.txt</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Værdier angivet her er hjemmeside-specifikke.\" groups=\"website.group_multi_website\"/> "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Sitemap</span>\n"
"                                    <span class=\"fa fa-lg fa-globe\" title=\"Værdier angivet her er hjemmeside-specifikke.\" groups=\"website.group_multi_website\"/> "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge badge-secondary o_animable\" data-"
"name=\"Emblem\"> <i class=\"fa fa-1x fa-fw fa-folder o_not-"
"animable\"/>Kategori </span> "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Nomen NESCIO</b> • Direktør for "
"MinVirksomhed</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Nomen NESCIO</b> • Direktør for "
"MinVirksomhed</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Nomen NESCIO</b> • Direktør for "
"MinVirksomhed</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">,00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">,00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span><br/>"
msgstr "<span class=\"s_number display-4\">12</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span><br/>"
msgstr "<span class=\"s_number display-4\">37</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span><br/>"
msgstr "<span class=\"s_number display-4\">45</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span><br/>"
msgstr "<span class=\"s_number display-4\">8</span><br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">80% Udvikling</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">Email Til</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Telefonnummer</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Emne</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Din Virksomhed</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Din Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Dit Navn</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "<span class=\"s_website_form_label_content\">Your Question</span>"
msgstr "<span class=\"s_website_form_label_content\">Dit Spørgsmål</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr "<span class=\"sr-only\">Tænd/sluk dropdown</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"
msgstr ""
"<span title=\"Mobile preview\" role=\"img\" aria-label=\"Mobile preview\" "
"class=\"fa fa-mobile\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_publisher
msgid ""
"<span/>\n"
"                    <span class=\"css_publish\">Unpublished</span>\n"
"                    <span class=\"css_unpublish\">Published</span>"
msgstr ""
"<span/>\n"
"                    <span class=\"css_publish\">Ikke udgivet</span>\n"
"                    <span class=\"css_unpublish\">Udgivet</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>Tema</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""
"En CDN hjælper dig med at servere din hjemmesides indhold med højere "
"tilgængelighed og ydelse til enhver besøgende, uanset hvor de er."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "En Diagram Titel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "En sektion undertekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""
"Et kort er en fleksible og extensibel indholds container. Den inkludere "
"indstillinger for side-hoved og -fod, en lang række indhold, kontekstuelle "
"baggrunds farver, og kraftfulde fremvisnings muligheder."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"En kode server handling kan udføres fra hjemmesiden, ved hjælp af en "
"dedikeret controller. Adressen er <base>/website/action/<website_path>. "
"Angiv dette felt som True for at give brugerne mulighed for at køre denne "
"handling. Hvis den er sat til False, kan handlingen ikke køres gennem "
"hjemmesiden."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "En farveblok"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "En fantastisk titel"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "En liste af komma-separerede felt navne"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "Et kort og en liste over dine butikker"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr ""
"Et nyt besøg bliver overvejet hvis den sidste forbindelse var mere end 8 "
"timer siden."

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_partner_uniq
msgid "A partner is linked to only one visitor."
msgstr "En partner er forbundet til kun én besøgende."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "En kort beskrivelse af denne fantastiske funktion."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr "En lille forklaring til denne formidable <br/>funktion, i klare ord."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr ""
"En tidslinje er en grafisk repræsentation hvorpå vigtige arrangementer er "
"markeret."

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr ""
"En besøgende anses som forbundet hvis deres sidste side-view var indenfor de"
" sidste 5 minutter."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "API Key"
msgstr "API nøgle"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
msgid "About Us"
msgstr "Om Os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "Om os"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Access Error"
msgstr "Adgangsfejl"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "Adgangstoken"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "Adgang til denne side"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "Adgangs token bør være unik."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "Tilbehør"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "Konto- & salgsstyring"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "Handling"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Activate anyway"
msgstr "Aktiver alligevel"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model:ir.model.fields,field_description:website.field_website_visitor__active
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Active"
msgstr "Aktiv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Tilpas disse tre kolonner til at passe med dit design behov. For at "
"duplikere, slette eller flytte kolonner, vælg den kolonne og brug top "
"ikonerne til at udføre den pågældende handling."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "Tilføj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "Tilføj funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "Tilføj Genstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "Tilføj Medie"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "Tilføj mega menu genstand"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "Tilføj menu genstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "Tilføj produkt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "Tilføj Række"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "Tilføj Serie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "Tilføj Dias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "Tilføj Fane"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "Tilføj År"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "Tilføj en Google skrifttype"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "Tilføj en billedtekst for at forbedre betydningen af dette billede."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "Tilføj en beskrivelse her"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "Tilføj et godt slogan."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "Tilføj en menu beskrivelse."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Add a menu item"
msgstr "Tilføj en menu genstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "tilføj et nyt felt efter denne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "Tilføj et nyt felt til slut"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr "Tilføj funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr "Tilføj links til sociale medier på din hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add new %s"
msgstr "Tilføj ny %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "Tilføj til kurv"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Add to menu"
msgstr "Tilføj til menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Adding a font requires a reload of the page. This will save all your "
"changes."
msgstr ""
"Tilføjelse af en skrifttype kræver at siden genopfriskes. Dette vil gemme "
"alle dine ændringer."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "Adresse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing"
msgstr "Reklame &amp; Marketing"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "Efter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Alert"
msgstr "Advarsel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "Justér Bund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "Justér Midt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "Justér Top"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "Justering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline er en af de ikoniske individer i livet, som kan sige, at hun elsker "
"hvad hun laver. Hun er mentor for 100+ in-house udviklere, og sørger for "
"communitiet af over tusinde udviklere."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "Alle"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "Hele hjemmesidens rute"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "All Websites"
msgstr "Alle Hjemmesider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "Al information du har behov for"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "Alle sider"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "Alle resultater"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "Alle disse ikoner er fuldstændig gratis til kommerciel brug."

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr ""
"Tillad at specificere hvorvidt én af hjemmesidens sider er sporbar eller ej"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "Tilladt at bruge i formularer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "Allerede installeret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "Skift billedtekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "Skift tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "Skift tekstbillede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "Skift tekst billede tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Selvom denne hjemmeside kan være forbundet til andre hjemmesider, "
"tilkendegiver vi ikke - direkte eller indirekte - nogen anerkendelse, "
"association, sponsorering, godkendelse eller forbindelse til nogen forbundet"
" hjemmeside, med mindre det specifikt er angivet."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underlined"
msgstr "Altid Understreget"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "Altid Synlig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always visible"
msgstr "Altid synlig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "Fantastiske sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "En adresse skal være angivet for at et kort kan indlejres"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "Der er opstået en fejl, formularen er ikke blevet sendt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "Der skete en fejl under gengivelse af skabelonen"

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics"
msgstr "Analyse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "Analytics cookies og privatlivs informationer."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "Forankring kopieret til udklipsholder<br>Link: %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "Anker navn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "Og en god undertitel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animate"
msgstr "Animér"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Animate text"
msgstr "Animér tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "Animeret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Delay"
msgstr "Animations forsinkelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Duration"
msgstr "Animations varighed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation Launch"
msgstr "Animation Afvikling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "En til farve blok"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "En anden funktion"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "Tilføj"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Apps"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "Apps URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "Arch"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "Arch blob"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "Arch filnavn"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "Arch Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Archived"
msgstr "Arkiveret"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr "Er nyoprettede brugerkonto hjemmeside specifikke"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr "Er du sikker på at du vil slette denne side ?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "Pile"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr "Som lovet, vil vi tilbyde 4 gratis billetter til vores næste topmøde."

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "Aktiv"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "Hjælpeaktiver "

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "Aktiver via en kopi af mig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "Til Sidst"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Vedhæftning"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "Vedhæftning via en kopi af mig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"Godkend brugere, beskyt brugerdata og lad hjemmesiden levere de tjenester, som brugerne forventer,\n"
"                                                såsom vedligeholdelse af indholdet af deres indkøbskurv eller tilladelse til at lægge filer op."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "Forfatter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "Auto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr ""
"Åbner automatisk et pop-up vindue hvis brugeren bliver på siden længere end "
"den specificerede tidsperiode."

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "Auto-diriger sprog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr "Autostørrelse"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "Synlig på hjemmesiden"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "BTS Base Colors"
msgstr "BTS Grund Farver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "Kulisse"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "Baggrund"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "Baggrundsform"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Badge"
msgstr "Badge"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "Poser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "Bar Horisontal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "Bar Vertikal"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "Basis"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "Basis view arkitektur"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "Grundlag for visning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "Basis salg og marketing for op til 2 brugere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "Smukke snippets"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "Oksekøds Carpaccio"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "Før"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Begynder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "Under hinanden"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Big"
msgstr "Stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "Store Ikoner Undertekster"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "Blacklist dette felt i webformularer"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "Blacklist i webformularer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "Blazere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "Blokér"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Blockquote"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "Blog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Blog Post"
msgstr "Blogindlæg"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "Blogging og postering af relevant indhold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "Bøger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "Bootstrap-baserede skabeloner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "Grænse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "Kant Bund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "Kant Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "Grænse Radius"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "Kant Bredde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "Afgrænset"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Bottom"
msgstr "Bund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "Bund til Top"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In"
msgstr "Hop Ind"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Down"
msgstr "Hop Ind-Ned"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Left"
msgstr "Hop Ind-Venstre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce In-Right"
msgstr "Hop Ind-Højre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "Boxet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "Kasser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Breadcrumb"
msgstr "Krummer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "Byg min hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "Byggeklods system"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "Bygger din %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Building your website..."
msgstr "Opbygger din hjemmeside..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "Bundt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "Knap"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "Knap Position"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Buttons"
msgstr "Knapper"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "By clicking on this banner, you give us permission to collect data."
msgstr ""
"Ved at klippe på dette banner, giver du os tilladelse til at indsamle data."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "CDN Basis URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "CDN Filtre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_key_expr
msgid "Cache Key Expr"
msgstr "Cache Nøgle Udtryk"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__cache_time
msgid "Cache Time"
msgstr "Cache Tid"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call to Action"
msgstr "Kald til Handling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "Kontakt os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "Call-to-action"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "Kamera"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "Kan udgives "

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/menu/new_content.js:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "Annullér"

#. module: website
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr ""
"Kan ikke deaktivere et sprog der i øjeblikket anvendes på en hjemmeside."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "Kan ikke indlæse google map."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Card"
msgstr "Kort"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr "Kort krop"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr "Kort fod"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr "Kort hoved"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "Kortstil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "Kort"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "Karriere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "Casestudier"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "Kategori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "Cookie Kategori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "Midten"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "Centréret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered Logo"
msgstr "Centreret Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Skift ikoner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"Ændr temaet i et par klik, og gennemgå Odoos katalog af\n"
"                             temaer som er klar til brug i vores app butik."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr ""
"Ændring af farvepaletten vil nulstille alle dine farvetilpasninger, er du "
"sikker på du vil fortsætte?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr ""
"For at ændre temaet skal editoren lukkes. Dette vil gemme alle dine "
"ændringer. Er du sikkert på du vil fortsætte? Vær opmærksom på, at ændring "
"af temaet også vil nulstille alle dine farvetilpasninger."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#, python-format
msgid "Chart"
msgstr "Oversigt"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "Chat med besøgende for at forbedre trækkraften"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "Betal nu og få $20 trukket fra din første ordre."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "Tjek din konfiguration."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "Tjek din forbindelse og prøv igen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "Afkrydsningsfelt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "Oste løgringe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "Kokkens Friske Dagens Supper"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "Under-felt"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Under-menuer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "Underkonti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "Vælg"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""
"Vælg et farverigt billede og skriv en inspirerende paragraf om den.<br/> Den"
" behøver ikke være særlig lang, men den bør forstærke dit billede."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "Vælg et anker navn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "Vælg et andet tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "Vælg din favorit"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Circle"
msgstr "Cirkel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "Klassisk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "Rens"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "Smart slogan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"Klik og ændr indhold direkte fra front-end: ingen kompleks back-end\n"
"                            at håndtere."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "Klik her for at gå tilbage til blok fanen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Click on"
msgstr "Klik på"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "Klik på ikonet for at tilpasse den <br/>til dine formål."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "Klik for at vælge flere billeder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "Klik for at vælge"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "Kunde ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr "Klienthemmelighed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr "Klon denne side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Luk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "Luk Knap Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "Tøj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
msgid "Code"
msgstr "Kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "Kode Indsprøjtning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "Sammenfald Ikon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color"
msgstr "Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"Farveblokke er en enkel og effektiv måde at <b>præsentere og fremhæve dit "
"indhold</b>. Vælg et billede eller en farve til baggrunden. Du kan endda "
"ændre størrelse og duplikere blokkene, for at oprette dit eget layout. "
"Tilføj billeder eller ikoner for at tilpasse blokkene."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "Farvefilter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Colors"
msgstr "Farver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Columns"
msgstr "Kolonner"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr ""
"Komma separeret liste over hjemmeside type/formål, for hvilke denne funktion"
" bør være forudvalgt"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "Virksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "Komplet CRM for teams af alle størrelser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "Komponenter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "Computers"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "Computere &amp; Enheder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "Betinget"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "Konfigurator Færdig"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "Konfigurér sociale netværk"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Connect Google Analytics"
msgstr "Forbind Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Opret forbindelse til os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "Forbundet"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "Konsol Google Søgning"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "Kontakt"

#. module: website
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_banner
#, python-format
msgid "Contact Us"
msgstr "Kontakt os"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "Kontakt Besøgende"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "Kontakt os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"Kontakt os vedrørende hvad som helst relateret til vores virksomhed eller ydelser.<br/>\n"
"Vi vil gøre vores bedste for at vende tilbage til dig så hurtigt som muligt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "Kontakt os til enhver tid"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "Kontakt os for ethvert problem eller spørgsmål"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "Adressebog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "Indeholder"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "Indhold"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Content Delivery Network (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "Indhold Bredde"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "Indhold til oversættelse"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Continue"
msgstr "Fortsæt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"
msgstr ""
"Fortsæt med at læse <i class=\"fa fa-long-arrow-right align-middle ml-1\"/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "Cookiepolitik"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "Cookie bars may significantly impair the experience"
msgstr "Cookie barre kan forringe oplevelsen betydeligt"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "Cookie Bar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"Cookies er små bidder af tekst som sendes af vores servere til din computer eller enhed når du tilgår vores tjenester.\n"
"                            De gemmes i din browser, og sendes senere tilbage til vores servere, så vi kan tilbyde kontekstuelt indhold.\n"
"                            Uden cookies ville det være en langt mere frustrerende oplevelse at gøre brug af nettet.\n"
"                            Vi bruger dem til at støtte dine aktiviteter på vores hjemmeside. For eksempel din session (så du ikke skal logge ind igen) eller din indkøbskurv.\n"
"                              <br/>\n"
"                              Cookies anvendes også til at hjælpe os med at forstå dine præferencer, baseret på tidligere eller nuværende aktivitet på vores hjemmeside (siderne du har\n"
"                              besøgt), samt dit sprog og land, hvilket gør os i stand til at tilbyde dig en bedre service.\n"
"                              Vi bruger også cookies til at hjælpe os med at kompilere samlede data om trafik og interaktioner på hjemmesiden, så vi kan tilbyde\n"
"                              en bedre oplevelse samt værktøjer i fremtiden."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "Ophavsret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "Nedtælling slutter om"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "Nedtælling er ovre - Fyrværkeri"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "Land"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "Landeflag"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "Landegrupper"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Course"
msgstr "Kursus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Cover"
msgstr "Dækker"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "Coverbillede"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "Cover egenskaber"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "Cover Egenskaber Hjemmeside Mixin"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "Opret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Opret side"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "Opret en"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "Opret et Google projekt og få en nøgle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr "Opret en ny hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "Opret et link for at pege til denne sektion"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "Opret ny"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"Opret din side fra bunden ved at trække og slippe forhåndslavede,\n"
"                            byggeklodser der kan tilpasses fuldt ud."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"Grundlagt i 2021, denne virksomhed er ung og dynamisk. Se kompositionen af "
"vores hold og deres færdigheder."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "Tilpasset"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "Tilpasset <head> kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "Tilpasset Kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "Tilpasset Nøgle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "Tilpasset URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "Tilpasset slutning af <body> kode"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom end of body code"
msgstr "Tilpasset slutning af body kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "Tilpasset felt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom head code"
msgstr "Tilpasset head kode"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "Konto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "Kunder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "Tilpasningsværktøj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr "Tilpas"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "Tilpas Show"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "D - H - M"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "D - H - M - S"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "TRÆK BYGGEBLOKKE HERTIL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "Fare"

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Dashboard"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "Streget"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Data"
msgstr "Data"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "Data Kant"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "Data Farve"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "Datasæt Kant"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "Datasæt Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "Dato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "Dato &amp; tid"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Days"
msgstr "Dage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "Decimalnummer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Standard"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Standard adgangsrettigheder"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "Standardsprog"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "Standard hovedmenu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "Standard Omvendt"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "Standard del på sociale medie billede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "Standardværdi"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Standardsprog"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Standard sprogkode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "Forsinkelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "Slet blokke"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "Fjern menu genstand"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Delete Page"
msgstr "Slet siden"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Slet ovenstående billede og erstat det med et billede der illustrerer din "
"besked. Klik på billedet for at ændre dens <em>afrundede kanter</em>-stil."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "Slet denne skrifttype"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr "Slet denne side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr ""
"Du skal genindlæse siden for at slette en skrifttype. Dette vil gemme alle "
"dine ændringer og genindlæse siden. Er du sikker på du vil fortsætte?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "Leveringer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "Afdelinger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Dependencies"
msgstr "Afhængigheder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Describe your field here."
msgstr "Beskriv dit felt her."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "Beskrivelse"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "Beskrivelse af dine ydelsestilbud"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "Beskrivelser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "Beskrivelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "Design"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "Design funktioner"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "Designet til at drive konvertering"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "Skrivebord"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "Stationære computere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "Detalje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "Detaljer:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "Opdag"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Direction"
msgstr "Retning"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "Direktiv"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
#: model:ir.cron,cron_name:website.website_disable_unused_snippets_assets
#: model:ir.cron,name:website.website_disable_unused_snippets_assets
msgid "Disable unused snippets assets"
msgstr "Deaktiver ubrugte snippet aktiver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Disabled"
msgstr "Deaktiveret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "Forsvindende"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "Forsvinder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Discard"
msgstr "Kassér"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Discard & Edit in backend"
msgstr "Kassér & Rediger i backend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover"
msgstr "Opdag"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "Opdag all funktionerne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "Opdag mere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "Se vores kultur og vores værdier"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "Se vores juridiske meddelelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "Se vores relationer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "Se vores hold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "Diskrete"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Diskussionsgruppe"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Disk"
msgstr "Disk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Display"
msgstr "Vis"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "Vis på linje"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website."
msgstr "Vis en cookie bar der kan tilpasses på din hjemmeside."

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "Vis badges"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "Vis biografi"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "Vis hjemmeside beskrivelsen"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "Vis dette logo på hjemmesiden."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "Vis denne hjemmeside når brugere besøger dette domæne"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Do not activate"
msgstr "Aktiver ej"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr ""
"Har du brug for specifik information? Vores specialister vil med glæde "
"hjælpe dig."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Do you want to edit the company data ?"
msgstr "Vil du redigere virksomhedsdataene?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "Vil du installere \"%s\" app'en?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "Dokumentation"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "Indeholder ikke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Domæne"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr "Husk at opdatere alle links, som henviser til denne side."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/theme_preview_kanban.js:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "Rolig, du kan skifte senere."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "Donation"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "Donation Knap"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "Prikker"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "Prikket"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "Dobbelt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "Dobbelt-klik på et ikon for at udskifte det med et efter eget valg."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "Berliner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "Træk <b>%s</b> byggeblokken og slip den neders på siden"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "Træk mod højre for at få en under-menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "Kjoler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr "Dropdown"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "Forfaldsdato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Duplikér"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Duplicate Page"
msgstr "Dupliker Side"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "Dupliker blokke <br/>for at tilføje flere trin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "Duplikér blokke og kolonner for at tilføre yderligere funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "Dynamisk Indhold"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "F.eks. https://www.mitdomæne.dk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr ""
"Design nemt dine egne Odoo skabeloner, takket være ren HTML\n"
"                              struktur og bootstrap CSS."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "Rediger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Redigér menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "Rediger menu genstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "Rediger Besked"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "Rediger stile"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr "Redigér top-menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr "Redigér kode i backend"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "Redigér indlejret kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr "Redigér backend"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Edit my Analytics Client ID"
msgstr "Rediger mit Analytics klient ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "Rediger robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "Rediger video"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "Redaktør og Designer"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "Enten action_server_id eller filter_id skal være angivet."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "Elektronik"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "E-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr "E-mailadresse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "E-mail support"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "Tomt felt navn i %r"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "Aktiver fakturering på dit Google Projekt"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "Aktivér formularbygge funktionen til denne model."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "Angiv en API Nøgle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr ""
"Angiv koden der vil blive tilføjet før </body> på samtlige sider på din "
"hjemmeside."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr ""
"Angiv koden der vil blive tilføjet til samtlige sider på din hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr ""
"Angiv koden der vil blive tilføjet til <head> på samtlige sider af din "
"hjemmeside."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr "Skriv email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "Ens Bredder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "Fejl"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "Essentielle olier"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Event"
msgstr "Arrangement"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "Begivenhed sidehoved"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "Arrangementer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "Hver Gang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "Alting"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "Eksempler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "Ekspert"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "Forklar hvordan du beskytter privatlivet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""
"Forklar fordelende du tilbyder. <br/>Lad være med at skrive om produkter "
"eller tjenester her; skriv om løsninger."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "Udforsk"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_key_expr
msgid ""
"Expression (tuple) to evaluate the cached key. \n"
"E.g.: \"(request.params.get(\"currency\"), )\""
msgstr ""
"Udtryk (tuple) til evaluering af den cachelagret nøgle.\n"
"F.eks.: \"(request.params.get(\"currency\"), )\""

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "Vis udvidelse"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "Eksternt ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "Ekstra Stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "Ekstra link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "Ekstra stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "Ekstra Lille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "F.A.Q."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Facebook konto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "Falme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In"
msgstr "Falm Ind"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Down"
msgstr "Falm Ind-Ned"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Left"
msgstr "Falm Ind-Venstre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Right"
msgstr "Falm Ind-Højre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade In-Up"
msgstr "Falm Ind-Op"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "Falm Ud"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "Kunne ikke installere \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "Farm Friendly Chicken Supreme"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "Funktion nr. 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "Funktion nr. 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "Funktions titel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "Funktion nr. 2"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "Funktion URL"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "Funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "Indhentede elementer"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "Felt Navne"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "Felt for brugerdefinerede formulardata"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "Felter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "Fil sendt"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"Fil fra hvor view oprinder.\n"
"Nyttig til at (hård) nulstille ødelægte views, eller til at læse arch fra filer i dev-xml tilstand."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "Filet Mignon 225gr"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "Udfyld"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "Udfyld og retfærdiggør"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Filter"
msgstr "Filter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "Filter intensitet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "Find en butik nær dig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr ""
"Find al information vedrørende vores leveringer, ekspres leveringer, og alt "
"du har brug for at vide omkring returnering af et produkt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr ""
"Find ud af hvordan vi var i stand til at hjælpe dem, og indføre løsninger "
"tilpasset deres behov."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "Find den perfekte løsning for dig"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "Første Forbindelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "Første funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "Første menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "Kun Første Gang"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "Første funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "Første liste af funktioner"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "Første side linket til dette view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "Tilpas indhold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "Tilpas tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "Fast"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "Anmeld"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "Flag og Tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "Blink"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Flat"
msgstr "Fast"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "Vend-Om-X"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "Vend-Om-Y"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "Kommatal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:ir.ui.view,arch_db:website.template_header_boxed_oe_structure_header_boxed_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_full_oe_structure_header_hamburger_full_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
#: model_terms:ir.ui.view,arch_db:website.template_header_vertical_oe_structure_header_vertical_1
msgid "Follow us"
msgstr "Følg os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr "Følg trafikken for din hjemmeside i Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "Skriftype"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "Font størrelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "Skrifttype familie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "Skrifttype størrelse"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "Sidefod Synlig"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "For session cookies, authentification and analytics,"
msgstr "For sessions cookies, autentificering, og analyse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr "Tving din bruger til at oprette en konto per hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "Formular"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr ""
"Formular label ved en handling. Ex: crm.lead kunne være 'Send en e-mail' og "
"project.issue kunne være 'Opret et problem'."

#. module: website
#: model:website.configurator.feature,name:website.feature_module_forum
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Forum"
msgstr "Forum"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"Grundlægger og chef visionær, Kaj er den drivende kraft bag Virksomhed. Han elsker\n"
"                                 at holde hans hænder beskæftiget ved at deltage i udvikling af software,\n"
"                                  markedsføring og kundeoplevelses strategier."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "Indrammet"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Gratis oprettelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Friends' Faces"
msgstr "Venners Ansigter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr ""
"Vi tilbyder en lang række arrangementer, fra seminarer til sammenholds "
"øvelser."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "Fuld"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "Fuld skærm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "Fuld Bredde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "Fuld skærm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "Fuld-bredde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "Møbler"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS &amp; navigation"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "Gaming"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "Få Leveret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "Få adgang til alle moduler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "Få adgang til alle moduler og funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "Kom i kontakt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "GitHub konto"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "Giv besøgende den information de har behov for"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "Briller"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Go To Page"
msgstr "Gå til side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Go to"
msgstr "Gå til"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "Gå til side administration"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr "Gå til hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "Gå til Tema fanen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""
"God tekst starter med at forstå hvordan dit produkt eller tjeneste hjælper "
"dine kunder. Simple ord kommunikerer bedre end store ord og pompøst sprog."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "Godt arbejde! Det er tid til at <b>Gemme</b> dit arbejde."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Analytics"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "Google Analytics instrumentbræt"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Google Analytics nøgle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""
"Start af Google Analytics mislykkedes. Måske er dette domæne ikke white-"
"listed i dit Google Analytics projekt for dette klient ID."

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr "Google kunde ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr "Google klient hemmelig"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "Google Font adresse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "Google Map"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Google Map API Key"
msgstr "Google Map API nøgle"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
msgid "Google Maps"
msgstr "Google Maps"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Google Maps API nøgle"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Google Søgning Konsol"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Google deprecated both its \"Universal Analytics\" and \"Google Sign-In\" "
"API. It means that only accounts and keys created before 2020 will be able "
"to integrate their Analytics dashboard in Odoo (or any other website). This "
"will be possible only up to mid 2023. After that, those services won't work "
"anymore, at all."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Google nøgle, eller Aktiver for at tilgå første besvarelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "Grå #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "Grå"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "Fantastisk værdi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Gode historier er <b>for alle</b> selv når de er skrevet <b>til kun én "
"person</b>. Hvis du forsøger at skrive til et bredt publikum, vil din "
"historie lyde falsk og være uinspirerende. Ingen vil være interesseret. "
"Skriv til én person. Hvis det er ægte for den enkelte, er det ægte for "
"resten."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Gode historier har <b>personlighed</b>. Overvej at fortælle en god historie "
"med personlighed. At skrive en historie med personlighed til potentielle "
"klienter vil hjælpe til med at lave styrke forbindelsen. Dette viser sig "
"gennem små finurligheder som ordvalg og fraser. Skriv fra dit synspunkt; "
"ikke fra en andens erfaringer."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Grid"
msgstr "Grid"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "Sortér efter"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Grupper"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H4 Card title"
msgstr "H4 Kort titel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "H5 Card subtitle"
msgstr "H5 Kort Under titel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS/JS Editor"
msgstr "HTML/CSS/JS Redaktør"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "Halv skærm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "Halv skærm"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Full"
msgstr "Burger Fuld"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger Type"
msgstr "Hamburger Type"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "Burger menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "Glædelig Odoo Jubilæum!"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "Har standard Social billede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "Overskrift"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "Sidehoved farve"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "Sidehoved overlay"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "Sidehoved Position"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "Sidehoved Synlig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr "Overskrifter 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr "Overskrifter 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr "Overskrifter 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr "Overskrifter 4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr "Overskrifter 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr "Overskrifter 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "Overskrift"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "Højde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "Højde (Rullet)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "Hjælpecenter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr ""
"Her er det visuelle der bruges, til at hjælpe dig med at oversætte "
"effektivt:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr ""
"Her er en oversigt over de cookies der kan blive gemt på din enhed når du "
"besøger vores hjemmeside:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "Skjult"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "Skjult for"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Hidden on mobile"
msgstr "Skjult på mobil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "Skjul"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "Skjul For"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Hide this page from search results"
msgstr "Skjul denne side fra søgeresultater"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "Høj"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr ""
"Tip: Hvordan du bruger Google Maps på din hjemmeside (Kontakt Os side og som"
" en snippet)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr ""
"Råd: Skriv '/' for at søge efter en eksisterende side, og '#' for at "
"forbinde til et anker."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "Startside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr "Hjem <span class=\"sr-only\">(nuværende)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "Hjemme audio"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Homepage"
msgstr "Startside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "Hættetrøjer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "Vandret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Hours"
msgstr "Timer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "Hvordan kan vi hjælpe?"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Client ID"
msgstr "Sådan får du dit kunde ID"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "How to get my Measurement ID"
msgstr "Hvordan jeg får mit Mål ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "Hybrid"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "Jeg er enig"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "Jeg vil have"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "Handlingens ID hvis defineret i en XML-fil"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID for visningen, som er defineret i xml fil"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Iap Sidekode"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "Ikon"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr ""
"Hvis Sandt, vil nye konti blive associeret med den nuværende hjemmeside"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "Hvis angivet, vil en hjemmesidemenu blive oprettet for funktionen."

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr ""
"Hvis angivet, vil menuen blive tilføjet som en anden-niveaus menu, et afkom "
"af \"Virksomhed\" menuen."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr ""
"Hvis angivet, erstattes hjemmeside logoet som standard social delings "
"billedet."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr ""
"Hvis denne fejl er forårsaget af en ændring i skabelonerne, har du "
"muligheden for at nulstille skabelonen til dens "
"<strong>fabriksindstillinger</strong>"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Hvis dette felt er tomt, gælder view'et for alle brugere. Ellers, gælder "
"view'et kun for brugere af disse grupper."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"Hvis view er nedarvet,\n"
"* hvis Sandt, forlænges view'et altid til ophavet\n"
"* hvis Falskt, forlænges view'et ikke på nuværende tidspunkt til ophavet, men det kan aktiveres"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Hvis du kassere de nuværende redigeringer, vil alle ændringer der ikke er "
"gemte gå tabt. Du kan annullere for at vende tilbage til redigerings "
"tilstand."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#, python-format
msgid "Image"
msgstr "Billede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "Billede Cover"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "Billede Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "Billege Størrelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "Tekst billede tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Images"
msgstr "Billeder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "Billeder Afstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "Billeder Undertekster"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr "I hovedmenu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "I mellemtiden inviterer vi dig til at besøge vores"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"        Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"I denne tilstand kan du kun oversætte tekster. For at ændre strukturen af siden, skal du redigere hovedsiden.\n"
"Enhver modifikation på hovedsiden anvendes automatisk på samtlige oversatte versioner."

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "Inkluder"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "Incorrect Client ID / Key"
msgstr "Ukorrekt klient ID / nøgle"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "Indekseret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "Indikatorer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Info"
msgstr "Information"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "Info og statistikker om din virksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "Information om"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "Arve"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Nedarvet visning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "Inline"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "Indre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "Indre indhold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "Input Justeret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "Input Type"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inputs"
msgstr "Input"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a badge snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a blockquote snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a card snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a chart snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a progress bar snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a rating snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a share snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert a text Highlight snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an alert snippet."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Insert an horizontal separator sippet."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"Indsæt tekst stile så som sidehoveder, fed, kursiv, lister, og skrifttyper med\n"
"                            en enkel WYSIWYG editor. Fleksibel og nem at bruge."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "Indsat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Instagram konto"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Install"
msgstr "Installer"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Installer sprog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr "Installér nyt sprog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Installerede programmer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "Installerede lokaliseringer / Kontodiagrammer"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "Installeret tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "Installerer \"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History"
msgstr "Interaktion Historik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "Intuitivt system"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "Ugyldig API nøgle. Den følgende fejl blev returneret fra Google:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Jytte, med hendes internationale erfaring, hjælper os til nemt at forstå tal og\n"
"forbedre dem. Hun er stålsat på at levere succes, og bringer med hendes professionelle skarphed bringer hun Virksomhed op på det næste niveau."

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "Er indekseret"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "Er installeret på nuværende hjemmeside"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "Er mega menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is Published"
msgstr "Er udgivet"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "Er synlig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "Er efter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "Er efter eller lig med"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "Er før"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "Er før eller lig med"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "Er mellem (inkluderet)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected ?"
msgstr "Er forbundet ?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "Er lig med"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "Er større end"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "Er større end eller lig med"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "Er mindre end"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "Er mindre end eller lig med"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "Er ikke mellem (eksluderet)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "Er ikke lig med"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is not set"
msgstr "Er ikke angivet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Is set"
msgstr "Er angivet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"It appears you are in debug=assets mode, all theme customization options "
"require a page reload in this mode."
msgstr ""
"Det ser ud til du er i fejlfinding=aktiver tilstand, alle tema tilpasnings "
"muligheder kræver en side genopfriskning i denne tilstand."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"Det ser ud til at din hjemmeside stadig anvender det gamle farve system fra\n"
"             Odoo 13,0 visse steder. Vi har forsikret at det stadig virker, men\n"
"             vi forslår, at du prøver det nye farve system, som stadig kan\n"
"             tilpasses."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "Det ser ud til at din fil bliver kaldet af"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "Genstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr "Genstand 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr "Genstand 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options
msgid "Items per row"
msgstr "Genstande per række"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Items per slide"
msgstr "Genstande per dias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "Jakke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "Jeans"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Job Offer"
msgstr "Jobtilbud"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "Slut dig til os og gør din virksomhed til et bedre sted."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "Hold tom for at bruge standardværdi"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Nøgle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "Tastaturer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keyword"
msgstr "Søgeord"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Keywords"
msgstr "Nøgleord"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Label"
msgstr "Tekst"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "Label til formular"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "Mærkat Bredde"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "Sprog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "Sprog Vælger"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "Sprog fra hjemmesiden når besøgende er blevet oprettet"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "Sprog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "Tilgængelige sprog på din hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "Bærbare"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "Stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "Seneste 7 dage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "Sidste aktion"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "Sidste forbindelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "Sidste Funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "Sidste menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment____last_update
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_theme_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_configurator_feature____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_rewrite____last_update
#: model:ir.model.fields,field_description:website.field_website_robots____last_update
#: model:ir.model.fields,field_description:website.field_website_route____last_update
#: model:ir.model.fields,field_description:website.field_website_snippet_filter____last_update
#: model:ir.model.fields,field_description:website.field_website_track____last_update
#: model:ir.model.fields,field_description:website.field_website_visitor____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Month"
msgstr "Sidste måned"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "Sidste side"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "Sidste besøgte side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Week"
msgstr "Sidste uge"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Last Year"
msgstr "Sidste år"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "Sidste handling"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "Sidst redigerede sider"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "Sidste side view dato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "Seneste nyt og enkeltsager"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "Layout"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "Layout Baggrund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "Layout Baggrundsfarve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "Lær mere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Venstre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "Venstre menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "Juridisk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "Juridisk Oplysning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "Legende"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "Lad dine kunder følge <br/>og forstå din process."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Lad dine kunder logge ind for at se deres dokumenter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "Lad os gøre det"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "Sådan!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Library"
msgstr "Bibliotek"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Light"
msgstr "Lys"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "Grænse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Begrænset tilpasning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Line"
msgstr "Linje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "Linje Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link"
msgstr "Link"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "Link anker"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "Link stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr "Link knap"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Link text"
msgstr "Link tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn konto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "Linkedin"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "Links"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "Link Stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "Links til andre hjemmesider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "Små Ikoner"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "Live Chat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "Live forhåndsvisning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Livechat Widget"
msgstr "Livechat Widget"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Loading..."
msgstr "Indlæser..."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logo Type"
msgstr "Logo Type"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "Logo for MinVirksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "Logoer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "Lang tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "Lav"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Magazine"
msgstr "Ugeblad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "Primær Kursus"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Hoved menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "Primære handlinger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr "Administrér sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr "Administrér dine sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr "Administrér dine hjemmeside sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr "Administrer denne side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "Marker Tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "Markeret Felter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "Markering stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "Markedsplads"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Masonry"
msgstr "Murværk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "Mål ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "Medie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "Medie sidehoved"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Medium"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "Mega menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "Mega menu klasser"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "Mega menu indhold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "Mænd"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "Menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "Menu Virksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "Menu genstand %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Menu Label"
msgstr "Menu mærkat"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "Menu Sekvens"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "Menu via en kopi af mig"

#. module: website
#: code:addons/website/models/website.py:0
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "Menuer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "Beskeder"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "Metadata"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Orla elsker udfordringer. Med hans mange års erfaring som Kommerciel\n"
" Direktør i software industrien, har Orla hjulpet Virksomhed med nå der, hvor det er i dag. Orla er blandt de skarpeste."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Middle"
msgstr "Mellem"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "Minimalistisk"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Minutes"
msgstr "Minutter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "Mobil"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile Alignment"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Mobile menu"
msgstr "Mobil menu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:0
#, python-format
msgid "Mobile preview"
msgstr "Mobil-visning"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "Tilstand"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Model"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Model data"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "Model"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "Modeller"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "Modificeret arkitektur"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "Modul"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "Overvåg Google Søgning resultats data"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"Overvåg dine besøgende imens de gennemser din hjemmeside med Odoo Social "
"app'en. Interager med dem ved blot et enkelt klik via en live chat anmodning"
" eller push notifikation. Hvis de har udfyldt en af dine formularer, kan du "
"sende dem en SMS, eller ringe til dem med det samme, imens de gennemser din "
"hjemmeside."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "Skærme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "Flere Detaljer"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr ""
"Der eksisterer over 90 former, og deres farve vælges så de stemmer overens "
"med dit Tema."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "More than one group has been set on the view."
msgstr "Mere end én gruppe er blevet angivet for visningen."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "Mosaik"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""
"Mest søgte emner relateret til dit nøgleord, prioriteret ud fra vigtighed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "Mus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "Gå Tilbage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "Gå Fremad"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to first"
msgstr "Flyt til første"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to last"
msgstr "Flyt til sidste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to next"
msgstr "Flyt til næste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Move to previous"
msgstr "Flyt til forrige"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "Flere Menuer"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "Mutli-hjemmeside mix-ind"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "Multi hjemmeside udgivet mix-ind"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "Multi hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "Multimedie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "Flere afkrydsningsfelter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Multiple tree exists for this view"
msgstr "Flere træer eksisterer for denne visning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_page
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "My Company"
msgstr "Min virksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
msgid "My Website"
msgstr "Min hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "MinVirksomhed"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "Navn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "Navn (A-Å)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (Z-A)"
msgstr "Navn (A-Å)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr "Din hjemmesides navn og favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Name, id or key"
msgstr "Navn, id, eller nøgle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "Snæver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr "Navigations bar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr ""
"Skal du afhente din ordre fra en af vores butikker? Se den der er nærmest "
"dig."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "Netværk Reklame Initiativ udmeldings side"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "Netværker"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "New"
msgstr "Ny"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"New Google Analytics accounts and keys are now using Google Analytics 4 "
"which, for now, can't be integrated/embed in external websites."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "New Page"
msgstr "Ny side"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Nyt vindue"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "Ny kollektion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "Ny kunde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr "Ny side"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "Nyheder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Nyhedsbrev"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "Nyhedsbrev pop-up"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "Næste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Animation"
msgstr "Ingen Animation"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "Ingen dias effekt"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_widget.xml:0
#, python-format
msgid "No Url"
msgstr "Ingen URL"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "Ingen Besøgende endnu!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Ingen tilpasninger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record !"
msgstr "Ingen matchende post!"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "Ingen side-visninger for denne besøgende endnu"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "Ingen partner forbundet til denne besøgende"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "No result found, broaden your search."
msgstr "Ingen resultater blev fundet; udvid din søgning."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "Ingen resultater fundet for '"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "Ingen resultater fundet. Prøv venligst en anden søgning."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Ingen support"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "Ingen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Normal"
msgstr "Normal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr "Ikke SEO optimeret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr "Ikke i hovedmenu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr "Ikke indekseret"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr ""
"Ikke alene kan du søge efter afgiftsfrie illustrationer, deres farver vil "
"også blive konverteret, så de stemmer overens med dit Tema."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "Ikke udgivet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "Ikke sporet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr ""
"Bemærk at visse tredjeparts tjenester kan installere yderligere cookies i "
"din browser, for at kunne identificere dig."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr "Bemærk: Afmærker denne side fra top Tilpas menuen for at gemme den."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "Intet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "Nummer"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
msgid "Number of languages"
msgstr "Antal sprog"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "ELLER"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Odoo Logo"
msgstr "Odoo logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Odoo Menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoo version"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Off-Canvas"
msgstr "Af-Lærred"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "Kontor Audio"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "Kontor skærme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "Offline"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok"
msgstr "OK"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "Ok, vis mig aldrig dette igen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "Ved klik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "Når det forlades"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "Når musen holdes over"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "Ved Succes"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "On Website"
msgstr "På hjemmeside"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "På invitation"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""
"Når valget af tilgængelige hjemmeside ud fra domæne er gennemført, kan du "
"filtrere ud fra lande-gruppe."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr ""
"Når brugeren lukker popup'en, vil den ikke blive vist igen i den "
"tidsperiode."

#. module: website
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr ""
"Et, og kun ét, af de to felter 'page_view_id' og 'module_id' bør være "
"angivet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "Online"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"Gælder kun hvis denne visning nedarver fra en anden (inherit_id er ikke falsk/nulværdi).\n"
"\n"
"* hvis extension (standard), hvis denne visning er efterspurgt, slås den nærmeste primære visning op (via inherit_id), hvorpå all visninger nedarver fra denne med denne\n"
"visnings-model anvendt\n"
"* hvis primary,  gennemkøres den nærmeste primære visning fuldt (selv hvis den bruger en anden model end denne), derefter er denne visnings nedarvnings specifikationer (<xpath/>) anvendt, og resultatet bliver brugt som var det denne visnings faktiske arch.\n"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "Open Source ERP"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr "Optimér SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr "Optimér denne sides SEO"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "Mulighed 1"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "Mulighed 2"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "Mulighed 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "Valgfri"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "Ranger ud fra"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Bestil nu"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "Anden information:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "Vores Virksomhed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "Vores referencer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "Vores seminarer og træning for dig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "Vores hold"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "Vores hold vil svare dig så hurtigt som muligt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr "Omrids"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "Begyndelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "Enestående billeder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "Over Indholdet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "Polstringer"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "Side"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr "Side <b>%s</b> indeholder et link til denne side"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr "Side <b>%s</b> kalder denne fil"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "Side indekseret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "Side Layout"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Page Name"
msgstr "Sidenavn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr "Sideegenskaber"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "Page Title"
msgstr "Side titel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#, python-format
msgid "Page URL"
msgstr "Side URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "Sidevisning"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "Sidevisninger"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "Side visnings historik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "Side Synlighed"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr ""
"Sidekode brugt til at fortælle IAP website_service for hvilken side en "
"snippet-liste bør genereres"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "Side via en kopi af mig"

#. module: website
#. openerp-web
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "Sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr "paginering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "Bukser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"Afsnit tekst. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""
"Paragraf med <strong>fed</strong>, <span class=\"text-muted\">dæmpet</span>,"
" og <em>kursiv</em> tekster"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Paragraph."
msgstr "Afsnit."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "Overordnet"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Overordnet menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "Overordnet sti"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "Partner til brugeren der sidst var logget ind."

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Partner-relaterede data fra brugeren"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "Partnere"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Password"
msgstr "Adgangskode"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "Sti"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "Mønster"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "Betal"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Phone Number"
msgstr "Telefonnummer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "Telefoner"

#. module: website
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Pick a Theme"
msgstr "Vælg et Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "Kage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr "Pille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "Piller"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "Pladsholder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "Normal"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/res_config_settings.js:0
#, python-format
msgid "Please confirm"
msgstr "Bekræft venligst"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "Udfyld venligst formularen korrekt."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "Salgssted"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Popup"
msgstr "Popup"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "Portefølje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Position"
msgstr "Stilling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "Indlæg sidehoved"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "Postkort"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences"
msgstr "Egenskaber"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "Foranstil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Preset"
msgstr "Forudinstilling"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Press"
msgstr "Tryk"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/view_hierarchy.js:0
#, python-format
msgid "Press %s for next %s"
msgstr "Tryk %s for næste %s"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Preview"
msgstr "Eksempel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr "Forrige"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "Tidligere visnings arkitektur"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "Priser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "Primære"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "Primær Stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "Printere"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "Prioritet"

#. module: website
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "Privatlivspolitik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Product"
msgstr "Produkt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "Varer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Professionel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "Professionelle temaer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "Profil"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Progress Bar"
msgstr "Statuslinje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "Fremskridt Bar Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "Fremskridt Bar Stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "Fremskridt Bar Vægt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "Projektører"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "Markedsfør"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "Udgiv denne side på web"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "Kampagner"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Public"
msgstr "Offentlig"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Offentlig partner"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Offentlig bruger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Publish"
msgstr "Publicer"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "Udgiv dit jobtilbud og lad folk ansøge"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "Udgiv på-stedet og online arrangementer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "Udgivet"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr "Udgivelsesdato"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "Pulsér"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "Formål"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "Sæt fokus på hvad du har at sige!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Kvalitet"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb felt kontakt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "Radar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "Radio buttons"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Rating"
msgstr "Bedømmelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Re-order"
msgstr "Genbestil"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "Klar til at bygge den"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Recipient Email"
msgstr "Modtager email"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Optage regel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "Redirect"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Redirect Old URL"
msgstr "Omdiriger gammel URL"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Redirect to URL in a new tab"
msgstr "Omdiriger til URL i en ny fane"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "Omdirigeringstype"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "Omdirigeringer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "Opdater rute liste"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "Almindelig"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "Relaterede menu genstande"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "Relaterede menuer"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "Relaterede sider"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "Relaterede nøgleord"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr ""
"Husk information om det fortrukkede udseende eller opførsel af hjemmesiden, "
"så som dit fortrukket sprog eller region."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "Fjern"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "Fjern Række"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "Fjern Serie"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "Fjern Dias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "Fjern Fane"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "Fjern alle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "Fjern tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "Rename Page To:"
msgstr "Omdøb siden til:"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "Erstat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid "Replace this with your own HTML code"
msgstr "Erstat dette med din egen HTML kode"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "Obligatorisk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "Nulstil skabeloner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "Nultil til første version (hård nulstilling)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "Ressourcer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "Det er vores prioritet at respektere dit privatliv."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "Gendan til tidligere version (blød nulstilling)."

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "Begræns udgivelse til denne hjemmeside."

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr "Restriktiv redigering"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "Begrænset Gruppe"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "Omskriv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Højre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "Højre menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple Effect"
msgstr "Bølge Effekt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "Vejkort"

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Robots.txt Editor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site.<br/>"
msgstr ""
"Robots.txt: Denne fil fortæller søgemaskine crawlers hvilke sider eller "
"filer de kan eller ikke kan anmode din side om.<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate In-Down-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "Afrund Hjørner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "Afrundet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "Afrundede Miniaturer"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "Rute"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "SEO"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "SEO Metadata"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr "SEO optimeret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Same as desktop"
msgstr ""

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "Ikon prøver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "Satellit"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/menu/content.js:0
#: code:addons/website/static/src/js/menu/seo.js:0
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "Gem"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & Reload"
msgstr "Gem & Genindlæs"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save & copy"
msgstr "Gem & kopier"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "Gem blokken til brug andetsteds"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "Score"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "Skærmbilleder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "Rul"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "Rulle Effekt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "Rul Til Toppen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "Rul Top Knap"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "Rul ned knap"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "Rul ned til næste sektion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "Søg"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Søg menuer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "Søgnings omdirigering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "Søg besøgende"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"Søg i medie dialogen når du skal bruge fotografier til at illustrere din "
"hjemmeside. Odoos integration med Unsplash, som har millioner af gratis "
"billeder af høj kvalitet, hjælper dig med at finde det perfekte billede med "
"blot et par klik."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Søg..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Anden funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "Anden menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "Sekundær funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "Anden liste af funktioner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "Anden"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "Sekundær Stil"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Seconds"
msgstr "Sekunder"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "Sektions undertitel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Select a Menu"
msgstr "Vælg en menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select a website to load its settings."
msgstr "Vælg en hjemmeside for at indlæse dens indstillinegr"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "Vælg et billede for social deling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "Vælg og slet blokke <br/>for at fjerne som trin."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "Vælg og slet blokke for at fjerne funktioner."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "Vælg en skrifttype på"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr "Vælg en hjemmeside at konfigurere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "Udvælgelse"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "Send e-mail"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "Send os en besked"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "SEO navn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "Separer email adresser med et komma."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr "Delt link"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#, python-format
msgid "Separator"
msgstr "Separator"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "Server handling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "Ydelse"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "Serviceydelser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security"
msgstr "Session &amp; Sikkerhed"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "Opsætning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "Indstillinger på denne side vil anvendes på denne hjemmeside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "Skygge"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Shadows"
msgstr "Skygger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#, python-format
msgid "Share"
msgstr "Del"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr ""

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "Shop"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "Bør brugere omdirigeres til deres browsers sprog"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Vis som valgfri nedarvning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "Vis Sidehoved"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "Vis Besked"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Sign In"
msgstr "Vis Login"

#. module: website
#: model:ir.actions.act_window,name:website.action_show_viewhierarchy
msgid "Show View Hierarchy"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Show in Top Menu"
msgstr "Vis i øverste menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_qweb
msgid "Show inactive views"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "Vis på"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "Vis reCaptcha Police"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "Sidebar"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "Log ind"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
#, python-format
msgid "Signed In"
msgstr "Logget ind"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"Sitemap.xml: Hjælp søgemaskiner crawlers med at finde ud af hvilke siger er "
"tilstede, og hvilke der er blevet ændret for nyligt, og til at crawle din "
"side derudfra. Denne fil genereres automatisk af Odoo."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Størrelse"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "Dias"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "Glid Ned"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "Glid Svæv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "Glid Venstre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "Glid Højre"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "Glid Op"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "Udglid Effekt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "Slideshow"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slogan"
msgstr "Slogan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "Lille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "Lille Sidehoved"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"Lille tekst. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit."
" <i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "Sociale medier"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "Social forhåndsvisning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "Fast"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Some Users"
msgstr "Visse Brugere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr "Noget andet her"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "Noget gik galt."

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Name"
msgstr "Sortér efter navn"

#. module: website
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Sort by Url"
msgstr "Sortér efter url"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr ""
"Talere fra hele verdenen vil slå sig sammen med vores eksperter, for at give"
" inspirerende forelæggelse om diverse emner. Hold dig ajour med de nyeste "
"forretningsledelsestendenser &amp; teknologier"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "Specifik brugerkonto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr ""
"Angiv feltet, der skal indeholde meta- og brugerdefinerede formularfelt "
"data."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "Hastighed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived !"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Square"
msgstr "Firkantet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "Firkantede Miniaturer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "Stablet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "Standard"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "Start Knap"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "Start nu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "Start nu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Start med kunden -- find ud af hvad de ønsker, og giv det til dem."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "Begynd din rejse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "Starter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "Status Farver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "Sticky"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr ""

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""
"Historefortælling er magtfuld.<br/> Den drager læseren in og engagerer dem."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "Stræk til samme højde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "Stribet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "Struktur"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Style"
msgstr "Style"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Styling"
msgstr "Styling"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "Undermenuer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Subject"
msgstr "Emne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Submit"
msgstr "Indsend"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "Indgiv sitemap til Google"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "Succes"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "Forslag"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "Omringet"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Mistænkelig aktivitet registreret af Google reCaptcha"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "Skift Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"TIP: Once loaded, follow the\n"
"                    <span class=\"o_tooltip o_tooltip_visible bottom o_animated position-relative\"/>\n"
"                    <br/>pointer to build the perfect page in 7 steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr "OVERSÆT"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs"
msgstr "Faner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "Faner farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "Mål"

#. module: website
#: model:ir.model.fields,help:website.field_ir_asset__key
msgid ""
"Technical field used to resolve multiple assets in a multi-website "
"environment."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""
"Teknisk felt brugt til at afvikle flere vedhæftninger i et multi-hjemmeside "
"miljø."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Teknisk navn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "Telefon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "Fortæl hvad værdien for <br/>kunden af denne funktion."

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Template"
msgstr "Skabelon"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr "Skabelon <b>%s (id:%s)</b> indeholder et link til denne side"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr "Skabelon<b>%s (id\\:%s)</b> kalder denne fil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "Skabelon default"

#. module: website
#: code:addons/website/models/website.py:0
#: code:addons/website/models/website.py:0
#, python-format
msgid "Templates"
msgstr "Skabeloner"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "Servicevilkår"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "Servicevilkår"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "Terræn"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "Test din robots.txt med Google Søgning Konsol"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_header_brand
msgid "Text"
msgstr "Tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "Skriftjustering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Color"
msgstr "Tekst Farve"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#, python-format
msgid "Text Highlight"
msgstr "Tekst Fremhæv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "Tekst billede tekst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "Tekst På linje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "Tekst Position"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "Dæmpet tekst. Lorem <b>ipsum dolor sit amet</b>, consectetur."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "Tak For Din Tilbagemelding"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "Thank You!"
msgstr "Tak!"

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr ""
"Google Analytics klient ID eller nøgle som du angav virker til at være "
"forkerte."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "Det valgte navn eksisterer allerede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "Firmaet denne hjemmeside tilhører"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"The current text selection cannot be animated. Try clearing the format and "
"try again."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""
"Beskrivelsen vil blive genereret  af søgemaskiner, baseret på side "
"indholdet, medmindre du angiver en."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""
"Beskrivelsen vil blive genereret af sociale medier, baseret på side "
"indholdet, medmindre du angiver en."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "Din formular er afsendt."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "Formularens specificerede model eksisterer ikke"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "Den fulde URL for at få adgang til dokumentet via hjemmesiden."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr ""
"Den fulde URL for at få adgang til serverens handling via hjemmesiden."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "En app er allerede ved at blive installeret."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "Nøgleord samt relaterede nøgleords sprog."

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "Grænsen er det maksimale antal datasæt indhentet"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "Grænsen skal være mellem 1 og 16."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr ""
"De valgte skabeloner vil blive nulstillet til deres fabriksindstillinger."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "Titlen vil tage en standard værdi medmindre du specificerer en."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr ""
"Hjemmesiden vil ikke fungere korrekt hvis du afviser eller kassere de "
"cookies."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr ""
"Hjemmesiden vil stadig fungere hvis du afviser eller kassere de cookies."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "Tema"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "Tema vedhæftninger"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
#, python-format
msgid "Theme Colors"
msgstr "Tema Farver"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Theme Options"
msgstr "Tema Indstillinger"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "Tema skabelon"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "Tema brugerinterface visning"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "Tema værktøjer"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr "Der er i øjeblikket ingen side på din hjemmeside."

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr ""
"Der er ingen kontakt og/eller ingen email forbundet til denne besøgende."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "There is no data currently available."
msgstr "Der er ingen tilgængelige data."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"There is no website available for this company. You could create a new one."
msgstr ""
"Der er ikke nogen tilgængelig hjemmeside for denne virksomhed. Du kan "
"oprette en ny."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Disse servicevilkår (\"Termer\", \"Aftale\") er en aftale mellem hjemmesiden"
" (\"Hjemmeside operatøren\", \"os\", \"vi\", eller \"vores\") og dig "
"(\"Bruger\", \"brugeren\", \"du\", \"dig\", \"dit\", \"din\" eller "
"\"dine\"). Denne Aftale fremsætter de generelle termer og forhold for dit "
"brug af denne hjemmeside og ethvert af dets produkter eller tjenester "
"(samlet \"Hjemmeside\" eller \"Tjenester\")."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "Tyk"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "Tynd"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "Tredje funktion"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "Tredje menu"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""
"Dette felt indeholder billedet, der bruges til at vise et favicon på "
"hjemmesiden."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "Dette felt er det samme som 'arch' feltet uden oversættelser"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "Dette felt bliver brugt til at sætte/modtage lokaler for brugeren"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"Dette felt bør blive brugt når arch visning tilgås. Den vil gøre brug af oversættelse.\n"
"Bemærk at den vil læse 'arch_db' eller 'arch_fs' hvis man er i dev-xml tilstand."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "Dette felt gemmer arch visningen."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"Dette felt vil gemme den nuværende 'arch_db' før den overskrives.\n"
"Brugbar til at (blødt) nulstille en ødelagt visning."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr "Dette er en \""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr ""
"Dette er en simpel hero enhed, en simpel komponent i kæmpe-skærms stil, "
"beregnet til at påkalde fremhævet indhold eller information ekstra "
"opmærksomhed."

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "Denne besked blev posteret til din hjemmeside!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "Denne side"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "Denne operatør er ikke understøttet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr "Denne side er i menuen <b>%s</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be published on {{ date_formatted }}"
msgstr "Denne side vil blive udgivet den {{ date_formatted }}"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "This translation is not editable."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr ""
"Denne værdi vil blive udelades for at være i overensstemmelse med alle "
"større browsere og anvendes i URL. Lad den være tom for at bruge standard "
"navnet på datasættet."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.report_viewhierarchy_children
msgid "This view arch has been modified"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid ""
"Those accounts should now check their Analytics dashboard in the Google "
"platform directly."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Miniaturebilleder"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "Tid siden sidste side visning. F.eks.: 2 minutter siden"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__cache_time
msgid "Time to cache the page. (0 = no cache)"
msgstr "Tid til cachelagring af siden. (0 = ingen cachelagring)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "Tiden er gået! Du kan nu besøge"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Timeline"
msgstr "Tidslinje"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "Tidszone"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "Tip: Interager med besøgende for at omdanne dem til kundeemner"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "Tip: Søgemaskine Optimering (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr ""

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "Tip: Brug royalty-frie fotos"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Title"
msgstr "Titel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share_options
msgid "Title Position"
msgstr "Titel Position"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"For at tilføje en fjerde kolonne, reducér da størrelsen på disse tre "
"kolonner ved at bruge det højre ikon ved hver blok. Duplikér derefter en af "
"kolonnerne for at opertte en ny som en kopi."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"For at få flere besøgende bør du sigte efter nøgleord der ofte søges efter i"
" Google. Med det indbyggede SEO værktøj - når du har defineret et par "
"nøgleord - vil Odoo foreslå dig de nøgleord der er bedst at bruge. Tilpas "
"derefter din titel og beskrivelse derudfra, for at forøge din trafik."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Hvis du vil sende invitationer i B2B-tilstand, skal du åbne en kontakt eller"
" vælge flere i listevisning og klikke på 'Portal Access Management' i "
"rullemenuen *Handling*."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr "Skift"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.option_header_off_canvas
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr "Skift navigering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "Værktøjstip"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_share_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Top"
msgstr "Top"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:0
#, python-format
msgid "Top Menu"
msgstr "Topmenu"

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "Top menu for hjemmeside %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "Top til Bund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "Samlet antal sporede side besøg"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "Samlet antal besøg på sporede sider"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "Spor"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/track_page.xml:0
#, python-format
msgid "Track Visitor"
msgstr "Spor Besøgende"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr "Spor besøg i Google Analytics"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "Sporet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "Overgang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Attribute"
msgstr "Oversæt egenskab"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate Selection Option"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "Oversat indhold"

#. module: website
#: model:ir.model,name:website.model_ir_translation
msgid "Translation"
msgstr "Oversættelse"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/translate.js:0
#, python-format
msgid "Translation Info"
msgstr "Oversættelsesinfo"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "Tun og Laks Burger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "Forvandl enhver funktion til en fordel for din læser."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Twitter konto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Twitter Scroller"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Type"
msgstr "Type"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Type '"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""
"Skriv '<i class=\"confirm_word\">ja</i>' i boksen nedenunder hvis du ønsker "
"er bekræfte."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"Type af omdirigering/omskrivelse:\n"
"\n"
"301 Flyttet permanent: Browseren vil bibeholde den nye URL i cache.\n"
"302 Flyttet midlertidigt: Browseren vil ikke bibeholde den nye URL i cache, og spørger igen efter den nye URL næste gang.\n"
"404 Ikke fundet: Hvis du vil fjerne en specifik side/controller (f.eks. hvis Ecommerce er installeret, men du vil ikke have /shop på en specifik hjemmeside)\n"
"308 Omdiriger / omskriv: Hvis du vil omdøbe en controller med et nyt URL. (F.eks. /shop -> /garden - Begge URL vil være tilgængelige, men /shop vil automatisk omdirigere til /garden)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "URL"
msgstr "URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL fra"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""
"URL der matcher disse filtre vil blive overskrevet via CDN basis URL'en"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL til"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "Uforanderlig unik identifikator"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "Understreg når musen holdes over"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"Forstå hvordan besøgende interagerer med din hjemmeside via Google Analytics.\n"
"                                              Lær mere om"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr "Uindekseret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Ubegrænset CRM kraft og support"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Ubegrænset tilpasning"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#, python-format
msgid "Unpublish"
msgstr "Afpublicér"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:0
#: code:addons/website/static/src/js/backend/button.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr "Ikke udgivet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "Opdater tema"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "Upload"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Adresse"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "URL på statisk flag billede"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "URL & Sider"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr "Brug Google Maps på din hjemmeside ("

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr "Brug Google Maps på din hjemmeside (Kontakt Os side, snippets, osv.)."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""
"Anvend en CDN til at optimere tilgængeligheden af din hjemmesides indhold"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "Brug et billede per standard til deling"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#, python-format
msgid "Use as Homepage"
msgstr "Anvend som Forside"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Brug af Cookies"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr ""
"Brug denne komponent til at oprette en liste over fremhævede elementer som "
"du vil gøre opmærksom på."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr ""
"Brug denne snippet til at bygge diverse slags komponenter, der indeholder et"
" venstre- eller højre-orienteret billede sammen med tekst indhold. Dupliker "
"elementet for at oprette en liste der passer til dine behov."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr ""
"Brug denne snippet til at præsentere dit indhold i et dias-lignende format. "
"Lad være med at skrive om produkter eller tjenester her; skriv om løsninger."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "Brug dette tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr ""
"Brug denne tidslinje som en del af dit resume, for at vise dine besøgende, "
"hvad du har præsteret i fortiden."

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "Brugt i FormBuilder registret"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "Brugt i sideindhold"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "Anvendt i sidebeskrivelse"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "Brugt i første niveau sidehoved"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "Brugt i andet niveau sidehoved"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "Brugt i side titel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"Anvendes til at indhente informationer vedrørende dine interaktioner med hjemmesiden, siderne du har set,\n"
"                                                  og enhver specifik marketing kampagne der bragte dig til hjemmesiden."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"Bruges til at gøre reklamer mere engagerende for brugere, og mere værdifuld for udgivere og annoncører,\n"
"                                               så som at udbyde mere relevante reklamer når du besøger andre hjemmesider der viser reklamer, eller til at forbedre rapporteringen om en kampagnes præstationsevne."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "Bruges når flere hjemmesider har samme domæne."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "Nyttige Links"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "Nyttige indstillinger"

#. module: website
#: model:ir.model.fields,help:website.field_website_menu__group_ids
msgid "User need to be at least in one of these groups to see the menu"
msgstr "Bruger skal være i mindst én af disse grupper for at se menuen"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "Brugere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr "Værktøjer & Typografi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "Værdi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "Vert. Justering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "Lodret"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "Vertikal Justering"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Video"
msgstr "Video"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Vis"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Vis arkitektur"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Navn på visning"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Se type"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Vis nedarvningstilstand"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "Visninger via en kopi af mig"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "Visninger, der arver fra denne visning"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:0
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Visibility"
msgstr "Synlighed"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "Synlighed Kodeord"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "Synlighed Kodeord Visning"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__group_ids
msgid "Visible Groups"
msgstr "Synlige grupper"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "Synlig for"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "Synlig på denne hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Visible on mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "Besøgsdato"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr ""
"Besøg vores Facebook side for at finde ud af om du var en af de heldige "
"vindere."

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "Besøgte sider"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "Besøgte side historik"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "Gæst"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "Besøgendes sidevisninger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "Besøgendes sidevisnings historik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "Besøgendes visninger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "Besøgendes visnings historik"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.menu_visitor_sub_menu
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "Besøgende"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#, python-format
msgid "Visits"
msgstr "Besøg"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr ""
"Vent på at besøgende besøger din hjemmeside for at se hvilke sider de "
"kiggede på."

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "Advarsel"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"Vi er et hold af passionerede individer, hvis mål er at forbedre alles\n"
"liv gennem nyskabende produkter. Vi laver fantastiske produkter for at løse jeres forretnings problemer.\n"
"                                   <br/><br/>Vores produkter er designet til små og mellemstore virksomheder, som er villige til, at optimere deres ydeevne."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"Vi er et hold af passionerede individer, hvis mål er at forbedre alles\n"
"liv gennem nyskabende produkter. Vi laver fantastiske produkter for at løse jeres forretnings problemer. Vores produkter er designet til små og mellemstore virksomheder, som er villige til, at optimere deres ydeevne."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"Vi er et hold af passionerede individer, hvis mål er at forbedre alles liv. "
"<br/>Vores tjenester er designet til små og mellemstore virksomheder."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "Vi er næsten færdige!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "Vi er i godt selskab."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr ""
"Vi understøtter i øjeblikket ikke Spor Ej signaler, eftersom der ikke er "
"nogen industri standard at følge."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "We found these ones:"
msgstr "Vi fandt disse:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_header_slogan_oe_structure_header_slogan_1
msgid "We help <b>you</b> grow your business"
msgstr "Vi hjælper <b>dig</b> med at vokse din forretning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr ""
"Vi kan muligvis ikke yde den bedste service til dig, hvis du afviser de "
"cookies, men hjemmesiden vil fungere."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "We use cookies to provide you a better user experience."
msgstr "Vi bruger cookies til at tilbyde dig en bedre bruger oplevelse."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use them to store info about your habits on our website. It will helps us"
" to provide you the very best experience and customize what you see."
msgstr ""
"Vi bruger dem til at lagre informationer om dine vaner på vores hjemmeside. "
"Det vil hjælpe os med at tilbyde dig den aller bedste oplevelse, og tilpasse"
" hvad du ser."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
#: model_terms:website.page,arch_db:website.contactus_thanks
msgid "We will get back to you shortly."
msgstr "Vi kommer tilbage hurtigst muligt."

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "Web besøgende"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/js/menu/edit.js:0
#: code:addons/website/static/src/xml/website.backend.xml:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "Hjemmeside"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "Hjemmeside virksomhed"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.start_configurator_act_url
msgid "Website Configurator"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Hjemmesidens domæne"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "Hjemmeside Favicon"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "Hjemmeside formular nøgle"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "Formularer"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "Hjemmeside logo"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Hjemmesidens menu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "Hjemmeside menu indstillinger"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "Hjemmesidens navn"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "Hjemmeside side"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "Hjemmeside side indstillinger"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "Hjemmeside sider"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "Hjemmeside sti"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "Hjemmeside udgivet mix-ind"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form
#, python-format
msgid "Website Settings"
msgstr "Website indstillinger"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Hjemmeside Snippet Filter"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "Hjemmeside tema menu"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "Hjemmeside tema side"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr "Hjemmeside overskrift"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "Hjemmeside URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "Hjemmeside URL"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "Hjemmeside besøgende"

#. module: website
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "Hjemmeside besøgende #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
#: model:ir.cron,cron_name:website.website_visitor_cron
#: model:ir.cron,name:website.website_visitor_cron
msgid "Website Visitor : Archive old visitors"
msgstr "Hjemmeside besøgende : Arkivér gamle besøgende"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Hjemmesiden kan bruge cookies til at personliggøre og faciliterer bedst "
"mulig navigation for brugeren af denne hjemmeside. Brugeren kan konfigurere "
"hans / hendes browser til at underrette og afvise installationen af cookies "
"sendt af os."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Hjemmesidens menu"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Meta beskrivelse"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta søgeord"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Meta titel"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "Hjemmeside - opengraph-billede"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "Hjemmeside omskrivning"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "Hjemmeside omskrivning indstillinger"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "Hjemmeside omskrivninger"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Analytics"
msgstr "Hjemmeside: Analytics"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "Hjemmeside: Instrumentbræt"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Websites"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Hjemmesider, som skal oversættes"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Websites-shared page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Welcome to your"
msgstr "Velkommen til din"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "Hvad du ser, er hvad du får"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "Bredde"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "Med Kodeord"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "Kvinder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Skriv et citat fra en af dine kunder her. Citater er en fantastisk måde til "
"at opbygge tillid i dine produkter og tjenester."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Skriv en eller to afsnit der beskriver dit produkt eller tjenester. For at "
"lykkedes bør dit indhold være nyttig for læserne."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Skriv en eller to paragraffer som beskriver dit produkt, tjeneste, eller "
"specifikke funktino.<br/> For at være vellykket skal dit inhold være "
"brugbart for dine læsere."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr "Skriv hvad kunden gerne vil vide, <br/>ikke hvad du gerne vil vise."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "År"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "Du er ved at vælge oversættelsestilstand."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"Du kan vælge at få din computer til at advare dig, hver gang en cookie afsendes, eller du kan vælge at deaktivere alle cookies.\n"
"                             Hver browser er forskellig, så kig i din browsers Hjælp menu for at finde ud af, hvad der er den korrekte måde for dig at redigere dine cookies."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "Du kan redigere farver og baggrunde for at fremhæve funktioner."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "Du kan redigere, duplikere..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""
"Du kan have 2 hjemmesider med samme domæne OG en betingelse for landegruppe "
"til at vælge hvilken hjemmeside at bruge."

#. module: website
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "Du kan ikke have to brugere med det samme login!"

#. module: website
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr "Du kan kun bruge skabeloner med præfikset dynamic_filter_template_"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate a model field."
msgstr "Du kan ikke duplikere et model felt."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "Du kan ikke kopiere indgiv knappen fra formularen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove a field that is required by the model itself."
msgstr "Du kan ikke fjerne et felt der er påkrævet af selve modellen."

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "Du kan ikke fjerne indgiv knappen fra formularen."

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr ""

#. module: website
#: code:addons/website/controllers/backend.py:0
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr "Du har ikke nok rettigheder til at udføre den handling."

#. module: website
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "Du har ikke rettigheder til at ud-/afgive"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr "Det lader ikke til at du har adgang til denne Analytiske konto."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""
"Du har gemt denne side fra søgeresultater. Den vil ikke blive indekseret af "
"søgemaskiner."

#. module: website
#: code:addons/website/models/res_config_settings.py:0
#, python-format
msgid "You haven't defined your domain"
msgstr "Du har ikke angivet dit domæne"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr "Du kan afmelde tredjeparters brug af cookies ved at besøge "

#. module: website
#: code:addons/website/models/website.py:0
#, python-format
msgid "You must keep at least one website."
msgstr "Du skal beholde mindst én hjemmeside."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr "Du skal logge ind på din Google konto før:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Du bør nøje gennemlæse lovkravene samt andre betingelser for brug af hvilken"
" som helst hjemmeside som du forbinder til gennem et link fra denne "
"hjemmeside. Din linkning til enhver anden off-site side eller hjemmeside er "
"på dit eget ansvar."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Client ID:"
msgstr "Dit klient ID:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Company"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr ""
"Din Dynamiske Snippet vil blive vist her... Denne besked vises, fordi du "
"ikke angav både et filter og en skabelon at bruge. <br/>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Email"
msgstr "Din e-mail"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:0
#, python-format
msgid "Your Measurement ID:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Name"
msgstr "Dit navn"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/form_editor_registry.js:0
#, python-format
msgid "Your Question"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "Din beskrivelse ser for lang ud."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "Din beskrivelse ser for kort ud."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr ""
"Din oplevelse kan blive forringet hvis du afviser disse cookies, men "
"hjemmesiden vil stadig fungere."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "Din søgning '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "Din titel"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Youtube konto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "Zoom"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "Zoom Ind"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Down"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Left"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In-Right"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "Zoom Ud"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "og"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "og kopier og indsæt adressen på forsiden her."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "breadcrumb"
msgstr "brødkrumme"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "fest, frokost"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "plan, tabel, diagram, kage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "citér"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "kolonner, beskrivelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "almindelige svar, almindelige spørgsmål"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "indhold"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "kunder, klienter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "dage"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "f.eks. /min-fantastiske-side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "e.g. About Us"
msgstr "fx Om os"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "f.eks. De Brouckere, Bruxelles, Belgien"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr "eCommerce Dashboard"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "eLearning"

#. module: website
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "email"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "esc"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "udvikling, vækst"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "found(s)"
msgstr "fandt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "gratis hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "galleri, karusel"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "hoved, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "hero, jumbotron"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "billede, medie, illustration"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "oppe i højre hjørne for at begynde at designe."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_header_contact_oe_structure_header_contact_1
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger_oe_structure_header_hamburger_3
#: model_terms:ir.ui.view,arch_db:website.template_header_sidebar_oe_structure_header_sidebar_1
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "Odoo instansen"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr "link"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "murværk, gitter"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "menu, prissætning"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "o-color-"
msgstr "o-color-"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid ""
"of\n"
"            your visitors. We recommend you avoid them unless you have\n"
"            verified with a legal advisor that you absolutely need cookie\n"
"            consent in your country."
msgstr ""
"af\n"
"            dine besøgende. Vi tilråder at du undgår dem medmindre du har\n"
"            bekræftet med din juridiske rådgiver, at du absolut skal have samtykke\n"
"            ift cookies i dit land."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "eller rediger Master"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "organisation, struktur"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr "side, snippets, ...)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_form_preview
msgid "phone"
msgstr "telefon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "placeholder"
msgstr "pladsholder"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/theme_preview.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "prissættelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "promovering, egenskaber, kvalitet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "rækker"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "separator, opdeler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "statistikker, stats, KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "udtalelser"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:website.page,arch_db:website.color_combinations_debug_page
msgid "text link"
msgstr "tekst link"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. openerp-web
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "denne side"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:0
#, python-format
msgid "to exit full screen"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "true"
msgstr "Sandt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "valuering, rang"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "Hjemmeside"

#. module: website
#. openerp-web
#: code:addons/website/static/src/components/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "ja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.res_config_settings.xml:0
#, python-format
msgid "you do not need to ask for the consent"
msgstr "du behøves ikke spørge om samtykket"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ Aktiv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Background"
msgstr "⌙ Baggrund"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Blur"
msgstr "⌙ Udtvær"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Buttons"
msgstr "⌙ Knapper"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Color"
msgstr "⌙ Farve"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Colors"
msgstr "⌙ Farver"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Country"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ Forsinkelse"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Desktop"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Display"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Headings"
msgstr "⌙ Hovedtitler"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Height"
msgstr "⌙ Højde"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Hue"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ Inaktiv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Intensity"
msgstr "⌙ Intensitet"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Label"
msgstr "⌙ Mærkat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Large"
msgstr "⌙ Stor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "⌙ Mobile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Off-Canvas Logo"
msgstr "⌙ Off-Canvas Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Offset (X, Y)"
msgstr "⌙ Forskyd (X, Y)"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "⌙ Page Anchor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Parallax"
msgstr "⌙ Parallaks"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "⌙ Position"
msgstr "⌙ Position"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Saturation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ Separator"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Small"
msgstr "⌙ Lille"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Spacing"
msgstr "⌙ Afstand"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "⌙ Spread"
msgstr "⌙ Spred"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ Stil"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Campaign"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Medium"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ UTM Source"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Users"
msgstr "⌙ Brugere"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "⌙ Video"
msgstr "⌙ Video"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "⌙ Width"
msgstr "⌙ Bredde"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "└ Height"
msgstr ""
