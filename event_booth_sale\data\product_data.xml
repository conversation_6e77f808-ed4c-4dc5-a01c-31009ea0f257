<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data noupdate="1">

    <record id="product_product_event_booth" model="product.product">
        <field name="name">Event Booth</field>
        <field name="purchase_ok" eval="False"/>
        <field name="list_price">100.0</field>
        <field name="description_sale" eval="False"/>
        <field name="categ_id" ref="event_sale.product_category_events"/>
        <field name="invoice_policy">order</field>
        <field name="detailed_type">event_booth</field>
    </record>

</data></odoo>
