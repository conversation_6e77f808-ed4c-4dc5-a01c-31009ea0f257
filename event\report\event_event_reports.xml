<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="paperformat_event_foldable_badge" model="report.paperformat">
        <field name="name">Custom Paperformat for the Event Foldable Badge report</field>
        <field name="default" eval="False"/>
        <field name="disable_shrinking" eval="True"/>
        <field name="format">A4</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="dpi">96</field>
    </record>

    <record id="paperformat_event_full_page_ticket" model="report.paperformat">
        <field name="name">Custom Paperformat for the Event Full Page Ticket report</field>
        <field name="default" eval="False"/>
        <field name="disable_shrinking" eval="True"/>
        <field name="format">A4</field>
        <field name="page_height">0</field>
        <field name="page_width">0</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">8</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="dpi">96</field>
    </record>

    <!-- The "Full Page Ticket", as opposed to the foldable badge that is meant to be folded and
    only contains the bare minimum (attendee name + barcode), gives all the information of the
    ticket in a portrait A4 format.
    It allows to add some information in the ticket_instructions field and, when printed, functions
    as an "official" ticket that the attendee can show to the registration desk where all
    the information are available (event name, organizer, SO reference, barcode, footer with
    sponsors, ...). -->
    <record id="action_report_event_registration_full_page_ticket" model="ir.actions.report">
        <field name="name">Full Page Ticket</field>
        <field name="model">event.registration</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">event.event_registration_report_template_full_page_ticket</field>
        <field name="report_file">event.event_registration_report_template_full_page_ticket</field>
        <field name="print_report_name">'Full Page Ticket - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), object.name.replace('/',''))</field>
        <field name="binding_model_id" ref="model_event_registration"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_event_full_page_ticket"/>
    </record>

    <record id="action_report_event_event_full_page_ticket" model="ir.actions.report">
        <field name="name">Full Page Ticket Example</field>
        <field name="model">event.event</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">event.event_event_report_template_full_page_ticket</field>
        <field name="report_file">event.event_event_report_template_full_page_ticket</field>
        <field name="print_report_name">'Full Page Ticket - %s' % (object.name or 'Event').replace('/','')</field>
        <field name="binding_model_id" ref="model_event_event"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_event_full_page_ticket"/>
    </record>

    <record id="action_report_event_registration_foldable_badge" model="ir.actions.report">
        <field name="name">Foldable Badge</field>
        <field name="model">event.registration</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">event.event_registration_report_template_foldable_badge</field>
        <field name="report_file">event.event_registration_report_template_foldable_badge</field>
        <field name="print_report_name">'Foldable Badge - %s - %s' % ((object.event_id.name or 'Event').replace('/',''), object.name.replace('/',''))</field>
        <field name="binding_model_id" ref="model_event_registration"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_event_foldable_badge"/>
    </record>

    <record id="action_report_event_event_foldable_badge" model="ir.actions.report">
        <field name="name">Foldable Badge Example</field>
        <field name="model">event.event</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">event.event_event_report_template_foldable_badge</field>
        <field name="report_file">event.event_event_report_template_foldable_badge</field>
        <field name="print_report_name">'Foldable Badge - %s' % (object.name or 'Event').replace('/','')</field>
        <field name="binding_model_id" ref="model_event_event"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="paperformat_event_foldable_badge"/>
    </record>

</odoo>
