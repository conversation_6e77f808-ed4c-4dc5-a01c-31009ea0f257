# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_landed_costs
# 
# Translators:
# <PERSON>, 2021
# hish, 2021
# <AUTHOR> <EMAIL>, 2021
# Batmunk<PERSON> Ganbat <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>jaaja<PERSON><PERSON> Badamjunai <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Chee<PERSON>umtsend <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Baskhuu <PERSON> <<EMAIL>>, 2022
# Bayarkhuu Bataa, 2023
# Torbat Jargalsaikhan, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Torbat Jargalsaikhan, 2023\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid " already out"
msgstr " хэдийнээ дууссан"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.stock_landed_cost_view_kanban
msgid "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_account_move_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Үлдэгдэл тооцох барааны хувьд та тэдгээр барааны нөөцийг хянах боломжтой юм. Үүний тулд Inventory app суулгасан байх шаардлагатай.\n"
"Үлдэгдэл тооцохгүй бараа бол нөөцийг хянаж бүртгэх шаардлагагүй бараа юм.\n"
"Үйлчилгээ бол таны ханган үзүүлдэг материаллаг бус ажил үйлчилгээ болно."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__account_id
msgid "Account"
msgstr "Данс"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_journal_id
msgid "Account Journal"
msgstr "Санхүүгийн журнал"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардсан"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Additional Costs"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__additional_landed_cost
msgid "Additional Landed Cost"
msgstr "Өртөгт шингэх зардал"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__target_model
msgid "Apply On"
msgstr "Хэрэгжүүлэх"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_current_cost_price
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_current_cost_price
msgid "By Current Cost"
msgstr "Мөрийн үнийн дүнд"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_quantity
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_quantity
msgid "By Quantity"
msgstr "Тоо хэмжээгээр"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_volume
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_volume
msgid "By Volume"
msgstr "Эзлэхүүнээр"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_weight
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_weight
msgid "By Weight"
msgstr "Жингээр"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cancel"
msgstr "Цуцлах"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__cancel
msgid "Cancelled"
msgstr "Цуцлагдсан"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company"
msgstr "Компани"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company related to this journal"
msgstr "Энэ журналын харъяалагдах компани"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Compute"
msgstr "Тооцоолох"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__price_unit
msgid "Cost"
msgstr "Өртөг"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_line_id
msgid "Cost Line"
msgstr "Өртөгийн мөр"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__cost_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cost Lines"
msgstr "Өртөгийн мөрүүд"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Cost and adjustments lines do not match. You should maybe recompute the "
"landed costs."
msgstr ""
"Өртөг болон тохируулгын мөрүүд таарахгүй байна. Магадгүй та өртөгт шингэх "
"зардлыг дахин тооцоолуулах шаардлагатай."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
msgid "Create Landed Costs"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Create a new landed cost"
msgstr "Өртөгт шингэх зардал шинээр нэмэх"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__currency_id
msgid "Currency"
msgstr "Валют"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__date
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Date"
msgstr "Огноо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_config_settings__lc_journal_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.res_config_settings_view_form
msgid "Default Journal"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method when used for Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__name
msgid "Description"
msgstr "Тайлбар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: stock_landed_costs
#: model:mail.message.subtype,name:stock_landed_costs.mt_stock_landed_cost_open
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Done"
msgstr "Дууссан"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__draft
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Draft"
msgstr "Ноорог"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__equal
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__equal
msgid "Equal"
msgstr "Тэнцүү"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""
"Тэнцүү : Өртөгийг мөр бүрт тэнцүү хуваарилна.\n"
"Тоогоор : Өртөгийг мөр бүрийн тоо хэмжээнд тохируулан хуваарилна.\n"
"Мөрийн өртөг дүн : Захиалгын мөр бүрийн өртөг үнийн дүнд тохируулан хуваарилна.\n"
"Жингээр : Өртөгийг мөр бүрийн барааны жинд тохируулан хуваарилна.\n"
"Эзлэхүүнээр : Өртөгийг мөр бүрийн барааны эзлэхүүнд тохируулан хуваарилна."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Future Activities"
msgstr "Ирээдүйн үйл ажиллагаанууд"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__id
msgid "ID"
msgstr "ID"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_unread
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Indicates whether the product is a landed cost."
msgstr "Энэхүү бараа нь өртөгт шингэх зардлын төрөл зүйл болохыг илэрхийлнэ."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__is_landed_costs_line
msgid "Is Landed Costs Line"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Is a Landed Cost"
msgstr "Өртөгт шингэх зардал эсэх"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__description
msgid "Item Description"
msgstr "Зүйлийн тайлбар"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Journal"
msgstr "Журнал"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_move_id
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move_line
msgid "Journal Item"
msgstr "Журналын бичилт"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_layer__stock_landed_cost_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed Cost"
msgstr "Өртөгт шингэсэн зардал"

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.action_stock_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_ids
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree2
msgid "Landed Costs"
msgstr "Өртөгт шингэх зардал"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_visible
msgid "Landed Costs Visible"
msgstr ""

#. module: stock_landed_costs
#: model:mail.message.subtype,description:stock_landed_costs.mt_stock_landed_cost_open
msgid "Landed cost validated"
msgstr "Өртөгт шингэх зардал батлагдсан"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost____last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines____last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Late Activities"
msgstr "Хоцорсон ажилбар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_company__lc_journal_id
msgid "Lc Journal"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_main_attachment_id
msgid "Main Attachment"
msgstr "Үндсэн хавсралт"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error
msgid "Message Delivery error"
msgstr "Алдаа үүссэн талаарх зурвас"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__name
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Name"
msgstr "Нэр"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "New"
msgstr "Шинэ"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__final_cost
msgid "New Value"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календар дээрх дараагийн Үйл ажиллагаа"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Үйлдэл шаардсан зурвасын тоо"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_unread_counter
msgid "Number of unread messages"
msgstr "Уншаагүй зурвасын тоо"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Only draft landed costs can be validated"
msgstr "Зөвхөн ноорог төлөвтэй өртөгт шингэх зардлыг батлах боломжтой"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__former_cost
msgid "Original Value"
msgstr "Анхны өртөг"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Picking"
msgstr "Бэлтгэх баримт"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please configure Stock Expense Account for product: %s."
msgstr "Дараах барааны хувьд Зардлын дансыг тохируулна уу: %s."

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please define %s on which those additional costs should apply."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__done
msgid "Posted"
msgstr "Батлагдсан"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__product_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__product_id
msgid "Product"
msgstr "Бараа"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_product_template
msgid "Product Template"
msgstr "Барааны загвар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__product_type
msgid "Product Type"
msgstr "Барааны төрөл"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Худалдан авалтын захиалгын мөр"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__quantity
msgid "Quantity"
msgstr "Тоо хэмжээ"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS илгээлтийн алдаа"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Өнөөдрийг хүртэлх хугацаанд дараагийн ажилбарын огноо нь тохируулагдсан бүх "
"тэмдэглэлүүд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid "Split Method"
msgstr "Хуваарилах арга"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__state
msgid "State"
msgstr "Төлөв"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Status"
msgstr "Төлөв"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost
msgid "Stock Landed Cost"
msgstr "Өртөгт шингэх зардал"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost_lines
msgid "Stock Landed Cost Line"
msgstr "Өртөгт шингэх зардлын мөр"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__move_id
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "Өртгийн бичилт"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Today Activities"
msgstr "Өнөөдрийн ажилбар"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__amount_total
msgid "Total"
msgstr "Нийт дүн"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__picking_ids
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__target_model__picking
msgid "Transfers"
msgstr "Гүйлгээ"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_unread
msgid "Unread Messages"
msgstr "Уншаагүй зурвас"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Уншаагүй зурвасын тоолуур"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Validate"
msgstr "Зөвшөөрөх"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Validated landed costs cannot be cancelled, but you could create negative "
"landed costs to reverse them"
msgstr ""
"Батласан төлөвтэй өртөгт шингэх зардалын бүртгэлийг цуцлах боломжгүй, гэхдээ"
" буцаахын тулд хасах утгатай зардлыг өртөгт шингээж болно"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation"
msgstr "Өртөгийн дүн"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_adjustment_lines
msgid "Valuation Adjustment Lines"
msgstr "Өртөг шингээлтийн мөрүүд"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__valuation_adjustment_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation Adjustments"
msgstr "Өртөг шингээлт"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__vendor_bill_id
msgid "Vendor Bill"
msgstr "Нийлүүлэгчийн нэхэмжлэл"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__volume
msgid "Volume"
msgstr "Эзлэхүүн"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website Messages"
msgstr "Вебсайтын зурвас"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website communication history"
msgstr "Вебсайтын харилцааны түүх"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__weight
msgid "Weight"
msgstr "Жин"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"You cannot apply landed costs on the chosen %s(s). Landed costs can only be "
"applied for products with FIFO or average costing method."
msgstr ""

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/models/product.py:0
#, python-format
msgid ""
"You cannot change the product type or disable landed cost option because the"
" product is used in an account move line."
msgstr ""
