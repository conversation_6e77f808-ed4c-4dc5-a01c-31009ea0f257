<?xml version="1.0"?>
<odoo><data noupdate="1">

    <record id="sms_template_data_event_registration" model="sms.template">
        <field name="name">Event: Registration</field>
        <field name="model_id" ref="event.model_event_registration"/>
        <field name="body">{{ object.event_id.organizer_id.name or object.event_id.company_id.name or user.env.company.name }}: We are happy to confirm your registration for the {{ object.event_id.name }} event.</field>
        <field name="lang">{{ object.partner_id.lang }}</field>
    </record>

    <record id="sms_template_data_event_reminder" model="sms.template">
        <field name="name">Event: Reminder</field>
        <field name="model_id" ref="event.model_event_registration"/>
        <field name="body">{{ object.event_id.organizer_id.name or object.event_id.company_id.name or user.env.company.name }}: We are excited to remind you that the {{ object.event_id.name }} event is starting {{ object.get_date_range_str() }}. We confirm your registration and hope to meet you there.</field>
        <field name="lang">{{ object.partner_id.lang }}</field>
    </record>

</data></odoo>
