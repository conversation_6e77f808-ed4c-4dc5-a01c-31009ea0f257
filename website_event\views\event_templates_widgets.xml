<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Search Box -->
<template id="events_search_box_input" name="Events search box">
    <t t-call="website.website_search_box_input">
        <t t-set="_classes" t-valuef="o_wevent_event_searchbar_form w-100 my-1 my-lg-0 #{_classes}"/>
        <t t-set="search_type" t-valuef="events"/>
        <t t-set="action" t-valuef="/event"/>
        <t t-set="display_description" t-valuef="true"/>
        <t t-set="display_detail" t-valuef="false"/>
        <t t-set="search" t-value="search or searches and searches['search']"/>
        <t t-set="placeholder">Search an event...</t>
        <t t-foreach="searches" t-as="item">
            <input t-if="item != 'search' and item_value != 'all'" type="hidden"
                t-att-name="item" t-att-value="item_value"/>
        </t>
        <t t-out="0"/>
    </t>
</template>

<template id="events_search_box" inherit_id="website.website_search_box" primary="True">
    <xpath expr="//div[@role='search']" position="replace">
        <form t-attf-class="o_wevent_event_searchbar_form o_wait_lazy_js w-100 my-1 my-lg-0 #{_classes}"
              t-att-action="action if action else '/event'" method="get">
            <t t-set="search" t-value="search or _searches and _searches['search']"/>
            <t t-set="placeholder" t-value="placeholder or _placeholder"/>
            <t>$0</t>
            <t t-foreach="_searches" t-as="search">
                <input t-if="search != 'search' and search_value != 'all'" type="hidden"
                    t-att-name="search" t-att-value="search_value"/>
            </t>
            <t t-out="0"/>
        </form>
    </xpath>
</template>

<!-- Snippet - Country Events - List -->
<template id="country_events_list" name="Country Events List">
    <div class="country_events_list">
        <t t-if="events">
            <h6 class="o_wevent_sidebar_title">
                <t t-if="country">
                    <i class="fa fa-flag mr-2"/>Events: <span t-esc="country.name"/>
                    <img class="img-fluid" t-att-src="country.image_url" alt=""/>
                </t>
                <t t-else="">
                    <i class="fa fa-globe mr-2"/>Upcoming Events
                </t>
            </h6>
            <ul class="list-group mb-3">
                <li t-foreach="events[:5]" t-as="event_dict" class="list-group-item d-flex justify-content-between">
                    <a t-att-href="event_dict['url']">
                        <i t-if="not event_dict['event'].website_published" class="fa fa-ban text-danger mr-1" role="img" aria-label="Unpublished" title="Unpublished"/>
                        <span t-esc="event_dict['event'].name" t-attf-class="#{(not event_dict['event'].website_published) and 'text-danger' or ''}"/>
                    </a>
                    <span t-esc="event_dict['date']"/>
                </li>
            </ul>
            <div t-if="len(events) &gt; 5">
                <t t-if="country">
                    <a t-attf-href="/event?country=#{country.id}" class="small"><b>See all events from <span t-esc="country.name"/></b></a>
                </t>
                <t t-else="">
                    <a href="/event" class="small"><b>View all</b></a>
                </t>
            </div>
        </t>
    </div>
</template>

<!-- Timer widget -->
<template id="display_timer_widget" name="Display Timer Widget">
    <t t-set="pre_countdown_display" t-value="bool(pre_countdown_text) or pre_countdown_display"/>
    <t t-set="pre_countdown_time" t-value="datetime.datetime.now().timestamp() + int(pre_remaining_time)"/>

    <div class="o_display_timer"
         t-att-data-display-class="display_class"
         t-att-data-main-countdown-time="datetime.datetime.now().timestamp() + int(main_remaining_time)"
         t-att-data-main-countdown-text="main_countdown_text"
         t-att-data-main-countdown-display="main_countdown_display"
         t-att-data-pre-countdown-time="pre_countdown_time"
         t-att-data-pre-countdown-display="pre_countdown_display"
         t-att-data-pre-countdown-text="pre_countdown_text">
        <t t-set="remaining_time" t-value="pre_remaining_time if pre_remaining_time else main_remaining_time"/>
        <span class="o_display_timer_countdown d-flex justify-content-center">
            <span class="o_countdown_text pr-1" t-esc="pre_countdown_text if pre_countdown_text else main_countdown_text if not pre_countdown_display else ''"/>
            <div t-if="int(remaining_time) > 86400"
             class="o_countdown_metric_container"><span class="o_countdown_remaining o_timer_days pr-1">0</span><span class="o_countdown_metric pr-1">days</span></div>
            <div t-if="int(remaining_time) > 3600"
                 class="o_countdown_metric_container"><span class="o_countdown_remaining o_timer_hours">00</span><span class="o_countdown_metric">:</span></div>
            <div class="o_countdown_metric_container"><span class="o_countdown_remaining o_timer_minutes">00</span><span class="o_countdown_metric">:</span></div>
            <div class="o_countdown_metric_container"><span class="o_countdown_remaining o_timer_seconds">00</span><span class="o_countdown_metric"></span></div>
        </span>
    </div>
</template>

<template id="display_timer_alert_widget" name="Display Countdown widget">
    <div class="o_we_track_timer alert alert-warning alert-dismissible fade show d-none mx-3 mt-3 mb-0" role="alert" t-att-data-time-to-live="time_to_live">
        Starts <span />
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <i class="fa fa-times"/>
        </button>
    </div>
</template>

</odoo>
