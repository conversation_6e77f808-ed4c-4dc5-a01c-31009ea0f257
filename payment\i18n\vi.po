# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>g <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>hi <<EMAIL>>, 2021
# <PERSON>h <PERSON> <<EMAIL>>, 2021
# Tr<PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# Vo <PERSON>h Thuy, 2022
# Th<PERSON><PERSON>, 2022
# Thin <PERSON>ran <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-10 14:27+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " Đã tìm nạp dữ liệu"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Amount:</b>"
msgstr "<b>Số tiền:</b>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<b>Reference:</b>"
msgstr "<b>Mã:</b>"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>Vui lòng gửi thanh toán cho: </h3><ul><li>Ngân hàng: %s</li><li>Số tài "
"khoản: %s</li><li>Chủ tài khoản: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "<i class=\"fa fa-arrow-circle-right\"/> Back to My Account"
msgstr "<i class=\"fa fa-arrow-circle-right\"/> Trở về tài khoản của tôi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Trang chủ\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "<i class=\"fa fa-trash\"/> Delete"
msgstr "<i class=\"fa fa-trash\"/> Xóa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"
msgstr ""
"<span class=\"badge badge-primary oe_inline "
"o_enterprise_label\">Enterprise</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Đã lưu phương thức thanh toán</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "<span><i class=\"fa fa-arrow-right\"/> Get my Stripe keys</span>"
msgstr "<span><i class=\"fa fa-arrow-right\"/> Lấy khóa Stripe của tôi</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span><i class=\"fa fa-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""
"<span><i class=\"fa fa-arrow-right\"/> Cách định cấu hình tài khoản "
"PayPal</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid ""
"<span>Start selling directly without an account; an email will be sent by "
"Paypal to create your new account and collect your payments.</span>"
msgstr ""
"<span>Bắt đầu bán hàng mà không cần tài khoản; một email sẽ được gửi tới "
"Paypal để tạo tài khoản mới cho bạn và nhận thanh toán của bạn.</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid "<strong>No suitable payment acquirer could be found.</strong>"
msgstr "<strong>Không tìm thấy nhà thanh toán nào phù hợp.</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Không tìm thấy lựa chọn thanh toán phù hợp.</strong><br/>\n"
"                                Nếu bạn tin rằng đây là lỗi, hãy liên hệ với quản trị website. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment acquirer configuration."
msgstr ""
"<strong>Cảnh báo!</strong> Thanh toán này có khoản hoàn tiền đang chờ.\n"
"                        Hãy chờ xử lý thêm một lúc. Nếu hoàn tiền vẫn ở trạng thái chờ trong \n"
"                        vài phút, hãy kiểm tra cấu hình nhà thanh toán của bạn."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid ""
"<strong>Warning</strong> Creating a payment acquirer from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Cảnh báo</strong> Tạo nhà thanh toán từ nút <em>TẠO</em> không được hỗ trợ.\n"
"                        Vui lòng dùng tác vụ <em>Nhân bản</em>."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure your are logged in as the right partner "
"before making this payment."
msgstr ""
"<strong>Cảnh báo</strong> Bảo đảm bạn đã đăng nhập với tư cách đối tác đúng "
"trước khi thực hiện thanh toán này."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""
"<strong>Cảnh báo</strong> Đơn vị tiền tệ bị thiếu hoặc không chính xác."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Cảnh báo</strong> Bạn phải đăng nhập để thanh toán."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "Giao dịch thanh toán với mã %s đã tồn tại."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(acq_name)s)."
msgstr ""
"Yêu cầu hoàn tiền %(amount)s đã được gửi. Thanh toán sẽ sớm được tạo. Mã "
"giao dịch hoàn tiền: %(ref)s (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Cần có mã token để tạo một giao dịch thanh toán mới."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(acq_name)s)."
msgstr "Giao dịch với mã %(ref)s đã được khởi tạo (%(acq_name)s)."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token_name)s (%(acq_name)s)."
msgstr ""
"Giao dịch với mã %(ref)s đã được khởi tạo bằng cách dùng phương thức thanh "
"toán %(token_name)s (%(acq_name)s)."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__access_token
msgid "Access Token"
msgstr "Token truy cập"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "Tài khoản"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Số tài khoản"

#. module: payment
#: code:addons/payment/models/account_payment_method.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
#, python-format
msgid "Acquirer"
msgstr "Nhà thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_id
msgid "Acquirer Account"
msgstr "Tài khoản nhà thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__acquirer_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__acquirer_reference
msgid "Acquirer Reference"
msgstr "Mã nhà thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__acquirer_ids
msgid "Acquirers"
msgstr "Nhà thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Acquirers list"
msgstr "Danh sách nhà thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Activate"
msgstr "Kích hoạt"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Activate Stripe"
msgstr "Kích hoạt Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_active
msgid "Add Extra Fees"
msgstr "Thêm phí bổ sung"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Địa chỉ"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Cho phép lưu phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Amount"
msgstr "Số tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "Khoản hiện có để hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Số tiền tối đa"

#. module: payment
#. openerp-web
#: code:addons/payment/controllers/portal.py:0
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "An error occurred during the processing of this payment."
msgstr "Đã xuất hiện lỗi trong quá trình xử lý thanh toán này."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Apply"
msgstr "Áp dụng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Đã lưu"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "Bạn có chắc muốn xóa phương thức thanh toán này không?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Bạn có chắc chắn muốn vô hiệu giao dịch được ủy quyền không? Không thể hoàn "
"tác tác vụ này."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_authorization
msgid "Authorize Mechanism Supported"
msgstr "Cơ chế ủy quyền được hỗ trợ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__auth_msg
msgid "Authorize Message"
msgstr "Tin nhắn ủy quyền"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Cơ chế ủy quyền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Giao dịch được ủy quyền"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Availability"
msgstr "Khả dụng"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "Ngân hàng"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Bank Accounts"
msgstr "Tài khoản ngân hàng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Tên ngân hàng"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "Mô-đun tài liệu Callback"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "Hoàn tất Callback"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "Callback Hash"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "Phương pháp Callback"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "ID hồ sơ callback"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
#, python-format
msgid "Cancel"
msgstr "Hủy"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Đã huỷ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__cancel_msg
msgid "Canceled Message"
msgstr "Tin nhắn đã hủy"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Cancelled payments"
msgstr "Đã hủy thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__capture_manually
msgid "Capture Amount Manually"
msgstr "Ghi số tiền theo cách thủ công"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Ghi giao dịch"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Ghi nhận số tiền từ Odoo, khi giao hàng hoàn tất.\n"
"Dùng tính năng này nếu bạn muốn tính phí khách hàng chỉ khi\n"
"bạn chắc chắn có thể giao hàng cho họ."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Check here"
msgstr "Chọn tại đây"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_open_payment_onboarding_payment_acquirer_wizard
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Chọn phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Thành phố"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Click here to be redirected to the confirmation page."
msgstr "Bấm vào đây để chuyển hướng đến trang xác nhận."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
msgid "Close"
msgstr "Đóng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Mã"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__color
msgid "Color"
msgstr "Màu sắc"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Communication"
msgstr "Liên lạc"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__company_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Công ty"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Configuration"
msgstr "Cấu hình"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Configure"
msgstr "Cấu hình"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "Xác nhận xóa"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Đã được xác nhận"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_id
msgid "Corresponding Module"
msgstr "Mô-đun tương ứng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__country_ids
msgid "Countries"
msgstr "Quốc gia"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Quốc gia"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Tạo token"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_acquirer
msgid "Create a new payment acquirer"
msgstr "Tạo một nhà thanh toán mới"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "Create a new payment token"
msgstr "Tạo token thanh toán mới"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_icon
msgid "Create a payment icon"
msgstr "Tạo một biểu tượng thanh toán mới"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Tạo bởi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__create_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Thời điểm tạo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Credentials"
msgstr "Thông tin đăng nhập"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_stripe
msgid "Credit & Debit Card"
msgstr "Thẻ tín dụng & ghi nợ"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_adyen
msgid "Credit Card (powered by Adyen)"
msgstr "Thẻ tín dụng (cung cấp bởi Adyen)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_alipay
msgid "Credit Card (powered by Alipay)"
msgstr "Thẻ tín dụng (cung cấp bởi Alipay)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_authorize
msgid "Credit Card (powered by Authorize)"
msgstr "Thẻ tín dụng (cung cấp bởi Authorize)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_buckaroo
msgid "Credit Card (powered by Buckaroo)"
msgstr "Thẻ tín dụng (cung cấp bởi Buckaroo)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_ogone
msgid "Credit Card (powered by Ogone)"
msgstr "Thẻ tín dụng (cung cấp bởi Ogone)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payulatam
msgid "Credit Card (powered by PayU Latam)"
msgstr "Thẻ tín dụng (cung cấp bởi PayU Latam)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_payumoney
msgid "Credit Card (powered by PayUmoney)"
msgstr "Thẻ tín dụng (cung cấp bởi PayUmoney)"

#. module: payment
#: model:payment.acquirer,display_as:payment.payment_acquirer_sips
msgid "Credit Card (powered by Sips)"
msgstr "Thẻ tín dụng (cung cấp bởi Sips)"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__stripe
msgid "Credit card (via Stripe)"
msgstr "Thẻ tín dụng (thông qua Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Tùy chỉnh giới thiệu phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Khách hàng"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__sequence
msgid "Define the display order"
msgstr "Xác định trình tự xuất hiện"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__description
msgid "Description"
msgstr "Mô tả"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__display_as
msgid "Description of the acquirer for customers"
msgstr "Mô tả nhà thanh toán cho khách hàng"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Disabled"
msgstr "Đã vô hiệu"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Dismiss"
msgstr "Bỏ qua"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_name
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_icon__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__display_as
msgid "Displayed as"
msgstr "Hiển thị là"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__done
msgid "Done"
msgstr "Hoàn thành"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__done_msg
msgid "Done Message"
msgstr "Hoàn tất tin nhắn"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Nháp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "Email"
msgstr "Email"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr "Bật thanh toán thẻ tín dụng &amp; ghi nợ được hỗ trợ bởi Stripe"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__enabled
msgid "Enabled"
msgstr "Đã bật"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Lỗi"

#. module: payment
#. openerp-web
#: code:addons/payment/models/payment_transaction.py:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Error: %s"
msgstr "Lỗi: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__fees
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Fees"
msgstr "Phí"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_fees_computation
msgid "Fees Computation Supported"
msgstr "Hỗ trợ tính toán phí"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_fixed
msgid "Fixed domestic fees"
msgstr "Phí cố định trong nước"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_fixed
msgid "Fixed international fees"
msgstr "Phí cố định quốc tế"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__acquirer_id
msgid "Force Payment Acquirer"
msgstr "Buộc người nhận thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_link_wizard__acquirer_id
msgid ""
"Force the customer to pay via the specified payment acquirer. Leave empty to"
" allow the customer to choose among all acquirers."
msgstr ""
"Buộc khách hàng thanh toán qua nhà thanh toán cụ thể. Để trống để cho phép "
"khách hàng chọn nhà thanh toán."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "From"
msgstr "Từ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__full_only
msgid "Full Only"
msgstr "Full Only"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Tạo liên kế thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Tạo liên kết thanh toán bán hàng"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Tạo liên kết thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Routing"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__has_multiple_acquirers
msgid "Has Multiple Acquirers"
msgstr "Có nhiều nhà thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "Có khoản hoàn tiền đang chờ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "Khoản thanh toán đã được xử lý sau"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pre_msg
msgid "Help Message"
msgstr "Thông điệp trợ giúp"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__new_user
msgid "I don't have a Paypal account"
msgstr "Tôi không có tài khoản Paypal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__paypal_user_type__existing_user
msgid "I have a Paypal account"
msgstr "Tôi có tài khoản Paypal"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__id
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_icon__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "If not defined, the acquirer name will be used."
msgstr "Nếu chưa được xác định, tên của nhà thanh toán sẽ được sử dụng."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "Nếu thanh toán chưa được xác nhận, bạn có thể liên hệ với chúng tôi."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "Nếu bạn cho rằng đây là lỗi, hãy liên hệ quản trị website."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__image_128
#: model:ir.model.fields,field_description:payment.field_payment_icon__image
msgid "Image"
msgstr "Hình ảnh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_icon__image_payment_form
msgid "Image displayed on the payment form"
msgstr "Hình ảnh hiển thị trên thanh toán từ"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,help:payment.field_payment_acquirer__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the acquirer."
msgstr ""
"Ở chế độ thử nghiệm, thanh toán giả sẽ được xử lý thông qua giao diện thanh toán thử nghiệm.\n"
"Khuyên dùng chế độ này khi cài đặt nhà thanh toán."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__inline_form_view_id
msgid "Inline Form Template"
msgstr "Mẫu biểu mẫu nội tuyến"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Install"
msgstr "Cài đặt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_state
msgid "Installation State"
msgstr "Trạng thái cài đặt"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Installed"
msgstr "Đã cài đặt"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "Lỗi máy chủ nội bộ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Hóa đơn"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Hóa đơn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "Số lượng hóa đơn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "Được xử lý sau"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "Nó hiện được liên kết với các tài liệu sau:"

#. module: payment
#: model:ir.model,name:payment.model_account_journal
msgid "Journal"
msgstr "Sổ kế toán"

#. module: payment
#: model:ir.model,name:payment.model_account_move
msgid "Journal Entry"
msgstr "Bút toán "

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__just_done
msgid "Just done"
msgstr "Vừa xong"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Landing Route"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Ngôn ngữ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer____last_update
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_icon____last_update
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard____last_update
#: model:ir.model.fields,field_description:payment.field_payment_token____last_update
#: model:ir.model.fields,field_description:payment.field_payment_transaction____last_update
msgid "Last Modified on"
msgstr "Sửa lần cuối vào"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Ngày cập nhật trạng thái gần nhất"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__write_date
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_icon__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Leave empty to allow all acquirers"
msgstr "Để trống để cho phép tất cả nhà thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay_meth_link
msgid "Manage payment methods"
msgstr "Quản lý phương thức thanh toán"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Thủ công"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "Khoản hoàn tiền tối đa được phép"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Merchant Account ID"
msgstr "ID tài khoản người bán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Thông báo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Messages"
msgstr "Tin nhắn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Phương thức"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Multiple payment options selected"
msgstr "Nhiều tùy chọn thanh toán đã được chọn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__name
#: model:ir.model.fields,field_description:payment.field_payment_icon__name
#: model:ir.model.fields,field_description:payment.field_payment_token__name
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Name"
msgstr "Tên"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__provider__none
msgid "No Provider Set"
msgstr "Không có nhà cung cấp được đặt"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Acquirer menu."
msgstr ""
"Không thể tìm thấy phương thức thanh toán thủ công cho công ty này. Vui lòng"
" tạo trong menu Nhà thanh toán."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "No payment has been processed."
msgstr "Không có thanh toán nào đã được xử lý."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "No payment option selected"
msgstr "Không có tùy chọn thanh toán được chọn"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_acquirer_onboarding_state__not_done
msgid "Not done"
msgstr "Chưa xong"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "Not verified"
msgstr "Không được xác minh"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from acquirers allowing to capture the amount are "
"available."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from acquirers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"Lưu ý rằng mã bảo mật từ tổ chức thanh toán được đặt chỉ ủy quyền giao dịch "
"(thay vì nắm bắt số tiền) không có sẵn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Mô-đun Odoo Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Thanh toán offline bằng token"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_ogone
msgid "Ogone"
msgstr "Ogone"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
#, python-format
msgid "Ok"
msgstr "Đồng ý"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online Payments"
msgstr "Thanh toán trực tuyến"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Thanh toán trực tiếp trực tuyến"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Thanh toán trực tuyến bằng token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Thanh toán trực tuyến với chuyển hướng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.onboarding_payment_acquirer_step
msgid "Online payments enabled"
msgstr "Thanh toán trực tuyến đã bật"

#. module: payment
#: code:addons/payment/wizards/payment_acquirer_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "Chỉ có admin mới có thể truy cập vào dữ liệu này."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be captured."
msgstr "Chỉ giao dịch được cấp quyền mới được ghi nhận."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "Chỉ các giao dịch được ủy quyền mới được hủy bỏ."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "Chỉ các giao dịch đã xác nhận mới có thể được hoàn tiền."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Hoạt động"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Khác"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__other
msgid "Other payment acquirer"
msgstr "Nhà thanh toán khác"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "Mã token nhận dạng PDT"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__support_refund__partial
msgid "Partial"
msgstr "Một phần"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Đối tác"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Tên đối tác"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Pay"
msgstr "Thanh toán"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:payment.acquirer,name:payment.payment_acquirer_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payulatam
msgid "PayU Latam"
msgstr "PayU Latam"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_payumoney
msgid "PayUmoney"
msgstr "PayUmoney"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Acquirer"
msgstr "Dịch vụ thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_acquirer
#: model:ir.ui.menu,name:payment.payment_acquirer_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_list
msgid "Payment Acquirers"
msgstr "Dịch vụ thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__available_acquirer_ids
msgid "Payment Acquirers Available"
msgstr "Nhà thanh toán khả dụng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Tổng thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Followup"
msgstr "Payment Followup"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Payment Form"
msgstr "Biểu mẫu thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_icon
#: model_terms:ir.ui.view,arch_db:payment.payment_icon_form
msgid "Payment Icon"
msgstr "Biểu tượng thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_icon
#: model:ir.ui.menu,name:payment.payment_icon_menu
msgid "Payment Icons"
msgstr "Biểu tượng thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Chỉ dẫn thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__journal_id
msgid "Payment Journal"
msgstr "Sổ thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Liên kết thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Phương thức thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_method_line
msgid "Payment Methods"
msgstr "Phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__description
msgid "Payment Ref"
msgstr "Mã thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "Hướng dẫn thanh toán hoàn tiền"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Token thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Số token thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model:ir.ui.menu,name:payment.payment_token_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Token thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__transaction_id
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
msgid "Payment Transaction"
msgstr "Giao dịch thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model:ir.ui.menu,name:payment.payment_transaction_menu
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Giao dịch thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Giao dịch thanh toán liên kết với token"

#. module: payment
#: model:ir.model,name:payment.model_payment_acquirer_onboarding_wizard
msgid "Payment acquire onboarding wizard"
msgstr "Cửa sổ tổ chức thanh toán mới"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__payment_acquirer_selection
msgid "Payment acquirer selected"
msgstr "Nhà thanh toán đã chọn"

#. module: payment
#: model:ir.model,name:payment.model_account_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Thanh toán"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments failed"
msgstr "Thanh toán thất bại"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Payments received"
msgstr "Thanh toán đã nhận"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__paypal_user_type
msgid "Paypal User Type"
msgstr "Loại người dùng Paypal"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Đang chờ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__pending_msg
msgid "Pending Message"
msgstr "Tin nhắn đang chờ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Điện thoại"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select a payment option."
msgstr "Chọn một tùy chọn thanh toán."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Please select only one payment option."
msgstr "Hãy chỉ chọn một tùy chọn thanh toán."

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount smaller than %s."
msgstr "Hãy đặt một khoản nhỏ hơn %s."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "Please switch to company '%s' to make this payment."
msgstr ""

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the following transfer details"
msgstr "Vui lòng sử dụng các chi tiết chuyển khoản sau"

#. module: payment
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_transfer
msgid "Please use the order name as communication reference."
msgstr "Vui lòng sử dụng tên đơn hàng làm tham chiếu giao tiếp."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Please wait ..."
msgstr "Vui lòng đợi ..."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Xử lý bởi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__provider
#: model:ir.model.fields,field_description:payment.field_payment_token__provider
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
msgid "Provider"
msgstr "Nhà cung cấp"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Reason:"
msgstr "Lý do:"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr "Lý do: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Chuyển hướng biểu mẫu"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Reference"
msgstr "Mã"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Mã phải là duy nhất!"

#. module: payment
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/account_payment.py:0
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#: model_terms:ir.ui.view,arch_db:payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "Hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "Khoản hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "Khoản đã hoàn tiền"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__refunds_count
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Số hoàn tiền"

#. module: payment
#: model:ir.model,name:payment.model_account_payment_register
msgid "Register Payment"
msgstr "Ghi nhận thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID tài liệu liên quan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Model tài liệu liên quan"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "SEPA Direct Debit"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_account_journal_form
msgid "SETUP"
msgstr "CÀI ĐẶT"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.manage
msgid "Save Payment Method"
msgstr "Lưu phương thức thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.checkout
msgid "Save my payment details"
msgstr "Lưu chi tiết thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "Token thanh toán đã lưu"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Token thanh toán đã lưu"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Select countries. Leave empty to use everywhere."
msgstr "Chọn quốc gia. Để trống để sử dụng  mọi nơi."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Chọn phương thức thanh toán mới"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__sequence
#: model:ir.model.fields,field_description:payment.field_payment_icon__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "Server Error"
msgstr "Lỗi máy chủ"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Server error:"
msgstr "Lỗi máy chủ:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr "Hiển thị Cho phép token hoá"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_auth_msg
msgid "Show Auth Msg"
msgstr "Hiển thị Thông báo uỷ quyền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "Hiển thị Thông báo huỷ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_credentials_page
msgid "Show Credentials Page"
msgstr "Hiển thị Trang thông tin đăng nhập"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_done_msg
msgid "Show Done Msg"
msgstr "Hiển thị Thông báo hoàn tất"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_payment_icon_ids
msgid "Show Payment Icon"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pending_msg
msgid "Show Pending Msg"
msgstr "Hiển thị Thông báo treo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__show_pre_msg
msgid "Show Pre Msg"
msgstr "Hiển thị Thông báo pre"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "Thanh toán gốc"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Giao dịch nguồn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment_method_line__payment_acquirer_state
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Tỉnh/TP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_acquirer_onboarding_state
msgid "State of the onboarding payment acquirer step"
msgstr "Trạng thái các bước thanh toán mới của tổ chức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Trạng thái"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.acquirer,name:payment.payment_acquirer_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr "Stripe Publishable Key"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr "Stripe Secret Key"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "Token thanh toán phù hợp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__payment_icon_ids
msgid "Supported Payment Icons"
msgstr "Hỗ trợ biểu tượng thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,help:payment.field_account_payment_register__use_electronic_payment_method
msgid "Technical field used to hide or show the payment_token_id if needed."
msgstr ""

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_test
msgid "Test"
msgstr "Kiểm thử"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_acquirer__state__test
#: model_terms:ir.ui.view,arch_db:payment.checkout
#: model_terms:ir.ui.view,arch_db:payment.manage
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
msgid "Test Mode"
msgstr "Chế độ kiểm thử"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__provider
#: model:ir.model.fields,help:payment.field_payment_token__provider
#: model:ir.model.fields,help:payment.field_payment_transaction__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Access token không hợp lệ."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__acquirer_ref
msgid "The acquirer reference of the token of the transaction"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__acquirer_reference
msgid "The acquirer reference of the transaction"
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "Số tiền cần hoàn lại phải lớn hơn 0 và không thể vượt quá %s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__name
msgid "The anonymized acquirer reference of the payment method"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__color
msgid "The color of the card in kanban view"
msgstr "Màu của thẻ trong chế độ xem kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "Thông tin bổ sung về trạng thái"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__country_ids
msgid ""
"The countries for which this payment acquirer is available.\n"
"If none is set, it is available for all countries."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__description
msgid "The description shown in the card in kanban view "
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__fees
msgid "The fees amount; set by the system as it depends on the acquirer"
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "Các trường sau phải được điền: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "Tham chiếu nội bộ của giao dịch"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__journal_id
msgid "The journal in which the successful transactions are posted"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__acquirer_ids
msgid "The list of acquirers supporting this payment icon"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "Thông báo hiển thị nếu thanh toán được ủy quyền"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr "Thông báo hiển thị nếu đơn hàng bị hủy trong quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"Thông báo hiển thị nếu đơn hàng được đặt thành công sau quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "Thông báo hiển thị nếu đơn hàng còn treo sau quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "Thông báo hiển thị để giải thích và hỗ trợ quá trình thanh toán"

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"Thanh toán phải được thực hiện trực tiếp, có chuyển hướng, hoặc được thực "
"hiện bằng token."

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "The related payment is posted: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "Trang người dùng được chuyển hướng đến sau giao dịch"

#. module: payment
#: model:ir.model.fields,help:payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "Thanh toán gốc của các khoản thanh toán hoàn tiền liên quan"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of related refund transactions"
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"Mẫu kết xuất biểu mẫu đã gửi để chuyển hướng người dùng khi thực hiện thanh "
"toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""
"Mẫu kết xuất biểu mẫu thanh toán nội tuyến khi tạo thanh toán trực tiếp"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is canceled "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s is pending "
"(%(acq_name)s)."
msgstr ""

#. module: payment
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "The value of the payment amount must be positive."
msgstr "Giá trị thanh toán phải là số dương."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Không có giao dịch để hiển thị"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Không có khoản thanh toán nào. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_acquirer__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"acquirer's database, allowing the customer to reuse it for a next purchase."
msgstr ""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_icon__image
#: model:ir.model.fields,help:payment.field_payment_icon__image_payment_form
msgid ""
"This field holds the image used for this payment icon, limited to 64x64 px"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment "
"acquirers. Setting an email for this partner is advised."
msgstr ""
"Đối tác không có email, việc này có thể gây vấn đề cho một số nhà thanh "
"toán. Bạn nên đặt email cho đối tác này."

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "This payment has not been processed yet."
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has been verified by our system."
msgstr "Hệ thống của chúng tôi đã xác minh phương thức thanh toán này."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.verified_token_checkmark
msgid "This payment method has not been verified by our system."
msgstr "Hệ thống của chúng tôi chưa xác minh phương thức thanh toán này."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "This transaction has been cancelled."
msgstr "Giao dịch này đã bị hủy."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_tokenization
msgid "Tokenization Supported"
msgstr "Hỗ trợ token hoá"

#. module: payment
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"acquirers: %s"
msgstr ""

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Giao dịch"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "Loại hoàn tiền được hỗ trợ"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the Odoo server."
msgstr "Không thể kết nối với máy chủ Odoo."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_form
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_kanban
msgid "Upgrade"
msgstr "Nâng cấp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "Sử dụng phương thức thanh toán điện tử"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Xác thực phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_dom_var
msgid "Variable domestic fees (in percents)"
msgstr "Phí nội địa thay đổi (tính bằng phần trăm)"

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#, python-format
msgid "Variable fees must always be positive and below 100%."
msgstr "Phí không cố định phải luôn lớn hơn 0 và dưới 100%."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_acquirer__fees_int_var
msgid "Variable international fees (in percents)"
msgstr "Phí quốc tế (tính theo phần trăm)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__verified
msgid "Verified"
msgstr "Đã xác thực"

#. module: payment
#: model:ir.model,name:payment.model_ir_ui_view
msgid "View"
msgstr "Xem"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.account_invoice_view_form_inherit_payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Giao dịch trống"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Waiting for payment"
msgstr "Đang đợi thành toán"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "Warning!"
msgstr "Cảnh báo!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to delete your payment method."
msgstr "Chúng tôi không thể xoá phương thức thanh toán của bạn."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr ""
"Chúng tôi không thể tìm khoản thanh toán của bạn, nhưng đừng lo lắng nhé."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/payment_form_mixin.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "Chúng tôi không thể xử lý thanh toán của bạn."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/manage_form.js:0
#, python-format
msgid "We are not able to save your payment method."
msgstr "Chúng tôi không thể lưu phương thức thanh toán của bạn."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment, please wait ..."
msgstr "Chúng tôi đang xử lý thanh toán của bạn, vui lòng đợi ..."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are waiting for the payment acquirer to confirm the payment."
msgstr "Chúng tôi đang chờ nhà thanh toán xác nhận khoản thanh toán."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr ""
"Có nên tạo token thanh toán trong giai đoạn xử lý sau giao dịch hay không"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "Lệnh gọi lại đã được thực hiện chưa"

#. module: payment
#: model:payment.acquirer,name:payment.payment_acquirer_transfer
msgid "Wire Transfer"
msgstr "Chuyển khoản ngân hàng"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You can click here to be redirected to the confirmation page."
msgstr "Bạn có thể bấm vào đây để chuyển hướng đến trang xác nhận."

#. module: payment
#: code:addons/payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to an acquirer in the enabled or test state.\n"
"Linked acquirer(s): %s"
msgstr ""

#. module: payment
#: code:addons/payment/models/ir_ui_view.py:0
#, python-format
msgid "You cannot delete a view that is used by a payment acquirer."
msgstr ""

#. module: payment
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr "Bạn không có quyền truy cập token thanh toán này. "

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr ""
"Bạn sẽ nhận được email xác nhận khoản thanh toán của bạn trong vài phút nữa."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is confirmed."
msgstr "Bạn sẽ được thông báo khi thanh toán được xác nhận. "

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You will be notified when the payment is fully confirmed."
msgstr "Bạn sẽ được thông báo khi thanh toán được xác nhận đầy đủ."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order has been processed."
msgstr "Đơn đặt hàng của bạn đã được xử lý."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your order is being processed, please wait ..."
msgstr "Đơn đặt hàng của bạn đang được xử lý, vui lòng chờ ..."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,auth_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been authorized."
msgstr "Thanh toán của bạn đã được chứng thực."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,cancel_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been cancelled."
msgstr "Thanh toán của bạn đã bị hủy."

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment has been received but need to be confirmed manually."
msgstr "Thanh toán của bạn đã được nhận nhưng cần xác nhận thủ công."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,pending_msg:payment.payment_acquirer_test
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Thanh toán của bạn đã được xử lý thành công nhưng đang chờ phê duyệt."

#. module: payment
#: code:addons/payment/models/payment_acquirer.py:0
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_adyen
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_alipay
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_authorize
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_buckaroo
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_mollie
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_ogone
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_paypal
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payulatam
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_payumoney
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sepa_direct_debit
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_sips
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_stripe
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_test
#: model_terms:payment.acquirer,done_msg:payment.payment_acquirer_transfer
#, python-format
msgid "Your payment has been successfully processed. Thank you!"
msgstr "Thanh toán của bạn đã được xử lý thành công. Cảm ơn bạn!"

#. module: payment
#. openerp-web
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Your payment is in pending state."
msgstr "Thanh toán của bạn đang ở trạng thái chờ."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "ZIP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "Zip"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
#: model:ir.cron,cron_name:payment.cron_post_process_payment_tx
#: model:ir.cron,name:payment.cron_post_process_payment_tx
msgid "payment: post-process transactions"
msgstr "thanh toán: xử lý sau giao dịch"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show less"
msgstr "hiển thị ít hơn"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.icon_list
msgid "show more"
msgstr "hiển thị thêm"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_acquirer_onboarding_wizard_form
msgid "to choose another payment method."
msgstr "để chọn phương thức thanh toán khác."
