# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * event_booth_sale
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_booth_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"저장 가능 제품은 재고를 관리하는 품목입니다. 창고 앱이 설치되어 있어야 합니다.\n"
"소모품은 재고가 관리되지 않는 품목입니다.\n"
"서비스는 귀하가 제공하는 비물질 품목입니다."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_ids
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__event_booth_id
msgid "Booth"
msgstr "부스"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_id
msgid "Booth Category"
msgstr "부스 카테고리"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr "사용할 수 있는 부스를 확인할 수 있는 부스 카테고리입니다. 프론트엔드에서 사용합니다."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_count
msgid "Booth Count"
msgstr "부스 수"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_category_view_form
msgid "Booth Details"
msgstr "부스 세부 정보"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_tree
msgid "Booth Registration"
msgstr "부스 등록"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_ids
#: model_terms:ir.ui.view,arch_db:event_booth_sale.sale_order_view_form
msgid "Booths"
msgstr "부스"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_category_id
msgid "Booths Category"
msgstr "부스 카테고리"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Cancel"
msgstr "취소"

#. module: event_booth_sale
msgid "Configure an event booth"
msgstr ""

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_ids
msgid "Confirmed Booths"
msgstr "확정된 부스"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_registration_ids
msgid "Confirmed Registration"
msgstr "등록 확정"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Contact"
msgstr "연락처"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_email
msgid "Contact Email"
msgstr "연락처 이메일"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_mobile
msgid "Contact Mobile"
msgstr "연락처 핸드폰 번호"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_name
msgid "Contact Name"
msgstr "담당자 이름"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_phone
msgid "Contact Phone"
msgstr "연락 번호"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_uid
msgid "Created by"
msgstr "작성자"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_date
msgid "Created on"
msgstr "작성일자"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__currency_id
msgid "Currency"
msgstr "통화"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__partner_id
msgid "Customer"
msgstr "고객"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Details"
msgstr "세부 정보"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__display_name
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__display_name
msgid "Display Name"
msgstr "표시명"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_id
msgid "Event"
msgstr "행사"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth
#: model:ir.model.fields.selection,name:event_booth_sale.selection__product_template__detailed_type__event_booth
#: model:product.product,name:event_booth_sale.product_product_event_booth
msgid "Event Booth"
msgstr "행사 부스"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_category
msgid "Event Booth Category"
msgstr "행사 부스 카테고리"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "사용할 수 있는 행사 부스 카테고리"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_configurator
msgid "Event Booth Configurator"
msgstr "행사 부스 구성기"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_registration
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__event_booth_registration_ids
msgid "Event Booth Registration"
msgstr "이벤트 부스 등록"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_type_booth
msgid "Event Booth Template"
msgstr "행사 부스 서식"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_id
msgid "Final Sale Order Line"
msgstr "최종 판매주문서 내역"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__id
msgid "ID"
msgstr "ID"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__image_1920
msgid "Image"
msgstr "이미지"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__is_event_booth
msgid "Is Event Booth"
msgstr "행사 부스임"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__is_paid
msgid "Is Paid"
msgstr "결제 완료"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_account_move
msgid "Journal Entry"
msgstr "분개"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator____last_update
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Ok"
msgstr "확인"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_id
msgid "Order Reference"
msgstr "주문 참조"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Paid"
msgstr "지불됨"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Pending Booths"
msgstr "대기 중인 부스"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__price
msgid "Price"
msgstr "가격"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce
msgid "Price Reduce"
msgstr "가격 인하"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_product
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__product_id
msgid "Product"
msgstr "품목"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_template
msgid "Product Template"
msgstr "품목 양식"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_booth_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "품목 유형"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Registrations"
msgstr "등록"

#. module: event_booth_sale
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid "Registrations from the same Order Line must belong to a single event."
msgstr "동일한 주문 내역에 대한 등록은 단일 행사에 속해 있어야 합니다"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_registration_ids
msgid "SO Lines with reservations"
msgstr "예약된 판매주문서 내역"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Sale Order"
msgstr "판매 주문"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__sale_order_line_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__sale_order_line_id
msgid "Sale Order Line"
msgstr "판매 주문 내역"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order
msgid "Sales Order"
msgstr "판매 주문"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: event_booth_sale
#: model:ir.actions.act_window,name:event_booth_sale.event_booth_configurator_action
msgid "Select an event booth"
msgstr "행사 부스 선택"

#. module: event_booth_sale
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid ""
"The following booths are unavailable, please remove them to continue : "
"%(booth_names)s"
msgstr "다음 부스는 사용하실 수 없습니다. 계속하시려면 삭제하시기 바랍니다: %(booth_names)s"

#. module: event_booth_sale
#: model:ir.model.constraint,message:event_booth_sale.constraint_event_booth_registration_unique_registration
msgid "There can be only one registration for a booth by sale order line"
msgstr "부스는 판매주문서 내역별로 하나만 등록하실 수 있습니다."

#. module: event_booth_sale
#: model:product.product,uom_name:event_booth_sale.product_product_event_booth
msgid "Units"
msgstr "단위"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Used to create registration when providing the desired event booth."
msgstr "희망하시는 행사 부스에 대해서 등록 항목을 생성할 때 사용합니다."

#. module: event_booth_sale
#: code:addons/event_booth_sale/models/event_booth.py:0
#, python-format
msgid ""
"You can't delete the following booths as they are linked to sales orders: "
"%(booths)s"
msgstr "다음의 부스는 판매주문서와 연결되어 있으므로 삭제할 수 없습니다: %(booths)s"

#. module: event_booth_sale
#: code:addons/event_booth_sale/wizard/event_booth_configurator.py:0
#, python-format
msgid "You have to select at least one booth."
msgstr "최소한 하나의 부스를 선택하셔야 합니다."

#. module: event_booth_sale
#: code:addons/event_booth_sale/models/event_booth_registration.py:0
#, python-format
msgid ""
"Your order has been cancelled because the following booths have been "
"reserved"
msgstr "다음 "
