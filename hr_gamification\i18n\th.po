# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_gamification
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid ""
"A goal is defined by a user and a goal type.\n"
"                    Goals can be created automatically by using challenges."
msgstr ""
"เป้าหมายถูกกำหนดโดยผู้ใช้และประเภทเป้าหมาย\n"
"                    เป้าหมายสามารถสร้างขึ้นได้โดยอัตโนมัติโดยใช้ความท้าทาย"

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__badge_ids
msgid ""
"All employee badges, linked to the employee either directly or through the "
"user"
msgstr "ป้ายพนักงานทั้งหมดเชื่อมโยงกับพนักงานโดยตรงหรือผ่านผู้ใช้"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                    The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                    The goals are created for the specified users or member of the group."
msgstr ""
"กำหนดรายการเป้าหมายให้กับผู้ใช้ที่เลือกเพื่อประเมิน\n"
"                    ความท้าทายสามารถใช้ระยะเวลา (รายสัปดาห์ รายเดือน...) เพื่อสร้างเป้าหมายโดยอัตโนมัติ\n"
"                    เป้าหมายถูกสร้างขึ้นสำหรับผู้ใช้ที่ระบุหรือสมาชิกของกลุ่ม"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:hr_gamification.gamification_badge_menu_hr
msgid "Badges"
msgstr "ป้าย"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid ""
"Badges are rewards of good work. Give them to people you believe deserve it."
msgstr "ป้ายเป็นรางวัลของการทำงานที่ดี มอบให้กับคนที่คุณเชื่อว่าสมควรได้รับ"

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,help:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Badges directly linked to the employee"
msgstr "ป้ายเชื่อมโยงโดยตรงกับพนักงาน"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_hr_employee_base
msgid "Basic Employee"
msgstr "พนักงานทั่วไป"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Cancel"
msgstr "ยกเลิก"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.challenge_list_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_challenge_menu_hr
#: model:ir.ui.menu,name:hr_gamification.menu_hr_gamification
msgid "Challenges"
msgstr "ความท้าทาย"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.challenge_list_action2
msgid "Create a new challenge"
msgstr "สร้างความท้าทายใหม่"

#. module: hr_gamification
#: model_terms:ir.actions.act_window,help:hr_gamification.goals_menu_groupby_action2
msgid "Create a new goal"
msgstr "สร้างเป้าหมายใหม่"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Describe what they did and why it matters (will be public)"
msgstr "อธิบายว่าพวกเขาทำอะไรและเหตุใดจึงสำคัญ (จะเป็นสาธารณะ)"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__direct_badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__direct_badge_ids
msgid "Direct Badge"
msgstr "ป้ายโดยตรง"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user__employee_id
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__employee_id
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__badge_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__badge_ids
msgid "Employee Badges"
msgstr "ป้ายพนักงาน"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__goal_ids
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__goal_ids
msgid "Employee HR Goals"
msgstr "เป้าหมายของพนักงาน HR"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "ป้ายรางวัลในรูปแบบเกม"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "ป้ายผู้ใช้รูปแบบเกม"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "ตัวช่วยป้ายผู้ใช้รูปแบบเกม"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_res_users__goal_ids
msgid "Goal"
msgstr "เป้าหมาย"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.goals_menu_groupby_action2
#: model:ir.ui.menu,name:hr_gamification.gamification_goal_menu_hr
msgid "Goals History"
msgstr "ประวัติเป้าหมาย"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant a Badge"
msgstr "มอบป้ายรางวัล"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Grant this employee his first badge"
msgstr "มอบป้ายรางวัลแรกให้พนักงานคนนี้"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_badge_form_view
msgid "Granted"
msgstr "มอบแล้ว"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge__granted_employees_count
msgid "Granted Employees Count"
msgstr "จำนวนพนักงานที่ได้รับ"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_base__has_badges
#: model:ir.model.fields,field_description:hr_gamification.field_hr_employee_public__has_badges
msgid "Has Badges"
msgstr "มีป้าย"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "Received Badges"
msgstr "ได้รับป้ายรางวัล"

#. module: hr_gamification
#: model:ir.model.fields,help:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "ชื่อผู้ใช้ที่เกี่ยวข้องสำหรับทรัพยากรเพื่อจัดการการเข้าถึง"

#. module: hr_gamification
#: model:ir.actions.act_window,name:hr_gamification.action_reward_wizard
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee"
msgstr "ให้รางวัลพนักงาน"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "Reward Employee with"
msgstr "ให้รางวัลพนักงานด้วย"

#. module: hr_gamification
#: code:addons/hr_gamification/models/gamification.py:0
#, python-format
msgid "The selected employee does not correspond to the selected user."
msgstr "พนักงานที่เลือกไม่ตรงกับผู้ใช้ที่เลือก"

#. module: hr_gamification
#: model:ir.model.fields,field_description:hr_gamification.field_gamification_badge_user_wizard__user_id
msgid "User"
msgstr "ผู้ใช้งาน"

#. module: hr_gamification
#: model:ir.model,name:hr_gamification.model_res_users
msgid "Users"
msgstr "ผู้ใช้งาน"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.view_badge_wizard_reward
msgid "What are you thankful for?"
msgstr "คุณรู้สึกขอบคุณอะไร?"

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can not send a badge to yourself."
msgstr "คุณไม่สามารถส่งป้ายรางวัลให้ตัวเองได้"

#. module: hr_gamification
#: code:addons/hr_gamification/wizard/gamification_badge_user_wizard.py:0
#, python-format
msgid "You can send badges only to employees linked to a user."
msgstr "คุณสามารถส่งป้ายรางวัลให้เฉพาะพนักงานที่เชื่อมโยงกับผู้ใช้เท่านั้น"

#. module: hr_gamification
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_gamification.hr_hr_employee_view_form
msgid "to reward this employee for a good action"
msgstr "เพื่อให้รางวัลแก่พนักงานสำหรับการกระทำที่ดี"
