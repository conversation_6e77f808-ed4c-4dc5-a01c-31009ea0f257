# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>essel<PERSON>ch, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-05 14:50+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00
msgid "0% Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-G
msgid "0% Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-CC
msgid "0% Cocont."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-CC
msgid "0% Cocont. - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-CC
msgid "0% Cocont. M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-CC
msgid "0% Cocont. S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-EU
msgid "0% EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-EU-G
msgid "0% EU - Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-EU
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-L
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-L
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-L
msgid "0% EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-S
msgid "0% EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-EU-T
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-EU-T
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-EU-T
msgid "0% EU T."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00
msgid "0% M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-ROW
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-ROW
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-ROW
msgid "0% Non EU"
msgstr "0% Nicht EU"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-00-ROW-CC
msgid "0% Non EU - Biens d'investissement"
msgstr "0% Nicht EU - Investitionsgüter"

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-00-ROW-CC
msgid "0% Non EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-ROW-CC
msgid "0% Non EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-00-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-00-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-00-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-00-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-00-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-00-S
msgid "0% S."
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_00
msgid "00"
msgstr "00"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_00
msgid "00 - Opérations soumises à un régime particulier"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_01
msgid "01"
msgstr "01"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_01
msgid "01 - Opérations avec TVA à 6%"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_02
msgid "02"
msgstr "02"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_02
msgid "02 - Opérations avec TVA à 12%"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_03
msgid "03"
msgstr "03"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_03
msgid "03 - Opérations avec TVA à 21%"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-12-L
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-12-L
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-12-L
msgid "12%"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12
msgid "12% Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-G
msgid "12% Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-CC
msgid "12% Cocont. - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-CC
msgid "12% Cocont. M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-CC
msgid "12% Cocont. S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-EU
msgid "12% EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-EU-G
msgid "12% EU - Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-EU
msgid "12% EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-EU-S
msgid "12% EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12
msgid "12% M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-12-ROW-CC
msgid "12% Non EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-12-ROW-CC
msgid "12% Non EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-ROW-CC
msgid "12% Non EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-12-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-12-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-12-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-12-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-12-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-12-S
msgid "12% S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-21-L
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-21-L
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-21-L
msgid "21%"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21
msgid "21% Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-G
msgid "21% Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-CC
msgid "21% Cocont .S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-CC
msgid "21% Cocont. - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-CC
msgid "21% Cocont. M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-EU
msgid "21% EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-EU-G
msgid "21% EU - Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-EU
msgid "21% EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-EU-S
msgid "21% EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21
msgid "21% M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-21-ROW-CC
msgid "21% Non EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-21-ROW-CC
msgid "21% Non EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-ROW-CC
msgid "21% Non EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-21-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-21-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-21-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-21-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-21-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-21-S
msgid "21% S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_TVA-21-inclus-dans-prix
#: model:account.tax,name:l10n_be.2_attn_TVA-21-inclus-dans-prix
#: model:account.tax.template,name:l10n_be.attn_TVA-21-inclus-dans-prix
msgid "21% S. TTC"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_44
msgid "44"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_44
msgid "44 - Services intra-communautaires"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_45
msgid "45"
msgstr "45"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_45
msgid "45 - Opérations avec TVA due par le cocontractant"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie_46
msgid "46 - Livraisons intra-communautaires exemptées"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_46L
msgid "46L"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_46L
msgid "46L - Livraisons biens intra-communautaires exemptées"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_46T
msgid "46T"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_46T
msgid "46T - Livraisons biens intra-communautaire exemptées"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_47
msgid "47"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_47
msgid "47 - Autres opérations exemptées"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie_48
msgid "48 - Notes de crédit aux opérations grilles [44] et [46]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s44
msgid "48s44"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s44
msgid "48s44 - Notes de crédit aux opérations grilles [44]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s46L
msgid "48s46L"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s46L
msgid "48s46L - Notes de crédit aux opérations grilles [46L]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_48s46T
msgid "48s46T"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_48s46T
msgid "48s46T - Notes de crédit aux opérations grilles [46T]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_49
msgid "49"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_49
msgid "49 - Notes de crédit aux opérations du point II"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-CAR-EXC
msgid "50% Non Déductible - Frais de voiture (Prix Excl.)"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_54
msgid "54"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_54
msgid "54 - TVA sur opérations des grilles [01], [02], [03]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_55
msgid "55"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_55
msgid "55 - TVA sur opérations des grilles [86] et [88]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_56
msgid "56"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_56
msgid "56 - TVA sur opérations de la grille [87]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_57
msgid "57"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_57
msgid "57 - TVA relatives aux importations"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_59
msgid "59"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_59
msgid "59 - TVA déductible"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06
msgid "6% Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-G
msgid "6% Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-CC
msgid "6% Cocont. - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-CC
msgid "6% Cocont. M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-CC
msgid "6% Cocont. S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-EU
msgid "6% EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-EU-G
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-EU-G
msgid "6% EU - Biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-EU
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-EU
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-EU
msgid "6% EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-EU-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-EU-S
msgid "6% EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06
msgid "6% M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V83-06-ROW-CC
msgid "6% Non EU - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V81-06-ROW-CC
msgid "6% Non EU M."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-ROW-CC
msgid "6% Non EU S."
msgstr ""

#. module: l10n_be
#: model:account.tax,name:l10n_be.1_attn_VAT-IN-V82-06-S
#: model:account.tax,name:l10n_be.1_attn_VAT-OUT-06-S
#: model:account.tax,name:l10n_be.2_attn_VAT-IN-V82-06-S
#: model:account.tax,name:l10n_be.2_attn_VAT-OUT-06-S
#: model:account.tax.template,name:l10n_be.attn_VAT-IN-V82-06-S
#: model:account.tax.template,name:l10n_be.attn_VAT-OUT-06-S
msgid "6% S."
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_61
msgid "61"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_61
msgid "61 - Diverses régularisations en faveur de l'Etat"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_62
msgid "62"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_62
msgid "62 - Diverses régularisations en faveur du déclarant"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_63
msgid "63"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_63
msgid "63 - TVA à reverser sur notes de crédit recues"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_64
msgid "64"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_64
msgid "64 - TVA à récupérer sur notes de crédit delivrées"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_71
msgid "71 - Taxes dues à l'état"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_72
msgid "72 - Somme due par l'état"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_81
msgid "81"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_81
msgid "81 - Marchandises, matières premières et auxiliaires"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_82
msgid "82"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_82
msgid "82 - Services et biens divers"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_83
msgid "83"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_83
msgid "83 - Biens d'investissement"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_84
msgid "84"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_84
msgid "84 - Notes de crédits sur opérations case [86] et [88]"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_85
msgid "85"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_85
msgid "85 - Notes de crédits autres opérations"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_86
msgid "86"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_86
msgid "86 - Acquisition intra-communautaires et ventes ABC"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_87
msgid "87"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_87
msgid "87 - Autres opérations"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,tag_name:l10n_be.tax_report_line_88
msgid "88"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_line_88
msgid "88 - Acquisition services intra-communautaires"
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a052
#: model:account.account,name:l10n_be.2_a052
#: model:account.account.template,name:l10n_be.a052
msgid "Accounts receivable for assignment commitments"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a010
#: model:account.account,name:l10n_be.2_a010
#: model:account.account.template,name:l10n_be.a010
msgid "Accounts receivable for commitments on bills in circulation"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a066
#: model:account.account,name:l10n_be.2_a066
#: model:account.account.template,name:l10n_be.a066
msgid "Accounts receivable for currencies sold forward"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a062
#: model:account.account,name:l10n_be.2_a062
#: model:account.account.template,name:l10n_be.a062
msgid "Accounts receivable for goods sold forward"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a012
#: model:account.account,name:l10n_be.2_a012
#: model:account.account.template,name:l10n_be.a012
msgid "Accounts receivable for other personal guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a492
#: model:account.account,name:l10n_be.2_a492
#: model:account.account.template,name:l10n_be.a492
msgid "Accrued charges"
msgstr "Anzurechnende Aufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a491
#: model:account.account,name:l10n_be.2_a491
#: model:account.account.template,name:l10n_be.a491
msgid "Accrued income"
msgstr "Erworbene Erträge"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_605
#: model:account.group,name:l10n_be.2_be_group_605
#: model:account.group.template,name:l10n_be.be_group_605
msgid "Achats d'immeubles destinés à la vente"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_601
#: model:account.group,name:l10n_be.2_be_group_601
#: model:account.group.template,name:l10n_be.be_group_601
msgid "Achats de fournitures"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_604
#: model:account.group,name:l10n_be.2_be_group_604
#: model:account.group.template,name:l10n_be.be_group_604
msgid "Achats de marchandises"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_600
#: model:account.group,name:l10n_be.2_be_group_600
#: model:account.group.template,name:l10n_be.be_group_600
msgid "Achats de matières premières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_602
#: model:account.group,name:l10n_be.2_be_group_602
#: model:account.group.template,name:l10n_be.be_group_602
msgid "Achats de services, travaux et études"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_19
#: model:account.group,name:l10n_be.2_be_group_19
#: model:account.group.template,name:l10n_be.be_group_19
msgid "Acompte aux associés sur le partage de l'actif net (-)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_176
#: model:account.group,name:l10n_be.1_be_group_46
#: model:account.group,name:l10n_be.2_be_group_176
#: model:account.group,name:l10n_be.2_be_group_46
#: model:account.group.template,name:l10n_be.be_group_176
#: model:account.group.template,name:l10n_be.be_group_46
msgid "Acomptes reçus sur commandes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_213
#: model:account.group,name:l10n_be.1_be_group_360
#: model:account.group,name:l10n_be.1_be_group_406
#: model:account.group,name:l10n_be.2_be_group_213
#: model:account.group,name:l10n_be.2_be_group_360
#: model:account.group,name:l10n_be.2_be_group_406
#: model:account.group.template,name:l10n_be.be_group_213
#: model:account.group.template,name:l10n_be.be_group_360
#: model:account.group.template,name:l10n_be.be_group_406
msgid "Acomptes versés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_36
#: model:account.group,name:l10n_be.2_be_group_36
#: model:account.group.template,name:l10n_be.be_group_36
msgid "Acomptes versés sur achats pour stocks"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a050
#: model:account.account,name:l10n_be.2_a050
#: model:account.account.template,name:l10n_be.a050
msgid "Acquisition commitments"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5100
#: model:account.group,name:l10n_be.1_be_group_5110
#: model:account.group,name:l10n_be.1_be_group_5190
#: model:account.group,name:l10n_be.2_be_group_5100
#: model:account.group,name:l10n_be.2_be_group_5110
#: model:account.group,name:l10n_be.2_be_group_5190
#: model:account.group.template,name:l10n_be.be_group_5100
#: model:account.group.template,name:l10n_be.be_group_5110
#: model:account.group.template,name:l10n_be.be_group_5190
msgid "Actions et parts"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_50
#: model:account.group,name:l10n_be.2_be_group_50
#: model:account.group.template,name:l10n_be.be_group_50
msgid "Actions propres"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_51
#: model:account.group,name:l10n_be.2_be_group_51
#: model:account.group.template,name:l10n_be.be_group_51
msgid ""
"Actions, parts et placements de trésorerie autres que placements à revenu "
"fixe"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a021
#: model:account.account,name:l10n_be.2_a021
#: model:account.account.template,name:l10n_be.a021
msgid "Actual guarantees established for own account"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7711
#: model:account.account,name:l10n_be.2_a7711
#: model:account.account.template,name:l10n_be.a7711
msgid "Adjustment of Belgian income taxes - Estimated taxes"
msgstr ""
"Erstattung Belgischer Ertragsteuern - Verminderung Geschätzter Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7712
#: model:account.account,name:l10n_be.2_a7712
#: model:account.account.template,name:l10n_be.a7712
msgid "Adjustment of Belgian income taxes - Tax provisions written back"
msgstr ""
"Erstattung Belgischer Ertragsteuern - Auflösungen von Steuerrückstellungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7710
#: model:account.account,name:l10n_be.2_a7710
#: model:account.account.template,name:l10n_be.a7710
msgid "Adjustment of Belgian income taxes - Taxes due or paid"
msgstr ""
"Erstattung Belgischer Ertragsteuern - Geschuldete oder gezahlte Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a773
#: model:account.account,name:l10n_be.2_a773
#: model:account.account.template,name:l10n_be.a773
msgid "Adjustment of foreign income taxes"
msgstr "Erstattung Ausländischer Ertragsteuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a77
#: model:account.account,name:l10n_be.2_a77
#: model:account.account.template,name:l10n_be.a77
msgid "Adjustment of income taxes and write-back of tax provisions"
msgstr "Steuererstattungen und Auflösung von Steuerrückstellungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_695
#: model:account.group,name:l10n_be.2_be_group_695
#: model:account.group.template,name:l10n_be.be_group_695
msgid "Administrateurs ou gérants"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a360
#: model:account.account,name:l10n_be.2_a360
#: model:account.account.template,name:l10n_be.a360
msgid "Advance payments on purchases for stocks - Acquisition value"
msgstr "Geleistete Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a369
#: model:account.account,name:l10n_be.2_a369
#: model:account.account.template,name:l10n_be.a369
msgid "Advance payments on purchases for stocks - amounts written down"
msgstr "Geleistete Anzahlungen - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a426
#: model:account.account,name:l10n_be.2_a426
#: model:account.account.template,name:l10n_be.a426
msgid ""
"Advance payments received on contract in progress payable after more than "
"one year falling due within one year"
msgstr ""
"Für innerhalb eines Jahres fällig werdende Bestellungen mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr erhaltene Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a19
#: model:account.account,name:l10n_be.2_a19
#: model:account.account.template,name:l10n_be.a19
msgid "Advance to associates on the sharing out of the assets"
msgstr "Vorschuss an die Gesellschafter auf der Verteilung des Nettoaktiva"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a461
#: model:account.account,name:l10n_be.2_a461
#: model:account.account.template,name:l10n_be.a461
msgid "Advances received"
msgstr "Erhaltene Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a176
#: model:account.account,name:l10n_be.2_a176
#: model:account.account.template,name:l10n_be.a176
msgid "Advances received on contracts in progress (more than one year)"
msgstr ""
"Erhaltene Anzahlungen auf Bestellungen mit einer Restlaufzeit von mehr als "
"einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a460
#: model:account.account,name:l10n_be.2_a460
#: model:account.account.template,name:l10n_be.a460
msgid "Advances to be received within one year"
msgstr "Zu erhaltende Anzahlungen mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_691
#: model:account.group,name:l10n_be.2_be_group_691
#: model:account.group.template,name:l10n_be.be_group_691
msgid "Affectations au capital et à la prime d'émission"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_69
#: model:account.group,name:l10n_be.1_be_group_79
#: model:account.group,name:l10n_be.2_be_group_69
#: model:account.group,name:l10n_be.2_be_group_79
#: model:account.group.template,name:l10n_be.be_group_69
#: model:account.group.template,name:l10n_be.be_group_79
msgid "Affectations et prélèvements"
msgstr "Affectations et prélèvements"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_660
#: model:account.group,name:l10n_be.2_be_group_660
#: model:account.group.template,name:l10n_be.be_group_660
msgid "Amortissements et réductions de valeur non récurrents (dotations)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_63
#: model:account.group,name:l10n_be.2_be_group_63
#: model:account.group.template,name:l10n_be.be_group_63
msgid ""
"Amortissements, réductions de valeur et provisions pour risques et charges"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a653
#: model:account.account,name:l10n_be.2_a653
#: model:account.account.template,name:l10n_be.a653
msgid ""
"Amount of the discount borne by the enterprise, as a result of negotiating "
"amounts receivable"
msgstr ""
"Betrag des für das Unternehmen bei der Umwandlung einer Forderung "
"entstandenen Skontoaufwands"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a428
#: model:account.account,name:l10n_be.2_a428
#: model:account.account.template,name:l10n_be.a428
msgid ""
"Amounts payable after more than one year falling due within one year - "
"Guarantees received in cash"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr - In Geldmitteln erhaltene "
"Kautionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4232
#: model:account.account,name:l10n_be.2_a4232
#: model:account.account.template,name:l10n_be.a4232
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Bank acceptances"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten bei Kreditinstitute"
" - Akzeptkredite"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4230
#: model:account.account,name:l10n_be.2_a4230
#: model:account.account.template,name:l10n_be.a4230
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Current account payable"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten bei Kreditinstitute"
" mit einer ursprünglichen Laufzeit von mehr als einem Jahr - "
"Kontoverbindlichkeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4231
#: model:account.account,name:l10n_be.2_a4231
#: model:account.account.template,name:l10n_be.a4231
msgid ""
"Amounts payable after more than one year falling due within one year to "
"credit institutions - Promissory notes"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten bei Kreditinstitute"
" mit einer ursprünglichen Laufzeit von mehr als einem Jahr - Solawechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4250
#: model:account.account,name:l10n_be.2_a4250
#: model:account.account.template,name:l10n_be.a4250
msgid ""
"Amounts payable after more than one year falling due within one year to "
"suppliers"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten aus Lieferungen und"
" Leistungen mit einer ursprünglichen Laufzeit von mehr als einem Jahr - "
"Lieferanten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1732
#: model:account.account,name:l10n_be.2_a1732
#: model:account.account.template,name:l10n_be.a1732
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Bank acceptances"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit von mehr als "
"einem Jahr - Akzeptkredite"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1730
#: model:account.account,name:l10n_be.2_a1730
#: model:account.account.template,name:l10n_be.a1730
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Current account payable"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit von mehr als "
"einem Jahr - Kontoverbindlichkeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1731
#: model:account.account,name:l10n_be.2_a1731
#: model:account.account.template,name:l10n_be.a1731
msgid ""
"Amounts payable to credit institutions with a remaining term of more than "
"one year - Promissory notes"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit von mehr als "
"einem Jahr - Solawechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a178
#: model:account.account,name:l10n_be.2_a178
#: model:account.account.template,name:l10n_be.a178
msgid ""
"Amounts payable with a remaining term of more than one year - Guarantees "
"received in cash"
msgstr ""
"Verbindlichkeiten mit einer Restlaufzeit von mehr als einem Jahr - In "
"Barmitteln erhaltene Kautionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a432
#: model:account.account,name:l10n_be.2_a432
#: model:account.account.template,name:l10n_be.a432
msgid ""
"Amounts payable within one year to credit institutions - Bank acceptances"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit bis zu einem "
"Jahr - Akzeptkredite"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a433
#: model:account.account,name:l10n_be.2_a433
#: model:account.account.template,name:l10n_be.a433
msgid ""
"Amounts payable within one year to credit institutions - Current account "
"payable"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit bis zu einem "
"Jahr - Kontokorrentverbindlichkeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a430
#: model:account.account,name:l10n_be.2_a430
#: model:account.account.template,name:l10n_be.a430
msgid ""
"Amounts payable within one year to credit institutions - Fixed term loans"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit bis zu einem "
"Jahr - Kontokredite mit fester Laufzeit"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a431
#: model:account.account,name:l10n_be.2_a431
#: model:account.account.template,name:l10n_be.a431
msgid ""
"Amounts payable within one year to credit institutions - Promissory notes"
msgstr ""
"Verbindlichkeiten bei Kreditinstitute mit einer Restlaufzeit bis zu einem "
"Jahr - Solawechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2819
#: model:account.account,name:l10n_be.2_a2819
#: model:account.account.template,name:l10n_be.a2819
msgid "Amounts receivable from affiliated enterprises - Amounts written down"
msgstr "Forderungen an verbundenen Unternehmen - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2811
#: model:account.account,name:l10n_be.2_a2811
#: model:account.account.template,name:l10n_be.a2811
msgid "Amounts receivable from affiliated enterprises - Bills receivable"
msgstr "Forderungen an verbundenen Unternehmen - Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2810
#: model:account.account,name:l10n_be.2_a2810
#: model:account.account.template,name:l10n_be.a2810
msgid "Amounts receivable from affiliated enterprises - Current account"
msgstr "Forderungen an verbundenen Unternehmen - Kontoforderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2812
#: model:account.account,name:l10n_be.2_a2812
#: model:account.account.template,name:l10n_be.a2812
msgid ""
"Amounts receivable from affiliated enterprises - Fixed income securities"
msgstr "Forderungen an verbundenen Unternehmen - Festverzinsliche Wertpapiere"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2839
#: model:account.account,name:l10n_be.2_a2839
#: model:account.account.template,name:l10n_be.a2839
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Amounts written down"
msgstr ""
"Forderungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht - "
"Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2831
#: model:account.account,name:l10n_be.2_a2831
#: model:account.account.template,name:l10n_be.a2831
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Bills receivable"
msgstr ""
"Forderungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht - "
"Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2830
#: model:account.account,name:l10n_be.2_a2830
#: model:account.account.template,name:l10n_be.a2830
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Current account"
msgstr ""
"Forderungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht - "
"Kontoforderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2837
#: model:account.account,name:l10n_be.2_a2837
#: model:account.account.template,name:l10n_be.a2837
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Doubtful amounts"
msgstr ""
"Forderungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht - "
"Zweifelhafte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2832
#: model:account.account,name:l10n_be.2_a2832
#: model:account.account.template,name:l10n_be.a2832
msgid ""
"Amounts receivable from other enterprises linked by participating interests "
"- Fixed income securities"
msgstr ""
"Forderungen an Unternehmen, mit denen ein Beteiligungsverhältnis besteht - "
"Festverzinsliche Wertpapiere"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6320
#: model:account.account,name:l10n_be.2_a6320
#: model:account.account.template,name:l10n_be.a6320
msgid "Amounts written off contracts in progress - Appropriations"
msgstr "Wertminderungen von in Ausführung befindlichen Bestellungen - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6321
#: model:account.account,name:l10n_be.2_a6321
#: model:account.account.template,name:l10n_be.a6321
msgid "Amounts written off contracts in progress - Write-backs"
msgstr ""
"Wertminderungen von in Ausführung befindlichen Bestellungen - Zurückgenommen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6510
#: model:account.account,name:l10n_be.2_a6510
#: model:account.account.template,name:l10n_be.a6510
msgid ""
"Amounts written off current assets except stocks, contracts in progress and "
"trade debtors - Appropriations"
msgstr ""
"Wertminderungen von Gegenständen des Umlaufvermögens mit Ausnahme der "
"Vorräten, in Ausführung befindlichen Bestellungen und Forderungen aus "
"Lieferungen und Leistungen - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6511
#: model:account.account,name:l10n_be.2_a6511
#: model:account.account.template,name:l10n_be.a6511
msgid ""
"Amounts written off current assets except stocks, contracts in progress and "
"trade debtors - Write-backs"
msgstr ""
"Wertminderungen von Gegenständen des Umlaufvermögens mit Ausnahme der "
"Vorräten, in Ausführung befindlichen Bestellungen und Forderungen aus "
"Lieferungen und Leistungen - Rücknahmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a661
#: model:account.account,name:l10n_be.2_a661
#: model:account.account.template,name:l10n_be.a661
msgid "Amounts written off financial fixed assets"
msgstr "Wertminderungen auf Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6308
#: model:account.account,name:l10n_be.2_a6308
#: model:account.account.template,name:l10n_be.a6308
msgid "Amounts written off intangible fixed assets"
msgstr "Wertminderungen von immateriellen Anlagewerten - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6310
#: model:account.account,name:l10n_be.2_a6310
#: model:account.account.template,name:l10n_be.a6310
msgid "Amounts written off stocks - Appropriations"
msgstr "Wertminderungen von Vorräten - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6311
#: model:account.account,name:l10n_be.2_a6311
#: model:account.account.template,name:l10n_be.a6311
msgid "Amounts written off stocks - Write-backs"
msgstr "Wertminderungen von Vorräten - Zurückgenommen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6309
#: model:account.account,name:l10n_be.2_a6309
#: model:account.account.template,name:l10n_be.a6309
msgid "Amounts written off tangible fixed assets"
msgstr "Wertminderungen von Sachanlagen - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6330
#: model:account.account,name:l10n_be.2_a6330
#: model:account.account.template,name:l10n_be.a6330
msgid ""
"Amounts written off trade debtors (more than one year) - Appropriations"
msgstr ""
"Wertminderungen von Forderungen aus Lieferungen und Leistungen mit einer "
"Restlaufzeit von mehr als einem Jahr - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6331
#: model:account.account,name:l10n_be.2_a6331
#: model:account.account.template,name:l10n_be.a6331
msgid "Amounts written off trade debtors (more than one year) - Write-backs"
msgstr ""
"Wertminderungen von Forderungen aus Lieferungen und Leistungen mit einer "
"Restlaufzeit von mehr als einem Jahr - Rücknahmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6340
#: model:account.account,name:l10n_be.2_a6340
#: model:account.account.template,name:l10n_be.a6340
msgid "Amounts written off trade debtors (within one year) - Appropriations"
msgstr ""
"Wertminderungen von Forderungen aus Lieferungen und Leistungen mit einer "
"Restlaufzeit bis zu einem Jahr - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6341
#: model:account.account,name:l10n_be.2_a6341
#: model:account.account.template,name:l10n_be.a6341
msgid "Amounts written off trade debtors (within one year) - Write-backs"
msgstr ""
"Wertminderungen von Forderungen aus Lieferungen und Leistungen mit einer "
"Restlaufzeit bis zu einem Jahr - Rücknahmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a691
#: model:account.account,name:l10n_be.2_a691
#: model:account.account.template,name:l10n_be.a691
msgid "Appropriations to capital and share premium account"
msgstr "Zuweisungen an das Kapital und das Agio"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6920
#: model:account.account,name:l10n_be.2_a6920
#: model:account.account.template,name:l10n_be.a6920
msgid "Appropriations to legal reserve"
msgstr "Zuweisungen an die gesetzliche Rücklage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6921
#: model:account.account,name:l10n_be.2_a6921
#: model:account.account.template,name:l10n_be.a6921
msgid "Appropriations to other reserves"
msgstr "Zuweisungen an die sonstigen Rücklagen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_31
#: model:account.group,name:l10n_be.2_be_group_31
#: model:account.group.template,name:l10n_be.be_group_31
msgid "Approvisionnements - Fournitures"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_30
#: model:account.group,name:l10n_be.2_be_group_30
#: model:account.group.template,name:l10n_be.be_group_30
msgid "Approvisionnements - Matières premières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_60
#: model:account.group,name:l10n_be.2_be_group_60
#: model:account.group.template,name:l10n_be.be_group_60
msgid "Approvisionnements et marchandises"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_284
#: model:account.group,name:l10n_be.2_be_group_284
#: model:account.group.template,name:l10n_be.be_group_284
msgid "Autres actions et parts"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_473
#: model:account.group,name:l10n_be.2_be_group_473
#: model:account.group.template,name:l10n_be.be_group_473
msgid "Autres allocataires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_697
#: model:account.group,name:l10n_be.2_be_group_697
#: model:account.group.template,name:l10n_be.be_group_697
msgid "Autres applications"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_64
#: model:account.group,name:l10n_be.2_be_group_64
#: model:account.group.template,name:l10n_be.be_group_64
msgid "Autres charges d'exploitation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_664
#: model:account.group,name:l10n_be.2_be_group_664
#: model:account.group.template,name:l10n_be.be_group_664
msgid "Autres charges d'exploitation non récurrentes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_668
#: model:account.group,name:l10n_be.2_be_group_668
#: model:account.group.template,name:l10n_be.be_group_668
msgid "Autres charges financières non récurrentes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_285
#: model:account.group,name:l10n_be.1_be_group_291
#: model:account.group,name:l10n_be.1_be_group_41
#: model:account.group,name:l10n_be.2_be_group_285
#: model:account.group,name:l10n_be.2_be_group_291
#: model:account.group,name:l10n_be.2_be_group_41
#: model:account.group.template,name:l10n_be.be_group_285
#: model:account.group.template,name:l10n_be.be_group_291
#: model:account.group.template,name:l10n_be.be_group_41
msgid "Autres créances"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_489
#: model:account.group,name:l10n_be.2_be_group_489
#: model:account.group.template,name:l10n_be.be_group_489
msgid "Autres dettes diverses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_459
#: model:account.group,name:l10n_be.2_be_group_459
#: model:account.group.template,name:l10n_be.be_group_459
msgid "Autres dettes sociales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_223
#: model:account.group,name:l10n_be.2_be_group_223
#: model:account.group.template,name:l10n_be.be_group_223
msgid "Autres droits réels sur des immeubles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_174
#: model:account.group,name:l10n_be.1_be_group_439
#: model:account.group,name:l10n_be.2_be_group_174
#: model:account.group,name:l10n_be.2_be_group_439
#: model:account.group.template,name:l10n_be.be_group_174
#: model:account.group.template,name:l10n_be.be_group_439
msgid "Autres emprunts"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_202
#: model:account.group,name:l10n_be.2_be_group_202
#: model:account.group.template,name:l10n_be.be_group_202
msgid "Autres frais d'établissement"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_623
#: model:account.group,name:l10n_be.2_be_group_623
#: model:account.group.template,name:l10n_be.be_group_623
msgid "Autres frais de personnel"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_26
#: model:account.group,name:l10n_be.2_be_group_26
#: model:account.group.template,name:l10n_be.be_group_26
msgid "Autres immobilisations corporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_74
#: model:account.group,name:l10n_be.2_be_group_74
#: model:account.group.template,name:l10n_be.be_group_74
msgid "Autres produits d'exploitation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_764
#: model:account.group,name:l10n_be.2_be_group_764
#: model:account.group.template,name:l10n_be.be_group_764
msgid "Autres produits d'exploitation non récurrents"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_769
#: model:account.group,name:l10n_be.2_be_group_769
#: model:account.group.template,name:l10n_be.be_group_769
msgid "Autres produits financiers non récurrents"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a133
#: model:account.account,name:l10n_be.2_a133
#: model:account.account.template,name:l10n_be.a133
msgid "Available reserves"
msgstr "Verfügbare Rücklagen"

#. module: l10n_be
#: model:account.chart.template,name:l10n_be.l10nbe_chart_template
msgid "Belgian PCMN"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6703
#: model:account.account,name:l10n_be.2_a6703
#: model:account.account.template,name:l10n_be.a6703
msgid "Belgian and foreign income taxes - Income taxes - Other income taxes"
msgstr ""
"Belgische und Ausländische Steuern - Einkommensteuern - Übrige Steuern auf "
"Einkommen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6701
#: model:account.account,name:l10n_be.2_a6701
#: model:account.account.template,name:l10n_be.a6701
msgid ""
"Belgian and foreign income taxes - Income taxes - Withholding taxes on "
"immovables"
msgstr ""
"Belgische und Ausländische Steuern - Einkommensteuern - Vermögenssteuer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6702
#: model:account.account,name:l10n_be.2_a6702
#: model:account.account.template,name:l10n_be.a6702
msgid ""
"Belgian and foreign income taxes - Income taxes - Withholding taxes on "
"investment income"
msgstr ""
"Belgische und Ausländische Steuern - Einkommensteuern - Kapitalertragsteuer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6711
#: model:account.account,name:l10n_be.2_a6711
#: model:account.account.template,name:l10n_be.a6711
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" estimated income taxes"
msgstr ""
"Belgische Steuern auf das Ergebnis vorhergehender Geschäftsjahre - "
"Geschätzte zusätzliche Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6710
#: model:account.account,name:l10n_be.2_a6710
#: model:account.account.template,name:l10n_be.a6710
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" income taxes due or paid"
msgstr ""
"Belgische Steuern auf das Ergebnis vorhergehender Geschäftsjahre - "
"Geschuldete oder gezahlte zusätzliche Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6712
#: model:account.account,name:l10n_be.2_a6712
#: model:account.account.template,name:l10n_be.a6712
msgid ""
"Belgian income taxes on the result of prior periods - Additional charges for"
" income taxes provided for"
msgstr ""
"Belgische Steuern auf das Ergebnis vorhergehender Geschäftsjahre - Gebildete"
" Steuerrückstellungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6700
#: model:account.account,name:l10n_be.2_a6700
#: model:account.account.template,name:l10n_be.a6700
msgid ""
"Belgian income taxes on the result of the current period - Income taxes paid"
" and withholding taxes due or paid"
msgstr ""
"Belgische Steuern auf das Ergebnis des Geschäftsjahres - Geschuldete oder "
"gezahlte Steuern und Steuervorhabzuge"

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_journal__invoice_reference_model__be
#: model:ir.ui.menu,name:l10n_be.account_reports_be_statements_menu
msgid "Belgium"
msgstr "Belgien"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_07
#: model:account.group,name:l10n_be.2_be_group_07
#: model:account.group.template,name:l10n_be.be_group_07
msgid "Biens et valeurs de tiers détenus par l'entreprise"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_072
#: model:account.group,name:l10n_be.2_be_group_072
#: model:account.group.template,name:l10n_be.be_group_072
msgid "Biens et valeurs de tiers reçus en dépôt, en consignation ou à façon"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_04
#: model:account.group,name:l10n_be.1_be_group_041
#: model:account.group,name:l10n_be.2_be_group_04
#: model:account.group,name:l10n_be.2_be_group_041
#: model:account.group.template,name:l10n_be.be_group_04
#: model:account.group.template,name:l10n_be.be_group_041
msgid ""
"Biens et valeurs détenus par des tiers en leur nom mais aux risques et "
"profits de l'entreprise"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_074
#: model:account.group,name:l10n_be.2_be_group_074
#: model:account.group.template,name:l10n_be.be_group_074
msgid ""
"Biens et valeurs détenus pour compte ou aux risques et profits de tiers"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1751
#: model:account.account,name:l10n_be.2_a1751
#: model:account.account.template,name:l10n_be.a1751
msgid "Bills of exchange payable after more than one year"
msgstr ""
"Verbindlichkeiten aus Lieferungen und Leistungen mit einer Restlaufzeit von "
"mehr als einem Jahr - Verbindlichkeiten aus Wechseln"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4251
#: model:account.account,name:l10n_be.2_a4251
#: model:account.account.template,name:l10n_be.a4251
msgid ""
"Bills of exchange payable after more than one year falling due within one "
"year"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten aus Lieferungen und"
" Leistungen mit einer ursprünglichen Laufzeit von mehr als einem Jahr - "
"Verbindlichkeiten aus Wechseln"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a441
#: model:account.account,name:l10n_be.2_a441
#: model:account.account.template,name:l10n_be.a441
msgid "Bills of exchange payable within one year"
msgstr ""
"Verbindlichkeiten aus Wechseln mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2211
#: model:account.account,name:l10n_be.2_a2211
#: model:account.account.template,name:l10n_be.a2211
msgid "Building owned by the association or the foundation in full property"
msgstr "Im Volleigentum der Vereinigung oder Stiftung befindliche Bauten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a221
#: model:account.account,name:l10n_be.2_a221
#: model:account.account.template,name:l10n_be.a221
msgid "Buildings"
msgstr "Bauten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2221
#: model:account.account,name:l10n_be.2_a2221
#: model:account.account.template,name:l10n_be.a2221
msgid ""
"Built-up lands owned by the association or the foundation in full property"
msgstr ""
"Im Volleigentum der Vereinigung oder Stiftung befindliche bebaute "
"Grundstücke"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_371
#: model:account.group,name:l10n_be.2_be_group_371
#: model:account.group.template,name:l10n_be.be_group_371
msgid "Bénéfice pris en compte"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_790
#: model:account.group,name:l10n_be.2_be_group_790
#: model:account.group.template,name:l10n_be.be_group_790
msgid "Bénéfice reporté de l'exercice précédent"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_14
#: model:account.group,name:l10n_be.2_be_group_14
#: model:account.group.template,name:l10n_be.be_group_14
msgid "Bénéfice reporté ou Perte reportée (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_693
#: model:account.group,name:l10n_be.2_be_group_693
#: model:account.group.template,name:l10n_be.be_group_693
msgid "Bénéfice à reporter"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_57
#: model:account.group,name:l10n_be.2_be_group_57
#: model:account.group.template,name:l10n_be.be_group_57
msgid "Caisses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_570
#: model:account.group,name:l10n_be.2_be_group_570
#: model:account.group.template,name:l10n_be.be_group_570
msgid "Caisses-espèces"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_578
#: model:account.group,name:l10n_be.2_be_group_578
#: model:account.group.template,name:l10n_be.be_group_578
msgid "Caisses-timbres"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a410
#: model:account.account,name:l10n_be.2_a410
#: model:account.account.template,name:l10n_be.a410
msgid "Called up capital, unpaid"
msgstr "Eingefordertes, noch nicht eingezahltes Kapital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_10
#: model:account.group,name:l10n_be.2_be_group_10
#: model:account.group.template,name:l10n_be.be_group_10
msgid "Capital"
msgstr "Kapital"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_410
#: model:account.group,name:l10n_be.2_be_group_410
#: model:account.group.template,name:l10n_be.be_group_410
msgid "Capital appelé, non versé"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7631
#: model:account.account,name:l10n_be.2_a7631
#: model:account.account.template,name:l10n_be.a7631
msgid "Capital gains on disposal of financial fixed assets"
msgstr "Mehrwerte aus dem Abgang von Gegenständen der Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7630
#: model:account.account,name:l10n_be.2_a7630
#: model:account.account.template,name:l10n_be.a7630
msgid "Capital gains on disposal of intangible and tangible fixed asset"
msgstr ""
"Mehrwerte aus dem Abgang von Gegenständen der immateriellen und materiellen "
"Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6631
#: model:account.account,name:l10n_be.2_a6631
#: model:account.account.template,name:l10n_be.a6631
msgid "Capital losses on disposal of financial fixed assets"
msgstr "Minderwerte aus dem Abgang von Gegenständen der Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6630
#: model:account.account,name:l10n_be.2_a6630
#: model:account.account.template,name:l10n_be.a6630
msgid "Capital losses on disposal of intangible and tangible fixed assets"
msgstr ""
"Minderwerte aus dem Abgang von Gegenständen der immateriellen und "
"materiellen Sachanlagen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_101
#: model:account.group,name:l10n_be.2_be_group_101
#: model:account.group.template,name:l10n_be.be_group_101
msgid "Capital non appelé (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_100
#: model:account.group,name:l10n_be.2_be_group_100
#: model:account.group.template,name:l10n_be.be_group_100
msgid "Capital souscrit"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6503
#: model:account.account,name:l10n_be.2_a6503
#: model:account.account.template,name:l10n_be.a6503
msgid "Capitalized Interests"
msgstr "Aktivierte Zinsen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a54
#: model:account.account,name:l10n_be.2_a54
#: model:account.account.template,name:l10n_be.a54
msgid "Cash at bank - Amounts overdue and in the process of collection"
msgstr "Zum Inkasso fällige Werte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a55
#: model:account.account,name:l10n_be.2_a55
#: model:account.account.template,name:l10n_be.a55
msgid "Cash at bank - Credit institutions"
msgstr "Kreditinstitute"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a560
#: model:account.account,name:l10n_be.2_a560
#: model:account.account.template,name:l10n_be.a560
msgid "Cash at bank - Giro account - Bank account"
msgstr "Postscheckamt - Girokonto"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a561
#: model:account.account,name:l10n_be.2_a561
#: model:account.account.template,name:l10n_be.a561
msgid "Cash at bank - Giro account - Cheques issued"
msgstr "Postscheckamt - Ausgestellte Schecks"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a58
#: model:account.account,name:l10n_be.2_a58
#: model:account.account.template,name:l10n_be.a58
msgid "Cash at bank and in hand - Internal transfers of funds"
msgstr "Interne Geldtransferkonten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a57
#: model:account.account,name:l10n_be.2_a57
#: model:account.account.template,name:l10n_be.a57
msgid "Cash in hand"
msgstr "Kassenbestand"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a578
#: model:account.account,name:l10n_be.2_a578
#: model:account.account.template,name:l10n_be.a578
msgid "Cash in hand - Stamps"
msgstr "Kassen - Wertmarken"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_178
#: model:account.group,name:l10n_be.1_be_group_488
#: model:account.group,name:l10n_be.2_be_group_178
#: model:account.group,name:l10n_be.2_be_group_488
#: model:account.group.template,name:l10n_be.be_group_178
#: model:account.group.template,name:l10n_be.be_group_488
msgid "Cautionnements reçus en numéraire"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_288
#: model:account.group,name:l10n_be.1_be_group_418
#: model:account.group,name:l10n_be.2_be_group_288
#: model:account.group,name:l10n_be.2_be_group_418
#: model:account.group.template,name:l10n_be.be_group_288
#: model:account.group.template,name:l10n_be.be_group_418
msgid "Cautionnements versés en numéraire"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_6
#: model:account.group,name:l10n_be.2_be_group_6
#: model:account.group.template,name:l10n_be.be_group_6
msgid "Charges"
msgstr "Kosten"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_653
#: model:account.group,name:l10n_be.2_be_group_653
#: model:account.group.template,name:l10n_be.be_group_653
msgid "Charges d'escompte de créances"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_643
#: model:account.group,name:l10n_be.2_be_group_643
#: model:account.group.template,name:l10n_be.be_group_643
msgid "Charges d'exploitation diverses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_66
#: model:account.group,name:l10n_be.2_be_group_66
#: model:account.group.template,name:l10n_be.be_group_66
msgid "Charges d'exploitation ou financières non récurrentes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_649
#: model:account.group,name:l10n_be.2_be_group_649
#: model:account.group.template,name:l10n_be.be_group_649
msgid ""
"Charges d'exploitation portées à l'actif au titre de frais de "
"restructuration (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_650
#: model:account.group,name:l10n_be.2_be_group_650
#: model:account.group.template,name:l10n_be.be_group_650
msgid "Charges des dettes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_65
#: model:account.group,name:l10n_be.2_be_group_65
#: model:account.group.template,name:l10n_be.be_group_65
msgid "Charges financières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_657
#: model:account.group,name:l10n_be.2_be_group_657
#: model:account.group.template,name:l10n_be.be_group_657
msgid "Charges financières diverses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_659
#: model:account.group,name:l10n_be.2_be_group_659
#: model:account.group.template,name:l10n_be.be_group_659
msgid ""
"Charges financières portées à l'actif au titre de frais de restructuration"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_640
#: model:account.group,name:l10n_be.2_be_group_640
#: model:account.group.template,name:l10n_be.be_group_640
msgid "Charges fiscales d'exploitation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_669
#: model:account.group,name:l10n_be.2_be_group_669
#: model:account.group.template,name:l10n_be.be_group_669
msgid "Charges portées à l'actif au titre de frais de restructuration (-)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_492
#: model:account.group,name:l10n_be.2_be_group_492
#: model:account.group.template,name:l10n_be.be_group_492
msgid "Charges à imputer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_490
#: model:account.group,name:l10n_be.2_be_group_490
#: model:account.group.template,name:l10n_be.be_group_490
msgid "Charges à reporter"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_70
#: model:account.group,name:l10n_be.2_be_group_70
#: model:account.group.template,name:l10n_be.be_group_70
msgid "Chiffre d'affaires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_561
#: model:account.group,name:l10n_be.2_be_group_561
#: model:account.group.template,name:l10n_be.be_group_561
msgid "Chèques émis (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_400
#: model:account.group,name:l10n_be.2_be_group_400
#: model:account.group.template,name:l10n_be.be_group_400
msgid "Clients"
msgstr "Kunden"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_37
#: model:account.group,name:l10n_be.2_be_group_37
#: model:account.group.template,name:l10n_be.be_group_37
msgid "Commandes en cours d'exécution"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_073
#: model:account.group,name:l10n_be.2_be_group_073
#: model:account.group.template,name:l10n_be.be_group_073
msgid "Commettants et déposants de biens et de valeurs"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Kommunikationsstandard"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a020
#: model:account.account,name:l10n_be.2_a020
#: model:account.account.template,name:l10n_be.a020
msgid "Company creditors, beneficiaries of real guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a000
#: model:account.account,name:l10n_be.2_a000
#: model:account.account.template,name:l10n_be.a000
msgid "Company creditors, beneficiaries of third party guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a738
#: model:account.account,name:l10n_be.2_a738
#: model:account.account.template,name:l10n_be.a738
msgid "Compensatory amounts meant to reduce wage costs"
msgstr "Ausgleichsbeträge zur Senkung der Lohnkosten"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_560
#: model:account.group,name:l10n_be.2_be_group_560
#: model:account.group.template,name:l10n_be.be_group_560
msgid "Compte courant"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_499
#: model:account.group,name:l10n_be.2_be_group_499
#: model:account.group.template,name:l10n_be.be_group_499
msgid "Comptes d'attente"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_49
#: model:account.group,name:l10n_be.2_be_group_49
#: model:account.group.template,name:l10n_be.be_group_49
msgid "Comptes de régularisation et comptes d'attente"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_550
#: model:account.group,name:l10n_be.2_be_group_550
#: model:account.group.template,name:l10n_be.be_group_550
msgid "Comptes ouverts auprès des divers établissements, à subdiviser en :"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_211
#: model:account.group,name:l10n_be.2_be_group_211
#: model:account.group.template,name:l10n_be.be_group_211
msgid ""
"Concessions, brevets, licences, savoir-faire, marques et droits similaires"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a211
#: model:account.account,name:l10n_be.2_a211
#: model:account.account.template,name:l10n_be.a211
msgid "Concessions, patents, licences, know-how, brands and similar rights"
msgstr ""
"Konzessionen, Patente, Lizenzen, Know-how, Warenzeichen und ähnliche Rechte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a091
#: model:account.account,name:l10n_be.2_a091
#: model:account.account.template,name:l10n_be.a091
msgid "Concordat resolution claims"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a090
#: model:account.account,name:l10n_be.2_a090
#: model:account.account.template,name:l10n_be.a090
msgid "Concordat resolution commitments"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_033
#: model:account.group,name:l10n_be.2_be_group_033
#: model:account.group.template,name:l10n_be.be_group_033
msgid "Constituants de garanties"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a033
#: model:account.account,name:l10n_be.2_a033
#: model:account.account.template,name:l10n_be.a033
msgid "Constituents of guarantees"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_221
#: model:account.group,name:l10n_be.2_be_group_221
#: model:account.group.template,name:l10n_be.be_group_221
msgid "Constructions"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a310
#: model:account.account,name:l10n_be.2_a310
#: model:account.account.template,name:l10n_be.a310
msgid "Consumables - Acquisition value"
msgstr "Hilfs- und Betriebsstoffe - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a319
#: model:account.account,name:l10n_be.2_a319
#: model:account.account.template,name:l10n_be.a319
msgid "Consumables - amounts written down"
msgstr "Hilfs- und Betriebsstoffe - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a370
#: model:account.account,name:l10n_be.2_a370
#: model:account.account.template,name:l10n_be.a370
msgid "Contracts in progress - Acquisition value"
msgstr "In Ausführung befindliche Bestellungen - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a371
#: model:account.account,name:l10n_be.2_a371
#: model:account.account.template,name:l10n_be.a371
msgid "Contracts in progress - Profit recognised"
msgstr "In Ausführung befindliche Bestellungen - Aktivierte Gewinnanteile"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a379
#: model:account.account,name:l10n_be.2_a379
#: model:account.account.template,name:l10n_be.a379
msgid "Contracts in progress - amounts written down"
msgstr "In Ausführung befindliche Bestellungen - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a730
#: model:account.account,name:l10n_be.2_a730
#: model:account.account.template,name:l10n_be.a730
msgid "Contributions from effective members"
msgstr "Beiträge (Zahlungen) von assoziierten Mitgliedern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a731
#: model:account.account,name:l10n_be.2_a731
#: model:account.account.template,name:l10n_be.a731
msgid "Contributions from members"
msgstr "Beiträge (Zahlungen) von ordentlichen Mitgliedern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a736
#: model:account.account,name:l10n_be.2_a736
#: model:account.account.template,name:l10n_be.a736
msgid ""
"Contributions, gifts, legacies and grants - Investment grants and interest "
"subsidies"
msgstr ""
"Beiträge, Schenkungen, Legate und Subventionen - Beiträge, Schenkungen, "
"Legate und Subventionen - Kapital- und Zinssubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a617
#: model:account.account,name:l10n_be.2_a617
#: model:account.account.template,name:l10n_be.a617
msgid ""
"Costs of hired temporary staff and persons placed at the enterprise's "
"disposal"
msgstr ""
"Auf Zeitarbeitpersonal und dem Unternehmen zur Verfügung gestellte Personen "
"bezüglichen Aufwand"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_621
#: model:account.group,name:l10n_be.2_be_group_621
#: model:account.group.template,name:l10n_be.be_group_621
msgid "Cotisations patronales d'assurances sociales"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a065
#: model:account.account,name:l10n_be.2_a065
#: model:account.account.template,name:l10n_be.a065
msgid "Creditors for forward currency purchases"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a061
#: model:account.account,name:l10n_be.2_a061
#: model:account.account.template,name:l10n_be.a061
msgid "Creditors for goods purchased at term"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a051
#: model:account.account,name:l10n_be.2_a051
#: model:account.account.template,name:l10n_be.a051
msgid "Creditors of acquisition commitments"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0110
#: model:account.account,name:l10n_be.2_a0110
#: model:account.account.template,name:l10n_be.a0110
msgid ""
"Creditors of commitments on bills in circulation - Bids ceded by the company"
" under its backing"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0111
#: model:account.account,name:l10n_be.2_a0111
#: model:account.account.template,name:l10n_be.a0111
msgid ""
"Creditors of commitments on notes in circulation - Other commitments on "
"notes in circulation"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a013
#: model:account.account,name:l10n_be.2_a013
#: model:account.account.template,name:l10n_be.a013
msgid "Creditors of other personal guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a095
#: model:account.account,name:l10n_be.2_a095
#: model:account.account.template,name:l10n_be.a095
msgid "Creditors of pending litigation"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a075
#: model:account.account,name:l10n_be.2_a075
#: model:account.account.template,name:l10n_be.a075
msgid ""
"Creditors of property and securities held on behalf of third parties or at "
"their risk and profit"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a022
#: model:account.account,name:l10n_be.2_a022
#: model:account.account.template,name:l10n_be.a022
msgid "Creditors of third parties, beneficiaries of real guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a092
#: model:account.account,name:l10n_be.2_a092
#: model:account.account.template,name:l10n_be.a092
msgid "Creditors under debt restructuring conditions"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_290
#: model:account.group,name:l10n_be.1_be_group_40
#: model:account.group,name:l10n_be.2_be_group_290
#: model:account.group,name:l10n_be.2_be_group_40
#: model:account.group.template,name:l10n_be.be_group_290
#: model:account.group.template,name:l10n_be.be_group_40
msgid "Créances commerciales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_416
#: model:account.group,name:l10n_be.2_be_group_416
#: model:account.group.template,name:l10n_be.be_group_416
msgid "Créances diverses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_407
#: model:account.group,name:l10n_be.1_be_group_417
#: model:account.group,name:l10n_be.2_be_group_407
#: model:account.group,name:l10n_be.2_be_group_417
#: model:account.group.template,name:l10n_be.be_group_407
#: model:account.group.template,name:l10n_be.be_group_417
msgid "Créances douteuses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_4
#: model:account.group,name:l10n_be.2_be_group_4
#: model:account.group.template,name:l10n_be.be_group_4
msgid "Créances et dettes à un an au plus"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_283
#: model:account.group,name:l10n_be.2_be_group_283
#: model:account.group.template,name:l10n_be.be_group_283
msgid ""
"Créances sur des entreprises avec lesquelles il existe un lien de "
"participation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_281
#: model:account.group,name:l10n_be.2_be_group_281
#: model:account.group.template,name:l10n_be.be_group_281
msgid "Créances sur des entreprises liées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_29
#: model:account.group,name:l10n_be.2_be_group_29
#: model:account.group.template,name:l10n_be.be_group_29
msgid "Créances à plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_013
#: model:account.group,name:l10n_be.2_be_group_013
#: model:account.group.template,name:l10n_be.be_group_013
msgid "Créanciers d'autres garanties personnelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_051
#: model:account.group,name:l10n_be.2_be_group_051
#: model:account.group.template,name:l10n_be.be_group_051
msgid "Créanciers d'engagements d'acquisition"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_011
#: model:account.group,name:l10n_be.2_be_group_011
#: model:account.group.template,name:l10n_be.be_group_011
msgid "Créanciers d'engagements sur effets en circulation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_075
#: model:account.group,name:l10n_be.2_be_group_075
#: model:account.group.template,name:l10n_be.be_group_075
msgid ""
"Créanciers de biens et valeurs détenus pour compte de tiers ou à leurs "
"risques et profits"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_000
#: model:account.group,name:l10n_be.2_be_group_000
#: model:account.group.template,name:l10n_be.be_group_000
msgid "Créanciers de l'entreprise, bénéficiaires de garanties de tiers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_020
#: model:account.group,name:l10n_be.2_be_group_020
#: model:account.group.template,name:l10n_be.be_group_020
msgid "Créanciers de l'entreprise, bénéficiaires de garanties réelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_071
#: model:account.group,name:l10n_be.2_be_group_071
#: model:account.group.template,name:l10n_be.be_group_071
msgid "Créanciers de loyers et redevances"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_022
#: model:account.group,name:l10n_be.2_be_group_022
#: model:account.group.template,name:l10n_be.be_group_022
msgid "Créanciers de tiers, bénéficiaires de garanties réelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_065
#: model:account.group,name:l10n_be.2_be_group_065
#: model:account.group.template,name:l10n_be.be_group_065
msgid "Créanciers pour devises achetées à terme"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_061
#: model:account.group,name:l10n_be.2_be_group_061
#: model:account.group.template,name:l10n_be.be_group_061
msgid "Créanciers pour marchandises achetées à terme"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a509
#: model:account.account,name:l10n_be.2_a509
#: model:account.account.template,name:l10n_be.a509
msgid ""
"Current investments other than shares, fixed income securities and term "
"accounts - Amounts written down"
msgstr ""
"Geldanlagen andere als Aktien und Anteile, festverzinsliche Wertpapiere und "
"Terminkonten - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a500
#: model:account.account,name:l10n_be.2_a500
#: model:account.account.template,name:l10n_be.a500
msgid ""
"Current investments other than shares, fixed income securities and term "
"accounts - Cost"
msgstr ""
"Geldanlagen andere als Aktien und Anteile, festverzinsliche Wertpapiere und "
"Terminkonten - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4001
#: model:account.account,name:l10n_be.2_a4001
#: model:account.account.template,name:l10n_be.a4001
msgid "Customer (POS)"
msgstr "Kunden (POS)"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_532
#: model:account.group,name:l10n_be.2_be_group_532
#: model:account.group.template,name:l10n_be.be_group_532
msgid "D'un mois au plus"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_530
#: model:account.group,name:l10n_be.2_be_group_530
#: model:account.group.template,name:l10n_be.be_group_530
msgid "De plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_531
#: model:account.group,name:l10n_be.2_be_group_531
#: model:account.group.template,name:l10n_be.be_group_531
msgid "De plus d'un mois et à un an au plus"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a096
#: model:account.account,name:l10n_be.2_a096
#: model:account.account.template,name:l10n_be.a096
msgid "Debtors on technical guarantees"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6095
#: model:account.account,name:l10n_be.2_a6095
#: model:account.account.template,name:l10n_be.a6095
msgid "Decrease (increase) in immovable property for resale"
msgstr ""
"Abnahme ( Zunahme) des Bestandes an zum Verkauf bestimmten gekauften "
"unbeweglichen Gegenstände"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6091
#: model:account.account,name:l10n_be.2_a6091
#: model:account.account.template,name:l10n_be.a6091
msgid "Decrease (increase) in stocks of consumables"
msgstr "Abnahme ( Zunahme) des Bestandes an Hilfs- und Betriebsstoffe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6094
#: model:account.account,name:l10n_be.2_a6094
#: model:account.account.template,name:l10n_be.a6094
msgid "Decrease (increase) in stocks of goods purchased for resale"
msgstr "Abnahme ( Zunahme) des Bestandes an Waren"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6090
#: model:account.account,name:l10n_be.2_a6090
#: model:account.account.template,name:l10n_be.a6090
msgid "Decrease (increase) in stocks of raw materials"
msgstr "Abnahme ( Zunahme) des Bestandes an Rohstoffe"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a124
#: model:account.account,name:l10n_be.2_a124
#: model:account.account.template,name:l10n_be.a124
msgid "Decrease in amounts written down current investments"
msgstr "Rücknahmen von Wertminderungen auf Geldanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a490
#: model:account.account,name:l10n_be.2_a490
#: model:account.account.template,name:l10n_be.a490
msgid "Deferred charges"
msgstr "Vorzutragende Aufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a493
#: model:account.account,name:l10n_be.2_a493
#: model:account.account.template,name:l10n_be.a493
msgid "Deferred income"
msgstr "Vorzutragende Erträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1681
#: model:account.account,name:l10n_be.2_a1681
#: model:account.account.template,name:l10n_be.a1681
msgid "Deferred taxes on gain on disposal of intangible fixed assets"
msgstr ""
"Aufgeschobene Steuern auf Mehrwerte, die auf immaterielle Anlagewerte "
"realisiert werden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1687
#: model:account.account,name:l10n_be.2_a1687
#: model:account.account.template,name:l10n_be.a1687
msgid ""
"Deferred taxes on gain on disposal of securities issued by Belgian public "
"authorities"
msgstr ""
"Aufgeschobene Steuern auf Mehrwerte, die auf von der belgischen öffentlichen"
" Hand ausgegebene Wertpapiere realisiert werden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1682
#: model:account.account,name:l10n_be.2_a1682
#: model:account.account.template,name:l10n_be.a1682
msgid "Deferred taxes on gain on disposal of tangible fixed assets"
msgstr ""
"Aufgeschobene Steuern auf Mehrwerte, die auf Sachanlagen realisiert werden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1680
#: model:account.account,name:l10n_be.2_a1680
#: model:account.account.template,name:l10n_be.a1680
msgid "Deferred taxes on investment grants"
msgstr "Aufgeschobene Steuern auf Subventionen in Kapitalform"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6300
#: model:account.account,name:l10n_be.2_a6300
#: model:account.account.template,name:l10n_be.a6300
msgid "Depreciation of formation expenses"
msgstr ""
"Abschreibungen auf Errichtungs- und Erweiterungsaufwendungen - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6301
#: model:account.account,name:l10n_be.2_a6301
#: model:account.account.template,name:l10n_be.a6301
msgid "Depreciation of intangible fixed assets"
msgstr "Abschreibungen auf immaterielle Anlagewerte - Gebucht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6501
#: model:account.account,name:l10n_be.2_a6501
#: model:account.account.template,name:l10n_be.a6501
msgid "Depreciation of loan issue expenses"
msgstr "Abschreibungen auf Kosten der Emission von Anleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6302
#: model:account.account,name:l10n_be.2_a6302
#: model:account.account.template,name:l10n_be.a6302
msgid "Depreciation of tangible fixed assets"
msgstr "Abschreibungen auf Sachanlagen - Gebucht"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_717
#: model:account.group,name:l10n_be.2_be_group_717
#: model:account.group.template,name:l10n_be.be_group_717
msgid "Des commandes en cours d'exécution"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_712
#: model:account.group,name:l10n_be.2_be_group_712
#: model:account.group.template,name:l10n_be.be_group_712
msgid "Des en-cours de fabrication"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_715
#: model:account.group,name:l10n_be.2_be_group_715
#: model:account.group.template,name:l10n_be.be_group_715
msgid "Des immeubles construits destinés à la vente"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_713
#: model:account.group,name:l10n_be.2_be_group_713
#: model:account.group.template,name:l10n_be.be_group_713
msgid "Des produits finis"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_175
#: model:account.group,name:l10n_be.1_be_group_44
#: model:account.group,name:l10n_be.2_be_group_175
#: model:account.group,name:l10n_be.2_be_group_44
#: model:account.group.template,name:l10n_be.be_group_175
#: model:account.group.template,name:l10n_be.be_group_44
msgid "Dettes commerciales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_172
#: model:account.group,name:l10n_be.2_be_group_172
#: model:account.group.template,name:l10n_be.be_group_172
msgid "Dettes de location-financement et dettes assimilées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_179
#: model:account.group,name:l10n_be.1_be_group_48
#: model:account.group,name:l10n_be.2_be_group_179
#: model:account.group,name:l10n_be.2_be_group_48
#: model:account.group.template,name:l10n_be.be_group_179
#: model:account.group.template,name:l10n_be.be_group_48
msgid "Dettes diverses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_47
#: model:account.group,name:l10n_be.2_be_group_47
#: model:account.group.template,name:l10n_be.be_group_47
msgid "Dettes découlant de l'affectation du résultat"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_43
#: model:account.group,name:l10n_be.2_be_group_43
#: model:account.group.template,name:l10n_be.be_group_43
msgid "Dettes financières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_450
#: model:account.group,name:l10n_be.2_be_group_450
#: model:account.group.template,name:l10n_be.be_group_450
msgid "Dettes fiscales estimées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_45
#: model:account.group,name:l10n_be.2_be_group_45
#: model:account.group.template,name:l10n_be.be_group_45
msgid "Dettes fiscales, salariales et sociales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_17
#: model:account.group,name:l10n_be.2_be_group_17
#: model:account.group.template,name:l10n_be.be_group_17
msgid "Dettes à plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_42
#: model:account.group,name:l10n_be.2_be_group_42
#: model:account.group.template,name:l10n_be.be_group_42
msgid ""
"Dettes à plus d'un an échéant dans l'année 16 (même subdivision que le 17)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a222
#: model:account.account,name:l10n_be.2_a222
#: model:account.account.template,name:l10n_be.a222
msgid "Developed land"
msgstr "Bebaute Grundstücke"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_064
#: model:account.group,name:l10n_be.2_be_group_064
#: model:account.group.template,name:l10n_be.be_group_064
msgid "Devises achetées à terme - à recevoir"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_067
#: model:account.group,name:l10n_be.2_be_group_067
#: model:account.group.template,name:l10n_be.be_group_067
msgid "Devises vendues à terme - à livrer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_654
#: model:account.group,name:l10n_be.1_be_group_754
#: model:account.group,name:l10n_be.2_be_group_654
#: model:account.group,name:l10n_be.2_be_group_754
#: model:account.group.template,name:l10n_be.be_group_654
#: model:account.group.template,name:l10n_be.be_group_754
msgid "Différences de change"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a472
#: model:account.account,name:l10n_be.2_a472
#: model:account.account.template,name:l10n_be.a472
msgid "Director's fees - Current financial period"
msgstr "Tantiemen für das Geschäftsjahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a695
#: model:account.account,name:l10n_be.2_a695
#: model:account.account.template,name:l10n_be.a695
msgid "Directors' or managers' entitlements"
msgstr "Verteilung zugunsten der Verwalter oder Geschäftsführer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a608
#: model:account.account,name:l10n_be.2_a608
#: model:account.account.template,name:l10n_be.a608
msgid ""
"Discounts, allowance and rebates received on purchase of raw materials, "
"consumables"
msgstr "Erhaltene Preisnachlässe, Rückvergütungen und Rabatte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a708
#: model:account.account,name:l10n_be.2_a708
#: model:account.account.template,name:l10n_be.a708
msgid "Discounts, allowances and rebates allowed"
msgstr "Preisnachlässe, Rückvergütungen und Rabatte"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_471
#: model:account.group,name:l10n_be.2_be_group_471
#: model:account.group.template,name:l10n_be.be_group_471
msgid "Dividendes de l'exercice"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_470
#: model:account.group,name:l10n_be.2_be_group_470
#: model:account.group.template,name:l10n_be.be_group_470
msgid "Dividendes et tantièmes d'exercices antérieurs"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a694
#: model:account.account,name:l10n_be.2_a694
#: model:account.account.template,name:l10n_be.a694
msgid "Dividends"
msgstr "Vergütung des Kapitals"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a471
#: model:account.account,name:l10n_be.2_a471
#: model:account.account.template,name:l10n_be.a471
msgid "Dividends - Current financial period"
msgstr "Dividenden für das Geschäftsjahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a470
#: model:account.account,name:l10n_be.2_a470
#: model:account.account.template,name:l10n_be.a470
msgid "Dividends and director's fees relating to prior financial periods"
msgstr "Dividenden und Tantiemen vorhergehender Geschäftsjahre"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_692
#: model:account.group,name:l10n_be.2_be_group_692
#: model:account.group.template,name:l10n_be.be_group_692
msgid "Dotation aux réserves"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_630
#: model:account.group,name:l10n_be.2_be_group_630
#: model:account.group.template,name:l10n_be.be_group_630
msgid ""
"Dotations aux amortissements et aux réductions de valeur sur immobilisations"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_070
#: model:account.group,name:l10n_be.2_be_group_070
#: model:account.group.template,name:l10n_be.be_group_070
msgid "Droits d'usage à long terme"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_09
#: model:account.group,name:l10n_be.2_be_group_09
#: model:account.group.template,name:l10n_be.be_group_09
msgid "Droits et engagements divers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_0
#: model:account.group,name:l10n_be.2_be_group_0
#: model:account.group.template,name:l10n_be.be_group_0
msgid "Droits et engagements hors bilan"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a093
#: model:account.account,name:l10n_be.2_a093
#: model:account.account.template,name:l10n_be.a093
msgid "Duties on loan conditions"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_012
#: model:account.group,name:l10n_be.2_be_group_012
#: model:account.group.template,name:l10n_be.be_group_012
msgid "Débiteurs pour autres garanties personnelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_066
#: model:account.group,name:l10n_be.2_be_group_066
#: model:account.group.template,name:l10n_be.be_group_066
msgid "Débiteurs pour devises vendues à terme"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_052
#: model:account.group,name:l10n_be.2_be_group_052
#: model:account.group.template,name:l10n_be.be_group_052
msgid "Débiteurs pour engagements de cession"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_010
#: model:account.group,name:l10n_be.2_be_group_010
#: model:account.group.template,name:l10n_be.be_group_010
msgid "Débiteurs pour engagements sur effets en circulation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_062
#: model:account.group,name:l10n_be.2_be_group_062
#: model:account.group.template,name:l10n_be.be_group_062
msgid "Débiteurs pour marchandises vendues à terme"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_031
#: model:account.group,name:l10n_be.2_be_group_031
#: model:account.group.template,name:l10n_be.be_group_031
msgid "Déposants statutaires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_030
#: model:account.group,name:l10n_be.2_be_group_030
#: model:account.group.template,name:l10n_be.be_group_030
msgid "Dépôts statutaires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_53
#: model:account.group,name:l10n_be.2_be_group_53
#: model:account.group.template,name:l10n_be.be_group_53
msgid "Dépôts à terme"
msgstr ""

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_5
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_5
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_5
msgid "EU privé"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_655
#: model:account.group,name:l10n_be.2_be_group_655
#: model:account.group.template,name:l10n_be.be_group_655
msgid "Ecarts de conversion des devises"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_441
#: model:account.group,name:l10n_be.2_be_group_441
#: model:account.group.template,name:l10n_be.be_group_441
msgid "Effets à payer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_401
#: model:account.group,name:l10n_be.2_be_group_401
#: model:account.group.template,name:l10n_be.be_group_401
msgid "Effets à recevoir"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a696
#: model:account.account,name:l10n_be.2_a696
#: model:account.account.template,name:l10n_be.a696
msgid "Employees' entitlements"
msgstr "Verteilung zugunsten der Arbeitnehmer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a621
#: model:account.account,name:l10n_be.2_a621
#: model:account.account.template,name:l10n_be.a621
msgid "Employers' contribution for social security"
msgstr "Arbeitgeberbeiträge zur Sozialversicherung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a622
#: model:account.account,name:l10n_be.2_a622
#: model:account.account.template,name:l10n_be.a622
msgid "Employers' premiums for extra statutory insurance"
msgstr "Arbeitgeberprämien für außergesetzliche Versicherungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_696
#: model:account.group,name:l10n_be.2_be_group_696
#: model:account.group.template,name:l10n_be.be_group_696
msgid "Employés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_171
#: model:account.group,name:l10n_be.2_be_group_171
#: model:account.group.template,name:l10n_be.be_group_171
msgid "Emprunts obligataires non subordonnés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_170
#: model:account.group,name:l10n_be.2_be_group_170
#: model:account.group.template,name:l10n_be.be_group_170
msgid "Emprunts subordonnés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_32
#: model:account.group,name:l10n_be.2_be_group_32
#: model:account.group.template,name:l10n_be.be_group_32
msgid "En-cours de fabrication"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_050
#: model:account.group,name:l10n_be.2_be_group_050
#: model:account.group.template,name:l10n_be.be_group_050
msgid "Engagements d'acquisition"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_05
#: model:account.group,name:l10n_be.2_be_group_05
#: model:account.group.template,name:l10n_be.be_group_05
msgid "Engagements d'acquisition et de cession d'immobilisations"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_053
#: model:account.group,name:l10n_be.2_be_group_053
#: model:account.group.template,name:l10n_be.be_group_053
msgid "Engagements de cession"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a450
#: model:account.account,name:l10n_be.2_a450
#: model:account.account.template,name:l10n_be.a450
msgid "Estimated taxes payable"
msgstr "Geschätzte Steuerschulden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4508
#: model:account.account,name:l10n_be.2_a4508
#: model:account.account.template,name:l10n_be.a4508
msgid "Estimated taxes payable - Foreign taxes"
msgstr "Geschätzte Steuerschulden - Ausländische Steuern und Abgaben"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_173
#: model:account.group,name:l10n_be.1_be_group_55
#: model:account.group,name:l10n_be.2_be_group_173
#: model:account.group,name:l10n_be.2_be_group_55
#: model:account.group.template,name:l10n_be.be_group_173
#: model:account.group.template,name:l10n_be.be_group_55
msgid "Etablissements de crédit"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_432
#: model:account.group,name:l10n_be.2_be_group_432
#: model:account.group.template,name:l10n_be.be_group_432
msgid "Etablissements de crédit - Crédits d'acceptation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_433
#: model:account.group,name:l10n_be.2_be_group_433
#: model:account.group.template,name:l10n_be.be_group_433
msgid "Etablissements de crédit - Dettes en compte courant"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_430
#: model:account.group,name:l10n_be.2_be_group_430
#: model:account.group.template,name:l10n_be.be_group_430
msgid "Etablissements de crédit - Emprunts en compte à terme fixe"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_431
#: model:account.group,name:l10n_be.2_be_group_431
#: model:account.group.template,name:l10n_be.be_group_431
msgid "Etablissements de crédit - Promesses"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_444
#: model:account.group,name:l10n_be.2_be_group_444
#: model:account.group.template,name:l10n_be.be_group_444
msgid "Factures à recevoir"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a654
#: model:account.account,name:l10n_be.2_a654
#: model:account.account.template,name:l10n_be.a654
msgid "Financial charges - Exchange differences"
msgstr "Finanzaufwendungen - Wechselkursdifferenzen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a655
#: model:account.account,name:l10n_be.2_a655
#: model:account.account.template,name:l10n_be.a655
msgid "Financial charges - Foreign currency translation differences"
msgstr "Finanzaufwendungen - Umrechnungsdifferenzen von Fremdwährungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a659
#: model:account.account,name:l10n_be.2_a659
#: model:account.account.template,name:l10n_be.a659
msgid "Financial charges carried to assets as restructuring costs"
msgstr ""
"Finanzaufwendungen als Restrukturierungskosten ausgewiesene außerordentliche"
" Aufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a754
#: model:account.account,name:l10n_be.2_a754
#: model:account.account.template,name:l10n_be.a754
msgid "Financial income - Exchange differences"
msgstr "Finanzerträge - Wechselkursdifferenzen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a755
#: model:account.account,name:l10n_be.2_a755
#: model:account.account.template,name:l10n_be.a755
msgid "Financial income - Foreign currency translation differences"
msgstr "Finanzerträge - Umrechnungsdifferenzen von Fremdwährungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a330
#: model:account.account,name:l10n_be.2_a330
#: model:account.account.template,name:l10n_be.a330
msgid "Finished goods - Acquisition value"
msgstr "Fertige Erzeugnisse - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a339
#: model:account.account,name:l10n_be.2_a339
#: model:account.account.template,name:l10n_be.a339
msgid "Finished goods - amounts written down"
msgstr "Fertige Erzeugnisse - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a520
#: model:account.account,name:l10n_be.2_a520
#: model:account.account.template,name:l10n_be.a520
msgid "Fixed income securities - Acquisition value"
msgstr "Festverzinsliche Wertpapiere - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a529
#: model:account.account,name:l10n_be.2_a529
#: model:account.account.template,name:l10n_be.a529
msgid "Fixed income securities - Amounts written down"
msgstr "Festverzinsliche Wertpapiere - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a539
#: model:account.account,name:l10n_be.2_a539
#: model:account.account.template,name:l10n_be.a539
msgid "Fixed term deposit - Amounts written down"
msgstr "Terminkonten - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a531
#: model:account.account,name:l10n_be.2_a531
#: model:account.account.template,name:l10n_be.a531
msgid "Fixed term deposit between one month and one year"
msgstr ""
"Terminkonten mit einer Laufzeit von mehr als einem Monat und bis zu einem "
"Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a530
#: model:account.account,name:l10n_be.2_a530
#: model:account.account.template,name:l10n_be.a530
msgid "Fixed term deposit over one year"
msgstr "Terminkonten mit einer Laufzeit von mehr als einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a532
#: model:account.account,name:l10n_be.2_a532
#: model:account.account.template,name:l10n_be.a532
msgid "Fixed term deposit up to one month"
msgstr "Terminkonten mit einer Laufzeit bis zu einem Monat"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_1
#: model:account.group,name:l10n_be.2_be_group_1
#: model:account.group.template,name:l10n_be.be_group_1
msgid ""
"Fonds propres, provisions pour risques et charges et dettes à plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a496
#: model:account.account,name:l10n_be.2_a496
#: model:account.account.template,name:l10n_be.a496
msgid "Foreign currency translation differences - Assets"
msgstr "Umrechnungsdifferenzen von Fremdwährungen - Aktiva"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a497
#: model:account.account,name:l10n_be.2_a497
#: model:account.account.template,name:l10n_be.a497
msgid "Foreign currency translation differences - Liabilities"
msgstr "Umrechnungsdifferenzen von Fremdwährungen - Passiva"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1688
#: model:account.account,name:l10n_be.2_a1688
#: model:account.account.template,name:l10n_be.a1688
msgid "Foreign deferred taxes"
msgstr "Ausländische aufgeschobene Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a673
#: model:account.account,name:l10n_be.2_a673
#: model:account.account.template,name:l10n_be.a673
msgid "Foreign income taxes on the result of prior periods"
msgstr "Ausländische Steuern auf das Ergebnis vorhergehender Geschäftsjahre"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a672
#: model:account.account,name:l10n_be.2_a672
#: model:account.account.template,name:l10n_be.a672
msgid "Foreign income taxes on the result of the current period"
msgstr "Ausländische Steuern auf das Ergebnis des Geschäftsjahres"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a200
#: model:account.account,name:l10n_be.2_a200
#: model:account.account.template,name:l10n_be.a200
msgid "Formation or capital increase expenses"
msgstr "Errichtungsaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a064
#: model:account.account,name:l10n_be.2_a064
#: model:account.account.template,name:l10n_be.a064
msgid "Forward transactions - Currencies purchased (to be received)"
msgstr "Termingeschäfte - Gekaufte (zu erhaltende) Devisen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a067
#: model:account.account,name:l10n_be.2_a067
#: model:account.account.template,name:l10n_be.a067
msgid "Forward transactions - Currencies sold (to be delivered)"
msgstr "Termingeschäfte - Verkaufte (zu liefernde) Devisen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a060
#: model:account.account,name:l10n_be.2_a060
#: model:account.account.template,name:l10n_be.a060
msgid "Forward transactions - Goods purchased (to be received)"
msgstr "Termingeschäfte - Gekaufte (zu erhaltende) Waren"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a063
#: model:account.account,name:l10n_be.2_a063
#: model:account.account.template,name:l10n_be.a063
msgid "Forward transactions - Goods sold (to be delivered)"
msgstr "Termingeschäfte - Verkaufte (zu liefernde) Waren"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_440
#: model:account.group,name:l10n_be.2_be_group_440
#: model:account.group.template,name:l10n_be.be_group_440
msgid "Fournisseurs"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_201
#: model:account.group,name:l10n_be.2_be_group_201
#: model:account.group.template,name:l10n_be.be_group_201
msgid "Frais d'émission d'emprunts"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_20
#: model:account.group,name:l10n_be.2_be_group_20
#: model:account.group.template,name:l10n_be.be_group_20
msgid "Frais d'établissement"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_2
#: model:account.group,name:l10n_be.2_be_group_2
#: model:account.group.template,name:l10n_be.be_group_2
msgid "Frais d'établissement, actifs immobilisés et créances à plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_200
#: model:account.group,name:l10n_be.2_be_group_200
#: model:account.group.template,name:l10n_be.be_group_200
msgid "Frais de constitution et d'augmentation de capital"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_210
#: model:account.group,name:l10n_be.2_be_group_210
#: model:account.group.template,name:l10n_be.be_group_210
msgid "Frais de recherche et de développement"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_204
#: model:account.group,name:l10n_be.2_be_group_204
#: model:account.group.template,name:l10n_be.be_group_204
msgid "Frais de restructuration"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a24
#: model:account.account,name:l10n_be.2_a24
#: model:account.account.template,name:l10n_be.a24
msgid "Furniture and vehicles"
msgstr "Geschäftsausstattung und Fuhrpark"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a241
#: model:account.account,name:l10n_be.2_a241
#: model:account.account.template,name:l10n_be.a241
msgid ""
"Furniture and vehicles owned by the association or the foundation in full "
"property"
msgstr ""
"Geschäftsausstattung und Fuhrpark im Volleigentum der Vereinigung oder "
"Stiftung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a752
#: model:account.account,name:l10n_be.2_a752
#: model:account.account.template,name:l10n_be.a752
msgid "Gain on disposal of current assets"
msgstr "Erträge aus dem Abgang von Gegenständen des Umlaufvermögens"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a741
#: model:account.account,name:l10n_be.2_a741
#: model:account.account.template,name:l10n_be.a741
msgid "Gain on ordinary disposal of tangible fixed assets"
msgstr "Erträge aus dem normalen Abgang von Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a742
#: model:account.account,name:l10n_be.2_a742
#: model:account.account.template,name:l10n_be.a742
msgid "Gain on ordinary disposal of trade debtors"
msgstr ""
"Mehrwerte bei Realisierung von Forderungen aus Lieferungen und Leistungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_00
#: model:account.group,name:l10n_be.2_be_group_00
#: model:account.group.template,name:l10n_be.be_group_00
msgid "Garanties constituées par des tiers pour compte de l'entreprise"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_01
#: model:account.group,name:l10n_be.2_be_group_01
#: model:account.group.template,name:l10n_be.be_group_01
msgid "Garanties personnelles constituées pour compte de tiers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_03
#: model:account.group,name:l10n_be.1_be_group_032
#: model:account.group,name:l10n_be.2_be_group_03
#: model:account.group,name:l10n_be.2_be_group_032
#: model:account.group.template,name:l10n_be.be_group_03
#: model:account.group.template,name:l10n_be.be_group_032
msgid "Garanties reçues"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_023
#: model:account.group,name:l10n_be.2_be_group_023
#: model:account.group.template,name:l10n_be.be_group_023
msgid "Garanties réelles constituées pour compte de tiers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_021
#: model:account.group,name:l10n_be.2_be_group_021
#: model:account.group.template,name:l10n_be.be_group_021
msgid "Garanties réelles constituées pour compte propre"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_02
#: model:account.group,name:l10n_be.2_be_group_02
#: model:account.group.template,name:l10n_be.be_group_02
msgid "Garanties réelles constituées sur avoirs propres"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a733
#: model:account.account,name:l10n_be.2_a733
#: model:account.account.template,name:l10n_be.a733
msgid "Gifts with a recovery right"
msgstr "Schenkungen mit Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a732
#: model:account.account,name:l10n_be.2_a732
#: model:account.account.template,name:l10n_be.a732
msgid "Gifts without any recovery right"
msgstr "Schenkungen ohne Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a041
#: model:account.account,name:l10n_be.2_a041
#: model:account.account.template,name:l10n_be.a041
msgid ""
"Goods and securities held by third parties on their behalf but at the risk "
"and profit of the company"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a074
#: model:account.account,name:l10n_be.2_a074
#: model:account.account.template,name:l10n_be.a074
msgid ""
"Goods and securities held for accounts or at the risk and profit of third "
"parties"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a072
#: model:account.account,name:l10n_be.2_a072
#: model:account.account.template,name:l10n_be.a072
msgid ""
"Goods and values ​​from third parties received on deposit, consignment or "
"custom"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a340
#: model:account.account,name:l10n_be.2_a340
#: model:account.account.template,name:l10n_be.a340
msgid "Goods purchased for resale - Acquisition value"
msgstr "Waren - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a349
#: model:account.account,name:l10n_be.2_a349
#: model:account.account.template,name:l10n_be.a349
msgid "Goods purchased for resale - amounts written down"
msgstr "Waren - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a212
#: model:account.account,name:l10n_be.2_a212
#: model:account.account.template,name:l10n_be.a212
#: model:account.group,name:l10n_be.1_be_group_212
#: model:account.group,name:l10n_be.2_be_group_212
#: model:account.group.template,name:l10n_be.be_group_212
msgid "Goodwill"
msgstr "Goodwill"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a413
#: model:account.account,name:l10n_be.2_a413
#: model:account.account.template,name:l10n_be.a413
msgid "Grants receivable"
msgstr "Zu erhaltende Subventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a032
#: model:account.account,name:l10n_be.2_a032
#: model:account.account.template,name:l10n_be.a032
msgid "Guarantees received"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a098
#: model:account.account,name:l10n_be.2_a098
#: model:account.account.template,name:l10n_be.a098
msgid "Holders of options (buying or selling securities)"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_sortie
msgid "II A la sortie"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations_entree
msgid "III A l'entrée"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_dues
msgid "IV Dues"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_35
#: model:account.group,name:l10n_be.2_be_group_35
#: model:account.group.template,name:l10n_be.be_group_35
msgid "Immeubles destinés à la vente"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_21
#: model:account.group,name:l10n_be.2_be_group_21
#: model:account.group.template,name:l10n_be.be_group_21
msgid "Immobilisation incorporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_27
#: model:account.group,name:l10n_be.2_be_group_27
#: model:account.group.template,name:l10n_be.be_group_27
msgid "Immobilisations corporelles en cours et acomptes versés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_25
#: model:account.group,name:l10n_be.2_be_group_25
#: model:account.group.template,name:l10n_be.be_group_25
msgid "Immobilisations détenues en location-financement et droits similaires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_28
#: model:account.group,name:l10n_be.2_be_group_28
#: model:account.group.template,name:l10n_be.be_group_28
msgid "Immobilisations financières"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a350
#: model:account.account,name:l10n_be.2_a350
#: model:account.account.template,name:l10n_be.a350
msgid "Immovable property intended for sale - Acquisition value"
msgstr "Zum Verkauf bestimmte unbewegliche Gegenstände - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a359
#: model:account.account,name:l10n_be.2_a359
#: model:account.account.template,name:l10n_be.a359
msgid "Immovable property intended for sale - amounts written down"
msgstr ""
"Zum Verkauf bestimmte unbewegliche Gegenstände - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_771
#: model:account.group,name:l10n_be.2_be_group_771
#: model:account.group.template,name:l10n_be.be_group_771
msgid "Impôts belges sur le résultat"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_671
#: model:account.group,name:l10n_be.2_be_group_671
#: model:account.group.template,name:l10n_be.be_group_671
msgid "Impôts belges sur le résultat d'exercices antérieurs"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_670
#: model:account.group,name:l10n_be.2_be_group_670
#: model:account.group.template,name:l10n_be.be_group_670
msgid "Impôts belges sur le résultat de l'exercice"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_168
#: model:account.group,name:l10n_be.2_be_group_168
#: model:account.group.template,name:l10n_be.be_group_168
msgid "Impôts différés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_412
#: model:account.group,name:l10n_be.2_be_group_412
#: model:account.group.template,name:l10n_be.be_group_412
msgid "Impôts et précomptes à récupérer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_452
#: model:account.group,name:l10n_be.2_be_group_452
#: model:account.group.template,name:l10n_be.be_group_452
msgid "Impôts et taxes à payer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_67
#: model:account.group,name:l10n_be.2_be_group_67
#: model:account.group.template,name:l10n_be.be_group_67
msgid "Impôts sur le résultat"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_773
#: model:account.group,name:l10n_be.2_be_group_773
#: model:account.group.template,name:l10n_be.be_group_773
msgid "Impôts étrangers sur le résultat"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_673
#: model:account.group,name:l10n_be.2_be_group_673
#: model:account.group.template,name:l10n_be.be_group_673
msgid "Impôts étrangers sur le résultat d'exercices antérieurs"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_672
#: model:account.group,name:l10n_be.2_be_group_672
#: model:account.group.template,name:l10n_be.be_group_672
msgid "Impôts étrangers sur le résultat de l'exercice"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a751
#: model:account.account,name:l10n_be.2_a751
#: model:account.account.template,name:l10n_be.a751
msgid "Income from current assets"
msgstr "Erträge aus Gegenständen des Umlaufvermögens"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a750
#: model:account.account,name:l10n_be.2_a750
#: model:account.account.template,name:l10n_be.a750
msgid "Income from financial fixed assets"
msgstr "Erträge aus Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7170
#: model:account.account,name:l10n_be.2_a7170
#: model:account.account.template,name:l10n_be.a7170
msgid "Increase (decrease) in contracts in progress - Acquisition value"
msgstr ""
"Zunahme (Abnahme) der in Ausführung befindlichen Bestellungen - "
"Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7171
#: model:account.account,name:l10n_be.2_a7171
#: model:account.account.template,name:l10n_be.a7171
msgid "Increase (decrease) in contracts in progress - Profit recognized"
msgstr ""
"Zunahme (Abnahme) der in Ausführung befindlichen Bestellungen - Aktivierte "
"Gewinnanteile"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a713
#: model:account.account,name:l10n_be.2_a713
#: model:account.account.template,name:l10n_be.a713
msgid "Increase (decrease) in stocks of finished goods"
msgstr "Zunahme (Abnahme) des Bestandes an fertigen Erzeugnisse"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a71
#: model:account.account,name:l10n_be.2_a71
#: model:account.account.template,name:l10n_be.a71
msgid ""
"Increase (decrease) in stocks of finished goods and work and contracts in "
"progress"
msgstr ""
"Zunahme (Abnahme) des Bestandes an fertigen und unfertigen Erzeugnissen und "
"an in Ausführung befindlichen Bestellungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a715
#: model:account.account,name:l10n_be.2_a715
#: model:account.account.template,name:l10n_be.a715
msgid ""
"Increase (decrease) in stocks of immovable property constructed for resale"
msgstr ""
"Zunahme (Abnahme) des Bestandes an zum Verkauf bestimmten, hergestellten "
"unbeweglichen Gegenstände"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a712
#: model:account.account,name:l10n_be.2_a712
#: model:account.account.template,name:l10n_be.a712
msgid "Increase (decrease) in work in progress"
msgstr "Zunahme (Abnahme) des Bestandes an unfertigen Erzeugnisse"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_23
#: model:account.group,name:l10n_be.1_be_group_251
#: model:account.group,name:l10n_be.2_be_group_23
#: model:account.group,name:l10n_be.2_be_group_251
#: model:account.group.template,name:l10n_be.be_group_23
#: model:account.group.template,name:l10n_be.be_group_251
msgid "Installations, machines et outillage"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a213
#: model:account.account,name:l10n_be.2_a213
#: model:account.account.template,name:l10n_be.a213
msgid "Intangible fixed assets - Advance payments"
msgstr "Immaterielle Anlagewerte - Geleistete Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6500
#: model:account.account,name:l10n_be.2_a6500
#: model:account.account.template,name:l10n_be.a6500
msgid "Interests, commissions and other charges relating to debts"
msgstr "Zinsen, Provisionen und Kosten der Verbindlichkeiten"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_794
#: model:account.group,name:l10n_be.2_be_group_794
#: model:account.group.template,name:l10n_be.be_group_794
msgid "Intervention d'associés (ou du propriétaire) dans la perte"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a15
#: model:account.account,name:l10n_be.2_a15
#: model:account.account.template,name:l10n_be.a15
msgid "Investment grants"
msgstr "Kapitalsubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a753
#: model:account.account,name:l10n_be.2_a753
#: model:account.account.template,name:l10n_be.a753
msgid "Investment grants and interest subsidies"
msgstr "Kapital- und Zinssubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a151
#: model:account.account,name:l10n_be.2_a151
#: model:account.account.template,name:l10n_be.a151
msgid "Investment grants received in cash"
msgstr "In Barmitteln erhaltene Kapitalsubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a152
#: model:account.account,name:l10n_be.2_a152
#: model:account.account.template,name:l10n_be.a152
msgid "Investment grants received in kind"
msgstr "In Naturalien erhaltene Kapitalsubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a444
#: model:account.account,name:l10n_be.2_a444
#: model:account.account.template,name:l10n_be.a444
msgid "Invoices to be received payable within one year"
msgstr "Zu erhaltende Rechnungen mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a100
#: model:account.account,name:l10n_be.2_a100
#: model:account.account.template,name:l10n_be.a100
msgid "Issued capital"
msgstr "Gezeichnetes Kapital"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_move
msgid "Journal Entry"
msgstr "Buchungssatz"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a220
#: model:account.account,name:l10n_be.2_a220
#: model:account.account.template,name:l10n_be.a220
msgid "Land"
msgstr "Grundstücke"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2201
#: model:account.account,name:l10n_be.2_a2201
#: model:account.account.template,name:l10n_be.a2201
msgid "Land owned by the association or the foundation in full property"
msgstr "Im Volleigentum der Vereinigung oder Stiftung befindliche Grundstücke"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a422
#: model:account.account,name:l10n_be.2_a422
#: model:account.account.template,name:l10n_be.a422
msgid ""
"Leasing and similar obligations payable after more than one year falling due"
" within one year"
msgstr ""
"Innerhalb eines Jahres fällig werdende Verbindlichkeiten aufgrund von "
"Leasing- und ähnlichen Verträgen mit einer ursprünglichen Laufzeit von mehr "
"als einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a252
#: model:account.account,name:l10n_be.2_a252
#: model:account.account.template,name:l10n_be.a252
msgid "Leasing and similar rights - Furniture and vehicles"
msgstr "Leasing und ähnliche Rechte - Geschäftsausstattung und Fuhrpark"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a250
#: model:account.account,name:l10n_be.2_a250
#: model:account.account.template,name:l10n_be.a250
msgid "Leasing and similar rights - Land and buildings"
msgstr "Leasing und ähnliche Rechte - Grundstücke und Bauten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a251
#: model:account.account,name:l10n_be.2_a251
#: model:account.account.template,name:l10n_be.a251
msgid "Leasing and similar rights - Plant, machinery and equipment"
msgstr ""
"Leasing und ähnliche Rechte - Anlagen, Maschinen und Betriebsausstattung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a735
#: model:account.account,name:l10n_be.2_a735
#: model:account.account.template,name:l10n_be.a735
msgid "Legacies with a recovery right"
msgstr "Legate mit Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a734
#: model:account.account,name:l10n_be.2_a734
#: model:account.account.template,name:l10n_be.a734
msgid "Legacies without any recovery right"
msgstr "Legate ohne Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a130
#: model:account.account,name:l10n_be.2_a130
#: model:account.account.template,name:l10n_be.a130
msgid "Legal reserve"
msgstr "Gesetzliche Rücklage"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a487
#: model:account.account,name:l10n_be.2_a487
#: model:account.account.template,name:l10n_be.a487
msgid "Lent securities to return"
msgstr "Entliehen zurückzugebende Aktien"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_l10nbe_chart_template_liquidity_transfer
#: model:account.account,name:l10n_be.2_l10nbe_chart_template_liquidity_transfer
#: model:account.account.template,name:l10n_be.l10nbe_chart_template_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Transferkonto"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a201
#: model:account.account,name:l10n_be.2_a201
#: model:account.account.template,name:l10n_be.a201
msgid "Loan issue expenses"
msgstr "Emissionskosten von Anleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0702
#: model:account.account,name:l10n_be.2_a0702
#: model:account.account.template,name:l10n_be.a0702
msgid "Long-term usage rights - On furniture and rolling stock"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0701
#: model:account.account,name:l10n_be.2_a0701
#: model:account.account.template,name:l10n_be.a0701
msgid "Long-term usage rights - On installations, machines and tools"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a0700
#: model:account.account,name:l10n_be.2_a0700
#: model:account.account.template,name:l10n_be.a0700
msgid "Long-term usage rights - On land and buildings"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a690
#: model:account.account,name:l10n_be.2_a690
#: model:account.account.template,name:l10n_be.a690
msgid "Loss brought forward from previous year"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a141
#: model:account.account,name:l10n_be.2_a141
#: model:account.account.template,name:l10n_be.a141
msgid "Loss carried forward"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a641
#: model:account.account,name:l10n_be.2_a641
#: model:account.account.template,name:l10n_be.a641
msgid "Loss on ordinary disposal of tangible fixed assets"
msgstr "Verluste aus dem normalen Abgang von Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a642
#: model:account.account,name:l10n_be.2_a642
#: model:account.account.template,name:l10n_be.a642
msgid "Loss on ordinary disposal of trade debtors"
msgstr ""
"Minderwerte bei Realisierung von Forderungen aus Lieferungen und Leistungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a652
#: model:account.account,name:l10n_be.2_a652
#: model:account.account.template,name:l10n_be.a652
msgid "Losses on disposal of current assets"
msgstr "Verluste aus dem Abgang von Gegenständen des Umlaufvermögens"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a793
#: model:account.account,name:l10n_be.2_a793
#: model:account.account.template,name:l10n_be.a793
msgid "Losses to be carried forward"
msgstr "Verlustvortrag auf neue Rechnung"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_34
#: model:account.group,name:l10n_be.2_be_group_34
#: model:account.group.template,name:l10n_be.be_group_34
msgid "Marchandises"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_060
#: model:account.group,name:l10n_be.2_be_group_060
#: model:account.group.template,name:l10n_be.be_group_060
msgid "Marchandises achetées à terme - à recevoir"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_063
#: model:account.group,name:l10n_be.2_be_group_063
#: model:account.group.template,name:l10n_be.be_group_063
msgid "Marchandises vendues à terme - à livrer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_06
#: model:account.group,name:l10n_be.2_be_group_06
#: model:account.group.template,name:l10n_be.be_group_06
msgid "Marchés à terme"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a429
#: model:account.account,name:l10n_be.2_a429
#: model:account.account.template,name:l10n_be.a429
msgid ""
"Miscellaneous amounts payable after more than one year falling due within "
"one year"
msgstr ""
"Andere innerhalb eines Jahres fällig werdende Verbindlichkeiten mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1792
#: model:account.account,name:l10n_be.2_a1792
#: model:account.account.template,name:l10n_be.a1792
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Cash Deposit"
msgstr ""
"Andere Verbindlichkeiten mit einer Restlaufzeit von mehr als einem Jahr - "
"Kautionen/Bürgschaften"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1790
#: model:account.account,name:l10n_be.2_a1790
#: model:account.account.template,name:l10n_be.a1790
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Interest-bearing"
msgstr ""
"Andere Verbindlichkeiten mit einer Restlaufzeit von mehr als einem Jahr - "
"Verzinslich"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1791
#: model:account.account,name:l10n_be.2_a1791
#: model:account.account.template,name:l10n_be.a1791
msgid ""
"Miscellaneous amounts payable with a remaining term of more than one year - "
"Non interest-bearing or with an abnormally low interest rate"
msgstr ""
"Andere Verbindlichkeiten mit einer Restlaufzeit von mehr als einem Jahr - "
"Unverzinslich oder niedrigverzinslich"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a480
#: model:account.account,name:l10n_be.2_a480
#: model:account.account.template,name:l10n_be.a480
msgid ""
"Miscellaneous amounts payable within one year - Debentures and matured "
"coupons"
msgstr ""
"Sonstige Verbindlichkeiten mit einer Restlaufzeit bis zu einem Jahr - "
"Fällige Schuldverschreibungen und Kupons"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a483
#: model:account.account,name:l10n_be.2_a483
#: model:account.account.template,name:l10n_be.a483
msgid "Miscellaneous amounts payable within one year - Grants to repay"
msgstr ""
"Sonstige Verbindlichkeiten mit einer Restlaufzeit bis zu einem Jahr - "
"Zurückzuzahlende Subventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a488
#: model:account.account,name:l10n_be.2_a488
#: model:account.account.template,name:l10n_be.a488
msgid ""
"Miscellaneous amounts payable within one year - Guarantees received in cash"
msgstr ""
"Sonstige Verbindlichkeiten mit einer Restlaufzeit bis zu einem Jahr - In "
"Geldmitteln erhaltene Kautionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4890
#: model:account.account,name:l10n_be.2_a4890
#: model:account.account.template,name:l10n_be.a4890
msgid ""
"Miscellaneous amounts payable within one year - Sundry interest-bearing "
"amounts payable"
msgstr ""
"Sonstige Verbindlichkeiten mit einer Restlaufzeit bis zu einem Jahr - Übrige"
" verzinsliche Verbindlichkeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4891
#: model:account.account,name:l10n_be.2_a4891
#: model:account.account.template,name:l10n_be.a4891
msgid ""
"Miscellaneous amounts payable within one year - Sundry non interest-bearing "
"amounts payable or with an abnormally low interest rate"
msgstr ""
"Sonstige Verbindlichkeiten mit einer Restlaufzeit bis zu einem Jahr - Übrige"
" Verbindlichkeiten, unverzinslich oder mit einem ungewöhnlich niedrigen "
"Zinssatz"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_24
#: model:account.group,name:l10n_be.1_be_group_252
#: model:account.group,name:l10n_be.2_be_group_24
#: model:account.group,name:l10n_be.2_be_group_252
#: model:account.group.template,name:l10n_be.be_group_24
#: model:account.group.template,name:l10n_be.be_group_252
msgid "Mobilier et matériel roulant"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_652
#: model:account.group,name:l10n_be.2_be_group_652
#: model:account.group.template,name:l10n_be.be_group_652
msgid "Moins-values sur réalisation d'actifs circulants"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_663
#: model:account.group,name:l10n_be.2_be_group_663
#: model:account.group.template,name:l10n_be.be_group_663
msgid "Moins-values sur réalisation d'actifs immobilisés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_641
#: model:account.group,name:l10n_be.2_be_group_641
#: model:account.group.template,name:l10n_be.be_group_641
msgid "Moins-values sur réalisations courantes d'immobilisations corporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_642
#: model:account.group,name:l10n_be.2_be_group_642
#: model:account.group.template,name:l10n_be.be_group_642
msgid "Moins-values sur réalisations de créances commerciales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_511
#: model:account.group,name:l10n_be.2_be_group_511
#: model:account.group.template,name:l10n_be.be_group_511
msgid "Montants non appelés (-)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a64012
#: model:account.account,name:l10n_be.2_a64012
#: model:account.account.template,name:l10n_be.a64012
msgid "Non deductible taxes"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2915
#: model:account.account,name:l10n_be.2_a2915
#: model:account.account.template,name:l10n_be.a2915
msgid ""
"Non interest-bearing amounts receivable after more than one year or with an "
"abnormally low interest rate"
msgstr ""
"Unverzinsliche Forderungen mit einer Restlaufzeit von mehr als einem Jahr "
"oder Forderungen mit einem ungewöhnlich niedrigen Zinssatz"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a415
#: model:account.account,name:l10n_be.2_a415
#: model:account.account.template,name:l10n_be.a415
msgid ""
"Non interest-bearing amounts receivable within one year or with an "
"abnormally low interest rate"
msgstr ""
"Unverzinsliche Forderungen mit einer Restlaufzeit bis zu einem Jahr oder "
"Forderungen mit einem ungewöhnlich niedrigen Zinssatz"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6600
#: model:account.account,name:l10n_be.2_a6600
#: model:account.account.template,name:l10n_be.a6600
msgid ""
"Non-recurring depreciation of and amounts written off formation expenses"
msgstr ""
"Nicht wiederkehrende Abschreibungen und Wertminderungen auf "
"Errichtungsaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6601
#: model:account.account,name:l10n_be.2_a6601
#: model:account.account.template,name:l10n_be.a6601
msgid ""
"Non-recurring depreciation of and amounts written off intangible fixed "
"assets"
msgstr ""
"Nicht wiederkehrende Abschreibungen und Wertminderungen auf immaterielle "
"Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6602
#: model:account.account,name:l10n_be.2_a6602
#: model:account.account.template,name:l10n_be.a6602
msgid ""
"Non-recurring depreciation of and amounts written off tangible fixed assets"
msgstr ""
"Nicht wiederkehrende Abschreibungen und Wertminderungen auf materielle "
"Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6691
#: model:account.account,name:l10n_be.2_a6691
#: model:account.account.template,name:l10n_be.a6691
msgid ""
"Non-recurring financial charges carried to assets as restructuring costs"
msgstr ""
"Als Restrukturierungskosten ausgewiesene nicht wiederkehrende "
"Finanzaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6690
#: model:account.account,name:l10n_be.2_a6690
#: model:account.account.template,name:l10n_be.a6690
msgid ""
"Non-recurring operating charges carried to assets as restructuring costs"
msgstr ""
"Als Restrukturierungskosten ausgewiesene nicht wiederkehrende betriebliche "
"Aufwendungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_480
#: model:account.group,name:l10n_be.2_be_group_480
#: model:account.group.template,name:l10n_be.be_group_480
msgid "Obligations et coupons échus"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_56
#: model:account.group,name:l10n_be.2_be_group_56
#: model:account.group.template,name:l10n_be.be_group_56
msgid "Office des chèques postaux"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_454
#: model:account.group,name:l10n_be.2_be_group_454
#: model:account.group.template,name:l10n_be.be_group_454
msgid "Office national de la sécurité sociale"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a094
#: model:account.account,name:l10n_be.2_a094
#: model:account.account.template,name:l10n_be.a094
msgid "Ongoing litigation"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a737
#: model:account.account,name:l10n_be.2_a737
#: model:account.account.template,name:l10n_be.a737
msgid "Operating Subsidies"
msgstr "Betriebssubventionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a643
#: model:account.account,name:l10n_be.2_a643
#: model:account.account.template,name:l10n_be.a643
msgid "Operating charges - Gifts"
msgstr "Betriebliche Aufwendungen - Schenkungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6431
#: model:account.account,name:l10n_be.2_a6431
#: model:account.account.template,name:l10n_be.a6431
msgid "Operating charges - Gifts with a recovery right"
msgstr "Betriebliche Aufwendungen - Schenkungen mit Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6432
#: model:account.account,name:l10n_be.2_a6432
#: model:account.account.template,name:l10n_be.a6432
msgid "Operating charges - Gifts without any recovery right"
msgstr "Betriebliche Aufwendungen - Schenkungen ohne Rücknahmerecht"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a649
#: model:account.account,name:l10n_be.2_a649
#: model:account.account.template,name:l10n_be.a649
msgid "Operating charges carried to assets as restructuring costs"
msgstr ""
"Auf der Aktivseite als Restrukturierungskosten ausgewiesene betriebliche "
"Aufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a740
#: model:account.account,name:l10n_be.2_a740
#: model:account.account.template,name:l10n_be.a740
msgid "Operating subsidies and compensatory amounts"
msgstr ""
"Betriebssubventionen und von der öffentlichen Hand erhaltene "
"Ausgleichszahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a099
#: model:account.account,name:l10n_be.2_a099
#: model:account.account.template,name:l10n_be.a099
msgid "Options (buy or sell) on securities issued."
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_operations
msgid "Opérations"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a668
#: model:account.account,name:l10n_be.2_a668
#: model:account.account.template,name:l10n_be.a668
msgid "Other  non-recurring financial charges"
msgstr "Sonstige nicht wiederkehrende Finanzaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a769
#: model:account.account,name:l10n_be.2_a769
#: model:account.account.template,name:l10n_be.a769
msgid "Other  non-recurring financial income"
msgstr "Sonstige nicht wiederkehrende Finanzerträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a473
#: model:account.account,name:l10n_be.2_a473
#: model:account.account.template,name:l10n_be.a473
msgid "Other allocations"
msgstr "Sonstige Berechtigte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a697
#: model:account.account,name:l10n_be.2_a697
#: model:account.account.template,name:l10n_be.a697
msgid "Other allocations entitlements"
msgstr "Verteilung zugunsten andere Berechtigte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2919
#: model:account.account,name:l10n_be.2_a2919
#: model:account.account.template,name:l10n_be.a2919
msgid ""
"Other amounts receivable after more than one year - Amounts written down"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit von mehr als einem Jahr - "
"Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2911
#: model:account.account,name:l10n_be.2_a2911
#: model:account.account.template,name:l10n_be.a2911
msgid "Other amounts receivable after more than one year - Bills receivable"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit von mehr als einem Jahr - "
"Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2910
#: model:account.account,name:l10n_be.2_a2910
#: model:account.account.template,name:l10n_be.a2910
msgid "Other amounts receivable after more than one year - Current account"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit von mehr als einem Jahr - "
"Kontoforderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2917
#: model:account.account,name:l10n_be.2_a2917
#: model:account.account.template,name:l10n_be.a2917
msgid "Other amounts receivable after more than one year - Doubtful amounts"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit von mehr als einem Jahr - "
"Zweifelhafte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2817
#: model:account.account,name:l10n_be.2_a2817
#: model:account.account.template,name:l10n_be.a2817
msgid ""
"Other amounts receivable from affiliated enterprises - Doubtful amounts"
msgstr "Forderungen an verbundenen Unternehmen - Zweifelhafte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a419
#: model:account.account,name:l10n_be.2_a419
#: model:account.account.template,name:l10n_be.a419
msgid "Other amounts receivable within one year - Amounts written down"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit bis zu einem Jahr - Gebuchte "
"Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a417
#: model:account.account,name:l10n_be.2_a417
#: model:account.account.template,name:l10n_be.a417
msgid "Other amounts receivable within one year - Doubtful amounts"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit bis zu einem Jahr - Zweifelhafte"
" Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a418
#: model:account.account,name:l10n_be.2_a418
#: model:account.account.template,name:l10n_be.a418
msgid "Other amounts receivable within one year - Guarantees paid in cash"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit bis zu einem Jahr - Gezahlte "
"Kautionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a414
#: model:account.account,name:l10n_be.2_a414
#: model:account.account.template,name:l10n_be.a414
msgid "Other amounts receivable within one year - Income receivable"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit bis zu einem Jahr - Zu "
"erhaltende Erträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a416
#: model:account.account,name:l10n_be.2_a416
#: model:account.account.template,name:l10n_be.a416
msgid "Other amounts receivable within one year - Sundry amounts"
msgstr ""
"Sonstige Forderungen mit einer Restlaufzeit bis zu einem Jahr - Übrige "
"Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2212
#: model:account.account,name:l10n_be.2_a2212
#: model:account.account.template,name:l10n_be.a2212
msgid "Other building"
msgstr "Sonstige Bauten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2222
#: model:account.account,name:l10n_be.2_a2222
#: model:account.account.template,name:l10n_be.a2222
msgid "Other built-up lands"
msgstr "Sonstige bebaute Grundstücke"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6502
#: model:account.account,name:l10n_be.2_a6502
#: model:account.account.template,name:l10n_be.a6502
msgid "Other debt charges"
msgstr "Sonstige Aufwendungen für Verbindlichkeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2859
#: model:account.account,name:l10n_be.2_a2859
#: model:account.account.template,name:l10n_be.a2859
msgid "Other financial assets - Amounts written down"
msgstr "Sonstige Finanzanlagen - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2851
#: model:account.account,name:l10n_be.2_a2851
#: model:account.account.template,name:l10n_be.a2851
msgid "Other financial assets - Bills receivable"
msgstr "Sonstige Finanzanlagen - Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a288
#: model:account.account,name:l10n_be.2_a288
#: model:account.account.template,name:l10n_be.a288
msgid "Other financial assets - Cash Guarantees"
msgstr "Sonstige Finanzanlagen - Gezahlte Kautionen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2850
#: model:account.account,name:l10n_be.2_a2850
#: model:account.account.template,name:l10n_be.a2850
msgid "Other financial assets - Current account"
msgstr "Sonstige Finanzanlagen - Kontoforderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2857
#: model:account.account,name:l10n_be.2_a2857
#: model:account.account.template,name:l10n_be.a2857
msgid "Other financial assets - Doubtful amounts"
msgstr "Sonstige Finanzanlagen - Zweifelhafte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2852
#: model:account.account,name:l10n_be.2_a2852
#: model:account.account.template,name:l10n_be.a2852
msgid "Other financial assets - Fixed income securities"
msgstr "Sonstige Finanzanlagen - Festverzinsliche Wertpapiere"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a202
#: model:account.account,name:l10n_be.2_a202
#: model:account.account.template,name:l10n_be.a202
msgid "Other formation expenses"
msgstr "Andere Errichtungs- und Erweiterungsaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a242
#: model:account.account,name:l10n_be.2_a242
#: model:account.account.template,name:l10n_be.a242
msgid "Other furniture and vehicles"
msgstr "Sonstige Geschäftsausstattung und Führpark"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2202
#: model:account.account,name:l10n_be.2_a2202
#: model:account.account.template,name:l10n_be.a2202
msgid "Other land"
msgstr "Sonstige Grundstücke"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a424
#: model:account.account,name:l10n_be.2_a424
#: model:account.account.template,name:l10n_be.a424
msgid ""
"Other loans payable after more than one year falling due within one year"
msgstr ""
"Sonstige Innerhalb eines Jahres fällig werdende Anleihen mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a439
#: model:account.account,name:l10n_be.2_a439
#: model:account.account.template,name:l10n_be.a439
msgid "Other loans payable within one year"
msgstr "Sonstige Anleihen mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a174
#: model:account.account,name:l10n_be.2_a174
#: model:account.account.template,name:l10n_be.a174
msgid "Other loans with a remaining term of more than one year"
msgstr "Sonstige Anleihen mit einer Restlaufzeit von mehr als einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2840
#: model:account.account,name:l10n_be.2_a2840
#: model:account.account.template,name:l10n_be.a2840
msgid "Other participating interests and shares - Acquisition value"
msgstr "Sonstige Beteiligungen und Gesellschaftsrechte - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2849
#: model:account.account,name:l10n_be.2_a2849
#: model:account.account.template,name:l10n_be.a2849
msgid "Other participating interests and shares - Amounts written down"
msgstr "Sonstige Beteiligungen und Gesellschaftsrechte - Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2848
#: model:account.account,name:l10n_be.2_a2848
#: model:account.account.template,name:l10n_be.a2848
msgid "Other participating interests and shares - Revaluation surpluses"
msgstr "Sonstige Beteiligungen und Gesellschaftsrechte - Mehrwerte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2841
#: model:account.account,name:l10n_be.2_a2841
#: model:account.account.template,name:l10n_be.a2841
msgid "Other participating interests and shares - Uncalled amounts"
msgstr ""
"Sonstige Beteiligungen und Gesellschaftsrechte - Nicht eingeforderte Beträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a623
#: model:account.account,name:l10n_be.2_a623
#: model:account.account.template,name:l10n_be.a623
msgid "Other personnel costs"
msgstr "Sonstige Personalaufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a232
#: model:account.account,name:l10n_be.2_a232
#: model:account.account.template,name:l10n_be.a232
msgid "Other plant, machinery and equipment"
msgstr "Sonstige Anlagen, Maschinen und Betriebsausstattung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1311
#: model:account.account,name:l10n_be.2_a1311
#: model:account.account.template,name:l10n_be.a1311
msgid "Other reserves not available"
msgstr "Sonstige nicht verfügbare Rücklagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a223
#: model:account.account,name:l10n_be.2_a223
#: model:account.account.template,name:l10n_be.a223
msgid "Other rights to immovable property"
msgstr "Sonstige dingliche Rechte an unbeweglichen Gütern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2232
#: model:account.account,name:l10n_be.2_a2232
#: model:account.account.template,name:l10n_be.a2232
msgid "Other rights to immovable property - Other"
msgstr "Sonstige dingliche Rechte an unbeweglichen Gegenständen - Sonstige"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2231
#: model:account.account,name:l10n_be.2_a2231
#: model:account.account.template,name:l10n_be.a2231
msgid ""
"Other rights to immovable property belonging to the association or the "
"foundation in full property"
msgstr ""
"Sonstige im Volleigentum der Vereinigung oder Stiftung befindliche dingliche"
" Rechte an unbeweglichen Gegenständen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a26
#: model:account.account,name:l10n_be.2_a26
#: model:account.account.template,name:l10n_be.a26
msgid "Other tangible fixed assets"
msgstr "Sonstige Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a262
#: model:account.account,name:l10n_be.2_a262
#: model:account.account.template,name:l10n_be.a262
msgid "Other tangible fixed assets - Other"
msgstr "Sonstige Sachanlagen - Sonstige"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a261
#: model:account.account,name:l10n_be.2_a261
#: model:account.account.template,name:l10n_be.a261
msgid ""
"Other tangible fixed assets owned by the association or the foundation in "
"full property"
msgstr ""
"Im Volleigentum der Vereinigung oder Stiftung befindliche Sonstige "
"Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a72
#: model:account.account,name:l10n_be.2_a72
#: model:account.account.template,name:l10n_be.a72
msgid "Own work capitalised"
msgstr "Andere aktivierte Eigenleistungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a794
#: model:account.account,name:l10n_be.2_a794
#: model:account.account.template,name:l10n_be.a794
msgid "Owners' contribution in respect of losses"
msgstr "Teilnahme der Gesellschafter am Verlust"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2800
#: model:account.account,name:l10n_be.2_a2800
#: model:account.account.template,name:l10n_be.a2800
msgid ""
"Participating interests and shares in associated enterprises - Acquisition "
"value"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Verbundenen Unternehmen - "
"Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2809
#: model:account.account,name:l10n_be.2_a2809
#: model:account.account.template,name:l10n_be.a2809
msgid ""
"Participating interests and shares in associated enterprises - Amounts "
"written down"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Verbundenen Unternehmen - "
"Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2808
#: model:account.account,name:l10n_be.2_a2808
#: model:account.account.template,name:l10n_be.a2808
msgid ""
"Participating interests and shares in associated enterprises - Revaluation "
"surpluses"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Verbundenen Unternehmen - Mehrwerte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2801
#: model:account.account,name:l10n_be.2_a2801
#: model:account.account.template,name:l10n_be.a2801
msgid ""
"Participating interests and shares in associated enterprises - Uncalled "
"amounts"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Verbundenen Unternehmen - Nicht "
"eingeforderte Beträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2820
#: model:account.account,name:l10n_be.2_a2820
#: model:account.account.template,name:l10n_be.a2820
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Acquisition value"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2829
#: model:account.account,name:l10n_be.2_a2829
#: model:account.account.template,name:l10n_be.a2829
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Amounts written down"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht - Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2828
#: model:account.account,name:l10n_be.2_a2828
#: model:account.account.template,name:l10n_be.a2828
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Revaluation surpluses"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht - Mehrwerte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2821
#: model:account.account,name:l10n_be.2_a2821
#: model:account.account.template,name:l10n_be.a2821
msgid ""
"Participating interests and shares in enterprises linked by a participating "
"interest - Uncalled amounts"
msgstr ""
"Beteiligungen und Gesellschaftsrechte an Unternehmen, mit denen ein "
"Beteiligungsverhältnis besteht - Nicht eingeforderte Beträge"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_282
#: model:account.group,name:l10n_be.2_be_group_282
#: model:account.group.template,name:l10n_be.be_group_282
msgid ""
"Participations dans des entreprises avec lesquelles il existe un lien de "
"participation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_280
#: model:account.group,name:l10n_be.2_be_group_280
#: model:account.group.template,name:l10n_be.be_group_280
msgid "Participations dans des entreprises liées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_624
#: model:account.group,name:l10n_be.2_be_group_624
#: model:account.group.template,name:l10n_be.be_group_624
msgid "Pensions de retraite et de survie"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_617
#: model:account.group,name:l10n_be.2_be_group_617
#: model:account.group.template,name:l10n_be.be_group_617
msgid ""
"Personnel intérimaire et personnes mises à la disposition de l'entreprise"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_690
#: model:account.group,name:l10n_be.2_be_group_690
#: model:account.group.template,name:l10n_be.be_group_690
msgid "Perte reportée de l'exercice précédent"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_793
#: model:account.group,name:l10n_be.2_be_group_793
#: model:account.group.template,name:l10n_be.be_group_793
msgid "Perte à reporter"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5101
#: model:account.group,name:l10n_be.1_be_group_5191
#: model:account.group,name:l10n_be.2_be_group_5101
#: model:account.group,name:l10n_be.2_be_group_5191
#: model:account.group.template,name:l10n_be.be_group_5101
#: model:account.group.template,name:l10n_be.be_group_5191
msgid "Placements de trésorerie autres que placements à revenu fixe"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_5
#: model:account.group,name:l10n_be.2_be_group_5
#: model:account.group.template,name:l10n_be.be_group_5
msgid "Placements de trésorerie et valeurs disponibles"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a23
#: model:account.account,name:l10n_be.2_a23
#: model:account.account.template,name:l10n_be.a23
msgid "Plant, machinery and equipment"
msgstr "Anlagen, Maschinen und Betriebsausstattung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a231
#: model:account.account,name:l10n_be.2_a231
#: model:account.account.template,name:l10n_be.a231
msgid ""
"Plant, machinery and equipment owned by the association or the foundation in"
" full property"
msgstr ""
"Anlagen, Maschinen und Betriebsausstattung im Volleigentum der Vereinigung "
"oder Stiftung"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_12
#: model:account.group,name:l10n_be.2_be_group_12
#: model:account.group.template,name:l10n_be.be_group_12
msgid "Plus-values de réévaluation"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_121
#: model:account.group,name:l10n_be.2_be_group_121
#: model:account.group.template,name:l10n_be.be_group_121
msgid "Plus-values de réévaluation sur immobilisations corporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_122
#: model:account.group,name:l10n_be.2_be_group_122
#: model:account.group.template,name:l10n_be.be_group_122
msgid "Plus-values de réévaluation sur immobilisations financières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_120
#: model:account.group,name:l10n_be.2_be_group_120
#: model:account.group.template,name:l10n_be.be_group_120
msgid "Plus-values de réévaluation sur immobilisations incorporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_123
#: model:account.group,name:l10n_be.2_be_group_123
#: model:account.group.template,name:l10n_be.be_group_123
msgid "Plus-values de réévaluation sur stocks"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_752
#: model:account.group,name:l10n_be.2_be_group_752
#: model:account.group.template,name:l10n_be.be_group_752
msgid "Plus-values sur réalisation d'actifs circulants"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_763
#: model:account.group,name:l10n_be.2_be_group_763
#: model:account.group.template,name:l10n_be.be_group_763
msgid "Plus-values sur réalisation d'actifs immobilisés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_742
#: model:account.group,name:l10n_be.2_be_group_742
#: model:account.group.template,name:l10n_be.be_group_742
msgid "Plus-values sur réalisation de créances commerciales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_741
#: model:account.group,name:l10n_be.2_be_group_741
#: model:account.group.template,name:l10n_be.be_group_741
msgid "Plus-values sur réalisations courantes d'immobilisations corporelles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_11
#: model:account.group,name:l10n_be.2_be_group_11
#: model:account.group.template,name:l10n_be.be_group_11
msgid "Primes d'émission"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_622
#: model:account.group,name:l10n_be.2_be_group_622
#: model:account.group.template,name:l10n_be.be_group_622
msgid "Primes patronales pour assurances extra-légales"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a073
#: model:account.account,name:l10n_be.2_a073
#: model:account.account.template,name:l10n_be.a073
msgid "Principals and depositors of goods and securities"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_72
#: model:account.group,name:l10n_be.2_be_group_72
#: model:account.group.template,name:l10n_be.be_group_72
msgid "Production immobilisée"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_7
#: model:account.group,name:l10n_be.2_be_group_7
#: model:account.group.template,name:l10n_be.be_group_7
msgid "Produits"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_491
#: model:account.group,name:l10n_be.2_be_group_491
#: model:account.group.template,name:l10n_be.be_group_491
msgid "Produits acquis"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_743
#: model:account.group,name:l10n_be.2_be_group_743
#: model:account.group.template,name:l10n_be.be_group_743
msgid "Produits d'exploitation divers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_76
#: model:account.group,name:l10n_be.2_be_group_76
#: model:account.group.template,name:l10n_be.be_group_76
msgid "Produits d'exploitation ou financiers non récurrents"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_751
#: model:account.group,name:l10n_be.2_be_group_751
#: model:account.group.template,name:l10n_be.be_group_751
msgid "Produits des actifs circulants"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_750
#: model:account.group,name:l10n_be.2_be_group_750
#: model:account.group.template,name:l10n_be.be_group_750
msgid "Produits des immobilisations financières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_75
#: model:account.group,name:l10n_be.2_be_group_75
#: model:account.group.template,name:l10n_be.be_group_75
msgid "Produits financiers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_756
#: model:account.group,name:l10n_be.2_be_group_756
#: model:account.group.template,name:l10n_be.be_group_756
msgid "Produits financiers divers"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_33
#: model:account.group,name:l10n_be.1_be_group_330
#: model:account.group,name:l10n_be.2_be_group_33
#: model:account.group,name:l10n_be.2_be_group_330
#: model:account.group.template,name:l10n_be.be_group_33
#: model:account.group.template,name:l10n_be.be_group_330
msgid "Produits finis"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_404
#: model:account.group,name:l10n_be.1_be_group_414
#: model:account.group,name:l10n_be.2_be_group_404
#: model:account.group,name:l10n_be.2_be_group_414
#: model:account.group.template,name:l10n_be.be_group_404
#: model:account.group.template,name:l10n_be.be_group_414
msgid "Produits à recevoir"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_493
#: model:account.group,name:l10n_be.2_be_group_493
#: model:account.group.template,name:l10n_be.be_group_493
msgid "Produits à reporter"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a790
#: model:account.account,name:l10n_be.2_a790
#: model:account.account.template,name:l10n_be.a790
msgid "Profit brought forward from previous year"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a140
#: model:account.account,name:l10n_be.2_a140
#: model:account.account.template,name:l10n_be.a140
msgid "Profit carried forward"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a693
#: model:account.account,name:l10n_be.2_a693
#: model:account.account.template,name:l10n_be.a693
msgid "Profits to be carried forward"
msgstr "Gewinnvortrag auf neue Rechnung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6360
#: model:account.account,name:l10n_be.2_a6360
#: model:account.account.template,name:l10n_be.a6360
msgid "Provision for major repairs and maintenance - Appropriations"
msgstr ""
"Rückstellungen für Großreparaturen und große Instandhaltungsarbeiten - "
"Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6361
#: model:account.account,name:l10n_be.2_a6361
#: model:account.account.template,name:l10n_be.a6361
msgid "Provision for major repairs and maintenance - Uses and write-backs"
msgstr ""
"Rückstellungen für Großreparaturen und große Instandhaltungsarbeiten - "
"Verbrauch und Auflösungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_16
#: model:account.group,name:l10n_be.2_be_group_16
#: model:account.group.template,name:l10n_be.be_group_16
msgid "Provisions et impôts différés"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a163
#: model:account.account,name:l10n_be.2_a163
#: model:account.account.template,name:l10n_be.a163
msgid "Provisions for environmental obligations"
msgstr "Rückstellungen für Umweltschutzverpflichtungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a162
#: model:account.account,name:l10n_be.2_a162
#: model:account.account.template,name:l10n_be.a162
msgid "Provisions for major repairs and maintenance"
msgstr "Rückstellungen für Großreparaturen und Instandhaltungsarbeiten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66210
#: model:account.account,name:l10n_be.2_a66210
#: model:account.account.template,name:l10n_be.a66210
msgid ""
"Provisions for non-recurring financial liabilities and charges - "
"Appropriations"
msgstr ""
"Rückstellungen für nicht wiederkehrende finanzielle Risiken und Aufwendungen"
" - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66211
#: model:account.account,name:l10n_be.2_a66211
#: model:account.account.template,name:l10n_be.a66211
msgid "Provisions for non-recurring financial liabilities and charges - Uses"
msgstr ""
"Rückstellungen für nicht wiederkehrende finanzielle Risiken und Aufwendungen"
" - Rücknahmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66200
#: model:account.account,name:l10n_be.2_a66200
#: model:account.account.template,name:l10n_be.a66200
msgid ""
"Provisions for non-recurring operating liabilities and charges - "
"Appropriations"
msgstr ""
"Rückstellungen für nicht wiederkehrende Betriebsrisiken und -Aufwendungen - "
"Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a66201
#: model:account.account,name:l10n_be.2_a66201
#: model:account.account.template,name:l10n_be.a66201
msgid "Provisions for non-recurring operating liabilities and charges - Uses"
msgstr ""
"Rückstellungen für nicht wiederkehrende Betriebsrisiken und Aufwendungen - "
"Rücknahmen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6370
#: model:account.account,name:l10n_be.2_a6370
#: model:account.account.template,name:l10n_be.a6370
msgid "Provisions for other risks and charges - Appropriations"
msgstr "Rückstellungen für sonstige Risiken und Aufwendungen - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6380
#: model:account.account,name:l10n_be.2_a6380
#: model:account.account.template,name:l10n_be.a6380
msgid ""
"Provisions for other risks and charges - Provisions for environmental "
"obligations excluded - Appropriations"
msgstr ""
"Rückstellungen für sonstige Risiken und Aufwendungen "
"-Umweltschutzverpflichtungen nicht einbegriffen - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6381
#: model:account.account,name:l10n_be.2_a6381
#: model:account.account.template,name:l10n_be.a6381
msgid ""
"Provisions for other risks and charges - Provisions for environmental "
"obligations excluded - Uses (write-back)"
msgstr ""
"Rückstellungen für sonstige Risiken und Aufwendungen-"
"Umweltschutzverpflichtungen nicht einbegriffen - Verbrauch und Auflösungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6371
#: model:account.account,name:l10n_be.2_a6371
#: model:account.account.template,name:l10n_be.a6371
msgid "Provisions for other risks and charges - Uses (write-back)"
msgstr ""
"Rückstellungen für sonstige Risiken und Aufwendungen - Verbrauch und "
"Auflösungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a160
#: model:account.account,name:l10n_be.2_a160
#: model:account.account.template,name:l10n_be.a160
msgid "Provisions for pensions and similar obligations"
msgstr "Rückstellungen für Pensionen und ähnliche Verpflichtungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6350
#: model:account.account,name:l10n_be.2_a6350
#: model:account.account.template,name:l10n_be.a6350
msgid "Provisions for pensions and similar obligations - Appropriations"
msgstr ""
"Rückstellungen für Pensionen und ähnliche Verpflichtungen - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6351
#: model:account.account,name:l10n_be.2_a6351
#: model:account.account.template,name:l10n_be.a6351
msgid "Provisions for pensions and similar obligations - Uses and write-backs"
msgstr ""
"Rückstellungen für Pensionen und ähnliche Verpflichtungen - Verbrauch und "
"Auflösungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a161
#: model:account.account,name:l10n_be.2_a161
#: model:account.account.template,name:l10n_be.a161
msgid "Provisions for taxation"
msgstr "Steuerrückstellungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6560
#: model:account.account,name:l10n_be.2_a6560
#: model:account.account.template,name:l10n_be.a6560
msgid "Provisions of a financial nature - Appropriations"
msgstr "Rückstellungen mit finanziellem Charakter - Zuführungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6561
#: model:account.account,name:l10n_be.2_a6561
#: model:account.account.template,name:l10n_be.a6561
msgid "Provisions of a financial nature - Uses and write-backs"
msgstr "Rückstellungen mit finanziellem Charakter - Verbrauch und Auflösungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_164
#: model:account.group,name:l10n_be.1_be_group_638
#: model:account.group,name:l10n_be.2_be_group_164
#: model:account.group,name:l10n_be.2_be_group_638
#: model:account.group.template,name:l10n_be.be_group_164
#: model:account.group.template,name:l10n_be.be_group_638
msgid "Provisions pour autres risques et charges"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_161
#: model:account.group,name:l10n_be.2_be_group_161
#: model:account.group.template,name:l10n_be.be_group_161
msgid "Provisions pour charges fiscales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_162
#: model:account.group,name:l10n_be.1_be_group_636
#: model:account.group,name:l10n_be.2_be_group_162
#: model:account.group,name:l10n_be.2_be_group_636
#: model:account.group.template,name:l10n_be.be_group_162
#: model:account.group.template,name:l10n_be.be_group_636
msgid "Provisions pour grosses réparations et gros entretien"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_163
#: model:account.group,name:l10n_be.1_be_group_637
#: model:account.group,name:l10n_be.2_be_group_163
#: model:account.group,name:l10n_be.2_be_group_637
#: model:account.group.template,name:l10n_be.be_group_163
#: model:account.group.template,name:l10n_be.be_group_637
msgid "Provisions pour obligations environnementales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_160
#: model:account.group,name:l10n_be.1_be_group_635
#: model:account.group,name:l10n_be.2_be_group_160
#: model:account.group,name:l10n_be.2_be_group_635
#: model:account.group.template,name:l10n_be.be_group_160
#: model:account.group.template,name:l10n_be.be_group_635
msgid "Provisions pour pensions et obligations similaires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_662
#: model:account.group,name:l10n_be.2_be_group_662
#: model:account.group.template,name:l10n_be.be_group_662
msgid "Provisions pour risques et charges non récurrents"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_656
#: model:account.group,name:l10n_be.2_be_group_656
#: model:account.group.template,name:l10n_be.be_group_656
msgid "Provisions à caractère financier"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_453
#: model:account.group,name:l10n_be.2_be_group_453
#: model:account.group.template,name:l10n_be.be_group_453
msgid "Précomptes retenus"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_791
#: model:account.group,name:l10n_be.2_be_group_791
#: model:account.group.template,name:l10n_be.be_group_791
msgid "Prélèvement sur le capital et les primes d'émission"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_792
#: model:account.group,name:l10n_be.2_be_group_792
#: model:account.group.template,name:l10n_be.be_group_792
msgid "Prélèvement sur les réserves"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_780
#: model:account.group,name:l10n_be.2_be_group_780
#: model:account.group.template,name:l10n_be.be_group_780
msgid "Prélèvements sur les impôts différés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_789
#: model:account.group,name:l10n_be.2_be_group_789
#: model:account.group.template,name:l10n_be.be_group_789
msgid "Prélèvements sur les réserves immunisées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_78
#: model:account.group,name:l10n_be.2_be_group_78
#: model:account.group.template,name:l10n_be.be_group_78
msgid "Prélèvements sur les réserves immunisées et les impôts différés"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a601
#: model:account.account,name:l10n_be.2_a601
#: model:account.account.template,name:l10n_be.a601
msgid "Purchases of consumables"
msgstr "Käufe von Hilfs- und Betriebsstoffen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a604
#: model:account.account,name:l10n_be.2_a604
#: model:account.account.template,name:l10n_be.a604
msgid "Purchases of goods for resale"
msgstr "Käufe von Handelswaren"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a605
#: model:account.account,name:l10n_be.2_a605
#: model:account.account.template,name:l10n_be.a605
msgid "Purchases of immovable property for resale"
msgstr "Kauf von für den Verkauf bestimmten unbeweglichen Gegenständen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a600
#: model:account.account,name:l10n_be.2_a600
#: model:account.account.template,name:l10n_be.a600
msgid "Purchases of raw materials"
msgstr "Käufe von Rohstoffen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a602
#: model:account.account,name:l10n_be.2_a602
#: model:account.account.template,name:l10n_be.a602
msgid "Purchases of services, works and studies"
msgstr "Käufe von Dienstleistungen, Arbeiten und Studien"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_456
#: model:account.group,name:l10n_be.2_be_group_456
#: model:account.group.template,name:l10n_be.be_group_456
msgid "Pécules de vacances"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a300
#: model:account.account,name:l10n_be.2_a300
#: model:account.account.template,name:l10n_be.a300
msgid "Raw materials - Acquisition value"
msgstr "Rohstoffe - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a309
#: model:account.account,name:l10n_be.2_a309
#: model:account.account.template,name:l10n_be.a309
msgid "Raw materials - amounts written down"
msgstr "Rohstoffe - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a023
#: model:account.account,name:l10n_be.2_a023
#: model:account.account.template,name:l10n_be.a023
msgid "Real guarantees provided on behalf of third parties"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_708
#: model:account.group,name:l10n_be.2_be_group_708
#: model:account.group.template,name:l10n_be.be_group_708
msgid "Remises, ristournes et rabais accordés (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_608
#: model:account.group,name:l10n_be.2_be_group_608
#: model:account.group.template,name:l10n_be.be_group_608
msgid "Remises, ristournes et rabais obtenus (–)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6200
#: model:account.account,name:l10n_be.2_a6200
#: model:account.account.template,name:l10n_be.a6200
msgid "Remuneration and direct social benefits - Directors and managers"
msgstr ""
"Arbeitsentgelte und direkte soziale Vorteile - Verwaltungsratsmitglieder "
"oder Geschäftsführer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6202
#: model:account.account,name:l10n_be.2_a6202
#: model:account.account.template,name:l10n_be.a6202
msgid "Remuneration and direct social benefits - Employees"
msgstr "Arbeitsentgelte und direkte soziale Vorteile - Angestellte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6201
#: model:account.account,name:l10n_be.2_a6201
#: model:account.account.template,name:l10n_be.a6201
msgid "Remuneration and direct social benefits - Executive"
msgstr "Arbeitsentgelte und direkte soziale Vorteile - Direktionspersonal"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6203
#: model:account.account,name:l10n_be.2_a6203
#: model:account.account.template,name:l10n_be.a6203
msgid "Remuneration and direct social benefits - Manual workers"
msgstr "Arbeitsentgelte und direkte soziale Vorteile - Arbeiter"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6204
#: model:account.account,name:l10n_be.2_a6204
#: model:account.account.template,name:l10n_be.a6204
msgid "Remuneration and direct social benefits - Other staff members"
msgstr ""
"Arbeitsentgelte und direkte soziale Vorteile - Sonstige "
"Belegschaftsmitglieder"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a456
#: model:account.account,name:l10n_be.2_a456
#: model:account.account.template,name:l10n_be.a456
msgid "Remuneration and social security - Holiday pay"
msgstr ""
"Verbindlichkeiten aufgrund von Arbeitsentgelte und Soziallasten - "
"Urlaubsgeld"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a454
#: model:account.account,name:l10n_be.2_a454
#: model:account.account.template,name:l10n_be.a454
msgid "Remuneration and social security - National Social Security Office"
msgstr ""
"Verbindlichkeiten aufgrund von Arbeitsentgelte und Soziallasten - Landesamt "
"für Soziale Sicherheit"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a459
#: model:account.account,name:l10n_be.2_a459
#: model:account.account.template,name:l10n_be.a459
msgid "Remuneration and social security - Other social obligations"
msgstr ""
"Verbindlichkeiten aufgrund von Arbeitsentgelte und Soziallasten - Sonstige "
"Soziallasten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a455
#: model:account.account,name:l10n_be.2_a455
#: model:account.account.template,name:l10n_be.a455
msgid "Remuneration and social security - Remuneration"
msgstr ""
"Verbindlichkeiten aufgrund von Arbeitsentgelte und Soziallasten - "
"Arbeitsentgelte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a618
#: model:account.account,name:l10n_be.2_a618
#: model:account.account.template,name:l10n_be.a618
msgid ""
"Remuneration, premiums for extra statutory insurance, pensions of the "
"directors, or the management staff which are not allowed following the "
"contract"
msgstr ""
"Arbeitsentgelte, Prämien für außergesetzliche Versicherungen, Ruhestands- "
"und Hinterbliebenenpensionen der Verwalter, Geschäftsführer oder aktiven "
"Gesellschafter, die nicht aufgrund eines Arbeitsvertrags zuerkannt werden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a071
#: model:account.account,name:l10n_be.2_a071
#: model:account.account.template,name:l10n_be.a071
msgid "Rent and royalty creditors"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_760
#: model:account.group,name:l10n_be.2_be_group_760
#: model:account.group.template,name:l10n_be.be_group_760
msgid "Reprises d'amortissements et de réductions de valeur"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_762
#: model:account.group,name:l10n_be.2_be_group_762
#: model:account.group.template,name:l10n_be.be_group_762
msgid "Reprises de provisions pour risques et charges non récurrents"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_761
#: model:account.group,name:l10n_be.2_be_group_761
#: model:account.group.template,name:l10n_be.be_group_761
msgid "Reprises de réductions de valeur sur immobilisations financières"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_124
#: model:account.group,name:l10n_be.2_be_group_124
#: model:account.group.template,name:l10n_be.be_group_124
msgid "Reprises de réductions de valeur sur placements de trésorerie"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a210
#: model:account.account,name:l10n_be.2_a210
#: model:account.account.template,name:l10n_be.a210
msgid "Research and development costs"
msgstr "Forschungs- und Entwicklungskosten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1310
#: model:account.account,name:l10n_be.2_a1310
#: model:account.account.template,name:l10n_be.a1310
msgid "Reserves not available in respect of own shares held"
msgstr "Rücklagen für eigene Aktien oder Anteile"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a204
#: model:account.account,name:l10n_be.2_a204
#: model:account.account.template,name:l10n_be.a204
msgid "Restructuring costs"
msgstr "Restrukturierungskosten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6240
#: model:account.account,name:l10n_be.2_a6240
#: model:account.account.template,name:l10n_be.a6240
msgid "Retirement and survivors' pensions - Directors and managers"
msgstr ""
"Ruhestands- und Hinterbliebenenpensionen - Verwaltungsratsmitglieder oder "
"Geschäftsführer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a6241
#: model:account.account,name:l10n_be.2_a6241
#: model:account.account.template,name:l10n_be.a6241
msgid "Retirement and survivors' pensions - Personnel"
msgstr "Ruhestands- und Hinterbliebenenpensionen - Personal"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a122
#: model:account.account,name:l10n_be.2_a122
#: model:account.account.template,name:l10n_be.a122
msgid "Revaluation surpluses on financial fixed assets"
msgstr "Neubewertungsrücklagen auf Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a120
#: model:account.account,name:l10n_be.2_a120
#: model:account.account.template,name:l10n_be.a120
msgid "Revaluation surpluses on intangible fixed assets"
msgstr "Neubewertungsrücklagen auf immaterielle Anlagewerte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a123
#: model:account.account,name:l10n_be.2_a123
#: model:account.account.template,name:l10n_be.a123
msgid "Revaluation surpluses on stocks"
msgstr "Neubewertungsrücklagen auf Vorräte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a121
#: model:account.account,name:l10n_be.2_a121
#: model:account.account.template,name:l10n_be.a121
msgid "Revaluation surpluses on tangible fixed assets"
msgstr "Neubewertungsrücklagen auf Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a097
#: model:account.account,name:l10n_be.2_a097
#: model:account.account.template,name:l10n_be.a097
msgid "Rights on technical guarantees"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_519
#: model:account.group,name:l10n_be.2_be_group_519
#: model:account.group.template,name:l10n_be.be_group_519
msgid "Réductions de valeur actées (-)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_309
#: model:account.group,name:l10n_be.1_be_group_319
#: model:account.group,name:l10n_be.1_be_group_329
#: model:account.group,name:l10n_be.1_be_group_339
#: model:account.group,name:l10n_be.1_be_group_349
#: model:account.group,name:l10n_be.1_be_group_359
#: model:account.group,name:l10n_be.1_be_group_369
#: model:account.group,name:l10n_be.1_be_group_379
#: model:account.group,name:l10n_be.1_be_group_409
#: model:account.group,name:l10n_be.1_be_group_419
#: model:account.group,name:l10n_be.1_be_group_529
#: model:account.group,name:l10n_be.1_be_group_539
#: model:account.group,name:l10n_be.2_be_group_309
#: model:account.group,name:l10n_be.2_be_group_319
#: model:account.group,name:l10n_be.2_be_group_329
#: model:account.group,name:l10n_be.2_be_group_339
#: model:account.group,name:l10n_be.2_be_group_349
#: model:account.group,name:l10n_be.2_be_group_359
#: model:account.group,name:l10n_be.2_be_group_369
#: model:account.group,name:l10n_be.2_be_group_379
#: model:account.group,name:l10n_be.2_be_group_409
#: model:account.group,name:l10n_be.2_be_group_419
#: model:account.group,name:l10n_be.2_be_group_529
#: model:account.group,name:l10n_be.2_be_group_539
#: model:account.group.template,name:l10n_be.be_group_309
#: model:account.group.template,name:l10n_be.be_group_319
#: model:account.group.template,name:l10n_be.be_group_329
#: model:account.group.template,name:l10n_be.be_group_339
#: model:account.group.template,name:l10n_be.be_group_349
#: model:account.group.template,name:l10n_be.be_group_359
#: model:account.group.template,name:l10n_be.be_group_369
#: model:account.group.template,name:l10n_be.be_group_379
#: model:account.group.template,name:l10n_be.be_group_409
#: model:account.group.template,name:l10n_be.be_group_419
#: model:account.group.template,name:l10n_be.be_group_529
#: model:account.group.template,name:l10n_be.be_group_539
msgid "Réductions de valeur actées (–)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_651
#: model:account.group,name:l10n_be.2_be_group_651
#: model:account.group.template,name:l10n_be.be_group_651
msgid "Réductions de valeur sur actifs circulants"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_632
#: model:account.group,name:l10n_be.2_be_group_632
#: model:account.group.template,name:l10n_be.be_group_632
msgid "Réductions de valeur sur commandes en cours"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_633
#: model:account.group,name:l10n_be.2_be_group_633
#: model:account.group.template,name:l10n_be.be_group_633
msgid "Réductions de valeur sur créances commerciales à plus d'un an"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_634
#: model:account.group,name:l10n_be.2_be_group_634
#: model:account.group.template,name:l10n_be.be_group_634
msgid "Réductions de valeur sur créances commerciales à un an au plus"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_661
#: model:account.group,name:l10n_be.2_be_group_661
#: model:account.group.template,name:l10n_be.be_group_661
msgid "Réductions de valeur sur immobilisations financières (dotations)"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_631
#: model:account.group,name:l10n_be.2_be_group_631
#: model:account.group.template,name:l10n_be.be_group_631
msgid "Réductions de valeur sur stocks"
msgstr ""

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_4
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_4
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_4
msgid "Régime Cocontractant"
msgstr ""

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_2
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_2
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_2
msgid "Régime Extra-Communautaire"
msgstr ""

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_3
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_3
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_3
msgid "Régime Intra-Communautaire"
msgstr ""

#. module: l10n_be
#: model:account.fiscal.position,name:l10n_be.1_fiscal_position_template_1
#: model:account.fiscal.position,name:l10n_be.2_fiscal_position_template_1
#: model:account.fiscal.position.template,name:l10n_be.fiscal_position_template_1
msgid "Régime National"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_77
#: model:account.group,name:l10n_be.2_be_group_77
#: model:account.group.template,name:l10n_be.be_group_77
msgid "Régularisations d'impôts et reprises de provisions fiscales"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_694
#: model:account.group,name:l10n_be.2_be_group_694
#: model:account.group.template,name:l10n_be.be_group_694
msgid "Rémunération du capital"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_455
#: model:account.group,name:l10n_be.2_be_group_455
#: model:account.group.template,name:l10n_be.be_group_455
msgid "Rémunérations"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_620
#: model:account.group,name:l10n_be.2_be_group_620
#: model:account.group.template,name:l10n_be.be_group_620
msgid "Rémunérations et avantages sociaux directs"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_62
#: model:account.group,name:l10n_be.2_be_group_62
#: model:account.group.template,name:l10n_be.be_group_62
msgid "Rémunérations, charges sociales et pensions"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_618
#: model:account.group,name:l10n_be.2_be_group_618
#: model:account.group.template,name:l10n_be.be_group_618
msgid ""
"Rémunérations, primes pour assurances extralégales, pensions de retraite et "
"de survie des administrateurs, gérants et associés actifs qui ne sont pas "
"attribuées en vertu d'un contrat de travail"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_130
#: model:account.group,name:l10n_be.2_be_group_130
#: model:account.group.template,name:l10n_be.be_group_130
msgid "Réserve légale"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_13
#: model:account.group,name:l10n_be.2_be_group_13
#: model:account.group.template,name:l10n_be.be_group_13
msgid "Réserves"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_133
#: model:account.group,name:l10n_be.2_be_group_133
#: model:account.group.template,name:l10n_be.be_group_133
msgid "Réserves disponibles"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_132
#: model:account.group,name:l10n_be.2_be_group_132
#: model:account.group.template,name:l10n_be.be_group_132
msgid "Réserves immunisées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_131
#: model:account.group,name:l10n_be.2_be_group_131
#: model:account.group.template,name:l10n_be.be_group_131
msgid "Réserves indisponibles"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a053
#: model:account.account,name:l10n_be.2_a053
#: model:account.account.template,name:l10n_be.a053
msgid "Sale commitment"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7012
#: model:account.account,name:l10n_be.2_a7012
#: model:account.account.template,name:l10n_be.a7012
msgid "Sales rendered for export (finished goods)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7002
#: model:account.account,name:l10n_be.2_a7002
#: model:account.account.template,name:l10n_be.a7002
msgid "Sales rendered for export (marchandises)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7010
#: model:account.account,name:l10n_be.2_a7010
#: model:account.account.template,name:l10n_be.a7010
msgid "Sales rendered in Belgium (finished goods)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7000
#: model:account.account,name:l10n_be.2_a7000
#: model:account.account.template,name:l10n_be.a7000
msgid "Sales rendered in Belgium (marchandises)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7011
#: model:account.account,name:l10n_be.2_a7011
#: model:account.account.template,name:l10n_be.a7011
msgid "Sales rendered in E.E.C. (finished goods)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7001
#: model:account.account,name:l10n_be.2_a7001
#: model:account.account.template,name:l10n_be.a7001
msgid "Sales rendered in E.E.C. (marchandises)"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a61
#: model:account.account,name:l10n_be.2_a61
#: model:account.account.template,name:l10n_be.a61
msgid "Services and other goods"
msgstr "Übrige Lieferungen und Leistungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_61
#: model:account.group,name:l10n_be.2_be_group_61
#: model:account.group.template,name:l10n_be.be_group_61
msgid "Services et biens divers"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7052
#: model:account.account,name:l10n_be.2_a7052
#: model:account.account.template,name:l10n_be.a7052
msgid "Services rendered for export"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7050
#: model:account.account,name:l10n_be.2_a7050
#: model:account.account.template,name:l10n_be.a7050
msgid "Services rendered in Belgium"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7051
#: model:account.account,name:l10n_be.2_a7051
#: model:account.account.template,name:l10n_be.a7051
msgid "Services rendered in E.E.C."
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a11
#: model:account.account,name:l10n_be.2_a11
#: model:account.account.template,name:l10n_be.a11
msgid "Share premium account"
msgstr "Agio"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a510
#: model:account.account,name:l10n_be.2_a510
#: model:account.account.template,name:l10n_be.a510
msgid ""
"Shares and current investments other than fixed income investments - "
"Acquisition value"
msgstr "Geldanlagen - Aktien oder Anteile - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a519
#: model:account.account,name:l10n_be.2_a519
#: model:account.account.template,name:l10n_be.a519
msgid ""
"Shares and current investments other than fixed income investments - Amounts"
" written down"
msgstr "Geldanlagen - Aktien oder Anteile - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a511
#: model:account.account,name:l10n_be.2_a511
#: model:account.account.template,name:l10n_be.a511
msgid ""
"Shares and current investments other than fixed income investments - "
"Uncalled amount"
msgstr "Geldanlagen - Aktien oder Anteile - Nicht eingeforderter Betrag"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_603
#: model:account.group,name:l10n_be.2_be_group_603
#: model:account.group.template,name:l10n_be.be_group_603
msgid "Sous-traitances générales"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a031
#: model:account.account,name:l10n_be.2_a031
#: model:account.account.template,name:l10n_be.a031
msgid "Statutory applicants"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a030
#: model:account.account,name:l10n_be.2_a030
#: model:account.account.template,name:l10n_be.a030
msgid "Statutory deposits"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_3
#: model:account.group,name:l10n_be.2_be_group_3
#: model:account.group.template,name:l10n_be.be_group_3
msgid "Stocks et commandes en cours d'exécution"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a603
#: model:account.account,name:l10n_be.2_a603
#: model:account.account.template,name:l10n_be.a603
msgid "Sub-contracting"
msgstr "Einsätze von Unterlieferanten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4200
#: model:account.account,name:l10n_be.2_a4200
#: model:account.account.template,name:l10n_be.a4200
msgid ""
"Subordinated loans payable after more than one year falling due within one "
"year - Convertible"
msgstr ""
"Innerhalb eines Jahres fällig werdende nachrangige Anleihen mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr - Wandelanleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4201
#: model:account.account,name:l10n_be.2_a4201
#: model:account.account.template,name:l10n_be.a4201
msgid ""
"Subordinated loans payable after more than one year falling due within one "
"year - Non convertible"
msgstr ""
"Innerhalb eines Jahres fällig werdende nachrangige Anleihen mit einer "
"ursprünglichen Laufzeit von mehr als einem Jahr - Nicht wandelbare Anleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1700
#: model:account.account,name:l10n_be.2_a1700
#: model:account.account.template,name:l10n_be.a1700
msgid ""
"Subordinated loans with a remaining term of more than one year - Convertible"
" bonds"
msgstr ""
"Nachrangige Anleihen mit einer Restlaufzeit von mehr als einem Jahr - "
"Wandelanleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1701
#: model:account.account,name:l10n_be.2_a1701
#: model:account.account.template,name:l10n_be.a1701
msgid ""
"Subordinated loans with a remaining term of more than one year - Non "
"convertible bonds"
msgstr ""
"Nachrangige Anleihen mit einer Restlaufzeit von mehr als einem Jahr - Nicht "
"wandelbare Anleihen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_740
#: model:account.group,name:l10n_be.2_be_group_740
#: model:account.group.template,name:l10n_be.be_group_740
msgid "Subsides d'exploitation et montants compensatoires"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_15
#: model:account.group,name:l10n_be.2_be_group_15
#: model:account.group.template,name:l10n_be.be_group_15
msgid "Subsides en capital"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_753
#: model:account.group,name:l10n_be.2_be_group_753
#: model:account.group.template,name:l10n_be.be_group_753
msgid "Subsides en capital et en intérêts"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1750
#: model:account.account,name:l10n_be.2_a1750
#: model:account.account.template,name:l10n_be.a1750
msgid "Suppliers (more than one year)"
msgstr ""
"Verbindlichkeiten aus Lieferungen und Leistungen mit einer Restlaufzeit von "
"mehr als einem Jahr - Lieferanten"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a440
#: model:account.account,name:l10n_be.2_a440
#: model:account.account.template,name:l10n_be.a440
msgid "Suppliers payable within one year"
msgstr "Lieferanten mit einer Restlaufzeit bis zu einem Jahr"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a499
#: model:account.account,name:l10n_be.2_a499
#: model:account.account.template,name:l10n_be.a499
msgid "Suspense account"
msgstr "Interimskonto"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_451
#: model:account.group,name:l10n_be.2_be_group_451
#: model:account.group.template,name:l10n_be.be_group_451
msgid "T.V.A. à payer"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_411
#: model:account.group,name:l10n_be.2_be_group_411
#: model:account.group.template,name:l10n_be.be_group_411
msgid "T.V.A. à récupérer"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_0
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-S
msgid "TVA 0%"
msgstr "MwSt. 0%"

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-CC
msgid "TVA 0% Cocont."
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-EU-T
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-EU-T
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-EU-T
msgid "TVA 0% EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-00-ROW
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-00-ROW
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-00-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-00-ROW
msgid "TVA 0% Non EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-12-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-12-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-12-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-12-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_12
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-12-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-12-S
msgid "TVA 12%"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-CC
msgid "TVA 12% Cocont."
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-EU
msgid "TVA 12% EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-12-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-12-ROW-CC
msgid "TVA 12% Non EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-21-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-21-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-21-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-21-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_21
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-21-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-21-S
msgid "TVA 21%"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-CC
msgid "TVA 21% Cocont."
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-EU
msgid "TVA 21% EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-21-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-21-ROW-CC
msgid "TVA 21% Non EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_TVA-21-inclus-dans-prix
#: model:account.tax,description:l10n_be.2_attn_TVA-21-inclus-dans-prix
#: model:account.tax.template,description:l10n_be.attn_TVA-21-inclus-dans-prix
msgid "TVA 21% TTC"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-CAR-EXC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-CAR-EXC
msgid "TVA 50% Non Déductible - Frais de voiture (Prix Excl.)"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-06-L
#: model:account.tax,description:l10n_be.1_attn_VAT-OUT-06-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-06-L
#: model:account.tax,description:l10n_be.2_attn_VAT-OUT-06-S
#: model:account.tax.group,name:l10n_be.tax_group_tva_6
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-06-L
#: model:account.tax.template,description:l10n_be.attn_VAT-OUT-06-S
msgid "TVA 6%"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-CC
msgid "TVA 6% Cocont."
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-EU
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-EU
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-EU-G
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-EU-S
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-EU
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-EU-G
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-EU-S
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-EU
msgid "TVA 6% EU"
msgstr ""

#. module: l10n_be
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,description:l10n_be.1_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax,description:l10n_be.2_attn_VAT-IN-V83-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V81-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V82-06-ROW-CC
#: model:account.tax.template,description:l10n_be.attn_VAT-IN-V83-06-ROW-CC
msgid "TVA 6% Non EU"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a27
#: model:account.account,name:l10n_be.2_a27
#: model:account.account.template,name:l10n_be.a27
msgid "Tangible fixed assets under construction and advance payments"
msgstr "Sachanlagen im Bau und geleistete Anzahlungen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_472
#: model:account.group,name:l10n_be.2_be_group_472
#: model:account.group.template,name:l10n_be.be_group_472
msgid "Tantièmes de l'exercice"
msgstr ""

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes
msgid "Taxes"
msgstr "Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a412
#: model:account.account,name:l10n_be.2_a412
#: model:account.account.template,name:l10n_be.a412
msgid "Taxes and withholdings taxes to be recovered"
msgstr "Zurückzuerstattende Steuern und Steuervorhabzüge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4128
#: model:account.account,name:l10n_be.2_a4128
#: model:account.account.template,name:l10n_be.a4128
msgid "Taxes and withholdings taxes to be recovered - Foreign taxes"
msgstr ""
"Zurückzuerstattende Steuern und Steuervorhabzüge - Ausländische Steuern und "
"Abgaben"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a452
#: model:account.account,name:l10n_be.2_a452
#: model:account.account.template,name:l10n_be.a452
msgid "Taxes payable"
msgstr "Zu zahlende Steuern und Abgaben"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4528
#: model:account.account,name:l10n_be.2_a4528
#: model:account.account.template,name:l10n_be.a4528
msgid "Taxes payable - Foreign taxes"
msgstr "Zu zahlende Steuern und Abgaben - Ausländische Steuern und Abgaben"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a640
#: model:account.account,name:l10n_be.2_a640
#: model:account.account.template,name:l10n_be.a640
msgid "Taxes related to operation"
msgstr "Betriebliche Steuern und Abgaben"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a453
#: model:account.account,name:l10n_be.2_a453
#: model:account.account.template,name:l10n_be.a453
msgid "Taxes withheld"
msgstr "Einbehaltene Steuervorhabzüge"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_220
#: model:account.group,name:l10n_be.2_be_group_220
#: model:account.group.template,name:l10n_be.be_group_220
msgid "Terrains"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_222
#: model:account.group,name:l10n_be.2_be_group_222
#: model:account.group.template,name:l10n_be.be_group_222
msgid "Terrains bâtis"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_250
#: model:account.group,name:l10n_be.2_be_group_250
#: model:account.group.template,name:l10n_be.be_group_250
msgid "Terrains et construction"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_22
#: model:account.group,name:l10n_be.2_be_group_22
#: model:account.group.template,name:l10n_be.be_group_22
msgid "Terrains et constructions"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a040
#: model:account.account,name:l10n_be.2_a040
#: model:account.account.template,name:l10n_be.a040
msgid ""
"Third parties, holders in their name but at the risks and profits of the "
"business of goods and values"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a001
#: model:account.account,name:l10n_be.2_a001
#: model:account.account.template,name:l10n_be.a001
msgid "Third party guarantees on behalf of the company"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_001
#: model:account.group,name:l10n_be.2_be_group_001
#: model:account.group.template,name:l10n_be.be_group_001
msgid "Tiers constituants de garanties pour compte de l'entreprise"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_040
#: model:account.group,name:l10n_be.2_be_group_040
#: model:account.group.template,name:l10n_be.be_group_040
msgid ""
"Tiers, détenteurs en leur nom mais aux risques et profits de l'entreprise de"
" biens et de valeurs"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_52
#: model:account.group,name:l10n_be.2_be_group_52
#: model:account.group.template,name:l10n_be.be_group_52
msgid "Titres à revenu fixe"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2906
#: model:account.account,name:l10n_be.2_a2906
#: model:account.account.template,name:l10n_be.a2906
msgid "Trade debtors after more than one year - Advance payments"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit von mehr "
"als einem Jahr - Geleistete Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2909
#: model:account.account,name:l10n_be.2_a2909
#: model:account.account.template,name:l10n_be.a2909
msgid "Trade debtors after more than one year - Amounts written down"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit von mehr "
"als einem Jahr - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2901
#: model:account.account,name:l10n_be.2_a2901
#: model:account.account.template,name:l10n_be.a2901
msgid "Trade debtors after more than one year - Bills receivable"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit von mehr "
"als einem Jahr - Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2900
#: model:account.account,name:l10n_be.2_a2900
#: model:account.account.template,name:l10n_be.a2900
msgid "Trade debtors after more than one year - Customer"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit von mehr "
"als einem Jahr - Kunden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a2907
#: model:account.account,name:l10n_be.2_a2907
#: model:account.account.template,name:l10n_be.a2907
msgid "Trade debtors after more than one year - Doubtful amounts"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit von mehr "
"als einem Jahr - Zweifelhafte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a406
#: model:account.account,name:l10n_be.2_a406
#: model:account.account.template,name:l10n_be.a406
msgid "Trade debtors within one year - Advance payments"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Geleistete Anzahlungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a409
#: model:account.account,name:l10n_be.2_a409
#: model:account.account.template,name:l10n_be.a409
msgid "Trade debtors within one year - Amounts written down"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a401
#: model:account.account,name:l10n_be.2_a401
#: model:account.account.template,name:l10n_be.a401
msgid "Trade debtors within one year - Bills receivable"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Besitzwechsel"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a400
#: model:account.account,name:l10n_be.2_a400
#: model:account.account.template,name:l10n_be.a400
msgid "Trade debtors within one year - Customer"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Kunden"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a407
#: model:account.account,name:l10n_be.2_a407
#: model:account.account.template,name:l10n_be.a407
msgid "Trade debtors within one year - Doubtful amounts"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Zweifelhalte Forderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a404
#: model:account.account,name:l10n_be.2_a404
#: model:account.account.template,name:l10n_be.a404
msgid "Trade debtors within one year - Income receivable"
msgstr ""
"Forderungen aus Lieferungen und Leistungen mit einer Restlaufzeit bis zu "
"einem Jahr - Zu erhaltene Erträge"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a780
#: model:account.account,name:l10n_be.2_a780
#: model:account.account.template,name:l10n_be.a780
msgid "Transfer from deferred taxes"
msgstr "Auflösung von aufgeschobenen Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a789
#: model:account.account,name:l10n_be.2_a789
#: model:account.account.template,name:l10n_be.a789
msgid "Transfer from untaxed reserves"
msgstr "Entnahmen aus den steuerfreien Rücklagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a680
#: model:account.account,name:l10n_be.2_a680
#: model:account.account.template,name:l10n_be.a680
msgid "Transfer to deferred taxes"
msgstr "Zuführung zu aufgeschobenen Steuern"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a689
#: model:account.account,name:l10n_be.2_a689
#: model:account.account.template,name:l10n_be.a689
msgid "Transfer to untaxed reserves"
msgstr "Einstellung in die steuerfreien Rücklagen"

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_680
#: model:account.group,name:l10n_be.2_be_group_680
#: model:account.group.template,name:l10n_be.be_group_680
msgid "Transferts aux impôts différés"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_68
#: model:account.group,name:l10n_be.2_be_group_68
#: model:account.group.template,name:l10n_be.be_group_68
msgid "Transferts aux impôts différés et aux réserves immunisées"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_689
#: model:account.group,name:l10n_be.2_be_group_689
#: model:account.group.template,name:l10n_be.be_group_689
msgid "Transferts aux réserves immunisées"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a101
#: model:account.account,name:l10n_be.2_a101
#: model:account.account.template,name:l10n_be.a101
msgid "Uncalled capital"
msgstr "Nicht eingefordertes Kapital"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4210
#: model:account.account,name:l10n_be.2_a4210
#: model:account.account.template,name:l10n_be.a4210
msgid ""
"Unsubordinated debentures payable after more than one year falling due "
"within one year - Convertible"
msgstr ""
"Innerhalb eines Jahres fällig werdende nicht nachrangige "
"Schuldverschreibungsanleihen mit einer ursprünglichen Laufzeit von mehr als "
"einem Jahr - Wandelanleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4211
#: model:account.account,name:l10n_be.2_a4211
#: model:account.account.template,name:l10n_be.a4211
msgid ""
"Unsubordinated debentures payable after more than one year falling due "
"within one year - Non convertible"
msgstr ""
"Innerhalb eines Jahres fällig werdende nicht nachrangige "
"Schuldverschreibungsanleihen mit einer ursprünglichen Laufzeit von mehr als "
"einem Jahr - Nicht wandelbare Anleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1710
#: model:account.account,name:l10n_be.2_a1710
#: model:account.account.template,name:l10n_be.a1710
msgid ""
"Unsubordinated debentures with a remaining term of more than one year - "
"Convertible bonds"
msgstr ""
"Nicht nachrangige Anleihen mit einer Restlaufzeit von mehr als einem Jahr - "
"Wandelanleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a1711
#: model:account.account,name:l10n_be.2_a1711
#: model:account.account.template,name:l10n_be.a1711
msgid ""
"Unsubordinated debentures with a remaining term of more than one year - Non "
"convertible bonds"
msgstr ""
"Nicht nachrangige Anleihen mit einer Restlaufzeit von mehr als einem Jahr - "
"Nicht wandelbare Anleihen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a132
#: model:account.account,name:l10n_be.2_a132
#: model:account.account.template,name:l10n_be.a132
msgid "Untaxed reserves"
msgstr "Steuerfreie Rücklagen"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_deductibles
msgid "V Déductibles"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4512
#: model:account.account,name:l10n_be.2_a4512
#: model:account.account.template,name:l10n_be.a4512
msgid "VAT due - Current Account"
msgstr "MwSt. Fällig - Girokonto"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451
#: model:account.account,name:l10n_be.2_a451
#: model:account.account.template,name:l10n_be.a451
msgid "VAT payable"
msgstr "Zu zahlende Mehrwertsteuer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451055
#: model:account.account,name:l10n_be.2_a451055
#: model:account.account.template,name:l10n_be.a451055
msgid "VAT payable - Intracommunity acquisitions - box 55"
msgstr "Zu zahlende Mehrwertsteuer - Intra-community"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451054
#: model:account.account,name:l10n_be.2_a451054
#: model:account.account.template,name:l10n_be.a451054
msgid "VAT payable - compartment 54"
msgstr "Zu zahlende MwSt. - Rubrik 54"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451063
#: model:account.account,name:l10n_be.2_a451063
#: model:account.account.template,name:l10n_be.a451063
msgid "VAT payable - credit notes - compartment 63"
msgstr "Zu zahlende MwSt. - Fach 63 KN"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451056
#: model:account.account,name:l10n_be.2_a451056
#: model:account.account.template,name:l10n_be.a451056
msgid "VAT payable - reverse charge (cocontracting) - compartment 56"
msgstr "Zu zahlende MwSt. - Cocontractant - Fach 56"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451057
#: model:account.account,name:l10n_be.2_a451057
#: model:account.account.template,name:l10n_be.a451057
msgid "VAT payable - reverse charge (import) - compartment 57"
msgstr "Zu zahlende MwSt. - Import - Fach 57"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451830
#: model:account.account,name:l10n_be.2_a451830
#: model:account.account.template,name:l10n_be.a451830
msgid "VAT payable - revisions"
msgstr "Zu zahlende MwSt. Revisionsbedürftig"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451800
#: model:account.account,name:l10n_be.2_a451800
#: model:account.account.template,name:l10n_be.a451800
msgid "VAT payable - revisions insufficiencies"
msgstr "Zu zahlende MwSt. Revisionsbedürftig"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a451820
#: model:account.account,name:l10n_be.2_a451820
#: model:account.account.template,name:l10n_be.a451820
msgid "VAT payable - revisions of deductions"
msgstr "Zu zahlende MwSt. Revisionsbedürftig"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a411
#: model:account.account,name:l10n_be.2_a411
#: model:account.account.template,name:l10n_be.a411
msgid "VAT recoverable"
msgstr "Zurückzuerhaltende Mehrwertsteuer"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a4112
#: model:account.account,name:l10n_be.2_a4112
#: model:account.account.template,name:l10n_be.a4112
msgid "VAT recoverable - Current Account"
msgstr "Mehrwertsteuer erstattungsfähig - Girokonto"

#. module: l10n_be
#: model:account.tax.report.line,name:l10n_be.tax_report_title_taxes_soldes
msgid "VI Soldes"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_300
#: model:account.group,name:l10n_be.1_be_group_310
#: model:account.group,name:l10n_be.1_be_group_320
#: model:account.group,name:l10n_be.1_be_group_340
#: model:account.group,name:l10n_be.1_be_group_350
#: model:account.group,name:l10n_be.1_be_group_370
#: model:account.group,name:l10n_be.1_be_group_510
#: model:account.group,name:l10n_be.1_be_group_520
#: model:account.group,name:l10n_be.2_be_group_300
#: model:account.group,name:l10n_be.2_be_group_310
#: model:account.group,name:l10n_be.2_be_group_320
#: model:account.group,name:l10n_be.2_be_group_340
#: model:account.group,name:l10n_be.2_be_group_350
#: model:account.group,name:l10n_be.2_be_group_370
#: model:account.group,name:l10n_be.2_be_group_510
#: model:account.group,name:l10n_be.2_be_group_520
#: model:account.group.template,name:l10n_be.be_group_300
#: model:account.group.template,name:l10n_be.be_group_310
#: model:account.group.template,name:l10n_be.be_group_320
#: model:account.group.template,name:l10n_be.be_group_340
#: model:account.group.template,name:l10n_be.be_group_350
#: model:account.group.template,name:l10n_be.be_group_370
#: model:account.group.template,name:l10n_be.be_group_510
#: model:account.group.template,name:l10n_be.be_group_520
msgid "Valeur d'acquisition"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_54
#: model:account.group,name:l10n_be.2_be_group_54
#: model:account.group.template,name:l10n_be.be_group_54
msgid "Valeurs échues à l'encaissement"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_71
#: model:account.group,name:l10n_be.2_be_group_71
#: model:account.group.template,name:l10n_be.be_group_71
msgid "Variation des stocks et des commandes en cours d'exécution"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_609
#: model:account.group,name:l10n_be.2_be_group_609
#: model:account.group.template,name:l10n_be.be_group_609
msgid "Variations des stocks"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_700
#: model:account.group,name:l10n_be.2_be_group_700
#: model:account.group.template,name:l10n_be.be_group_700
msgid "Ventes et prestations de services"
msgstr ""

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_58
#: model:account.group,name:l10n_be.2_be_group_58
#: model:account.group.template,name:l10n_be.be_group_58
msgid "Virements internes"
msgstr ""

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a792
#: model:account.account,name:l10n_be.2_a792
#: model:account.account.template,name:l10n_be.a792
msgid "Withdrawal from allocated funds"
msgstr "Entnahmen aus dem zweckgebundenen Vermögen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a791
#: model:account.account,name:l10n_be.2_a791
#: model:account.account.template,name:l10n_be.a791
msgid "Withdrawal from the association or foundation funds"
msgstr "Entnahmen aus dem Vermögen der Vereinigung oder Stiftung"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a320
#: model:account.account,name:l10n_be.2_a320
#: model:account.account.template,name:l10n_be.a320
msgid "Work in progress - Acquisition value"
msgstr "Unfertige Erzeugnisse - Anschaffungswert"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a329
#: model:account.account,name:l10n_be.2_a329
#: model:account.account.template,name:l10n_be.a329
msgid "Work in progress - amounts written down"
msgstr "Unfertige Erzeugnisse - Gebuchte Wertminderungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a761
#: model:account.account,name:l10n_be.2_a761
#: model:account.account.template,name:l10n_be.a761
msgid "Write-back of amounts written down financial fixed assets"
msgstr "Rücknahme von Wertminderungen auf Finanzanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7600
#: model:account.account,name:l10n_be.2_a7600
#: model:account.account.template,name:l10n_be.a7600
msgid ""
"Write-back of depreciation and of amounts written off intangible fixed "
"assets"
msgstr ""
"Rücknahme von Abschreibungen und Wertminderungen auf immaterielle "
"Anlagewerte"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7601
#: model:account.account,name:l10n_be.2_a7601
#: model:account.account.template,name:l10n_be.a7601
msgid ""
"Write-back of depreciation and of amounts written off tangible fixed assets"
msgstr "Rücknahme von Abschreibungen und Wertminderungen auf Sachanlagen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7621
#: model:account.account,name:l10n_be.2_a7621
#: model:account.account.template,name:l10n_be.a7621
msgid ""
"Write-back of provisions for non-recurring financial liabilities and charges"
msgstr ""
"Auflösung von Rückstellungen für nicht-rekurrente finanziellen Risiken und "
"Aufwendungen"

#. module: l10n_be
#: model:account.account,name:l10n_be.1_a7620
#: model:account.account,name:l10n_be.2_a7620
#: model:account.account.template,name:l10n_be.a7620
msgid ""
"Write-back of provisions for non-recurring operating liabilities and charges"
msgstr ""
"Auflösung von Rückstellungen für nicht-rekurrente betriebliche Risiken und "
"Aufwendungen"

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
"Sie können für jede Art von Referenz verschiedene Modelle auswählen. Die "
"Standardeinstellung ist die Odoo-Referenz."

#. module: l10n_be
#: model:account.group,name:l10n_be.1_be_group_755
#: model:account.group,name:l10n_be.2_be_group_755
#: model:account.group.template,name:l10n_be.be_group_755
msgid "Écart de conversion des devises"
msgstr ""
