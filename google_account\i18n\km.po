# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "Google Service"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__id
msgid "ID"
msgstr "ID"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service____last_update
msgid "Last Modified on"
msgstr "កាលបរិច្ឆេតកែប្រែចុងក្រោយ"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: google_account
#: code:addons/google_account/models/google_service.py:172
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:120
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:56
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:150
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:193
#, python-format
msgid "Something went wrong with your request to google"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:131
#, python-format
msgid "The account for the Google service '%s' is not configured."
msgstr ""
