<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="0">

    <record id="survey_demo_quiz" model="survey.survey">
        <field name="title">Quiz about our Company</field>
        <field name="access_token">b137640d-9876-1234-abcd-344ca256531e</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="access_mode">public</field>
        <field name="users_can_go_back" eval="False"/>
        <field name="scoring_type">scoring_with_answers</field>
        <field name="scoring_success_min">55</field>
        <field name="questions_layout">page_per_question</field>
        <field name="description" type="html">
<p>This small quiz will test your knowledge about our Company. Be prepared !</p></field>
        <field name="background_image" type="base64" file="survey/static/src/img/survey_background.jpg"/>
    </record>

    <!-- Page 1: general informations -->
    <record id="survey_demo_quiz_p1" model="survey.question">
        <field name="title">Who are you ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">1</field>
        <field name="question_type" eval="False"/>
        <field name="is_page" eval="True"/>
        <field name="description" type="html">
<p>Some general information about you. It will be used internally for statistics only.</p></field>
    </record>
    <record id="survey_demo_quiz_p1_q1" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">2</field>
        <field name="title">What is your email ?</field>
        <field name="question_type">char_box</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="validation_email" eval="True"/>
        <field name="save_as_email" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p1_q2" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">3</field>
        <field name="title">What is your nickname ?</field>
        <field name="question_type">char_box</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="save_as_nickname" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p1_q3" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">4</field>
        <field name="title">Where are you from ?</field>
        <field name="question_type">char_box</field>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p1_q4" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">5</field>
        <field name="title">How old are you ?</field>
        <field name="description" type="html"><p>Just to categorize your answers, don't worry.</p></field>
        <field name="question_type">numerical_box</field>
        <field name="constr_mandatory" eval="True"/>
    </record>

    <!-- Page 2: quiz about company -->
    <record id="survey_demo_quiz_p2" model="survey.question">
        <field name="title">Our Company in a few questions ...</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">10</field>
        <field name="question_type" eval="False"/>
        <field name="is_page" eval="True"/>
        <field name="description" type="html">
<p>Some questions about our company. Do you really know us?</p></field>
    </record>
    <record id="survey_demo_quiz_p2_q1" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">11</field>
        <field name="title">When is Mitchell Admin born ?</field>
        <field name="description" type="html"><span>Our famous Leader !</span></field>
        <field name="question_type">date</field>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p2_q2" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">12</field>
        <field name="title">When did precisely Marc Demo crop its first apple tree ?</field>
        <field name="question_type">datetime</field>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p2_q3" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">13</field>
        <field name="title">Give the list of all types of wood we sell.</field>
        <field name="question_type">text_box</field>
        <field name="constr_mandatory" eval="False"/>
    </record>

    <!-- Page 3: quiz about fruits and vegetables -->
    <record id="survey_demo_quiz_p3" model="survey.question">
        <field name="title">Fruits and vegetables</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">20</field>
        <field name="question_type" eval="False"/>
        <field name="is_page" eval="True"/>
        <field name="description" type="html">
<p>An apple a day keeps the doctor away.</p></field>
    </record>
    <record id="survey_demo_quiz_p3_q1" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">21</field>
        <field name="title">Which category does a tomato belong to</field>
        <field name="description" type="html"><span>"Red" is not a category, I know what you are trying to do ;)</span></field>
        <field name="question_type">simple_choice</field>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
    </record>
        <record id="survey_demo_quiz_p3_q1_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
            <field name="sequence">1</field>
            <field name="value">Fruits</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">20</field>
        </record>
        <record id="survey_demo_quiz_p3_q1_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
            <field name="sequence">2</field>
            <field name="value">Vegetables</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">10</field>
        </record>
        <record id="survey_demo_quiz_p3_q1_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q1"/>
            <field name="sequence">3</field>
            <field name="value">Space stations</field>
        </record>
    <record id="survey_demo_quiz_p3_q2" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">22</field>
        <field name="title">Which of the following would you use to pollinate</field>
        <field name="question_type">simple_choice</field>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="False"/>
        <field name="constr_mandatory" eval="True"/>
        <field name="is_time_limited" eval="True"/>
        <field name="time_limit">15</field>
    </record>
        <record id="survey_demo_quiz_p3_q2_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
            <field name="sequence">1</field>
            <field name="value">Bees</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">20</field>
        </record>
        <record id="survey_demo_quiz_p3_q2_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
            <field name="sequence">2</field>
            <field name="value">Dogs</field>
        </record>
        <record id="survey_demo_quiz_p3_q2_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q2"/>
            <field name="sequence">3</field>
            <field name="value">Mooses</field>
        </record>
    <record id="survey_demo_quiz_p3_q3" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">23</field>
        <field name="title">Select trees that made more than 20K sales this year</field>
        <field name="description" type="html"><span>Our sales people have an advantage, but you can do it !</span></field>
        <field name="question_type">multiple_choice</field>
        <field name="constr_mandatory" eval="False"/>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="True"/>
        <field name="is_time_limited" eval="True"/>
        <field name="time_limit">20</field>
    </record>
        <record id="survey_demo_quiz_p3_q3_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
            <field name="sequence">1</field>
            <field name="value">Apple Trees</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">20</field>
        </record>
        <record id="survey_demo_quiz_p3_q3_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
            <field name="sequence">2</field>
            <field name="value">Lemon Trees</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">10</field>
        </record>
        <record id="survey_demo_quiz_p3_q3_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
            <field name="sequence">3</field>
            <field name="value">Baobab Trees</field>
            <field name="answer_score">-10</field>
        </record>
        <record id="survey_demo_quiz_p3_q3_sug4" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q3"/>
            <field name="sequence">4</field>
            <field name="value">Cookies</field>
            <field name="answer_score">-10</field>
        </record>
    <record id="survey_demo_quiz_p3_q4" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">24</field>
        <field name="title">A "Citrus" could give you ...</field>
        <field name="question_type">multiple_choice</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="False"/>
        <field name="is_time_limited" eval="True"/>
        <field name="time_limit">20</field>
    </record>
        <record id="survey_demo_quiz_p3_q4_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
            <field name="sequence">1</field>
            <field name="value">Pomelos</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">20</field>
        </record>
        <record id="survey_demo_quiz_p3_q4_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
            <field name="sequence">2</field>
            <field name="value">Grapefruits</field>
            <field name="is_correct" eval="True"/>
            <field name="answer_score">20</field>
        </record>
        <record id="survey_demo_quiz_p3_q4_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
            <field name="sequence">3</field>
            <field name="value">Cosmic rays</field>
            <field name="answer_score">-10</field>
        </record>
        <record id="survey_demo_quiz_p3_q4_sug4" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q4"/>
            <field name="sequence">4</field>
            <field name="value">Bricks</field>
            <field name="answer_score">-10</field>
        </record>
    <record id="survey_demo_quiz_p3_q5" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">25</field>
        <field name="title">How often should you water those plants</field>
        <field name="question_type">matrix</field>
        <field name="matrix_subtype">simple</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="comments_allowed" eval="True"/>
    </record>
        <record id="survey_demo_quiz_p3_q5_row1" model="survey.question.answer">
            <field name="matrix_question_id" ref="survey_demo_quiz_p3_q5"/>
            <field name="sequence">1</field>
            <field name="value">Cactus</field>
        </record>
        <record id="survey_demo_quiz_p3_q5_row2" model="survey.question.answer">
            <field name="matrix_question_id" ref="survey_demo_quiz_p3_q5"/>
            <field name="sequence">2</field>
            <field name="value">Ficus</field>
        </record>
        <record id="survey_demo_quiz_p3_q5_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
            <field name="sequence">1</field>
            <field name="value">Once a month</field>
        </record>
        <record id="survey_demo_quiz_p3_q5_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q5"/>
            <field name="sequence">2</field>
            <field name="value">Once a week</field>
        </record>
    <record id="survey_demo_quiz_p3_q6" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">26</field>
        <field name="title">When do you harvest those fruits</field>
        <field name="description" type="html"><span>Best time to do it, is the right time to do it.</span></field>
        <field name="question_type">matrix</field>
        <field name="matrix_subtype">multiple</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="comments_allowed" eval="False"/>
    </record>
        <record id="survey_demo_quiz_p3_q6_row1" model="survey.question.answer">
            <field name="matrix_question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">1</field>
            <field name="value">Apples</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_row2" model="survey.question.answer">
            <field name="matrix_question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">2</field>
            <field name="value">Strawberries</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_row3" model="survey.question.answer">
            <field name="matrix_question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">3</field>
            <field name="value">Clementine</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">1</field>
            <field name="value">Spring</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">2</field>
            <field name="value">Summer</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">3</field>
            <field name="value">Autumn</field>
        </record>
        <record id="survey_demo_quiz_p3_q6_sug4" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p3_q6"/>
            <field name="sequence">4</field>
            <field name="value">Winter</field>
        </record>
    
    <!-- Page 4: Trees -->
    <record id="survey_demo_quiz_p4" model="survey.question">
        <field name="title">Trees</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">30</field>
        <field name="is_page" eval="True"/>
        <field name="question_type" eval="False"/>
        <field name="description" type="html">
            <p>
                We like to say that the apple doesn't fall far from the tree, so here are trees.
            </p>
        </field>
    </record>

    <record id="survey_demo_quiz_p4_q1" model="survey.question">
        <field name="title">Dogwood is from which family of trees ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">31</field>
        <field name="question_type">simple_choice</field>
        <field name="column_nb">6</field>
        <field name="allow_value_image" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p4_q1_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="sequence">1</field>
        <field name="value">Pinaceae</field>
        <field name="value_image" type="base64" file="survey/static/img/pinaceae.jpg"/>
    </record>
    <record id="survey_demo_quiz_p4_q1_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="sequence">2</field>
        <field name="value">Ulmaceae</field>
        <field name="value_image" type="base64" file="survey/static/img/ulmaceae.jpg"/>
    </record>
    <record id="survey_demo_quiz_p4_q1_sug3" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="sequence">3</field>
        <field name="value">Cornaceae</field>
        <field name="value_image" type="base64" file="survey/static/img/cornaceae.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">20</field>
    </record>
    <record id="survey_demo_quiz_p4_q1_sug4" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q1"/>
        <field name="sequence">4</field>
        <field name="value">Salicaceae</field>
        <field name="value_image" type="base64" file="survey/static/img/salicaceae.jpg"/>
    </record>

    <record id="survey_demo_quiz_p4_q2" model="survey.question">
        <field name="title">In which country did the bonsai technique develop ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">32</field>
        <field name="question_type">simple_choice</field>
        <field name="column_nb">6</field>
        <field name="allow_value_image" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p4_q2_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="sequence">1</field>
        <field name="value">Japan</field>
        <field name="value_image" type="base64" file="survey/static/img/japan.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">20</field>
    </record>
    <record id="survey_demo_quiz_p4_q2_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="sequence">2</field>
        <field name="value">China</field>
        <field name="value_image" type="base64" file="survey/static/img/china.jpg"/>
    </record>
    <record id="survey_demo_quiz_p4_q2_sug3" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="sequence">3</field>
        <field name="value">Vietnam</field>
        <field name="value_image" type="base64" file="survey/static/img/vietnam.jpg"/>
    </record>
    <record id="survey_demo_quiz_p4_q2_sug4" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q2"/>
        <field name="sequence">4</field>
        <field name="value">South Korea</field>
        <field name="value_image" type="base64" file="survey/static/img/south_korea.jpg"/>
    </record>

    <record id="survey_demo_quiz_p4_q3" model="survey.question">
        <field name="title">Is the wood of a coniferous hard or soft ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">33</field>
        <field name="question_type">simple_choice</field>
        <field name="column_nb">6</field>
        <field name="constr_mandatory" eval="True"/>
        <field name="description" type="html">
            <p>
                <img class="img-fluid o_we_custom_image d-block mx-auto"
                    src="/survey/static/img/coniferous.jpg"/><br/>
            </p>
        </field>
    </record>
    <record id="survey_demo_quiz_p4_q3_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q3"/>
        <field name="sequence">1</field>
        <field name="value">Hard</field>
    </record>
    <record id="survey_demo_quiz_p4_q3_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q3"/>
        <field name="sequence">2</field>
        <field name="value">Soft</field>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">10</field>
    </record>

    <record id="survey_demo_quiz_p4_q4" model="survey.question">
        <field name="title">From which continent is native the Scots pine (pinus sylvestris) ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">34</field>
        <field name="question_type">simple_choice</field>
        <field name="allow_value_image" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
        <field name="description" type="html">
            <p>
                <img class="img-fluid o_we_custom_image d-block mx-auto"
                    src="/survey/static/img/pinus_sylvestris.jpg" style="width: 100%;"/><br/>
            </p>
        </field>
    </record>
    <record id="survey_demo_quiz_p4_q4_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="sequence">1</field>
        <field name="value">Africa</field>
        <field name="value_image" type="base64" file="survey/static/img/africa.png"/>
    </record>
    <record id="survey_demo_quiz_p4_q4_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="sequence">2</field>
        <field name="value">Asia</field>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">20</field>
        <field name="value_image" type="base64" file="survey/static/img/asia.png"/>
    </record>
    <record id="survey_demo_quiz_p4_q4_sug3" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="sequence">3</field>
        <field name="value">Europe</field>
        <field name="value_image" type="base64" file="survey/static/img/europe.png"/>
    </record>
    <record id="survey_demo_quiz_p4_q4_sug4" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q4"/>
        <field name="sequence">4</field>
        <field name="value">South America</field>
        <field name="value_image" type="base64" file="survey/static/img/south_america.png"/>
    </record>

    <record id="survey_demo_quiz_p4_q5" model="survey.question">
        <field name="title">In the list below, select all the coniferous.</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">35</field>
        <field name="question_type">multiple_choice</field>
        <field name="column_nb">6</field>
        <field name="allow_value_image" eval="True"/>
        <field name="constr_mandatory" eval="True"/>
    </record>
    <record id="survey_demo_quiz_p4_q5_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="sequence">1</field>
        <field name="value">Douglas Fir</field>
        <field name="value_image" type="base64" file="survey/static/img/douglas_fir.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">5</field>
    </record>
    <record id="survey_demo_quiz_p4_q5_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="sequence">2</field>
        <field name="value">Norway Spruce</field>
        <field name="value_image" type="base64" file="survey/static/img/norway_spruce.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">5</field>
    </record>
    <record id="survey_demo_quiz_p4_q5_sug3" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="sequence">3</field>
        <field name="value">European Yew</field>
        <field name="value_image" type="base64" file="survey/static/img/european_yew.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">5</field>
    </record>
    <record id="survey_demo_quiz_p4_q5_sug4" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q5"/>
        <field name="sequence">4</field>
        <field name="value">Mountain Pine</field>
        <field name="value_image" type="base64" file="survey/static/img/mountain_pine.jpg"/>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">5</field>
    </record>

    <record id="survey_demo_quiz_p4_q6" model="survey.question">
        <field name="title">After watching this video, will you swear that you are not going to procrastinate to trim your hedge this year ?</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">36</field>
        <field name="question_type">simple_choice</field>
        <field name="column_nb">6</field>
        <field name="description" type="html">
            <div class="text-center">
                <div class="media_iframe_video" data-oe-expression="//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0" style="width: 50%;">
                    <div class="css_editable_mode_display"/>
                    <div class="media_iframe_video_size" contenteditable="false"/>
                    <iframe src="//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0" frameborder="0" contenteditable="false"></iframe>
                </div><br/>
            </div>
        </field>
    </record>
    <record id="survey_demo_quiz_p4_q6_sug1" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="sequence">1</field>
        <field name="value">Yes</field>
        <field name="is_correct" eval="True"/>
        <field name="answer_score">10</field>
    </record>
    <record id="survey_demo_quiz_p4_q6_sug2" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="sequence">2</field>
        <field name="value">No</field>
    </record>
    <record id="survey_demo_quiz_p4_q6_sug3" model="survey.question.answer">
        <field name="question_id" ref="survey_demo_quiz_p4_q6"/>
        <field name="sequence">3</field>
        <field name="value">Perhaps</field>
        <field name="answer_score">-10</field>
    </record>

    <!-- Page 5: Feedback - non scored question -->
    <record id="survey_demo_quiz_p5" model="survey.question">
        <field name="title">Your feeling</field>
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">40</field>
        <field name="question_type" eval="False"/>
        <field name="is_page" eval="True"/>
        <field name="description" type="html">
            <p>We may be interested by your input.</p></field>
    </record>
    <record id="survey_demo_quiz_p5_q1" model="survey.question">
        <field name="survey_id" ref="survey_demo_quiz"/>
        <field name="sequence">41</field>
        <field name="title">What do you think about this survey ?</field>
        <field name="description" type="html"><span>If you don't like us, please try to be as objective as possible.</span></field>
        <field name="question_type">simple_choice</field>
        <field name="comments_allowed" eval="True"/>
        <field name="comment_count_as_answer" eval="True"/>
        <field name="constr_mandatory" eval="False"/>
    </record>
        <record id="survey_demo_quiz_p5_q1_sug1" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p5_q1"/>
            <field name="sequence">1</field>
            <field name="value">Good</field>
        </record>
        <record id="survey_demo_quiz_p5_q1_sug2" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p5_q1"/>
            <field name="sequence">2</field>
            <field name="value">Not Good, Not Bad</field>
        </record>
        <record id="survey_demo_quiz_p5_q1_sug3" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p5_q1"/>
            <field name="sequence">3</field>
            <field name="value">Iznogoud</field>
        </record>
        <record id="survey_demo_quiz_p5_q1_sug4" model="survey.question.answer">
            <field name="question_id" ref="survey_demo_quiz_p5_q1"/>
            <field name="sequence">4</field>
            <field name="value">I have no idea, I'm a dog!</field>
        </record>
</data></odoo>
