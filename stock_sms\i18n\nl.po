# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_sms
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# 04a2cd0fd6ee22172c36ea91f27a38c5_60041bf, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: 04a2cd0fd6ee22172c36ea91f27a38c5_60041bf, 2021\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_sms
#: model:sms.template,body:stock_sms.sms_template_data_stock_delivery
msgid ""
"\n"
"                {{ (object.company_id.name + ': We are glad to inform you that your order n° ' + object.origin + ' has been shipped.' if object.origin else object.company_id.name + ': We are glad to inform you that your order has been shipped.') + (' Your tracking reference is ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}\n"
"            "
msgstr ""
"\n"
"                {{ (object.company_id.name + ': We zijn blij je te kunnen mededelen datje bestelling nr. ' + object.origin + ' is verzonden.' if object.origin else object.company_id.name + ': We zijn blij je te kunnen melden dat je bestelling is verzonden.') + ('Je tracking referentie is ' + (object.carrier_tracking_ref) + '.' if hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref else '') }}"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Cancel"
msgstr "Annuleren"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Confirm"
msgstr "Bevestigen"

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_confirm_stock_sms
msgid "Confirm Stock SMS"
msgstr "Bevestig voorraad SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: stock_sms
#: model:sms.template,name:stock_sms.sms_template_data_stock_delivery
msgid "Delivery: Send by SMS Text Message"
msgstr "Levering: verzenden per sms-bericht"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid "Disable SMS"
msgstr "SMS uitschakelen"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__has_received_warning_stock_sms
msgid "Has Received Warning Stock Sms"
msgstr "Heeft voorraadwaarschuwing SMS ontvangen"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__id
msgid "ID"
msgstr "ID"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_confirm_stock_sms__pick_ids
msgid "Pick"
msgstr "Pick"

#. module: stock_sms
#: code:addons/stock_sms/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
#, python-format
msgid "SMS"
msgstr "SMS"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_move_sms_validation
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Confirmation"
msgstr "SMS bevestiging"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
#: model_terms:ir.ui.view,arch_db:stock_sms.res_config_settings_view_form_stock
msgid "SMS Template"
msgstr "SMS sjabloon"

#. module: stock_sms
#: model:ir.model.fields,field_description:stock_sms.field_res_config_settings__stock_move_sms_validation
msgid "SMS Validation with stock move"
msgstr "SMS bevestiging met voorraadverplaatsing"

#. module: stock_sms
#: model:ir.model.fields,help:stock_sms.field_res_company__stock_sms_confirmation_template_id
#: model:ir.model.fields,help:stock_sms.field_res_config_settings__stock_sms_confirmation_template_id
msgid "SMS sent to the customer once the order is done."
msgstr "SMS verzonden naar de klant wanneer de order gereed is."

#. module: stock_sms
#: model:ir.model,name:stock_sms.model_stock_picking
msgid "Transfer"
msgstr "Verplaatsing"

#. module: stock_sms
#: model_terms:ir.ui.view,arch_db:stock_sms.view_confirm_stock_sms
msgid ""
"You are about to confirm this Delivery Order by SMS Text Message.<br/>\n"
"                This feature can easily be disabled from the Settings of Inventory or by clicking on \"Disable SMS\".<br/>"
msgstr ""
"Je staat op het punt deze levering per sms te bevestigen.<br/>\n"
"Deze functie kan eenvoudig worden uitgeschakeld via de instellingen van de voorraad of door te klikken op \"SMS uitschakelen\".<br/>"
