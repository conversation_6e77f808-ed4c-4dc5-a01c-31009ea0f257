# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_quotation_builder
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is put on the quote."
msgstr ""
":\n"
"                                        เนื้อหานี้จะปรากฏบนใบเสนอราคาก็ต่อเมื่อ\n"
"                                        สินค้าอยู่ในใบเสนอราคา"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
":\n"
"                                        this content will appear on the quotation only if this\n"
"                                        product is used in the quote."
msgstr ""
":\n"
"                                        เนื้อหานี้จะปรากฏบนใบเสนอราคาก็ต่อเมื่อ\n"
"                                       สินค้าถูกใช้อยู่ในใบเสนอราคา"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid ""
":\n"
"                        the content below will disappear if this\n"
"                        product is removed from the quote."
msgstr ""
":\n"
"                        เนื้อหาด้านล่างจะหายไปหาก\n"
"                        สินค้าถูกนำออกจากใบเสนอราคา"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"<strong>Template Header:</strong> this content\n"
"                                    will appear on all quotations using this\n"
"                                    template."
msgstr ""
"<strong>หัวเรื่องเทมเพลต:</strong>เนื้อหานี้ \n"
"                                    จะปรากฎบนทุกใบเสนอราคาที่ใช้\n"
"                                    เทมเพลตนี้"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "About us"
msgstr "เกี่ยวกับเรา"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "An awesome"
msgstr "ยอดเยี่ยม!"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"As a leading professional services firm,\n"
"                                we know that success is all about the\n"
"                                commitment we put on strong services."
msgstr ""
"ในฐานะบริษัทผู้ให้บริการมืออาชีพชั้นนำ\n"
"                                เรารู้ว่าความสำเร็จนั้นขึ้นอยู่กับ\n"
"                                ความมุ่งมั่นที่เราทุ่มเทให้กับการบริการที่แข็งแกร่ง"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
msgid "Close"
msgstr "ปิด"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Design Template"
msgstr "ออกแบบเทมเพลต"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Great quotation templates will significantly\n"
"                                <strong>boost your success rate</strong>. The\n"
"                                first section is usually about your company,\n"
"                                your references, your methodology or\n"
"                                guarantees, your team, SLA, terms and conditions, etc."
msgstr ""
"เทมเพลตใบเสนอราคาที่ยอดเยี่ยมจะสามารถ\n"
"                                <strong>เพิ่มอัตราความสำเร็จ</strong>อย่างมาก\n"
"                                ส่วนแรกมักเกี่ยวกับบริษัทของคุณ\n"
"                                ข้อมูลอ้างอิง วิธีการของคุณ หรือ\n"
"                                การรับประกัน ทีมของคุณ SLA ข้อกำหนดและเงื่อนไขต่างๆ"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"If you edit a quotation from the 'Preview' of a quotation, you will\n"
"                        update that quotation only. If you edit the quotation\n"
"                        template (from the Configuration menu), all future quotations will\n"
"                        use this modified template."
msgstr ""
"หากคุณแก้ไขใบเสนอราคาจาก 'ตัวอย่าง' ของใบเสนอราคา คุณจะ\n"
"                        ปรับปรุงใบเสนอราคานั้นเท่านั้น หากคุณแก้ไขใบเสนอราคา\n"
"                       เทมเพลต (จากเมนูการกำหนดค่า) ใบเสนอราคาทั้งหมดในอนาคตจะ\n"
"                        ใช้เทมเพลตที่แก้ไขแล้วนี้"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.brand_promotion
msgid "Open Source CRM"
msgstr "โอเพนซอร์ส CRM"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Optional Product:"
msgstr "สินค้าทางเลือก:"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Offer"
msgstr "ข้อเสนอของเรา"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Quality"
msgstr "คุณภาพของเรา"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Our Service"
msgstr "บริการของเรา"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid "Price"
msgstr "ราคา"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"Product quality is the foundation we\n"
"                                stand on; we build it with a relentless\n"
"                                focus on fabric, performance and craftsmanship."
msgstr ""
"คุณภาพของสินค้าคือรากฐานที่เรา\n"
"                               ยืนหยัด เราสร้างมันขึ้นมาอย่างไม่หยุดยั้ง\n"
"                                เน้นเนื้อผ้า ประสิทธิภาพ และฝีมือช่าง"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_portal_content_inherit_sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Product:"
msgstr "สินค้า:"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_description
msgid "Quotation Description"
msgstr "คำอธิบายใบเสนอราคา"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_product_template__quotation_only_description
msgid "Quotation Only Description"
msgstr "คำอธิบายใบเสนอราคาเท่านั้น"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template
msgid "Quotation Template"
msgstr "เทมเพลตใบเสนอราคา"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "ไลน์เทมเพลตใบเสนอราคา"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_template_option
msgid "Quotation Template Option"
msgstr "ตัวเลือกเทมเพลตใบเสนอราคา"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_option
msgid "Sale Options"
msgstr "ตัวเลือกการขาย"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_quotation_builder
#: model:ir.model,name:sale_quotation_builder.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "Terms &amp; Conditions"
msgstr "เงื่อนไขและข้อกำหนด"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_only_description
#: model:ir.model.fields,help:sale_quotation_builder.field_sale_order_template_line__website_description
msgid "The quotation description (not used on eCommerce)"
msgstr "คำอธิบายใบเสนอราคา (ไม่ได้ใช้กับอีคอมเมิร์ซ)"

#. module: sale_quotation_builder
#: model:ir.model.fields,help:sale_quotation_builder.field_product_product__quotation_description
#: model:ir.model.fields,help:sale_quotation_builder.field_product_template__quotation_description
msgid ""
"This field uses the Quotation Only Description if it is defined, otherwise "
"it will try to read the eCommerce Description."
msgstr ""
"ฟิลด์นี้ใช้ เฉพาะคำอธิบายใบเสนอราคาเท่านั้น ถ้ามีการกำหนดไว้ "
"มิฉะนั้นจะพยายามอ่านคำอธิบายอีคอมเมิร์ซ"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"This is a <strong>sample quotation template</strong>. You should\n"
"                                customize it to fit your own needs from the <i>Sales</i>\n"
"                                application, using the menu: Configuration /\n"
"                                Quotation Templates."
msgstr ""
"นี้คือ <strong>ตัวอย่างเทมเพลตใบเสนอราคา</strong>คุณควร\n"
"                                ปรับแต่งมันให้ตรงกับความต้องการของคุณจากแอป <i>การขาย</i>\n"
"                                โดยใช้เมนู: กำหนดค่า /\n"
"                                เทมเพลตใบเสนอราคา"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid "This is a preview of the sale order template."
msgstr "นี่คือตัวอย่างเทมเพลตใบสั่งขาย"

#. module: sale_quotation_builder
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.so_template
msgid ""
"Titles with style <i>Heading 2</i> and\n"
"                                    <i>Heading 3</i> will be used to generate the\n"
"                                    table of content automatically."
msgstr ""
"ชื่อเรื่องอย่างมีสไตล์ <i>หัวเรื่อง 2</i> และ\n"
"                                    <i>หัวเรื่อง 3</i> จะถูกนำมาใช้เพื่อสร้าง\n"
"                                   สารบัญโดยอัตโนมัติ"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"We always ensure that our products are\n"
"                                set at a fair price so that you will be\n"
"                                happy to buy them."
msgstr ""
"เรามั่นใจเสมอว่าสินค้าของเรานั้น\n"
"                                กำหนดราคาที่ยุติธรรมเพื่อให้คุณ\n"
"                                ยินดีที่จะซื้อสินค้า"

#. module: sale_quotation_builder
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_option__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_line__website_description
#: model:ir.model.fields,field_description:sale_quotation_builder.field_sale_order_template_option__website_description
#: model_terms:ir.ui.view,arch_db:sale_quotation_builder.sale_order_template_view_form_inherit_sale_quotation_builder
msgid "Website Description"
msgstr "คำอธิบายเว็บไซต์"

#. module: sale_quotation_builder
#: model_terms:sale.order.template,website_description:sale_quotation_builder.sale_order_template_default
msgid ""
"You can <strong>set a description per product</strong>. Odoo will\n"
"                        automatically create a quotation using the descriptions\n"
"                        of all products in the proposal. The table of content\n"
"                        on the left is generated automatically using the styles you\n"
"                        used in your description (heading 1, heading 2, ...)"
msgstr ""
"คุณสามารถ<strong>ตั้งคำอธิบายต่อรายการสินค้า</strong> Odoo จะ\n"
"                        สร้างใบเสนอราคาอัตโนมัติโดยใช้คำอธิบาย\n"
"                        ของสินค้าทั้งหมดในข้อเสนอ สารบัญ\n"
"                        ทางด้านซ้ายจะถูกสร้างขึ้นโดยอัตโนมัติโดยใช้สไตล์ที่คุณ\n"
"                        ใช้ในคำอธิบายของคุณ (หัวเรื่อง 1, หัวเรื่อง 2, ...)"
