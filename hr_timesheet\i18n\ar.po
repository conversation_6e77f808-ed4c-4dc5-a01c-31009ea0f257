# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr "%s مقضي "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(%(sign)s%(hours)s:%(minutes)s متبقي) "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "(%s days remaining)"
msgstr "(%s أيام متبقية) "

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr "<b class=\"tip_title\">نصيحة: قم بتسجيل جداولك الزمنية بشكل أسرع</b> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for employee:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">الجداول الزمنية للموظف:</em> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for project:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">الجداول الزمنية للمشروع:</em> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for task:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">الجداول الزمنية للمهمة:</em> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "<em class=\"font-weight-normal text-muted\">Timesheets on </em>"
msgstr "<em class=\"font-weight-normal text-muted\">الجداول الزمنية </em>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar\" role=\"img\" aria-label=\"Date\" title=\"التاريخ \"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o \" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o \" title=\"القيم المحددة هنا خاصة "
"بالشركة فقط. \" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"القيم المحددة هنا خاصة "
"بالشركة فقط. \" groups=\"base.group_multi_company\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">مسجل</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">الجداول الزمنية</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Days Spent on Sub-tasks:</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">الساعات المقضية في المهام الفرعية:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">الأيام المقضية في المهام الفرعية:</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Sub-tasks Hours Spent</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Sub-tasks Days Spent</span>"
msgstr ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">الساعات المقضية في المهام الفرعية</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">الأيام المقضية في المهام الفرعية</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\">إجمالي (الأيام)</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\">إجمالي (الساعات)</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> day(s)</span>"
msgstr "<span> يوم (أيام)</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
msgid "<span> hour(s)</span>"
msgstr "<span> ساعة (ساعات)</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>التاريخ</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>الوصف</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Project</span>"
msgstr "<span>المشروع</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Responsible</span>"
msgstr "<span>المسؤول</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Task</span>"
msgstr "<span>المهام</span> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days recorded:</strong>"
msgstr "<strong>الأيام المسجلة:</strong> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr "<strong>المدة: </strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours recorded:</strong>"
msgstr "<strong>الساعات المسجلة:</strong> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task_planned_hours_template
msgid "<strong>Planned Days:</strong>"
msgstr "<strong>الأيام المخطط لها:</strong> "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>مدى التقدم:</strong> "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr "الحساب التحليلي الفعال "

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "المدير "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr "الكل"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "كافة الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr "السماح بالجداول الزمنية "

#. module: hr_timesheet
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr "التحليل"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "القيد التحليلي "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "البند التحليلي"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project is linked for financial management. "
"Use an analytic account to record cost and revenue on your project."
msgstr ""
"الحساب التحليلي الذي يرتبط به هذا المشروع للإدارة المالية. استخدم حساباً "
"تحليلياً لتسجيل التكلفة والإيرادات في مشروعك. "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Apple App Store"
msgstr "متجر تطبيقات Apple "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "Application Settings"
msgstr "إعدادات التطبيق "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr "الجداول الزمنية المرتبطة "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_synchro
msgid "Awesome Timesheet"
msgstr "الجداول الزمنية الرائعة "

#. module: hr_timesheet
#: model:project.task.type,legend_blocked:hr_timesheet.internal_project_default_stage
msgid "Blocked"
msgstr "محجوب"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "حسب الموظف"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "حسب المشروع"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "حسب المهمة"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "المتعاونون في المشروع المُشارَك "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "التهيئة "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_task_create_timesheet
msgid "Create Timesheet from task"
msgstr "إنشاء جداول زمنية من مهمة "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__currency_id
msgid "Currency"
msgstr "العملة"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "التاريخ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr "الأيام المستغرقة "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Days recorded on sub-tasks:"
msgstr "الأيام المسجلة في المهام الفرعية: "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr "قيمة المشروع الافتراضية للجداول الزمنية المنشأة من نوع الإجازة. "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Delete"
msgstr "حذف"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "القسم"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Describe your activity..."
msgstr "قم بوصف نشاطك..."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__description
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "الوصف"

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#, python-format
msgid "Discard"
msgstr "تجاهل"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__progress
msgid "Display progress of current task."
msgstr "عرض نسبة التقدم في المهمة الحالية."

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "Download our App"
msgstr "حمّل تطبيقنا "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "المدة"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Days)"
msgstr "المدة (بالأيام)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Duration (Hours)"
msgstr "المدة (ساعات) "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_effective
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "ساعات العمل الفعلية"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "الموظف"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "تذكير الموظف"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__allow_timesheets
msgid "Enable timesheeting on the project."
msgstr "تمكين الجدول الزمني في المشروع."

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr "ترميز وحدة القياس بالأيام "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid "Encoding Unit"
msgstr "وحدة الترميز"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr "وحدة قياس الترميز "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Generate timesheets upon time off validation"
msgstr "قم بإنشاء الجداول الزمنية بمجرد تصديق الإجازة "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Chrome Store"
msgstr "متجر Google Chrome "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Google Play Store"
msgstr "متجر Google Play "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Group By"
msgstr "التجميع حسب "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "Has Planned Hours Tasks"
msgstr "يحتوي على مهام الساعات المخطط لها "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Hours"
msgstr "الساعات"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr "الوقت المستغرق"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Hours recorded on sub-tasks:"
msgstr "الساعات المسجلة في المهام الفرعية: "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__id
msgid "ID"
msgstr "المُعرف"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "If checked, send an email to all manager"
msgstr "إذا كان محددًا، قم بإرسال رسالة بريد إلكتروني إلى كافة المدراء "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid ""
"If checked, send an email to all users who have not recorded their timesheet"
msgstr ""
"إذا كان محددًا، قم بإرسال رسالة بريد إلكتروني إلى كافة المستخدمين الذين لم "
"يسجلوا جداولهم الزمنية "

#. module: hr_timesheet
#: model:project.task.type,legend_normal:hr_timesheet.internal_project_default_stage
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Initially Planned Days"
msgstr "الأيام المخطط لها مبدئياً "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Initially Planned Hours"
msgstr "ساعات العمل المبدئية المخطط لها"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr "داخلي"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "مشروع داخلي"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr "ترميز أيام وحدة القياس "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr "مشروع داخلي "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr "الشهر الماضي"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr "الأسبوع الماضي "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr "العام الماضي"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid "Log time on tasks"
msgstr "تسجيل وقت المهام "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_manager_allow
msgid "Manager Reminder"
msgstr "تذكير المدير"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr "الاجتماع"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "القائمة"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Projects"
msgstr "مشاريع فريقي "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "My Team's Tasks"
msgstr "مهام فريقي "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "جداولي الزمنية "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr "الأحدث"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No activities found"
msgstr "لم يتم العثور على أي أنشطة "

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr "لم يتم العثور على أي أنشطة. فلنبدأ نشاطاً جديداً! "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr "لا شيء"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
msgid "Overtime"
msgstr "الوقت الإضافي "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__hours_planned
msgid "Planned Hours"
msgstr "الساعات المخطط لها"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "وحدة قياس المنتج"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "مدى التقدم "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
#, python-format
msgid "Project"
msgstr "المشروع"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "وحدة الزمن للمشروع"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "الجداول الزمنية للمشروع "

#. module: hr_timesheet
#: model:project.task.type,legend_done:hr_timesheet.internal_project_default_stage
msgid "Ready"
msgstr "جاهز"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr "تسجيل نشاط جديد"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""
"قم بتسجيل جداولك الزمنية في لحظات عن طريق الضغط على Shift+ مفتاح التشغيل "
"السريع المناسب لإضافة 15 دقيقة إلى مشاريعك. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "Recorded"
msgstr "تم التسجيل "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr "الأيام المتبقية"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Days:"
msgstr "الأيام المتبقية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr "الساعات المتبقية"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Hours:"
msgstr "الساعات المتبقية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr "الوقت المفوتر المتبقي "

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save"
msgstr "حفظ"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_create_timesheet_view_form
msgid "Save time"
msgstr "حفظ الوقت "

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/xml/qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Awesome Timesheet app:"
msgstr "قم بمسح الـ QR كود للحصول على تطبيق الجداول الزمنية الرائعة: "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr "البحث في الكل"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr "البحث في الوصف "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr "البحث في الموظفين "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr "البحث في المشاريع "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr "البحث في المهام "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "See timesheet entries"
msgstr "رؤية قيود الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets managers"
msgstr "إرسال تذكير دوري عبر البريد الإلكتروني لمدراء الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets users"
msgstr "إرسال تذكير دوري عبر البريد الإلكتروني لمستخدمي الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "الإعدادات"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Sub-tasks Hours Spent"
msgstr "الساعات المستغرقة لإجراء المهام الفرعية"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Days"
msgstr "الأيام المخطط لها للمهام الفرعية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Planned Hours"
msgstr "الساعات المخطط لها المهام الفرعية "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#, python-format
msgid "Task"
msgstr "المهمة"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task_create_timesheet__task_id
msgid "Task for which we are creating a sales order"
msgstr "المهام التي ننشئ أمر مبيعات لها "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "الجداول الزمنية للمهمة "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
msgid "Task:"
msgstr "المهمة: "

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "تحليل المهام"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_user_view_search
msgid "Tasks in Overtime"
msgstr "المهام في الوقت الإضافي "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr "يجب أن يكون المشروع الداخلي للشركة في تلك الشركة. "

#. module: hr_timesheet
#: model:ir.model.constraint,message:hr_timesheet.constraint_project_task_create_timesheet_time_positive
msgid "The timesheet's time must be positive"
msgstr "يجب أن يكون الوقت المسجل في الجداول الزمنية موجباً "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "لا توجد جداول زمنية"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""
"لدى هذه المشاريع قيود جداول زمنية تشير إليها. عليك إزالة قيود الجداول "
"الزمنية هذه قبل إزالة المشاريع. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""
"لدى هذه المهام قيود جداول زمنية تشير إليها. عليك إزالة قيود الجداول الزمنية "
"هذه قبل إزالة المهام. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr "ربع السنة الجاري"

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr "هذا الشهر"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""
"لدى هذا المشروع قيود جداول زمنية تشير إليه. عليك إزالة قيود الجداول الزمنية "
"هذه قبل إزالة المشروع. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""
"لدى هذه المهمة قيود جداول زمنية تشير إليها. عليك إزالة قيود الجداول الزمنية "
"هذه قبل إزالة المهمة. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid ""
"This task must be part of a project because there are some timesheets linked"
" to it."
msgstr ""
"يجب أن تكون هذه المهمة جزءًا من المشروع نظرًا لوجود بعض الجداول الزمنية "
"المرتبطة بها."

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr "هذا الأسبوع "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""
"سيقوم هذا بتعيين وحدة القياس المستخدمة في المشاريع والمهام.\n"
"إذا استخدمت الجداول الزمنية المرتبطة بالمشاريع، لا تنس ضبط وحدة القياس الصحيحة لموظفيك. "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_company__timesheet_encode_uom_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"سيقوم هذا بتعيين وحدة القياس المستخدمة لترميز الجداول الزمنية. سيوفر هذا أدوات\n"
"        للمساعدة في عملية الترميز. لكن ستظل التقارير مُعبر عنها بالساعات (القيمة الافتراضية). "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr "هذا العام"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task_create_timesheet__time_spent
msgid "Time"
msgstr "الوقت"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr "ترميز الوقت"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "الإجازات "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Days)"
msgstr "الوقت المقضي (الأيام) "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Time Spent (Hours)"
msgstr "الوقت المقضي (الساعات) "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr "الوقت المقضي في المهام الفرعية (ومهامها الفرعية) لهذه المهمة "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__effective_hours
msgid "Time spent on this task, excluding its sub-tasks."
msgstr "الوقت المقضي في هذه المهمة، باستثناء مهامها الفرعية. "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr "الوقت المقضي في هذه المهمة، شاملة المهام الفرعية. "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "الوحدة الزمنية المستخدمة لتسجيل جداولك الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "أنشطة الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__timesheet_cost
msgid "Timesheet Cost"
msgstr "تكلفة الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "تكاليف الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_count
msgid "Timesheet Count"
msgstr "عدد الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "وحدة ترميز الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "Timesheet Entries"
msgstr "قيود الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Timesheet by Date"
msgstr "ترتيب الجداول الزمنية حسب التاريخ "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#: code:addons/hr_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "الجداول الزمنية "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "التحكم في الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "الجداول الزمنية بواسطة الموظف "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "الجداول الزمنية حسب المشروع "

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "الجداول الزمنية حسب المهمة "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr "يمكن التسجيل في الجداول الزمنية لهذه المهمة. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created with an active employee."
msgstr "يجب أن يتم إنشاء الجداول الزمنية مع موظف نشط. "

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr "نصيحة: قم بتسجيل جداولك الزمنية بشكل أسرع "

#. module: hr_timesheet
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr "اليوم"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr "الإجمالي"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr "إجمالي الأيام "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Hours"
msgstr "عدد الساعات الكلية"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr "إجمالي وقت الجداول الزمنية "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid ""
"Total number of time (in the proper UoM) recorded in the project, rounded to"
" the unit."
msgstr ""
"إجمالي الوقت (بوحدة القياس المناسبة) مسجل في المشروع، مقرب إلى الوحدة. "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid ""
"Total remaining time, can be re-estimated periodically by the assignee of "
"the task."
msgstr ""
"إجمالي الوقت المتبقي، يمكن إعادة تقديره دوريًا من قبل الشخص المُحالة إليه "
"المهمة."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "الإجمالي:"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Track your time from anywhere, even offline, with our web/mobile apps"
msgstr ""
"تتبع الوقت من أي مكان حتى وإن لم تكن متصلاً بالإنترنت، عن طريق تطبيقاتنا على"
" الويب/الهاتف المحمول "

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr "تتبع ساعات عملك في كل مشروع كل يوم وقم بفوترة هذا الوقت لعملائك. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr "التدريب"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__has_planned_hours_tasks
msgid "True if any of the project's task has a set planned hours"
msgstr "تكون القيمة صحيحة إذا مانت لأي من المهام ساعات مخططة محددة "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
msgid "User"
msgstr "المستخدم"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "المستخدم: كافة الجداول الزمنية "

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "المستخدم: جداول المستخدم الزمنية فقط "

#. module: hr_timesheet
#. openerp-web
#: code:addons/hr_timesheet/static/src/js/timesheet_config_form_view.js:0
#, python-format
msgid "View App"
msgstr "عرض التطبيق "

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr "أداة ذكية "

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_uom_uom__timesheet_widget
msgid ""
"Widget used in the webclient when this unit is the one used to encode "
"timesheets."
msgstr ""
"أداة ذكية تُستخدم في عميل الويب عندما يتم اختيار هذه الوحدة لترميز الجداول "
"الزمنية. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr "لا يمكنك الوصول إلى جداول زمنية لشخص آخر. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project linked to an inactive analytic "
"account."
msgstr "لا يمكنك إضافة جداول زمنية إلى مشروع مرتبط بحساب تحليلي غير نشط. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid ""
"You cannot add timesheets to a project or a task linked to an inactive "
"analytic account."
msgstr ""
"لا يمكنك إضافة جداول زمنية إلى مشروع أو مهمة مرتبطة بحساب تحليلي غير نشط. "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account. Please change this account, or reactivate the current one "
"to timesheet on the project."
msgstr ""
"لا يمكنك تسجيل الجداول الزمنية في هذا المشروع حيث أنه مرتبط بحساب تحليلي غير"
" نشط. يرجى تغيير هذا الحساب أو إعادة تنشيط الحساب الحالي لتسجيل الجداول "
"الزمنية في المشروع. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr "لا يمكنك وضع موظف قد تمت أرشفته في الجداول الزمنية الموجودة بالفعل. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr "لا يمكنك استخدام الجداول الزمنية دون حساب تحليليلي. "

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr "يوم"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_project_task_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_project
msgid "for the"
msgstr "من أجل"

#. module: hr_timesheet
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr "ساعات"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "per hour"
msgstr "في الساعة"
