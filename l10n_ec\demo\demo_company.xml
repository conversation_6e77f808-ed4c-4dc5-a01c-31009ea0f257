<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ec" model="res.partner">
        <field name="name">EC Company</field>
        <field name="vat">2067636473651</field>
        <field name="street">A</field>
        <field name="city">Quito</field>
        <field name="country_id" ref="base.ec"/>
        
        <field name="zip">170112</field>
        <field name="phone">+593 99 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ecexample.com</field>
    </record>

    <record id="demo_company_ec" model="res.company">
        <field name="name">EC Company</field>
        <field name="partner_id" ref="partner_demo_company_ec"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ec')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ec.demo_company_ec'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="ref('l10n_ec.l10n_ec_ifrs')"/>
        <value model="res.company" eval="obj().env.ref('l10n_ec.demo_company_ec')"/>
    </function>
</odoo>
