<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_libya_deduction">
    <t t-call="web.html_container">
<!--        <t t-foreach="docs" t-as="o">-->
            <t t-call="web.external_layout">
                <div class="page"><br/><br/><br/><br/><br/>
                    <h4 class="text-center">كشف المرتبات والضرائب</h4><br/>
                    <h6 class="text-center"> <span t-esc="date_year"/><span>/</span>&#160;<strong t-esc="date_month"/>&#160;لشهر </h6>

                    <table class="table table-bordered">
                           <thead>
                               <tr class="text-center">
                                   <th>م</th>
                                   <th>الاسم</th>
                                   <th>إجمالي المرتب</th>
                                   <th>خصم الضمان</th>
                                   <th>خصم التضامن</th>
                                   <th>مجموع الاستقطاعات</th>
                                   <th>الدخل الخاضع للجهاد</th>
                                   <th>ضريبة الجهاد</th>
                                   <th>حد الإعفاء</th>
                                   <th>ش1</th>
                                   <th>ش2</th>
                                   <th>مجموع ضريبة الدخل</th>
                                   <th>مجموع الضرائب المستحقة</th>
                                   <th>صافي الدخل</th>
                                   <th>دمغة المرتبات</th>
                                </tr>
                           </thead>
                           <tbody>
                                <tr t-foreach="payslip_ids" t-as="payslip" class="text-center">
                                    <td style="background-color:#D0D0D0;"><span t-esc="payslip_index + 1" /></td>
                                    <td ><span t-esc="payslip['employee_name']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['basic']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['employee_insurance']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['company_insurance']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['TASTG']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['basic_insurance']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['gehad_tax']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['deduction_tax']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['total_tax1']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['total_tax2']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['total_tax']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['total_taxes']"/></td>
                                    <td ><span t-esc="'%.3f'% payslip['net_salary']"/></td>
                                    <td style="background-color:white;"><span t-esc="'%.3f'% payslip['dm8a_tax']"/></td>
                                </tr>
                           </tbody>
                           <thead style="background-color:#D0D0D0;">
                                <tr class="text-center">
                                    <th colspan="2">الإجمالي</th>
                                    <th ><span t-esc="'%.2f'% sum(m['basic'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['employee_insurance'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['company_insurance'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['TASTG'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['basic_insurance'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['gehad_tax'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['deduction_tax'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['total_tax1'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['total_tax2'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['total_tax'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['total_taxes'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['net_salary'] for m in payslip_ids)" /></th>
                                    <th ><span t-esc="'%.2f'% sum(m['dm8a_tax'] for m in payslip_ids)" /></th>

                                </tr>
                            </thead>
                    </table>

                </div>
            </t>
        </t>
<!--    </t>-->
</template>
</odoo>
