<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="True">
      <record id="uom_categ_energy" model="uom.category">
        <field name="name">Energía</field>
      </record>
      <record id="uom_categ_others" model="uom.category">
        <field name="name">Otros</field>
      </record>

      <record id="uom.product_uom_unit" model="uom.uom">
        <field name="l10n_cl_sii_code">10</field>
      </record>

      <record id="uom.product_uom_dozen" model="uom.uom">
        <field name="l10n_cl_sii_code">11</field>
      </record>

      <record id="uom.product_uom_meter" model="uom.uom">
        <field name="l10n_cl_sii_code">14</field>
      </record>

      <record id="uom.product_uom_foot" model="uom.uom">
        <field name="l10n_cl_sii_code">13</field>
      </record>

      <record id="uom.product_uom_kgm" model="uom.uom">
        <field name="l10n_cl_sii_code">6</field>
      </record>

      <record id="uom.product_uom_litre" model="uom.uom">
        <field name="l10n_cl_sii_code">9</field>
      </record>

      <record id="product_uom_sum" model="uom.uom">
        <field name="l10n_cl_sii_code">0</field>
        <field name="name">S.U.M</field>
        <field name="category_id" ref="uom.product_uom_categ_unit"/>
        <field name="uom_type">smaller</field>
      </record>

      <record id="product_uom_tmb" model="uom.uom">
        <field name="l10n_cl_sii_code">1</field>
        <field name="name">TMB</field>
        <field name="category_id" ref="uom.product_uom_categ_kgm"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_u" model="uom.uom">
        <field name="l10n_cl_sii_code">12</field>
        <field name="name">U(JGO)</field>
        <field name="category_id" ref="uom.product_uom_categ_unit"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_mt2" model="uom.uom">
        <field name="l10n_cl_sii_code">15</field>
        <field name="name">MT2</field>
        <field name="category_id" ref="uom.uom_categ_length"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_mcub" model="uom.uom">
        <field name="l10n_cl_sii_code">16</field>
        <field name="name">MCUB</field>
        <field name="category_id" ref="uom.product_uom_categ_vol"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_par" model="uom.uom">
        <field name="l10n_cl_sii_code">17</field>
        <field name="name">PAR</field>
        <field name="category_id" ref="uom.product_uom_categ_unit"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_knfc" model="uom.uom">
        <field name="l10n_cl_sii_code">18</field>
        <field name="name">KNFC</field>
        <field name="category_id" ref="uom.product_uom_categ_kgm"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_carton" model="uom.uom">
        <field name="l10n_cl_sii_code">19</field>
        <field name="name">CARTON</field>
        <field name="category_id" ref="uom.product_uom_categ_unit"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_qmb" model="uom.uom">
        <field name="l10n_cl_sii_code">2</field>
        <field name="name">QMB</field>
        <field name="category_id" ref="uom.product_uom_categ_kgm"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_kwh" model="uom.uom">
        <field name="l10n_cl_sii_code">20</field>
        <field name="name">KWH</field>
        <field name="category_id" ref="uom_categ_energy"/>
      </record>

      <record id="product_uom_bar" model="uom.uom">
        <field name="l10n_cl_sii_code">23</field>
        <field name="name">BAR</field>
        <field name="category_id" ref="uom.product_uom_categ_vol"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_mm" model="uom.uom">
        <field name="l10n_cl_sii_code">24</field>
        <field name="name">M2/1MM</field>
        <field name="category_id" ref="uom.uom_categ_length"/>
        <field name="uom_type">smaller</field>
      </record>

      <record id="product_uom_mkwh" model="uom.uom">
        <field name="l10n_cl_sii_code">3</field>
        <field name="name">MKWH</field>
        <field name="category_id" ref="uom_categ_energy"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_tmn" model="uom.uom">
        <field name="l10n_cl_sii_code">4</field>
        <field name="name">TMN</field>
        <field name="category_id" ref="uom.product_uom_categ_kgm"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_qnt" model="uom.uom">
        <field name="l10n_cl_sii_code">5</field>
        <field name="name">QNT</field>
        <field name="category_id" ref="uom.product_uom_categ_kgm"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="uom.product_uom_gram" model="uom.uom">
        <field name="l10n_cl_sii_code">7</field>
        <field name="uom_type">smaller</field>
      </record>

      <record id="product_uom_hl" model="uom.uom">
        <field name="l10n_cl_sii_code">8</field>
        <field name="name">HL</field>
        <field name="category_id" ref="uom.product_uom_categ_vol"/>
        <field name="uom_type">bigger</field>
      </record>

      <record id="product_uom_sum_99" model="uom.uom">
        <field name="l10n_cl_sii_code">99</field>
        <field name="name">S.U.M</field>
        <field name="category_id" ref="uom.product_uom_categ_unit"/>
        <field name="uom_type">bigger</field>
      </record>
    </data>
</odoo>
