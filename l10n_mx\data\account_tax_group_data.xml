<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="tax_group_iva_0" model="account.tax.group">
            <field name="name">IVA 0%</field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_iva_16" model="account.tax.group">
            <field name="name">IVA 16% </field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_iva_8" model="account.tax.group">
            <field name="name">IVA 8%</field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_iva_ret_4" model="account.tax.group">
            <field name="name">IVA Retencion 4%</field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_iva_ret_10" model="account.tax.group">
            <field name="name">IVA Retencion 10%</field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_iva_ret_1067" model="account.tax.group">
            <field name="name">IVA Retencion 10.67%</field>
            <field name="country_id" ref="base.mx"/>
        </record>
        <record id="tax_group_isr_ret_10" model="account.tax.group">
            <field name="name">ISR Retencion 10%</field>
            <field name="country_id" ref="base.mx"/>
        </record>

        <record id="tax_group_ieps_8" model="account.tax.group">
            <field name="name">IEPS 8%</field>
        </record>

        <record id="tax_group_ieps_25" model="account.tax.group">
            <field name="name">IEPS 25%</field>
        </record>

        <record id="tax_group_ieps_26_5" model="account.tax.group">
            <field name="name">IEPS 26.5%</field>
        </record>

        <record id="tax_group_ieps_30" model="account.tax.group">
            <field name="name">IEPS 30%</field>
        </record>

        <record id="tax_group_ieps_53" model="account.tax.group">
            <field name="name">IEPS 53%</field>
        </record>
    </data>
</odoo>
