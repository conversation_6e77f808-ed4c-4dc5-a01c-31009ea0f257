# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product
# 
# Translators:
# <PERSON><PERSON><PERSON> <djord<PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid " Product Category"
msgstr "Kategorija proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_count
msgid "# Product Variants"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_product_count
msgid "# Products"
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:446
#, python-format
msgid "%s %% discount"
msgstr "%s %% popust"

#. module: product
#: code:addons/product/models/product_pricelist.py:448
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:324
#, python-format
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "16 GB"
msgstr "16 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid ""
"17\" LCD Monitor\n"
"Processor AMD 8-Core"
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_25
#: model:product.template,description:product.product_product_25_product_template
msgid ""
"17\" Monitor, 4GB RAM\n"
"Standard-1294P Processor"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "2.4 GHz"
msgstr "2.4 GHz"

#. module: product
#: model:product.product,description_sale:product.product_product_8
#: model:product.template,description_sale:product.product_product_8_product_template
msgid ""
"2.7GHz quad-core Intel Core i5\n"
"                Turbo Boost up to 3.2GHz\n"
"                8GB (two 4GB) memory\n"
"                1TB hard drive\n"
"                Intel Iris Pro graphics\n"
"            "
msgstr ""
"2.7GHz quad-core Intel Core i5\n"
"                Turbo Boost up to 3.2GHz\n"
"                8GB (two 4GB) memory\n"
"                1TB hard drive\n"
"                Intel Iris Pro graphics\n"
"            "

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "32 GB"
msgstr "32 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4_product_template
#: model:product.template,description_sale:product.product_product_4b_product_template
#: model:product.template,description_sale:product.product_product_4c_product_template
#: model:product.template,description_sale:product.product_product_4d_product_template
msgid ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 with quad-core graphics\n"
"FaceTime HD Camera, 1.2 MP Photos"
msgstr ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 with quad-core graphics\n"
"FaceTime HD Camera, 1.2 MP Photos"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "75 percent less reflection."
msgstr "75 percent less reflection."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Public Price  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'standard_price')]}\">Cost  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', 'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "<span class=\"o_stat_text\"> Products</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>m³</span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr "<strong>Valuta</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Opis</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr "<strong>Naziv cjenovnika</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Štampano dana</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                        will delete and recreate existing variants and lead\n"
"                        to the loss of their possible customizations."
msgstr ""

#. module: product
#: sql_constraint:product.product:0
msgid "A barcode can only be assigned to one product !"
msgstr "Nije dozvoljeno da dva različita proizvoda imaju isti barkod !"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_sale
#: model:ir.model.fields,help:product.field_product_template_description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_purchase
#: model:ir.model.fields,help:product.field_product_template_description_purchase
msgid ""
"A description of the Product that you want to communicate to your vendors. "
"This description will be copied to every Purchase Order, Receipt and Vendor "
"Bill/Credit Note."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid "A great Keyboard. Cordless."
msgstr "A great Keyboard. Cordless."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description
#: model:ir.model.fields,help:product.field_product_template_description
msgid ""
"A precise description of the Product, used only for internal information "
"purposes."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price list contains rules to be evaluated in order to compute\n"
"                the sale price of the products."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "A screen worthy of iPad."
msgstr "A screen worthy of iPad."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_type
#: model:ir.model.fields,help:product.field_product_template_type
msgid ""
"A stockable product is a product for which you manage stock. The \"Inventory\" app has to be installed.\n"
"A consumable product, on the other hand, is a product for which stock is not managed.\n"
"A service is a non-material product you provide.\n"
"A digital content is a non-material product you sell online. The files attached to the products are the one that are sold on the e-commerce such as e-books, music, pictures,... The \"Digital Product\" module has to be installed."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, photos, and videos you love."
msgstr ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, photos, and videos you love."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_active
#: model:ir.model.fields,field_description:product.field_product_product_active
#: model:ir.model.fields,field_description:product.field_product_template_active
#: model:ir.model.fields,field_description:product.field_product_uom_active
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Active"
msgstr "Aktivan"

#. module: product
#: model:product.category,name:product.product_category_all
msgid "All"
msgstr "Sve"

#. module: product
#: code:addons/product/models/product_pricelist.py:441
#, python-format
msgid "All Products"
msgstr "Svi proizvodi"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "All general settings about this product are managed on"
msgstr "Sva opšta podešavanja za ovaj proizvod se nalaze na"

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings_group_sale_pricelist
msgid ""
"Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"And at the Apple Online Store, you can configure your iMac with an even more"
" powerful Intel Core i7 processor, up to 3.5GHz."
msgstr ""
"And at the Apple Online Store, you can configure your iMac with an even more"
" powerful Intel Core i7 processor, up to 3.5GHz."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "And because it’s so easy to use, it’s easy to love."
msgstr "And because it’s so easy to use, it’s easy to love."

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Apple In-Ear Headphones"
msgstr "Apple In-Ear Headphones"

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Apple Wireless Keyboard"
msgstr "Apple Wireless Keyboard"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr "Primjenjivo na"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_applied_on
msgid "Apply On"
msgstr "Primjeni na"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Archived"
msgstr "Arhivirani"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_sequence
msgid "Assigns the priority to the list of product vendor."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_attribute_id
msgid "Attribute"
msgstr "Atribut"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_line_form
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Name"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_extra
msgid "Attribute Price Extra"
msgstr "Dodatak cijeni za atribut"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_ids
msgid "Attribute Prices"
msgstr "Cijene atributa"

#. module: product
#: model:ir.actions.act_window,name:product.variants_action
#: model:ir.model.fields,field_description:product.field_product_attribute_line_value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Attribute Values"
msgstr "Vrijednosti atributa"

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model:ir.model.fields,field_description:product.field_product_product_attribute_value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Attributes"
msgstr "Atributi"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_product_variant
msgid "Attributes and Variants"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary input for portable devices"
msgstr "Auxiliary input for portable devices"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Auxiliary port lets you connect other audio sources, like an MP3 player"
msgstr ""
"Auxiliary port lets you connect other audio sources, like an MP3 player"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_barcode
#: model:ir.model.fields,field_description:product.field_product_product_barcode
#: model:ir.model.fields,field_description:product.field_product_template_barcode
msgid "Barcode"
msgstr "Barkod"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_barcode
msgid "Barcode used for packaging identification."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_base
msgid ""
"Base price for computation.\n"
"Public Price: The base price will be the Sale/public Price.\n"
"Cost Price : The base price will be the cost price.\n"
"Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_list_price
#: model:ir.model.fields,help:product.field_product_template_list_price
#: model:ir.model.fields,help:product.field_product_template_lst_price
msgid ""
"Base price to compute the customer price. Sometimes called the catalog "
"price."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base
msgid "Based on"
msgstr "Bazirano na"

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Basic Computer"
msgstr "Basic Computer"

#. module: product
#: model:product.product,name:product.membership_2
#: model:product.template,name:product.membership_2_product_template
msgid "Basic Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Beautiful 7.9‑inch display."
msgstr "Beautiful 7.9‑inch display."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Beautiful widescreen display."
msgstr "Beautiful widescreen display."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image
msgid "Big-sized image"
msgstr "Velika slika"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor_inv
msgid "Bigger Ratio"
msgstr ""

#. module: product
#: selection:product.uom,uom_type:0
msgid "Bigger than the reference Unit of Measure"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr "Crna"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bluetooth connectivity"
msgstr "Bluetooth connectivity"

#. module: product
#: model:product.product,name:product.product_product_5b
#: model:product.template,name:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker"
msgstr "Bose Mini Bluetooth Speaker"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker."
msgstr "Bose Mini Bluetooth Speaker."

#. module: product
#: model:product.product,description_sale:product.product_product_5b
#: model:product.template,description_sale:product.product_product_5b_product_template
msgid "Bose's smallest portable Bluetooth speaker"
msgstr "Bose's smallest portable Bluetooth speaker"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Brilliance onscreen. And behind it."
msgstr "Brilliance onscreen. And behind it."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano without gorgeous, hard-to-choose-from color."
msgstr ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano without gorgeous, hard-to-choose-from color."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr "Izračunaj cijenu proizvoda po komadu baziranu na verziji cjenovnika"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_purchase_ok
#: model:ir.model.fields,field_description:product.field_product_template_purchase_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Purchased"
msgstr "Može biti naručen"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_rental
#: model:ir.model.fields,field_description:product.field_product_template_rental
msgid "Can be Rent"
msgstr "Moze se iznajmiti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_sale_ok
#: model:ir.model.fields,field_description:product.field_product_template_sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Moze se prodati"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "Odustani"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_category_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Category"
msgstr "Категорија"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr "Naziv kategorije"

#. module: product
#: code:addons/product/models/product_pricelist.py:435
#, python-format
msgid "Category: %s"
msgstr "Kategorija: %s"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Characteristics"
msgstr "Karakteristike"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Charges iPod/iPhone"
msgstr "Charges iPod/iPhone"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Charging cradle recharges the battery and serves as a convenient\n"
"                                    home base for your speaker, and it lets you play while it charges."
msgstr ""
"Charging cradle recharges the battery and serves as a convenient\n"
"                                    home base for your speaker, and it lets you play while it charges."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_create_variant
msgid "Check this if you want to create multiple variants for this attribute."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_child_id
msgid "Child Categories"
msgstr "Podkategorije"

#. module: product
#: model:product.product,name:product.product_product_11c
#: model:product.template,name:product.product_product_11c_product_template
msgid "Cleaning"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid "Click to add a new unit of measure category."
msgstr "Klikni da dodaš novu kategoriju jedinice mjere."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid "Click to add a new unit of measure."
msgstr "Klikni da dodaš novu jedinicu mjere"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Click to create a pricelist."
msgstr "Klikni da dodaš novi cjenovnik."

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_all
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Click to define a new product."
msgstr "Klikni da dodaš novi proizvod."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Kodovi"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr "Boja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_color
#: model:ir.model.fields,field_description:product.field_product_template_color
msgid "Color Index"
msgstr "Indeks boje"

#. module: product
#: model:product.product,description_sale:product.product_product_6
#: model:product.template,description_sale:product.product_product_6_product_template
msgid ""
"Color: White\n"
"Capacity: 16GB\n"
"Connectivity: Wifi\n"
"Beautiful 7.9-inch display\n"
"Over 375,000 apps\n"
"Ultrafast wireless\n"
"iOS7\n"
"            "
msgstr ""
"Color: White\n"
"Capacity: 16GB\n"
"Connectivity: Wifi\n"
"Beautiful 7.9-inch display\n"
"Over 375,000 apps\n"
"Ultrafast wireless\n"
"iOS7\n"
"            "

#. module: product
#: model_terms:ir.ui.view,arch_db:product.res_config_settings_view_form
msgid "Common Product Catalog"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_company_id
#: model:ir.model.fields,field_description:product.field_product_product_company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_company_id
#: model:ir.model.fields,field_description:product.field_product_template_company_id
msgid "Company"
msgstr "Preduzeće"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_complete_name
msgid "Complete Name"
msgstr "Puno Ime"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_compute_price
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr "Izračunaj cijenu"

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Computer Case"
msgstr "Computer Case"

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Computer SC234"
msgstr "Computer SC234"

#. module: product
#: code:addons/product/models/product_template.py:50
#: selection:product.template,type:0
#, python-format
msgid "Consumable"
msgstr "Potrošni"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: product
#: code:addons/product/models/product_uom.py:98
#, python-format
msgid ""
"Conversion from Product UoM %s to Default UoM %s is not possible as they "
"both belong to different Category!."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_cost
#: model:ir.model.fields,field_description:product.field_product_product_standard_price
#: model:ir.model.fields,field_description:product.field_product_template_standard_price
#: selection:product.pricelist.item,base:0
msgid "Cost"
msgstr "Cijena koštanja"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_standard_price
msgid ""
"Cost used for stock valuation in standard price and as a first price to set "
"in average/fifo. Also used as a base price for pricelists. Expressed in the "
"default unit of measure of the product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_standard_price
msgid ""
"Cost used for stock valuation in standard price and as a first price to set "
"in average/fifo. Also used as a base price for pricelists. Expressed in the "
"default unit of measure of the product. "
msgstr ""

#. module: product
#: model:product.product,name:product.service_delivery
#: model:product.template,name:product.service_delivery_product_template
msgid "Cost-plus Contract"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_country_group
msgid "Country Group"
msgstr "Regija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_country_group_ids
msgid "Country Groups"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_variant
msgid "Create Variant"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_uid
#: model:ir.model.fields,field_description:product.field_product_category_create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_uid
#: model:ir.model.fields,field_description:product.field_product_product_create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_uid
#: model:ir.model.fields,field_description:product.field_product_template_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_date
#: model:ir.model.fields,field_description:product.field_product_category_create_date
#: model:ir.model.fields,field_description:product.field_product_packaging_create_date
#: model:ir.model.fields,field_description:product.field_product_price_history_create_date
#: model:ir.model.fields,field_description:product.field_product_price_list_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_date
#: model:ir.model.fields,field_description:product.field_product_product_create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_date
#: model:ir.model.fields,field_description:product.field_product_template_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything"
" about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."
msgstr ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything"
" about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_currency_id
#: model:ir.model.fields,field_description:product.field_product_product_currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_currency_id
#: model:ir.model.fields,field_description:product.field_product_template_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Custom Computer (kit)"
msgstr "Custom Computer (kit)"

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Custom Laptop based on customer's requirement."
msgstr "Custom Laptop based on customer's requirement."

#. module: product
#: model:product.product,description:product.product_product_5
#: model:product.template,description:product.product_product_5_product_template
msgid "Custom computer shipped in kit."
msgstr "Custom computer shipped in kit."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_partner_ref
msgid "Customer Ref"
msgstr ""

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Datacard"
msgstr "Datacard"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_datetime
msgid "Date"
msgstr "Datum"

#. module: product
#: model:product.uom,name:product.product_uom_day
msgid "Day(s)"
msgstr "Dan(a)"

#. module: product
#: code:addons/product/models/res_company.py:59
#, python-format
msgid "Default %(currency)s pricelist for %(company)s"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_id
#: model:ir.model.fields,help:product.field_product_template_uom_id
msgid "Default Unit of Measure used for all stock operation."
msgstr ""
"Podrezumjevana jedinica mjere koja se koristi za sve operacije na zalihama."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_po_id
#: model:ir.model.fields,help:product.field_product_template_uom_po_id
msgid ""
"Default Unit of Measure used for purchase orders. It must be in the same "
"category than the default unit of measure."
msgstr ""
"Podrazumjevana jedinica mjere kod narudžbi. Mora biti iste kategorije kao i "
"podrazumjevana jedinica mjere."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_delay
msgid "Delivery Lead Time"
msgstr "Vreme trajanja isporuke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description
#: model:ir.model.fields,field_description:product.field_product_template_description
msgid "Description"
msgstr "Opis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Customers"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Design. The thinnest iPod ever."
msgstr "Design. The thinnest iPod ever."

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_sequence
#: model:ir.model.fields,help:product.field_product_attribute_value_sequence
msgid "Determine the display order"
msgstr "Odredi redoslijed prikaza"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_line_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_price_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value_display_name
#: model:ir.model.fields,field_description:product.field_product_category_display_name
#: model:ir.model.fields,field_description:product.field_product_packaging_display_name
#: model:ir.model.fields,field_description:product.field_product_price_history_display_name
#: model:ir.model.fields,field_description:product.field_product_price_list_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_display_name
#: model:ir.model.fields,field_description:product.field_product_product_display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_display_name
#: model:ir.model.fields,field_description:product.field_product_template_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: product
#: model:product.uom,name:product.product_uom_dozen
msgid "Dozen(s)"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid ""
"Dvorak keyboard\n"
"left-handed mouse"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"Each rule include a set of applicability criteria (date range,\n"
"                product category...) and a computation that easily helps to achieve\n"
"                any kind of pricing."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Efficient, high-quality audio"
msgstr "Efficient, high-quality audio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_end
msgid "End Date"
msgstr "Završni datum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_end
msgid "End date for this vendor price"
msgstr "Datum do kad važi ova cijena dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_end
msgid "Ending valid for the pricelist item validation"
msgstr ""

#. module: product
#: code:addons/product/models/product.py:53
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr ""

#. module: product
#: code:addons/product/models/product_attribute.py:101
#, python-format
msgid "Error ! You cannot use this attribute with the following value."
msgstr ""

#. module: product
#: code:addons/product/models/product.py:283
#, python-format
msgid ""
"Error! It is not allowed to choose more than one value for a given "
"attribute."
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:427
#, python-format
msgid "Error! The minimum margin should be lower than the maximum margin."
msgstr ""

#. module: product
#: code:addons/product/models/product_pricelist.py:421
#, python-format
msgid ""
"Error! You cannot assign the Main Pricelist as Other Pricelist in PriceList "
"Item!"
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:274
#, python-format
msgid ""
"Error: The default Unit of Measure and the purchase Unit of Measure must be "
"in the same category."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an experience every bit as big as iPad."
msgstr ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an experience every bit as big as iPad."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, FaceTime and\n"
"                                    iSight cameras, thousands of amazing apps, 10-hour\n"
"                                    battery life* — is everything you’ll love about\n"
"                                    iPad mini, too. And you can hold it in one hand."
msgstr ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, FaceTime and\n"
"                                    iSight cameras, thousands of amazing apps, 10-hour\n"
"                                    battery life* — is everything you’ll love about\n"
"                                    iPad mini, too. And you can hold it in one hand."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. And you can hold it in one hand."
msgstr ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. And you can hold it in one hand."

#. module: product
#: model:product.product,description:product.product_product_2
#: model:product.template,description:product.product_product_2_product_template
msgid "Example of product to invoice based on delivery."
msgstr ""

#. module: product
#: model:product.product,description:product.service_order_01
#: model:product.template,description:product.service_order_01_product_template
msgid "Example of product to invoice on order."
msgstr ""

#. module: product
#: model:product.product,description:product.service_cost_01
#: model:product.template,description:product.service_cost_01_product_template
msgid "Example of products to invoice based on cost."
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_1
#: model:product.template,description:product.product_product_1_product_template
msgid "Example of products to invoice based on delivery."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_name
#: model:ir.model.fields,help:product.field_product_pricelist_item_price
msgid "Explicit rule name for this pricelist line."
msgstr "Eksplicitno ime pravila za ovu stavku cjenovnika."

#. module: product
#: model:product.product,name:product.service_cost_01
#: model:product.template,name:product.service_cost_01_product_template
msgid "External Audit"
msgstr "External Audit"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Fast connections.The world over."
msgstr "Fast connections.The world over."

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Fix Price"
msgstr "Fiksna cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_fixed_price
msgid "Fixed Price"
msgstr "Fiksna cijena"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Formula"
msgstr "Formula"

#. module: product
#: model:product.product,description_sale:product.product_product_7
#: model:product.template,description_sale:product.product_product_7_product_template
msgid ""
"Frequency: 5Hz to 21kHz\n"
"Impedance: 23 ohms\n"
"Sensitivity: 109 dB SPL/mW\n"
"Drivers: two-way balanced armature\n"
"Cable length: 1065 mm\n"
"Weight: 10.2 grams\n"
"            "
msgstr ""
"Frequency: 5Hz to 21kHz\n"
"Impedance: 23 ohms\n"
"Sensitivity: 109 dB SPL/mW\n"
"Drivers: two-way balanced armature\n"
"Cable length: 1065 mm\n"
"Weight: 10.2 grams\n"
"            "

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Friendly to the environment."
msgstr "Friendly to the environment."

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "GAP Analysis Service"
msgstr "GAP Analysis Service"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Opšti podaci"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Genius. Your own personal DJ."
msgstr "Genius. Your own personal DJ."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_packaging_ids
#: model:ir.model.fields,help:product.field_product_template_packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sequence
#: model:ir.model.fields,help:product.field_product_template_sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Daje redosled sekvenci pri prikazu liste proizvoda."

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid "Global"
msgstr "Globalna"

#. module: product
#: code:addons/product/models/product_template.py:30
#, python-format
msgid "Go to Internal Categories"
msgstr ""

#. module: product
#: model:product.product,name:product.membership_0
#: model:product.template,name:product.membership_0_product_template
msgid "Gold Membership"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Graphics Card"
msgstr "Graphics Card"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Group By"
msgstr "Grupiši po"

#. module: product
#: model:product.product,name:product.product_product_17
#: model:product.template,name:product.product_product_17_product_template
msgid "HDD SH-1"
msgstr "HDD SH-1"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Have Genius call the tunes."
msgstr "Have Genius call the tunes."

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Hear, hear."
msgstr "Hear, hear."

#. module: product
#: model:product.product,description_sale:product.product_product_11
#: model:product.product,description_sale:product.product_product_11b
#: model:product.template,description_sale:product.product_product_11_product_template
#: model:product.template,description_sale:product.product_product_11b_product_template
msgid ""
"Height: 76.5 mm\n"
"Width:  39.6 mm\n"
"Depth:  5.4 mm\n"
"Weight: 31 grams"
msgstr ""
"Height: 76.5 mm\n"
"Width:  39.6 mm\n"
"Depth:  5.4 mm\n"
"Weight: 31 grams"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Highly rated designs."
msgstr "Highly rated designs."

#. module: product
#: model:product.uom,name:product.product_uom_hour
msgid "Hour(s)"
msgstr "Sat(i)"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."
msgstr ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category: 1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category: 1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "How to get your groove on."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_line_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_category_id
#: model:ir.model.fields,field_description:product.field_product_packaging_id
#: model:ir.model.fields,field_description:product.field_product_price_history_id
#: model:ir.model.fields,field_description:product.field_product_price_list_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_id
#: model:ir.model.fields,field_description:product.field_product_product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_id
#: model:ir.model.fields,field_description:product.field_product_template_id
#: model:ir.model.fields,field_description:product.field_product_uom_categ_id
#: model:ir.model.fields,field_description:product.field_product_uom_id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_id
msgid "ID"
msgstr "ID"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "If it's made for iPad, it's made for iPad mini."
msgstr "If it's made for iPad, it's made for iPad mini."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_id
msgid ""
"If not set, the vendor price will apply to all variants of this products."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_active
#: model:ir.model.fields,help:product.field_product_template_active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_image
msgid "Image"
msgstr "Slika"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_medium
msgid ""
"Image of the product variant (Medium-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_small
msgid ""
"Image of the product variant (Small-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Individually calibrated for true-to-life color."
msgstr "Individually calibrated for true-to-life color."

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Information about a product vendor"
msgstr "Informacije o dobavljaču proizvoda"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Inside each earpiece is a stainless steel mesh cap that protects the precision acoustic\n"
"                                    components from dust and debris. You can remove the caps for cleaning or replace\n"
"                                    them with an extra set that’s included in the box."
msgstr ""
"Inside each earpiece is a stainless steel mesh cap that protects the precision acoustic\n"
"                                    components from dust and debris. You can remove the caps for cleaning or replace\n"
"                                    them with an extra set that’s included in the box."

#. module: product
#: model:product.category,name:product.product_category_2
msgid "Internal"
msgstr "Interni"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_categ_id
#: model:ir.model.fields,field_description:product.field_product_template_categ_id
msgid "Internal Category"
msgstr "Interna kategorija"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_default_code
#: model:ir.model.fields,field_description:product.field_product_template_default_code
msgid "Internal Reference"
msgstr "Interna oznaka"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_barcode
#: model:ir.model.fields,help:product.field_product_template_barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Skladište"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Keep it clean."
msgstr "Keep it clean."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Key Features"
msgstr "Key Features"

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Laptop Customized"
msgstr "Laptop Customized"

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Laptop E5023"
msgstr "Laptop E5023"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_line___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_price___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value___last_update
#: model:ir.model.fields,field_description:product.field_product_category___last_update
#: model:ir.model.fields,field_description:product.field_product_packaging___last_update
#: model:ir.model.fields,field_description:product.field_product_price_history___last_update
#: model:ir.model.fields,field_description:product.field_product_price_list___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item___last_update
#: model:ir.model.fields,field_description:product.field_product_product___last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo___last_update
#: model:ir.model.fields,field_description:product.field_product_template___last_update
#: model:ir.model.fields,field_description:product.field_product_uom___last_update
#: model:ir.model.fields,field_description:product.field_product_uom_categ___last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_write_uid
#: model:ir.model.fields,field_description:product.field_product_category_write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_uid
#: model:ir.model.fields,field_description:product.field_product_product_write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_uid
#: model:ir.model.fields,field_description:product.field_product_template_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_write_date
#: model:ir.model.fields,field_description:product.field_product_category_write_date
#: model:ir.model.fields,field_description:product.field_product_packaging_write_date
#: model:ir.model.fields,field_description:product.field_product_price_history_write_date
#: model:ir.model.fields,field_description:product.field_product_price_list_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_date
#: model:ir.model.fields,field_description:product.field_product_product_write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_date
#: model:ir.model.fields,field_description:product.field_product_template_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_left
msgid "Left Parent"
msgstr "Lijevi roditelj"

#. module: product
#: model:product.uom.categ,name:product.uom_categ_length
msgid "Length / Distance"
msgstr "Duzina / Udaljenost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_attribute_line_ids
msgid "Lines"
msgstr "Stavke"

#. module: product
#: model:product.uom,name:product.product_uom_litre
msgid "Liter(s)"
msgstr "Litar(a)"

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Little server"
msgstr "Little server"

#. module: product
#: model:res.groups,name:product.group_uom
msgid "Manage Multiple Units of Measure"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_pricelist_item
msgid "Manage Pricelist Items"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr "Maks. marža"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_max_margin
msgid "Max. Price Margin"
msgstr "Maks. marža cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_medium
#: model:ir.model.fields,field_description:product.field_product_template_image_medium
msgid "Medium-sized image"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_medium
msgid ""
"Medium-sized image of the product. It is automatically resized as a "
"128x128px image, with aspect ratio preserved, only when the image exceeds "
"one of those sizes. Use this field in form views or some kanban views."
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Memory"
msgstr "Memory"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr "Min. marža"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_min_margin
msgid "Min. Price Margin"
msgstr "Min. marža cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_min_quantity
msgid "Min. Quantity"
msgstr "Min. količina"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_min_qty
msgid "Minimal Quantity"
msgstr "Minimalna količina"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "More energy efficient."
msgstr "More energy efficient."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "More features."
msgstr "More features."

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Motherboard I9P57"
msgstr "Motherboard I9P57"

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Mouse, Optical"
msgstr "Mouse, Optical"

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Mouse, Wireless"
msgstr "Mouse, Wireless"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Music. It's what beats inside."
msgstr "Music. It's what beats inside."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_name
#: model:ir.model.fields,field_description:product.field_product_category_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_name
#: model:ir.model.fields,field_description:product.field_product_product_name
#: model:ir.model.fields,field_description:product.field_product_template_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_name
msgid "Name"
msgstr "Naziv"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr "Nova cijena ="

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Notes"
msgstr "Zabilješke"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr "Ostale informacije"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base_pricelist_id
#: selection:product.pricelist.item,base:0
msgid "Other Pricelist"
msgstr "Drugi cjenovnik"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Over 375,000 apps."
msgstr "Over 375,000 apps."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_name
msgid "Package Type"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Packaging"
msgstr "Pakovanje"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_id
msgid "Parent Category"
msgstr "Roditeljska kategorija"

#. module: product
#: model:product.product,name:product.product_product_5c
#: model:product.template,name:product.product_product_5c_product_template
msgid "Parts Replacement"
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Percentage (discount)"
msgstr "Procenat (popust)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_percent_price
msgid "Percentage Price"
msgstr "Cijena u procentima"

#. module: product
#: model:product.category,name:product.product_category_5
msgid "Physical"
msgstr "Physical"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Playlists. The perfect mix for every mood."
msgstr "Playlists. The perfect mix for every mood."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Plays where you play"
msgstr "Plays where you play"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest"
" yet. Every model in the lineup comes standard with a quad-core Intel Core "
"i5 processor, starting at 2.7GHz and topping out at 3.4GHz."
msgstr ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest"
" yet. Every model in the lineup comes standard with a quad-core Intel Core "
"i5 processor, starting at 2.7GHz and topping out at 3.4GHz."

#. module: product
#: model:product.product,name:product.service_order_01
#: model:product.template,name:product.service_order_01_product_template
msgid "Prepaid Consulting"
msgstr "Prepaid Consulting"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price
#: model:ir.model.fields,field_description:product.field_product_product_price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_price
#: model:ir.model.fields,field_description:product.field_product_template_price
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Price"
msgstr "Cijena"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr "Računanje cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_discount
msgid "Price Discount"
msgstr "Popust"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_price_extra
msgid "Price Extra"
msgstr "Dodatak cijeni"

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value_price_extra
msgid ""
"Price Extra: Extra price for the variant with this attribute value on sale "
"price. eg. 200 price extra, 1000 + 200 = 1200."
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model:ir.model,name:product.model_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr "Cjenovnik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_round
msgid "Price Rounding"
msgstr "Zaokruživanje cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_surcharge
msgid "Price Surcharge"
msgstr "Cijena doplate"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr "Cijena:"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_price_list
msgid "PriceList"
msgstr "Cjenovnik"

#. module: product
#: model:ir.actions.report,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template_pricelist_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist"
msgstr "Cjenovnik"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_pricelist_item_ids
msgid "Pricelist Item"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_ids
#: model:ir.model.fields,field_description:product.field_product_product_item_ids
#: model:ir.model.fields,field_description:product.field_product_template_item_ids
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist Items"
msgstr "Stavke cjenovnika"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_name
msgid "Pricelist Name"
msgstr "Naziv cjenovnika"

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
msgid "Pricelist item"
msgstr "Artikal cjenovnika"

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.model.fields,field_description:product.field_res_country_group_pricelist_ids
msgid "Pricelists"
msgstr "Cjenovnici"

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Pricelists On Product"
msgstr "Cjenovnici na proizvodu"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr "Cjenovnici su primjenjeni na"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Odredjivanje cijene"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Štampaj"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Processor Core i5 2.70 Ghz"
msgstr "Processor Core i5 2.70 Ghz"

#. module: product
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging_product_id
#: model:ir.model.fields,field_description:product.field_product_price_history_product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_id
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_id
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_id
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: selection:product.pricelist.item,applied_on:0
#: model:res.request.link,name:product.req_link_product
msgid "Product"
msgstr "Proizvod"

#. module: product
#: model:ir.model,name:product.model_product_attribute
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Product Attribute"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_value_id
msgid "Product Attribute Value"
msgstr "Vrijednost atributa proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_attribute_line_form
msgid "Product Attribute and Values"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line_ids
msgid "Product Attributes"
msgstr "Atributi proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_categ_id
msgid "Product Category"
msgstr "Grupa proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr "Naziv proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
#: model:ir.model.fields,field_description:product.field_product_product_packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template_packaging_ids
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_stock_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
msgid "Product Packages"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
msgid "Product Packaging"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_attribute_line_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_type
#: model:ir.model.fields,field_description:product.field_product_template_type
msgid "Product Type"
msgstr "Tip proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_uom
msgid "Product Unit of Measure"
msgstr "JM proizvoda"

#. module: product
#: model:ir.model,name:product.model_product_uom_categ
msgid "Product UoM Categories"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_id
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product Variant"
msgstr "Varijanta proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_all
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products"
msgstr "Proizvodi"

#. module: product
#: model:ir.actions.report,name:product.report_product_label
#: model:ir.actions.report,name:product.report_product_template_label
msgid "Products Labels"
msgstr "Natpisi proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr "Cijene proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr "Cjenovnik proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr "Pretrazi cijene proizvoda"

#. module: product
#: code:addons/product/models/product.py:448
#, python-format
msgid "Products: "
msgstr "Proizvodi: "

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_lst_price
#: selection:product.pricelist.item,base:0
msgid "Public Price"
msgstr "Javna cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_purchase
#: model:ir.model.fields,field_description:product.field_product_template_description_purchase
msgid "Purchase Description"
msgstr "Opis za nabavku"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Jedinica mjere kod nabavke"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_qty
msgid "Quantity per Package"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty1
msgid "Quantity-1"
msgstr "Količina-1"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty2
msgid "Quantity-2"
msgstr "Količina-2"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty3
msgid "Quantity-3"
msgstr "Količina-3"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty4
msgid "Quantity-4"
msgstr "Količina-4"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty5
msgid "Quantity-5"
msgstr "Količina-5"

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "RAM SR5"
msgstr "RAM SR5"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor
msgid "Ratio"
msgstr "Odnos"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_code
msgid "Reference"
msgstr "Šifra"

#. module: product
#: selection:product.uom,uom_type:0
msgid "Reference Unit of Measure for this category"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Remote control for power, volume, track seek"
msgstr "Remote control for power, volume, track seek"

#. module: product
#: model:product.product,name:product.product_product_6c
#: model:product.template,name:product.product_product_6c_product_template
msgid "Repair"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_right
msgid "Right Parent"
msgstr "Desni roditelj"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that let you do almost anything\n"
"                                    you can imagine. And with automatic updates,\n"
"                                    you're always getting the best experience possible."
msgstr ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that let you do almost anything\n"
"                                    you can imagine. And with automatic updates,\n"
"                                    you're always getting the best experience possible."

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and light yet\n"
"                                    full-featured. It can do just about everything and\n"
"                                    be just about anything."
msgstr ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and light yet\n"
"                                    full-featured. It can do just about everything and\n"
"                                    be just about anything."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light yet full-\n"
"                                   featured. It can do just about everything and be just\n"
"                                   about anything.And because it’s so easy to use, it’s\n"
"                                   easy to love."
msgstr ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light yet full-\n"
"                                   featured. It can do just about everything and be just\n"
"                                   about anything.And because it’s so easy to use, it’s\n"
"                                   easy to love."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr "Način zaokruživanja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_rounding
msgid "Rounding Precision"
msgstr "Preciznost zaokruživanja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_sale
#: model:ir.model.fields,field_description:product.field_product_template_description_sale
msgid "Sale Description"
msgstr "Opis za prodaju"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_lst_price
msgid "Sale Price"
msgstr "Prodajna cijena"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner_property_product_pricelist
#: model:ir.model.fields,field_description:product.field_res_users_property_product_pricelist
msgid "Sale Pricelist"
msgstr "Prodajni ckenovnik"

#. module: product
#: model:product.category,name:product.product_category_1
msgid "Saleable"
msgstr "Za prodaju"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Prodaja"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_list_price
#: model:ir.model.fields,field_description:product.field_product_template_list_price
msgid "Sales Price"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_sale_pricelist
msgid "Sales Pricelists"
msgstr "Prodajni cjenovnik"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. For more song combinations\n"
"                                        you wouldn’t have thought of yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard in a while — or find music you forgot you even had."
msgstr ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. For more song combinations\n"
"                                        you wouldn’t have thought of yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard in a while — or find music you forgot you even had."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_categ_id
#: model:ir.model.fields,help:product.field_product_template_categ_id
msgid "Select category for the current product"
msgstr "Odaberi kategoriju datog proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value_sequence
#: model:ir.model.fields,field_description:product.field_product_packaging_sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist_sequence
#: model:ir.model.fields,field_description:product.field_product_product_sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_sequence
#: model:ir.model.fields,field_description:product.field_product_template_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Server"
msgstr "Server"

#. module: product
#: code:addons/product/models/product_template.py:51
#: selection:product.template,type:0
#, python-format
msgid "Service"
msgstr "Servis"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model:product.category,name:product.product_category_3
msgid "Services"
msgstr "Usluge"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""
"Postavlja cenu tako da je ona višestruka vrednost ove vrednosti.\n"
"Zaokruženje se primenjuje nakon popusta a pre poskupljenja.\n"
"Ako hoćete da Vam se cena završava sa 9.99, postavite zaokruženje 10, dodatak -0.01"

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings_company_share_product
msgid "Share product to all companies"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_res_config_settings_company_share_product
msgid ""
"Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company is defined). Product not related to a company are visible for all companies."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_product_pricelist
msgid "Show pricelists On Products"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_pricelist_item
msgid "Show pricelists to customers"
msgstr ""

#. module: product
#: model:product.product,name:product.membership_1
#: model:product.template,name:product.membership_1_product_template
msgid "Silver Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Sleek, compact design"
msgstr "Sleek, compact design"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_small
#: model:ir.model.fields,field_description:product.field_product_template_image_small
msgid "Small-sized image"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_small
msgid ""
"Small-sized image of the product. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""

#. module: product
#: selection:product.uom,uom_type:0
msgid "Smaller than the reference Unit of Measure"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Soft covers are available separately in blue, green or orange. Pick a color "
"to match your style."
msgstr ""
"Soft covers are available separately in blue, green or orange. Pick a color "
"to match your style."

#. module: product
#: model:product.category,name:product.product_category_4
msgid "Software"
msgstr "Software"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sale_ok
#: model:ir.model.fields,help:product.field_product_template_sale_ok
msgid "Specify if the product can be selected in a sales order line."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_start
msgid "Start Date"
msgstr "Početni datum"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_start
msgid "Start date for this vendor price"
msgstr "Datum od koga počinje da važi ova cijena dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_start
msgid "Starting date for the pricelist item validation"
msgstr ""

#. module: product
#: selection:product.template,type:0
msgid "Stockable Product"
msgstr "Zalihe proizvoda"

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Support Contract (on timesheet)"
msgstr ""

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Switch, 24 ports"
msgstr "Switch, 24 ports"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Sync to your heart’s content."
msgstr "Sync to your heart’s content."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano a shake and it shuffles to a different song in your music library."
msgstr ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano a shake and it shuffles to a different song in your music library."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_pricelist_id
#: model:ir.model.fields,help:product.field_product_template_pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"The Apple In-Ear Headphones deliver a truly immersive sound experience by drastically\n"
"                                    reducing unwanted outside noises. The soft, silicone ear tips fit snugly and comfortably\n"
"                                    in your ear, creating a seal that isolates your music from your surroundings.\n"
"                                    Three different sizes of ear tips are included so you can find a perfect fit for each ear.\n"
"                                    Also included are a convenient carrying case for the ear tips and a cable-control case\n"
"                                    for the headphones themselves."
msgstr ""
"The Apple In-Ear Headphones deliver a truly immersive sound experience by drastically\n"
"                                    reducing unwanted outside noises. The soft, silicone ear tips fit snugly and comfortably\n"
"                                    in your ear, creating a seal that isolates your music from your surroundings.\n"
"                                    Three different sizes of ear tips are included so you can find a perfect fit for each ear.\n"
"                                    Also included are a convenient carrying case for the ear tips and a cable-control case\n"
"                                    for the headphones themselves."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The Bose® SoundLink® mini is Bose's smallest portable Bluetooth speaker. Its ultra-compact size fits in the \n"
"                                    palm of your hand, yet gives you full, natural sound wirelessly from your iPhone, iPad, or iPod. Grab it and go \n"
"                                    full-featured. It can do just about everything and\n"
"                                    experience music just about anywhere."
msgstr ""
"The Bose® SoundLink® mini is Bose's smallest portable Bluetooth speaker. Its ultra-compact size fits in the \n"
"                                    palm of your hand, yet gives you full, natural sound wirelessly from your iPhone, iPad, or iPod. Grab it and go \n"
"                                    full-featured. It can do just about everything and\n"
"                                    experience music just about anywhere."

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The SoundLink® Mini speaker is small and light enough\n"
"                                        to tuck into your bag. It weighs in at just 1.5 pounds.\n"
"                                        Its low profile lets you place it almost anywhere and\n"
"                                        provides a low center of gravity that makes it nearly\n"
"                                        impossible to tip over."
msgstr ""
"The SoundLink® Mini speaker is small and light enough\n"
"                                        to tuck into your bag. It weighs in at just 1.5 pounds.\n"
"                                        Its low profile lets you place it almost anywhere and\n"
"                                        provides a low center of gravity that makes it nearly\n"
"                                        impossible to tip over."

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""
"Izračunata cijena je izražena u podrazumjevanoj jedinici mjere za proizvod."

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""

#. module: product
#: sql_constraint:product.uom:0
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "The desktop. In its most advanced form ever"
msgstr "The desktop. In its most advanced form ever"

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_sequence
msgid "The first in the sequence is the default one."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "The full iPad experience."
msgstr "The full iPad experience."

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And you’re free to type wherever\n"
"                                    you like — with the keyboard in front of your iPad or on your lap."
msgstr ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And you’re free to type wherever\n"
"                                    you like — with the keyboard in front of your iPad or on your lap."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_min_qty
msgid ""
"The minimal quantity to purchase from this vendor, expressed in the vendor "
"Product Unit of Measure if not any, in the default unit of measure of the "
"product otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_category_product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""

#. module: product
#: code:addons/product/models/product_attribute.py:74
#, python-format
msgid ""
"The operation cannot be completed:\n"
"You are trying to delete an attribute value with a reference on a product variant."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_price
msgid "The price to purchase a product"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"The product form contains information to simplify the sale\n"
"                process: price, notes in the quotation, accounting data,\n"
"                procurement methods, etc."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"The product form contains information to simplify the sale process: price, "
"notes in the quotation, accounting data, procurement methods, etc."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The rechargeable lithium-ion battery delivers up to seven hours of playtime.\n"
"                                    And at home, you can listen even longer—the charging cradle lets\n"
"                                    you listen while it charges."
msgstr ""
"The rechargeable lithium-ion battery delivers up to seven hours of playtime.\n"
"                                    And at home, you can listen even longer—the charging cradle lets\n"
"                                    you listen while it charges."

#. module: product
#: sql_constraint:product.uom:0
msgid "The rounding precision must be greater than 0!"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Variant "
"Prices' button to set the extra attribute prices."
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_9
#: model:product.template,description_sale:product.product_product_9_product_template
msgid ""
"The sleek aluminium Apple Wireless Keyboard.\n"
"            "
msgstr ""
"The sleek aluminium Apple Wireless Keyboard.\n"
"            "

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The speaker has a range of about 30 feet, so you can enjoy\n"
"                                    the sound you want without wires. It pairs easily with your\n"
"                                    smartphone, iPad® or other Bluetooth device.\n"
"                                    And it remembers the most recent six devices you've used,\n"
"                                    so reconnecting is even simpler."
msgstr ""
"The speaker has a range of about 30 feet, so you can enjoy\n"
"                                    the sound you want without wires. It pairs easily with your\n"
"                                    smartphone, iPad® or other Bluetooth device.\n"
"                                    And it remembers the most recent six devices you've used,\n"
"                                    so reconnecting is even simpler."

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_qty
msgid "The total number of products you can have per pallet or box."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_volume
#: model:ir.model.fields,help:product.field_product_template_volume
msgid "The volume in m3."
msgstr "Zapremina u m3"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_weight
#: model:ir.model.fields,help:product.field_product_template_weight
msgid "The weight of the contents in Kg, not including any packaging, etc."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "There is less of it, but no less to it."
msgstr "There is less of it, but no less to it."

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "There's less of it, but no less to it."
msgstr "There's less of it, but no less to it."

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"There’s another way to get a good mix of music on iPod: Let Genius do the work.\n"
"                                        Activate Genius in iTunes on your computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover songs you haven’t listened to in forever."
msgstr ""
"There’s another way to get a good mix of music on iPod: Let Genius do the work.\n"
"                                        Activate Genius in iTunes on your computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover songs you haven’t listened to in forever."

#. module: product
#: sql_constraint:product.attribute.value:0
msgid "This attribute value already exists !"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_uom
msgid "This comes from the product form."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_variant
msgid ""
"This field holds the image used as image for the product variant, limited to"
" 1024x1024px."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will show up on sales orders."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_res_partner_property_product_pricelist
#: model:ir.model.fields,help:product.field_res_users_property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Ovaj cenovnik ce se koristiti umesto dosadasnjeg za prodaju datom partneru."

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Two is better than one."
msgstr "Two is better than one."

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_uom_type
msgid "Type"
msgstr "Tip"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"USB port allows for software update to ensure ongoing Bluetooth device "
"compatibility"
msgstr ""
"USB port allows for software update to ensure ongoing Bluetooth device "
"compatibility"

#. module: product
#: model:product.pricelist,name:product.list0
msgid "USD"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Ultrafast wireless."
msgstr "Ultrafast wireless."

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Ultrathin design"
msgstr "Ultrathin design"

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_active
msgid ""
"Uncheck the active field to disable a unit of measure without deleting it."
msgstr ""

#. module: product
#: model:product.uom.categ,name:product.product_uom_categ_unit
msgid "Unit"
msgstr "Jedinica"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_id
#: model:ir.model.fields,field_description:product.field_product_uom_name
msgid "Unit of Measure"
msgstr "Jedinica mere"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Kategorija jedinice mjere"

#. module: product
#: model:product.uom,name:product.product_uom_unit
msgid "Unit(s)"
msgstr "Komad(a)"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_form_action
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_uom
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:product.product_uom_tree_view
msgid "Units of Measure"
msgstr "Jedinice mjere"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_categ_form_view
msgid "Units of Measure categories"
msgstr "Kategorije jedinice mjere"

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"            converted between each others. For example, in the category\n"
"            <i>'Time'</i>, you will have the following units of measure:\n"
"            Hours, Days."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Universal iPod docking station fits most iPod/iPhone models"
msgstr "Universal iPod docking station fits most iPod/iPhone models"

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Unlike many small headphones, each earpiece of the Apple In-Ear Headphones\n"
"                                    contains two separate high-performance drivers — a woofer to handle bass and\n"
"                                    mid-range sounds and a tweeter for high-frequency audio. These dedicated\n"
"                                    drivers help ensure accurate, detailed sound across the entire sonic spectrum.\n"
"                                    The result: you’re immersed in the music and hear details you never knew existed.\n"
"                                    Even when listening to an old favorite, you may feel like you’re hearing it for the first time."
msgstr ""
"Unlike many small headphones, each earpiece of the Apple In-Ear Headphones\n"
"                                    contains two separate high-performance drivers — a woofer to handle bass and\n"
"                                    mid-range sounds and a tweeter for high-frequency audio. These dedicated\n"
"                                    drivers help ensure accurate, detailed sound across the entire sonic spectrum.\n"
"                                    The result: you’re immersed in the music and hear details you never knew existed.\n"
"                                    Even when listening to an old favorite, you may feel like you’re hearing it for the first time."

#. module: product
#: model:ir.model.fields,field_description:product.field_res_config_settings_group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr "Validnost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_name
msgid "Value"
msgstr "Vrijednost"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_ids
#: model_terms:ir.ui.view,arch_db:product.product_attribute_line_form
#: model_terms:ir.ui.view,arch_db:product.product_attribute_view_form
msgid "Values"
msgstr "Vrijednosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_variant_count
msgid "Variant Count"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_variant
msgid "Variant Image"
msgstr "Slika varijante"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr "Informacije"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_price_extra
msgid "Variant Price Extra"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Variant Prices"
msgstr "Cijene"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_variant_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template_variant_seller_ids
msgid "Variant Seller"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:product.variants_tree_view
msgid "Variant Values"
msgstr "Vrijednosti"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr "Varijacije"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_search_view
msgid "Vendor"
msgstr "Dobavljač"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr "Informacije dobavljača"

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Vendor Pricelists"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_code
msgid "Vendor Product Code"
msgstr "Šifra kod dobavljača"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_name
msgid "Vendor Product Name"
msgstr "Naziv kod dobavljača"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_uom
msgid "Vendor Unit of Measure"
msgstr "Jedinica mjere kod dobavljača"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_name
msgid "Vendor of this product"
msgstr "Dobavljač ovog proizvoda"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template_seller_ids
msgid "Vendors"
msgstr "Dobavljači"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_volume
#: model:ir.model.fields,field_description:product.field_product_template_volume
#: model:product.uom.categ,name:product.product_uom_categ_vol
msgid "Volume"
msgstr "Zapremina"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Volume control on main system"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Wall charger can be plugged into the cradle or directly into the speaker"
msgstr ""
"Wall charger can be plugged into the cradle or directly into the speaker"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_weight
#: model:ir.model.fields,field_description:product.field_product_template_weight
#: model:product.uom.categ,name:product.product_uom_categ_kgm
msgid "Weight"
msgstr "Težina"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Weights"
msgstr "Težine"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "When one playlist isn’t enough."
msgstr "When one playlist isn’t enough."

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr "Bijela"

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Why you'll love an iPad."
msgstr "Why you'll love an iPad."

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Wi-Fi"
msgstr "Wi-Fi"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access to fast\n"
"                                   cellular data networks around the world, iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."
msgstr ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access to fast\n"
"                                   cellular data networks around the world, iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."

#. module: product
#: model:product.uom.categ,name:product.uom_categ_wtime
msgid "Working Time"
msgstr "Radno vrijeme"

#. module: product
#: code:addons/product/models/decimal_precision.py:16
#, python-format
msgid ""
"You cannot define the decimal precision of 'Account' as greater than the "
"rounding factor of the company's main currency"
msgstr ""

#. module: product
#: code:addons/product/wizard/product_price_list.py:26
#, python-format
msgid "You have to set a logo or a layout for your company."
msgstr ""

#. module: product
#: code:addons/product/wizard/product_price_list.py:28
#, python-format
msgid "You have to set your reports's header and footer layout."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"            Measure within the same category."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell, whether it's\n"
"                a physical product, a consumable or a service you offer to\n"
"                customers."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell, whether it's a physical "
"product, a consumable or a service you offer to customers."
msgstr ""

#. module: product
#: code:addons/product/models/product_template.py:29
#, python-format
msgid ""
"You must define at least one product category in order to be able to create "
"products."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the gym. Sync those playlists\n"
"                                        to iPod, and you can play the perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells you the name of each playlist,\n"
"                                        so it’s easy to switch between them and find the one you want without looking."
msgstr ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the gym. Sync those playlists\n"
"                                        to iPod, and you can play the perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells you the name of each playlist,\n"
"                                        so it’s easy to switch between them and find the one you want without looking."

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Zed+ Antivirus"
msgstr "Zed+ Antivirus"

#. module: product
#: model:product.uom,name:product.product_uom_cm
msgid "cm"
msgstr "cm"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "days"
msgstr "dana"

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "decimal.precision"
msgstr "decimal.precision"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr "npr. Lampe"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Subscription"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_floz
msgid "fl oz"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_foot
msgid "foot(ft)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_gal
msgid "gal(s)"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "iMac"
msgstr "iMac"

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "iPad Mini"
msgstr "iPad Mini"

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4_product_template
#: model:product.template,name:product.product_product_4b_product_template
#: model:product.template,name:product.product_product_4c_product_template
#: model:product.template,name:product.product_product_4d_product_template
msgid "iPad Retina Display"
msgstr "iPad Retina Display"

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11_product_template
#: model:product.template,name:product.product_product_11b_product_template
msgid "iPod"
msgstr "iPod"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the playlists, audiobooks,\n"
"                                        podcasts, and other audio files you want, then sync."
msgstr ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the playlists, audiobooks,\n"
"                                        podcasts, and other audio files you want, then sync."

#. module: product
#: model:product.uom,name:product.product_uom_inch
msgid "inch(es)"
msgstr "inch(es)"

#. module: product
#: model:product.uom,name:product.product_uom_kgm
msgid "kg"
msgstr "kg"

#. module: product
#: model:product.uom,name:product.product_uom_km
msgid "km"
msgstr "km"

#. module: product
#: model:product.uom,name:product.product_uom_lb
msgid "lb(s)"
msgstr "lb(s)"

#. module: product
#: model:product.uom,name:product.product_uom_mile
msgid "mile(s)"
msgstr "mile(s)"

#. module: product
#: model:product.uom,name:product.product_uom_oz
msgid "oz(s)"
msgstr "oz(s)"

#. module: product
#: model:ir.model,name:product.model_product_attribute_line
msgid "product.attribute.line"
msgstr "product.attribute.line"

#. module: product
#: model:ir.model,name:product.model_product_attribute_price
msgid "product.attribute.price"
msgstr "product.attribute.price"

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
msgid "product.attribute.value"
msgstr "product.attribute.value"

#. module: product
#: model:ir.model,name:product.model_product_price_history
msgid "product.price.history"
msgstr "product.price.history"

#. module: product
#: model:product.uom,name:product.product_uom_qt
msgid "qt"
msgstr "qt"

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid "raid 1, 512ECC ram"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid "raid 10, 2048ECC ram"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "report.product.report_pricelist"
msgstr "report.product.report_pricelist"

#. module: product
#: model:ir.model,name:product.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr "roditeljsko preduzeće"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template"
msgstr "šablon proizvoda"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "do"
