# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fr_facturx_chorus_pro
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-07 11:31+0000\n"
"PO-Revision-Date: 2022-06-07 11:31+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__purchase_order_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_move__purchase_order_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_payment__purchase_order_reference
msgid "'Engagement Juridique' in Chorus PRO."
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__buyer_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_move__buyer_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_payment__buyer_reference
msgid "'Service Exécutant' in Chorus PRO."
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__contract_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_move__contract_reference
#: model:ir.model.fields,help:l10n_fr_facturx_chorus_pro.field_account_payment__contract_reference
msgid "'Numéro de Marché' in Chorus PRO."
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__buyer_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move__buyer_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_payment__buyer_reference
msgid "Buyer reference"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model_terms:ir.ui.view,arch_db:l10n_fr_facturx_chorus_pro.view_move_form_inherit_chorus_pro
msgid "Chorus Pro"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__contract_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move__contract_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_payment__contract_reference
msgid "Contract Reference"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move__id
msgid "ID"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model,name:l10n_fr_facturx_chorus_pro.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_fr_facturx_chorus_pro
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_bank_statement_line__purchase_order_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_move__purchase_order_reference
#: model:ir.model.fields,field_description:l10n_fr_facturx_chorus_pro.field_account_payment__purchase_order_reference
msgid "Purchase order reference"
msgstr ""
