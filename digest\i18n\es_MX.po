# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* digest
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"button\" id=\"button_open_report\">Open Report</span>"
msgstr "<span class=\"button\" id=\"button_open_report\">Abrir reporte</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span class=\"odoo_link_text\">Odoo</span>"
msgstr "<span class=\"odoo_link_text\">Odoo</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "<span style=\"color: #8f8f8f;\">Unsubscribe</span>"
msgstr "<span style=\"color: #8f8f8f;\">Cancelar suscripción</span>"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Activate"
msgstr "Activar"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__activated
msgid "Activated"
msgstr "Activado"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Add new users as recipient of a periodic email with key metrics"
msgstr ""
"Agregue nuevos usuarios como recipientes de un correo electrónico periódico "
"con métricas clave"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__group_id
msgid "Authorized Group"
msgstr "Grupo autorizado"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__available_fields
msgid "Available Fields"
msgstr "Campos disponibles"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Choose the metrics you care about"
msgstr "Elija las métricas que le interesan"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__company_id
msgid "Company"
msgstr "Empresa"

#. module: digest
#: model:ir.model,name:digest.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Configure Digest Emails"
msgstr "Configure los correos electrónicos del resumen"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Connect"
msgstr "Conectar"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected
msgid "Connected Users"
msgstr "Usuarios conectados"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"Create or edit the mail template: you may get computed KPI's value using "
"these fields:"
msgstr ""
"Cree o edite su plantilla de correo: puede obtener valores KPI calculados "
"utilizando estos campos:"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__create_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__create_date
msgid "Created on"
msgstr "Creado el"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__daily
msgid "Daily"
msgstr "Diariamente"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Deactivate for everyone"
msgstr "Desactivar para todos"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__state__deactivated
msgid "Deactivated"
msgstr "Desactivado"

#. module: digest
#: model:ir.model,name:digest.model_digest_digest
msgid "Digest"
msgstr "Resumen"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_id
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Digest Email"
msgstr "Correo electrónico de resumen"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_digest_action
#: model:ir.actions.server,name:digest.ir_cron_digest_scheduler_action_ir_actions_server
#: model:ir.cron,cron_name:digest.ir_cron_digest_scheduler_action
#: model:ir.cron,name:digest.ir_cron_digest_scheduler_action
#: model:ir.model.fields,field_description:digest.field_res_config_settings__digest_emails
#: model:ir.ui.menu,name:digest.digest_menu
msgid "Digest Emails"
msgstr "Correos electrónicos de resumen"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Digest Name"
msgstr "Nombre del resumen"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "Digest Subscriptions"
msgstr "Suscripciones al resumen"

#. module: digest
#: model:ir.actions.act_window,name:digest.digest_tip_action
#: model:ir.model,name:digest.model_digest_tip
#: model:ir.ui.menu,name:digest.digest_tip_menu
msgid "Digest Tips"
msgstr "Consejos del resumen"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__display_name
#: model:ir.model.fields,field_description:digest.field_digest_tip__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "General"
msgstr "General"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Group by"
msgstr "Agrupar por"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid ""
"Have a question about a document? Click on the responsible user's picture to"
" start a conversation. If his avatar has a green dot, he is online."
msgstr ""
"¿Tiene una pregunta sobre el documento? Haga clic en la fotografía del "
"usuario responsable para iniciar una conversación. Si el avatar tiene un "
"punto verde, está en línea."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "How to customize your digest?"
msgstr "¿Cómo personalizar su resumen?"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__id
#: model:ir.model.fields,field_description:digest.field_digest_tip__id
msgid "ID"
msgstr "ID"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "In order to build your customized digest, follow these steps:"
msgstr "Siga los siguientes pasos para construir su resumen personalizado:"

#. module: digest
#: code:addons/digest/controllers/portal.py:0
#, python-format
msgid "Invalid periodicity set on digest"
msgstr "La periodicidad configurada en el resumen es inválida"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__is_subscribed
msgid "Is user subscribed"
msgstr "¿El usuario está suscrito?"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_tree
msgid "KPI Digest"
msgstr "Resumen KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_form
msgid "KPI Digest Tip"
msgstr "Consejo del Resumen KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_tip_view_tree
msgid "KPI Digest Tips"
msgstr "Consejos del Resumen KPI"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "KPIs"
msgstr "KPIs"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total_value
msgid "Kpi Mail Message Total Value"
msgstr "Valor KPI total de mensajes"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_res_users_connected_value
msgid "Kpi Res Users Connected Value"
msgstr "Valor KPI de usuarios conectados"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 24 hours"
msgstr "Últimas 24 horas"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 30 Days"
msgstr "Últimos 30 días"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Last 7 Days"
msgstr "Últimos 7 días"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest____last_update
#: model:ir.model.fields,field_description:digest.field_digest_tip____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_uid
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__write_date
#: model:ir.model.fields,field_description:digest.field_digest_tip__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__kpi_mail_message_total
msgid "Messages"
msgstr "Mensajes"

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__monthly
msgid "Monthly"
msgstr "Mensual"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__name
#: model:ir.model.fields,field_description:digest.field_digest_tip__name
msgid "Name"
msgstr "Nombre"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid ""
"New users are automatically added as recipient of the following digest "
"email."
msgstr ""
"Los nuevos usuarios se agregan automáticamente como destinatarios del "
"siguiente correo electrónico de resumen."

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__next_run_date
msgid "Next Send Date"
msgstr "Siguiente fecha de envío"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Odoo Mobile"
msgstr "Odoo móvil"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__periodicity
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_search
msgid "Periodicity"
msgstr "Periodicidad"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Powered by"
msgstr "Con la tecnología de"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Prefer a broader overview ?"
msgstr "¿Prefiere una vista más amplia?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid ""
"Press ALT in any screen to highlight shortcuts for every button in the "
"screen. It is useful to process multiple documents in batch."
msgstr ""
"Presione ALT en cualquier pantalla para resaltar atajos para cada botón en "
"la pantalla. Es útil para procesar múltiples documentos en lote. "

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__quarterly
msgid "Quarterly"
msgstr "Trimestralmente"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__user_ids
#: model:ir.model.fields,field_description:digest.field_digest_tip__user_ids
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Recipients"
msgstr "Destinatarios"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_section_mobile
msgid "Run your business from anywhere with <b>Odoo Mobile</b>."
msgstr "Dirija su negocio desde cualquier parte con <b>Odoo Móvil</b>."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Select your KPIs in the KPI's tab."
msgstr "Seleccione sus KPI's en la pestaña de KPI's."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Send Now"
msgstr "Enviar ahora"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_mail_main
msgid "Sent by"
msgstr "Enviado por"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.res_config_settings_view_form
msgid "Statistics"
msgstr "Estadísticas"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_digest__state
msgid "Status"
msgstr "Estado"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Subscribe"
msgstr "Suscribirse"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Switch to weekly Digests"
msgstr "Cambiar a resúmenes semanales"

#. module: digest
#: model:ir.model.fields,field_description:digest.field_digest_tip__tip_description
msgid "Tip description"
msgstr "Descripción del consejo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Consejo: Una calculadora en Odoo"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Consejo: Haga clic en un avatar para chatear con un usuario"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Consejo: ¿Cómo notificar usuarios en las notas internas?"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Consejo: El conocimiento es poder"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Consejo: Acelere su flujo de trabajo con atajos"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_2
msgid "Tip: A calculator in Odoo"
msgstr "Consejo: Una calculadora en Odoo"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_1
msgid "Tip: Click on an avatar to chat with a user"
msgstr "Consejo: Haga clic en un avatar para chatear con el usuario"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_3
msgid "Tip: How to ping users in internal notes?"
msgstr "Consejo: ¿Cómo notificar usuarios en las notas internas?"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_4
msgid "Tip: Knowledge is power"
msgstr "Consejo: El conocimiento es poder"

#. module: digest
#: model:digest.tip,name:digest.digest_tip_digest_0
msgid "Tip: Speed up your workflow with shortcuts"
msgstr "Consejo: Acelere su flujo de trabajo con atajos"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_3
msgid ""
"Type \"@\" to notify someone in a message, or \"#\" to link to a channel. "
"Try to notify @OdooBot to test the feature."
msgstr ""
"Escriba \"@\" para notificar a alguien en un mensaje, o \"#\" para vincular "
"un canal. Intente notificar a @OdooBot para probar esta función."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "Unsubscribe me"
msgstr "Cancelar mi suscripción"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__sequence
msgid "Used to display digest tip in email template base on order"
msgstr ""
"Se utiliza para presentar un consejo del resumen en la plantilla de correo "
"basada en la orden"

#. module: digest
#: model:ir.model,name:digest.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: digest
#: model:ir.model.fields,help:digest.field_digest_tip__user_ids
msgid "Users having already received this tip"
msgstr "Usuarios que ya han recibido este consejo"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "Want to customize this email?"
msgstr "¿Desea personalizar este correo electrónico?"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid ""
"We have noticed you did not connect these last few days. We have "
"automatically switched your preference to %(new_perioridicy_str)s Digests."
msgstr ""
"Nos dimos cuenta de que no se ha conectado los últimos días. Cambiamos de "
"forma automática su preferencia a resúmenes %(new_perioridicy_str)s."

#. module: digest
#: model:ir.model.fields.selection,name:digest.selection__digest_digest__periodicity__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_2
msgid ""
"When editing a number, you can use formulae by typing the `=` character. "
"This is useful when computing a margin or a discount on a quotation, sale "
"order or invoice."
msgstr ""
"Cuando edite un número, puede utilizar fórmulas al escribir el carácter `=`."
" Esto es útil cuando calcula un margen o un descuento en una cotización, "
"orden de venta o factura."

#. module: digest
#: model_terms:digest.tip,tip_description:digest.digest_tip_digest_4
msgid ""
"When following documents, use the pencil icon to fine-tune the information you want to receive.\n"
"Follow a project / sales team to keep track of this project's tasks / this team's opportunities."
msgstr ""
"Cuando siga documentos, use el icono de lápiz para ajustar la información que desea recibir.\n"
"Siga un proyecto o un equipo de ventas para llevar el seguimiento de las tareas de este proyecto/las oportunidades de este equipo."

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.portal_digest_unsubscribed
msgid "You have been successfully unsubscribed from"
msgstr "Se canceló su suscripción con éxito"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "You may want to add new computed fields with Odoo Studio:"
msgstr "Tal vez quiera agregar nuevos campos calculados con Odoo Studio:"

#. module: digest
#: model:digest.digest,name:digest.digest_digest_default
msgid "Your Odoo Periodic Digest"
msgstr "Su resumen periódico de Odoo"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid "e.g. Your Weekly Digest"
msgstr "Por ejemplo, Tu resumen semanal"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"first create a boolean field called\n"
"                                                <code>kpi_myfield</code>\n"
"                                                and display it in the KPI's tab;"
msgstr ""
"primero cree un campo booleano llamado\n"
"                                                 <code>kpi_myfield</code>\n"
"                                                 y muéstrelo en la pestaña de KPI's;"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "monthly"
msgstr "mensualmente"

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "quarterly"
msgstr "trimestralmente"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"then create a computed field called\n"
"                                                <code>kpi_myfield_value</code>\n"
"                                                that will compute your customized KPI."
msgstr ""
"luego cree un campo calculado llamado\n"
"                                                <code>kpi_myfield_value</code>\n"
"                                                 que calculará su KPI personalizado."

#. module: digest
#: code:addons/digest/models/digest.py:0
#, python-format
msgid "weekly"
msgstr "semanalmente"

#. module: digest
#: model_terms:ir.ui.view,arch_db:digest.digest_digest_view_form
msgid ""
"you must create 2 fields on the\n"
"                                                <code>digest</code>\n"
"                                                object:"
msgstr ""
"debe crear 2 campos en el objeto de \n"
"                                                    <code>resumen:</code>"
