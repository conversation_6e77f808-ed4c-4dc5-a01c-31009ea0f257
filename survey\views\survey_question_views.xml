<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <!-- QUESTIONS -->
    <record model="ir.ui.view" id="survey_question_form">
        <field name="name">Form view for survey question</field>
        <field name="model">survey.question</field>
        <field name="arch" type="xml">
            <form string="Survey Question" create="false">
                <field name="is_page" invisible="1"/>
                <field name="page_id" invisible="1" required="False"/>
                <field name="survey_id" invisible="1"/>
                <field name="sequence" invisible="1"/>
                <field name="scoring_type" invisible="1"/>
                <sheet>
                    <div class="oe_title" style="width: 100%;">
                        <label for="title" string="Section" attrs="{'invisible': [('is_page', '=', False)]}"/>
                        <label for="title" string="Question" attrs="{'invisible': [('is_page', '=', True)]}"/>
                        <separator />
                        <field name="title" colspan="4"/>
                        <separator />
                        <field name="questions_selection" invisible="1"/>
                    </div>
                    <group class="o_label_nowrap" attrs="{'invisible': ['|', ('is_page', '=', False), ('questions_selection', '=', 'all')]}">
                        <field name="random_questions_count"/>
                    </group>
                    <group attrs="{'invisible': [('is_page', '=', True)]}">
                        <group>
                            <field name="question_type" widget="radio" attrs="{'required': [('is_page', '=', False)]}" />
                        </group>
                        <group>
                            <div class="col-lg-6 offset-lg-3 o_preview_questions">
                                <!-- Multiple Lines Text Zone -->
                                <div attrs="{'invisible': [('question_type', '!=', 'text_box')]}">
                                        <i class="fa fa-align-justify fa-4x" role="img" aria-label="Multiple lines" title="Multiple Lines"/>
                                </div>
                                <!-- Single Line Text Zone -->
                                <div attrs="{'invisible': [('question_type', '!=', 'char_box')]}">
                                    <i class="fa fa-minus fa-4x" role="img" aria-label="Single Line" title="Single Line"/>
                                </div>
                                <!-- Numerical Value -->
                                <div attrs="{'invisible': [('question_type', '!=', 'numerical_box')]}">
                                    <i class="fa fa-2x" role="img" aria-label="Numeric" title="Numeric">123..</i>
                                </div>
                                <!-- Date -->
                                <div attrs="{'invisible': [('question_type', '!=', 'date')]}">
                                    <p class="o_datetime">YYYY-MM-DD
                                        <i class="fa fa-calendar fa-2x" role="img" aria-label="Calendar" title="Calendar"/>
                                    </p>
                                </div>
                                <!-- Date and Time -->
                                <div attrs="{'invisible': [('question_type', '!=', 'datetime')]}">
                                    <p class="o_datetime">YYYY-MM-DD hh:mm:ss
                                        <i class="fa fa-calendar fa-2x" role="img" aria-label="Calendar" title="Calendar"/>
                                    </p>
                                </div>
                                <!-- Multiple choice: only one answer -->
                                <div attrs="{'invisible': [('question_type', '!=', 'simple_choice')]}" role="img" aria-label="Multiple choice with one answer" title="Multiple choice with one answer">
                                    <div class="row"><i class="fa fa-circle-o  fa-lg"/> answer</div>
                                    <div class="row"><i class="fa fa-dot-circle-o fa-lg"/> answer</div>
                                    <div class="row"><i class="fa fa-circle-o  fa-lg"/> answer</div>
                                </div>
                                <!-- Multiple choice: multiple answers allowed -->
                                <div attrs="{'invisible': [('question_type', '!=', 'multiple_choice')]}" role="img" aria-label="Multiple choice with multiple answers" title="Multiple choice with multiple answers">
                                    <div class="row"><i class="fa fa-square-o fa-lg"/> answer</div>
                                    <div class="row"><i class="fa fa-check-square-o fa-lg"/> answer</div>
                                    <div class="row"><i class="fa fa-square-o fa-lg"/> answer</div>
                                </div>
                                <!-- Matrix -->
                                <div attrs="{'invisible': [('question_type', '!=', 'matrix')]}">
                                    <div class="row o_matrix_head">
                                        <div class="col-3"></div>
                                        <div class="col-3">ans</div>
                                        <div class="col-3">ans</div>
                                        <div class="col-3">ans</div>
                                    </div>
                                    <div class="row o_matrix_row">
                                        <div class="col-3">Row1</div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                        <div class="col-3"><i class="fa fa-dot-circle-o fa-lg" role="img" aria-label="Checked" title="Checked"/></div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                    </div>
                                    <div class="row o_matrix_row">
                                        <div class="col-3">Row2</div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                        <div class="col-3"><i class="fa fa-dot-circle-o fa-lg" role="img" aria-label="Checked" title="Checked"/></div>
                                    </div>
                                    <div class="row o_matrix_row">
                                        <div class="col-3">Row3</div>
                                        <div class="col-3"><i class="fa fa-dot-circle-o fa-lg" role="img" aria-label="Checked" title="Checked"/></div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                        <div class="col-3"><i class="fa fa-circle-o fa-lg" role="img" aria-label="Not checked" title="Not checked"/></div>
                                    </div>
                                </div>
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page string="Answers" name="answers" attrs="{'invisible': [('is_page', '=', True)]}">
                            <div class="row">
                                <div class="col-6"
                                    attrs="{'invisible': [('question_type', 'not in', ['char_box', 'numerical_box', 'date', 'datetime'])]}">
                                    <div attrs="{'invisible': [('question_type', '!=', 'char_box')]}">
                                        <field name="validation_email" nolabel="1"/>
                                        <label for="validation_email"/><br />

                                        <field name="save_as_email" nolabel="1" attrs="{'invisible': [('validation_email', '=', False)]}"/>
                                        <label for="save_as_email" attrs="{'invisible': [('validation_email', '=', False)]}"/>
                                        <br attrs="{'invisible': [('validation_email', '=', False)]}"/>

                                        <field name="save_as_nickname" nolabel="1"/>
                                        <label for="save_as_nickname"/>
                                    </div>
                                    <div>
                                        <field name="validation_required" nolabel="1"/>
                                        <label for="validation_required"/>
                                        <group attrs="{'invisible': [('validation_required', '=', False)]}">
                                            <field name="validation_length_min" attrs="{'invisible': [('question_type', '!=', 'char_box')]}"/>
                                            <field name="validation_length_max" attrs="{'invisible': [('question_type', '!=', 'char_box')]}"/>
                                            <field name="validation_min_float_value" attrs="{'invisible': [('question_type', '!=', 'numerical_box')]}"/>
                                            <field name="validation_max_float_value" attrs="{'invisible': [('question_type', '!=', 'numerical_box')]}"/>
                                            <field name="validation_min_date" attrs="{'invisible': [('question_type', '!=', 'date')]}"/>
                                            <field name="validation_max_date" attrs="{'invisible': [('question_type', '!=', 'date')]}"/>
                                            <field name="validation_min_datetime" widget="datetime" attrs="{'invisible': [('question_type', '!=', 'datetime')]}"/>
                                            <field name="validation_max_datetime" widget="datetime" attrs="{'invisible': [('question_type', '!=', 'datetime')]}"/>
                                            <field name="validation_error_msg" attrs="{'required': [('validation_required', '=', True)]}"/>
                                        </group>
                                    </div>
                                </div>

                                <div class="col-6" attrs="{'invisible': [
                                        '|', ('scoring_type', '=', 'no_scoring'),
                                        ('question_type', 'not in', ['numerical_box', 'date', 'datetime'])]
                                    }">
                                    <group>
                                        <field name="answer_numerical_box" string="Correct Answer" attrs="{'invisible': [('question_type', '!=', 'numerical_box')]}"/>
                                        <field name="answer_date" string="Correct Answer" attrs="{'invisible': [('question_type', '!=', 'date')]}"/>
                                        <field name="answer_datetime" string="Correct Answer" attrs="{'invisible': [('question_type', '!=', 'datetime')]}"/>
                                        <field name="is_scored_question"/>
                                        <field name="answer_score"/>
                                    </group>
                                </div>
                            </div>
                            <group attrs="{'invisible': [('question_type', 'not in', ['simple_choice', 'multiple_choice', 'matrix'])]}">
                                <field name="suggested_answer_ids" string="Answers" context="{'default_question_id': active_id}">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="value" string="Choices"/>
                                        <field name="value_image" options="{'accepted_file_extensions': 'image/*'}"
                                            attrs="{'column_invisible': ['|', ('parent.allow_value_image', '=', False), ('parent.question_type', '=', 'matrix')]}"/>
                                        <field name="is_correct" attrs="{'column_invisible': ['|', ('parent.scoring_type', '=', 'no_scoring'), ('parent.question_type', '=', 'matrix')]}"/>
                                        <field name="answer_score" attrs="{'column_invisible': ['|', ('parent.scoring_type', '=', 'no_scoring'), ('parent.question_type', '=', 'matrix')]}"/>
                                    </tree>
                                </field>
                            </group>
                            <group attrs="{'invisible': [('question_type', '!=', 'matrix')]}">
                                <field name="matrix_row_ids" context="{'default_matrix_question_id': active_id}" attrs="{'invisible': [('question_type', '!=', 'matrix')]}">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="value" string="Rows"/>
                                    </tree>
                                </field>
                            </group>
                        </page>
                        <page string="Description" name="survey_description">
                            <field name="description" widget="html"/>
                        </page>
                        <page string="Options" name="options" attrs="{'invisible': [('is_page', '=', True)]}">
                            <group string="Constraints">
                                <group>
                                    <field name="constr_mandatory" string="Mandatory Answer"/>
                                    <field name="constr_error_msg" attrs="{'invisible': [('constr_mandatory', '=', False)]}"/>
                                    <field name="matrix_subtype" attrs="{'invisible':[('question_type','not in',['matrix'])],'required':[('question_type','=','matrix')]}"/>
                                </group>
                            </group>
                            <group>
                                <group string="Display" attrs="{'invisible':[('question_type','not in',['simple_choice', 'multiple_choice'])]}">
                                    <field name="column_nb" string="Number of columns" invisible="1"/>
                                    <field name="allow_value_image"/>
                                </group>
                                <group string="Conditional Display" attrs="{'invisible': [('questions_selection', '=', 'random')]}">
                                    <field name="is_conditional"/>
                                    <field name="triggering_question_id"  options="{'no_open': True, 'no_create': True}"
                                           attrs="{'invisible': [('is_conditional','=', False)], 'required': [('is_conditional','=', True)]}"/>
                                    <field name="triggering_answer_id" options="{'no_open': True, 'no_create': True}"
                                           attrs="{'invisible': ['|', ('is_conditional','=', False), ('triggering_question_id','=', False)],
                                                   'required': [('is_conditional','=', True)]}"/>
                                </group>
                            </group>
                            <group string="Allow Comments" attrs="{'invisible':[('question_type','not in',['simple_choice','multiple_choice', 'matrix'])]}">
                                <field name='comments_allowed' />
                                <field name='comments_message' attrs="{'invisible': [('comments_allowed', '=', False)]}"/>
                                <field name='comment_count_as_answer' attrs="{'invisible': ['|', ('comments_allowed', '=', False), ('question_type', 'in', ['matrix'])]}" />
                            </group>
                            <group string="Live Sessions">
                                <label for="is_time_limited" string="Question Time Limit"/>
                                <div>
                                    <field name="is_time_limited" nolabel="1"/>
                                    <field name="time_limit" nolabel="1" class="oe_inline"
                                        attrs="{'invisible': [('is_time_limited', '=', False)]}" />
                                    <span attrs="{'invisible': [('is_time_limited', '=', False)]}"> seconds</span>
                                </div>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record model="ir.ui.view" id="survey_question_tree">
        <field name="name">Tree view for survey question</field>
        <field name="model">survey.question</field>
        <field name="arch" type="xml">
            <tree string="Survey Question" create="false">
                <field name="sequence" widget="handle"/>
                <field name="title"/>
                <field name="survey_id"/>
                <field name="question_type"/>
                <field name="triggering_question_id" invisible="1"/>
                <button disabled="disabled" icon="fa-code-fork" attrs="{'invisible': [('triggering_question_id', '=', False)]}"
                    title="This question depends on another question's answer." class="icon_rotates"/>
            </tree>
        </field>
    </record>
    <record model="ir.ui.view" id="survey_question_search">
        <field name="name">Search view for survey question</field>
        <field name="model">survey.question</field>
        <field name="arch" type="xml">
            <search string="Search Question">
                <field name="title"/>
                <field name="survey_id" string="Survey"/>
                <field name="question_type" string="Type"/>
                <group expand="1" string="Group By">
                    <filter name="group_by_type" string="Type" domain="[]" context="{'group_by':'question_type'}"/>
                    <filter name="group_by_survey" string="Survey" domain="[]" context="{'group_by':'survey_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_survey_question_form">
        <field name="name">Questions</field>
        <field name="res_model">survey.question</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="survey_question_search"/>
        <field name="context">{'search_default_group_by_page': True}</field>
        <field name="domain">[('is_page', '=', False)]</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_empty_folder">
            No questions found
          </p>
        </field>
    </record>

    <!-- LABELS -->
    <record id="survey_question_answer_view_tree" model="ir.ui.view">
        <field name="name">survey.question.answer.view.tree</field>
        <field name="model">survey.question.answer</field>
        <field name="arch" type="xml">
            <tree string="Survey Label" create="false">
                <field name="sequence" widget="handle"/>
                <field name="question_id"/>
                <field name="matrix_question_id"/>
                <field name="value"/>
                <field name="answer_score" groups="base.group_no_one"/>
            </tree>
        </field>
    </record>
    <record id="survey_question_answer_view_search" model="ir.ui.view">
        <field name="name">survey.question.answer.view.search</field>
        <field name="model">survey.question.answer</field>
        <field name="arch" type="xml">
            <search string="Search Label">
                <field name="question_id"/>
                <group expand="1" string="Group By">
                    <filter name="group_by_question" string="Question" domain="[]" context="{'group_by':'question_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="survey_question_answer_action" model="ir.actions.act_window">
        <field name="name">Suggested Values</field>
        <field name="res_model">survey.question.answer</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="survey_question_answer_view_search"/>
        <field name="context">{'search_default_group_by_question': True}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_empty_folder">
            No survey labels found
          </p>
        </field>
    </record>

    <menuitem name="Questions"
        id="menu_survey_question_form1"
        action="action_survey_question_form"
        parent="survey_menu_questions"
        sequence="2"/>
    <menuitem name="Suggested Values"
        id="menu_survey_label_form1"
        action="survey_question_answer_action"
        parent="survey_menu_questions"
        sequence="3"/>
</data>
</odoo>
