# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-08-24 09:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: hr
#: code:addons/hr/models/hr.py:76
#, python-format
msgid "%s (copy)"
msgstr "%s (afrita)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<i>Work in a fun atmosphere</i>"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<i>You are passionate</i>"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<i>You autonomously and quickly learn</i>"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "<i>You easily manage them</i>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Skýrslugerð</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>To Approve</span>"
msgstr "<span>To Approve</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>To Do</span>"
msgstr "<span>To Do</span>"

#. module: hr
#: model:mail.template,body_html:hr.mail_template_data_unknown_employee_email_address
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Hi,<br/>\n"
"                        Your document has not been created because your email address is not recognized. Please send emails with the email address recorded on your employee information, or contact your HR manager.\n"
"                    </div>\n"
"                </td></tr>\n"
"            </table>\n"
"    </td></tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=hr\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"A full time job, with an attractive salary package in a small team of smart "
"people. You will start contract with a full technical and functional "
"training. If you are passionate, motivate and flexible apply for this job, "
"you will certainly join the best company ever."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A good job in a young and dynamic team"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Þarfnast aðgerðar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
msgid "Active"
msgstr "Virkur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
msgid "Activities"
msgstr "Aðgerðir"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Staða aðgerðar"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
msgid "Additional Note"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Contact Security"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Analyse needs, write specification documents and quotation"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Archived"
msgstr "Geymt"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Employees"
msgstr ""

#. module: hr
#: selection:mail.alias,alias_contact:0
msgid "Authenticated Partners"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.mail_channel_view_form_
msgid "Auto Subscribe Departments"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr ""

#. module: hr
#: selection:hr.employee,certificate:0
msgid "Bachelor"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account Number"
msgstr "Bank Account Number"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
msgid ""
"Because of our constant growth, we're now looking for people to reinforce "
"our team of enthusiastic chief technical officer. So if working on an open "
"source project in a friendly and cooperative atmosphere sounds like fun to "
"you, read on..."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid ""
"Because of our constant growth, we're now looking for people to reinforce "
"our team of enthusiastic developers. So if working on an open source project"
" in a friendly and cooperative atmosphere sounds like fun to you, read on..."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Because of our constant growth, we're now looking for people to reinforce "
"our team of enthusiastic trainees. So if working on an open source project "
"in a friendly and cooperative atmosphere sounds like fun to you, read on..."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Birth"
msgstr "Birth"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
msgid "Certificate Level"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Child Departments"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship & Other Information"
msgstr "Citizenship & Other Information"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Close to the perfection ..."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Coach"
msgstr "Coach"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
msgid "Color Index"
msgstr "Color Index"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Fyrirtæki"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Fyrirtæki"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Fullt nafn"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
msgid "Configuration"
msgstr "Uppsetning"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Tengiliður"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Contact Information"
msgstr "Tengiiðaupplýsingar"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Contributions to open source projects"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
msgid "Country of Birth"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_module_tree_department
msgid "Create a new department"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Current Number of Employees"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
msgid "Date of Birth"
msgstr "Date of Birth"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Define a clear communication strategy"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Define the schedule of resource"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Deliver generic Odoo functional training sessions"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Department"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Department Name"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_module_tree_department
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Departments"

#. module: hr
#: model:ir.model,name:hr.model_mail_channel
msgid "Discussion channel"
msgstr "Discussion channel"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: hr
#: selection:hr.employee,marital:0
msgid "Divorced"
msgstr "Divorced"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "Email Aliases"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
msgid "Emergency Contact"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
msgid "Emergency Phone"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_employee
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee"
msgstr "Starfsmaður"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Employee Category"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__google_drive_link
msgid "Employee Documents"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Employee Tag"
msgstr "Employee Tag"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Employee Tags"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
msgid "Employee bank salary account"
msgstr "Employee bank salary account"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Employee's Name"

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model:ir.ui.menu,name:hr.menu_open_view_employee_list_my
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
#: model_terms:ir.ui.view,arch_db:hr.view_partner_tree2
msgid "Employees"
msgstr "Starfsfólk"

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr "Employees Structure"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Employees Tags"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__address_home_id
msgid ""
"Enter here the private address of the employee, not the one linked to your "
"company."
msgstr ""

#. module: hr
#: selection:mail.alias,alias_contact:0
msgid "Everyone"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Expected New Employees"
msgstr "Expected New Employees"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr ""
"Expected number of employees for this job position after new recruitment."

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr ""

#. module: hr
#: selection:hr.employee,gender:0
msgid "Female"
msgstr "Female"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
msgid "Field of Study"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Follow and check the development part"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Fylgjendur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_channel_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_channel_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_channel_ids
msgid "Followers (Channels)"
msgstr "Followers (Channels)"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Fylgjendur (viðskiptafélagar)"

#. module: hr
#: selection:mail.alias,alias_contact:0
msgid "Followers only"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
msgid "Gender"
msgstr "Gender"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "Good knowledge of HTML and Javascript"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Good knowledge of object oriented programming"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Good knowledge of the latest web technologies"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Good knowledge of the programming language"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Good knowledge of web design"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Good language skills in another language"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Hópa eftir"

#. module: hr
#: model:ir.model,name:hr.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_channel__subscription_department_ids
msgid "HR Departments"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr "HR Settings"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Help with the configuration of the software"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_hrm
msgid "Hire a team of great Human Resources people"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
msgid "Hire a team of great executive people"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Hire a team of great marketing people"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_hired_employee
msgid "Hired Employees"
msgstr "Hired Employees"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Human Resources"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
msgid "ID"
msgstr "Auðkenni"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
msgid "Identification No"
msgstr "Identification No"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_unread
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread
#: model:ir.model.fields,help:hr.field_hr_job__message_unread
msgid "If checked new messages require your attention."
msgstr "Ef merkt við þá eru ný skilaboð"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr "If checked, new messages require your attention."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."

#. module: hr
#: code:addons/hr/models/hr.py:296
#, python-format
msgid "Import Template for Employees"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Improve our communication to customers"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Position"
msgstr "In Position"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "In Recruitment"
msgstr "In Recruitment"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Increase opportunities"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Increase the visibility of the product"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Is Follower"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job"
msgstr "Job"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr "Job Description"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
msgid "Job Position"
msgstr "Starfsheiti"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
msgid "Job Positions"
msgstr "Job Positions"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
msgid "Job Title"
msgstr "Job Title"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Jobs"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
msgid "Km home-work"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee____last_update
#: model:ir.model.fields,field_description:hr.field_hr_employee_category____last_update
#: model:ir.model.fields,field_description:hr.field_hr_job____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Launch new marketing campaigns and offers"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_hrm
msgid "Launch new products, Human Resources campaigns and offers"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
msgid "Launch new products, marketing campaigns and offers"
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Legal Cohabitant"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
#: model:ir.model.fields,field_description:hr.field_hr_job__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr
#: selection:hr.employee,gender:0
msgid "Male"
msgstr "Male"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model:res.groups,name:hr.group_hr_manager
msgid "Manager"
msgstr "Yfirmaður"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
msgid "Marital Status"
msgstr "Marital Status"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Married"
msgstr "Married"

#. module: hr
#: selection:hr.employee,certificate:0
msgid "Master"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master or engineer in computer science"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_medium
msgid "Medium-sized photo"
msgstr "Medium-sized photo"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__image_medium
msgid ""
"Medium-sized photo of the employee. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."
msgstr ""
"Medium-sized photo of the employee. It is automatically resized as a "
"128x128px image, with aspect ratio preserved. Use this field in form views "
"or some kanban views."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Members"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Skilaboð"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must have ..."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
msgid "Name"
msgstr "Nafn"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Nationality (Country)"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Samantekt næstu virkni"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr ""

#. module: hr
#: selection:hr.job,state:0
msgid "Not Recruiting"
msgstr "Not Recruiting"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
msgid "Note"
msgstr "Athugasemd"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Athugasemdir"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Fjöldi aðgerða"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
msgid "Number of Children"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Number of employees currently occupying this job position."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""
"Number of hired employees for this job position during recruitment phase."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Fjöldi skilaboð sem bíða afgreiðslu"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "Number of new employees you expect to recruit."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_unread_counter
msgid "Number of unread messages"
msgstr "Number of unread messages"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_module_tree_department
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                leaves, recruitments, etc."
msgstr ""

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer"
msgstr "Officer"

#. module: hr
#: selection:hr.employee,certificate:0 selection:hr.employee,gender:0
msgid "Other"
msgstr "Annað"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Other Information ..."
msgstr "Other Information ..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our staff at work"
msgstr ""

#. module: hr
#: selection:hr.employee,activity_state:0
msgid "Overdue"
msgstr "Overdue"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Parent Department"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "Passion for the Internet and its culture"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
msgid "Passport No"
msgstr "Passport No"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image
msgid "Photo"
msgstr "Photo"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Pictures of smart and enthusiastic people"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
msgid "Place of Birth"
msgstr ""

#. module: hr
#: selection:hr.employee,activity_state:0
msgid "Planned"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Position"
msgstr "Position"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Preferably 1 year of experience"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_home_id
msgid "Private Address"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Quantify and negotiate the resources required"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "Quick and autonomous learner"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Recruitment"

#. module: hr
#: selection:hr.job,state:0
msgid "Recruitment in Progress"
msgstr "Recruitment in Progress"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Related User"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employees"
msgstr "Related employees"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Related user name for the resource to manage its access."

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Skýrslur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Kröfur"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
msgid "Resource"
msgstr "Auðlind"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Responsibilities"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "SIN No"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
msgid "SSN No"
msgstr "SSN No"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Scale our events organization all around the world"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
msgid "School"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Set default calendar used to compute time allocation for leaves, timesheets,"
" ..."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__state
msgid ""
"Set whether the recruitment process is open or closed for this job position."
msgstr ""
"Set whether the recruitment process is open or closed for this job position."

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Settings"
msgstr "Stillingar"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_org_chart
msgid "Show Organizational Chart"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Show organizational chart on employee form"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Show organizational chart on employee form."
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Single"
msgstr "Single"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Skills"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_small
msgid "Small-sized photo"
msgstr "Small-sized photo"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__image_small
msgid ""
"Small-sized photo of the employee. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."
msgstr ""
"Small-sized photo of the employee. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is"
" required."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "Social Insurance Number"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
msgid "Social Security Number"
msgstr "Social Security Number"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
msgid "Spouse Birthdate"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
msgid "Spouse Complete Name"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Start Recruitment"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__state
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Status"
msgstr "Staða"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Stop Recruitment"
msgstr "Stop Recruitment"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_employee_action_subordinate_hierachy
msgid "Subordinate Hierarchy"
msgstr "Subordinate Hierarchy"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
msgid "Subordinates"
msgstr "Subordinates"

#. module: hr
#: sql_constraint:hr.employee.category:0
msgid "Tag name already exists !"
msgstr "Tag name already exists !"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
msgid "Tags"
msgstr "Flokkar"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Take part in the consulting services"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "Team spirit and good communication"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_address_home_a_company
msgid "The employee adress has a company linked"
msgstr ""

#. module: hr
#: sql_constraint:hr.job:0
msgid "The name of the job position must be unique per department in company!"
msgstr ""
"The name of the job position must be unique per department in company!"

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__image
msgid ""
"This field holds the image used as photo for the employee, limited to "
"1024x1024px."
msgstr ""
"This field holds the image used as photo for the employee, limited to "
"1024x1024px."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
msgid "Timezone"
msgstr "Timezone"

#. module: hr
#: selection:hr.employee,activity_state:0
msgid "Today"
msgstr "Í dag"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Total Forecasted Employees"

#. module: hr
#: model:hr.job,name:hr.job_trainee
msgid "Trainee"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Transform our product into a suite of well positioned business Apps"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Ólesin skilaboð"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_unread_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teljari fyrir ólesin skilaboð"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid ""
"Use here the home address of the employee.\n"
"                                            This private address is used in the expense report reimbursement document.\n"
"                                            It should be different from the work address."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
msgid "User"
msgstr "Notandi"

#. module: hr
#: model:ir.model,name:hr.model_res_users
msgid "Users"
msgstr "Notendur"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies :"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
msgid "Visa Expire Date"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
msgid "Visa No"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid ""
"We are looking for a motivated and results-driven Functional Consultant! You"
" will take part in the consulting services we provide to our partners and "
"customers, on the functional side. Your job start from the quotation to the "
"customer to the delivery to the customer. You listen the customer and try to"
" give him the best service. You report to the head of consulting service and"
" will be coached by a senior consultant."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_hrm
msgid ""
"We are looking for a passionated Human Resources Manager to help us make "
"products people love to use. We need someone who is ambitious, passionated, "
"and ..... not afraid to start new things, a lot of new things and scale "
"them."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
msgid ""
"We are looking for a passionated executive manager to help us make products "
"people love to use. We need someone who is ambitious, passionated, and ....."
" not afraid to start new things, a lot of new things and scale them."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_marketing
msgid ""
"We are looking for a passionated marketer manager to help us make products "
"people love to use. We need someone who is ambitious, passionated, and ....."
" not afraid to start new things, a lot of new things and scale them."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What we offer"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What you will do ..."
msgstr ""

#. module: hr
#: selection:hr.employee,marital:0
msgid "Widower"
msgstr "Widower"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "Will report to the Head of Professional Services"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"With our product, which is a suite of 3000 business apps. It is fully open "
"source, full featured and it’s online offer is 3 times cheaper than "
"traditional competitors. We have 2.000.000 users, and we're growing fast."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
msgid "Work Address"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
msgid "Work Email"
msgstr "Work Email"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location
msgid "Work Location"
msgstr "Work Location"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
msgid "Work Mobile"
msgstr "Work Mobile"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
msgid "Work Permit No"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
msgid "Work Phone"
msgstr "Work Phone"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "Write attractive content"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_hrm
msgid "Write attractive content and build up Human Resources materials"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
msgid "Write attractive content and build up executive materials"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You are a quick and autonomous learner"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You are an excellent communicator and negotiator"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "You are approachable, honest and fun team player"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You are available immediately"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "You are creative"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You are ready to travel in US"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "You are ready to work in a dynamic company"
msgstr ""

#. module: hr
#: code:addons/hr/models/hr.py:206
#, python-format
msgid "You cannot create a recursive hierarchy."
msgstr ""

#. module: hr
#: code:addons/hr/models/hr.py:331
#, python-format
msgid "You cannot create recursive departments."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You have 2 or 3 years of experience"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You have a Master degree in Business Management"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "You have an affinity with the IT world"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid "You have an affinity with the product"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You have good knowledge in accounting"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You speak a third language"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_consultant
msgid "You speak fluently French and English"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"You will be responsible for developing and improving applications. You will "
"work autonomously as well as                     coordinate and supervise "
"small distributed development teams for specific projects. You will become a"
" technical                 expert of the product."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid ""
"You will be responsible for developing and improving applications. You will "
"work autonomously as well as coordinate and supervise small distributed "
"development teams for specific projects. You will become a technical expert "
"of the product."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
msgid ""
"You will be responsible for developing and improving product communication. "
"You will work autonomously as well as coordinate and supervise small "
"distributed development teams for specific projects. You will become a "
"techinical expert of the product."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "You will become a technical expert of the product."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "You will initially be coached by senior developers"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
msgid "You will initially be coached by senior technical officer"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "You will initially be coached by senior trainees"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "You will report to the head of R&amp;D"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_developer
msgid "You will work closely with all developers"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_cto
msgid "You will work closely with all technical officer"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "You will work closely with all trainees"
msgstr ""

#. module: hr
#: model:mail.template,subject:hr.mail_template_data_unknown_employee_email_address
msgid "Your document has not been created"
msgstr "Your document has not been created"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "department"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. Part Time"
msgstr "e.g. Part Time"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "e.g. Sales Manager"
