$o-kanban-attachement-image-size: 80px;

.o_kanban_view {

    .o_kanban_record.o_kanban_attachment {
        padding: map-get($spacers, 0);

        .o_kanban_image {
            width: $o-kanban-attachement-image-size;

            + div {
                padding-left: $o-kanban-attachement-image-size + $o-kanban-inside-hgutter;
                @include media-breakpoint-down(sm) {
                    padding-left:  $o-kanban-attachement-image-size + $o-kanban-inside-hgutter-mobile;
                }
            }

            .o_kanban_image_wrapper {
                min-height: $o-kanban-attachement-image-size;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .o_attachment_image {
                @include size($o-kanban-attachement-image-size);
            }

            .o_image {
                @include size($o-kanban-attachement-image-size*0.7);
            }
        }

        .o_kanban_details {
            .o_kanban_details_wrapper {
                display: flex;
                flex-direction: column;
                min-height: $o-kanban-attachement-image-size;
                padding: $o-kanban-inside-vgutter $o-kanban-inside-hgutter;

                .o_kanban_record_title {
                    margin-bottom: $o-kanban-inside-vgutter*0.5;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 95%;
                }

                .o_kanban_record_body {
                    flex: 1 1 auto;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    font-size: smaller;
                }

                .oe_kanban_avatar {
                    border-radius: 4px;
                    border: 1px solid $component-active-color;
                }
            }
        }
    }
}
