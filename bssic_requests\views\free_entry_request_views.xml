<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Free Entry Request Form View -->
        <record id="view_free_entry_request_form" model="ir.ui.view">
            <field name="name">free.entry.request.form</field>
            <field name="model">bssic.request</field>
            <field name="inherit_id" ref="bssic_requests.view_bssic_request_form"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <!-- Override header to show only relevant buttons for free form requests -->
                <xpath expr="//header" position="replace">
                    <header>
                        <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                        <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                                states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                        <button name="action_approve_audit_manager" string="Approve (Audit Manager)" type="object" class="oe_highlight"
                                states="audit_manager" groups="bssic_requests.group_bssic_audit_manager"/>
                        <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                                states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                                states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                                states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                        <button name="action_reject" string="Reject" type="object" class="btn-danger"
                                states="direct_manager,audit_manager,it_manager,assigned,in_progress"/>
                        <field name="is_technical" invisible="1"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                    </header>
                </xpath>

                <!-- Hide all other request type fields and show only free entry fields -->
                <xpath expr="//page[@name='request_details']" position="replace">
                    <page string="Request Details" name="request_details">
                        <!-- Free Form Specific Fields -->
                        <group string="Free Form Details" attrs="{'invisible': [('show_free_entry_fields', '=', False)]}">
                            <group>
                                <field name="free_entry_subject" string="Subject" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="free_entry_user_name" string="User Name" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="free_entry_type" string="Entry Type" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="free_entry_from_date" string="From Date" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="free_entry_to_date" string="To Date" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                            <group>
                                <field name="free_entry_details" string="Operation Details" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}" widget="text"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>

                        <!-- Hidden fields for computation -->
                        <field name="show_free_entry_fields" invisible="1"/>
                        <field name="request_type_code" invisible="1"/>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Free Entry Request Tree View -->
        <record id="view_free_entry_request_tree" model="ir.ui.view">
            <field name="name">free.entry.request.tree</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <tree string="Free Form Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="request_date"/>
                    <field name="free_entry_subject"/>
                    <field name="free_entry_user_name"/>
                    <field name="free_entry_type"/>
                    <field name="free_entry_from_date"/>
                    <field name="free_entry_to_date"/>
                    <field name="state" decoration-info="state=='draft'"
                           decoration-warning="state in ['submitted','direct_manager','audit_manager','it_manager']"
                           decoration-success="state=='completed'"
                           decoration-danger="state=='rejected'"/>
                </tree>
            </field>
        </record>

        <!-- Free Entry Request Search View -->
        <record id="view_free_entry_request_search" model="ir.ui.view">
            <field name="name">free.entry.request.search</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <search string="Free Form Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="free_entry_subject"/>
                    <field name="free_entry_user_name"/>
                    <field name="free_entry_type"/>
                    <field name="free_entry_details"/>
                    <field name="free_entry_from_date"/>
                    <field name="free_entry_to_date"/>
                    <separator/>
                    <filter string="My Requests" name="my_requests"
                            domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Pending Approval" name="pending"
                            domain="[('state', 'in', ['submitted', 'direct_manager', 'audit_manager', 'it_manager'])]"/>
                    <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                        <filter string="Department" name="group_department" context="{'group_by': 'department_id'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Request Date" name="group_date" context="{'group_by': 'request_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Free Entry Request Action for Employees (Own Requests Only) -->
        <record id="action_free_entry_request_employee" model="ir.actions.act_window">
            <field name="name">Free Form Requests</field>
            <field name="res_model">bssic.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('request_type_id.code', '=', 'free_entry'), ('employee_id.user_id', '=', uid)]</field>
            <field name="context">{
                'default_request_type_code': 'free_entry',
                'form_view_ref': 'bssic_requests.view_free_entry_request_form'
            }</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_free_entry_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_free_entry_request_form')})]"/>
            <field name="groups_id" eval="[(4, ref('bssic_requests.group_bssic_employee'))]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Free Form Request!
                </p>
                <p>
                    Submit requests for free form authorization.
                </p>
            </field>
        </record>

        <!-- Free Entry Request Action for Managers (All Requests) -->
        <record id="action_free_entry_request_manager" model="ir.actions.act_window">
            <field name="name">Free Form Requests</field>
            <field name="res_model">bssic.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('request_type_id.code', '=', 'free_entry')]</field>
            <field name="context">{
                'default_request_type_code': 'free_entry',
                'form_view_ref': 'bssic_requests.view_free_entry_request_form'
            }</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_free_entry_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_free_entry_request_form')})]"/>
            <field name="groups_id" eval="[
                (4, ref('bssic_requests.group_bssic_direct_manager')),
                (4, ref('bssic_requests.group_bssic_audit_manager')),
                (4, ref('bssic_requests.group_bssic_it_manager')),
                (4, ref('bssic_requests.group_bssic_it_staff'))
            ]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Free Form Request!
                </p>
                <p>
                    Submit requests for free form authorization.
                </p>
            </field>
        </record>

        <!-- Menu Item for Free Entry Requests (Employees) -->
        <menuitem id="menu_free_entry_request_employee"
                  name="Free Form"
                  parent="menu_bssic_request"
                  action="action_free_entry_request_employee"
                  groups="bssic_requests.group_bssic_employee"
                  sequence="70"/>

        <!-- Menu Item for Free Entry Requests (Managers) -->
        <menuitem id="menu_free_entry_request_manager"
                  name="Free Form (All)"
                  parent="menu_bssic_request"
                  action="action_free_entry_request_manager"
                  groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_audit_manager,bssic_requests.group_bssic_it_manager,bssic_requests.group_bssic_it_staff"
                  sequence="71"/>

    </data>
</odoo>
