# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "# línur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr ""

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
msgid ""
"${(object.name or '').replace('/','_')}${object.state == 'draft' and "
"'_draft' or ''}"
msgstr ""
"${(object.name or '').replace('/','_')}${object.state == 'draft' and "
"'_draft' or ''}"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and 'Tilboð' "
"or 'Pöntun'} (Nr. ${object.name or 'n/a' })"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "% discount"
msgstr "% afslætti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "&amp;nbsp;<span>on</span>&amp;nbsp;"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "&amp;times;"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.brand_promotion
msgid ""
",\n"
"                an awesome"
msgstr ""

#. module: sale
#: model:product.product,description_sale:sale.product_product_4e
#: model:product.product,description_sale:sale.product_product_4f
#: model:product.template,description_sale:sale.product_product_4e_product_template
msgid "160x80cm, with large legs."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:70
#, python-format
msgid "<b>Print this quotation to preview it.</b>"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        % set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order'\n"
"        Dear ${object.partner_id.name}\n"
"        % if object.partner_id.parent_id:\n"
"            (${object.partner_id.parent_id.name})\n"
"        % endif\n"
"        <br/><br/>\n"
"        Here is\n"
"        % if ctx.get('proforma')\n"
"            in attachment your pro-forma invoice\n"
"        % else\n"
"            the ${doc_name} <strong>${object.name}</strong>\n"
"        % endif\n"
"        % if object.origin:\n"
"            (with reference: ${object.origin} )\n"
"        % endif\n"
"        amounting in <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}</strong>\n"
"        from ${object.company_id.name}.\n"
"        <br/><br/>\n"
"        Do not hesitate to contact us if you have any question.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-check\"/> Accept &amp; Sign"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get the final version."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires in</b></small>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span class=\"badge badge-info orders_label_text_align\"><i class=\"fa fa-fw"
" fa-clock-o\"/> Waiting Payment</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span class=\"badge badge-success orders_label_text_align\"><i class=\"fa "
"fa-fw fa-check\"/> Paid</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.optional_product_items
msgid ""
"<span class=\"js_item add_qty\">1 </span><span class=\"js_items d-none\">5 "
"</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Down Payments</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<span groups=\"account.group_show_line_subtotals_tax_excluded\">Amount</span>\n"
"                                <span groups=\"account.group_show_line_subtotals_tax_included\">Total Price</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Disc.(%)</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Discount</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoicing Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Shipping Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"text-muted d-block mb-1\">Invoices</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Your Contact</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Date Ordered:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Skattaleg staðsetning:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Payment Terms:</strong>"
msgstr "<strong>Greiðsluskilmálar:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Quotation Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Sölumaður:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Afhendingarstaður:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Subtotal</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This is a draft quotation.</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Total</strong>"
msgstr "<strong>Samtals</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Þín tilvísun:</strong>"

#. module: sale
#: code:addons/sale/models/sale.py:811
#, python-format
msgid "A journal must be specified of the acquirer %s."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:804
#, python-format
msgid "A payment acquirer is required to create a transaction."
msgstr ""

#. module: sale
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "A single sales price per product"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:775
#, python-format
msgid ""
"A transaction can't be linked to sales orders having different currencies."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:780
#, python-format
msgid ""
"A transaction can't be linked to sales orders having different partners."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"A typical example is the pre-paid hours of service,\n"
"                where you want to sell extra hours to the customer\n"
"                because the initial hours have already been used."
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "A warning can be set on a product or a customer (Sale)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Account Number"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "Account used for deposits"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Þarfnast aðgerðar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "Aðgerðir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Staða aðgerðar"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_configurator_view_form
msgid "Add"
msgstr "Bæta við"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
msgid "Add one"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice__comment
msgid "Additional Information"
msgstr "Ítarupplýsingar"

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Addresses in Sales Orders"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:165
#, python-format
msgid "Advance: %s"
msgstr "Advance: %s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_attribute_value__is_custom
#: model:ir.model.fields,help:sale.field_product_template_attribute_value__is_custom
msgid "Allow users to input custom values for this attribute value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Amount"
msgstr "Upphæð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "Kostnaðarreikningur"

#. module: sale
#: selection:sale.order.line,qty_delivered_method:0
msgid "Analytic From Expenses"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytic Line"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Analytic Tags"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Kostnaðarfærslur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_quotation_layout_form
msgid "Apply"
msgstr "Virkja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""

#. module: sale
#: selection:product.template,expense_policy:0
msgid "At cost"
msgstr "At cost"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__attribute_value_id
msgid "Attribute"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Eiginleikar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
msgid "Available Options:"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_controller.js:124
#, python-format
msgid "Back"
msgstr "Til baka"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nafn banka"

#. module: sale
#: code:addons/sale/models/payment.py:14
#: selection:payment.acquirer,so_reference_type:0
#, python-format
msgid "Based on Customer ID"
msgstr ""

#. module: sale
#: code:addons/sale/models/payment.py:13
#: selection:payment.acquirer,so_reference_type:0
#, python-format
msgid "Based on Document Reference"
msgstr ""

#. module: sale
#: model:product.template.attribute.value,name:sale.product_template_attribute_value_5
msgid "Black"
msgstr ""

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "Blocking Message"
msgstr "Lokunarskilaboð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.sale_product_configurator_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Hætta við"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Cancelled"
msgstr "Afpöntuð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr ""

#. module: sale
#: model:product.template,name:sale.product_product_1_product_template
msgid "Chair floor protection"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away. This will add specific action buttons to "
"your dashboard."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__use_invoices
msgid "Check this box to set an invoicing target for this Sales Team."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose how to confirm quotations and get paid."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:41
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define a target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Loka"

#. module: sale
#: selection:res.company,sale_quotation_onboarding_state:0
msgid "Closed"
msgstr ""

#. module: sale
#: selection:product.attribute,type:0
msgid "Color"
msgstr "Color"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
msgid "Commitment Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_acquirer__so_reference_type
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication"
msgstr "Communication"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Fyrirtæki"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Fyrirtæki"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Uppsetning"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_controller.js:125
#, python-format
msgid "Configure"
msgstr "Configure"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Configure a product"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_quotation_layout
msgid "Configure your document layout"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Configure your products with variants and select optional products"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/product_configurator_controller.js:123
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Confirm"
msgstr "Staðfesta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Confirmation &amp; Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__confirmation_date
#: model:ir.model.fields,field_description:sale.field_sale_report__confirmation_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Confirmation Date"
msgstr "Dags Staðfestingar"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "Tengiliður"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__dashboard_graph_model
msgid "Content"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoices"
msgstr "Útbúa reikning"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Create a new quotation, the first step of a new sale!"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoices"
msgstr "Create and View Invoices"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__create_date
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
msgid "Creation Date"
msgstr "Búið til þann"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
msgid "Currency"
msgstr "Gjaldmiðill"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Currency Rate"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Custom"

#. module: sale
#: selection:sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Custom payment instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__custom_value
msgid "Custom value"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Viðskiptavinur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Tilvísun viðskiptavinar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "Customer Taxes"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Viðskiptavinir"

#. module: sale
#: model:product.product,name:sale.product_product_4e
#: model:product.product,name:sale.product_product_4f
#: model:product.template,name:sale.product_product_4e_product_template
msgid "Customizable Desk"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Dags."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__date_order
msgid "Date Order"
msgstr "Dagsetning pöntunar"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__create_date
msgid "Date on which sales order is created."
msgstr "Dags. hvenær sölu pöntun er gerð."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__confirmation_date
msgid "Date on which the sales order is confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Date:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default Limit:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__use_sale_note
msgid "Default Terms & Conditions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_note
msgid "Default Terms and Conditions"
msgstr "Default Terms and Conditions"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "Default product used for payment advances"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:1060
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Delivered Quantity"
msgstr "Afhent magn"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Delivered quantities"
msgstr "Afhent magn"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice__partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Afhendingarstaður"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_sale_order_dates
msgid "Delivery Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Delivery Lead Time"
msgstr "Delivery Lead Time"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_invoice__partner_shipping_id
msgid "Delivery address for current invoice."
msgstr "Delivery address for current invoice."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__partner_shipping_id
msgid "Delivery address for current sales order."
msgstr "Afhendingarstaður fyrir valda sölupöntun"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from product lead "
"times and from the shipping policy of the order."
msgstr ""

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Deposit Product"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Lýsing"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Details"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Afsláttur (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_pricelist__discount_policy
msgid "Discount Policy"
msgstr ""

#. module: sale
#: selection:product.pricelist,discount_policy:0
msgid "Discount included in the price"
msgstr "Discount included in the price"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Discount on lines"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "Afslættir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__display_name
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__display_name
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma__display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_order_confirmation_state:0
#: selection:res.company,sale_onboarding_sample_quotation_state:0
#: selection:res.company,sale_quotation_onboarding_state:0
msgid "Done"
msgstr "Lokið"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:87
#, python-format
msgid "Down Payment"
msgstr "Down Payment"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "Down Payment Amount"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "Down Payment Product"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (fixed amount)"
msgstr "Down payment (fixed amount)"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (percentage)"
msgstr "Down payment (percentage)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:84
#, python-format
msgid "Down payment of %s%%"
msgstr "Down payment of %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "Niðurhal"

#. module: sale
#: selection:sale.report,state:0
msgid "Draft Quotation"
msgstr "Draft Quotation"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__template_id
msgid "Email Template"
msgstr "Email Template"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Viðbótarsíur"

#. module: sale
#: code:addons/sale/models/sale.py:1034
#, python-format
msgid "Extra line with %s "
msgstr "Extra line with %s "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Bókhaldsleg staðsetning "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Fylgjendur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "Followers (Channels)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Fylgjendur (viðskiptafélagar)"

#. module: sale
#: sql_constraint:sale.order.line:0
msgid "Forbidden values on non-accountable sale order line"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Fully Invoiced"
msgstr "Að fullu reikningsfært"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Aðgerðir"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Gross Weight"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Hópa eftir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_value__html_color
#: model:ir.model.fields,field_description:sale.field_product_template_attribute_value__html_color
msgid "HTML Color Index"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_attribute_value__html_color
#: model:ir.model.fields,help:sale.field_product_template_attribute_value__html_color
msgid ""
"Here you can set a\n"
"        specific HTML color index (e.g. #ff0000) to display the color if the\n"
"        attribute type is 'Color'."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__hide_expense_policy
msgid "Hide Expense Policy"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_payment_acquirer_wizard
msgid "How your customers can confirm an order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__id
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma__id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "Auðkenni"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread
msgid "If checked new messages require your attention."
msgstr "Ef merkt við þá eru ný skilaboð"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "If checked, new messages require your attention."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."

#. module: sale
#: code:addons/sale/controllers/portal.py:280
#, python-format
msgid ""
"If we store your payment information on our server, subscription payments "
"will be made automatically."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."

#. module: sale
#: code:addons/sale/models/product_template.py:89
#, python-format
msgid "Import Template for Products"
msgstr ""

#. module: sale
#: code:addons/sale/models/product_template.py:92
#, python-format
msgid "Import Template for Products (with several prices)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "Tekjureikningur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:198
#, python-format
msgid "Invalid order"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:794
#, python-format
msgid "Invalid token found! Token acquirer %s != %s"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:797
#, python-format
msgid "Invalid token found! Token partner %s != %s"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_invoice
msgid "Invoice"
msgstr "Reikningur"

#. module: sale
#: code:addons/sale/models/account_invoice.py:62
#, python-format
msgid "Invoice %s paid"
msgstr "Invoice %s paid"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Reikningur sendist til"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Invoice Confirmed"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Invoice Created"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_line
msgid "Invoice Line"
msgstr "Invoice Line"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Reikningslínur"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Invoice Order"
msgstr "Invoice Order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Invoice Sales Order"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
msgid "Invoice Status"
msgstr "Invoice Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__partner_invoice_id
msgid "Invoice address for current sales order."
msgstr "Invoice address for current sales order."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""

#. module: sale
#: selection:res.config.settings,default_invoice_policy:0
msgid "Invoice what is delivered"
msgstr ""

#. module: sale
#: selection:res.config.settings,default_invoice_policy:0
msgid "Invoice what is ordered"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines"
msgstr "Invoiceable lines"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines (deduct down payments)"
msgstr "Invoiceable lines (deduct down payments)"

#. module: sale
#: selection:sale.report,state:0
msgid "Invoiced"
msgstr "Reikningsfært"

#. module: sale
#: code:addons/sale/models/sale.py:1061
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Invoiced Quantity"
msgstr "Invoiced Quantity"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Reikningsfært í mánuðinum"

#. module: sale
#: selection:crm.team,dashboard_graph_model:0
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Reikningar"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Reikningagreining"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Invoices Statistics"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."

#. module: sale
#: code:addons/sale/models/sales_team.py:96
#, python-format
msgid "Invoices: Untaxed Total"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Reikningagerð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Regla við reikningagerð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing address:"
msgstr "Reikningur sendist til:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and shipping address:"
msgstr "Invoicing and shipping address:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Is Follower"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_value__is_custom
#: model:ir.model.fields,field_description:sale.field_product_template_attribute_value__is_custom
msgid "Is custom value"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is expired"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:1083
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"

#. module: sale
#: code:addons/sale/models/sale.py:667
#, python-format
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_order_confirmation_state:0
#: selection:res.company,sale_onboarding_sample_quotation_state:0
#: selection:res.company,sale_quotation_onboarding_state:0
msgid "Just done"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
msgid "Last 365 Days"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value____last_update
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales____last_update
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma____last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order____last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard____last_update
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator____last_update
#: model:ir.model.fields,field_description:sale.field_sale_report____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__write_date
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Late Activities"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:24
#, python-format
msgid ""
"Let's create a new quotation.<br/><i>Note that colored buttons usually point"
" to the next logical actions.</i>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "Lock"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__auto_done_setting
msgid "Lock Confirmed Sales"
msgstr ""

#. module: sale
#: selection:sale.order,state:0
msgid "Locked"
msgstr "Læst"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage delivery dates from sales orders"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_sale_order_dates
msgid "Manage delivery dates from sales orders."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: sale
#: selection:sale.order.line,qty_delivered_method:0
msgid "Manual"
msgstr "Manual"

#. module: sale
#: selection:product.template,service_type:0
msgid "Manually set quantities on order"
msgstr "Manually set quantities on order"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Margins"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Minnispunktar fyrir sölupöntun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Message for Sales Order Line"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Skilaboð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_name
msgid "Method"
msgstr "Greiðslumáti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale
#: sql_constraint:sale.order.line:0
msgid "Missing required fields on accountable sale order line."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__multi_sales_price
msgid "Multiple Sales Prices per Product"
msgstr ""

#. module: sale
#: selection:res.config.settings,multi_sales_price_method:0
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "Multiple prices per product (e.g. customer segments, currencies)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Activities"
msgstr "Mínir vinnuliðir"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "My Orders"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "My Sales Order Lines"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signed_by
msgid "Name of the person that signed the SO."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:131 code:addons/sale/models/sale.py:367
#: code:addons/sale/models/sale.py:369 code:addons/sale/models/sale.py:371
#: selection:sale.report,state:0
#, python-format
msgid "New"
msgstr "Nýtt"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "New Quotation"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Samantekt næstu virkni"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: sale
#: selection:product.template,expense_policy:0
msgid "No"
msgstr "No"

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "No Message"
msgstr "Engin skilaboð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_order_confirmation_state:0
#: selection:res.company,sale_onboarding_sample_quotation_state:0
#: selection:res.company,sale_quotation_onboarding_state:0
msgid "Not done"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: selection:sale.order.line,display_type:0
msgid "Note"
msgstr "Athugasemd"

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Nothing to Invoice"
msgstr "Ekkert til að reikningsfæra"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Fjöldi aðgerða"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Fjöldi skilaboð sem bíða afgreiðslu"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Number of unread messages"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.brand_promotion
msgid "Odoo"
msgstr ""

#. module: sale
#: model:product.template,description_sale:sale.product_product_1_product_template
msgid "Office chairs can harm your floor: protect it."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:65
#, python-format
msgid "Once your quotation is ready, you can save, print or send it by email."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:32
#, python-format
msgid "Only Integer Value should be valid."
msgstr "Only Integer Value should be valid."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.brand_promotion
msgid "Open Source CRM"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.optional_product_items
msgid "Option not available"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__optional_product_ids
msgid "Optional Products"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_template__optional_product_ids
msgid ""
"Optional Products are suggested whenever the customer hits *Add to Cart* "
"(cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Options"
msgstr "Valmöguleikar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Pöntun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Pöntun nr."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:51
#: code:addons/sale/controllers/portal.py:103
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "Dags. pöntunar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Pantanalínur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr "Pöntunarnúmer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__name
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Tilvísun í Pöntun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Order Status"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:201
#, python-format
msgid "Order is not in a state requiring customer signature."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:215
#, python-format
msgid "Order signed by %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Order to Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Qty"
msgstr "Pantað magn"

#. module: sale
#: code:addons/sale/models/sale.py:1057
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Ordered Quantity"
msgstr "Pantað magn"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Ordered quantities"
msgstr "Pantað magn"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Pantanir"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Orders to Invoice"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Viðbótarsölur á pantanir"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:13
#: code:addons/sale/static/src/js/tour.js:18
#, python-format
msgid "Organize your sales activities with the <b>Sales Management app</b>."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Information"
msgstr "Aðrar upplýsingar"

#. module: sale
#: selection:sale.order,activity_state:0
msgid "Overdue"
msgstr "Overdue"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Paid"
msgstr "Greitt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__partner_id
msgid "Partner"
msgstr "Viðskipta aðili"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__country_id
msgid "Partner Country"
msgstr "Partner Country"

#. module: sale
#: code:addons/sale/controllers/portal.py:277
#, python-format
msgid "Pay & Confirm"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay &amp; Confirm"
msgstr ""

#. module: sale
#: code:addons/sale/models/payment.py:160
#, python-format
msgid "Pay Now"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay now"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr ""

#. module: sale
#: selection:sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with PayPal"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_payment_method:0
#: selection:sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with another payment acquirer"
msgstr ""

#. module: sale
#: selection:sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Pay with credit card (via Stripe)"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_payment_method:0
msgid "PayPal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_email_account
msgid "PayPal Email ID"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Payment Acquirer"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Payment Method"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Greiðsluskilmálar"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_seller_account
msgid "Paypal Merchant ID"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__paypal_pdt_token
msgid "Paypal PDT Token"
msgstr ""

#. module: sale
#: selection:crm.team,dashboard_graph_model:0
msgid "Pipeline"
msgstr "Pípan"

#. module: sale
#: selection:sale.order,activity_state:0
msgid "Planned"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:450
#, python-format
msgid "Please define an accounting sales journal for this company."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:1318
#, python-format
msgid ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."
msgstr ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Posted"
msgstr "Bókað"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.brand_promotion
msgid "Powered by"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
msgid "Price"
msgstr "Verð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce
msgid "Price Reduce"
msgstr "Price Reduce"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Price Reduce Tax excl"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Price Reduce Tax inc"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_subtotal
msgid "Price Subtotal"
msgstr ""

#. module: sale
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "Price computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_pricelist
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Verðlisti"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "Pricelist for current sales order."
msgstr "Verðlisti fyrir þessa sölupöntun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__sale_pricelist_setting
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Verðlistar"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__multi_sales_price_method
msgid "Pricelists Method"
msgstr ""

#. module: sale
#: selection:res.config.settings,multi_sales_price_method:0
msgid "Prices computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Pricing"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Print"
msgstr "Prenta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Pro-forma Invoices"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_product_configurator__product_template_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Vara"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_template_attribute_value
msgid "Product Attribute Value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Vöruflokkur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_image
msgid "Product Image"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_qty
msgid "Product Quantity"
msgstr "Magn vöru"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__product_tmpl_id
msgid "Product Template"
msgstr "Sniðmát vöru"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "Vöruafbrigði"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Vöruafbrigði"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Vörur"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "Proforma Report"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Magn"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Qty Delivered"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Magn reikningsfært"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Qty To Invoice"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "Magn"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:262
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#: selection:sale.order,state:0
#, python-format
msgid "Quotation"
msgstr "Tilboð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Pöntunarnúmer"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "Quotation / Order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr "Númer tilboðs"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Quotation Sent"
msgstr "Tilboð sent"

#. module: sale
#: sql_constraint:res.company:0
msgid "Quotation Validity is required and must be greater than 0."
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Quotation confirmed"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Quotation sent"

#. module: sale
#: code:addons/sale/controllers/portal.py:159
#, python-format
msgid "Quotation viewed by customer"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:101
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.model.fields,field_description:sale.field_crm_team__use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Quotations"
msgstr "Tilboð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Quotations Analysis"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Tilboð og Sölur"

#. module: sale
#: selection:product.attribute,type:0
msgid "Radio"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Policy"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:52
#: code:addons/sale/controllers/portal.py:104
#, python-format
msgid "Reference"
msgstr "Tilvísun"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request."
msgstr "Vísun í skjalið sem bjó til þessa sölupöntun"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__remaining_validity_days
msgid "Remaining Validity Days"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
msgid "Remove one"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr "Skýrslur"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:359
#, python-format
msgid "Requested date is too soon."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order
msgid "Sale Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
msgid "Sale Order Count"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_acquirer_onboarding_wizard
msgid "Sale Payment acquire onboarding wizard"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_product_configurator
msgid "Sale Product Configurator"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
msgid "Sale order line"
msgstr ""

#. module: sale
#: selection:crm.team,dashboard_graph_model:0
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Sales"
msgstr "Sala"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Sales Advance Payment Invoice"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Sölugreining"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_crm_team
msgid "Sales Channels"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Sales Done"
msgstr "Sales Done"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Information"
msgstr "Söluupplýsingar"

#. module: sale
#: code:addons/sale/models/sale.py:262
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: selection:sale.order,state:0 selection:sale.report,state:0
#, python-format
msgid "Sales Order"
msgstr "Sölupöntun"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Pöntun staðfest"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Sales Order Line"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Sölupöntunarlínur"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Sölupantanalínur til að reikningsfæra"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Sales Order Lines related to a Sales Order of mine"

#. module: sale
#: code:addons/sale/models/payment.py:133
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:102
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
#, python-format
msgid "Sales Orders"
msgstr "Sölupantanir"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Söluhópur"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Söluhópur"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
msgid "Sales Warnings"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
msgid "Sales by Channel"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "Sales by Channel (All in One)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_pivot
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
msgid "Sales by Channel Analysis"
msgstr ""

#. module: sale
#: selection:product.template,expense_policy:0
msgid "Sales price"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:94
#, python-format
msgid "Sales: Untaxed Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Söluaðili"

#. module: sale
#: code:addons/sale/models/res_company.py:60
#, python-format
msgid "Sample Order Line"
msgstr ""

#. module: sale
#: code:addons/sale/models/res_company.py:57
#, python-format
msgid "Sample Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Söluleit"

#. module: sale
#: selection:sale.order.line,display_type:0
msgid "Section"
msgstr "Section"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr ""

#. module: sale
#: selection:product.attribute,type:0
msgid "Select"
msgstr "Select"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:46
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is paid"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_onboarding_sample_quotation
msgid "Send a sample quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Senda með tölvupósti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Runa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__use_invoices
msgid "Set Invoicing Target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set a default validity on your quotations"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:25
#, python-format
msgid "Set an invoicing target: "
msgstr "Set an invoicing target: "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set specific billing and shipping addresses"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Set to Quotation"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Stillingar"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Shipping Costs"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping Information"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Show all records which has next action date is before today"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr ""

#. module: sale
#: selection:product.pricelist,discount_policy:0
msgid "Show public price & discount to the customer"
msgstr "Show public price & discount to the customer"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show standard terms &amp; conditions on orders"
msgstr ""

#. module: sale
#: selection:res.company,sale_onboarding_payment_method:0
#: selection:sale.payment.acquirer.onboarding.wizard,payment_method:0
msgid "Sign online"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Signature"

#. module: sale
#: code:addons/sale/controllers/portal.py:203
#, python-format
msgid "Signature is missing."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__signature
msgid "Signature received through the portal."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed by"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Upprunaskjal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:53
#: code:addons/sale/controllers/portal.py:105
#, python-format
msgid "Stage"
msgstr "Staða"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Staða"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: sale
#: model:product.template.attribute.value,name:sale.product_template_attribute_value_3
#: model:product.template.attribute.value,name:sale.product_template_attribute_value_4
msgid "Steel"
msgstr ""

#. module: sale
#: selection:sale.order.line,qty_delivered_method:0
msgid "Stock Moves"
msgstr "Stock Moves"

#. module: sale
#: selection:res.company,sale_onboarding_payment_method:0
msgid "Stripe"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_publishable_key
msgid "Stripe Publishable Key"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_acquirer_onboarding_wizard__stripe_secret_key
msgid "Stripe Secret Key"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Alls"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Target of invoice revenue for the current month. This is the amount the "
"sales channel estimates to be able to invoice this month."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_by_group
msgid "Tax amount by group"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Taxes"
msgstr "VSK"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Taxes used for deposits"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__sale_note
msgid "Terms & Conditions"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Terms &amp; Conditions"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Skilmálar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Terms and conditions... (note: you can setup default ones in the "
"Configuration menu)"
msgstr ""

#. module: sale
#: code:addons/sale/models/analytic.py:116
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is cancelled. You "
"cannot register an expense on a cancelled Sales Order."
msgstr ""

#. module: sale
#: code:addons/sale/models/analytic.py:115
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s is currently locked. "
"You cannot register an expense on a locked Sales Order. Please create a new "
"SO linked to this Analytic Account."
msgstr ""

#. module: sale
#: code:addons/sale/models/analytic.py:111
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account %s must be validated "
"before registering expenses."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The amount to be invoiced in advance, taxes excluded."
msgstr "The amount to be invoiced in advance, taxes excluded."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "The analytic account related to a sales order."

#. module: sale
#: code:addons/sale/models/sale.py:360
#, python-format
msgid ""
"The commitment date is sooner than the expected date.You may be unable to "
"honor the commitment date."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__dashboard_graph_model
msgid "The graph this channel will display in the Dashboard.\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:154
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:152
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:80
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "The value of the down payment amount must be positive."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"There are two ways to manage pricelists: 1) Multiple prices per product: "
"must be set in the Sales tab of the product detail form. 2) Price computed "
"from formulas: must be set in the pricelist form."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:76
#, python-format
msgid ""
"There is no income account defined for this product: \"%s\". You may have to"
" install a chart of account from Accounting app, settings menu."
msgstr ""
"There is no income account defined for this product: \"%s\". You may have to"
" install a chart of account from Accounting app, settings menu."

#. module: sale
#: code:addons/sale/models/sale.py:549 code:addons/sale/models/sale.py:553
#, python-format
msgid ""
"There is no invoiceable line. If a product has a Delivered quantities "
"invoicing policy, please make sure that a quantity has been delivered."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"These are orders with products invoiced based on ordered quantities,\n"
"                in the case you have delivered more than what was ordered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure
msgid "This combination does not exist."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model:res.groups,comment:sale.group_sale_order_dates
msgid ""
"This option introduces extra fields in the sales order to easily schedule "
"product deliveries on your own: expected date, commitment date, effective "
"date."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."

#. module: sale
#: selection:sale.order.line,qty_delivered_method:0
msgid "Timesheets"
msgstr "Timesheets"

#. module: sale
#: selection:product.template,service_type:0
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "To Invoice"
msgstr "Reikningsfæra"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""

#. module: sale
#: selection:sale.order,activity_state:0
msgid "Today"
msgstr "Í dag"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Aðgerðir dagsins"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales__price_total
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Samtals"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_pivot
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Total Price"
msgstr "Samtals verð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Samtals skattar í upphæð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_configurator_configure_optional_products
msgid "Total:"
msgstr "Samtals:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Track Service"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Transactions"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_attribute__type
msgid "Type"
msgstr "Gerð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:734
#, python-format
msgid "Uncategorized"
msgstr "Uncategorized"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Einingarverð"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Eining"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Mælieiningar"

#. module: sale
#: model:product.product,uom_name:sale.advance_product_0
#: model:product.product,uom_name:sale.product_product_4e
#: model:product.product,uom_name:sale.product_product_4f
#: model:product.template,uom_name:sale.advance_product_0_product_template
#: model:product.template,uom_name:sale.product_product_1_product_template
#: model:product.template,uom_name:sale.product_product_4e_product_template
msgid "Unit(s)"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Mælieiningar"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread
msgid "Unread Messages"
msgstr "Ólesin skilaboð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Teljari fyrir ólesin skilaboð"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Upphæð án skatta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Untaxed Total"

#. module: sale
#: code:addons/sale/models/sale.py:402
#, python-format
msgid ""
"Upsell <a href='#' data-oe-model='%s' data-oe-id='%d'>%s</a> for customer <a"
" href='#' data-oe-model='%s' data-oe-id='%s'>%s</a>"
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Upselling Opportunity"
msgstr "Tækifæri til aukinnar sölu"

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:75
#, python-format
msgid "Use the breadcrumbs to <b>go back to preceeding screens</b>."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:79
#, python-format
msgid "Use this menu to access quotations, sales orders and customers."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "User entered custom product attribute values"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Validity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__validity_date
msgid ""
"Validity date of the quotation, after this date, the customer won't be able "
"to validate the quotation online."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Rúmmál"

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "Warning"
msgstr "Viðvörun"

#. module: sale
#: code:addons/sale/models/sale.py:1417
#, python-format
msgid "Warning for %s"
msgstr "Warning for %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Warning on the Sales Order"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Warning when Selling this Product"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Skilaboð frá vef"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Website communication history"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
msgid "What do you want to invoice?"
msgstr "What do you want to invoice?"

#. module: sale
#: selection:res.company,sale_onboarding_payment_method:0
msgid "Wire Transfer"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/tour.js:29
#, python-format
msgid ""
"Write the name of your customer to create one on the fly, or select an "
"existing one."
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:32
#, python-format
msgid "Wrong value entered!"
msgstr "Wrong value entered!"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__partner_id
#: model:ir.model.fields,help:sale.field_sale_order_line__order_partner_id
msgid "You can find a customer by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:268
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:1491
#, python-format
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"                or check every order and invoice them one by one."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_acquirer__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you purchase,\n"
"                    whether it's a physical product, a consumable or services."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "loka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "day"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dagar"

#. module: sale
#: model:product.product,weight_uom_name:sale.advance_product_0
#: model:product.product,weight_uom_name:sale.product_product_4e
#: model:product.product,weight_uom_name:sale.product_product_4f
#: model:product.template,weight_uom_name:sale.advance_product_0_product_template
#: model:product.template,weight_uom_name:sale.product_product_1_product_template
#: model:product.template,weight_uom_name:sale.product_product_4e_product_template
msgid "kg"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:106
#, python-format
msgid "sale order"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr ""
