# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_transfer
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "<strong>Communication: </strong>"
msgstr "<strong>資訊：</strong>"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable QR Codes"
msgstr "啟用QR Codes"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "通過電匯付款時啟用QR Codes"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "沒有找到匹配參考 %s 的交易。"

#. module: payment_transfer
#: model_terms:ir.ui.view,arch_db:payment_transfer.transfer_transaction_status
msgid "Or scan me with your banking app."
msgstr "或者用您的銀行應用掃瞄我。"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "付款收單方"

#. module: payment_transfer
#: model:ir.model,name:payment_transfer.model_payment_transaction
msgid "Payment Transaction"
msgstr "付款交易"

#. module: payment_transfer
#: model:ir.model.fields,field_description:payment_transfer.field_payment_acquirer__provider
msgid "Provider"
msgstr "服務商"

#. module: payment_transfer
#: model:ir.model.fields,help:payment_transfer.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr "與此收單行一起使用的支付服務供應商"

#. module: payment_transfer
#: code:addons/payment_transfer/models/payment_transaction.py:0
#, python-format
msgid "The customer has selected %(acq_name)s to make the payment."
msgstr "客戶已選擇 %(acq_name)s 進行付款。"

#. module: payment_transfer
#: model:ir.model.fields.selection,name:payment_transfer.selection__payment_acquirer__provider__transfer
msgid "Wire Transfer"
msgstr "電匯"
