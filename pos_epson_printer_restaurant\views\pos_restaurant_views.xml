<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_restaurant_printer_iot_form" model="ir.ui.view">
        <field name="name">pos.restaurant.iot.config.form.view</field>
        <field name="model">restaurant.printer</field>
        <field name="inherit_id" ref="pos_restaurant.view_restaurant_printer_form"/>
        <field name="arch" type="xml">
            <field name="printer_type" position="after">
                <field name="epson_printer_ip" attrs="{'invisible': [('printer_type', '!=', 'epson_epos')]}"/>
            </field>
        </field>
    </record>
</odoo>
