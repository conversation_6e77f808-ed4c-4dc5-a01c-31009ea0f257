# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr "오호 정말 귀엽지만 알다시피 봇은 그런 식으로 작동하지 않아요. 날 너무 인간적으로 대하시네요! 프로답게 행동하자구요 ❤️"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "비활성화"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion Channel"
msgstr "메일 및 채팅 채널"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr "이메일 스레드"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Great! 👍<br/>To access special commands, <b>start your sentence with</b> "
"<span class=\"o_odoobot_command\">/</span>. Try getting help."
msgstr ""
"잘하셨습니다! 👍<br/>특수 명령어를 사용하시려면, <b>입력 시</b> <span "
"class=\"o_odoobot_command\">/</span>를 사용하여 시작하시면 됩니다. 편리하게 사용해보세요."

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_channel.py:0
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji</b> <span "
"class=\"o_odoobot_command\">:)</span>"
msgstr ""

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "Hmmm..."
msgstr "음..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I am a simple bot, but if that's a dog, he is the cutest 😊 "
"<br/>Congratulations, you finished this tour. You can now <b>close this chat"
" window</b>. Enjoy discovering Odoo."
msgstr ""
"저는 단순한 봇일 뿐이지만, 만약 강아지라면 정말 귀엽겠지요 😊 <br/>축하합니다, 투어를 완료하셨습니다. 이제 <b>채팅 창을 "
"닫으셔도 됩니다</b>. 마음껏 Odoo를 즐기시기 바랍니다."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr "죄송하지만 이해가 안 돼요. 죄송합니다!"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask: "
"<span class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"해당 질문에 대답할 수 있을 정도로 제가 똑똑하지는 않습니다.<br/>제 안내를 따르시려면, 이렇게 질문해주세요: <span "
"class=\"o_odoobot_command\">투어 시작하기</span>."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr "대기"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr "메일 봇"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji: <b>type</b> <span "
"class=\"o_odoobot_command\">:)</span> and press enter."
msgstr ""
"꼭 그렇지만은 않습니다. 투어를 계속하시려면 이모티콘을 입력해주세요. <b>이렇게 입력하시고:</b><span "
"class=\"o_odoobot_command\">:)</span> 엔터를 누르세요."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr "초기화되지 않음"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">/</span> and wait for the propositions. Select "
"<span class=\"o_odoobot_command\">help</span> and press enter"
msgstr ""
"잘 이해하지 못했습니다. <span class=\"o_odoobot_command\">/</span>을 입력하신 후 제안을 드릴 동안 "
"기다려주시기 바랍니다. <span class=\"o_odoobot_command\">도움 요청</span> 선택 후 엔터를 누르세요."

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "Odoo봇 상태"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr "Odoo봇 실패"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachement"
msgstr "신입사원 연수 프로그램 첨부파일"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr "온보딩 명령"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr "온보딩 이모지"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr "온보딩 핑"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write: <span "
"class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"죄송하지만 너무 졸리네요. 그게 아니라면, 인간 언어에 대해서 제가 가진 무지함을 숨기려는 것일 수도 있겠습니다...<br/><span "
"class=\"o_odoobot_command\">투어 시작하기</span>를 입력하시면 각종 기능에 대해서 안내드리겠습니다."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write <span class=\"o_odoobot_command\">@OdooBot</span> and select me."
msgstr ""
"죄송하지만 듣지 못했습니다. 누군가 관심을 가지게 하려면, <b>연락하시면 됩니다</b>. <span "
"class=\"o_odoobot_command\">@OdooBot</span>을 입력한 후 저를 선택해주세요."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr "그건 좋지 않아요! 저는 봇이지만 감정이 있어요... 💔"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"To <b>send an attachment</b>, click on the <i class=\"fa fa-paperclip\" "
"aria-hidden=\"true\"></i> icon and select a file."
msgstr ""
"<b>첨부 파일을 전송</b>하려면, <i class=\"fa fa-paperclip\" aria-hidden=\"true\"></i> "
"아이콘을 클릭한 후 파일을 선택하세요."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr "시작하려면 이모지 :)를 보내주세요"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">our "
"documentation</a> or <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">our videos</a>."
msgstr ""
"안타깝지만 저는 그냥 봇일 뿐입니다 😞 제대로 이해하지 못했습니다. 저희 제품에 대해서 안내가 필요하시다면, <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">관련 문서</a> 또는 "
"<a href=\"https://www.odoo.com/slides\" target=\"_blank\">관련 동영상</a>을 확인해주시기"
" 바랍니다."

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "Users"
msgstr "사용자"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone with @username to grab their "
"attention. <b>Try to ping me using</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> in a sentence."
msgstr ""
"정말 자연스럽습니다!<br/>누군가에게 연락을 하고 싶으신 경우 @사용자이름 을 사용하시면 됩니다. <b>저에게 한번</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span>이 포함된 문장을 입력해서 연락해보세요."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>Now, try <b>sending an attachment</b>, like a picture"
" of your cute dog..."
msgstr ""
"네, 여기 있습니다! 🎉 <br/>이제 <b>첨부 문서 보내기</b>를 해보겠습니다. 귀여운 강아지 사진같은 문서를 전송해보도록 "
"하겠습니다."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "fuck"
msgstr "제길."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "help"
msgstr "도움말"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "i love you"
msgstr "사랑해요"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "love"
msgstr "사랑"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "start the tour"
msgstr "둘러보기 시작"
