.nav {
  list-style-type: none;
  text-align: center;
  margin: 0;
  padding: 0;
  display: table;
  margin-right: auto;
  margin-left: auto;
}

.nav li {
  display: inline-block;
  font-size: 20px;
  padding: 20px;
}

.styled-table {
  text-align: right;
  border-collapse: collapse;
  margin: 25px 0;
  font-size: 0.9em;
  min-width: 400px;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.styled-table thead tr {
  background-color: #9fc5e8;
  color: #f3f6f4;
  text-align: right;
  font-weight: bold;
}

.styled-table th,
.styled-table td {
  text-align: right;
  height: 4px;
  padding: 4px 6px;
}

.styled-table tbody tr {
  border-bottom: 1px solid #dddddd;
}

/* .styled-table tbody tr:nth-of-type(even) {*/
/*   background-color: #f3f3f3;*/
/* }*/

.styled-table tbody tr:last-of-type {
  border-bottom: 2px solid #009879;
}

.styled-table tbody tr.active-row {
  font-weight: bold;
  color: #009879;
}
