<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record id="website_crm_partner_assign.res_partner_grade_platinium" model="res.partner.grade">
            <field name="is_published" eval="True" />
        </record>
        <record id="website_crm_partner_assign.res_partner_grade_gold" model="res.partner.grade">
            <field name="is_published" eval="True" />
        </record>
        <record id="website_crm_partner_assign.res_partner_grade_silver" model="res.partner.grade">
            <field name="is_published" eval="True" />
        </record>
        <record id="website_crm_partner_assign.res_partner_grade_bronze" model="res.partner.grade">
            <field name="is_published" eval="True" />
        </record>
        <record id="base.partner_demo_portal" model="res.partner">
            <field name="grade_id" ref="website_crm_partner_assign.res_partner_grade_platinium"/>
            <field name="partner_weight">10</field>
        </record>
    </data>
</odoo>
