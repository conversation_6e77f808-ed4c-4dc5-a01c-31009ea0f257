<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data noupdate="1">

    <!-- Standard stages for Maintenance Request -->
    <record id="stage_0" model="maintenance.stage">
        <field name="name">New Request</field>
        <field name="sequence" eval="1" />
        <field name="fold" eval="False" />
    </record>
    <record id="stage_1" model="maintenance.stage">
        <field name="name">In Progress</field>
        <field name="sequence" eval="2" />
        <field name="fold" eval="False" />
    </record>
    <record id="stage_3" model="maintenance.stage">
        <field name="name">Repaired</field>
        <field name="sequence" eval="3" />
        <field name="fold" eval="True" />
        <field name="done" eval="True" />
    </record>
    <record id="stage_4" model="maintenance.stage">
        <field name="name">Scrap</field>
        <field name="sequence" eval="4" />
        <field name="fold" eval="True" />
        <field name="done" eval="True" />
    </record>

</data>
</odoo>
