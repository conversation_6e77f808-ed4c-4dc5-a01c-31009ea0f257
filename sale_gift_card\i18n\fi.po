# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_gift_card
# 
# Translators:
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_gift_card
#: model:mail.template,body_html:sale_gift_card.mail_template_gift_card
msgid ""
"<div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                Here is your gift card!\n"
"            </div>\n"
"            <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                <img src=\"/gift_card/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\"/>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                    <strong t-out=\"format_amount(object.initial_amount, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                    <strong>Gift Card Code</strong>\n"
"                </p>\n"
"                <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Card expires <t t-out=\"format_date(object.expired_date) or ''\">05/05/2021</t></h3>\n"
"            </div>\n"
"            <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                    <a t-attf-href=\"{{ object.buy_line_id.order_id.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Use it right now!</a>\n"
"                </span>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                Tässä on lahjakorttisi!\n"
"            </div>\n"
"            <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                <img src=\"/gift_card/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\"/>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                    <strong t-out=\"format_amount(object.initial_amount, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                    <strong>Lahjakortin koodi</strong>\n"
"                </p>\n"
"                <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"            </div>\n"
"            <div style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Kortti vanhenee <t t-out=\"format_date(object.expired_date) or ''\">05/05/2021</t></h3>\n"
"            </div>\n"
"            <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                    <a t-attf-href=\"{{ object.buy_line_id.order_id.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Käytä sitä heti!</a>\n"
"                </span>\n"
"            </div>\n"
"        "

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.sale_purchased_gift_card
msgid "<span class=\"fa fa-clipboard\"/> Copy"
msgstr "<span class=\"fa fa-clipboard\"/> Kopioi"

#. module: sale_gift_card
#: model:ir.model.fields,field_description:sale_gift_card.field_sale_order_line__generated_gift_card_ids
msgid "Bought Gift Card"
msgstr "Ostettu Lahjakortti"

#. module: sale_gift_card
#: model:ir.model.fields,field_description:sale_gift_card.field_gift_card__buy_line_id
msgid "Buy Line"
msgstr "Osto Linja"

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.used_gift_card
msgid "Code:"
msgstr "Koodi:"

#. module: sale_gift_card
#: model:ir.model.fields,help:sale_gift_card.field_sale_order_line__gift_card_id
msgid "Deducted from this Gift Card"
msgstr "Vähennetty tästä lahjakortista"

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.used_gift_card
msgid "Expired Date:"
msgstr "Vanhentunut päivämäärä:"

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.sale_purchased_gift_card
msgid "Gift #"
msgstr "Lahja #"

#. module: sale_gift_card
#: model:ir.model,name:sale_gift_card.model_gift_card
#: model:ir.model.fields,field_description:sale_gift_card.field_sale_order_line__gift_card_id
msgid "Gift Card"
msgstr "Lahjakortti"

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.sale_purchased_gift_card
msgid "Gift Card Code"
msgstr "Lahjakortin Koodi"

#. module: sale_gift_card
#: model:ir.model.fields,field_description:sale_gift_card.field_sale_order__gift_card_count
msgid "Gift Card Count"
msgstr "Lahjakorttien Määrä"

#. module: sale_gift_card
#: code:addons/sale_gift_card/models/sale_order.py:0
#, python-format
msgid "Gift Card already used."
msgstr "Lahjakortti on jo käytetty."

#. module: sale_gift_card
#: code:addons/sale_gift_card/models/sale_order.py:0
#, python-format
msgid "Gift Card are restricted for another user."
msgstr "Lahjakortti on rajoitettu toisen käyttäjän käyttöön."

#. module: sale_gift_card
#: model:mail.template,name:sale_gift_card.mail_template_gift_card
msgid "Gift Card: Send by Email"
msgstr "Lahjakortti: Lähetä sähköpostilla"

#. module: sale_gift_card
#: model:ir.actions.act_window,name:sale_gift_card.gift_card_sale_order_action
#: model_terms:ir.ui.view,arch_db:sale_gift_card.sale_order_view_extend_gift_card_form
msgid "Gift Cards"
msgstr "Lahjakortit"

#. module: sale_gift_card
#: code:addons/sale_gift_card/models/sale_order.py:0
#, python-format
msgid "Invalid or Expired Gift Card."
msgstr "Virheellinen tai vanhentunut lahjakortti."

#. module: sale_gift_card
#: model:ir.model.fields,field_description:sale_gift_card.field_gift_card__redeem_line_ids
msgid "Redeems"
msgstr "Lunastus"

#. module: sale_gift_card
#: model:ir.model.fields,help:sale_gift_card.field_gift_card__buy_line_id
msgid "Sale Order line where this gift card has been bought."
msgstr "Myyntirivi, josta tämä lahjakortti on ostettu."

#. module: sale_gift_card
#: model:ir.model,name:sale_gift_card.model_sale_order
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: sale_gift_card
#: model:ir.model,name:sale_gift_card.model_sale_order_line
msgid "Sales Order Line"
msgstr "Myyntitilausrivi"

#. module: sale_gift_card
#: model_terms:ir.ui.view,arch_db:sale_gift_card.sale_purchased_gift_card
msgid ""
"You will find below your gift cards code. An email has been sent with it. "
"You can use it starting right now."
msgstr ""
"Alta löydät lahjakorttisi koodin. Sähköposti on lähetetty joka sisältää "
"koodin. Voit käyttää sitä heti."

#. module: sale_gift_card
#: model:mail.template,subject:sale_gift_card.mail_template_gift_card
msgid "Your Gift Card"
msgstr "Lahjakorttisi"
