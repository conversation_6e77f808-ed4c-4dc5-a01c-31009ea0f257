// ------------------------------------------------------------------
// Layout
// ------------------------------------------------------------------

.o_ThreadView {
    display: flex;
    position: relative;
    flex-flow: column;
    min-height: 0;
}

.o_ThreadView_bottomPart {
    min-height: 0;
}

.o_ThreadView_channelMemberList {
    width: $o-mail-chat-sidebar-width;
}

.o_ThreadView_composer {
    flex: 0 0 auto;
}

.o_ThreadView_core {
    min-width: 0;
}

.o_ThreadView_alertLoadingFailed {
    display: flex;
    align-items: center;
}

.o_ThreadView_loading {
    display: flex;
    align-self: center;
    flex: 1 1 auto;
    align-items: center;
}

.o_ThreadView_loadingFailed {
    display: flex;
    flex: 1 1 auto;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.o_ThreadView_messageList {
    flex: 1 1 auto;
}

// ------------------------------------------------------------------
// Style
// ------------------------------------------------------------------

.o_ThreadView {
    background-color: gray('100');
}

.o_ThreadView_loadingIcon {
    margin-right: map-get($spacers, 1);
}
