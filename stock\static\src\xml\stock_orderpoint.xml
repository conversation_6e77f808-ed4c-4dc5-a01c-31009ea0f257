<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
    <div t-name="stock.leadDaysPopOver">
        <h2>Lead Times</h2>
        <hr/>
        <p>
            The forecasted stock on the <b><t t-esc="lead_days_date"/></b>
            is <t t-if="qty_to_order &lt;= 0"><b><t t-esc="qty_forecast"/> <t t-esc="product_uom_name"/></b></t><t t-else="">
            below the inventory <b>minimum of <t t-esc="product_min_qty"/> <t t-esc="product_uom_name"/>
            : <t t-esc="qty_to_order"/> <t t-esc="product_uom_name"/> should be replenished</b> to reach the maximum of
            <t t-esc="product_max_qty"/> <t t-esc="product_uom_name"/>.</t>
        </p>
        <table t-if="lead_days_description" class="table table-borderless table-sm">
            <tbody>
                <tr>
                    <td>
                        Today
                    </td>
                    <td class="text-right">
                        <t t-out="today"/>
                    </td>
                </tr>
                <tr t-foreach="lead_days_description" t-as="descr">
                    <td><t t-out="descr[0]"/></td>
                    <td class="text-right"><t t-out="descr[1]"/></td>
                </tr>
                <tr class="table-info">
                    <td>
                        Forecasted Date
                    </td>
                    <td class="text-right text-nowrap">
                        = <t t-out="lead_days_date"/>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <t t-name="StockOrderpoint.Buttons">
        <span>
            <button type="button" class="btn d-none btn-primary o_button_order">
                Order
            </button>
            <button type="button" class="btn d-none btn-primary o_button_snooze">
                Snooze
            </button>
        </span>
    </t>

    <div t-name="stock.replenishmentHistory">
        <h2>Sales History</h2>
        <hr/>
        <table t-if="replenishment_history" class="table table-borderless">
            <tbody>
                <t t-foreach="replenishment_history" t-as="line">
                    <tr>
                        <td><t t-esc="line.name"/></td>
                        <td class="text-right text-nowrap" t-esc="line.quantity + ' ' + line.uom_name"/>
                    </tr>
                </t>
            </tbody>
        </table>
    </div>
</templates>
