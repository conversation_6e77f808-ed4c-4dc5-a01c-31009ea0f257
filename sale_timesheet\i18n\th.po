# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON>wu<PERSON><PERSON>awa<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    บันทึกเวลา\n"
"                </p><p>\n"
"                    คุณสามารถลงทะเบียนและติดตามชั่วโมงการทำงานของคุณในแต่ล่ะโปรเจกต์ได้ทุกวัน\n"
"                    ทุกช่วงเวลาที่ใช้ในโปรเจกต์รจะถูกคิดเงินและสามารถออกใบแจ้งหนี้ใหม่ให้แก่\n"
"                    ลูกค้าได้หากจำเป็น\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice___candidate_orders
msgid " Candidate Orders"
msgstr "คำสั่งผู้สมัคร"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_percentage
msgid ""
"% of timesheets that are billable compared to the total number of timesheets"
" linked to the AA of the project, rounded to the unit."
msgstr ""
"% "
"ของใบบันทึกเวลาที่เรียกเก็บเงินได้เมื่อเปรียบเทียบกับจำนวนใบบันทึกเวลาทั้งหมดที่เชื่อมโยงกับ"
" AA ของโปรเจกต์ โดยปัดเศษเป็นหน่วย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "%(amount)s %(label)s will be added to the new Sales Order."
msgstr "%(amount)s %(label)s จะถูกเพิ่มในคำสั่งขายใหม่"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", for a revenue of"
msgstr ", สำหรับรายได้ของ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", leading to a"
msgstr ", นำไปสู่"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for Invoice:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">ใบบันทึกเวลาสำหรับแจ้งหนี้:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order "
"item:</em>"
msgstr ""
"<em class=\"font-weight-normal text-"
"muted\">ใบบันทึกเวลาสำหรับราการสั่งขาย:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order:</em>"
msgstr ""
"<em class=\"font-weight-normal text-"
"muted\">ใบบบันทึกเวลาสำหรับคำสั่งขาย:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"ลูกศร\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr "<span class=\"o_form_label\">การเรียกเก็บเงินตามเวลา</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                        Billable Time\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                        เวลาที่สามารถเรียกเก็บได้\n"
"                        </span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">บันทึกแล้ว</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>แจ้งหนี้แล้ว:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>แจ้งหนี้:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>คำสั่งขาย:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>To invoice:</strong>"
msgstr "<strong>ออกใบแจ้งหนี้:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>การได้กำไร</u>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Sold</u>"
msgstr "<u>ขายแล้ว</u>"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"ตามการกำหนดค่าสินค้า จำนวนที่จัดส่งสามารถคำนวณได้โดยอัตโนมัติโดยใช้กลไก:\n"
"- ด้วยตนเอง: จำนวนถูกตั้งค่าด้วยตนเองในไลน์\n"
" - วิเคราะห์จากค่าใช้จ่าย: จำนวนคือผลรวมจำนวนจากค่าใช้จ่ายที่ลงรายการบัญชี\n"
"- ใบบันทึกเวลา: จำนวนคือผลรวมของชั่วโมงที่บันทึกในงานที่เชื่อมโยงกับรายการขายนี้\n"
"- การย้ายสต๊อก: จำนวนมาจากการเลือกที่ยืนยันแล้ว\n"

#. module: sale_timesheet
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "บริการหลังการขาย"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_profitability_report__other_revenues
msgid ""
"All revenues that are not from timesheets and that are linked to the "
"analytic account of the project."
msgstr ""
"รายได้ทั้งหมดที่ไม่ได้มาจากใบบบันทึกเวลาและเชื่อมโยงกับบัญชีวิเคราะห์ของโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_invoiced
msgid "Amount Invoiced"
msgstr "จำนวนการแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_invoiced
msgid "Amount Re-invoiced"
msgstr "จำนวนการออกใบแจ้งหนี้ใหม่"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_to_invoice
msgid "Amount to Invoice"
msgstr "จำนวนที่จะออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_to_invoice
msgid "Amount to Re-invoice"
msgstr "จำนวนที่จะออกใบแจ้งหนี้ใหม่"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "จำนวนที่จะออกใบ้แจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"ไม่สามารถเลือกพนักงานได้มากกว่าหนึ่งครั้งในแผนผัง "
"โปรดลบรายการที่ซ้ำกันและลองใหม่อีกครั้ง"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__analytic_account_id
msgid "Analytic Account"
msgstr "บัญชีวิเคราะห์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "ไลน์การวิเคราะห์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "ไลน์การวิเคราะห์"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "At least one line should be filled."
msgstr "ควรกรอกข้อมูลอย่างน้อยหนึ่งบรรทัด"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_manual
msgid "Based on Milestones"
msgstr "อิงตามเหตุการณ์สำคัญ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_timesheet
msgid "Based on Timesheets"
msgstr "อิงตามใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__allow_billable
msgid "Billable"
msgstr "สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_percentage
msgid "Billable Percentage"
msgstr "เปอร์เซ็นต์ที่เรียกเก็บเงินได้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Billable Time"
msgstr "เวลาที่เรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billable Type"
msgstr "ประเภทการเรียกเก็บเงิน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "เรียกเก็บเงินในราคาคงที่"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "เรียกเก็บเงินในราคาคงที่"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "เรียกเก็บเงินตามใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "ทำรายการบิล"

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_1
#: model:project.task,legend_blocked:sale_timesheet.project_task_2
#: model:project.task,legend_blocked:sale_timesheet.project_task_3
#: model:project.task,legend_blocked:sale_timesheet.project_task_internal
msgid "Blocked"
msgstr "บล็อก"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_4
msgid "Buzz or set as done"
msgstr "บัซหรือตั้งค่าว่าเสร็จแล้ว"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "โดยประเภทบิล"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "เลือกคำสั่งขายที่จะออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "หน่วยงานในเชิงพาณิชย์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "พาร์ทเนอร์ทางการค้า"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__company_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__company_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Company"
msgstr "บริษัท"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "กำหนดค่าบริการของคุณ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "ต้นทุน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost_currency_id
msgid "Cost Currency"
msgstr "ต้นทุนสกุลเงิน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Costs"
msgstr "ต้นทุน"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_costs_and_revenues
msgid "Costs and Revenues"
msgstr "ต้นทุนและรายได้"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "สร้างใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "สร้างใบแจ้งหนี้ของโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr "สร้างไลน์ SO จากโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr "สร้าง SO จากโปรเจกต์"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/models/project.py:0
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
#, python-format
msgid "Create Sales Order"
msgstr "สร้างคำสั่งขาย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "สร้างคำสั่งขายจากโปรเจกต์"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create a Sales Order"
msgstr "สร้างคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__partner_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Customer"
msgstr "ลูกค้า"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_order_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "ฝ่ายดูแลลูกค้า (ชั่วโมงที่จ่ายล่วงหน้า)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "การให้คะแนนลูกค้า"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr "ลูกค้าของคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__line_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Date"
msgstr "วันที่"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "วันที่สังแล้ว,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "วันคงเหลือ)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Sales Order Item"
msgstr "ค่าเริ่มต้นรายการคำสั่งขาย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Service"
msgstr "ค่าเริ่มต้นบริการ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__display_create_order
msgid "Display Create Order"
msgstr "แสดงการสร้างคำสั่ง"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Effective"
msgstr "มีประสิทธิภาพ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "พนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "อัตราพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr "พนักงานที่มีใบบันทึกเวลาในโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Employee/Sale Order Item Mapping:\n"
" Defines to which sales order item an employee's timesheet entry will be linked.By extension, it defines the rate at which an employee's time on the project is billed."
msgstr ""
"แผนผังพนักงาน/รายการคำสั่งขาย:\n"
"กำหนดการเชื่อมโยงรายการในคำสั่งขายและรายการใบบันทึกเวลาของพนักงาน โดยส่วนขยายจะกำหนดอัตราเวลาของพนักงานในโปรเจกต์ที่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "วันสิ้นสุด"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "ราคาค่าบริการคงที่"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Group By"
msgstr "จัดกลุ่มตาม"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__has_displayed_warning_upsell
msgid "Has Displayed Warning Upsell"
msgstr "ได้แสดงคำเตือนการเพิ่มยอดขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "มีหลากหลาย Sol"

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.product,uom_name:sale_timesheet.product_service_order_timesheet
#: model:product.product,uom_name:sale_timesheet.time_product
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_1_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_2_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_order_timesheet_product_template
#: model:product.template,uom_name:sale_timesheet.time_product_product_template
msgid "Hours"
msgstr "ชั่วโมง"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ไอดี"

#. module: sale_timesheet
#: model:project.task,legend_normal:sale_timesheet.project_task_1
#: model:project.task,legend_normal:sale_timesheet.project_task_2
#: model:project.task,legend_normal:sale_timesheet.project_task_3
#: model:project.task,legend_normal:sale_timesheet.project_task_4
#: model:project.task,legend_normal:sale_timesheet.project_task_internal
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__info_invoice
msgid "Info Invoice"
msgstr "ข้อมูลใบเสนอราคา"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Invoice"
msgstr "การแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "นโยบายการออกใบแจ้งหนี้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity) on projects or tasks you'll"
" create later on."
msgstr ""
"ใบแจ้งหนี้ตามใบบันทึกเวลา (จำนวนที่จัดส่ง) "
"ของโปรเจกต์หรืองานที่คุณจะสร้างขึ้นในภายหลัง"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a project for "
"the order with a task for each sales order line to track the time spent."
msgstr ""
"ใบแจ้งหนี้ตามใบบันทึกเวลา (จำนวนที่จัดส่ง) "
"และสร้างโปรเจกต์สำหรับคำสั่งที่มีงานสำหรับไลน์คำสั่งขายแต่ละรายการเพื่อติดตามเวลาที่ใช้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a task in an "
"existing project to track the time spent."
msgstr ""
"ออกใบแจ้งหนี้ตามใบบันทึกเวลา (จำนวนที่จัดส่ง) "
"และสร้างงานในโปรเจกต์ที่มีอยู่เพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create an empty "
"project for the order to track the time spent."
msgstr ""
"ใบแจ้งหนี้ตามใบบันทึกเวลา (จำนวนที่จัดส่ง) "
"และสร้างโปรเจกต์ที่ว่างสำหรับคำสั่งเพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "ใบแจ้งหนี้ที่สร้างจากใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,help:sale_timesheet.field_project_task__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "ออกใบแจ้งหนี้เวลาและวัสดุของคุณจากงาน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoice your time and material to customers"
msgstr "ออกใบแจ้งหนี้เวลาและวัสดุของคุณให้กับลูกค้า"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "การแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "ออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Invoicing Policy"
msgstr "นโยบายการแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__invoicing_timesheet_enabled
msgid "Invoicing Timesheet Enabled"
msgstr "เปิดใช้งานการออกใบแจ้งหนี้ใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__is_cost_changed
msgid "Is Cost Manually Changed"
msgstr "เป็นการเปลี่ยนแปลงต้นทุนด้วยตนเอง"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "แผนผังโครงการว่างเปล่า"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "มีการแก้ไขรายการสั่งซื้อด้วยตนเอง"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "รายการบันทึก"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "รายการบันทึก"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "สถาปนิกรุ่นเยาว์ (แจ้งหนี้ในใบบันทึกเวลา)"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_manual
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "การประกอบครัว (เหตุการณ์สำคัญ)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งล่าสุดเมื่อ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr "ไลน์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"กำหนดปริมาณตามคำสั่งซื้อด้วยตนเอง: ใบแจ้งหนี้ตามปริมาณที่ป้อนด้วยตนเอง โดยไม่ต้องสร้างบัญชีวิเคราะห์\n"
"ใบบันทึกเวลาในสัญญา: ใบแจ้งหนี้ตามชั่วโมงที่ติดตามบนใบบันทึกเวลาที่เกี่ยวข้อง\n"
"สร้างงานและติดตามชั่วโมง: สร้างงานในการตรวจสอบคำสั่งขายและติดตามชั่วโมงทำงาน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__margin
#, python-format
msgid "Margin"
msgstr "อัตราส่วน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "วิธีการอัปเดตจำนวนที่จัดส่ง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "บริการหลักที่สำคัญ"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "My Projects"
msgstr "โปรเจกต์ของฉัน"

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_4
msgid "Need functional or technical help"
msgstr "ต้องการความช่วยเหลือด้านการทำงานหรือด้านเทคนิค"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "ไม่พบกิจกรรม"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "ยังไม่มีข้อมูล!"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable"
msgstr "ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
msgid "Non Billable Tasks"
msgstr "งานที่ไม่สามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_count
msgid "Number of timesheets"
msgstr "จำนวนใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"เฉพาะใบบันทึกเวลาที่ยังไม่ได้ออกใบแจ้งหนี้ (และผ่านการตรวจสอบ หากมี) "
"จากช่วงเวลานี้เท่านั้นที่จะถูกออกใบแจ้งหนี้ หากไม่มีการระบุช่วงเวลา "
"ใบบันทึกเวลาทั้งหมดที่ยังไม่ได้ออกใบแจ้งหนี้ (และผ่านการตรวจสอบ หากมี) "
"จะถูกออกใบแจ้งหนี้โดยไม่มีส่วนต่าง"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Operation not supported"
msgstr "ไม่รองรับการทำงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "ข้อมูลอ้างอิงคำสั่ง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Ordered,"
msgstr "สั่งแล้ว,"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_cost
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
msgid "Other Costs"
msgstr "ต้นทุนอื่น ๆ "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
msgid "Other Revenues"
msgstr "รายได้อื่น ๆ "

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"เปอร์เซ็นต์ของเวลาที่จัดส่งเมื่อเปรียบเทียบกับจำนวนเงินที่ชำระล่วงหน้าซึ่งจะต้องมีพอสำหรับกิจกรรมโอกาสการขายต่อยอดที่จะถูกเรียกใช้"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Planned"
msgstr "วางแผน"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__ordered_timesheet
msgid "Prepaid/Fixed Price"
msgstr "ชำระล่วงหน้า/ราคาคงที่"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "ราคา"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__product_id
msgid "Product"
msgstr "สินค้า"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""
"สินค้าของรายการคำสั่งขาย ต้องเป็นบริการที่ออกใบแจ้งหนี้ตามใบบันทึกเวลางาน"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Profitability"
msgstr "การทำกำไร"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_graph
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Profitability Analysis"
msgstr "วิเคราะห์การทำทำกำไร"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project"
msgstr "โปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__company_id
msgid "Project Company"
msgstr "โครงการบริษัท"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__currency_id
msgid "Project Currency"
msgstr "สกุลเงินโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project Manager"
msgstr "ผู้จัดการโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_profitability_report
msgid "Project Profitability Report"
msgstr "รายงานการทำกำไรของโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "ไลน์การขายโปรเจกต์ แผนผังพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "เทมเพลตโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "การอัปเดตโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr "โครงการที่เรากำลังสร้างคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "อัตราโปรเจกต์"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "โปรเจกต์ที่จะทำให้เรียกเก็บเงินได้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Rating"
msgstr "การให้คะแนน"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_1
#: model:project.task,legend_done:sale_timesheet.project_task_2
#: model:project.task,legend_done:sale_timesheet.project_task_3
#: model:project.task,legend_done:sale_timesheet.project_task_internal
msgid "Ready"
msgstr "พร้อม"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Remaining"
msgstr "เหลืออยู่"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Days on SO"
msgstr "วันที่คงเหลือบน SO"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Days on SO:"
msgstr "วันที่คงเหลือบน SO:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "ชั่วโมงคงเหลือที่มีอยู่"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Hours on SO"
msgstr "ชั่วโมงคงเหลือบน SO"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Hours on SO:"
msgstr "ชั่วโมงคงเหลือบน SO:"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Remaining)"
msgstr "คงเหลือ)"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Revenues"
msgstr "รายได้"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"ตรวจสอบใบบันทึกเวลาของคุณตามประเภทการเรียกเก็บเงิน "
"และตรวจสอบให้แน่ใจว่าเวลาของคุณสามารถเรียกเก็บเงินได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_order_id
msgid "Sale Order"
msgstr "คำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__so_analytic_account_id
msgid "Sale Order Analytic Account"
msgstr "บัญชีวิเคราะห์คำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
msgid "Sale Order Item"
msgstr "รายการคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_line_id
msgid "Sale Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr "ไลน์การขาย/แผนผังพนักงาน"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "ใบแจ้งหนี้การชำระเงินล่วงหน้าการขาย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
#, python-format
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__order_confirmation_date
msgid "Sales Order Confirmation Date"
msgstr "วันที่ยืนยันคำสั่งขาย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Sales Order Item"
msgstr "รายการคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity."
msgstr ""
"ไลน์คำสั่งขายกำหนดเหตุการณ์สำคัญของโปรเจกต์เพื่อออกใบแจ้งหนี้โดยการตั้งค่าจำนวนที่จัดส่ง"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"รายการคำสั่งขายกำหนดเหตุการณ์สำคัญของโปรเจกต์เพื่อออกใบแจ้งหนี้โดยการตั้งค่าจำนวนที่จัดส่ง"
" "
"สร้างโปรเจกต์สำหรับคำสั่งที่มีงานในไลน์คำสั่งขายแต่ละรายการเพื่อติดตามเวลาที่ใช้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a task in an existing project to track the time "
"spent."
msgstr ""
"ไลน์คำสั่งขายกำหนดเหตุการณ์สำคัญของโปรเจกต์เพื่อออกใบแจ้งหนี้โดยการตั้งค่าปริมาณที่จัดส่ง"
" สร้างงานในโปรเจกต์ที่มีอยู่เพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create an empty project for the order to track the time"
" spent."
msgstr ""
"ไลน์คำสั่งขายกำหนดเหตุการณ์สำคัญของโปรเจกต์เพื่อออกใบแจ้งหนี้โดยการตั้งค่าจำนวนที่จัดส่ง"
" สร้างโปรเจกต์ที่ว่างสำหรับคำสั่งเพื่อติดตามเวลาที่ใช้ไป"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "คำสั่งขายที่มีการเชื่อมโยงงาน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "ค้นหาในใบแจ้งหนี้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "ค้นหาในคำสั่งขาย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "ค้นหาในรายการคำสั่งขาย"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Select a Service product with which you would like to bill your time spent "
"on tasks."
msgstr "เลือกสินค้าบริการที่คุณต้องการเรียกเก็บเงินตามเวลาที่ใช้ไปกับงานนั้น"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"เลือกโปรเจกต์ที่เรียกเก็บเงินได้ซึ่งสามารถสร้างงานได้ "
"จำเป็นต้องตั้งค่านี้สำหรับแต่ละบริษัท"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"เลือกโปรเจกต์ที่เรียกเก็บเงินได้เพื่อเป็นโครงร่างของโปรเจกต์ที่สร้างขึ้นใหม่เมื่อขายสินค้าปัจจุบัน"
" ขั้นตอนและงานจะถูกทำซ้ำ"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr "เลือกโปรเจกต์ที่เรียกเก็บเงินไม่ได้ซึ่งสามารถสร้างงานได้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "ขายบริการและออกใบแจ้งหนี้ตามเวลาที่ใช้"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "สถาปนิกอาวุโส (ใบแจ้งหนี้ในบนใบบันทึกเวลา)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Service"
msgstr "บริการ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "นโยบายการแจ้งหนี้บริการ"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "รายได้บริการ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold_ratio
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold_ratio
msgid "Service Upsell Threshold Ratio"
msgstr "อัตราส่วนเริ่มต้นการขายต่อยอดบริการ"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.time_product
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheet"
msgstr "บริการบนใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
msgid "Services"
msgstr "บริการ"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Sold"
msgstr "ขาย"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "วันที่เริ่ม"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount Invoiced"
msgstr "ผลรวมที่จะออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount to Invoice"
msgstr "จำนวนที่จะออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Margin"
msgstr "ผลรวมของอัตราส่วน"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Timesheet Cost"
msgstr "ผลรวมของต้นทุนใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__task_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Task"
msgstr "งาน"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "การเกิดซ้ำของงาน"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "อัตรางาน"

#. module: sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Tasks"
msgstr "งาน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"%s สินค้าที่จำเป็นสำหรับแอประบบบันทึกเวลาและไม่สามารถเก็บถาวรหรือลบได้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""
"ไม่สามารถสร้างคำสั่งขายได้เนื่องจากคุณไม่ได้ป้อนพนักงานบางคนที่ระบุในใบบันทึกเวลาของโปรเจกต์นี้ โปรดระบุพนักงานที่เกี่ยวข้องทั้งหมดก่อนที่จะสร้างคำสั่งขาย\n"
"พนักงานขาดหายไป: %s"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__so_analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "บัญชีการวิเคราะห์ที่เกี่ยวข้องกับคำสั่งขาย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "The cost of the project is now at"
msgstr "ต้นทุนของโปรเจกต์อยู่ที่"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project has already a sale order."
msgstr "โปรเจกต์มีคำสั่งขายแล้ว"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already linked to a sales order item."
msgstr "โปรเจกต์ที่เชื่อมโยงกับรายการคำสั่งขาย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""
"ไม่สามารถสร้างคำสั่งขายได้เนื่องจากใบบันทึกเวลาบางรายการของโปรเจกต์นี้เชื่อมโยงกับคำสั่งขายอื่นแล้ว"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr "คำสั่งขายที่เลือกควรมีบางอย่างสำหรับออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"อัตรางานนั้นสมบูรณ์แบบถ้าคุณต้องการเรียกเก็บเงินบริการต่าง ๆ "
"ให้กับลูกค้าที่แตกต่างกันในอัตราที่ต่างกัน "
"อัตราคงที่จะสมบูรณ์แบบถ้าคุณเรียกเก็บเงินค่าบริการในอัตราคงที่ต่อชั่วโมงหรือต่อวันโดยไม่คำนึงถึงพนักงานที่ดำเนินการ"
" อัตราพนักงานจะสำคัญถ้าพนักงานของคุณให้บริการเดียวกันในอัตราที่แตกต่างกัน "
"ตัวอย่างเช่น ที่ปรึกษาระดับเริ่มต้นและอาวุโสจะให้บริการเดียวกัน (= "
"การให้คำปรึกษา) แต่ในอัตราที่แตกต่างกันเนื่องจากระดับความอาวุโส"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default timesheet cost in employee's HR "
"Settings"
msgstr ""
"ค่าใช้จ่ายนี้จะแทนที่ต้นทุนใบบันทึกเวลาเริ่มต้นของพนักงานในการตั้งค่า HR "
"ของพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"ซึ่งจะกำหนดหน่วยวัดที่ใช้ในการเข้ารหัสใบบันทึกเวลา ซึ่งจะมอบเครื่องมือ\n"
"        และวิดเจ็ตเพื่อช่วยในการเข้ารหัส การรายงานทั้งหมดจะยังคงแสดงเป็นหน่วยชั่วโมง (ค่าเริ่มต้น)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "เกณฑ์"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "บริการตามเวลา"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
msgid "Timesheet"
msgstr "การบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_cost
msgid "Timesheet Cost"
msgstr "ต้นทุนใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "ต้นทุนใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_unit_amount
msgid "Timesheet Duration"
msgstr "ใบบันทึกเวลาระยะเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "หน่วยเข้ารหัสใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "ใบบันทึกเวลาสินค้า"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "ใบบันทึกเวลารายได้"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "ใบบันทึกเวลาระยะเวลาทั้งหมด"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr "ใบบันทึกเวลากิจกรรม"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "ใบบันทึกเวลาที่เกี่ยวข้องกับการขายนี้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
#, python-format
msgid "Timesheets"
msgstr "การบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "ช่วงเวลาใบบันทึกเวลา"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "ใบบันทึกเวลาตามประเภทการเรียกเก็บเงิน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets of %s"
msgstr "การบันทึกเวลาของ %s"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "ใบบันทึกเวลาในโปรเจกต์(หนึ่งค่าต่อ SO/โปรเจกต์)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "ใบบันทึกเวลาที่นำมาพิจารณาในการออกใบแจ้งหนี้เวลาของคุณ"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "ใบบันทึกเวลาที่ใช้ไปเพื่อออกใบแจ้งหนี้"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Total"
msgstr "รวม"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Total Sold"
msgstr "ขายแล้วทั้งหมด"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"ยอดรวมในการออกใบแจ้งหนี้ในคำสั่งขาย รวมถึงรายการทั้งหมด (บริการ "
"สินค้าที่สามารถเก็บได้ ค่าใช้จ่าย ...)"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr "ระยะเวลาที่บันทึกไว้ทั้งหมด แสดงในการเข้ารหัส UoM และปัดเศษเป็นหน่วย"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Total:"
msgstr "ทั้งหมด:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "ติดตามบริการ"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"ติดตามชั่วโมงทำงานของคุณตามโปรเจกต์ทุกวัน "
"และออกใบแจ้งหนี้ของเวลานี้ให้กับลูกค้าของคุณ"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "ราคาต่อหน่วย"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr "ราคาต่อหน่วยของรายการในคำสั่งขาย"

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_manual
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Units"
msgstr "หน่วย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Value does not exist in the pricing type"
msgstr "ไม่มีค่าในประเภทการกำหนดราคา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "ดูใบบันทึกเวลา"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "เตือนพนักงานขายให้ทำยอดขายเพิ่มขึ้นเมื่อมีงานเสร็จเกิน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__warning_employee_rate
msgid "Warning Employee Rate"
msgstr "คำเตือนอัตราพนักงาน"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr "ตัวช่วย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "You can only apply this action from a project."
msgstr "คุณสามารถใช้การดำเนินการนี้ได้เฉพาะจากโปรเจ็กต์เท่านั้น"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"คุณไม่สามารถเชื่อมโยงโปรเจกต์ที่เรียกเก็บเงินได้กับรายการคำสั่งขายที่มาจากค่าใช้จ่ายหรือใบเรียกเก็บเงินของผู้จัดจำหน่าย"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"คุณไม่สามารถเชื่อมโยงโปรเจกต์ที่เรียกเก็บเงินกับรายการคำสั่งขายที่ไม่ใช่บริการได้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot modify timesheets that are already invoiced."
msgstr "คุณไม่สามารถแก้ไขใบบันทึกเวลาที่ออกใบแจ้งหนี้แล้ว"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "คุณไม่สามารถลบใบบันทึกเวลาที่ออกใบแจ้งหนี้ไปแล้วได้"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid "day"
msgstr "วัน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "days"
msgstr "วัน"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "hours"
msgstr "ชั่วโมง"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "margin ("
msgstr "อัตราส่วน ("

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold. ("
msgstr "ของชั่วโมงที่ขายได้ ("
