# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone


class OverTimeReport(models.TransientModel):
    _name = "hr.over.time.report"

    date_from = fields.Date(string='تاريخ من', required=True)
    date_to = fields.Date(string='تاريخ الى', required=True)

    only_approved_by_hr = fields.Boolean(string='فقط التي تمت الموافقة عليه من قبل الموارد البشرية')

    def get_report(self):
        if self.only_approved_by_hr:
            all_requests = self.env['hr.masarat.overtime.line'].search([('state','=','hr_approval'),
                                                                   ('overtime_date','>=',self.date_from),
                                                                   ('overtime_date','<=',self.date_to)])
        else:
            all_requests = self.env['hr.masarat.overtime.line'].search([('overtime_date','>=',self.date_from),
                                                                        ('overtime_date','<=',self.date_to)])
        employee_dict = {}
        for elem in all_requests:
            employee_dict.setdefault(str(elem.employee_id.id),{
                'name':elem.employee_id.name,
                'holidays':0,
                'at_work':0,
                'at_home':0,
                'total':0
            })

            if elem.overtime_type == 'holidays':
                employee_dict[str(elem.employee_id.id)]['holidays']+=elem.overtime_hours
            elif elem.overtime_type == 'at_work':
                employee_dict[str(elem.employee_id.id)]['at_work']+=elem.overtime_hours
            elif elem.overtime_type == 'at_home':
                employee_dict[str(elem.employee_id.id)]['at_home']+=elem.overtime_hours

        employee_list = []
        for each_employee in employee_dict.keys():
            total_calc = (employee_dict[each_employee]['holidays'] *3)+(employee_dict[each_employee]['at_work'] *2)+(employee_dict[each_employee]['at_home'] *1)
            employee_list.append({
                 'name':str(employee_dict[each_employee]['name']),
                 'holidays': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['holidays'] * 60, 60)),
                 'at_work': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['at_work'] * 60, 60)),
                 'at_home': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['at_home'] * 60, 60)),
                 'calc_holidays': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['holidays'] *3 * 60, 60)),
                 'calc_at_work': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['at_work'] *2 * 60, 60)),
                 'calc_at_home': '{0:02.0f}:{1:02.0f}'.format(*divmod(employee_dict[each_employee]['at_home'] * 60, 60)),
                 'total_calc':'{0:02.0f}:{1:02.0f}'.format(*divmod(total_calc * 60, 60))})

        data = {
            'date_from':str(self.date_from),
            'date_to':str(self.date_to),
            'employee_list':employee_list
        }
        return self.sudo().env.ref('hr_approvales_masarat.hr_masarat_over_time_report_x1').report_action(self, data=data)