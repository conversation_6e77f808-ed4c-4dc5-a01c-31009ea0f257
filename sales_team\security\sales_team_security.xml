<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="base.module_category_sales_sales" model="ir.module.category">
            <field name="description">Helps you handle your quotations, sale orders and invoicing.</field>
            <field name="sequence">1</field>
        </record>

        <record id="group_sale_salesman" model="res.groups">
            <field name="name">User: Own Documents Only</field>
            <field name="category_id" ref="base.module_category_sales_sales"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">the user will have access to his own data in the sales application.</field>
        </record>

        <record id="group_sale_salesman_all_leads" model="res.groups">
            <field name="name">User: All Documents</field>
            <field name="category_id" ref="base.module_category_sales_sales"/>
            <field name="implied_ids" eval="[(4, ref('group_sale_salesman'))]"/>
            <field name="comment">the user will have access to all records of everyone in the sales application.</field>
        </record>

        <record id="group_sale_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="comment">the user will have an access to the sales configuration as well as statistic reports.</field>
            <field name="category_id" ref="base.module_category_sales_sales"/>
            <field name="implied_ids" eval="[(4, ref('group_sale_salesman_all_leads'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <record model="ir.ui.menu" id="sales_team.menu_sale_config">
            <field name="name">Configuration</field>
            <field eval="[(6,0,[ref('base.group_system')])]" name="groups_id"/>
        </record>

    <data noupdate="1">
        <record id="crm_rule_all_salesteam" model="ir.rule">
            <field name="name">All Salesteam</field>
            <field ref="sales_team.model_crm_team" name="model_id"/>
            <field name="domain_force">[(1,'=',1)]</field>
            <field name="groups" eval="[(4, ref('sales_team.group_sale_salesman_all_leads'))]"/>
        </record>

        <record model="ir.rule" id="sale_team_comp_rule">
            <field name="name">Sales Team multi-company</field>
            <field name="model_id" ref="model_crm_team"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4,ref('sales_team.group_sale_manager'))]"/>
        </record>
    </data>
</odoo>
