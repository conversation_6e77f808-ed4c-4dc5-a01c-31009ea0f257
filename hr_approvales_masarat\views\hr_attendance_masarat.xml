<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_attendance_tree_x1" model="ir.ui.view">
        <field name="name">hr.attendance.tree.x1</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="after">
                <field name="attendance_type_id" invisible="0" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='check_in']" position="before">
                <field name="attendance_date" invisible="0"/>
                <field name="dayofweek" invisible="1"/>
                <field name="dayofweek_is_workingday" invisible="1"/>
                <field name="globle_time_off" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='check_in']" position="after">
                <field name="there_is_letancy" invisible="1"/>
                <field name="checkin_tolarence" invisible="1"/>
                <field name="checkout_tolarence" invisible="1"/>
                <field name="total_working_hour" invisible="1"/>
                <field name="checkin_latency" invisible="0" decoration-danger="checkin_latency != 0" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='check_out']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='check_out']" position="after">
                <field name="checkout_early" invisible="0" decoration-danger="checkout_early != 0" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='worked_hours']" position="after">
                <field name="less_than_workinghour" invisible="0" decoration-danger="less_than_workinghour != 0" optional="show"/>
                <field name="computed_latency_note" invisible="0" optional="show"/>
                <field name="computed_latency" invisible="0" decoration-danger="computed_latency != 0" optional="show"/>
            </xpath>
        </field>
    </record>


    <record id="hr_attendance_view_filter_x1" model="ir.ui.view">
        <field name="name">hr.attendance.search.x1</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='groupby_check_out']" position="after">
                <filter string="نوع الدوام" name="groupby_attendance_type" context="{'group_by': 'attendance_type_id'}"/>
                <filter string="ٍالملاحظة" name="groupby__note" context="{'group_by': 'computed_latency_note'}"/>
            </xpath>
        </field>
    </record>

</odoo>