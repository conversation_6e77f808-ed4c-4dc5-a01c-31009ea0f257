# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Noor E <PERSON> Sid<PERSON>, 2023\n"
"Language-Team: Bengali (https://app.transifex.com/odoo/teams/41243/bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>Buy a card reader"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "বারকোড নিয়ম"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Cancel"
msgstr "বাতিল"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Could not read card"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "দ্বারা সৃষ্টি"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "তৈরি"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "প্রদর্শন নাম"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/ProductScreen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Handling transaction..."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "আইডি "

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +1 (800) 846-4472\n"
"                                to create one."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration____last_update
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction____last_update
msgid "Last Modified on"
msgstr "সর্বশেষ সংশোধিত"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "সর্বশেষ আপডেট করেছেন"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "সর্বশেষ আপডেট হয়েছে"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "নাম"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr ""

#. module: pos_mercury
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Ok"
msgstr "বেশ"

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentTransactionPopup.js:0
#, python-format
msgid "Online Payment"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Partially approved"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Pay with: "
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS অর্ডার"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "পয়েন্ট অফ সেল মূল্যপরিশোধ পদ্ধতি"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "পয়েন্ট অফ সেল মূল্যপরিশোধ"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr ""

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Refunds not supported"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Sending reversal..."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "The name of the card owner"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Transaction approved"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "ধরণ"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr ""

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr ""

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/js/PaymentScreen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/PaymentScreenPaymentLines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr ""

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""

#. module: pos_mercury
#. openerp-web
#: code:addons/pos_mercury/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "X______________________________"
msgstr ""
