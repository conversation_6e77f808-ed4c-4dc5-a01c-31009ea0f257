# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * event_booth
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Email</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Mobile</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Name</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Phone</b>:"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
msgid "<span class=\"o_stat_text\">Booths</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span class=\"text-white\">4m²</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span class=\"text-white\">8m²</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 Branded Booth</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 desk</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>10 + 1 passes</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>100 words description on website</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 Branded Booth</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 desks</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 x 46\" display screens</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>46\" display screen</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "<span>50 words description on website</span>"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>Logo &amp; link on website</span>"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction
msgid "Action Needed"
msgstr "Intervenție necesară"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__active
msgid "Active"
msgstr "Activ"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activitate Excepție Decorare"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_search
msgid "Archived"
msgstr "Arhivat"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__available
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Available"
msgstr "Disponibil"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count_available
msgid "Available Booths"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_00_event_7
#: model:event.booth,name:event_booth.event_booth_0_event_0
msgid "Booth A1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_01_event_7
#: model:event.booth,name:event_booth.event_booth_1_event_0
msgid "Booth A2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_02_event_7
#: model:event.booth,name:event_booth.event_booth_2_event_0
msgid "Booth A3"
msgstr ""

#. module: event_booth
#: model:mail.message.subtype,name:event_booth.mt_event_booth_booked
msgid "Booth Booked"
msgstr ""

#. module: event_booth
#: model:ir.ui.menu,name:event_booth.menu_event_booth_category
msgid "Booth Categories"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_category_action
#: model:ir.model.fields,field_description:event_booth.field_event_booth__booth_category_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__booth_category_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Booth Category"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_event__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Booth Type"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid ""
"Booth categories are used to represent the different types of booths you "
"rent (Premium Booth, Table and Chairs, ...)"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_action
#: model:ir.actions.act_window,name:event_booth.event_booth_action_from_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_type__event_type_booth_ids
#: model:ir.ui.menu,name:event_booth.menu_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_view_form
msgid "Booths"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Booths are the physical stands that you rent during your event."
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
msgid "Create a Booth"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid "Create a Booth Category"
msgstr ""

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Create a Type Booth"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_date
msgid "Created on"
msgstr "Creat în"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__description
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Description"
msgstr "Descriere"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event"
msgstr "Eveniment"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event Booth"
msgstr ""

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth_category
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_ids
msgid "Event Booth Category"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr ""

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type_booth
msgid "Event Booth Template"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_type_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__event_type_id
msgid "Event Category"
msgstr "Categoria Evenimentului"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type
msgid "Event Template"
msgstr "Șablon Eveniment"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Event Type Booth"
msgstr ""

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_type_booth_action
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_tree_from_type
msgid "Event Type Booths"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_follower_ids
msgid "Followers"
msgstr "Persoane interesate"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă minunată pentru font, de ex. fa-sarcini"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_20_event_7
msgid "Gold Booth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_21_event_7
msgid "Gold Booth 2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_22_event_7
msgid "Gold Booth 3"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Group By"
msgstr "Grupează după"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__id
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__id
msgid "ID"
msgstr "ID"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma pentru a indica o activitate de excepție."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction
#: model:ir.model.fields,help:event_booth.field_event_booth__message_unread
msgid "If checked, new messages require your attention."
msgstr "Dacă este selectat, mesajele noi necesită atenția dumneavoastră."

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, unele mesaje au o eroare de livrare."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1920
msgid "Image"
msgstr "Imagine"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1024
msgid "Image 1024"
msgstr "Imagine 1024"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_128
msgid "Image 128"
msgstr "Imagine 128"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_256
msgid "Image 256"
msgstr "Imagine 256"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_512
msgid "Image 512"
msgstr "Imagine 512"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__is_available
msgid "Is Available"
msgstr "Este Valabil"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth____last_update
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category____last_update
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth____last_update
msgid "Last Modified on"
msgstr "Ultima modificare la"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error
msgid "Message Delivery error"
msgstr "Eroare livrare mesaj"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Name"
msgstr "Nume"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_id
msgid "Next Activity Type"
msgstr "Tip de activitate urmatoare"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error_counter
msgid "Number of errors"
msgstr "Numărul de erori"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Număr de mesaje ce necesită intervenție"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_unread_counter
msgid "Number of unread messages"
msgstr "Număr de mesaje necitite"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_10_event_7
msgid "OpenWood Demonstrator 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_11_event_7
msgid "OpenWood Demonstrator 2"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_12_event_7
msgid "OpenWood Demonstrator 3"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "Premium"
msgstr ""

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_premium
msgid "Premium Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_0
msgid "Premium Booth A4"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_2_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_2
msgid "Premium Showbooth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_3
msgid "Premium Showbooth 2"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__partner_id
msgid "Renter"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_email
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Email"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_mobile
msgid "Renter Mobile"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Name"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_phone
msgid "Renter Phone"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_0_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_0
msgid "Showbooth 1"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_1_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_1
msgid "Showbooth 2"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__state
msgid "Shows the availability of a Booth"
msgstr ""

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "Standard"
msgstr "Standard"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_standard
msgid "Standard Booth"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__state
msgid "Status"
msgstr "Stare"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data scadentă este deja trecută\n"
"Astăzi: activității pentru astăzi\n"
"Planificate: activități viitoare."

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count
msgid "Total Booths"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul activității de excepție din înregistrare."

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__unavailable
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Unavailable"
msgstr "Indisponibil"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_unread
msgid "Unread Messages"
msgstr "Mesaje necitite"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contor mesaje necitite"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "VIP"
msgstr "VIP"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_vip
msgid "VIP Booth"
msgstr ""

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_4_event_0
msgid "VIP Booth A5"
msgstr ""

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__website_message_ids
msgid "Website Messages"
msgstr "Mesaje Website"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__website_message_ids
msgid "Website communication history"
msgstr "Istoric comunicare website"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "e.g. First Booth Alley 1"
msgstr ""

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. Premium Booth"
msgstr ""
