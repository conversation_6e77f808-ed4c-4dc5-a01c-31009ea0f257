<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.WelcomeView" owl="1">
        <div class="o_WelcomeView h-100 d-flex flex-column justify-content-center align-items-center bg-light">
            <t t-if="welcomeView">
                <h1 class="font-weight-light">
                    <span t-if="welcomeView.mediaPreview">You've been invited to a meeting!</span>
                    <span t-if="!welcomeView.mediaPreview">You've been invited to a chat!</span>
                </h1>
                <h2 class="m-5" t-esc="messaging.companyName"/>
                <div class="d-flex justify-content-center">
                    <MediaPreview t-if="welcomeView.mediaPreview" class="mr-5" localId="welcomeView.mediaPreview.localId"/>
                    <div class="d-flex flex-column justify-content-center">
                        <t t-if="messaging.currentGuest">
                            <label class="o_WelcomeView_guestNameInputLabel text-center" t-att-for="welcomeView.guestNameInputUniqueId">What's your name?</label>
                            <input class="form-control mb-3" type="text" placeholder="Your name" t-att-name="welcomeView.guestNameInputUniqueId" t-att-id="welcomeView.guestNameInputUniqueId" t-att-value="welcomeView.pendingGuestName" t-ref="guestNameInput" t-on-input="welcomeView.onInputGuestNameInput" t-on-keydown="welcomeView.onKeydownGuestNameInput"/>
                        </t>
                        <t t-if="messaging.currentUser">
                            <p class="o_WelcomeView_loggedAsStatus" t-esc="loggedInAsText"/>
                        </t>
                        <button class="o_WelcomeView_joinButton btn btn-success btn-lg align-self-end p-0 rounded-circle shadow fa fa-phone" title="Join Channel" t-att-disabled="welcomeView.isJoinButtonDisabled" t-on-click="welcomeView.onClickJoinButton"/>
                    </div>
                </div>
            </t>
        </div>
    </t>

</templates>
