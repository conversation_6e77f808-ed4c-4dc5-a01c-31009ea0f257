<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <linearGradient id="linearGradient-1" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-2" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-3" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <linearGradient id="linearGradient-5" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#00A09D"/>
      <stop offset="100%" stop-color="#00E2FF"/>
    </linearGradient>
    <rect id="path-6" width="22" height="2" x="0" y="0"/>
    <filter id="filter-7" width="104.5%" height="200%" x="-2.3%" y="-25%" filterUnits="objectBoundingBox">
      <feOffset dy="1" in="SourceAlpha" result="shadowOffsetOuter1"/>
      <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"/>
      <feColorMatrix in="shadowOffsetOuter1" values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0"/>
    </filter>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_chart">
      <rect width="82" height="60" class="bg"/>
      <g class="group_2" transform="translate(22 22)">
        <rect width="6" height="12" y="5" fill="url(#linearGradient-1)" class="rectangle" rx="1"/>
        <rect width="6" height="7" x="8" y="10" fill="url(#linearGradient-2)" class="rectangle" rx="1"/>
        <rect width="6" height="9" x="16" y="8" fill="url(#linearGradient-3)" class="rectangle" rx="1"/>
        <rect width="6" height="4" x="25" y="13" fill="url(#linearGradient-4)" class="rectangle" rx="1"/>
        <rect width="6" height="13" x="33" y="4" fill="url(#linearGradient-5)" class="rectangle" rx="1"/>
        <g class="rectangle">
          <use fill="#000" filter="url(#filter-7)" xlink:href="#path-6"/>
          <use fill="#FFF" fill-opacity=".95" xlink:href="#path-6"/>
        </g>
      </g>
    </g>
  </g>
</svg>
