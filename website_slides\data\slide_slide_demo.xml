<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">

    <!-- CHANNEL 0: Basics of Gardening -->
    <!-- ================================================== -->
    <record id="slide_slide_demo_0_0" model="slide.slide">
        <field name="name">Gardening: The Know-How</field>
        <field name="sequence">1</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/channel-training-default.jpg"/>
        <field name="slide_type">presentation</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_0_gard_0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">10</field>
        <field name="completion_time">2.5</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_tools')), (4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">A summary of know-how: how and what. All the basics for this course about gardening.</field>
    </record>
        <!--RESOURCE-->
        <record id="slide_slide_demo_0_0_resource_0" model="slide.slide.resource">
            <field name="name">Document</field>
            <field name="data" type="base64" file="website_slides/static/src/img/document.png"/>
            <field name="slide_id" ref="slide_slide_demo_0_0"/>
        </record>
    <record id="slide_slide_demo_0_1" model="slide.slide">
        <field name="name">Home Gardening</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_gardening_1.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_0_gard_0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">5</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful')), (4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">Interesting information about home gardening. Keep it close !</field>
    </record>
    <record id="slide_slide_demo_0_2" model="slide.slide">
        <field name="name">Mighty Carrots</field>
        <field name="sequence">3</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_gardening_2.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_0_gard_0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">2</field>
        <field name="completion_time">2</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful')), (4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">You won't believe those facts about carrots.</field>
    </record>
    <record id="slide_slide_demo_0_3" model="slide.slide">
        <field name="name">How to Grow and Harvest The Best Strawberries | Basics</field>
        <field name="sequence">4</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_l0JZ25VvbwE.jpg"/>
        <field name="slide_type">document</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_0_gard_0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">Here is How to get the Sweetest Strawberries you ever tasted!</field>
    </record>
    <record id="slide_slide_demo_0_4" model="slide.slide">
        <field name="name">Test your knowledge</field>
        <field name="sequence">5</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_owl.jpg"/>
        <field name="slide_type">quiz</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_0_gard_0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="description">Show your newly mastered knowledge !</field>
    </record>
        <record id="slide_slide_demo_0_4_question_0" model="slide.question">
            <field name="question">What is a strawberry ?</field>
            <field name="sequence">1</field>
            <field name="slide_id" ref="slide_slide_demo_0_4"/>
        </record>
        <record id="slide_slide_demo_0_4_question_0_0" model="slide.answer">
            <field name="text_value">A fruit</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct ! A strawberry is a fruit because it's the product of a tree.</field>
            <field name="question_id" ref="slide_slide_demo_0_4_question_0"/>
        </record>
        <record id="slide_slide_demo_0_4_question_0_1" model="slide.answer">
            <field name="text_value">A vegetable</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! A strawberry is not a vegetable.</field>
            <field name="question_id" ref="slide_slide_demo_0_4_question_0"/>
        </record>
        <record id="slide_slide_demo_0_4_question_0_2" model="slide.answer">
            <field name="text_value">A table</field>
            <field name="sequence">3</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! A table is a piece of furniture.</field>
            <field name="question_id" ref="slide_slide_demo_0_4_question_0"/>
        </record>
        <record id="slide_slide_demo_0_4_question_1" model="slide.question">
            <field name="question">What is the best tool to dig a hole for your plants ?</field>
            <field name="sequence">2</field>
            <field name="slide_id" ref="slide_slide_demo_0_4"/>
        </record>
        <record id="slide_slide_demo_0_4_question_1_0" model="slide.answer">
            <field name="text_value">A shovel</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct ! A shovel is the perfect tool to dig a hole.</field>
            <field name="question_id" ref="slide_slide_demo_0_4_question_1"/>
        </record>
        <record id="slide_slide_demo_0_4_question_1_1" model="slide.answer">
            <field name="text_value">A spoon</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! Good luck digging a hole with a spoon...</field>
            <field name="question_id" ref="slide_slide_demo_0_4_question_1"/>
        </record>

    <!-- CHANNEL 1: Taking care of Trees -->
    <!-- ================================================== -->

    <!--                    Categories                      -->
    <record id="slide_category_demo_1_0" model="slide.slide">
        <field name="name">Interesting Facts</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="sequence">0</field>
    </record>
    <record id="slide_category_demo_1_1" model="slide.slide">
        <field name="name">Methods</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="sequence">4</field>
    </record>

    <!--                    Slides                      -->
    <record id="slide_slide_demo_1_0" model="slide.slide">
        <field name="name">Tree Infographic</field>
        <field name="sequence">1</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_infographic_1.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">5</field>
        <field name="completion_time">0.5</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful'))]"/>
        <field name="description">Just some basics Tree Infographic.</field>
    </record>
    <record id="slide_slide_demo_1_1" model="slide.slide">
        <field name="name">Interesting Tree Facts</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_infographic_2.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">5</field>
        <field name="completion_time">1.5</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful'))]"/>
        <field name="description">Just some basics Interesting Tree Facts.</field>
    </record>
    <record id="slide_slide_demo_1_2" model="slide.slide">
        <field name="name">Energy Efficiency Facts</field>
        <field name="sequence">3</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_infographic_3.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">10</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful')), (4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">Just some basics Energy Efficiency Facts.</field>
    </record>
        <record id="slide_slide_demo_1_2_link_0" model="slide.slide.link">
            <field name="name">Energy Efficient Link 1</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_1_2"/>
        </record>
        <record id="slide_slide_demo_1_2_link_1" model="slide.slide.link">
            <field name="name">Energy Efficient Link 2</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_1_2"/>
        </record>
        <!--RESOURCE-->
        <record id="slide_slide_demo_1_2_resource_0" model="slide.slide.resource">
            <field name="name">Presentation</field>
            <field name="data" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
            <field name="slide_id" ref="slide_slide_demo_1_2"/>
        </record>
    <record id="slide_slide_demo_1_3" model="slide.slide">
        <field name="name">How to plant a potted tree</field>
        <field name="sequence">5</field>
        <field name="url">https://www.youtube.com/watch?v=QYmgrw0PgLU</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_QYmgrw0PgLU.jpg"/>
        <field name="document_id">QYmgrw0PgLU</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and Landscaping. Narrated by Leif Knecht, owner.</field>
    </record>
    <record id="slide_slide_demo_1_4" model="slide.slide">
        <field name="name">A little chat with Harry Potted</field>
        <field name="sequence">6</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_img_1.jpg"/>
        <field name="slide_type">webpage</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="html_content" type="html">
<section class="s_cover parallax bg-black-50 pt16 pb16" data-scroll-background-ratio="0" style="background-image: none;" data-snippet="s_cover">
    <span class="s_parallax_bg oe_img_bg" style="background-image: url('/website_slides/static/src/img/slide_demo_tree_img_1.jpg'); background-position: 50% 0;"></span>
    <div class="o_we_bg_filter bg-black-50"/>
    <div class="container">
        <div class="row s_nb_column_fixed">
            <div class="col-lg-12">
                <h1 class="o_default_snippet_text" style="font-size: 62px; text-align: center;">Catchy Headline</h1>
                <p class="lead o_default_snippet_text" style="text-align: center;">Write one or two paragraphs describing your product, services or a specific feature.<br/> To be successful your content needs to be useful to your readers.</p>
                <p>
                    <a href="/contactus" class="btn btn-primary rounded-circle o_default_snippet_text">Contact us</a>
                </p>
            </div>
        </div>
    </div>
</section>
<section class="s_text_image pt32 pb32" data-snippet="s_text_image">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 pt16 pb16">
                <img src="/website_slides/static/src/img/slide_demo_tree_img_1.jpg" class="img img-fluid mx-auto" alt="Odoo • Image and Text"/>
            </div>
            <div class="col-lg-6 pt16 pb16">
                <h2 class="o_default_snippet_text">Section Subtitle</h2>
                <p class="o_default_snippet_text">Write one or two paragraphs describing your product or services. <br/>To be successful your content needs to be useful to your readers.</p>
                <p class="o_default_snippet_text">Start with the customer – find out what they want and give it to them.</p>
                <p class="o_default_snippet_text"><a href="#" class="btn btn-outline-primary">Discover more</a></p>
            </div>
        </div>
    </div>
</section></field>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">5</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful'))]"/>
        <field name="description">We had a little chat with Harry Potted, sure he had interesting things to say !</field>
    </record>
        <record id="slide_slide_demo_1_4_link_0" model="slide.slide.link">
            <field name="name">Know More Link 1</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_1_4"/>
        </record>
        <record id="slide_slide_demo_1_4_link_1" model="slide.slide.link">
            <field name="name">Know More Link 2</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_1_4"/>
        </record>
        <record id="slide_slide_demo_1_4_question_0" model="slide.question">
            <field name="question">Do you think Harry Potted has a good name ?</field>
            <field name="sequence">1</field>
            <field name="slide_id" ref="slide_slide_demo_1_4"/>
        </record>
        <record id="slide_slide_demo_1_4_question_0_0" model="slide.answer">
            <field name="text_value">Yes</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct !</field>
            <field name="question_id" ref="slide_slide_demo_1_4_question_0"/>
        </record>
        <record id="slide_slide_demo_1_4_question_0_1" model="slide.answer">
            <field name="text_value">No</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect !</field>
            <field name="question_id" ref="slide_slide_demo_1_4_question_0"/>
        </record>
        <record id="slide_slide_demo_1_4_question_1" model="slide.question">
            <field name="question">Did you read the whole article ?</field>
            <field name="sequence">2</field>
            <field name="slide_id" ref="slide_slide_demo_1_4"/>
        </record>
        <record id="slide_slide_demo_1_4_question_1_0" model="slide.answer">
            <field name="text_value">Yes</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct ! Congratulations you have time to loose</field>
            <field name="question_id" ref="slide_slide_demo_1_4_question_1"/>
        </record>
        <record id="slide_slide_demo_1_4_question_1_1" model="slide.answer">
            <field name="text_value">No</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! You really should read it.</field>
            <field name="question_id" ref="slide_slide_demo_1_4_question_1"/>
        </record>
        <record id="slide_slide_demo_1_4_question_1_2" model="slide.answer">
            <field name="text_value">What was the question again ?</field>
            <field name="sequence">3</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! Seriously ?</field>
            <field name="question_id" ref="slide_slide_demo_1_4_question_1"/>
        </record>
    <record id="slide_slide_demo_1_5" model="slide.slide">
        <field name="name">3 Main Methodologies</field>
        <field name="sequence">6</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/channel-training-default.jpg"/>
        <field name="slide_type">presentation</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">10</field>
        <field name="completion_time">2.5</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">A summary of know-how: how and what.</field>
    </record>
    <record id="slide_slide_demo_1_6" model="slide.slide">
        <field name="name">How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks</field>
        <field name="sequence">7</field>
        <field name="url">https://www.youtube.com/watch?v=l0JZ25VvbwE</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_l0JZ25VvbwE.jpg"/>
        <field name="document_id">l0JZ25VvbwE</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_1_gard1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">Here is How to get the Sweetest Strawberries you ever tasted!</field>
    </record>

    <!-- CHANNEL 2: Trees, Wood and Garden -->
    <!-- ================================================== -->

    <!--                    Categories                      -->
    <record id="slide_category_demo_2_0" model="slide.slide">
        <field name="name">Trees</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="sequence">0</field>
    </record>
    <record id="slide_category_demo_2_1" model="slide.slide">
        <field name="name">Wood</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="sequence">4</field>
    </record>

    <!--                    Slides                      -->
    <!-- Category: Trees -->
    <record id="slide_slide_demo_2_0" model="slide.slide">
        <field name="name">Main Trees Categories</field>
        <field name="sequence">1</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_img_2.jpg"/>
        <field name="slide_type">presentation</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="quiz_first_attempt_reward">100</field>
        <field name="quiz_second_attempt_reward">75</field>
        <field name="quiz_third_attempt_reward">50</field>
        <field name="quiz_fourth_attempt_reward">25</field>
        <field name="description">A summary of know-how: what are the main trees categories and how to differentiate them.</field>
    </record>
        <!-- LINKS -->
        <record id="slide_slide_demo_2_0_link_0" model="slide.slide.link">
            <field name="name">Trees Classification Link</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_2_0"/>
        </record>
        <record id="slide_slide_demo_2_0_link_1" model="slide.slide.link">
            <field name="name">Main types of trees Link</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_2_0"/>
        </record>
        <!--RESOURCE-->
        <record id="slide_slide_demo_2_0_resource_0" model="slide.slide.resource">
            <field name="name">Tree image</field>
            <field name="data" type="base64" file="website_slides/static/src/img/slide_demo_tree_img_2.jpg"/>
            <field name="slide_id" ref="slide_slide_demo_2_0"/>
        </record>
        <!-- QUIZZ -->
        <record id="slide_slide_demo_2_0_question_0" model="slide.question">
            <field name="question">Do you make beams out of lemon trees ?</field>
            <field name="sequence">1</field>
            <field name="slide_id" ref="slide_slide_demo_2_0"/>
        </record>
        <record id="slide_slide_demo_2_0_question_0_0" model="slide.answer">
            <field name="text_value">Yes</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect !</field>
            <field name="question_id" ref="slide_slide_demo_2_0_question_0"/>
        </record>
        <record id="slide_slide_demo_2_0_question_0_1" model="slide.answer">
            <field name="text_value">No</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct !</field>
            <field name="question_id" ref="slide_slide_demo_2_0_question_0"/>
        </record>
        <record id="slide_slide_demo_2_0_question_1" model="slide.question">
            <field name="question">Do you make lemons out of beams ?</field>
            <field name="sequence">2</field>
            <field name="slide_id" ref="slide_slide_demo_2_0"/>
        </record>
        <record id="slide_slide_demo_2_0_question_1_0" model="slide.answer">
            <field name="text_value">Yes</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect !</field>
            <field name="question_id" ref="slide_slide_demo_2_0_question_1"/>
        </record>
        <record id="slide_slide_demo_2_0_question_1_1" model="slide.answer">
            <field name="text_value">No</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct !</field>
            <field name="question_id" ref="slide_slide_demo_2_0_question_1"/>
        </record>
        <record id="slide_slide_demo_2_0_question_1_2" model="slide.answer">
            <field name="text_value">And also bananas</field>
            <field name="sequence">3</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! of course not ...</field>
            <field name="question_id" ref="slide_slide_demo_2_0_question_1"/>
        </record>
    <record id="slide_slide_demo_2_1" model="slide.slide">
        <field name="name">A Mighty Forest from Ages</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_tree_img_3.jpg"/>
        <field name="slide_type">webpage</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="html_content" type="html">
<section class="s_cover parallax bg-black-50 pt16 pb16" data-scroll-background-ratio="0" style="background-image: none;" data-snippet="s_cover">
    <span class="s_parallax_bg oe_img_bg" style="background-image: url('/website_slides/static/src/img/slide_demo_tree_img_3.jpg'); background-position: 50% 0;"></span>
    <div class="o_we_bg_filter bg-black-50"/>
    <div class="container">
        <div class="row s_nb_column_fixed">
            <div class="col-lg-12">
                <h1 class="o_default_snippet_text" style="font-size: 62px; text-align: center;">Catchy Headline</h1>
                <p class="lead o_default_snippet_text" style="text-align: center;">Write one or two paragraphs describing your product, services or a specific feature.<br/> To be successful your content needs to be useful to your readers.</p>
                <p class="o_default_snippet_text">
                    <a href="/contactus" class="btn btn-primary rounded-circle">Contact us</a>
                </p>
            </div>
        </div>
    </div>
</section>
<section class="s_text_image pt32 pb32" data-snippet="s_text_image">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 pt16 pb16">
                <img src="/website_slides/static/src/img/slide_demo_tree_img_3.jpg" class="img img-fluid mx-auto" alt="Odoo • Image and Text"/>
            </div>
            <div class="col-lg-6 pt16 pb16">
                <h2 class="o_default_snippet_text">Section Subtitle</h2>
                <p class="o_default_snippet_text">Write one or two paragraphs describing your product or services. <br/>To be successful your content needs to be useful to your readers.</p>
                <p class="o_default_snippet_text">Start with the customer – find out what they want and give it to them.</p>
                <p class="o_default_snippet_text">
                    <a href="#" class="btn btn-outline-primary">Discover more</a>
                </p>
            </div>
        </div>
    </div>
</section></field>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">2</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">Mighty forest just don't appear in a few weeks. Learn how time made our forests mighty and mysterious.</field>
    </record>
    <record id="slide_slide_demo_2_2" model="slide.slide">
        <field name="name">Tree planting in hanging bottles on wall</field>
        <field name="sequence">3</field>
        <field name="url">https://www.youtube.com/watch?v=ebBez6bcSEc</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_ebBez6bcSEc.jpg"/>
        <field name="document_id">ebBez6bcSEc</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">How to wall decorating by tree planting in hanging plastic bottles.</field>
    </record>
    <!-- Category: Wood -->
    <record id="slide_slide_demo_2_3" model="slide.slide">
        <field name="name">Wood Characteristics</field>
        <field name="sequence">5</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/channel-documentation-default.jpg"/>
        <field name="slide_type">presentation</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_2_gard2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">2</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_cheatsheet'))]"/>
        <field name="quiz_first_attempt_reward">100</field>
        <field name="quiz_second_attempt_reward">75</field>
        <field name="quiz_third_attempt_reward">50</field>
        <field name="quiz_fourth_attempt_reward">25</field>
        <field name="description">Knowing wood characteristics is a requirement in order to know which kind of wood to use in a given situation.</field>
    </record>


    <!-- CHANNEL 3: Choose your wood !           -->
    <!-- ======================================= -->

    <!--                 Categories              -->
    <record id="slide_category_demo_3_0" model="slide.slide">
        <field name="name">Working with Wood</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_3_furn0"/>
        <field name="sequence">1</field>
    </record>

    <!--                    Slides                      -->
    <record id="slide_slide_demo_3_0" model="slide.slide">
        <field name="name">Comparing Hardness of Wood Species</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_wood_infographic_1.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_3_furn0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">10</field>
        <field name="completion_time">12</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_colorful'))]"/>
        <field name="description">Comparing Hardness of Wood Species</field>
    </record>
    <record id="slide_slide_demo_3_1" model="slide.slide">
        <field name="name">Wood Bending With Steam Box</field>
        <field name="sequence">3</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_PYr1rK8pS30.jpg"/>
        <field name="url">https://www.youtube.com/watch?v=PYr1rK8pS30</field>
        <field name="document_id">PYr1rK8pS30</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_3_furn0"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">10</field>
        <field name="completion_time">3</field>
        <field name="description">Watching the master(s) at work</field>
    </record>

    <!-- CHANNEL 4: Furniture Technical Specifications -->
    <!-- ======================================= -->

    <!--                 Categories              -->
    <record id="slide_category_demo_4_0" model="slide.slide">
        <field name="name">Documents</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="sequence">0</field>
    </record>
    <record id="slide_category_demo_4_1" model="slide.slide">
        <field name="name">Technical Drawings</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="sequence">10</field>
    </record>

    <!--                    Slides                      -->
    <record id="slide_slide_demo_4_0" model="slide.slide">
        <field name="name">Foreword</field>
        <field name="sequence">1</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_furniture_2.jpg"/>
        <field name="slide_type">webpage</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="html_content" type="html">
<section class="s_cover parallax bg-black-50 pt16 pb16" data-scroll-background-ratio="0" style="background-image: none;" data-snippet="s_cover">
    <span class="s_parallax_bg oe_img_bg" style="background-image: url('/website_slides/static/src/img/slide_demo_tree_img_3.jpg'); background-position: 50% 0;"></span>
    <div class="o_we_bg_filter bg-black-50"/>
    <div class="container">
        <div class="row s_nb_column_fixed">
            <div class="col-lg-12">
                <h1 class="o_default_snippet_text" style="font-size: 62px; text-align: center;">Foreword</h1>
                <p class="lead o_default_snippet_text" style="text-align: center;">Write one or two paragraphs describing your product, services or a specific feature.<br/> To be successful your content needs to be useful to your readers.</p>
                <p>
                    <a href="/contactus" class="btn btn-primary rounded-circle o_default_snippet_text">Contact us</a>
                </p>
            </div>
        </div>
    </div>
</section>
<section class="s_text_image pt32 pb32" data-snippet="s_text_image">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 pt16 pb16">
                <img src="/website_slides/static/src/img/slide_demo_tree_img_1.jpg" class="img img-fluid mx-auto" alt="Odoo • Image and Text"/>
            </div>
            <div class="col-lg-6 pt16 pb16">
                <h2 class="o_default_snippet_text">Section Subtitle</h2>
                <p class="o_default_snippet_text">Write one or two paragraphs describing your product or services. <br/>To be successful your content needs to be useful to your readers.</p>
                <p class="o_default_snippet_text">Start with the customer – find out what they want and give it to them.</p>
                <p class="o_default_snippet_text"><a href="#" class="btn btn-outline-primary">Discover more</a></p>
            </div>
        </div>
    </div>
</section></field>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=6)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">10</field>
        <field name="completion_time">1</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">Foreword for this documentation: how to use it, main attention points</field>
    </record>
    <record id="slide_slide_demo_4_1" model="slide.slide">
        <field name="name">Wood Types</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_bvSe6r5BpaY.jpg"/>
        <field name="url">https://www.youtube.com/watch?v=bvSe6r5BpaY</field>
        <field name="document_id">bvSe6r5BpaY</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=3)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">0.5</field>
        <field name="description">Which wood type is best for my solid wood furniture? That's the question we help you answer in this video !</field>
    </record>
    <!-- Catgory: technical drawings -->
    <record id="slide_slide_demo_4_10" model="slide.slide">
        <field name="name">Drawing 1</field>
        <field name="sequence">11</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_furniture_img.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=4)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">2</field>
        <field name="completion_time">2</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">Technical drawing</field>
    </record>
    <record id="slide_slide_demo_4_11" model="slide.slide">
        <field name="name">Drawing 2</field>
        <field name="sequence">12</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_furniture_img_1.jpg"/>
        <field name="slide_type">infographic</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=4)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">2</field>
        <field name="completion_time">2</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_theory'))]"/>
        <field name="description">Technical drawing</field>
    </record>
    <record id="slide_slide_demo_4_12" model="slide.slide">
        <field name="name">Presentation</field>
        <field name="sequence">13</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/channel-documentation-default.jpg"/>
        <field name="slide_type">document</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_4_furn1"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=4)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">3</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_howto'))]"/>
        <field name="description">GLork</field>
    </record>

    <!-- CHANNEL 5: Basics of Furniture Creation -->
    <!-- ======================================= -->

    <!--                 Categories              -->
    <record id="slide_category_demo_5_0" model="slide.slide">
        <field name="name">Tools and Methods</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="sequence">0</field>
    </record>

    <record id="slide_category_demo_5_1" model="slide.slide">
        <field name="name">Hand on !</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="sequence">3</field>
    </record>

    <record id="slide_category_demo_5_2" model="slide.slide">
        <field name="name">Test Yourself</field>
        <field name="is_category" eval="True"/>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="sequence">5</field>
    </record>

    <!--                    Slides                     -->
    <record id="slide_slide_demo_5_0" model="slide.slide">
        <field name="name">Unforgettable Tools</field>
        <field name="sequence">1</field>
        <field name="datas" type="base64" file="website_slides/static/src/img/presentation.pdf"/>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/channel-training-default.jpg"/>
        <field name="slide_type">presentation</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">10</field>
        <field name="completion_time">2.5</field>
        <field name="tag_ids" eval="[(4, ref('website_slides.slide_tag_demo_tools'))]"/>
        <field name="description">Tools you will need to complete this course.</field>
    </record>
        <record id="slide_slide_demo_5_0_link_0" model="slide.slide.link">
            <field name="name">Example Link 1</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_5_0"/>
        </record>
        <record id="slide_slide_demo_5_0_link_1" model="slide.slide.link">
            <field name="name">Example Link 2</field>
            <field name="link">http://www.example.com</field>
            <field name="slide_id" ref="slide_slide_demo_5_0"/>
        </record>
    <record id="slide_slide_demo_5_1" model="slide.slide">
        <field name="name">How to find quality wood</field>
        <field name="sequence">2</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_5WMqwTnZ-qs.jpg"/>
        <field name="url">https://www.youtube.com/watch?v=5WMqwTnZ-qs</field>
        <field name="document_id">5WMqwTnZ-qs</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">3</field>
        <field name="description">Learn to identify quality wood in order to create solid furnitures.</field>
    </record>
     <record id="slide_slide_demo_5_2" model="slide.slide">
        <field name="name">How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS</field>
        <field name="sequence">4</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_thumb_grrXe1QZNzQ.jpg"/>
        <field name="url">https://www.youtube.com/watch?v=grrXe1QZNzQ</field>
        <field name="document_id">grrXe1QZNzQ</field>
        <field name="slide_type">video</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="True"/>
        <field name="public_views">5</field>
        <field name="completion_time">3</field>
        <field name="description">From a piece of wood to a fully functional furniture, step by step.</field>
    </record>
    <record id="slide_slide_demo_5_3" model="slide.slide">
        <field name="name">Test your knowledge !</field>
        <field name="sequence">6</field>
        <field name="image_1920" type="base64" file="website_slides/static/src/img/slide_demo_owl.jpg"/>
        <field name="slide_type">quiz</field>
        <field name="channel_id" ref="website_slides.slide_channel_demo_5_furn2"/>
        <field name="is_published" eval="True"/>
        <field name="date_published" eval="datetime.now() - timedelta(days=8)"/>
        <field name="is_preview" eval="False"/>
        <field name="public_views">0</field>
        <field name="completion_time">0.5</field>
        <field name="description">Test your knowledge !</field>
    </record>
        <record id="slide_slide_demo_5_3_question_0" model="slide.question">
            <field name="question">Do you want to reply correctly ?</field>
            <field name="sequence">1</field>
            <field name="slide_id" ref="slide_slide_demo_5_3"/>
        </record>
        <record id="slide_slide_demo_5_3_question_0_0" model="slide.answer">
            <field name="text_value">Yes</field>
            <field name="sequence">1</field>
            <field name="is_correct" eval="True"/>
            <field name="comment">Correct ! You did it !</field>
            <field name="question_id" ref="slide_slide_demo_5_3_question_0"/>
        </record>
        <record id="slide_slide_demo_5_3_question_0_1" model="slide.answer">
            <field name="text_value">No</field>
            <field name="sequence">2</field>
            <field name="is_correct" eval="False"/>
            <field name="comment">Incorrect ! You better think twice...</field>
            <field name="question_id" ref="slide_slide_demo_5_3_question_0"/>
        </record>

    <!-- CHANNEL 6: DIY Furniture -->
    <!-- ======================================= -->

</data></odoo>
