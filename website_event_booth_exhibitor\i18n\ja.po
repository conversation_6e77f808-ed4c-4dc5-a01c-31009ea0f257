# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth_exhibitor
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_booked_template
msgid "<b>Sponsor</b>:"
msgstr "<b>スポンサー</b>:"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""
"<span>Eメール</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                        <span class=\"mandatory_mark\"> *</span>"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "<strong>Sponsor Details</strong>"
msgstr "<strong>スポンサー詳細</strong>"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Catchy marketing sentence for promote"
msgstr ""

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Contact me through a different email/phone."
msgstr "別のEメール/電話から連絡して下さい。"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "Create Sponsor"
msgstr "スポンサーを作成"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Description"
msgstr "説明"

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth
msgid "Event Booth"
msgstr "イベントブース"

#. module: website_event_booth_exhibitor
#: model:ir.model,name:website_event_booth_exhibitor.model_event_booth_category
msgid "Event Booth Category"
msgstr "イベントブース区分"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Exhibitor type"
msgstr "出展者タイプ"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Group By"
msgstr "グループ化"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth__use_sponsor
#: model:ir.model.fields,help:website_event_booth_exhibitor.field_event_booth_category__use_sponsor
msgid "If set, when booking a booth a sponsor will be created for the user"
msgstr "設定すると、ブースを予約する際に、ユーザ用にスポンサーが作成されます"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Phone"
msgstr "電話"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Picture"
msgstr "画像"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid "Slogan"
msgstr "スローガン"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_id
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_view_search
msgid "Sponsor"
msgstr "スポンサー"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_website_description
msgid "Sponsor Description"
msgstr "スポンサー説明"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_email
msgid "Sponsor Email"
msgstr "スポンサーメール"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_type_id
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__sponsor_type_id
msgid "Sponsor Level"
msgstr "スポンサーレベル"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_image_512
msgid "Sponsor Logo"
msgstr "スポンサーロゴ"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_mobile
msgid "Sponsor Mobile"
msgstr "スポンサーモバイル"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_name
msgid "Sponsor Name"
msgstr "スポンサー名"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_phone
msgid "Sponsor Phone"
msgstr "スポンサー電話"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth__sponsor_subtitle
msgid "Sponsor Slogan"
msgstr "スポンサー・スローガン"

#. module: website_event_booth_exhibitor
#: model:ir.model.fields,field_description:website_event_booth_exhibitor.field_event_booth_category__exhibitor_type
msgid "Sponsor Type"
msgstr "スポンサータイプ"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_search
msgid "Sponsor type"
msgstr "スポンサータイプ"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_category_view_form
msgid "Sponsorship"
msgstr "スポンサーシップ"

#. module: website_event_booth_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_booth_exhibitor.event_booth_registration_details
msgid ""
"This booth type allows you to have visibility on the event website. Please "
"fill in this form"
msgstr ""
