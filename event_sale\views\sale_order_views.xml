<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="sale_order_view_form" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.event.sale</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form" />
        <field name="arch" type="xml">
            <xpath expr="//button[@name='preview_sale_order']" position="before">
                <button name="action_view_attendee_list" type="object"
                        class="oe_stat_button" icon="fa-users" attrs="{'invisible': [('attendee_count', '=', 0)]}">
                    <field name="attendee_count" widget="statinfo" string="Attendees"/>
                </button>
            </xpath>
            <xpath expr="//field[@name='order_line']//form//field[@name='product_id']" position="after">
                <field
                    name="event_id"
                    domain="[
                        ('event_ticket_ids.product_id','=', product_id),
                        ('date_end','&gt;=',time.strftime('%Y-%m-%d 00:00:00')),
                        '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)
                    ]"
                    attrs="{'invisible': [('event_ok', '=', False)], 'required': [('event_ok', '!=', False)]}"
                    options="{'no_open': True, 'no_create': True}"
                />
                <field
                    name="event_ticket_id"
                    domain="[
                        ('event_id', '=', event_id),
                        ('product_id','=',product_id),
                        '|', ('seats_limited', '=', False), ('seats_available', '>', 0), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)
                    ]"
                    attrs="{
                        'invisible': ['|', ('event_ok', '=', False), ('event_id', '=', False)],
                        'required': [('event_ok', '!=', False), ('event_id', '!=', False)],
                    }"
                    options="{'no_open': True, 'no_create': True}"
                />
                <field name="event_ok" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='product_template_id']" position="after">
                <field name="event_ok" invisible="1" />
                <field name="event_id" optional="hide" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                <field name="event_ticket_id" optional="hide" domain="['|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
            </xpath>
        </field>
    </record>
</odoo>
