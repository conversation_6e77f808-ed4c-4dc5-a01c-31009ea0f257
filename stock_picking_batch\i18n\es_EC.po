# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_picking_wave
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-26 16:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_picking_wave
#: model:ir.model,name:stock_picking_wave.model_stock_picking_to_wave
msgid "Add pickings to a picking wave"
msgstr "Añadir movimientos de inventario a una agrupación"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Add pickings to wave"
msgstr "Añadir movimientos de inventarios a la agrupación"

#. module: stock_picking_wave
#: model:ir.actions.act_window,name:stock_picking_wave.action_picking_to_wave
#: model:ir.actions.act_window,name:stock_picking_wave.picking_to_wave_act
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Add to Wave"
msgstr "Añadir a la agrupación"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Cancel picking"
msgstr "Cancelar Movimiento de inventario"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Cancelled"
msgstr "Cancelada"

#. module: stock_picking_wave
#: model_terms:ir.actions.act_window,help:stock_picking_wave.action_picking_wave
msgid "Click to create a Picking Wave."
msgstr "Pulse aquí para crear un nuevo evento."

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Confirm"
msgstr "Confirmar"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Confirm picking"
msgstr "Confirmar Movimiento de inventario"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_create_uid
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_create_date
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_create_date
msgid "Created on"
msgstr "Creado en"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_display_name
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
#: selection:stock.picking.wave,state:0
msgid "Done"
msgstr "Realizado"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Draft"
msgstr "Borrador"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Force availability"
msgstr "Forzar disponibilidad"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_id
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_id
msgid "ID"
msgstr "ID (identificación)"

#. module: stock_picking_wave
#: model:product.product,name:stock_picking_wave.product_product_ice_cream_choco
#: model:product.template,name:stock_picking_wave.product_product_ice_cream_choco_product_template
msgid "Ice Cream Chocolate"
msgstr "Helado de chocolate"

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_ice_cream_choco
#: model:product.template,description_sale:stock_picking_wave.product_product_ice_cream_choco_product_template
#, fuzzy
msgid "Ice Cream Chocolate with sticks"
msgstr "Helado de chocolate con trocitos"

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_ice_cream_vani
#: model:product.product,name:stock_picking_wave.product_product_ice_cream_vani
#: model:product.template,description_sale:stock_picking_wave.product_product_ice_cream_vani_product_template
#: model:product.template,name:stock_picking_wave.product_product_ice_cream_vani_product_template
msgid "Ice Cream Vanilla"
msgstr "Helado de vainilla"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "In Progress"
msgstr "En proceso"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave___last_update
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_write_uid
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_write_date
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_picking_ids
msgid "List of picking associated to this wave"
msgstr "Lista de movimientos de inventario asociados a esta agrupación"

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_name
msgid "Name of the picking wave"
msgstr "Nombre de la agrupación"

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:42
#, python-format
msgid "Nothing to print."
msgstr "Nada que imprimir."

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_user_id
msgid "Person responsible for this wave"
msgstr "Persona responsable de la agrupación"

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:60
#: model:ir.model,name:stock_picking_wave.model_stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_to_wave_wave_id
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_id_9535
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
#, python-format
msgid "Picking Wave"
msgstr "Agrupación de movimientos de inventario"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_name
msgid "Picking Wave Name"
msgstr "Nombre de la agrupación de movimientos de inventario"

#. module: stock_picking_wave
#: model:ir.actions.act_window,name:stock_picking_wave.action_picking_wave
#: model:ir.ui.menu,name:stock_picking_wave.menu_action_picking_wave
msgid "Picking Waves"
msgstr "Agrupaciones de movimientos de inventario"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Picking Waves not finished"
msgstr "Agrupaciones de movimientos de inventario no finalizados"

#. module: stock_picking_wave
#: model:ir.model.fields,help:stock_picking_wave.field_stock_picking_wave_id_9535
msgid "Picking wave associated to this picking"
msgstr "Agrupación asociada este Movimiento de inventario"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_picking_ids
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Pickings"
msgstr "Movimientos de inventario"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
msgid "Print all pickings"
msgstr "Imprimir todos los movimientos de inventario"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_user_id
msgid "Responsible"
msgstr "Responsable"

#. module: stock_picking_wave
#: selection:stock.picking.wave,state:0
msgid "Running"
msgstr "En proceso"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "Search Picking Waves"
msgstr "Buscar agrupaciones de movimientos de inventario"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.picking_to_wave_form
msgid "Select a wave"
msgstr "Seleccione una agrupación"

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:59
#, python-format
msgid ""
"Some pickings are still waiting for goods. Please check or force their "
"availability before setting this wave to done."
msgstr ""
"Algunos movimientos de inventario aún están en espera de material. Compruebe "
"por favor disponibilidad o forzarla antes de establecer esta agrupación a "
"realizada."

#. module: stock_picking_wave
#: model:product.product,name:stock_picking_wave.product_product_dry_specu
#: model:product.template,name:stock_picking_wave.product_product_dry_specu_product_template
msgid "Speculoos"
msgstr "Spéculoos"

#. module: stock_picking_wave
#: model:product.product,description_sale:stock_picking_wave.product_product_dry_specu
#: model:product.template,description_sale:stock_picking_wave.product_product_dry_specu_product_template
msgid "Speculoos - A belgian speciality"
msgstr "Spéculoos - Una especialidad belga"

#. module: stock_picking_wave
#: model:mail.message.subtype,description:stock_picking_wave.mt_wave_state
#: model:mail.message.subtype,name:stock_picking_wave.mt_wave_state
msgid "Stage Changed"
msgstr "Etapa cambiada"

#. module: stock_picking_wave
#: model:ir.model.fields,field_description:stock_picking_wave.field_stock_picking_wave_state
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "State"
msgstr "Estado"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_form
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_tree
msgid "Stock Picking Waves"
msgstr "Agrupaciones de movimientos de inventario"

#. module: stock_picking_wave
#: model_terms:ir.actions.act_window,help:stock_picking_wave.action_picking_wave
#, fuzzy
msgid ""
"The goal of the picking waves is to group operations that may\n"
"                (needs to) be done together in order to increase their "
"efficiency.\n"
"                It may also be useful to assign jobs (one person = one wave) "
"or\n"
"                help the timing management of operations (tasks to be done "
"at 1pm)."
msgstr ""
"El objetivo de las agrupaciones es agrupar operaciones que pueden necesitar "
"(requieren) ser completadas conjuntamente para incrementar la eficiencia.\n"
"También puede ser útil para asignar trabajos (una persona = una agrupación) "
"o\n"
"para ayudar a contabilizar el tiempo de gestión de las operaciones (tareas "
"por ser completadas antes de la 1pm)."

#. module: stock_picking_wave
#: model:ir.model,name:stock_picking_wave.model_stock_picking
msgid "Transfer"
msgstr "Transferir"

#. module: stock_picking_wave
#: code:addons/stock_picking_wave/stock_picking_wave.py:60
#, python-format
msgid "Transferred by"
msgstr "Transferido por"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_picking_wave_filter
msgid "User"
msgstr "Usuario"

#. module: stock_picking_wave
#: model_terms:ir.ui.view,arch_db:stock_picking_wave.view_stock_picking_wave_search_inherit
msgid "Wave"
msgstr "Agrupación"

#~ msgid "Action Needed"
#~ msgstr "Necesaria acción"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Empresas)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#~ msgid "Is Follower"
#~ msgstr "Es un seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes e historial de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de mensajes no leidos"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del sitio web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicaciones del sitio web"
