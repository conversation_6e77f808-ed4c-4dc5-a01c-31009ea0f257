<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="pos_config_view_form_inherit_pos_discount" model="ir.ui.view">
            <field name="name">pos.config.form.inherit.pos.discount</field>
            <field name="model">pos.config</field>
            <field name="inherit_id" ref="point_of_sale.pos_config_view_form" />
            <field name="arch" type="xml">
                <div id="warning_text_pos_discount" position="replace">
                    <div class="row mt16">
                        <label string="Discount Product" for="discount_product_id" class="col-lg-3 o_light_label"/>
                        <field name="discount_product_id" attrs="{'required':[('module_pos_discount','=',True)]}"/>
                    </div>
                    <div class="row">
                        <label string="Discount %" for="discount_pc" class="col-lg-3 o_light_label"/>
                        <field name="discount_pc"/>
                    </div>
                </div>
            </field>
        </record>

        <data noupdate="1">
            <function model="pos.config" name="_default_discount_value_on_module_install"/>
        </data>
</odoo>
