<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.AttachmentBox" owl="1">
        <div class="o_AttachmentBox">
            <t t-if="chatter">
                <div class="o_AttachmentBox_title">
                    <hr class="o_AttachmentBox_dashedLine"/>
                    <span class="o_AttachmentBox_titleText">
                        Attachments
                    </span>
                    <hr class="o_AttachmentBox_dashedLine"/>
                </div>
                <div class="o_AttachmentBox_content">
                    <t t-if="isDropZoneVisible.value">
                        <DropZone
                            class="o_AttachmentBox_dropZone"
                            t-on-o-dropzone-files-dropped="_onDropZoneFilesDropped"
                            t-ref="dropzone"
                        />
                    </t>
                    <t t-if="chatter.attachmentList">
                        <AttachmentList
                            class="o_attachmentBox_attachmentList"
                            attachmentListLocalId="chatter.attachmentList.localId"
                            t-on-o-attachment-removed="_onAttachmentRemoved"
                        />
                    </t>
                    <button class="o_AttachmentBox_buttonAdd btn btn-link" type="button" t-on-click="_onClickAdd">
                        <i class="fa fa-plus-square"/>
                        Add attachments
                    </button>
                </div>
                <FileUploader
                    threadLocalId="chatter.thread.localId"
                    t-on-o-attachment-created="_onAttachmentCreated"
                    t-ref="fileUploader"
                />
            </t>
        </div>
    </t>

</templates>
