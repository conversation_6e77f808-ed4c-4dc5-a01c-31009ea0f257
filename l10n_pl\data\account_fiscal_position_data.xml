<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    
        <!-- Fiscal Position Templates -->
        
        <record id="fiscal_position_template_1" model="account.fiscal.position.template">
            <field name="sequence">1</field>
            <field name="name"><PERSON><PERSON></field>
            <field name="chart_template_id" ref="pl_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_id" ref="base.pl"/>
        </record>

        <record id="fiscal_position_template_4" model="account.fiscal.position.template">
            <field name="sequence">2</field>
            <field name="name">Wspólnota Prywatny</field>
            <field name="chart_template_id" ref="pl_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>

        <record id="fiscal_position_template_2" model="account.fiscal.position.template">
            <field name="sequence">3</field>
            <field name="name">Wspólnota</field>
            <field name="chart_template_id" ref="pl_chart_template"/>
            <field name="auto_apply" eval="True"/>
            <field name="vat_required" eval="True"/>
            <field name="country_group_id" ref="base.europe"/>
        </record>

        <record id="fiscal_position_template_3" model="account.fiscal.position.template">
            <field name="sequence">4</field>
            <field name="name">Import/Eksport</field>
            <field name="chart_template_id" ref="pl_chart_template"/>
            <field name="auto_apply" eval="True"/>
        </record>
    
    <!-- Fiscal Position Tax Templates -->

        <!-- European Union -->
        <!-- Sales -->

        <record id="fiscal_position_tax_template_1a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_23" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_1" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_22" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_2a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_8" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_2" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_7" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_3a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_5" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_3" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_3" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_4" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_0" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_5" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_zw" />
            <field name="tax_dest_id" ref="vs_unia" />
        </record>

        <record id="fiscal_position_tax_template_6a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_usl_23" />
            <field name="tax_dest_id" ref="vs_dostu" />
        </record>

        <record id="fiscal_position_tax_template_6" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vs_kraj_usl_22" />
            <field name="tax_dest_id" ref="vs_dostu" />
        </record>

        <!-- European Union -->
        <!-- Purchase -->

        <record id="fiscal_position_tax_template_11a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_23" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_11" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_22" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_12a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_8" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_12" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_7" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_13a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_5" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_13" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_3" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_14" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_0" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_15" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_zw" />
            <field name="tax_dest_id" ref="vz_unia" />
        </record>

        <record id="fiscal_position_tax_template_16a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_usl_23" />
            <field name="tax_dest_id" ref="vz_nabu" />
        </record>

        <record id="fiscal_position_tax_template_16" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_2"  />
            <field name="tax_src_id" ref="vz_kraj_usl_22" />
            <field name="tax_dest_id" ref="vz_nabu" />
        </record>


        <!-- World Export/Import -->
        <!-- Sales -->

        <record id="fiscal_position_tax_template_21a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_23" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_21" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_22" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_22a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_8" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_22" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_7" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_23a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_5" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_23" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_3" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_24" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_0" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_25" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_zw" />
            <field name="tax_dest_id" ref="vs_eksp_tow" />
        </record>

        <record id="fiscal_position_tax_template_26a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_usl_23" />
            <field name="tax_dest_id" ref="vs_ekspu" />
        </record>

        <record id="fiscal_position_tax_template_26" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vs_kraj_usl_22" />
            <field name="tax_dest_id" ref="vs_ekspu" />
        </record>

        <!-- World Export/Import -->
        <!-- Purchase -->

        <record id="fiscal_position_tax_template_31a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_23" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_31" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_22" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_32a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_8" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_32" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_7" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_33a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_5" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_33" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_3" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_34" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_0" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_35" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_zw" />
            <field name="tax_dest_id" ref="vz_imp_tow" />
        </record>

        <record id="fiscal_position_tax_template_36a" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_usl_23" />
            <field name="tax_dest_id" ref="vz_impu" />
        </record>

        <record id="fiscal_position_tax_template_36" model="account.fiscal.position.tax.template">
            <field name="position_id" ref="fiscal_position_template_3"  />
            <field name="tax_src_id" ref="vz_kraj_usl_22" />
            <field name="tax_dest_id" ref="vz_impu" />
        </record>

    </data>
</odoo>
