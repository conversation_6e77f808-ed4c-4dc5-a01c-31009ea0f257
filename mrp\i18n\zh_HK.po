# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mrp
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-07-06 08:35+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Chinese (Hong Kong) (http://www.transifex.com/odoo/odoo-9/"
"language/zh_HK/)\n"
"Language: zh_HK\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp
#: code:addons/mrp/stock.py:231
#, python-format
msgid " Manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template_bom_count
msgid "# Bill of Material"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_mo_count
#: model:ir.model.fields,field_description:mrp.field_product_template_mo_count
msgid "# Manufacturing Orders"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:341
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:1052
#, python-format
msgid "%s produced"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_product_produce_mode
msgid ""
"'Consume only' mode will only consume the products with the quantity "
"selected.\n"
"'Consume & Produce' mode will consume as well as produce the products with "
"the quantity selected and it will finish the production order when total "
"ordered quantities are produced."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Consumed Products</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Destination Location</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Name</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Cycles</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>No. Of Hours</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Partner Ref:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Printing date:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Products to Consume</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>SO Number:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Scheduled Date:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Sequence</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Document:</strong><br/>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source Location</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Work Orders</strong>"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_efficiency
msgid "A factor of 0.9 means a loss of 10% during the production process."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_product_efficiency
msgid "A factor of 0.9 means a loss of 10% within the production process."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
msgid ""
"A manufacturing order, based on a bill of materials, will\n"
"                consume raw materials and produce finished products."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_active
msgid "Active"
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:51
#, python-format
msgid "Active Id not found"
msgstr ""

#. module: mrp
#: sql_constraint:mrp.bom.line:0
msgid ""
"All product quantities must be greater than 0.\n"
"You should install the mrp_byproduct module if you want to manage extra "
"products on BoMs !"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,module_mrp_operations:0
msgid "Allow detailed planning of work orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_config_settings_group_rounding_efficiency
msgid ""
"Allow to manage product rounding on quantity and product efficiency during "
"production process"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_config
msgid "Apply"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Approve"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_product_produce_delay
#: model:ir.model.fields,help:mrp.field_product_template_produce_delay
msgid ""
"Average delay in days to produce this product. In the case of multi-level "
"BOM, the manufacturing lead times of the components will be added."
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "Awaiting Raw Materials"
msgstr ""

#. module: mrp
#: model:ir.actions.report.xml,name:mrp.action_report_bom_price
msgid "BOM Cost"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrpbomstructure
msgid "BOM Name"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_attribute_value_ids
msgid "BOM Product Variants needed form apply this line."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrpbomstructure
msgid "BOM Ref"
msgstr ""

#. module: mrp
#: model:ir.actions.report.xml,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrpbomstructure
msgid "BOM Structure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_child_line_ids
msgid "BOM lines of the referred bom"
msgstr ""

#. module: mrp
#: code:addons/mrp/stock.py:25
#, python-format
msgid ""
"Because the product %s requires it, you must assign a serial number to your "
"raw material %s to proceed further in your production. Please use the "
"'Produce' button to do so."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Bill Of Material"
msgstr "物料清單"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production_bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Bill of Material"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product_bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template_bom_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Bill of Materials"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action2
msgid "Bill of Materials Structure"
msgstr "物料清單結構"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_bom_id
msgid ""
"Bill of Materials allow you to define the list of required raw materials to "
"make a finished product."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of Materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a "
"manufacturing\n"
"                order or a pack of products."
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,module_mrp_byproduct:0
msgid "Bills of materials may produce residual products (A + B --> C + D)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_procurement_order_bom_id
msgid "BoM"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:316
#, python-format
msgid "BoM \"%s\" contains a BoM line with a product recursion: \"%s\"."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_bom_line_ids
msgid "BoM Lines"
msgstr "物料清單明細"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_type
msgid "BoM Type"
msgstr "物料清單類型"

#. module: mrp
#: selection:mrp.product.produce,tracking:0
msgid "By Lots"
msgstr ""

#. module: mrp
#: selection:mrp.product.produce,tracking:0
msgid "By Unique Serial Number"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_module_mrp_byproduct
msgid "By-Products"
msgstr ""

#. module: mrp
#: code:addons/mrp/stock.py:228
#, python-format
msgid "Can't find any generic Manufacture route."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_consume_wizard
msgid "Cancel"
msgstr "取消"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Cancel Production"
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "Cancelled"
msgstr ""

#. module: mrp
#: code:addons/mrp/stock.py:150
#, python-format
msgid "Cannot consume a move with negative or zero quantity."
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:649
#, python-format
msgid "Cannot delete a manufacturing order in state '%s'."
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:719
#, python-format
msgid "Cannot find a bill of material for this product."
msgstr ""

#. module: mrp
#: code:addons/mrp/wizard/change_production_qty.py:67
#: code:addons/mrp/wizard/change_production_qty.py:72
#, python-format
msgid "Cannot find bill of material for this product."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Capacity Information"
msgstr "產能資料"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity_per_cycle
msgid "Capacity per Cycle"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Quantity of Products"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid "Click to add a work center."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "Click to create a bill of material."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_property_group_action
msgid "Click to create a group of properties."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "Click to create a manufacturing order."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_property_action
msgid "Click to create a new property."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Click to create a new routing."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
msgid "Click to start a new manufacturing order."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_code
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_code
msgid "Code"
msgstr "代碼"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_company_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout_company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_component_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.product_template_search_view_procurment
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Compute Data"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
msgid "Configuration"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_config
msgid "Configure Manufacturing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Confirm"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Confirm Production"
msgstr "確認生產"

#. module: mrp
#: selection:mrp.product.produce,mode:0
msgid "Consume & Produce"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Consume Lines"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.move_consume
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_consume_wizard
msgid "Consume Move"
msgstr ""

#. module: mrp
#: selection:mrp.product.produce,mode:0
msgid "Consume Only"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_consume
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_consume_wizard
msgid "Consume Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_lines2
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Consumed Products"
msgstr "損耗的貨品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_consumed_for
msgid "Consumed for"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
msgid "Cost Structure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_costs_cycle
msgid "Cost per cycle"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_costs_hour
msgid "Cost per hour"
msgstr "每小時之成本"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_property_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_create_uid
msgid "Created by"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_property_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_create_date
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_create_date
msgid "Created on"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_costs_cycle_account_id
msgid "Cycle Account"
msgstr "循環戶口"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Default Unit of Measure"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_property_group_action
msgid ""
"Define specific property groups that can be assigned to your\n"
"                bill of materials and sales orders. Properties allows Odoo\n"
"                to automatically select the right bill of materials "
"according\n"
"                to properties selected in the sales order by salesperson."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_calendar_id
msgid "Define the schedule of resource"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_property_description
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_description
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_note
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_property_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_property_group_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_note
msgid ""
"Description of the Work Center. Explain here what's a cycle according to "
"this Work Center."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Destination Loc."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_property_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_display_name
#: model:ir.model.fields,field_description:mrp.field_report_mrp_bom_cost_display_name
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout_display_name
#: model:ir.model.fields,field_description:mrp.field_report_mrp_report_mrpbomstructure_display_name
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_display_name
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_display_name
msgid "Display Name"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,module_mrp_operations:0
msgid "Do not use a planning for the work orders "
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "Done"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_efficiency
msgid "Efficiency Factor"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_finished
msgid "End Date"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Extra Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_costs_cycle_account_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_costs_hour_account_id
msgid ""
"Fill this only if you want automatic analytic accounting entries on "
"production orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_product_id
msgid ""
"Fill this product to easily track your production costs in the analytic "
"accounting."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Finished Products"
msgstr "成品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_location_dest_id
msgid "Finished Products Location"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_property_group_action
msgid ""
"For instance, in the property group \"Warranty\", you an have\n"
"                two properties: 1 year warranty, 3 years warranty. "
"Depending\n"
"                on the propoerties selected in the sales order, Odoo will\n"
"                schedule a production using the matching bill of materials."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Force Reservation"
msgstr "強制預留"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_costs_general_account_id
msgid "General Account"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_sequence
msgid "Gives the sequence order when displaying a list of bills of material."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_workcenter_line_sequence
msgid "Gives the sequence order when displaying a list of work orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_sequence
msgid "Gives the sequence order when displaying."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_property_search
msgid "Group By"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_costs_hour_account_id
msgid "Hour Account"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_id
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_id
#: model:ir.model.fields,field_description:mrp.field_mrp_property_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_bom_cost_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout_id
#: model:ir.model.fields,field_description:mrp.field_report_mrp_report_mrpbomstructure_id
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_id
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_id
msgid "ID"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_active
msgid ""
"If the active field is set to False, it will allow you to hide the bills of "
"material without removing it."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_active
msgid ""
"If the active field is set to False, it will allow you to hide the routing "
"without removing it."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "In Production"
msgstr "生產中"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Inactive"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_position
msgid "Internal Reference"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_location_id
msgid ""
"Keep empty if you produce at the location where the finished products are "
"needed.Set a location if you produce at a fixed location. This can be a "
"partner location if you subcontract the manufacturing operations."
msgstr ""
"如果您在所需成品地點生產，請留空。如在固定地點生產請設置地點。如果您外判這個"
"生產業務，這也可以是業務夥伴的地點。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_property___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter___last_update
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter___last_update
#: model:ir.model.fields,field_description:mrp.field_report_mrp_bom_cost___last_update
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout___last_update
#: model:ir.model.fields,field_description:mrp.field_report_mrp_report_mrpbomstructure___last_update
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load___last_update
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume___last_update
msgid "Last Modified on"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_property_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_property_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_write_date
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_location_id
msgid "Location"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_location_src_id
msgid "Location where the system will look for components."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_location_dest_id
msgid "Location where the system will stock the finished products."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_lot_id
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_restrict_lot_id
msgid "Lot"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_mrp_routings:0
msgid "Manage production by manufacturing orders"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_mrp_routings:0
msgid "Manage production by work orders"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_rounding_efficiency
msgid "Manage rounding and efficiency of BoM components"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_rounding_efficiency:0
msgid "Manage rounding and efficiency of bills of materials components"
msgstr ""

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Manager"
msgstr ""

#. module: mrp
#: code:addons/mrp/procurement.py:15 code:addons/mrp/stock.py:225
#: model:stock.location.route,name:mrp.route_warehouse0_manufacture
#, python-format
msgid "Manufacture"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_manufacture_pull_id
msgid "Manufacture Rule"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_manufacture_to_resupply
msgid "Manufacture in this Warehouse"
msgstr ""

#. module: mrp
#: selection:mrp.bom,type:0
msgid "Manufacture this product"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufacturing"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_efficiency
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_efficiency
msgid "Manufacturing Efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product_produce_delay
#: model:ir.model.fields,field_description:mrp.field_product_template_produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company_manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_production_id
#: model:ir.model.fields,field_description:mrp.field_procurement_order_production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_config
msgid "Manufacturing Order"
msgstr ""

#. module: mrp
#: code:addons/mrp/procurement.py:112
#, python-format
msgid "Manufacturing Order <em>%s</em> created."
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_planning
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are currently in production."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are ready to start production."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are waiting for raw materials."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:784
#, python-format
msgid "Manufacturing order cancelled."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action_planning
msgid ""
"Manufacturing orders are usually proposed automatically based\n"
"                on customer requirements or automated rules like the "
"minimum\n"
"                stock rule."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Mark as Started"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_config
msgid "Master Data"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_mode
msgid "Mode"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Mrp Workcenter"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_name
#: model:ir.model.fields,field_description:mrp.field_mrp_property_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_name
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_property_search
msgid "Name"
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "New"
msgstr "新建"

#. module: mrp
#: code:addons/mrp/procurement.py:108
#, python-format
msgid "No BoM exists for this product!"
msgstr ""

#. module: mrp
#: selection:mrp.product.produce,tracking:0
msgid "No Tracking"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,module_mrp_byproduct:0
msgid "No by-products in bills of materials (A + B --> C)"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_rounding_efficiency:0
msgid "No rounding and efficiency on bills of materials"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Normal"
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Not urgent"
msgstr "不緊急"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_property_composition
msgid "Not used in computations, for information purpose only."
msgstr "不是用來計算，僅僅是提供資訊而已。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "Notes"
msgstr "記事"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_cycle_nbr
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_cycle
msgid "Number of Cycles"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_hour_nbr
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_hour
msgid "Number of Hours"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_cycle_nbr
msgid ""
"Number of iterations this work center has to do in the specified operation "
"of the routing."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity_per_cycle
msgid ""
"Number of operations this Work Center can do in parallel. If this Work "
"Center represents a team of 5 workers, the capacity per cycle is 5."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Odoo uses these BoMs to automatically propose manufacturing\n"
"                orders according to procurement needs."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_consume_wizard
msgid "Ok"
msgstr "Ok"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "Operation"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_production_order_action
msgid "Order Planning"
msgstr ""

#. module: mrp
#: constraint:mrp.production:0
msgid "Order quantity cannot be negative or zero!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_bom_id
msgid "Parent BoM"
msgstr "父系物料清單"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_routing_id
msgid "Parent Routing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Pending"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_planning
msgid "Planning"
msgstr ""

#. module: mrp
#: code:addons/mrp/stock.py:136
#, python-format
msgid "Please provide proper quantity."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_priority
msgid "Priority"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_order
msgid "Procurement"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_rule
msgid "Procurement Rule"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_product_produce
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_produce_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "Produce"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_created_ids2
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produced Products"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_product_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Product"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_prod_id
msgid "Product Move"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_product_produce
msgid "Product Produce"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_product_produce_line
msgid "Product Produce Consume lines"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty_product_qty
msgid "Product Qty"
msgstr "貨品數量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_qty
msgid "Product Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_rounding
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_rounding
msgid "Product Rounding"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_product_template
msgid "Product Template"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_product_uom
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_uom
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_product_uom
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_uom
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_product_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_product_id
msgid "Product Variant"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_group_product_variant
msgid "Product Variants"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Production"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_location_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
msgid "Production Location"
msgstr "生產地點"

#. module: mrp
#: model:ir.actions.report.xml,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_line_production_id
msgid "Production Order"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Production Order # :"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_production_id
msgid "Production Order for Produced Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_raw_material_production_id
msgid "Production Order for Raw Materials"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_product_line
msgid "Production Scheduled Product"
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "Production Started"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Production Work Centers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_progress
msgid "Production progress"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Production started late"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_gantt
msgid "Productions"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_consume_lines
msgid "Products Consumed"
msgstr ""

#. module: mrp
#: selection:mrp.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_lines
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Products to Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Products to Finish"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_move_created_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Products to Produce"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_property_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_property_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_property_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_order_property_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_property_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_property_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_property_tree_view
msgid "Properties"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_property_group_form_view
msgid "Properties categories"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_property_composition
msgid "Properties composition"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_property
msgid "Property"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_property_group
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_id_9665
#: model:ir.model.fields,field_description:mrp.field_mrp_property_group_name
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_property_search
msgid "Property Group"
msgstr "屬性組別"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_property_group_action
#: model:ir.ui.menu,name:mrp.menu_mrp_property_group_action
msgid "Property Groups"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move_consume_product_qty
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrpbomstructure
msgid "Quantity"
msgstr "數量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_line_product_qty
msgid "Quantity (in default UoM)"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
msgid "Raw Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_location_src_id
msgid "Raw Materials Location"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Ready"
msgstr "就緒"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_ready_production
msgid "Ready for production"
msgstr ""

#. module: mrp
#: selection:mrp.production,state:0
msgid "Ready to Produce"
msgstr "就緒生產"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_code
#: model:ir.model.fields,field_description:mrp.field_mrp_production_name
msgid "Reference"
msgstr "參考"

#. module: mrp
#: sql_constraint:mrp.production:0
msgid "Reference must be unique per Company!"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_origin
msgid "Reference of the document that generated this production order request."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_position
msgid "Reference to a position in an external plan."
msgstr "於外部計劃中之位置的引用。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Reserve"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_resource_id
msgid "Resource"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_view_resource_calendar_leaves_search_mrp
msgid "Resource Leaves"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_resource_type
msgid "Resource Type"
msgstr ""

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_pm_resources_config
msgid "Resources"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_user_id
msgid "Responsible"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_product_rounding
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_rounding
msgid "Rounding applied on the product quantity."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_group_rounding_efficiency
msgid "Rounding efficiency"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_routing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_routing_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Routing"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model,name:mrp.model_mrp_routing
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_group_mrp_routings
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Routings"
msgstr "工藝流程"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Routings allow you to create and manage the manufacturing\n"
"                operations that should be followed within your work centers "
"in\n"
"                order to produce a product.  They are attached to bills of\n"
"                materials that will define the required raw materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_routing_id
msgid ""
"Routings indicates all the Work Centers used, for how long and/or cycles.If "
"Routings is set then,the third tab of a production order (Work Centers) will "
"be automatically pre-completed."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_planned
msgid "Scheduled Date"
msgstr "預定日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Date by Month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Scheduled Month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_product_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_product_tree_view
msgid "Scheduled Products"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_product_lines
msgid "Scheduled goods"
msgstr "預定之貨品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scrap Products"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_property_search
msgid "Search"
msgstr "搜尋"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Search Bill Of Material Components"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company_manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_qty
msgid "Select Quantity"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_sequence
msgid "Sequence"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_type
msgid ""
"Set: When processing a sales order for this product, the delivery order will "
"contain the raw materials, instead of the finished product."
msgstr ""

#. module: mrp
#: selection:mrp.bom,type:0
msgid "Ship this product as a set of components (kit)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_origin
msgid "Source Document"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_costs_cycle
msgid "Specify Cost of Work Center per cycle."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_costs_hour
msgid "Specify Cost of Work Center per hour."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_date_start
msgid "Start Date"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_state
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Status"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout_value
msgid "Stock value"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_inout
#: model_terms:ir.ui.view,arch_db:mrp.view_report_in_out_picking_form
#: model_terms:ir.ui.view,arch_db:mrp.view_report_in_out_picking_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_report_in_out_picking_tree
msgid "Stock value variation"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move_consumed_for
msgid "Technical field used to make the traceability of produced products"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:351 code:addons/mrp/mrp.py:450
#, python-format
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_property_action
msgid ""
"The Properties in Odoo are used to select the right bill of\n"
"                materials for manufacturing a product when you have "
"different\n"
"                ways of building the same product.  You can assign several\n"
"                properties to each bill of materials.  When a salesperson\n"
"                creates a sales order, they can relate it to several "
"properties\n"
"                and Odoo will automatically select the BoM to use according\n"
"                the needs."
msgstr ""

#. module: mrp
#: constraint:mrp.workcenter:0
msgid "The capacity per cycle must be strictly positive."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_routing_id
msgid ""
"The list of operations (list of work centers) to produce the finished "
"product. The routing is mainly used to compute work center costs during "
"operations and to plan future loads on work centers based on production "
"plannification."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_routing_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_routing_id
msgid ""
"The list of operations (list of work centers) to produce the finished "
"product. The routing is mainly used to compute work center costs during "
"operations and to plan future loads on work centers based on production "
"planning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_config_settings_module_mrp_operations
msgid ""
"This allows to add state, date_start,date_stop in production order operation "
"lines (in the \"Work Centers\" tab).\n"
"-This installs the module mrp_operations."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_efficiency
msgid ""
"This field depict the efficiency of the resource to complete tasks. e.g  "
"resource put alone on a phase of 5 days with 5 tasks assigned to him, will "
"show a load of 100% for this phase by default, but if we put a efficiency of "
"200%, then his load will only be 50%."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_stop
msgid "Time after prod."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_start
msgid "Time before prod."
msgstr "生產前的時間"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_time_cycle
msgid "Time for 1 cycle (hour)"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_cycle
msgid "Time in hours for doing one cycle."
msgstr "一個循環所須的小時數"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_stop
msgid "Time in hours for the cleaning."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_time_start
msgid "Time in hours for the setup."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter_hour_nbr
msgid ""
"Time in hours for this Work Center to achieve the operation of the specified "
"routing."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_produce_wizard
msgid "To Consume"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
msgid "Total Cost"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
msgid "Total Cost of Raw Materials"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_cycle_total
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Cycles"
msgstr "週期總數"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_hour_total
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Hours"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Type"
msgstr "類型"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_cost
msgid "Unit Cost"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unit of Measure"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_product_uom
#: model:ir.model.fields,help:mrp.field_mrp_bom_product_uom
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update"
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Urgent"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_user_id
#: model:res.groups,name:mrp.group_mrp_user
msgid "User"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_date_start
msgid "Valid From"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Valid From Date by Month"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_line_filter
msgid "Valid From Month"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_date_stop
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_date_stop
msgid "Valid Until"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_date_start
#: model:ir.model.fields,help:mrp.field_mrp_bom_line_date_stop
msgid "Validity of component. Keep empty if it's always valid."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_date_start
#: model:ir.model.fields,help:mrp.field_mrp_bom_date_stop
msgid "Validity of this BoM. Keep empty if it's always valid."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line_attribute_value_ids
msgid "Variants"
msgstr ""

#. module: mrp
#: selection:mrp.production,priority:0
msgid "Very Urgent"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse
msgid "Warehouse"
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:351 code:addons/mrp/mrp.py:450
#, python-format
msgid "Warning"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_report_mrp_inout_date
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_name
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_search
msgid "Week"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_report_in_out_picking_tree
msgid "Weekly Stock Value Variation"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_report_in_out_picking_tree
msgid ""
"Weekly Stock Value Variation enables you to track the stock value evolution "
"linked to manufacturing activities, receipts of products and delivery orders."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production_state
msgid ""
"When the production order is created the status is set to 'Draft'.\n"
"If the order is confirmed the status is set to 'Waiting Goods.\n"
"If any exceptions are there, the status is set to 'Picking Exception.\n"
"If the stock is available then the status is set to 'Ready to Produce.\n"
"When the production gets started then the status is set to 'In Production.\n"
"When the production is over, the status is set to 'Done'."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_workcenter_id
#: model:ir.model.fields,field_description:mrp.field_report_workcenter_load_workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_workcenter_load
msgid "Work Center Load"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_tree
msgid "Work Center Loads"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_form_view
msgid "Work Center Operations"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_product_id
msgid "Work Center Product"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_search
msgid "Work Center load"
msgstr ""

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter_lines
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
msgid "Work Centers"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_lines
msgid "Work Centers Utilisation"
msgstr ""

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Work Centers allow you to create and manage manufacturing\n"
"                units. They consist of workers and/or machines, which are\n"
"                considered as units for task assignation as well as "
"capacity\n"
"                and planning forecast."
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_workcenter_line
#: model:ir.model.fields,field_description:mrp.field_mrp_production_workcenter_line_name
msgid "Work Order"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_config_settings_group_mrp_routings
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required raw materials."
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_config_settings_module_mrp_operations
msgid "Work Order Planning"
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Work Orders"
msgstr ""

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_calendar_id
#: model:ir.ui.menu,name:mrp.menu_view_resource_calendar_search_mrp
msgid "Working Time"
msgstr "工作時間"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_config_settings_module_mrp_byproduct
msgid ""
"You can configure by-products in the bill of material.\n"
"Without this module: A + B + C -> D.\n"
"With this module: A + B + C -> D + E.\n"
"-This installs the module mrp_byproduct."
msgstr ""

#. module: mrp
#: code:addons/mrp/mrp.py:357
#, python-format
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_product_template_form_inherited
msgid "days"
msgstr ""

#. module: mrp
#: selection:mrp.property,composition:0
msgid "max"
msgstr ""

#. module: mrp
#: selection:mrp.property,composition:0
msgid "min"
msgstr "最少"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "mrp.bom.line"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_config_settings
msgid "mrp.config.settings"
msgstr ""

#. module: mrp
#: selection:mrp.property,composition:0
msgid "plus"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mrpbomstructure
msgid "report.mrp.report_mrpbomstructure"
msgstr ""

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_bom_cost
msgid "report.mrp_bom_cost"
msgstr ""

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_product_produce_tracking
msgid "unknown"
msgstr ""
