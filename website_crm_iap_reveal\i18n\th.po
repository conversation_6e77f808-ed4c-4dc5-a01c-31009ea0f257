# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_website
# 
# Translators:
# <PERSON>, 2021
# Khwu<PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Wichanon Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
" 1 "
"เครดิตต่อผู้เข้าชมที่ตรงกับเงื่อนไขการเข้าชมเว็บไซต์และสามารถระบุบริษัทได้<br/>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> ลูกค้าเป้าหมาย </span>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> โอกาสในการขาย </span>"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "กฎการสร้างลูกค้าเป้าหมาย CRM"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "CRM Reveal View"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr "เลือกว่าจะติดตามบริษัทเท่านั้นหรือบริษัทและผู้ติดต่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "บริษัท"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "บริษัทและผู้ติดต่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "ขนาดบริษัท"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "ขนาดบริษัทสูงสุด"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "ตัวกรองการติดต่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "ประเทศ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "วันที่สร้าง"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "สร้างกฎการแปลง"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"สร้างกฎเพื่อสร้างลูกค้าเป้าหมาย/โอกาสแบบ B2B จากผู้เยี่ยมชมเว็บไซต์ของคุณ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "การติดตามข้อมูล"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Enter Valid Regex."
msgstr "ป้อน Regex ที่ถูกต้อง"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "เปิดตัวกรอง"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "กรองบริษัทตามขนาด"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "กรองตามขนาด"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "From"
msgstr "จาก"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "สร้างลูกค้าเป้าหมาย / โอกาส"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "สูง"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "IAP เครดิต"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "ที่อยู่ IP"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "อุตสาหกรรม"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule____last_update
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "ลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Lead Data"
msgstr "ข้อมูลลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "ข้อมูลการสร้างลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "กฎการสร้างลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_view_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "มุมมองการสร้างลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid ""
"Lead Generation requires a GeoIP resolver which could not be found on your "
"system. Please consult https://pypi.org/project/GeoIP/."
msgstr ""
"การสร้างลูกค้าเป้าหมายต้องใช้ตัวแก้ไข GeoIP ซึ่งไม่พบในระบบของคุณ โปรดปรึกษา"
" https://pypi.org/project/GeoIP/"

#. module: crm_iap_lead_website
#: model:ir.actions.server,name:crm_iap_lead_website.ir_cron_crm_reveal_lead_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_website.ir_cron_crm_reveal_lead
#: model:ir.cron,name:crm_iap_lead_website.ir_cron_crm_reveal_lead
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr "การสร้างลูกค้าเป้าหมาย: การสร้างลูกค้าเป้าหมาย/โอกาส"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_lead
msgid "Lead/Opportunity"
msgstr "ลูกค้าเป้าหมาย / โอกาส"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"เว้นว่างไว้เพื่อให้ตรงกันเสมอ Odoo จะไม่สร้างลูกค้าเป้าหมายหากไม่ตรงกัน"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "ต่ำ"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"ตรวจสอบให้แน่ใจว่าคุณต้องปฏิบัติตาม GDPR ในการจัดเก็บข้อมูลส่วนบุคคลหรือไม่"

#. module: crm_iap_lead_website
#: model:ir.model.constraint,message:crm_iap_lead_website.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "อนุญาตให้มีผู้ติดต่อได้สูงสุด 5 ราย!"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "กลาง"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Missing Library"
msgstr "คลังที่สูญหาย"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "ไม่พบ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "จำนวนผู้ติดต่อ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "จำนวนลูกค้าเป้าหมายที่สร้างขึ้น"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "จำนวนโอกาสการขายที่สร้าง"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"เฉพาะผู้เยี่ยมชมจากประเทศต่อไปนี้เท่านั้นที่จะถูกแปลงเป็นลูกค้าเป้าหมาย/โอกาส"
" (โดยใช้ GeoIP)"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"เฉพาะผู้เยี่ยมชมจากรัฐต่อไปนี้เท่านั้นที่จะถูกแปลงเป็นลูกค้าเป้าหมาย/โอกาส"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "โอกาสการขาย"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "ข้อมูลโอกาสการขาย"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "เงื่อนไขการสร้างโอกาสการขาย"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr "โอกาสที่สร้างโดย การสร้างลูกค้าเป้าหมาย Odoo "

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "บทบาทอื่น ๆ "

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "บทบาทที่ต้องการ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "ระดับความสำคัญ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Regex เพื่อติดตามหน้าเว็บไซต์ เว้นว่างไว้เพื่อติดตามทั้งเว็บไซต์ หรือ / "
"เพื่อกำหนดเป้าหมายหน้าแรก ตัวอย่าง: /page* "
"เพื่อติดตามเพจทั้งหมดที่ขึ้นต้นด้วย /page"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "จำกัดการสร้างลูกค้าเป้าหมายในเว็บไซต์นี้"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "บทบาท"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Rule"
msgstr "กฎ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "ชื่อกฎ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "ทีมขาย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "พนักงานขาย"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "ค้นหา CRM Reveal Rule"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "อาวุโส"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "สถานะ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "รัฐ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "คำต่อท้าย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "แท็ก"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"นี่คือจำนวนผู้ติดต่อที่จะติดตามว่าบทบาท/อาวุโสของของพวกเขาตรงกับเกณฑ์ของคุณหรือไม่"
" รายละเอียดของพวกเขาจะปรากฏในเธรดประวัติของลูกค้าเป้าหมาย/โอกาสที่สร้างขึ้น "
"หนึ่งเครดิตถูกใช้ต่อหนึ่งผู้ติดต่อที่ติดตาม"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"สิ่งนี้จะถูกผนวกเข้ากับชื่อลูกค้าเป้าหมายที่สร้างขึ้น "
"เพื่อให้คุณสามารถระบุลูกค้าเป้าหมาย/โอกาสที่ถูกสร้างขึ้นด้วยกฎนี้"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "รอดำเนินการ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "ประเภท"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "URL Expression"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Up to"
msgstr "จนถึง"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"ใช้เพื่อสร้างคำสั่งด้วยกฎกับ URL และประเทศเดียวกัน "
"กฎที่มีหมายเลขลำดับต่ำกว่าจะได้รับการประมวลผลก่อน"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "สูงมาก"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_rule_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "กฎจากผู้เยี่ยมชมเป็นลูกค้าเป้าหมาย"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "เว็บไซต์"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "เงื่อนไขการเข้าดูเว็บไซต์"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr "เครดิตเพิ่มเติมจะถูกใช้หากบริษัทตรงกับกฎนี้"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "เช่น /page"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "เช่น ผู้เยี่ยมชม US "

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "employees"
msgstr "พนักงาน"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "to"
msgstr "ถึง"
