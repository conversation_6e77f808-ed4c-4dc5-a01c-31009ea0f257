<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
<template id="report_repairorder">
        <t t-set="o" t-value="doc"/>
            <t t-call="web.external_layout">
                <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang)" />
                <t t-set="information_block">
                    <strong t-if="o.address_id == o.partner_invoice_id">Invoice and shipping address:</strong>
                    <div t-if="o.partner_invoice_id">
                        <strong t-if="o.address_id != o.partner_invoice_id">Invoice address: </strong>
                        <div t-field="o.partner_invoice_id" 
                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        <p t-if="o.partner_invoice_id.vat"><t t-esc="o.company_id.country_id.vat_label or 'Tax ID'"/>: <span t-field="o.partner_invoice_id.vat"/></p>
                    </div>
                    <t t-if="o.address_id != o.partner_invoice_id">
                        <strong>Shipping address :</strong>
                        <div t-field="o.address_id" 
                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                        <p t-if="o.address_id.vat"><t t-esc="o.company_id.country_id.vat_label or 'Tax ID'"/>: <span t-field="o.address_id.vat"/></p>
                    </t>
                </t>
                <t t-set="address">
                    <div t-field="o.partner_id"
                        t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
                </t>
                <div class="page">
                    <div class="oe_structure"/>

                    <h2>
                        <span t-if="o.state != 'draft'">Repair Order #:</span>
                        <span t-if="o.state == 'draft'">Repair Quotation #:</span>
                        <span t-field="o.name"/>
                    </h2>

                    <div id="informations" class="row mt32 mb32">
                        <div t-if="o.product_id.name" class="col-3 bm-2">
                            <strong>Product to Repair:</strong>
                            <p t-field="o.product_id.name" class="m-0"/>
                        </div>
                        <div class="col-3 bm-2" groups="stock.group_production_lot">
                            <strong>Lot/Serial Number:</strong>
                            <t t-if="o.lot_id">
                                <span t-field="o.lot_id.name"/>
                            </t>
                        </div>
                        <div t-if="o.guarantee_limit" class="col-3 bm-2">
                            <strong>Warranty:</strong>
                            <p t-field="o.guarantee_limit" class="m-0"/>
                        </div>
                        <div class="col-3 bm-2">
                            <strong>Printing Date:</strong>
                            <p t-esc="datetime.datetime.now().strftime('%Y-%m-%d')" t-options="{'widget': 'date'}" class="m-0"/>
                        </div>
                    </div>

                    <table class="table table-sm o_main_table">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th class="text-right">Quantity</th>
                                <t t-if="o.invoice_method != 'none'">
                                    <th class="text-right">Unit Price</th>
                                    <th class="text-center">Tax</th>
                                    <th class="text-right">Price</th>
                                </t>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-if="o.operations">
                                <tr class="bg-200 o_line_section"><td colspan="5"><strong>Parts</strong></td></tr>
                                <tr t-foreach="o.operations" t-as="line">
                                    <td>
                                        <p t-if="line.type == 'add'"><i>(Add)</i> <span t-field="line.name" /></p>
                                        <p t-if="line.type == 'remove'">(<i>Remove</i>) <span t-field="line.name"/></p>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.product_uom_qty"/>
                                        <span groups="uom.group_uom" t-field="line.product_uom.name"/>
                                    </td>
                                    <t t-if="(line.repair_id.invoice_method != 'none')">
                                        <td class="text-right">
                                            <span t-field="line.price_unit"/>
                                        </td>
                                        <td class="text-center">
                                            <span t-esc="','.join(map( lambda x: x.name, line.tax_id))"/>
                                        </td>
                                        <td class="text-right o_price_total">
                                            <span t-field="line.price_subtotal" t-options='{"widget": "monetary", "display_currency": o.pricelist_id.currency_id}'/>
                                       </td>
                                    </t>
                                </tr>
                            </t>
                            <t t-if="o.fees_lines">
                                <tr class="bg-200 o_line_section"><td colspan="5"><strong>Operations</strong></td></tr>
                                <tr t-foreach="o.fees_lines" t-as="fees">
                                    <td>
                                        <span t-field="fees.name"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="fees.product_uom_qty"/>
                                        <span groups="uom.group_uom" t-field="fees.product_uom.name"/>
                                    </td>
                                    <t t-if="(fees.repair_id.invoice_method != 'none')">
                                        <td class="text-right">
                                            <span t-field="fees.price_unit"/>
                                        </td>
                                        <td class="text-center">
                                            <span t-esc="','.join(map( lambda x: x.name, fees.tax_id))"/>
                                        </td>
                                        <td class="text-right o_price_total">
                                            <span t-field="fees.price_subtotal"
                                                t-options='{"widget": "monetary", "display_currency": o.pricelist_id.currency_id}'/>
                                       </td>
                                    </t>
                                </tr>
                            </t>
                        </tbody>
                    </table>

                    <div id="total" class="row justify-content-end">
                        <div class="col-4">
                            <table class="table table-sm">
                                <t t-if="o.invoice_method !='none'">
                                    <tr class="border-black o_subtotal">
                                        <td><strong>Total Without Taxes</strong></td>
                                        <td class="text-right">
                                            <span t-field="o.amount_untaxed"
                                                t-options='{"widget": "monetary", "display_currency": o.pricelist_id.currency_id}'/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Taxes</td>
                                        <td class="text-right o_price_total">
                                            <span t-field="o.amount_tax"
                                                t-options='{"widget": "monetary", "display_currency": o.pricelist_id.currency_id}'/>
                                        </td>
                                    </tr>
                                    <tr class="border-black o_total">
                                        <td><strong>Total</strong></td>
                                        <td class="text-right o_price_total">
                                            <span t-field="o.amount_total"
                                                t-options='{"widget": "monetary", "display_currency": o.pricelist_id.currency_id}'/>
                                        </td>
                                    </tr>
                                </t>
                            </table>
                        </div>
                    </div>

                    <p t-field="o.quotation_notes"/>
                    <div class="oe_structure"/>
                </div>
            </t>
</template>

<template id="report_repairorder2">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="doc">
            <t t-call="repair.report_repairorder" t-lang="doc.partner_id.lang"/>
        </t>
    </t>
</template>
</data>
</odoo>
