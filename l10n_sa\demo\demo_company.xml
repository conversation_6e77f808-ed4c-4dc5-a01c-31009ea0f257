<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_sa" model="res.partner">
        <field name="name">SA Company</field>
        <field name="vat"></field>
        <field name="street">Al <PERSON> Street</field>
        <field name="city">المدينة المنورة</field>
        <field name="country_id" ref="base.sa"/>
        
        <field name="zip">42317</field>
        <field name="phone">+966 51 234 5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.saexample.com</field>
    </record>

    <record id="demo_company_sa" model="res.company">
        <field name="name">SA Company</field>
        <field name="vat">***************</field>
        <field name="partner_id" ref="partner_demo_company_sa"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_sa')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_sa.demo_company_sa'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_sa.sa_chart_template_standard')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_sa.demo_company_sa')"/>
    </function>
</odoo>
