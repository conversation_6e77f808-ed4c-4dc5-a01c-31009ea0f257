# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: resource
#: code:addons/resource/models/resource.py:0
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Actief"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Middag"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 1 week calendar ? All "
"entries will be lost"
msgstr ""
"Ben je zeker dat je de kalender weergave naar 1 week wilt wisselen? Alle "
"boekingen gaan verloren"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 2 weeks calendar ? All "
"entries will be lost"
msgstr ""
"Ben je zeker dat je de kalender weergave naar 2 weken wilt wisselen? Alle "
"boekingen gaan verloren"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Attendances can't overlap."
msgstr "Aanwezigheden kunnen niet overlappen"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Gemiddeld aantal uren per dag"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Gemiddeld aantal uren per dag dat een resource met deze kalender hoort te "
"werken."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Kalender in 2 weken weergave"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Dagen gesloten"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model:ir.model.fields,field_description:resource.field_resource_test__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Bedrijf"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
#: model:ir.model.fields,field_description:resource.field_resource_test__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Dag periode"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Dag v/d week"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Standaard werkuren"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
#: model:ir.model.fields,help:resource.field_resource_test__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Definieert de planning van de resource"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Definieer werkuren en tijdschema dat kan gepland worden voor je projectleden"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
#: model:ir.model.fields,field_description:resource.field_resource_test__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Soort weergave"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Efficiëntie Factor"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Einddatum"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Uitleg"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Eerste"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "First week"
msgstr "Eerste week"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Vrijdag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Afternoon"
msgstr "Vrijdagnamiddag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Morning"
msgstr "Vrijdagochtend"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr ""
"Geeft de reeks van deze regels wanneer ze getoond wordt in de resource "
"kalender."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Algemene verlof"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Groeperen op"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Uren"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Persoon"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
#: model:ir.model.fields,field_description:resource.field_resource_test__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"Indien leeg, dan is dit een algemene vrije dag voor het bedrijf. Als de "
"resource is ingevuld dan geldt de verlof alleen voor deze resource"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Als het actief veld uitstaat, kun je het resource record verbergen zonder "
"deze te verwijderen."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Indien het actief veld is uitgevinkt heb je de mogelijkheid om de werktijd "
"te verbergen, zonder deze te verwijderen."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"In een kalender met 2 weken modus moeten alle periodes in de secties staan."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves____last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource____last_update
#: model:ir.model.fields,field_description:resource.field_resource_test____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
#: model:ir.model.fields,field_description:resource.field_resource_test__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Date"
msgstr "Verlof datum"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr "Verlofdetail"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Materiaal"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Maandag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Afternoon"
msgstr "Maandagnamiddag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Morning"
msgstr "Maandagochtend"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Ochtend"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
#: model:ir.model.fields,field_description:resource.field_resource_test__name
msgid "Name"
msgstr "Naam"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Overige"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Periode"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Reden"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""
"Gekoppelde gebruikersnaam voor de resource om zijn toegang te beheren."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Resource"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Resource Mixin"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Resource verlof"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Resource verlof detail"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Resource werktijd"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Resource's agenda"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Resources"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Resource verlof"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Resources laat je resources maken en beheren die worden betrokken bij een "
"specifieke projectfase. Je kunt ook hun efficiëntie niveau en werkbelasting "
"instellen op basis van hun wekelijkse werktijden."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Zaterdag"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Zoek resource"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Zoek werkperiode verlof"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Werktijd zoeken"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Seconde"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Second week"
msgstr "Tweede week"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Sectie"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: resource
#: code:addons/resource/models/res_company.py:0
#, python-format
msgid "Standard 40 hours/week"
msgstr "Standaard 40 uren/week"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Startdatum"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Start- en eindtijd voor werken.\n"
"Een specifieke waarde van 24:00 wordt geïnterpreteerd and 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Startdatum"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave"
msgstr "Startdatum verlof"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Zondag"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Switch naar 1 week kalender"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Overschakelen naar 2 weken kalender"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Technisch veld voor UI doeleinden."

#. module: resource
#: model:ir.model,name:resource.model_resource_test
msgid "Test Resource Model"
msgstr "Test bron model"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The current week (from %s to %s) correspond to the  %s one."
msgstr "De huidige week (van %s t/m %s ) komt overeen met die %s."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The start date of the time off must be earlier than the end date."
msgstr "De startdatum van het verlof moet eerder zijn dan de einddatum."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
#: model:ir.model.fields,help:resource.field_resource_resource__tz
#: model:ir.model.fields,help:resource.field_resource_test__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Dit veld wordt gebruikt in de order om te definiëren in welke tijdzone de "
"resource zal werken."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Dit veld wordt gebruikt om de verwachte duur van een werkorder in deze "
"werkplek te berekenen. Bijvoorbeeld, als een werkorder een uur duurt en de "
"efficiëntie factor 100% is, zal de verwachte duur 1 uur zijn. Indien de "
"efficiëntie factor 200% is zal de verwachte duur echter 30 minuten zijn."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Donderdag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Afternoon"
msgstr "Donderdag namiddag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Morning"
msgstr "Vrijdagochtend"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Time Off"
msgstr "Verlof"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Soort tijd"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "Tijdefficiëntie moet positief zijn"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
#: model:ir.model.fields,field_description:resource.field_resource_test__tz
msgid "Timezone"
msgstr "Tijdzone"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Tijdzone compensatie"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Dinsdag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Afternoon"
msgstr "Dinsdagnamiddag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Morning"
msgstr "Dinsdagochtend"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Soort"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Gebruiker"

#. module: resource
#: model:ir.model,name:resource.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Woensdag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Afternoon"
msgstr "Woensdagnamiddag"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Morning"
msgstr "Woensdagochtend"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Weeknummer"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Of dit berekend moet worden als afwezige tijd of als werktijd (bijvoorbeeld:"
" formatie)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Werk detail"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Work Resources"
msgstr "Werk resources"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Werk vanaf"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Werk tot"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Werkuren"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Working Hours of %s"
msgstr "Werkuren van %s"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Werktijd"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Times"
msgstr "Werktijden"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "You can't delete section between weeks."
msgstr "Je kunt geen sectie verwijderen tussen weken."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "first"
msgstr "eerste"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "other week"
msgstr "andere week"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "second"
msgstr "seconde"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "this week"
msgstr "deze week"
