<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="coupon_generate_view_form" model="ir.ui.view">
        <field name="name">coupon.generate.wizard.form</field>
        <field name="model">coupon.generate.wizard</field>
        <field name="arch" type="xml">
            <form string="Generate Coupons">
                <group>
                    <field name="has_partner_email" invisible="1"/>
                    <field name="generation_type" widget="radio"/>
                    <field name="partners_domain" attrs="{'invisible': [('generation_type', '!=', 'nbr_customer')]}" widget="domain" options="{'model': 'res.partner'}"/>
                    <field name="nbr_coupons" attrs="{'invisible': [('generation_type', '!=', 'nbr_coupon')]}"/>
                    <field name="template_id"
                        options="{'no_create': True, 'no_edit': True}"
                        context="{'default_model': 'coupon.coupon'}"
                        attrs="{'invisible': [('generation_type', '!=', 'nbr_customer')]}" />
                    <div class="text-muted" attrs="{'invisible': [('generation_type', '!=', 'nbr_customer')]}">
                        Specify a mail template to send the generated coupons as email.
                    </div>
                </group>
                <div role="alert" class="alert alert-warning" attrs="{'invisible': ['|', ('generation_type', '!=', 'nbr_customer'), ('has_partner_email', '=', True)]}">
                    Some selected customers do not have an email address and will not receive the coupon.
                </div>
                <footer>
                    <button name="generate_coupon" type="object" string="Generate" class="oe_highlight" data-hotkey="q"/>
                    <button special="cancel" data-hotkey="z" string="Cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="coupon_generate_action" model="ir.actions.act_window">
        <field name="name">Number of Coupons To Generate</field>
        <field name="res_model">coupon.generate.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="coupon_generate_view_form"/>
    </record>
</odoo>
