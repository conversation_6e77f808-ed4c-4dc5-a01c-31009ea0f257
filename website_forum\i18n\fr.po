# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
msgid "# Answers"
msgstr "# Réponses"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# Favoris"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# Publications"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
msgid "# Views"
msgstr "Nb. de vues"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to accept or refuse an answer."
msgstr "%dkarma requis pour accepter ou refuser une réponse."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to answer a question."
msgstr "%dkarma requis pour répondre à une question."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to close or reopen a post."
msgstr "%dkarma requis pour fermer ou rouvrir un message."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to comment."
msgstr "%dkarma requis pour commenter."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert a comment to an answer."
msgstr "%dkarma requis pour convertir un commentaire en réponse."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert an answer to a comment."
msgstr "%dkarma requis pour convertir une réponse en commentaire."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to convert your comment to an answer."
msgstr "%dkarma requis pour convertir votre commentaire en réponse."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new Tag."
msgstr "%d karma exige de créer une nouvelle étiquette."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to create a new question."
msgstr "%dkarma requis pour créer une nouvelle question."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to delete or reactivate a post."
msgstr "%dkarma requis pour supprimer ou réactiver une publication."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to downvote."
msgstr "%d karma exige de voter."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to edit a post."
msgstr "%dkarma requis pour modifier une publication."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to flag a post."
msgstr "%dkarma requis pour signaler un poste."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to mark a post as offensive."
msgstr "%dkarma requis pour marquer un message comme offensant."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to post an image or link."
msgstr "%dkarma requis pour publier une image ou un lien."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to refuse a post."
msgstr "%dkarma requis pour refuser un poste."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to retag."
msgstr "%d karma exige de retaguer."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a comment."
msgstr "%d karma de délier un commentaire."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to unlink a post."
msgstr "%d karma de délier une publication."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to upvote."
msgstr "%d karma exige de voter"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "%d karma required to validate a post."
msgstr "%dkarma requis pour valider une publication."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "&amp;nbsp;and&amp;nbsp;"
msgstr "&amp;nbsp;et&amp;nbsp;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(La section ci-dessus a été adaptée depuis la FAQ de Stackoverflow)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(votes - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ", by"
msgstr ", par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ", consider <b>adding an example</b>."
msgstr ", pensez à <b>ajouter un exemple</b>."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/ (days + 2) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "0 Answers"
msgstr "0 réponse"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "45% of questions shared"
msgstr "45% des questions publiées"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr "65% de chances en plus d'avoir une réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">Vous avez un message en attente</b>\n"
"Veuillez attendre qu'un modérateur valide votre message précédent pour pouvoir répondre aux questions."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b> [Offensive]</b>"
msgstr "<b> [Offensant]</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Les réponses ne doivent pas ajouter ou développer de questions</b>. "
"Modifiez plutôt la question ou ajoutez un commentaire."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr ""
"<b>Les réponses ne devraient pas ajouter ou développer des questions</b>. "
"Modifiez plutôt la question ou ajoutez un commentaire."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b>Les réponses ne doivent pas commenter d'autres réponses</b>. A la place "
"ajoutez une commentaire sur les autres réponses."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>Les réponses ne doivent pas lancer des débats</b>.Cette communauté de Q&R"
" n'est pas un groupe de discussion. Merci d'éviter de tenir des débats dans "
"vos réponses afin de minimiser le risque de diluer l'essence même de la "
"question et de la réponse. Pour une brève discussion, veuillez utiliser les "
"commentaires."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>Les réponses ne devraient pas simplement renvoyer à d'autres "
"questions</b>. A la place, ajouter une indication dans commentaire du type "
"\"Doublon probable de...\". Cependant, vous pouvez ajouter des liens à "
"d'autres questions ou réponses en fournissant des informations "
"supplémentaires."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Les réponses ne devraient pas simplement renvoyer à d'autres "
"questions</b>. Ajoutez plutôt une indication dans les commentaires, du type "
"<i>\"Doublon probable de...\"</i>. Cependant, vous pouvez ajouter des liens "
"à d'autres questions ou réponses fournissant des informations "
"supplémentaires utiles."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr "<b/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>Avant de demander - faites une recherche pour une question similaire.</b> Vous pouvez chercher dans les questions via leur titre ou leurs étiquettes. Vous pouvez également\n"
"répondre à votre propre question."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr ""
"<b>Veuillez éviter de poser des questions qui sont trop subjectives et "
"controversées</b> ou encore non pertinentes pour cette commaunauté."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>Veuillez essayer de donner une réponse substantielle.</b> Si vous voulez commenter la question ou la réponse,\n"
" <b>utilisez simplement l'outil de commentaire.</b> Rappelez-vous que vous pouvez toujours <b>réviser vos réponses</b>\n"
"        - pas besoin de répondre deux fois à la même question. Aussi <b>n'oubliez pas de voter</b>\n"
"        - cela aide vraiment à sélectionner les meilleures questions et réponses !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<b>Tags</b> I Follow"
msgstr "<b>Etiquettes</b> auxquelles je suis abonné"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid ""
"<b>This forum is empty.</b><br/>\n"
"                    Be the first one asking a question"
msgstr ""
"<b>Ce forum est vide.</b><br/>\n"
"Soyez le premier à poser une question"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What kinds of questions can I ask here?</b>"
msgstr "<b>Quel genre de questions puis-je poser ici ?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my answers?</b>"
msgstr "<b>Que dois-je éviter de mettre dans mes réponses ?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<b>What should I avoid in my questions?</b>"
msgstr "<b>Que dois-je éviter de mettre dans mes questions ?</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr ""
"<b>Pourquoi d'autres personnes peuvent modifier mes questions/réponses ?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>Vous avez déjà un message en attente.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[Réponse]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy badge-gold ml-2\" role=\"img\" aria-label=\"Badge d'or\" title=\"Badge d'or\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<em class=\"d-block mb-2\">or</em>"
msgstr "<em class=\"d-block mb-2\">ou</em>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\" fa fa-comment text-muted mr-1\"/>Comment"
msgstr "<i class=\" fa fa-comment text-muted mr-1\"/>Commentez"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-bell fa-fw\"/> Followed Posts"
msgstr "<i class=\"fa fa-bell fa-fw\"/>Publications suivies"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-bug\"/> Filter Tool"
msgstr "<i class=\"fa fa-bug\"/>Outil de filtrage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw mr-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw mr-1\"/>Accepter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>You've Completely Caught Up!</b><br/>"
msgstr ""
"<i class=\"fa fa-check text-success d-block display-2\"/>\n"
"            <b>Vous avez complètement rattrapé !</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr ""
"<i class=\"fa fa-check\"/>Comment configurer les taxes canadiennes de TPS et"
" TVQ ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-check-square-o fa-fw\"/> To Validate"
msgstr "<i class=\"fa fa-check-square-o fa-fw\"/>A valider"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<i class=\"fa fa-chevron-left mr-1\"/>Back"
msgstr "<i class=\"fa fa-chevron-left mr-1\"/>Retour"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-chevron-left mr-2\"/>Back to All Topics"
msgstr "<i class=\"fa fa-chevron-left mr-2\"/>Retour à tous les sujets"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"fa fa-chevron-left small\"/> Back"
msgstr "<i class=\"fa fa-chevron-left small\"/>Retour"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-flag fa-fw\"/> Flagged"
msgstr "<i class=\"fa fa-flag fa-fw\"/>Signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"                                    Flagged"
msgstr ""
"<i class=\"fa fa-flag ml-4 mr4\"/>\n"
"Signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/>Pays"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Modifier<span class=\"d-none d-lg-inline\"> votre réponse</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-question-circle-o fa-fw\"/> My Posts"
msgstr "<i class=\"fa fa-question-circle-o fa-fw\"/> Mes publications"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-reply mr-1\"/>Answer"
msgstr "<i class=\"fa fa-reply mr-1\"/>Répondre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-share-alt text-muted mr-1\"/>Share"
msgstr "<i class=\"fa fa-share-alt text-muted mr-1\"/>Partager"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Share"
msgstr ""
"<i class=\"fa fa-share-alt text-muted\"/>\n"
"                                Partager"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-star fa-fw\"/> Favourites"
msgstr "<i class=\"fa fa-star fa-fw\"/>Favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "<i class=\"fa fa-tags fa-fw\"/> Followed Tags"
msgstr "<i class=\"fa fa-tags fa-fw\"/> Etiquettes auxquelles vous êtes abonné"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ml-3\" role=\"img\" aria-"
"label=\"Votes négatifs\" title=\"Votes négatifs\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Votes "
"positifs\" title=\"Votes positifs\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw mr-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw mr-1\"/>Rejeter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/>Bonjour à tous! Quelqu'un peut-il m'aider à "
"résoudre mon problème de calcul fiscal au Canada ? Merci !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/>Utilisateur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"font-weight-bold\">Votes</small>"
msgstr "<small class=\"font-weight-bold\">Votes</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    Signalé\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span aria-label=\"Close\">×</span>"
msgstr "<span aria-label=\"Close\">×</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span class=\"badge badge-info\">Closed</span>"
msgstr "<span class=\"badge badge-info\">Fermé</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-bronze ml-2\" role=\"img\" aria-"
"label=\"Badge de bronze\" title=\"Badge de bronze\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy badge-silver ml-2\" role=\"img\" aria-"
"label=\"Badge d'argent\" title=\"Badge d'argent\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No answer posted yet.</span>"
msgstr ""
"<span class=\"font-weight-bold\">Aucune réponse publiée pour le "
"moment.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<span class=\"font-weight-bold\">No question posted yet.</span>"
msgstr ""
"<span class=\"font-weight-bold\">Aucune question postée pour le "
"moment.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"
msgstr "<span class=\"mx-1 d-none d-sm-inline\">&amp;nbsp;|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"
msgstr "<span class=\"mx-1 text-400 d-none d-lg-block\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<span class=\"mx-1\">|</span>"
msgstr "<span class=\"mx-1\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"
msgstr ""
"<span class=\"mx-1\">|</span>\n"
"                    <i class=\"fa fa-star\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"
msgstr "<span class=\"mx-3  mx-lg-2 text-400 d-none d-md-inline\">|</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "<span class=\"navbar-text mr-1\">Go to:</span>"
msgstr "<span class=\"navbar-text mr-1\">Aller à :</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "<span class=\"navbar-text mr-3\">Show Tags Starting By</span>"
msgstr ""
"<span class=\"navbar-text mr-3\">Montrer les Etiquettes en commençant "
"par</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">Favoris</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\">Aller au <br/>Site web</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">Publications</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"                            Best Answer\n"
"                        </span>"
msgstr ""
"<span class=\"o_wforum_answer_correct_badge border small border-success rounded-pill font-weight-bold text-success ml-2 px-2\">\n"
"Meilleure réponse\n"
"</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted mx-3\">or</span>"
msgstr "<span class=\"text-muted mx-3\">ou</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<span>By </span>"
msgstr "<span>Par </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "A clear, explicit and concise title"
msgstr "Un titre clair, explicite et concis"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "Une nouvelle réponse sur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "Une nouvelle question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "About"
msgstr "À propos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Valider une réponse sur vos propres questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Valider une réponse sur n'importe quelle question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "Réponse validée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Valider une réponse"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Access Denied"
msgstr "Accès refusé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "Active"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "Activités"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "Activité"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Add to menu"
msgstr "Ajouter au menu"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Après avoir publié, le système proposera à l'utilisateur de partager sa "
"question ou sa réponse sur les réseaux sociaux, permettant la propagation du"
" contenu du forum."

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "Tous"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All Forums"
msgstr "Tous les Forums"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "All Tags"
msgstr "Toutes les sessions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "Tous les sujets"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "All forums"
msgstr "Tous les forums"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_bump
msgid "Allow Bump"
msgstr "Autoriser les bosses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Amazing! There are no unanswered questions left!"
msgstr "Génial ! Il ne reste aucune question sans réponse !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Answer"
msgstr "Réponse"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "Réponse modifiée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Réponse validée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Réponse validée après 15 votes ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Réponse rejetée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Réponse inappropriée"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "Répondre aux questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Réponse approuvée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "La réponse a été votée plus de 15 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Réponse votée plus de 4 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Réponse plébiscitée plus de 6 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "Réponse validée avec au moins 3 votes"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "Répondu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Posts"
msgstr "Publications répondues"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "Répondu par"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "A répondu à sa propre question avec au moins 4 votes favorables"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Réponses"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Apparaît sur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Archived"
msgstr "Archivé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "Poser des questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "Poser une question sans validation"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "A posé une question et validé une réponse"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "A posé une question avec au moins 150 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "A posé une question avec au moins 250 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "A posé une question avec au moins 500 vues"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "A posé sa première question avec au moins un vote favorable"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "Posée le"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "Poser une question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "Compte des pièces jointes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Auteur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "Groupe Autorisé"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Auteur (autobiographie)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Avatar"
msgstr "Avatar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Back"
msgstr "Retour"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "Retour à la question"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Bad Request"
msgstr "Mauvaise requête"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Badges"
msgstr "Badges"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "Basique"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Be less specific in your wording for a wider search result"
msgstr ""
"Soyez moins précis dans votre formulation pour un résultat de recherche plus"
" large"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Browse All"
msgstr "Parcourir tout"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__bump_date
msgid "Bumped on"
msgstr "Remonté le"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr "En partageant votre réponse, vous gagnerez plus de"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "Peut valider "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "Peut répondre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "Peut demander"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "Peut être automatiquement Validé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "Peut clôturer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "Peut commenter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "Peut convertir pour commenter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "Peut voter défavorablement"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "Peut modifier"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "Peut signaler"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "Peut modérer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "Peut supprimer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "Peut voter favorablement"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "Peut voir"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "Changer les étiquettes des questions"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"Cochez cette case pour afficher un popup pour les questions sans réponses, "
"datant de plus de 10 jours. Le popup proposera de partager la question sur "
"les réseaux sociaux. Lorsque qu'elle est partagée, la question est placée en"
" tête de liste."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Check your spelling and try again"
msgstr "Vérifiez l'orthographe et réessayez"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Commentateur en chef"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click here to accept this answer."
msgstr "Cliquez ici pour valider cette réponse."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate in the "
"forum."
msgstr ""
"Cliquez ici pour envoyer un courriel de vérification vous permettant de "
"participer au forum. "

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to answer."
msgstr "Cliquez pour répondre."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your answer."
msgstr "Cliquez pour publier votre réponse."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Click to post your question."
msgstr "Cliquez pour publier votre question."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
#, python-format
msgid "Close"
msgstr "Fermer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Close Post"
msgstr "Fermer le message"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "Motifs de fermeture"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "Fermer tous les messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "Fermer ses propres messages"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Close post"
msgstr "Fermer le message"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
msgid "Closed"
msgstr "Fermé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "Fermé par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "Fermé le"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Closing"
msgstr "Fermeture"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "Motif de fermeture"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment"
msgstr "Commentaires"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "Commenter les messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "Commenter ses propres messages"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "Commenter ce message..."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.challenge.line,name:website_forum.line_chief_commentator
#: model:gamification.challenge.line,name:website_forum.line_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Commentateur"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comments"
msgstr "Commentaires"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Compléter sa propre biographie"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_configure_profile
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "A complété sa propre biographie"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "Contient des remarques offensantes ou malveillantes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "Contenu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "Convertir toutes les questions en commentaires et vice versa"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert as a answer"
msgstr "Convertir comme réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Convert as a comment"
msgstr "Convertir en commentaire"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "Convertir les commentaires en questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "Convertir ses propres questions en commentaires et vice versa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "Corriger"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "Réponse correcte ou validée"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "Create"
msgstr "Créer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "Date de création"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.action_forum_post
msgid "Create a new forum post"
msgstr "Créez une nouvelle publication sur le forum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Create a new post in this forum by clicking on the button."
msgstr "Créez un nouveau poste dans ce forum en cliquant sur le bouton."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "Créer une nouvelle étiquette"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "Créer de nouvelles étiquettes"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "Créez un tag utilisé par 15 questions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
msgid "Created on"
msgstr "Créé le"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Question crédible"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.challenge.line,name:website_forum.line_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Critiquer "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "Date"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "Date (récent à ancien)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "Date (ancien à récent)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "Par défaut"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Default Sort"
msgstr "Tri par défaut"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Définir la visibilité du défi via les menus"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Delete"
msgstr "Supprimer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "Supprimer n'importe quel message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "Supprimer ses propres messages"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Deleted"
msgstr "Supprimé"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Supprimer son propre message avec 3 votes négatifs ou plus"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Supprimer son propre message avec 3 votes positifs ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "Description"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Description visible on website"
msgstr "Description visible sur le site web"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#, python-format
msgid "Discard"
msgstr "Ignorer"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.challenge.line,name:website_forum.line_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Discipliné"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Discussions"
msgstr "Discussions"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "Discussions (réponses multiples)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "Afficher la biographie détaillée de l'utilisateur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
msgid "Downvote"
msgstr "Voter négativement"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "Dupliquer le poste"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Edit"
msgstr "Modifier"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Answer"
msgstr "Modifier la réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Edit Forum in Backend"
msgstr "Editer le forum depuis le Backend"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Edit Question"
msgstr "Modifier la question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "Modifier n'importe quel message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "Modifier ses propres questions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Modifier vos messages"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.challenge.line,name:website_forum.line_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Éditeur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "Fonctionnalités de l'éditeur: image et liens"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.challenge.line,name:website_forum.line_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Mettre en lumière"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                        <i class=\"fa fa-question-circle\"/>"
msgstr ""
"Exemple\n"
"                        <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Question célèbre"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
msgid "Favorite"
msgstr "Favori"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Question favorite"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "Favori"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_1
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Question favorite (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_stellar_question_25
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Question favorite (25)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_favorite_question_5
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Question favorite (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "Questions favorites"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Favourites"
msgstr "Favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "Filtrer par :"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Paramètre d'adéquation"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Premier vote négatif"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Première modification"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Premier vote positif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Flag"
msgstr "Signaler"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "Marquer un message comme offensant"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Flagged"
msgstr "Marqué"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "Marqué par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "Questions suivies"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Following"
msgstr "Abonné(e)"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 10 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"Par exemple, si vous posez une question intéressante ou donnez une réponse "
"utile, votre participation sera votée favorablement. D'un autre coté, si la "
"réponse est trompeuse - cela  vous sera défavorable. Chaque vote en votre "
"faveur génère 10 points, chaque votre en votre défaveur vous soustrait 10 "
"points. Par jour, un maximum de 200 points accumulés peuvent être attribués "
"pour une question ou un réponse. Le tableau donné à la fin explique la "
"réputation des points d'exigence pour chaque type de tâche de modération."

#. module: website_forum
#: code:addons/website_forum/models/website.py:0
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#, python-format
msgid "Forum"
msgstr "Forum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Forum Mode"
msgstr "Mode forum"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#, python-format
msgid "Forum Name"
msgstr "Nom du Forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "Message du forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr "Messages du forum"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Tag du forum"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "Forums"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forums_count
msgid "Forums Count"
msgstr "Nombre de forums"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Défi de ludification"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Give your post title."
msgstr "Donnez un titre à votre poste."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Bonne réponse"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_answer
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Bonne réponse (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Bonne question "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "Graphique des messages publiés"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Super Réponse"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_answer
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Super Réponse (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Super Question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Regrouper par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Guidelines"
msgstr "Directives"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Gourou"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_guru
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Gourou (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "A répondu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "A un message"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Aide"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "Voici un tableau avec les privilèges et le niveau de karma"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Hide Intro"
msgstr "Masquer l'intro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "I'm <b>Following</b>"
msgstr "Je suis <b>Abonné</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "I'm Following"
msgstr "Je suis "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si actif, certains messages ont une erreur de livraison."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Si l'auteur n'a pas assez de karma, un attribut no-follow est ajouté aux "
"liens"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr ""
"Si cette approche n'est pas pour vous, veuillez respecter la communauté."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Si vous clôturez ce message, il sera caché pour la plupart des utilisateurs."
" Seuls les utilisateurs ayant un karma élévé peuvent voir les messages "
"clôturés, afin de les modérer."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"Si vous correspondez à l'un de ces exemples ou si votre motivation à poser "
"la question est “Je voudrai participer à une discussion au sujet de ______”,"
" dans ce cas vous devriez vous rendre sur nos mailing listes. Cependant, si "
"votre motivation est “J'aimerai que les autres m'expliquent ________” alors "
"vous êtes au bon endroit. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Si vous signalez ce message comme offensant, il sera caché pour la plupart "
"des utilisateurs. Seuls les utilisateurs ayant un karma élevé peuvent voir "
"les messages signalés, afin de les modérer."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "Image"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "Image 1024"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "Image 128"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "Image 256"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "Image 512"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "Déclarations inappropriées et inacceptables"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Insert tags related to your question."
msgstr "Insérez des étiquettes (tags) à votre question."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "Langage insultant et offensant"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "Est favori"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "A été répondu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "C'est la biographie de l'auteur visible depuis ses messages"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to modify someone else's vote."
msgstr "Il n'est pas permis de modifier le vote de quelqu'un d'autre."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "It is not allowed to vote for its own post."
msgstr "Il n'est pas permis de voter pour sa propre publication."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Karma"
msgstr "Karma"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Karma Error"
msgstr "Erreur de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Gains de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "Droits liés au Karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "Karma pour fermer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "Karma pour commenter"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma pour convertir un commentaire en réponse"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "Editer le karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "Karma pour délier"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote____last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
msgid "Last Post"
msgstr "Dernière publication"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Last Post:"
msgstr "Dernière publication : "

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__write_date_desc
msgid "Last Updated"
msgstr "Dernière mise à jour"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "Dernière activié"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "A laissé 10 réponses avec un score de 10 ou plus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_main_attachment_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Mark as Best Answer"
msgstr "Marquer comme meilleure réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "Marquer comme offensant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "Marquer comme spam"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__menu_id
msgid "Menu"
msgstr "Menu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "Messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "Mode"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "Modérer les messages"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation"
msgstr "Modération"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "Moderation tools"
msgstr "Outils de modération"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "More"
msgstr "Plus"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "Mais encore:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "Les plus approuvés"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "Le plus demandé:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "Le plus voté:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Move this question to the top of the list by sharing it on social networks."
msgstr ""
"Remontez cette question au début de la liste en la partageant sur les "
"réseaux sociaux."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Favourites</b>"
msgstr "Mes <b>Favoris</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My <b>Posts</b>"
msgstr "Mes <b>publications</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Favourites"
msgstr "Mes favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "My Posts"
msgstr "Mes publications"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "Mon vote:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "My profile"
msgstr "Mon profil"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "Nom"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Negative vote"
msgstr "Vote négatif"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Nouvelle réponse"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:0
#, python-format
msgid "New Forum"
msgstr "Nouveau Forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Post"
msgstr "Nouvelle Publication"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Nouvelle Question"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Newest"
msgstr "Le plus récent"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Chouette réponse"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_answer
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Chouette réponse (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Chouette question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "No activities yet!"
msgstr "Pas encore d'activités !"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "No favourite questions in this forum (yet).<br/>"
msgstr "Pas (encore) de questions favorites dans ce forum.<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No flagged posts"
msgstr "Aucun message signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "Forum pas encore disponible."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "No post to be validated"
msgstr "Aucun poste à valider"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "No tags"
msgstr "Pas d'étiquette"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "No vote given by you yet!"
msgstr "Vous n'avez encore donné aucun vote !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "Liens nofollow"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "N'est pas un vrai poste"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "Pas pertinent ou obsolète"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Question pertinente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "Nombre de messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "Nombre de messages signalés"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Nombre de messages exigeant une action"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Nombre de messages en attente de validation"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "Hors sujet ou non pertinent"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Offensive"
msgstr "Offensant"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Offensive Post"
msgstr "Message offensant"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "On average,"
msgstr "En moyenne,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Only one answer per question is allowed"
msgstr "Une seule réponse par question est autorisée"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Options"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Order and Visibility"
msgstr "Ordre et visibilité"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Order by"
msgstr "Trier par "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Our forums"
msgstr "Nos forums"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.challenge.line,name:website_forum.line_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Pression du groupe"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "People"
msgstr "Contacts"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "Contenu simplifié"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Veuillez remplir ce champ"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr ""
"Veuillez attendre qu'un modérateur valide votre message précédent avant de "
"continuer."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Question populaire"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_popular_question
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Question populaire (150)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_notable_question
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Question populaire (250)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_famous_question
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Question populaire (500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Positive vote"
msgstr "Vote positif"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Publier"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Answer"
msgstr "Publier votre réponse "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "Publier des réponses"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reasons_action
msgid "Post Close Reasons"
msgstr "Motif de fermeture du poste"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Motif de fermeture"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Post Comment"
msgstr "Publier un commentaire"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "Vote publication"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Publier votre question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr "Message :"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "10 commentaires publiés"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "100 commentaires publiés"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr ""
"Il n'est pas possible de poster une réponse à une question [Supprimée] ou "
"[Clôturée]."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_posts
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Posts"
msgstr "Articles"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_options
msgid "Posts:"
msgstr "Publications :"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
#, python-format
msgid "Privacy"
msgstr "Confidentialité"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
#, python-format
msgid "Public"
msgstr "Public"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"Public : Le forum est public\n"
"Utilisateurs connectés : Le forum est visible pour les utilisateurs connectés\n"
"Certains utilisateurs : Le forum et ses contenus sont pour les nom-membres des groupes sélectionnés"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Public: Forum is public\\nSigned In: Forum is visible for signed in "
"users\\nSome users: Forum and their content are hidden for non members of "
"selected group"
msgstr ""
"Public : Le forum est public\n"
"Utilisateurs connectés : Le forum est visible pour les utilisateurs connectés\n"
"Certains utilisateurs : Le forum et ses contenus sont pour les nom-membres des groupes sélectionnés"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.challenge.line,name:website_forum.line_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Expert"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your answer here."
msgstr "Mettez votre réponse ici"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
#, python-format
msgid "Put your question here."
msgstr "Mettez votre question ici."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question"
msgstr "Question"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "Questions modifiées"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question by"
msgstr "Question par "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Question votée défavorablement"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Question non trouvée"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Question ajoutée comme en favori par 1 utilisateur"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Question ajoutée en favori par 25 utilisateurs"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Question ajoutée en favori par 5 utilisateurs"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Question should not be empty."
msgstr "La question ne peut pas être vide."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Question votée favorablement"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Question votée plus de 15 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Question votée plus de 4 fois"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Question votée plus de 6 fois"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "Questions"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "Questions (1 réponse)"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Questions and Answers"
msgstr "Questions et Réponses"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid ""
"Questions and Answers mode: only one answer allowed\\n Discussions mode: "
"multiple answers allowed"
msgstr ""
"Mode questions et réponses : une seule réponse autorisée Mode discussions : "
"plusieurs réponses autorisées"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"Mode questions: une seule réponse autorisée\n"
"  Mode discussions: plusieurs réponses autorisées"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "Les discours haineux ou raciste"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "Rangs"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "Re: %s"
msgstr "Re: %s"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Read the guidelines to know how to gain karma."
msgstr "Lisez les lignes directrices pour savoir comment gagner du karma."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Read: #{question.name}"
msgstr "Lire : #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "Motif"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "Motif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "Motif:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "Raisons"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "Au moins 3 votes ont été reçus pour une réponse pour la première fois"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "Refuser"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Register"
msgstr "S'inscrire"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "Pertinence"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "Calcul de pertinence"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Reopen"
msgstr "Rouvrir"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Reply should not be empty."
msgstr "La réponse ne devrait pas être vide."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "Répondre à sa propre question"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict publishing to this website."
msgstr "Limiter la publication sur ce site web."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the forum."
msgstr "Revenir au forum"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Return to the question list."
msgstr "Retour à la liste de question"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "Revu par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Référencement optimisé"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "Sauver les changements"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.challenge.line,name:website_forum.line_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Erudit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Search Tips"
msgstr "Conseils de recherche"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "Chercher dans les publications"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "Recherche…"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Deuxième paramètre d'adéquation"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "See"
msgstr "Voir"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "Voir la publication"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "Voir la question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "Tout sélectionner"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Select Authorized Group"
msgstr "Sélectionner le groupe autorisé"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.challenge.line,name:website_forum.line_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Auto-didacte"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Nom SEO"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr ""
"Partagez ce contenu pour augmenter vos chances d'être mis en avant sur la "
"première page et attirez plus de visiteurs."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "Options de partage"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Show"
msgstr "Montrer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Show Tags Starting By"
msgstr "Montrer les étiquettes en commençant par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Showing results for"
msgstr "Affichage des résultats pour"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Sign in"
msgstr "Se connecter"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
#, python-format
msgid "Signed In"
msgstr "Utilisateurs connectés"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Solved"
msgstr "Résolu"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_templates.xml:0
#, python-format
msgid "Some Users"
msgstr "Certains utilisateurs"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "Certains utilisateurs"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged in to perform this action"
msgstr ""
"Désolé, vous devez être connecté pour pouvoir effectuer cette opération"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr "Vous devez être enregistré pour signaler un message"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "Vous devez être enregistré pour pouvoir voter. "

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr "Un utilisateur anonyme ne peut pas accepter une réponse."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Désolé, cette question n'est plus disponible."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sorry, we could not find any <b>%s</b> result <b>%s</b> %s%s%s."
msgstr "Désolé, nous n'avons trouvé aucun <b>%s</b> résultat <b>%s</b>%s%s%s."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr "Il n'est pas possible de voter pour votre propre message. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "Spammer toute la publication."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "Spam ou publicité"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "État"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Question stellaire"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Etudiant"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.challenge.line,name:website_forum.line_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Supporter"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "Étiquette"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Ce nom d'étiquette existe déjà !"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Tags"
msgstr "Étiquettes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Tags I Follow"
msgstr "Etiquettes auxquelles je suis abonné"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.challenge.line,name:website_forum.line_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Taxonomiste"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.challenge.line,name:website_forum.line_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Professeur"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "Teaser"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"Champ technique permettant de promouvoir une question. Modifier ce champ "
"changera le champ write_date et placera la question en tête de liste. Ecrire"
" directement sur la write_date n'est actuellement pas possible et ce champ "
"est une solution de contournement."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "Thanks for posting!"
msgstr "Merci pour votre message ! "

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr ""
"Le but de ce site est de créer une base de connaissances pertinente qui "
"répond aux questions liées à Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "La question est close"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"Par conséquent, les questions et réponses peuvent être modifiées comme des "
"pages wiki par des utilisateurs expérimentés de ce site afin d'améliorer la "
"qualité globale du contenu de la base de connaissances. Ces privilèges sont "
"accordés en fonction du niveau de karma de l'utilisateur : vous pourrez "
"faire de même une fois que votre karma sera suffisamment élevé."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers."
msgstr ""
"Cette communauté est destinée aux utilisateurs, partenaires et programmeurs "
"professionnels et passionnés."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"Cette communauté est destinée aux utilisateurs, partenaires et programmeurs "
"professionnels et passionnés. Vous pouvez poser des questions sur :"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and services.\n"
"                                        <br>Share and discuss the best content and new marketing ideas, build your professional profile and become a better marketer together."
msgstr ""
"Cette communauté est déidée aux professionnels et aux personnes intéressées par nos produits et services.\n"
"                                        <br>Partagez et discutez du meilleur contenu et de nouvelles idées marketing, construisez votre profil professionnel et devenez de meilleurs marketeurs ensemble."

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Cette communauté est dédiée aux professionnels et utilisateurs enthousiastes"
" de nos produits et services. Partagez et discutez du meilleur contenu et de"
" nouvelles idées marketing, construisez votre profil professionnel et "
"devenez de meilleurs vendeurs tous ensemble."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Cette formule est utilisée afin de trier par pertinence. La variable 'votes'"
" représente le nombre de votes, et 'days' est le nombre de jours depuis la "
"création du message."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "Ce forum a été archivé."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post can not be flagged"
msgstr "Ce message ne peut être signalé"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:0
#, python-format
msgid "This post is already flagged"
msgstr "Ce message a déjà été signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and it's not published yet.<br/>\n"
"                Do you want <b>Accept</b> or <b>Reject</b> this post ?"
msgstr ""
"Ce message est en attente de modération et n'est pas encore publié.<br/>\n"
"                Voulez-vous <b>Accepter</b> ou <b>Refuser</b> ce message ?"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "Langage menaçant "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "Titre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "Le titre ne peut être vide"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:0
#, python-format
msgid "Title should not be empty."
msgstr "Le titre ne peut être vide"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "Vers"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "To Validate"
msgstr "A Valider"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr ""
"Pour éviter que votre question soit signalée et éventuellement supprimée, "
"évitez de poser des questions subjectives où…"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Toggle favorite status"
msgstr "Basculer en status favoris"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "Trop localisé"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "Trop subjectif et argumentatif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Toolbar with button groups"
msgstr "Barre d'outils avec groupes de boutons"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_nav_header
msgid "Topics"
msgstr "Sujets"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Answers"
msgstr "Total des réponses"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Favorites"
msgstr "Total des favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Total Views"
msgstr "Total des vues"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "Nombre total de votes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Trending"
msgstr "Tendance"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Try searching for one or two words"
msgstr "Essayez de chercher un ou deux mots"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "Sans réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Undelete"
msgstr "Dé-supprimer"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "Délier tous les commentaires"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "Délier vos propres commentaires"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "Unmark as Best Answer"
msgstr "Désélectionner comme meilleure réponse"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_unread_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Compteur de messages non lus"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unsolved"
msgstr "Non résolu"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "Mis à jour par"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "Mis à jour le "

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
msgid "Upvote"
msgstr "Voter positivement"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_student
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Question votée (1)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_great_question
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Question votée (15)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_nice_question
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Question votée (4)"

#. module: website_forum
#: model:gamification.challenge.line,name:website_forum.line_good_question
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Question votée (6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "Utilisez un titre clair, explicite et concis"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "Utilisateur"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_favorites
msgid "Users favorite posts"
msgstr "Postes favoris des utilisateurs"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.question_dropdown
msgid "Validate"
msgstr "Valider"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "Valider la question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "View"
msgstr "Vue"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Views"
msgstr "Vues"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "Langage violent"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "Voter"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists !"
msgstr "Le vote existe déjà !"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "Votes"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "En attente de validation"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "En attente de validation"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users__forum_waiting_posts_count
msgid "Waiting post"
msgstr "Article en attente de publication"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "Site Web"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "Site web / Forum"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "Description du site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Mots-clés du site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "Titre du site"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Image opengraph"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "Message d'accueil"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "Bienvenue !"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"Quand une question ou réponse est votée, l'utilisateur qui l'a publié gagne "
"des points, qui sont appelés \"points de karma'. Ces points servent de "
"mesure approximative pour la confiance de la communauté envers cette "
"personne. Différentes tâches de modération sont attribuées graduellement à "
"l'utilisateur sur base de ces points."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You already have a pending post"
msgstr "Vous avez déjà un message en attente"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "You can share your question once it has been validated"
msgstr "Vous pouvez partager votre question une fois qu'elle a été validée."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:0
#, python-format
msgid "You cannot create recursive forum posts."
msgstr "Vous ne pouvez pas créer des publications Forum récursives. "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "Vous ne pouvez pas poster une réponse vide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You have no posts in this forum (yet)."
msgstr "Vous n'avez pas (encore) de messages dans ce forum."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "Vous pouvez maintenant participer à nos forums."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "Vous devez avoir un meilleur karma pour modifier les étiquettes"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"Vous devriez ne poser que des questions pratiques et qui ont une réponse, "
"basée sur des problèmes que vous rencontrez. Les questions ouvertes et qui "
"portent débat diminuent l'utilité de ce site et poussent les autres "
"questions hors de la première page."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "You're not following any topic in this forum (yet).<br/>"
msgstr "Vous ne suivez (encore) aucun sujet sur ce forum.<br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Votre réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "Votre réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "Votre favori"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Closed]"
msgstr "[Fermé]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Deleted]"
msgstr "[Supprimé]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "[Offensive]"
msgstr "[Offensant]"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "Valider n'importe quelle réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "activity date"
msgstr "date d'activité"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "breadcrumb"
msgstr "fil d'Ariane"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "par"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "fermer n'importe quel message"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "supprimer tous les commentaires"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "effacez n'importe quelle question ou réponse"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "supprimer son propre commentaire"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "vote négatif"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "e.g. Help"
msgstr "par ex. Aide"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "e.g. Who to do this particular thing?"
msgstr "par ex. Qui pour faire cette chose particulière ?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "modifiez n'importe quel message, voir les messages signalés"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "chaque réponse est valable: \"Quel est votre _______ préféré ?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "signaler des messages, fermer vos propres questions"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "pour la raison suivante:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""
"a été publiée et nécessite votre validation. Cliquez ici pour accéder à la "
"question :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "a été publiée. Cliquez ici pour accéder au message :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "a été publiée. Cliquez ici pour accéder à la question :"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "here"
msgstr "ici"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"comment configurer ou personnaliser Odoo aux besoins business spécifiques,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "comment développer des modules pour vos propres besoins,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "Comment installer Odoo pour une infrastructure spécifique,"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"si votre\n"
"réponse est acceptée. Regardez ce que vous pouvez faire avec vos points de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your favourites"
msgstr "dans vos favoris"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your followed list"
msgstr "dans votre liste de suivi"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "in your posts"
msgstr "dans vos postes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "insérer des liens hypertextes, charger des documents"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "instead."
msgstr "à la place."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "C'est une critique déguisée en question: \"_____ est nul, pas vrai ?\""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid "karma points"
msgstr "points de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "matching \""
msgstr "correspondant à \""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most answered"
msgstr "plus répondu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "most voted"
msgstr "plus voté"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "newest"
msgstr "le plus récent"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "le"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:0
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"sur les réseaux sociaux obtiennent une réponse endéans\n"
"les 5 heures. Les questions partagées sur deux réseaux sociaux ont"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "post"
msgstr "message"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "solved"
msgstr "résolu"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "questions spécifiques sur les services, offres,... d'Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "tag"
msgstr "étiquette"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"il n'y a pas de problème à résoudre: \"Je suis curieux de voir si les gens "
"ont le même ressenti que moi.\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "to partecipate"
msgstr "pour participer"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "trending"
msgstr "tendance"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unanswered"
msgstr "non répondu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "unsolved"
msgstr "non résolu"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "voter favorablement, ajouter des commentaires"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "using the"
msgstr "utilisant"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"une question ouverte, hypothétique nous est posée: \"Que se passerait-il si"
"  ________ ?\""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"quelle est la meilleure manière d'utiliser Odoo pour un besoin business "
"spécifique,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"la réponse est fournie avec la question, et d'autres réponses sont "
"attendues: \"J'utilise_____ pour _____, qu'utilisez-vous ?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "votre biographie est visible en infobulle"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "| Flagged"
msgstr "| Signalé"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "圾 Text"
msgstr "圾 Texte"
