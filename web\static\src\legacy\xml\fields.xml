<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="web.FieldBoolean" owl="1">
        <CustomCheckbox disabled="hasReadonlyModifier"
            value="value" class="o_field_boolean" t-on-change="_onChange"/>
    </t>

    <t t-name="web.FieldBadge" owl="1">
        <span class="badge badge-pill o_field_badge" t-esc="_formatValue(value)"/>
    </t>

</templates>
