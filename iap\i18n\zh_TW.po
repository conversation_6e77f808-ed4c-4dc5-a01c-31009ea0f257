# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: 敬雲 林 <<EMAIL>>, 2021\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "帳號資訊"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "帳戶代碼(token)"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Buy credits"
msgstr "購買點數"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "公司"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
msgid "Created by"
msgstr "創立者"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
msgid "Created on"
msgstr "建立於"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Documentation"
msgstr "系統使用說明"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "IAP 帳戶"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "IAP 帳戶"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP Lead 點數 API"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
msgid "ID"
msgstr "ID"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Insufficient Balance"
msgstr "餘額不足"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#, python-format
msgid "Insufficient credit to perform this service."
msgstr "額度不足，無法執行此服務。"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr "開啟 IAP 帳戶"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr "服務名稱"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/js/crash_manager.js:0
#, python-format
msgid "Start a Trial at Odoo"
msgstr "在odoo中開始試用"

#. module: iap
#: code:addons/iap/tools/iap_tools.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr "此服務請求的 URL 返回錯誤。請與應用程式的作者聯繫。它嘗試聯繫的 URL 是 %s"

#. module: iap
#. openerp-web
#: code:addons/iap/static/src/xml/iap_templates.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
#, python-format
msgid "View My Services"
msgstr "查看我的服務"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "查看您的 IAP 服務並為您的點數加值"
