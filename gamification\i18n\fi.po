# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* gamification
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <mi<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__rank_users_count
msgid "# Users"
msgstr "# Käyttäjiä"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has joined the challenge"
msgstr "%s on liittynyt haasteeseen"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "%s has refused the challenge"
msgstr "%s on hylännyt haasteen"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "<br/> %(rank)d. %(user_name)s - %(reward_name)s"
msgstr "<br/> %(rank)d. %(user_name)s - %(reward_name)s"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Nobody has succeeded to reach every goal, no badge is rewarded for this"
" challenge."
msgstr ""
"<br/>Kukaan ei ole onnistunut saavuttamaan kaikkia tavoitteita, joten tästä "
"haasteesta ei ole vielä myönnetty ansiomerkkejä."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Reward (badge %(badge_name)s) for every succeeding user was sent to "
"%(users)s."
msgstr ""
"<br/>Palkinto (ansiomerkki %(badge_name)s) lähetettiin jokaiselle "
"onnistuneelle käyttäjälle: %(users)s."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid ""
"<br/>Special rewards were sent to the top competing users. The ranking for "
"this challenge is :"
msgstr ""
"<br/>Erityispalkinto lähetettiin parhaiten menestyneille käyttäjille. Tämän "
"haasteen tuloslistaus:"

#. module: gamification
#: model:mail.template,body_html:gamification.mail_template_data_new_rank_reached
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"<table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"    <tbody>\n"
"        <tr>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>\n"
"                    Congratulations\n"
"                    <span t-out=\"object.name or ''\">Joel Willis</span>!\n"
"                </p>\n"
"                <p>\n"
"                    You just reached a new rank : <strong t-out=\"object.rank_id.name or ''\">Newbie</strong>\n"
"                </p>\n"
"                <t t-if=\"object.next_rank_id.name\">\n"
"                    <p>Continue your work to become a <strong t-out=\"object.next_rank_id.name or ''\">Student</strong> !</p>\n"
"                </t>\n"
"                <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                    <t t-set=\"gamification_redirection_data\" t-value=\"object.get_gamification_redirection_data()\"/>\n"
"                    <t t-foreach=\"gamification_redirection_data\" t-as=\"data\">\n"
"                        <t t-set=\"url\" t-value=\"data['url']\"/>\n"
"                        <t t-set=\"label\" t-value=\"data['label']\"/>\n"
"                        <a t-att-href=\"url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-out=\"label or ''\">LABEL</a>\n"
"                    </t>\n"
"                </div>\n"
"            </td>\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p style=\"text-align: center;\">\n"
"                    <img t-attf-src=\"/web/image/gamification.karma.rank/{{ object.rank_id.id }}/image_128\"/>\n"
"                </p>\n"
"            </td>\n"
"        </tr>\n"
"        <tr t-if=\"user.signature\">\n"
"            <td style=\"padding:15px 20px 10px 20px;\">\n"
"                <t t-out=\"user.signature or ''\">--<br/>Mitchell Admin</t>\n"
"            </td>\n"
"        </tr>\n"
"    </tbody>\n"
" </table>\n"
"</div>"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_goal_reminder
msgid ""
"<div>\n"
"    <strong>Reminder</strong><br/>\n"
"    You have not updated your progress for the goal <t t-out=\"object.definition_id.name or ''\"/> (currently reached at <t t-out=\"object.completeness or ''\"/>%) for at least <t t-out=\"object.remind_update_delay or ''\"/> days. Do not forget to do it.\n"
"    <br/><br/>\n"
"    Thank you,\n"
"    <t t-if=\"object.challenge_id.manager_id.signature\">\n"
"        <br/>\n"
"        <t t-out=\"object.challenge_id.manager_id.signature or ''\"/>\n"
"    </t>\n"
"</div>"
msgstr ""

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Goal in Progress\" "
"aria-label=\"Goal in Progress\"/>"
msgstr ""
"<i role=\"img\" class=\"fa fa-clock-o fa-3x\" title=\"Keskeneräinen "
"tavoite\" aria-label=\"Keskeneräinen tavoite\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Goal Reached\" "
"aria-label=\"Goal Reached\"/>"
msgstr ""
"<i role=\"img\" class=\"o_green fa fa-check fa-3x\" title=\"Tavoite "
"saavutettu\" aria-label=\"Tavoite saavutettu\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Goal Failed\" "
"aria-label=\"Goal Failed\"/>"
msgstr ""
"<i role=\"img\" class=\"o_red fa fa-times fa-3x\" title=\"Tavoite "
"epäonnistunut\" aria-label=\"Tavoite epäonnistunut\"/>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"<span class=\"o_stat_text\">Related</span>\n"
"                                <span class=\"o_stat_text\">Goals</span>"
msgstr ""
"<span class=\"o_stat_text\">Liittyvät</span>\n"
"                                <span class=\"o_stat_text\">tavoitteet</span>"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "<span class=\"o_stat_text\">Users</span>"
msgstr "<span class=\"o_stat_text\">Käyttäjät</span>"

#. module: gamification
#: model:mail.template,body_html:gamification.email_template_badge_received
msgid ""
"<table border=\"0\" cellpadding=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" width=\"590\" cellpadding=\"0\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Badge</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.badge_id.name or ''\"/>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"                    <div>\n"
"                        Congratulations <t t-out=\"object.user_id.name or ''\"/> !<br/>\n"
"                        You just received badge <strong t-out=\"object.badge_id.name or ''\"/> !<br/>\n"
"                        <table t-if=\"not is_html_empty(object.badge_id.description)\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" style=\"width: 560px; margin-top: 5px;\">\n"
"                            <tbody><tr>\n"
"                                <td valign=\"center\">\n"
"                                    <img t-attf-src=\"/web/image/gamification.badge/{{ object.badge_id.id }}/image_128/80x80\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                                </td>\n"
"                                <td valign=\"center\">\n"
"                                    <cite t-out=\"object.badge_id.description or ''\"/>\n"
"                                </td>\n"
"                            </tr></tbody>\n"
"                        </table>\n"
"                        <br/>\n"
"                        <t t-if=\"object.sender_id\">\n"
"                            This badge was granted by <strong t-out=\"object.sender_id.name or ''\"/>.\n"
"                        </t>\n"
"                        <br/>\n"
"                        <t t-if=\"object.comment\" t-out=\"object.comment or ''\"/>\n"
"                        <br/><br/>\n"
"                        Thank you,\n"
"                        <t t-if=\"object.sender_id.signature\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.sender_id.signature or ''\"/>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: white; font-size: 12px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table width=\"590\" border=\"0\" cellpadding=\"0\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 14px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=gamification\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: gamification
#: model:mail.template,body_html:gamification.simple_report_template
msgid ""
"<table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"background-color: #EEE; border-collapse: collapse;\">\n"
"<tr>\n"
"    <td valign=\"top\" align=\"center\">\n"
"        <t t-set=\"object_ctx\" t-value=\"ctx.get('object')\"/>\n"
"        <t t-set=\"company\" t-value=\"object_ctx and object_ctx.company_id or user.company_id\"/>\n"
"        <t t-set=\"challenge_lines\" t-value=\"ctx.get('challenge_lines', [])\"/>\n"
"        <table cellspacing=\"0\" cellpadding=\"0\" width=\"600\" style=\"margin: 0 auto; width: 570px;\">\n"
"            <tr><td>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n"
"                    <tr>\n"
"                        <div>\n"
"                            <t t-if=\"object.visibility_mode == 'ranking'\">\n"
"                                <td style=\"padding:15px;\">\n"
"                                    <p style=\"font-size:20px;color:#666666;\" align=\"center\">Leaderboard</p>\n"
"                                </td>\n"
"                            </t>\n"
"                        </div>\n"
"                    </tr>\n"
"                </table>\n"
"                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" bgcolor=\"#fff\" style=\"background-color:#fff;\">\n"
"                    <tr><td style=\"padding: 15px;\">\n"
"                        <t t-if=\"object.visibility_mode == 'personal'\">\n"
"                            <span style=\"color:#666666;font-size:13px;\">Here is your current progress in the challenge <strong t-out=\"object.name or ''\"/>.</span>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:20px;\">\n"
"                                <tr>\n"
"                                    <td align=\"center\">\n"
"                                        <div>Personal Performance</div>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;color:#666666;\">\n"
"                                <thead>\n"
"                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                        <th align=\"left\" style=\"padding-bottom: 0px;width:40%;text-align:left;\">Goals</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"left\">Target</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Current</th>\n"
"                                        <th style=\"width:20%;text-align:right;\" align=\"right\">Completeness</th>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                    </tr>\n"
"                                </thead>\n"
"                                <tbody t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                    <tr style=\"font-weight:bold;\">\n"
"                                        <td style=\"padding: 20px 0;\" align=\"left\">\n"
"                                            <t t-out=\"line['name'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                (<t t-out=\"line['full_suffix'] or ''\"/>)\n"
"                                            </t>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;\" align=\"right\"><t t-out=\"&quot;%.2f&quot; % line['current'] or ''\"/>\n"
"                                            <t t-if=\"line['suffix']\" t-out=\"line['suffix'] or ''\"/>\n"
"                                        </td>\n"
"                                        <td style=\"padding: 20px 0;font-size:25px;color:#9A6C8E;\" align=\"right\"><strong><t t-out=\"int(line['completeness']) or ''\"/>%</strong></td>\n"
"                                    </tr>\n"
"                                    <tr>\n"
"                                        <td colspan=\"5\" style=\"height:1px;background-color:#e3e3e3;\"/>\n"
"                                    </tr>\n"
"                                </tbody>\n"
"                            </table>                   \n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <span style=\"color:#A8A8A8;font-size:13px;\">\n"
"                                Challenge: <strong t-out=\"object.name or ''\"/>.\n"
"                            </span> \n"
"                            <t t-foreach=\"challenge_lines\" t-as=\"line\">\n"
"                                <!-- Header + Button table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:35px;\">\n"
"                                    <tr>\n"
"                                        <td width=\"50%\">\n"
"                                            <div>Top Achievers for goal <strong t-out=\"line['name'] or ''\"/></div>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table>\n"
"                                <!-- Podium -->\n"
"                                <t t-if=\"len(line['goals']) == 2\">\n"
"                                    <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:10px;\">\n"
"                                        <tr><td style=\"padding:0 30px;\">\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"table-layout: fixed;\">\n"
"                                                <tr>\n"
"                                                    <t t-set=\"top_goals\" t-value=\"line['goals'][:3]\"/>\n"
"                                                    <t t-foreach=\"top_goals\" t-as=\"goal\">\n"
"                                                        <td align=\"center\" style=\"width:32%;\">\n"
"                                                            <t t-if=\"loop.index == 1\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:40px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"95\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"75\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#b898b0'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"50\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'2'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 2\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"''\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"85\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'1'\"/>\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"loop.index == 3\">\n"
"                                                                <t t-set=\"extra_div\" t-value=\"'&lt;div style=&quot;height:60px;&quot;&gt;&lt;/div&gt;'\"/>\n"
"                                                                <t t-set=\"heightA\" t-value=\"115\"/>\n"
"                                                                <t t-set=\"heightB\" t-value=\"55\"/>\n"
"                                                                <t t-set=\"bgColor\" t-value=\"'#c8afc1'\"/>\n"
"                                                                <t t-set=\"fontSize\" t-value=\"35\"/>\n"
"                                                                <t t-set=\"podiumPosition\" t-value=\"'3'\"/>\n"
"                                                            </t>\n"
"                                                            <div style=\"margin:0 3px 0 3px;height:220px;\">\n"
"                                                                <div t-attf-style=\"height:{{ heightA }}px;\">\n"
"                                                                    <t t-out=\"extra_div or ''\"/>\n"
"                                                                    <div style=\"height:55px;\">\n"
"                                                                        <img style=\"margin-bottom:5px;width:50px;height:50px;border-radius:50%;object-fit:cover;\" t-att-src=\"image_data_uri(object.env['res.users'].browse(goal['user_id']).partner_id.image_128)\" t-att-alt=\"goal['name']\"/>\n"
"                                                                    </div>\n"
"                                                                    <div align=\"center\" t-attf-style=\"color:{{ bgColor }};height:20px\">\n"
"                                                                        <t t-out=\"goal['name'] or ''\"/>\n"
"                                                                    </div>\n"
"                                                                </div>\n"
"                                                                <div t-attf-style=\"background-color:{{ bgColor }};height:{{ heightB }}px;\">\n"
"                                                                    <strong><span t-attf-style=\"color:#fff;font-size:{{ fontSize }}px;\" t-out=\"podiumPosition or ''\"/></strong>\n"
"                                                                </div>\n"
"                                                                <div style=\"height:30px;\">\n"
"                                                                    <t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/>\n"
"                                                                    <t t-if=\"line['suffix'] or line['monetary']\">\n"
"                                                                        <t t-out=\"line['full_suffix'] or ''\"/>\n"
"                                                                    </t>\n"
"                                                                </div>\n"
"                                                            </div>\n"
"                                                        </td>\n"
"                                                    </t>\n"
"                                                </tr>\n"
"                                            </table>\n"
"                                            </td>\n"
"                                        </tr>\n"
"                                    </table>\n"
"                                </t>\n"
"                                <!-- data table -->\n"
"                                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-bottom:5px\">\n"
"                                    <tr>\n"
"                                        <td>\n"
"                                            <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" style=\"margin-top:30px;margin-bottom:5px;color:#666666;\">\n"
"                                                <thead>\n"
"                                                    <tr style=\"color:#9A6C8E; font-size:12px;\">\n"
"                                                        <th style=\"width:15%;text-align:center;\">Rank</th>\n"
"                                                        <th style=\"width:25%;text-align:left;\">Name</th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Performance \n"
"                                                            <t t-if=\"line['suffix']\">\n"
"                                                                (<t t-out=\"line['suffix'] or ''\"/>)\n"
"                                                            </t>\n"
"                                                            <t t-elif=\"line['monetary']\">\n"
"                                                                (<t t-out=\"company.currency_id.symbol or ''\"/>)\n"
"                                                            </t>\n"
"                                                        </th>\n"
"                                                        <th style=\"width:30%;text-align:right;\">Completeness</th>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#9A6C8E;\"/>\n"
"                                                    </tr>\n"
"                                                </thead>\n"
"                                                <tbody t-foreach=\"line['goals']\" t-as=\"goal\">\n"
"                                                    <tr>\n"
"                                                        <t t-set=\"tdBgColor\" t-value=\"'#fff'\"/>\n"
"                                                        <t t-set=\"tdColor\" t-value=\"'gray'\"/>\n"
"                                                        <t t-set=\"mutedColor\" t-value=\"'#AAAAAA'\"/>\n"
"                                                        <t t-set=\"tdPercentageColor\" t-value=\"'#9A6C8E'\"/>\n"
"                                                        <td width=\"15%\" align=\"center\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:20px;\"><t t-out=\"goal['rank']+1 or ''\"/>\n"
"                                                        </td>\n"
"                                                        <td width=\"25%\" align=\"left\" valign=\"middle\" t-attf-style=\"background-color:{{ tdBgColor }};padding :5px 0;font-size:13px;\"><t t-out=\"goal['name'] or ''\"/></td>\n"
"                                                        <td width=\"30%\" align=\"right\" t-attf-style=\"background-color:{{ tdBgColor }};padding:5px 0;line-height:1;\"><t t-out=\"&quot;%.2f&quot; % goal['current'] or ''\"/><br/><span t-attf-style=\"font-size:13px;color:{{ mutedColor }};\">on <t t-out=\"&quot;%.2f&quot; % line['target'] or ''\"/></span>\n"
"                                                        </td>\n"
"                                                        <td width=\"30%\" t-attf-style=\"color:{{ tdPercentageColor }};background-color:{{ tdBgColor }};padding-right:15px;font-size:22px;\" align=\"right\"><strong><t t-out=\"int(goal['completeness']) or ''\"/>%</strong></td>\n"
"                                                    </tr>\n"
"                                                    <tr>\n"
"                                                        <td colspan=\"5\" style=\"height:1px;background-color:#DADADA;\"/>\n"
"                                                    </tr>\n"
"                                                </tbody>\n"
"                                            </table>\n"
"                                        </td>\n"
"                                    </tr>\n"
"                                </table> \n"
"                            </t>\n"
"                        </t>\n"
"                    </td></tr>\n"
"                </table>\n"
"            </td></tr>\n"
"        </table>\n"
"    </td>\n"
"</tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid ""
"A badge is a symbolic token granted to a user as a sign of reward.\n"
"                It can be deserved automatically when some conditions are met or manually by users.\n"
"                Some badges are harder than others to get with specific conditions."
msgstr ""
"Ansiomerkki on symbolinen tunnus, joka myönnetään käyttäjälle palkkion merkiksi.\n"
"                Se voidaan ansaita automaattisesti, kun jotkin ehdot täyttyvät, tai manuaalisesti käyttäjien toimesta.\n"
"                Joitakin merkkejä on vaikeampi saada tietyissä tapauksissa."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid ""
"A goal definition is a technical specification of a condition to reach.\n"
"                The dates, values to reach or users are defined in goal instance."
msgstr ""
"Tavoitteen määritelmä on saavutettavan ehdon tekninen määrittely.\n"
"                Päivämäärät, saavutettavat arvot tai käyttäjät määritellään tavoiteinstanssissa."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__condition
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_condition
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__condition
msgid ""
"A goal is considered as completed when the current value is compared to the "
"value to reach"
msgstr ""
"Tavoitteen katsotaan toteutuneen, kun nykyistä arvoa verrataan "
"saavutettavaan arvoon"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid ""
"A goal is defined by a user and a goal definition.\n"
"                Goals can be created automatically by using challenges."
msgstr ""
"Tavoitteen määrittelevät käyttäjä ja tavoitteen määritelmä.\n"
"                Tavoitteita voidaan luoda automaattisesti käyttämällä haasteita."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid ""
"A rank correspond to a fixed karma level. The more you have karma, the more your rank is high.\n"
"                    This is used to quickly know which user is new or old or highly or not active."
msgstr ""
"Arvosana vastaa kiinteää karmatasoa. Mitä enemmän sinulla on karmaa, sitä korkeampi arvo on.\n"
"Tätä käytetään nopeasti selville, mikä käyttäjä on uusi tai vanha tai erittäin aktiivinen tai ei aktiivinen."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__users
msgid "A selected list of users"
msgstr "Valittujen käyttäjien listaus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__action_id
msgid "Action"
msgstr "Toiminto"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__active
msgid "Active"
msgstr "Aktiivinen"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Advanced Options"
msgstr "Lisäasetukset"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth
msgid "Allowance to Grant"
msgstr "Lupa myöntää"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_domain
msgid "Alternative to a list of users"
msgstr "Vaihtoehto käyttäjäluettelolle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Esiintyy"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Assign Challenge to"
msgstr "Määritä haaste"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid ""
"Assign a list of goals to chosen users to evaluate them.\n"
"                The challenge can use a period (weekly, monthly...) for automatic creation of goals.\n"
"                The goals are created for the specified users or member of the group."
msgstr ""
"Määritä valituille käyttäjille luettelo tavoitteista niiden arvioimiseksi.\n"
"                Haaste voi käyttää ajanjaksoa (viikoittain, kuukausittain...) tavoitteiden automaattiseen luomiseen.\n"
"                Tavoitteet luodaan määritetyille käyttäjille tai ryhmän jäsenille."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_attachment_count
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Authorized Users"
msgstr "Valtuutetut käyttäjät"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__python
msgid "Automatic: execute a specific Python code"
msgstr "Automaattinen: aja määritelty Python-koodi"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__count
msgid "Automatic: number of records"
msgstr "Automaattinen: tietueiden määrä"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__sum
msgid "Automatic: sum on a field"
msgstr "Automaattinen: summa kentällä"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_bachelor
msgid "Bachelor"
msgstr "Kandidaatti (B.Sc)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__badge_id
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge"
msgstr "Ansiomerkki"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Badge Description"
msgstr "Ansiomerkin kuvaus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__level
msgid "Badge Level"
msgstr "Ansiomerkin taso"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_list_view
msgid "Badge List"
msgstr "Ansiomerkkien listaus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__badge_name
msgid "Badge Name"
msgstr "Ansiomerkin nimi"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.badge_list_action
#: model:ir.model.fields,field_description:gamification.field_res_users__badge_ids
#: model:ir.ui.menu,name:gamification.gamification_badge_menu
msgid "Badges"
msgstr "Ansiomerkit"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Badges are granted when a challenge is finished. This is either at the end "
"of a running period (eg: end of the month for a monthly challenge), at the "
"end date of a challenge (if no periodicity is set) or when the challenge is "
"manually closed."
msgstr ""
"Ansiomerkit myönnetään kun haaste on valmis. Tämä on siis joko "
"käynnissäolevan ajanjakson lopussa (esimerkiksi kuukausittaisissa haasteissa"
" kuukauden lopussa), haasteen päättymispäivänä (jos toistuvuutta ei ole "
"asetettu) tai kun haaste suljetaan manuaalisesti."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_mode
msgid "Batch Mode"
msgstr "Erätila"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_idea
msgid "Brilliant"
msgstr "Erinomainen"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__bronze
msgid "Bronze"
msgstr "Pronssi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__bronze_badge
msgid "Bronze badges count"
msgstr "Pronssimerkkien määrä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Can not grant"
msgstr "Ei voida myöntää"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Can not modify the configuration of a started goal"
msgstr "Käynnistyneen tavoitteen asetuksia ei voida muuttaa"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Cancel"
msgstr "Peruuta"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__canceled
msgid "Canceled"
msgstr "Peruttu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Category"
msgstr "Tuoteryhmä(t)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__challenge_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__challenge_id
msgid "Challenge"
msgstr "Haaste"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__line_id
msgid "Challenge Line"
msgstr "Haasterivi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Challenge Lines"
msgstr "Haasterivit"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__name
msgid "Challenge Name"
msgstr "Haasteen otsikko"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__challenge_id
msgid "Challenge originating"
msgstr "Haasteen alkuperä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__challenge_id
msgid ""
"Challenge that generated the goal, assign challenge to users to generate "
"goals with a value in this field."
msgstr ""
"Haaste, joka loi tavoitteen, anna käyttäjille haaste luoda tavoitteita, "
"joilla on arvo tässä kentässä."

#. module: gamification
#: model:mail.template,name:gamification.simple_report_template
msgid "Challenge: Simple Challenge Report Progress"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.challenge_list_action
#: model:ir.ui.menu,name:gamification.gamification_challenge_menu
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Challenges"
msgstr "Haasteet"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max
msgid "Check to set a monthly limit per person of sending this badge"
msgstr ""
"Valitse asettaaksesi kuukausittainen raja arvomerkin myöntämismäärille."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Clickable Goals"
msgstr "Valittavat tavoitteet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__closed
msgid "Closed goal"
msgstr "Suljetut tavoitteet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__comment
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__comment
msgid "Comment"
msgstr "Kommentti"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_discover
msgid "Complete your Profile"
msgstr "Täydennä profiilisi tiedot"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__completeness
msgid "Completeness"
msgstr "Valmiusaste"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__computation_mode
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Computation Mode"
msgstr "Laskentasääntö"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__condition
msgid "Condition"
msgstr "Ehto"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__consolidated
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Consolidated"
msgstr "Konsolidoitu"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_new_simplified_res_users
msgid "Create User"
msgstr "Luo käyttäjä"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.badge_list_action
msgid "Create a new badge"
msgstr "Luo uusi ansiomerkki"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.challenge_list_action
msgid "Create a new challenge"
msgstr "Luo uusi haaste"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_list_action
msgid "Create a new goal"
msgstr "Luo uusi tavoite"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goal_definition_list_action
msgid "Create a new goal definition"
msgstr "Luo uusi tavoitteen määritelmä"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.gamification_karma_ranks_action
msgid "Create a new rank"
msgstr "Luo uusi taso"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.action_new_simplified_res_users
msgid ""
"Create and manage users that will connect to the system. Users can be "
"deactivated should there be a period of time during which they will/should "
"not connect to the system. You can assign them groups in order to give them "
"specific access to the applications they need to use in the system."
msgstr ""
"Luo ja hallitse käyttäjiä jotka ottavat yhteyttä järjestelmään. Käyttäjiltä "
"voidaan estää käyttö määräajaksi, kun heidän ei haluta käyttävän "
"järjestelmää. Voit liittää heidät ryhmiin antaaksesi heille ryhmäkohtaisesti"
" käyttöoikeuksia järjestelmään."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__create_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__create_date
msgid "Created on"
msgstr "Luotu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__current
msgid "Current"
msgstr "Nykyinen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__current
msgid "Current Value"
msgstr "Nykyinen arvo"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__daily
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__daily
msgid "Daily"
msgstr "Päivittäin"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Data"
msgstr "Data"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_date_id
msgid "Date Field"
msgstr "Päivämääräkenttä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__computation_mode
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__computation_mode
msgid ""
"Define how the goals will be computed. The result of the operation will be "
"stored in the field 'Current'."
msgstr ""
"Määritä, miten tavoitteet lasketaan. Toiminnon tulos tallennetaan kenttään "
"\"Nykyinen\"."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Määrittele haasteen näkyvyys valikoissa"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_condition
msgid "Definition Condition"
msgstr "Määritelmän ehto"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_description
msgid "Definition Description"
msgstr "Määrittelyn kuvaus"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Depending on the Display mode, reports will be individual or shared."
msgstr "Näkymästä riippuen raportit ovat joko yksilölliset, tai jaetut."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid ""
"Describe the challenge: what is does, who it targets, why it matters..."
msgstr ""
"Kirjoita haasteen kuvaus: mitä siinä on tarkoitus tehdä, kenelle se on "
"tarkoitettu, miksi sillä on merkitystä.."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Describe what they did and why it matters (will be public)"
msgstr ""
"Kirjoita kuvaus mistä hyvästä ansiomerkki myönnettiin (julkinen tieto)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__description
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Description"
msgstr "Kuvaus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__visibility_mode
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_display
msgid "Display Mode"
msgstr "Tarkastelunäkymä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__display_name
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__display_mode
msgid "Displayed as"
msgstr "Näytetään muodossa"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid "Distinctive field for batch user"
msgstr "Erottuva kenttä eräkäyttäjälle"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_doctor
msgid "Doctor"
msgstr "Doctor"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__domain
msgid ""
"Domain for filtering records. General rule, not user depending, e.g. "
"[('state', '=', 'done')]. The expression can contain reference to 'user' "
"which is a browse record of the current user if not in batch mode."
msgstr ""
"Domain filter on tietueiden suodattamista varten. Yleinen sääntö, ei "
"käyttäjästä riippuvainen, esim. [('state', '=', 'done')]. Lauseke voi "
"sisältää viittauksen 'user'-tietueeseen, joka on nykyisen käyttäjän "
"selaustietue, jos se ei ole eräajotilassa."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__done
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Done"
msgstr "Valmis"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__draft
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__draft
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Draft"
msgstr "Luonnos"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_newbie
msgid "Earn your first points and join the adventure !"
msgstr "Ansaitse ensimmäiset pisteesi ja liity seikkailuun!"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__end_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__end_date
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "End Date"
msgstr "Päättymispäivä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_mode
msgid "Evaluate the expression in batch instead of once for each user"
msgstr "Arvioi lauseke erässä kunkin käyttäjän sijasta"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__batch_user_expression
msgid "Evaluated expression for batch mode"
msgstr "Arvioitu lauseke eräajotilalle"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__everyone
msgid "Everyone"
msgstr "Näytä kaikille"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__boolean
msgid "Exclusive (done or not-done)"
msgstr "Eksklusiivinen (tehty tai ei tehty)"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__failed
msgid "Failed"
msgstr "Epäonnistui"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__field_id
msgid "Field to Sum"
msgstr "Kentästä summaan"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__domain
msgid "Filter Domain"
msgstr "Suodatin domain"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_follower_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_partner_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_first_id
msgid "For 1st user"
msgstr "1. käyttäjälle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_second_id
msgid "For 2nd user"
msgstr "2. käyttäjälle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_third_id
msgid "For 3rd user"
msgstr "3. käyttäjälle"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_id
msgid "For Every Succeeding User"
msgstr "Jokaiselle onnistuneelle käyttäjälle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Formatting Options"
msgstr "Muotoiluasetukset"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__level
msgid "Forum Badge Level"
msgstr "Ansiomerkin taso"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "From"
msgstr "Lähtöpaikka"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__full_suffix
msgid "Full Suffix"
msgstr "Yksikkö"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge
msgid "Gamification Badge"
msgstr "Pelillistämisen ansiomerkki"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Pelillistämishaaste"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal
msgid "Gamification Goal"
msgstr "Pelillistämisen tavoite"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_definition
msgid "Gamification Goal Definition"
msgstr "Pelillistämisen tavoitteen määritelmä"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_goal_wizard
msgid "Gamification Goal Wizard"
msgstr "Pelillistämisen tavoitteen wizardi"

#. module: gamification
#: model:ir.ui.menu,name:gamification.gamification_menu
msgid "Gamification Tools"
msgstr "Pelillistämistyökalut"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user
msgid "Gamification User Badge"
msgstr "Pelillistämisen käyttäjän ansiomerkki"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_badge_user_wizard
msgid "Gamification User Badge Wizard"
msgstr "Pelillistämisen käyttäjän ansiomerkki wizardi"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_challenge_line
msgid "Gamification generic goal for challenge"
msgstr "Pelillistämisen yleinen tavoite haasteelle"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_check_challenge_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_check_challenge
#: model:ir.cron,name:gamification.ir_cron_check_challenge
msgid "Gamification: Goal Challenge Check"
msgstr "Pelillistäminen: Tavoitteiden haasteiden tarkistus"

#. module: gamification
#: model:ir.actions.server,name:gamification.ir_cron_consolidate_last_month_ir_actions_server
#: model:ir.cron,cron_name:gamification.ir_cron_consolidate_last_month
#: model:ir.cron,name:gamification.ir_cron_consolidate_last_month
msgid "Gamification: Karma tracking consolidation"
msgstr "Pelillistäminen: Karman seurannan konsolidointi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__goal_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goal"
msgstr "Tavoite"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__name
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Goal Definition"
msgstr "Tavoitteen määrittely"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_definition_list_action
#: model:ir.ui.menu,name:gamification.gamification_definition_menu
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_list_view
msgid "Goal Definitions"
msgstr "Tavoitteiden määrittelyt"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__description
msgid "Goal Description"
msgstr "Tavoitteen kuvaus"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Failed"
msgstr "Tavoite saavuttamatta"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_list_view
msgid "Goal List"
msgstr "Tavoitelistaus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__condition
msgid "Goal Performance"
msgstr "Tavoitteen suunta"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Goal Reached"
msgstr "Saavutetut tavoitteet"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_list_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Goal definitions"
msgstr "Tavoitteiden määrittelyt"

#. module: gamification
#: model:mail.template,name:gamification.email_template_goal_reminder
msgid "Goal: Reminder for Goal Update"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goal_list_action
#: model:ir.ui.menu,name:gamification.gamification_goal_menu
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Goals"
msgstr "Tavoitteet"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__gold
msgid "Gold"
msgstr "Kulta"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__gold_badge
msgid "Gold badges count"
msgstr "Kultamerkkien määrä"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_good_job
msgid "Good Job"
msgstr "Hyvää työtä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Grant"
msgstr "Myönnä"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_grant_wizard
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Grant Badge"
msgstr "Myönnä ansiomerkki"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Grant Badge To"
msgstr "Myönnä ansiomerkki"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Grant this Badge"
msgstr "Myönnä tämä ansiomerkki"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "Granted by"
msgstr "Myöntäjä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Granting"
msgstr "Myöntäminen"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Group By"
msgstr "Ryhmittely"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__report_message_group_id
msgid "Group that will receive a copy of the report in addition to the user"
msgstr "Ryhmä joka saa kopion raportista käyttäjän lisäksi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "HR Challenges"
msgstr "HR haasteet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__has_message
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_hidden
msgid "Hidden"
msgstr "Piilotettu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "How is the goal computed?"
msgstr "Miten tavoite lasketaan?"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__hr
msgid "Human Resources / Engagement"
msgstr "Henkilöstöresurssit / sitoutuminen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__res_id_field
msgid "ID Field of user"
msgstr "Käyttäjän ID-kenttä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__remaining_sending
msgid "If a maximum is set"
msgstr "Jos maksimi on asetettu"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__challenge_id
msgid "If this badge was rewarded through a challenge"
msgstr "Jos tämä ansiomerkki myönnettiin haasteen kautta"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1920
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1920
msgid "Image"
msgstr "Kuva"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_1024
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_1024
msgid "Image 1024"
msgstr "Kuva 1024"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_128
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_128
msgid "Image 128"
msgstr "Kuva 128"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_256
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_256
msgid "Image 256"
msgstr "Kuva 256"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__image_512
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__image_512
msgid "Image 512"
msgstr "Kuva 512"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__state__inprogress
msgid "In Progress"
msgstr "Käynnissä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid ""
"In batch mode, the domain is evaluated globally. If enabled, do not use "
"keyword 'user' in above filter domain."
msgstr ""
"Erätilassa toimialue arvioidaan globaalisti. Jos tämä on käytössä, älä käytä"
" avainsanaa \"käyttäjä\" yllä olevassa suodattimessa."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_distinctive_field
msgid ""
"In batch mode, this indicates which field distinguishes one user from the "
"other, e.g. user_id, partner_id..."
msgstr ""
"Erätilassa tämä ilmaisee, mikä kenttä erottaa yhden käyttäjän toisesta, "
"esim. user_id, partner_id...."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__last_update
msgid ""
"In case of manual goal, reminders are sent if the goal as not been updated "
"for a while (defined in challenge). Ignored in case of non-manual goal or "
"goal not linked to a challenge."
msgstr ""
"Manuaalisessa tavoitteessa lähetetään muistutuksia, jos tavoitetta ei ole "
"päivitetty vähään aikaan (määritelty haasteessa). Ohitetaan, jos tavoite on "
"ei-manuaalinen tai tavoite ei liity haasteeseen."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__inprogress
msgid "In progress"
msgstr "Kesken"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__personal
msgid "Individual Goals"
msgstr "Yksilölliset tavoitteet"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "Inherited models"
msgstr "Perityt mallit"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin3
#: model:gamification.goal.definition,name:gamification.definition_base_invite
msgid "Invite new Users"
msgstr "Kutsu uusia käyttäjiä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_is_follower
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma
msgid "Karma"
msgstr "Karma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__karma_tracking_ids
msgid "Karma Changes"
msgstr "Karman muutokset"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank____last_update
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__last_report_date
msgid "Last Report Date"
msgstr "Viimeinen raportointipäivämäärä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__last_update
msgid "Last Update"
msgstr "Viimeinen päivitys"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_uid
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_wizard__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__write_date
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__visibility_mode__ranking
msgid "Leader Board (Group Ranking)"
msgstr "Tulostaulu (ryhmäsijoitus)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max_number
msgid "Limitation Number"
msgstr "Rajoitenumero"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Line List"
msgstr "Rivilista"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__line_ids
msgid "Lines"
msgstr "Rivit"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__line_ids
msgid "List of goals that will be set"
msgstr "Listaus tavoitteista, jotka asetetaan"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__user_ids
msgid "List of users participating to the challenge"
msgstr "Listaus käyttäjistä jotka osallistuvat haasteeseen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_main_attachment_id
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliite"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_master
msgid "Master"
msgstr "Maisteri (M.Sc)"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_challenge_kanban
msgid "Member"
msgstr "Jäsen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__karma_min
msgid "Minimum karma needed to reach this rank"
msgstr "Vähimmäiskarma, joka tarvitaan tämän tason saavuttamiseen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__model_id
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Model"
msgstr "Malli"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_monetary
msgid "Monetary"
msgstr "Rahallinen"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__monetary
msgid "Monetary Value"
msgstr "Rahallinen arvo"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__monthly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__monthly
msgid "Monthly"
msgstr "Kuukausittain"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_max
msgid "Monthly Limited Sending"
msgstr "Kuukausittainen lähetysrajoitus"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_this_month
msgid "Monthly total"
msgstr "Kuukausittainen yhteismäärä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__description_motivational
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Motivational"
msgstr "Motivoiva"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__description_motivational
msgid "Motivational phrase to reach this rank"
msgstr "Motivaatiolause tämän tason saavuttamiseksi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "My Goals"
msgstr "Omat tavoitteeni"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "My Monthly Sending Total"
msgstr "Kuukaudessa myöntämäni yhteissumma"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my_this_month
msgid "My Monthly Total"
msgstr "Kuukausittainen yhteismääräni"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__stat_my
msgid "My Total"
msgstr "Kokonaissummani"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__name
msgid "Name"
msgstr "Nimi"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__never
msgid "Never"
msgstr "Ei koskaan"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__remind_update_delay
msgid "Never reminded if no value or zero is specified."
msgstr "Älä muistuta, jos arvoa ei määriteltynä tai määritelty arvo on nolla."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__new_value
msgid "New Karma Value"
msgstr "Uusi karma-arvo"

#. module: gamification
#: model:mail.template,subject:gamification.email_template_badge_received
msgid "New badge {{ object.badge_id.name }} granted"
msgstr "Uusi merkki {{ object.badge_id.name }} myönnetty"

#. module: gamification
#: model:mail.template,subject:gamification.mail_template_data_new_rank_reached
msgid "New rank: {{ object.rank_id.name }}"
msgstr "Uusi sijoitus: {{ object.rank_id.name }}"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_newbie
msgid "Newbie"
msgstr "Tulokas"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__next_rank_id
msgid "Next Rank"
msgstr "Seuraava taso"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__next_report_date
msgid "Next Report Date"
msgstr "Seuraava raportointipäivä"

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid "No goal found"
msgstr "Tavoitetta ei löytynyt"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "No monthly sending limit"
msgstr "Ei kuukausittaista lähetysrajaa"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_problem_solver
msgid "No one can solve challenges like you do."
msgstr "Kukaan ei ratkaise haasteita kuin sinä."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__nobody
msgid "No one, assigned through challenges"
msgstr "Ei kukaan, haasteiden kautta määrätty"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Nobody reached the required conditions to receive special badges."
msgstr ""
"Kukaan käyttäjistä ei saavuttanut vaadittavia ehtoja ansaitakseen "
"erityisansiomerkkejä."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__once
msgid "Non recurring"
msgstr "Ei toistuva"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__remind_update_delay
msgid "Non-updated manual goals will be reminded after"
msgstr "Päivittämättömät manuaaliset tavoitteet muistutetaan tämän jälkeen"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Notification Messages"
msgstr "Ilmoitusviestit"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_needaction_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_has_error_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,help:gamification.field_gamification_challenge__message_unread_counter
msgid "Number of unread messages"
msgstr "Lukemattomia viestejä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_users_count
msgid "Number of users"
msgstr "Käyttäjämäärä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__old_value
msgid "Old Karma Value"
msgstr "Vanha karma-arvo"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__onchange
msgid "On change"
msgstr "Vaihdettaessa"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Only the people having these badges can give this badge"
msgstr ""
"Vain henkilöt, joilla itsellä on kyseinen ansiomerkki, voivat  myöntää "
"niitä."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth_user_ids
msgid "Only these people can give this badge"
msgstr "Vain nämä henkilöt voivat möyntää tämän ansiomerkin"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "Optimisation"
msgstr "Optimointi"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "Owner"
msgstr "Omistaja"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__owner_ids
msgid "Owners"
msgstr "Omistajat"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__rule_auth__having
msgid "People having some badges"
msgstr "Henkilöt, joille on myönnetty ansiomerkkejä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Period"
msgstr "Jakso"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__period
msgid ""
"Period of automatic goal assigment. If none is selected, should be launched "
"manually."
msgstr ""
"Automaattisen tavoitteen määrityksen jakso. Jos mitään ei ole valittu, se "
"tulee käynnistää manuaalisesti."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__period
msgid "Periodicity"
msgstr "Jaksotus"

#. module: gamification
#: model:gamification.badge,name:gamification.badge_problem_solver
msgid "Problem Solver"
msgstr "Ongelmanratkaisija"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__display_mode__progress
msgid "Progressive (using numerical values)"
msgstr "Etenevä (käyttäen numeerisia arvoja)"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__compute_code
msgid "Python Code"
msgstr "Python-koodi"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__compute_code
msgid ""
"Python code to be executed for each user. 'result' should contains the new "
"current value. Evaluated user can be access through object.user_id."
msgstr ""
"Python-koodi, joka suoritetaan kullekin käyttäjälle. 'result' sisältää uuden"
" nykyisen arvon. Arvioitu käyttäjä on käytettävissä object.user_id:n kautta."

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__rank_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "Rank"
msgstr "Sija"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__name
msgid "Rank Name"
msgstr "Sijan nimi"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_rank
msgid "Rank based on karma"
msgstr "Karmaan perustuva sija"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_ranks_action
#: model:ir.ui.menu,name:gamification.gamification_karma_ranks_menu
msgid "Ranks"
msgstr "Sijat"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_tree
msgid "Ranks List"
msgstr "Sijojen listaus"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_bachelor
msgid "Reach the next rank and gain a very magic wand !"
msgstr "Saavuta seuraava taso ja hanki taikasauva!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_master
msgid "Reach the next rank and gain a very nice hat !"
msgstr "Saavuta seuraava taso ja ansaitse erittäin kiva hattu!"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_student
msgid "Reach the next rank and gain a very nice mug !"
msgstr "Saavuta seuraava taso ja ansaitse todella hieno muki !"

#. module: gamification
#: model_terms:gamification.karma.rank,description_motivational:gamification.rank_doctor
msgid "Reach the next rank and gain a very nice unicorn !"
msgstr "Saavuta seuraava taso ja hanki erittäin mukava yksisarvinen!"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal__state__reached
msgid "Reached"
msgstr "Saavutettu"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reached when current value is"
msgstr "Saavutettu, kun nykyinen arvo on"

#. module: gamification
#: model:mail.template,name:gamification.email_template_badge_received
msgid "Received Badge"
msgstr ""

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__computation_mode__manually
msgid "Recorded manually"
msgstr "Raportoitu manuaalisesti"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reference"
msgstr "Viite"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Refresh Challenge"
msgstr "Päivitä haaste"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.goals_from_challenge_act
msgid "Related Goals"
msgstr "Liittyvät tavoitteet"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user_wizard__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Liittyvä käyttäjätunnus resurssille sen oikeuksien määrittämiseksi"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__remaining_sending
msgid "Remaining Sending Allowed"
msgstr "Jäljellä oleva lähetys sallittu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__remind_update_delay
msgid "Remind delay"
msgstr "Muistutuksen viive"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reminders for Manual Goals"
msgstr "Muistutukset manuaalisille tavoitteille"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_frequency
msgid "Report Frequency"
msgstr "Raportointitiheys"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_template_id
msgid "Report Template"
msgstr "Raporttipohja"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__rule_auth_badge_ids
msgid "Required Badges"
msgstr "Vaaditut ansiomerkit"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__karma_min
msgid "Required Karma"
msgstr "Vaadittava karma"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Reset Completion"
msgstr "Nollaa suoritukset"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__manager_id
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "Retrieving progress for personal challenge without user information"
msgstr ""
"Haetaan edistymistä henkilökohtaiseen haasteeseen ilman käyttäjätietoja"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Reward"
msgstr "Palkinto"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_failure
msgid "Reward Bests if not Succeeded?"
msgstr "Palkitse parhaat, jos ei riittävää menestystä?"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__reward_realtime
msgid "Reward as soon as every goal is reached"
msgstr "Palkitse heti kun kaikki tavoitteet on saavutettu"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__challenge_ids
msgid "Reward of Challenges"
msgstr "Palkinto haasteista"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__goal_definition_ids
msgid "Rewarded by"
msgstr "Myöntäjä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Rewards for challenges"
msgstr "Palkinnot haasteista"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Running"
msgstr "Käynnissä olevat"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Running Challenges"
msgstr "Käynnissä olevat haasteet"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Schedule"
msgstr "Aikatauluta"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_badge_view_search
msgid "Search Badge"
msgstr "Etsi ansiomerkkiä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
msgid "Search Challenges"
msgstr "Etsi haasteita"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_search_view
msgid "Search Goal Definitions"
msgstr "Etsi tavoitteiden määrittelyjä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "Search Goals"
msgstr "Etsi tavoitteita"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_ranks_view_search
msgid "Search Ranks"
msgstr "Etsi tasoa"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
msgid "Search Trackings"
msgstr "Etsi seurantaa"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid ""
"Security rules to define who is allowed to manually grant badges. Not "
"enforced for administrator."
msgstr ""
"Säännöt, jotka määrittävät kuka pystyy myöntämään ansiomerkkejä. Eivät "
"kosketa järjestelmäylläpitäjää."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Send Report"
msgstr "Lähetä raportti"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__report_message_group_id
msgid "Send a copy to"
msgstr "Lähetä kopio"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__sender_id
msgid "Sender"
msgstr "Lähettäjä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__sequence
msgid "Sequence number for ordering"
msgstr "Järjestysnumero tilausta varten"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Set the current value you have reached for this goal"
msgstr "Määrittele tähän mennessä saavutamasi arvot kyseiselle haasteelle."

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin1
#: model:gamification.goal.definition,name:gamification.definition_base_company_data
msgid "Set your Company Data"
msgstr "Määrittele yrityksesi tiedot"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_admin2
#: model:gamification.goal.definition,name:gamification.definition_base_company_logo
msgid "Set your Company Logo"
msgstr "Aseta yrityksesi logo"

#. module: gamification
#: model:gamification.challenge.line,name:gamification.line_base_discover1
#: model:gamification.goal.definition,name:gamification.definition_base_timezone
msgid "Set your Timezone"
msgstr "Aseta aikavyöhykkeesi"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__challenge_category__other
msgid "Settings / Gamification Tools"
msgstr "Asetukset / Pelillistämisen työkalut"

#. module: gamification
#: model:gamification.challenge,name:gamification.challenge_base_configure
msgid "Setup your Company"
msgstr "Konfiguroi yritystiedot"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_badge__level__silver
msgid "Silver"
msgstr "Hopea"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_res_users__silver_badge
msgid "Silver badges count"
msgstr "Hopeamerkkien määrä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Start Challenge"
msgstr "Käynnistä haaste"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__start_date
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__start_date
msgid "Start Date"
msgstr "Alkupäivä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "Start goal"
msgstr "Käynnistä tavoite"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__state
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__state
#: model_terms:ir.ui.view,arch_db:gamification.challenge_search_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "State"
msgstr "Tila"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "Statistics"
msgstr "Tilastot"

#. module: gamification
#: model:gamification.karma.rank,name:gamification.rank_student
msgid "Student"
msgstr "Oppilas"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "Subscriptions"
msgstr "Tilaukset"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,field_description:gamification.field_gamification_goal_definition__suffix
msgid "Suffix"
msgstr "Pääte"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__invited_user_ids
msgid "Suggest to users"
msgstr "Ehdota käyttäjille"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.challenge_line_list_view
msgid "Target"
msgstr "Tavoite"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__target_goal
msgid "Target Value to Reach"
msgstr "Asetettu tavoitearvo"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "Target: less than"
msgstr "Tavoite: vähemmän kuin"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__action_id
msgid "The action that will be called to update the goal value."
msgstr "Toiminto, jota kutsutaan päivittämään tavoitearvo."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "The challenge %s is finished."
msgstr "Haaste on %s valmis."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_full_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__full_suffix
msgid "The currency and suffix field"
msgstr "Rahayksikkö ja pääte"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_date_id
msgid "The date to use for the time period evaluated"
msgstr "Arvioidulle ajanjaksolle käytettävä päivämäärä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__end_date
msgid ""
"The day a new challenge will be automatically closed. If no periodicity is "
"set, will use this date as the goal end date."
msgstr ""
"Päivä, jolloin uusi haaste suljetaan automaattisesti. Jos jaksotusta ei ole "
"asetettu, tätä päivämäärää käytetään tavoitteen päättymispäivänä."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__start_date
msgid ""
"The day a new challenge will be automatically started. If no periodicity is "
"set, will use this date as the goal start date."
msgstr ""
"Päivä. jolloin uusi haaste alkaa automaattisesti. Jos jaksotusta ei ole "
"asetettu, tätä päivämäärää käytetään tavoitteen aloituspäivänä."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The domain for the definition %s seems incorrect, please check it.\n"
"\n"
"%s"
msgstr ""
"Määritelmän %s toimialue vaikuttaa virheelliseltä, tarkista se.\n"
"\n"
"%s"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__field_id
msgid "The field containing the value to evaluate"
msgstr "Kenttä, joka sisältää arvioitavan arvon"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__res_id_field
msgid ""
"The field name on the user profile (res.users) containing the value for "
"res_id for action."
msgstr ""
"Käyttäjäprofiilin (res.users) kentän nimi, joka sisältää toiminnan res_id "
"arvon."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__higher
msgid "The higher the better"
msgstr "Mitä korkeampi, sitä parempi"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__owner_ids
msgid "The list of instances of this badge granted to users"
msgstr "Luettelo käyttäjistä, joille tämä ansiomerkki on myönnetty"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_inherited_ids
msgid "The list of models that extends the current model."
msgstr "Lista malleistajotka laajentavat tätä mallia."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__unique_owner_ids
msgid "The list of unique users having received this badge."
msgstr ""
"Listaus yksittäisistä käyttäjistä, jotka ovat vastaanottaneet tämän "
"ansiomerkin."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_goal_definition__condition__lower
msgid "The lower the better"
msgstr "Mitä matalampi, sitä parempi"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_max_number
msgid ""
"The maximum number of time this badge can be sent per month per person."
msgstr ""
"Maksimimäärä, jonka käyttäjä voi lähettää tämän ansiomerkin kuukaudessa."

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(error)s not found"
msgstr ""
"Määritelmän %(name)s mallin konfiguraatio vaikuttaa virheelliseltä, tarkista se.\n"
"\n"
"%(error)s ei löydy"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid ""
"The model configuration for the definition %(name)s seems incorrect, please check it.\n"
"\n"
"%(field_name)s not stored"
msgstr ""
"Määritelmän %(name)s mallin konfiguraatio vaikuttaa virheelliseltä, tarkista se.\n"
"\n"
"%(field_name)s ei tallennettu"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__model_id
msgid "The model object for the field to evaluate"
msgstr ""

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__remind_update_delay
msgid ""
"The number of days after which the user assigned to a manual goal will be "
"reminded. Never reminded if no value is specified."
msgstr ""
"Päivien määrä, jonka jälkeen manuaalisen tavoitteen määrittämää käyttäjää "
"muistutetaan. Ei koskaan muistuteta, jos arvoa ei ole määritetty."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_this_month
msgid ""
"The number of time the current user has received this badge this month."
msgstr ""
"Kertojen määrä, jolloin käyttäjä on saanut kyseisen ansiomerkin tässä "
"kuussa."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my
msgid "The number of time the current user has received this badge."
msgstr "Kertojen määrä, jolloin käyttäjä on saanut kyseisen ansiomerkin."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_my_monthly_sending
msgid "The number of time the current user has sent this badge this month."
msgstr ""
"Kerrat tässä kuussa, jolloin käyttäjä on saavuttanut tämän arvomerkin."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_users_count
msgid "The number of time this badge has been received by unique users."
msgstr ""
"Kertojen määrä, jolloin yksittäiset käyttäjät ovat saavuttaneet tämän "
"arvomerkin."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__stat_this_month
msgid "The number of time this badge has been received this month."
msgstr "Kertojen määrä, jolloin tämä arvomerkki on saatu tässä kuussa."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__granted_count
msgid "The number of time this badge has been received."
msgstr "Kertojen määrä, jolloin tämä arvomerkki on saatu."

#. module: gamification
#: model:ir.model.constraint,message:gamification.constraint_gamification_karma_rank_karma_min_check
msgid "The required karma has to be above 0."
msgstr "Vaaditun karman on oltava yli 0."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_monetary
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__monetary
msgid "The target and current value are defined in the company currency."
msgstr "Tavoite- ja nykyarvo määritellään yhtiön valuutassa."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge_line__definition_suffix
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__suffix
msgid "The unit of the target and current values"
msgstr "Tavoite- ja nykyarvojen yksikkö"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__manager_id
msgid "The user responsible for the challenge."
msgstr "Haasteesta vastuussa oleva käyttäjä."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge_user__sender_id
msgid "The user who has send the badge"
msgstr "Ansiomerkin myöntänyt käyttäjä"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__goal_definition_ids
msgid ""
"The users that have succeeded theses goals will receive automatically the "
"badge."
msgstr ""
"Käyttäjät, jotka ovat saavuttaneet nämä tavoitteet, saavat automaattisesti "
"arvomerkin."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal_definition__batch_user_expression
msgid ""
"The value to compare with the distinctive field. The expression can contain "
"reference to 'user' which is a browse record of the current user, e.g. "
"user.id, user.partner_id.id..."
msgstr ""
"Erottuvaan kenttään verrattava arvo. Lauseke voi sisältää viittauksen "
"\"käyttäjään\", joka on nykyisen käyttäjän selaustietue, esim. user.id, "
"user.partner_id.id..."

#. module: gamification
#: model_terms:ir.actions.act_window,help:gamification.goals_from_challenge_act
msgid ""
"There is no goal associated to this challenge matching your search.\n"
"            Make sure that your challenge is active and assigned to at least one user."
msgstr ""
"Tähän haasteeseen ei liity tavoitetta, joka vastaisi hakuasi.\n"
"            Varmista, että haasteesi on aktiivinen ja määritetty vähintään yhdelle käyttäjälle."

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_goal__closed
msgid "These goals will not be recomputed."
msgstr "Näitä tuloksia ei lasketa uudelleen."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "This badge can not be sent by users."
msgstr "Käyttäjät eivät voi lähettää tätä ansiomerkkiä."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_kanban_view
msgid "To"
msgstr "Kohdepaikka"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__target_goal
msgid "To Reach"
msgstr "Saavutettava määrä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__to_update
msgid "To update"
msgstr "Päivittää"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__granted_count
msgid "Total"
msgstr "Yhteensä"

#. module: gamification
#: model:ir.model,name:gamification.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Seuraa karman muutoksia"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_form
msgid "Tracking"
msgstr "Seuranta"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__tracking_date
msgid "Tracking Date"
msgstr "Seurauspäivämäärä"

#. module: gamification
#: model:ir.actions.act_window,name:gamification.gamification_karma_tracking_action
#: model:ir.ui.menu,name:gamification.gamification_karma_tracking_menu
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_tree
msgid "Trackings"
msgstr "Seuranta"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__unique_owner_ids
msgid "Unique Owners"
msgstr "Yksilölliset omistajat"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge_line__definition_suffix
msgid "Unit"
msgstr "Yksikkö"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread
msgid "Unread Messages"
msgstr "Lukemattomat viestit"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge__message_unread_counter
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lukemattomien viestien laskuri"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_goal_wizard_update_current
msgid "Update"
msgstr "Päivitä"

#. module: gamification
#: code:addons/gamification/models/goal.py:0
#, python-format
msgid "Update %s"
msgstr "Päivitä %s"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_badge_user_wizard__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_goal__user_id
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_tracking__user_id
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:gamification.goal_search_view
msgid "User"
msgstr "Käyttäjä"

#. module: gamification
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_domain
msgid "User domain"
msgstr "Käyttäjän verkkotunnus"

#. module: gamification
#: model:mail.template,name:gamification.mail_template_data_new_rank_reached
msgid "User: New rank reached"
msgstr ""

#. module: gamification
#: model:ir.actions.act_window,name:gamification.action_current_rank_users
#: model:ir.model,name:gamification.model_res_users
#: model:ir.model.fields,field_description:gamification.field_gamification_challenge__user_ids
#: model:ir.model.fields,field_description:gamification.field_gamification_karma_rank__user_ids
msgid "Users"
msgstr "Käyttäjät"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_karma_rank__user_ids
msgid "Users having this rank"
msgstr "Käyttäjät, joilla on tämä taso"

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__weekly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__weekly
msgid "Weekly"
msgstr "Viikottainen"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_badge__rule_auth
msgid "Who can grant this badge"
msgstr "Kuka pystyy myöntämään tämän ansiomerkin"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.view_badge_wizard_grant
msgid "Who would you like to reward?"
msgstr "Kenet haluaisit palkita?"

#. module: gamification
#: model:ir.model.fields,help:gamification.field_gamification_challenge__reward_realtime
msgid ""
"With this option enabled, a user can receive a badge only once. The top 3 "
"badges are still rewarded only at the end of the challenge."
msgstr ""
"Kun tämä valinta on valittuna, käyttäjä pystyy saamaan ansiomerkin vain "
"kerran. 3 parasta ansiomerkkiä myönnetään vain haasteen lopuksi."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_idea
msgid "With your brilliant ideas, you are an inspiration to others."
msgstr "Loistavilla ideoillasi olet inspiraationa muille."

#. module: gamification
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__period__yearly
#: model:ir.model.fields.selection,name:gamification.selection__gamification_challenge__report_message_frequency__yearly
msgid "Yearly"
msgstr "Vuosittainen"

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You are not in the user allowed list."
msgstr "Et ole hyväksyttyjen käyttäjien listalla."

#. module: gamification
#: code:addons/gamification/wizard/grant_badge.py:0
#, python-format
msgid "You can not grant a badge to yourself."
msgstr "Et voi myöntää ansiomerkkiä itsellesi."

#. module: gamification
#: code:addons/gamification/models/challenge.py:0
#, python-format
msgid "You can not reset a challenge with unfinished goals."
msgstr "Et voi nollata haastetta keskeneräisten tavoitteiden kanssa."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "You can still grant"
msgstr "Voit silti myöntää"

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_good_job
msgid "You did great at your job."
msgstr "Olet onnistunut työssäsi loistavasti."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You do not have the required badges."
msgstr "Sinulla ei ole vaadittuja ansiomerkkejä."

#. module: gamification
#: code:addons/gamification/models/badge.py:0
#, python-format
msgid "You have already sent this badge too many time this month."
msgstr "Olet jo lähettänyt tämän ansiomerkin liian monta kertaa tässä kuussa."

#. module: gamification
#: model_terms:gamification.badge,description:gamification.badge_hidden
msgid "You have found the hidden badge"
msgstr "Olet löytänyt piilotetun ansiomerkin"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_doctor
msgid "You have reached the last rank. Congratulations!"
msgstr "Olet saavuttanut viimeisen tason. Onnittelut!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_newbie
msgid "You just began the adventure! Welcome!"
msgstr "Aloitit juuri seikkailusi! Tervetuloa!"

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_master
msgid "You know what you are talking about. People learn from you."
msgstr "Tiedät mistä puhut. Ihmiset oppivat sinulta."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_bachelor
msgid "You love learning things. Curiosity is a good way to progress."
msgstr "Rakastat oppia asioita. Uteliaisuus on hyvä tapa edistyä."

#. module: gamification
#: model_terms:gamification.karma.rank,description:gamification.rank_student
msgid "You're a young padawan now. May the force be with you!"
msgstr "Olet nyt nuori padawan. Olkoon voima kanssasi!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "badges this month"
msgstr "tämän kuukauden ansiomerkit"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "days"
msgstr "päivää"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid ""
"e.g. A Master Chief knows quite everything on the forum! You cannot beat "
"him!"
msgstr ""
"esim. Päällikkö tietää aivan kaiken foorumilla! Et voi päihittää häntä!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Get started"
msgstr "esim. Valmistaudu aloittamaan"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Master Chief"
msgstr "esim. Päällikkö"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.challenge_form_view
msgid "e.g. Monthly Sales Objectives"
msgstr "esim. kuukausittainen myytitavoite"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_form_view
msgid "e.g. Problem Solver"
msgstr "esim. ongelmanratkaisija"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.gamification_karma_rank_view_form
msgid "e.g. Reach this rank to gain a free mug !"
msgstr "esim. Saavuta tämä taso saadaksesi ilmaisen mukin!"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. Register to the platform"
msgstr "esim. Rekisteröidy alustalle"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. days"
msgstr "esim. päiviä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_definition_form_view
msgid "e.g. user.partner_id.id"
msgstr "esim. user.partner_id.id"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "granted,"
msgstr "myönnetty,"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "refresh"
msgstr "päivitä"

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.goal_form_view
msgid "than the target."
msgstr "kuin tavoite."

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_user_kanban_view
msgid "the"
msgstr " "

#. module: gamification
#: model_terms:ir.ui.view,arch_db:gamification.badge_kanban_view
msgid "this month"
msgstr "tämä kuukausi"
