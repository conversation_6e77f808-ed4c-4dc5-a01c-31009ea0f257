# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_drive
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"<b>To create a new filter:</b><br/>\n"
"                                - Go to the Odoo document you want to filter. For instance, go to Opportunities and search on Sales Department.<br/>\n"
"                                - In this \"Search\" view, select the option \"Save Current Filter\", enter the name (Ex: Sales Department)<br/>\n"
"                                - If you select \"Share with all users\", link of google document in \"More\" options will appear for all users in opportunities of Sales Department.<br/>\n"
"                                - If you don't select \"Share with all users\", link of google document in \"More\" options will not appear for other users in opportunities of Sales Department.<br/>\n"
"                                - If filter is not specified, link of google document will appear in \"More\" option for all users for all opportunities."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Active</strong>"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Model</strong>"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_kanban
msgid "<strong>Template</strong>"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_active
msgid "Active"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:41
#, python-format
msgid "At least one key cannot be found in your Google Drive name pattern"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings_google_drive_authorization_code
msgid "Authorization Code"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_google_drive_config_name_template
msgid ""
"Choose how the new google drive will be named, on google side. Eg. "
"gdoc_%(field_name)s"
msgstr ""

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid "Click to add a new template."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_create_date
msgid "Created on"
msgstr "Creado en"

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:157
#, python-format
msgid "Creating google drive may only be done by one at a time."
msgstr ""

#. module: google_drive
#: model:ir.filters,name:google_drive.filter_partner
msgid "Customer"
msgstr "Cliente"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_filter_id
msgid "Filter"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:60
#: code:addons/google_drive/models/google_drive.py:81
#, python-format
msgid "Go to the configuration panel"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_google_drive_client_id
msgid "Google Client"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_tree
msgid "Google Drive Configuration"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_name_template
msgid "Google Drive Name Pattern"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.res_config_settings_view_form
msgid "Google Drive Templates"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:62
#: code:addons/google_drive/models/google_drive.py:83
#, python-format
msgid "Google Drive is not yet configured. Please contact your administrator."
msgstr ""

#. module: google_drive
#: model:ir.model,name:google_drive.model_google_drive_config
msgid "Google Drive templates config"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_id
msgid "ID"
msgstr "ID (identificación)"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: google_drive
#: model_terms:ir.actions.act_window,help:google_drive.action_google_drive_users_config
msgid ""
"Link your own google drive templates to any record of Odoo. If you have "
"really specific documents you want your collaborator fill in, e.g. Use a "
"spreadsheet to control the quality of your product or review the delivery "
"checklist for each order in a foreign country, ... Its very easy to manage "
"them, link them to Odoo and use them to collaborate with your employees."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_model_id
msgid "Model"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:199
#, python-format
msgid "Please enter a valid Google Document URL."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_model
msgid "Related Model"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_google_drive_resource_id
msgid "Resource Id"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:80
#, python-format
msgid ""
"Something went wrong during the token generation. Please request again an "
"authorization code ."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_name
msgid "Template Name"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_google_drive_config_google_drive_template_url
msgid "Template URL"
msgstr ""

#. module: google_drive
#: model:ir.actions.act_window,name:google_drive.action_google_drive_users_config
msgid "Templates"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:98
#, python-format
msgid "The Google Template cannot be found. Maybe it has been deleted."
msgstr ""

#. module: google_drive
#: model:ir.model.fields,help:google_drive.field_res_config_settings_google_drive_uri
msgid "The URL to generate the authorization code from Google"
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"The name of the attached document can use fixed or variable data. To distinguish between documents in\n"
"                                Google Drive, use fixed words and fields. For instance, in the example above, if you wrote Agrolait_%(name)s_Sales\n"
"                                in the Google Drive name field, the document in your Google Drive and in Odoo attachment will be named\n"
"                                'Agrolait_SO0001_Sales'."
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:134
#, python-format
msgid ""
"The permission 'reader' for 'anyone with the link' has not been written on "
"the document"
msgstr ""

#. module: google_drive
#: model:ir.model.fields,field_description:google_drive.field_res_config_settings_google_drive_uri
msgid "URI"
msgstr ""

#. module: google_drive
#: code:addons/google_drive/models/google_drive.py:59
#, python-format
msgid ""
"You haven't configured 'Authorization Code' generated from google, Please "
"generate and configure it ."
msgstr ""

#. module: google_drive
#: model_terms:ir.ui.view,arch_db:google_drive.view_google_drive_config_form
msgid ""
"https://docs.google.com/document/d/1vOtpJK9scIQz6taD9tJRIETWbEw3fSiaQHArsJYcua4/edit"
msgstr ""

#. module: google_drive
#: model:ir.model,name:google_drive.model_res_config_settings
msgid "res.config.settings"
msgstr ""
