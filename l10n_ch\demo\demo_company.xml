<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ch" model="res.partner">
        <field name="name">CH Company</field>
        <field name="vat">CHE-530781296TVA</field>
        <field name="street">14 Meierskappelerstrasse</field>
        <field name="city">Risch-Rotkreuz</field>
        <field name="country_id" ref="base.ch"/>
        
        <field name="zip">6343</field>
        <field name="phone">+41 78 123 45 67</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.chexample.com</field>
    </record>

    <record id="demo_company_ch" model="res.company">
        <field name="name">CH Company</field>
        <field name="partner_id" ref="partner_demo_company_ch"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ch')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ch.demo_company_ch'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ch.l10nch_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ch.demo_company_ch')"/>
    </function>
</odoo>
