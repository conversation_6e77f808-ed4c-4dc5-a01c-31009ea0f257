<?xml version="1.0"?>
<odoo>

    <record id="masarat_expense_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.expense.form</field>
        <field name="model">hr.masarat.expense</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="payment_state" widget="statusbar"/>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_refused" type="object"/>
                    <button string="Finance Approval" attrs="{'invisible': [('state','in',('finance_approval','finance_refused'))]}" name="make_finance_approval" type="object" class="oe_highlight" groups="masarat_expenses_requist.group_finance_approvales"/>
                    <button string="Finance Refuse" attrs="{'invisible': [('state','in',('finance_approval','finance_refused'))]}" name="make_finance_refused" type="object" groups="masarat_expenses_requist.group_finance_approvales"/>
                    <button string="Paid" attrs="{'invisible': [('payment_state','=','paid')]}" name="make_payment_state" type="object" class="oe_highlight" groups="masarat_expenses_requist.group_finance_manager"/>
                    <button string="Cancel Paid" attrs="{'invisible': [('payment_state','=','not_paid')]}" name="make_cancel_payment_state" type="object" groups="masarat_expenses_requist.group_finance_manager"/>
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval" type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee" attrs="{'readonly': [('is_finance_group', '!=', 'yes')]}"/>
                            <field name="is_finance_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="معلومات الطلب">
                            <field name="is_manager" invisible="1"/>
                            <field name="is_closed" invisible="1"/>
                            <field name="expense_use"/>
                            <field name="manager_id" options='{"no_open": True}'/>
                            <field name="request_date"/>
                            <field name="closer_date"/>
                        </group>
                        <group string="معلومات مالية">
                            <field name="amount"/>
                            <field name="currency_id" options="{'no_create': True, 'no_create_edit':True, 'no_open':True}"/>
                            <field name="payment_type" nolabel="1" widget="radio"/>
                        </group>
                        <group>
                            <field name="Note" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="masarat_expense_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.expense.tree</field>
        <field name="model">hr.masarat.expense</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="state"/>
                <field name="payment_state"/>
                <field name="expense_use"/>
                <field name="request_date"/>
                <field name="closer_date"/>
                <field name="amount"/>
            </tree>
        </field>
    </record>

    <record id="action_masarat_expense_view" model="ir.actions.act_window">
        <field name="name">طلبات العهد</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.expense</field>
        <field name="view_mode">tree,form</field>
    </record>


    <menuitem
            id="menu_absence"
            name="طلبات العهد"
            parent="hr_expense.menu_hr_expense_root"
            action="action_masarat_expense_view"
            sequence="3"/>'


</odoo>

