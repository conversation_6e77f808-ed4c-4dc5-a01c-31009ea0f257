/* Onscreen Keyboard http://net.tutsplus.com/tutorials/javascript-ajax/creating-a-keyboard-with-css-and-jquery/ */

/*rtl:ignore*/
.pos .keyboard_frame{
    display: none;
    position:absolute;
    left:    0;
    bottom:  0px;
    margin:  0;
    padding: 0;
    padding-top: 15px;
    width: 100%;
    height: 0px;    /* 235px, animated via jquery */
	background-color: #BBB;
    overflow:hidden;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    z-index:10000;
}
.pos .keyboard_frame .close_button{
    height:40px;
    width:60px;
    text-align:center;
    background-color: #DDD;
    font-size: 12px;
    line-height:40px;
	-webkit-border-radius: 5px;
    position:absolute;
    top:0;
    right:15px;
	cursor: pointer;
}
/*rtl:ignore*/
.pos .keyboard li {
	float: left;
	text-align: center;
	background-color: #fff;
	border: 1px solid #f0f0f0;
    top:0;
    cursor: pointer;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
    -webkit-transition-property: top, background-color;
    -webkit-transition-duration: 0.2s;
    -webkit-transition-timing-function: linear;
}
/*rtl:ignore*/
.pos .keyboard li:active{
	position: relative;
	top: 2px;
	left: 0px;
	border-color: #ddd;
    background-color:#e5e5e5;
	cursor: pointer;
    -webkit-transition-property: top, background-color;
    -webkit-transition-duration: 0.1s;
    -webkit-transition-timing-function: ease-out;
}
.pos .uppercase {
	text-transform: uppercase;
}
.pos .on {
	display: none;
}
/*rtl:ignore*/
.pos .firstitem{
	clear: left;
}
/*rtl:ignore*/
.pos .keyboard .lastitem {
	margin-right: 0 !important;
}

/* ---- full sized keyboard ---- */

.pos .full_keyboard {
    list-style: none;
    font-size: 14px;
    width: 685px;
    height: 100%;
    margin-left: auto !important;
    margin-right: auto !important;
}
/*rtl:ignore*/
.pos .full_keyboard li{
	margin: 0 5px 5px 0 !important;
	width: 40px;
	height: 40px;
	line-height: 40px;
}
.pos .full_keyboard .tab, .pos .full_keyboard .delete {
	width: 70px;
}
.pos .full_keyboard .capslock {
	width: 80px;
}
.pos .full_keyboard .return {
	width: 77px;
}
.pos .full_keyboard .left-shift {
	width: 95px;
}
.pos .full_keyboard .right-shift {
	width: 109px;
}

/*rtl:ignore*/
.pos .full_keyboard .space {
	clear: left;
	width: 673px;
}

/* ---- simplified keyboard ---- */

.pos .simple_keyboard {
    list-style: none;
    font-size: 16px;
    width: 555px;
    height: 220px;
    margin-left: auto !important;
    margin-right: auto !important;
}
/*rtl:ignore*/
.pos .simple_keyboard li{
    margin: 0 5px 5px 0 !important;
    width: 49px;
    height: 49px;
    line-height: 49px;
}
/*rtl:ignore*/
.pos .simple_keyboard .firstitem.row_asdf{
    margin-left:25px !important;
}
/*rtl:ignore*/
.pos .simple_keyboard .firstitem.row_zxcv{
    margin-left:55px !important;
}
.pos .simple_keyboard .delete{
    width: 103px;
}
.pos .simple_keyboard .return{
    width: 103px;
}
.pos .simple_keyboard .space{
    width:273px;
}
.pos .simple_keyboard .numlock{
    width:103px;
}
