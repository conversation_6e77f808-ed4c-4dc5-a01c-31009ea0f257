# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_links
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " clicks"
msgstr "點擊次數"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid " countries"
msgstr "國家"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "# of clicks"
msgstr "點擊次數"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.share_page_menu
msgid "<span title=\"Track this page to count clicks\">Link Tracker</span>"
msgstr "<span title=\"追蹤此頁，以統計點擊數目\">連結追蹤工具</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>宣傳活動</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>媒介</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>原始網址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>重新定向網址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>來源</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>追蹤連結</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "持續"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Campaign <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the context of your link. It might be an event you want to promote or a "
"special promotion.\"/>"
msgstr ""
"宣傳活動 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"工具提示資訊\" "
"title=\"界定連結的背景資料。可以是你想宣傳的某個活動，或特別的促銷 / 推廣計劃。\"/>"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Copied"
msgstr "複製"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Copy"
msgstr "複製"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "Edit code"
msgstr "編輯代碼"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Generating link..."
msgstr "產生連結中..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Get tracked link"
msgstr "獲取追蹤連結"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Icon"
msgstr "圖示"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "上月"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "上周"

#. module: website_links
#: model:ir.model,name:website_links.model_link_tracker
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "連結追蹤"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Medium <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the medium used to share your link. It might be an email, or a Facebook Ads "
"for instance.\"/>"
msgstr ""
"媒介 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"工具提示資訊\" "
"title=\"界定用作分享連結的媒介。例如，可以是一封電郵，或 Facebook 廣告。\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Most Clicked"
msgstr "點選最多"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "最新"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "No data"
msgstr "沒有資料"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Recently Used"
msgstr "最近使用"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr "使用包含<strong>分析追蹤器</strong>的<strong>短網址</strong> 來分享這個頁面。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Source <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"Tooltip info\" title=\"Defines "
"the source from which your traffic will come from, Facebook or Twitter for "
"instance.\"/>"
msgstr ""
"來源 <i class=\"fa fa-info-circle\" data-toggle=\"tooltip\" data-"
"placement=\"top\" role=\"img\" aria-label=\"工具提示資訊\" title=\"界定瀏覽流量的來源，例如 "
"Facebook 或 Twitter。\"/>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#: model_terms:ir.ui.view,arch_db:website_links.link_tracker_view_tree
msgid "Statistics"
msgstr "統計資訊"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "Stats"
msgstr "統計資料"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "The code cannot be left empty"
msgstr "代碼不可空白"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "There is no data to show"
msgstr "沒有顯示的資料"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
#, python-format
msgid "This code is already taken"
msgstr "此代碼已被使用"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr "這些追蹤器可以被Google分析去追蹤點選和訪問，或者在odoo的報表裡追蹤機會和相關收益。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "URL"
msgstr "網址"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "Unable to get recent links"
msgstr "無法獲取最近連結"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links_charts.js:0
#, python-format
msgid "Undefined"
msgstr "未定義的"

#. module: website_links
#: code:addons/website_links/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage Statistics"
msgstr "查看網頁統計資訊"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "You don't have any recent links."
msgstr "您沒有任何最近的連結。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "您追蹤的連結"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "cancel"
msgstr "取消"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#, python-format
msgid "clicks"
msgstr "點擊次數"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "copy"
msgstr "複製"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Newsletter, Social Network, .."
msgstr "例如：快訊、社交網路、..."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Promotion of June, Winter Newsletter, .."
msgstr "例如：六月促銷，冬季通訊，..."

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/js/website_links.js:0
#, python-format
msgid "e.g. Search Engine, Website page, .."
msgstr "例如：搜尋引擎，網站頁面，..."

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/contactus"
msgstr "例：https://www.odoo.com/contactus"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "ok"
msgstr "ok"

#. module: website_links
#. openerp-web
#: code:addons/website_links/static/src/xml/recent_link.xml:0
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#, python-format
msgid "or"
msgstr "或"
