<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Purchase-related subtypes for messaging / Chatter -->
        <record id="mt_rfq_confirmed" model="mail.message.subtype">
            <field name="name">RFQ Confirmed</field>
            <field name="default" eval="False"/>
            <field name="res_model">purchase.order</field>
        </record>
        <record id="mt_rfq_approved" model="mail.message.subtype">
            <field name="name">RFQ Approved</field>
            <field name="default" eval="False"/>
            <field name="res_model">purchase.order</field>
        </record>
        <record id="mt_rfq_done" model="mail.message.subtype">
            <field name="name">RFQ Done</field>
            <field name="default" eval="False"/>
            <field name="res_model">purchase.order</field>
        </record>

        <!-- Sequences for purchase.order -->
        <record id="seq_purchase_order" model="ir.sequence">
            <field name="name">Purchase Order</field>
            <field name="code">purchase.order</field>
            <field name="prefix">P</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>

        <!-- Share Button in action menu -->
        <record id="model_purchase_order_action_share" model="ir.actions.server">
            <field name="name">Share</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="binding_model_id" ref="purchase.model_purchase_order"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_share()</field>
        </record>

        <!-- Default value for company_dependant field -->
        <record forcecreate="True" id="receipt_reminder_email" model="ir.property">
            <field name="name">receipt_reminder_email</field>
            <field name="type" eval="'boolean'"/>
            <field name="fields_id" search="[('model','=','res.partner'),('name','=','receipt_reminder_email')]"/>
            <field eval="False" name="value"/>
        </record>
        <record forcecreate="True" id="reminder_date_before_receipt" model="ir.property">
            <field name="name">reminder_date_before_receipt</field>
            <field name="type" eval="'integer'"/>
            <field name="fields_id" search="[('model','=','res.partner'),('name','=','reminder_date_before_receipt')]"/>
            <field eval="1" name="value"/>
        </record>
    </data>
</odoo>
