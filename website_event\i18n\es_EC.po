# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_event
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON><PERSON> <oihane<PERSON><PERSON><EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-07-06 22:17+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event
#: code:addons/website_event/controllers/main.py:219
#, python-format
msgid "%(month)s %(start_day)s%(end_day)s"
msgstr "%(month)s %(start_day)s%(end_day)s"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>Upcoming Events</b>"
msgstr "<b>Próximos eventos</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> To"
msgstr ""
"<br/>\n"
"                    <i class=\"fa fa-clock-o\"/> Para"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<em>Configure and Launch Event Registration</em>"
msgstr "<em>Configura y Cargar Registro de Evento</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-clock-o\"/> From"
msgstr "<i class=\"fa fa-clock-o\"/> De"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i>to</i>"
msgstr "<i>a</i>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid "<small>Author</small>"
msgstr "<small>Autor</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "<span class=\"oe_snippet_thumbnail_title\">Local Events</span>"
msgstr "<span class=\"oe_snippet_thumbnail_title\">Eventos locales</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Eventos en línea</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span>Unlimited</span>"
msgstr "<span>Ilimitado</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Attendees</strong>"
msgstr "<strong>Asistentes</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Email</strong>"
msgstr "<strong>Email</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Name</strong>"
msgstr "Nombre"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<strong>Participate on Twitter</strong>"
msgstr "<strong>Participar en Twitter</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong>Phone</strong> <span class=\"text-muted\">(Optional)</span>"
msgstr "<strong>Teléfono</strong> <span class=\"text-muted\">(Opcional)</span>"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:25
#, python-format
msgid "Add Content"
msgstr "Añadir contenido"

#. module: website_event
#: code:addons/website_event/controllers/main.py:106
#, python-format
msgid "All Categories"
msgstr "Todas las categorías"

#. module: website_event
#: code:addons/website_event/controllers/main.py:117
#, python-format
msgid "All Countries"
msgstr "Todos los países"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "All Events"
msgstr "Todos los eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendee #%s"
msgstr "Asistentes #%s"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel Registration"
msgstr "Cancelar registro"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:48
#, python-format
msgid "Click <em>Continue</em> to create the event."
msgstr "Pulse en <em>continuar</em> para crear el evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:33
#, python-format
msgid "Click here to create a new event."
msgstr "Pulse aquí para crear un nuevo evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:82
#, python-format
msgid "Click here to customize your event further."
msgstr "Pulse aquí para personalizar más su evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:75
#, python-format
msgid "Click to publish your event."
msgstr "Pulse para publicar su evento."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:54
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:47
#, python-format
msgid "Create Event"
msgstr "Crear evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:41
#, python-format
msgid ""
"Create a name for your new event and click <em>'Continue'</em>. e.g: "
"Technical Training"
msgstr ""
"Crear un nombre para su nuevo evento y pulse <em>'Continuar'</em>. Por "
"ejemplo: Formación técnica"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:18
#, python-format
msgid "Create an Event"
msgstr "Crear un evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:40
#, python-format
msgid "Create an Event Name"
msgstr "Crear un nombre de evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:15
#, python-format
msgid "Create an event"
msgstr "Crear un evento"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event_show_menu
msgid ""
"Creates menus Introduction, Location and Register on the page  of the event "
"on the website."
msgstr ""
"Crea menús Introducción, Ubicación y Registro en la página del evento en la "
"página web."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:81
#, python-format
msgid "Customize your event"
msgstr "Personalizar su evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_show_menu
msgid "Dedicated Menu"
msgstr "Menú dedicado"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:59
#, python-format
msgid "Drag & Drop a block"
msgstr "Arrastre y suelte un bloque"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:60
#, python-format
msgid "Drag the 'Image-Text' block and drop it in your page."
msgstr "Arrastrar el bloque 'Imagen-Texto' y soltarlo en su página."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "E-mail"
msgstr "Email"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.view_event_sale_form
msgid "Edit Badges"
msgstr "Editar Insignia"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "End of Subscription"
msgstr "Fin de la suscripción"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
msgid "Event"
msgstr "Evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Event Details"
msgstr "Detalles de evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid "Event Introduction"
msgstr "Introducción del evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Localización del evento"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_menu_id
msgid "Event Menu"
msgstr "Menú del evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event Subscription"
msgstr "Suscripción a eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Evento no encontrado"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Evento publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Event registration not yet started."
msgstr "Registro de eventos aún no comenzó."

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Evento despublicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.header_footer_custom
#: model:website.menu,name:website_event.menu_events
msgid "Events"
msgstr "Eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_country_event
msgid "Events from Your Country"
msgstr "Eventos en su país"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events
msgid "Events in visitor's country"
msgstr "Eventos en el país del visitante"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Events:"
msgstr "Eventos:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event,\n"
"                            and join the conversation."
msgstr ""
"Encontrar qué gente ve y habla sobre este evento, y unirse a la conversación."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Free"
msgstr "Libre"

#. module: website_event
#: code:addons/website_event/models/event.py:44
#, python-format
msgid "Introduction"
msgstr "Introducción"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:19
#, python-format
msgid "Let's go through the first steps to publish a new event."
msgstr "Vayamos a través de los primeros pasos para publicar un nuevo evento."

#. module: website_event
#: code:addons/website_event/models/event.py:45
#, python-format
msgid "Location"
msgstr "Ubicación"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr "N/A"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Name"
msgstr "Nombre"

#. module: website_event
#. openerp-web
#: code:addons/website_event/controllers/main.py:201
#: code:addons/website_event/static/src/js/website.tour.event.js:32
#: code:addons/website_event/static/src/js/website_event.editor.js:14
#: model_terms:ir.ui.view,arch_db:website_event.content_new_event
#, python-format
msgid "New Event"
msgstr "Nuevo evento"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:52
#, python-format
msgid "New Event Created"
msgstr "Creado nuevo evento"

#. module: website_event
#: code:addons/website_event/controllers/main.py:38
#, python-format
msgid "Next Events"
msgstr "Próximos eventos"

#. module: website_event
#: code:addons/website_event/controllers/main.py:47
#, python-format
msgid "Next Week"
msgstr "Próxima semana"

#. module: website_event
#: code:addons/website_event/controllers/main.py:55
#, python-format
msgid "Next month"
msgstr "Próximo mes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "No event found"
msgstr "No se encontró ningún evento"

#. module: website_event
#: code:addons/website_event/controllers/main.py:59
#, python-format
msgid "Old Events"
msgstr "Eventos antiguos"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:67
#, python-format
msgid "Once you click on save, your event is updated."
msgstr "Una vez pulse en guardar, su evento será actualizado."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Online"
msgstr "En línea"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Organized by:"
msgstr "Organizado por:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizador"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Our Events"
msgstr "Nuestros eventos"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Our Trainings"
msgstr "Nuestras formaciones"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Phone"
msgstr "Teléfono"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_photos
msgid "Photos of Past Events"
msgstr "Fotos de eventos pasados"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Price"
msgstr "Precio"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:74
#, python-format
msgid "Publish your event"
msgstr "Publicar su evento"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Quantity"
msgstr "Cantidad"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Reference"
msgstr "Referencia"

#. module: website_event
#: code:addons/website_event/models/event.py:53
#, python-format
msgid "Register"
msgstr "Registro"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Register Now"
msgstr "Registrar Ahora"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Regresar a la lista de eventos"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:66
#, python-format
msgid "Save your modifications"
msgstr "Guardar los cambios"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr "Ver todos los eventos de"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all upcoming events"
msgstr "Ver todos los eventos próximos"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Skip It"
msgstr "Saltar este paso"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Social Stream"
msgstr "Flujo social"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Agotado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Lo sentimos, el evento solicitado no está ya disponible."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:20
#, python-format
msgid "Start Tutorial"
msgstr "Iniciar tutorial"

#. module: website_event
#: code:addons/website_event/controllers/main.py:43
#, python-format
msgid "This Week"
msgstr "Esta semana"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:26
#, fuzzy, python-format
msgid "This button allows you to create new pages, events, menus, etc."
msgstr ""
"El menú <em>Contenido</em> permite crear nuevas páginas, eventos, menús, etc."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website.tour.event.js:53
#, python-format
msgid "This is your new event page. We will edit the event presentation page."
msgstr ""
"Ésta es su página de nuevo evento. Se editará a continuación la página de "
"presentación."

#. module: website_event
#: code:addons/website_event/controllers/main.py:51
#, python-format
msgid "This month"
msgstr "Este mes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Type"
msgstr "Tipo de Entrada"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket Type #"
msgstr "Nº de tipo de entrada"

#. module: website_event
#: code:addons/website_event/controllers/main.py:39
#, python-format
msgid "Today"
msgstr "Hoy"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_twitter_hashtag
msgid "Twitter Hashtag"
msgstr "Hashtag de Twitter"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "Use the top menu <i>'Content'</i> to create your first event."
msgstr "Usa el menú superior <i>\"Contenido\"</i> para crear su primer evento."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Use this tag:"
msgstr "Utilice esta etiqueta:"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event_website_published
msgid "Visible in Website"
msgstr "Visible en el sitio web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "We are glad to confirm your subscription to our event"
msgstr "Nos complace confirmar su suscripción a nuestro evento"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Página de inicio del sitio web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "When"
msgstr "Cuándo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Where"
msgstr "Dónde"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_right_quotes
msgid ""
"Write here a quote from one of your attendees.\n"
"                        It gives confidence in your\n"
"                        events."
msgstr ""
"Escriba aquí una cita de uno de los asistentes. Dará credibilidad a sus "
"eventos."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Your subscription"
msgstr "Su suscripción"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "not published"
msgstr "no publicado"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "or"
msgstr "o"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index
msgid "pull-right"
msgstr "pull-right"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "to"
msgstr "a"

#~ msgid "Drag the 'Text Block' in your event page."
#~ msgstr "Arrastar el bloque 'Texto' en su página de evento."

#~ msgid "The full URL to access the document through the website."
#~ msgstr "La URL completa para acceder al documento a través de la web."

#~ msgid "Website Messages"
#~ msgstr "Mensajes del sitio web"

#~ msgid "Website URL"
#~ msgstr "URL del sitio web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicaciones del sitio web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"
