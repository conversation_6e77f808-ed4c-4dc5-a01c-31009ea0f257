<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="sale_coupon_view_tree" model="ir.ui.view">
        <field name="name">coupon.coupon.tree</field>
        <field name="model">coupon.coupon</field>
        <field name="inherit_id" ref="coupon.coupon_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="order_id"/>
            </xpath>
        </field>
    </record>

    <record id="sale_coupon_view_form" model="ir.ui.view">
        <field name="name">coupon.coupon.form</field>
        <field name="model">coupon.coupon</field>
        <field name="inherit_id" ref="coupon.coupon_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="order_id" invisible="1"/>
                <field name="sales_order_id" invisible="1"/>
                <field name="order_id" readonly="1" attrs="{'invisible': [('order_id', '=', False)]}" />
                <field name="sales_order_id" readonly="1" attrs="{'invisible': [('sales_order_id', '=', False)]}" />
            </xpath>
        </field>
    </record>

</odoo>
