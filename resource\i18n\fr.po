# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: resource
#: code:addons/resource/models/resource.py:0
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Active"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "Après-midi"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Archivé"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 1 week calendar ? All "
"entries will be lost"
msgstr ""
"Êtes-vous sûr de vouloir passer à un calendrier d'une semaine ? Toutes les "
"entrées seront perdues"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch this calendar to 2 weeks calendar ? All "
"entries will be lost"
msgstr ""
"Êtes-vous sûr de vouloir passer à un calendrier de 2 semaines ? Toutes les "
"entrées seront perdues"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Attendances can't overlap."
msgstr "Les présences ne peuvent pas se chevaucher."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Nombre moyen d'heures par jour"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Le nombre d'heures en moyenne qu'un employé est supposé travailler avec ce "
"calendrier. "

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Calendrier en mode 2 semaines"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Jours de fermeture"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model:ir.model.fields,field_description:resource.field_resource_test__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Société"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
#: model:ir.model.fields,field_description:resource.field_resource_test__create_date
msgid "Created on"
msgstr "Créé le"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Période en jour"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "Jour de la semaine"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Heures de travail par défaut"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
#: model:ir.model.fields,help:resource.field_resource_test__resource_calendar_id
msgid "Define the schedule of resource"
msgstr "Définir la planification de la ressource"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Définir les heures de travail et un emploi du temps qui pourrait être établi"
" pour les membres de votre projet"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
#: model:ir.model.fields,field_description:resource.field_resource_test__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Type d'affichage"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Facteur d'efficacité"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Date de fin"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Explication"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Premier"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "First week"
msgstr "Première semaine"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Vendredi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Afternoon"
msgstr "Vendredi après-midi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Friday Morning"
msgstr "Vendredi Matin"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr ""
"Indique la séquence de cette ligne lors de l'affichage du calendrier des "
"ressources."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Congé global"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Regrouper par"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Heures"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Humain"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
#: model:ir.model.fields,field_description:resource.field_resource_test__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"S'il est vide, il s'agit d'un congé générique pour l'entreprise. Si une "
"ressource est définie, le congé est uniquement pour cette ressource."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Si vous cochez la case \"Active\", la resource sera cachée mais pas "
"supprimée."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Si le champs actif est False, vous pourrez cacher le temps de travail sans "
"le supprimer."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Impossible to switch calendar type for the default company schedule."
msgstr ""
"Impossible de changer le type de calendrier pour le calendrier d'entreprise "
"par défaut."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"En mode calendrier à 2 semaines, toutes les périodes doivent être dans les "
"sections."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance____last_update
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves____last_update
#: model:ir.model.fields,field_description:resource.field_resource_resource____last_update
#: model:ir.model.fields,field_description:resource.field_resource_test____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_test__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
#: model:ir.model.fields,field_description:resource.field_resource_test__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Leave Date"
msgstr "Date de congé"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Leave Detail"
msgstr "Détail du congé"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Matériel"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Lundi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Afternoon"
msgstr "Lundi après-midi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Monday Morning"
msgstr "Lundi Matin"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Matin"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
#: model:ir.model.fields,field_description:resource.field_resource_test__name
msgid "Name"
msgstr "Nom"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Autre"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Période"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Motif"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Utilisateur associé à la ressource pour gérer les droits d'accès."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Ressource"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Combinaison de ressources"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Congé de la ressource"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Détails des congés de la ressource"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Temps de travail de la ressource"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Calendrier de la ressource"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Ressources"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Congé des ressources"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Les ressources vous permettent de créer et de gérer les ressources qui sont "
"impliquées dans une phase spécifique d'un projet. Vous pouvez aussi indiquer"
" le niveau d'efficacité et la charge de travail en fonction des heures "
"travaillées hebdomadairement."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Samedi"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Chercher une ressource"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Chercher les congés sur une période de travail"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Chercher un horaire"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Seconde"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Second week"
msgstr "Deuxième semaine"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Section"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: resource
#: code:addons/resource/models/res_company.py:0
#, python-format
msgid "Standard 40 hours/week"
msgstr "Standard 40 heures/semaine"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Date de début"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Heure de Début et Fin de travail.\n"
"Une valeur spécifique de 24:00 sera interprétée comme 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Date de début"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Leave"
msgstr "Date de Début du Congé"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Dimanche"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Passer à un calendrier d'une semaine"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Passer à un calendrier de 2 semaines"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Champ technique utilisé à des fins ergonomiques"

#. module: resource
#: model:ir.model,name:resource.model_resource_test
msgid "Test Resource Model"
msgstr "Tester le modèle de ressource"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The current week (from %s to %s) correspond to the  %s one."
msgstr "La semaine actuelle (du %s au %s) correspond à la  %s."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "The start date of the time off must be earlier than the end date."
msgstr "La date de début du congé doit se situer plus tôt que la date de fin."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
#: model:ir.model.fields,help:resource.field_resource_resource__tz
#: model:ir.model.fields,help:resource.field_resource_test__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Ce champ est utilisé pour définir dans quel fuseau horaire la ressource "
"travaillera."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Ce champ est utilisé pour calculer la durée prévue d'un ordre de travail à "
"ce poste de travail. Par exemple, si un ordre de travail prend une heure et "
"que le facteur d'efficacité est de 100 %, la durée prévue sera d'une heure. "
"Si le facteur d'efficacité est de 200 %, la durée prévue sera de 30 minutes."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Jeudi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Afternoon"
msgstr "Jeudi après-midi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Thursday Morning"
msgstr "Jeudi Matin"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Time Off"
msgstr "Congés"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Type de temps"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr "Le rendement temporal doit être strictement positif."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
#: model:ir.model.fields,field_description:resource.field_resource_test__tz
msgid "Timezone"
msgstr "Fuseau horaire"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Décalage horaire"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Mardi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Afternoon"
msgstr "Mardi après-midi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Tuesday Morning"
msgstr "Mardi Matin"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Type"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Utilisateur"

#. module: resource
#: model:ir.model,name:resource.model_res_users
msgid "Users"
msgstr "Utilisateurs"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Mercredi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Afternoon"
msgstr "Mercredi après-midi"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Wednesday Morning"
msgstr "Mercredi Matin"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Numéro de la semaine"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Si cela doit être calculé en tant que congé ou en tant que temps de travail "
"(ex : formation)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Détail du travail"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Work Resources"
msgstr "Ressources de travail"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Travaille à partir de"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Travaille jusqu'à"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_test__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Heures de travail"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "Working Hours of %s"
msgstr "Heures de travail de %s"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Temps de travail"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Times"
msgstr "Temps de travail"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "You can't delete section between weeks."
msgstr "Vous ne pouvez pas supprimer une section entre deux semaines."

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "first"
msgstr "remière"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "other week"
msgstr "autre semaine"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "second"
msgstr "seconde"

#. module: resource
#: code:addons/resource/models/resource.py:0
#, python-format
msgid "this week"
msgstr "cette semaine"
