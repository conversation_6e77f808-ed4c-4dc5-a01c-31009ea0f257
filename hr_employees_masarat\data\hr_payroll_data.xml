<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">


        <record id="hr_rule_marige" model="hr.salary.rule">
            <field name="name">مكافئة زواج</field>
            <field name="sequence" eval="5"/>
            <field name="code">MARIGE</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.look_for_marige</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.look_for_marige</field>
        </record>

        <record id="hr_rule_leave_topaid" model="hr.salary.rule">
            <field name="name">تخصيص مالية بدل من الاجازة</field>
            <field name="sequence" eval="6"/>
            <field name="code">LEVMAR</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.leave_allocation_monetry_amount</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.leave_allocation_monetry_amount</field>
        </record>

        <record id="payslip_annual_assessment_amount" model="hr.salary.rule">
            <field name="name">التقويم النصف سنوي</field>
            <field name="sequence" eval="6"/>
            <field name="code">ANNAMO</field>
            <field name="category_id" ref="hr_payroll_community.ALW"/>
            <field name="condition_select">python</field>
            <field name="condition_python">result = payslip.annually_assessment_result</field>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = payslip.annually_assessment_result</field>
        </record>



    </data>
</odoo>
