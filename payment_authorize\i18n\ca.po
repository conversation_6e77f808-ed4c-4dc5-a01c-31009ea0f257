# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_authorize
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# j<PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "ABA Routing Number"
msgstr "Número d'encaminament ABA"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_client_key
msgid "API Client Key"
msgstr "Clau del client de l'API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_login
msgid "API Login ID"
msgstr "ID d'inici de sessió de l'API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_signature_key
msgid "API Signature Key"
msgstr "Clau de signatura de l'API"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_transaction_key
msgid "API Transaction Key"
msgstr "Clau de l'API de transacció"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Account Number"
msgstr "Número de compte"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Allow Payments From"
msgstr "Permet pagaments des de"

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr "S'ha produït un error en mostrar aquest formulari de pagament."

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__authorize_currency_id
msgid "Authorize Currency"
msgstr "Autoritza la moneda"

#. module: payment_authorize
#: model:account.payment.method,name:payment_authorize.payment_method_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__provider__authorize
msgid "Authorize.Net"
msgstr "Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "Authorize.Net Payment Type"
msgstr "Autoritza.Tipus de pagament net"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_token__authorize_profile
msgid "Authorize.Net Profile ID"
msgstr "Autoritza.Identificador del perfil de xarxa"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Bank (powered by Authorize)"
msgstr "Banc (impulsat per Authorize)"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__bank_account
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__bank_account
msgid "Bank Account (USA Only)"
msgstr "Compte bancari (només Estats Units)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Account Type"
msgstr "Tipus de compte bancari"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Bank Name"
msgstr "Nom del Banc "

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Business Checking"
msgstr "Comprovació de negocis"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Code"
msgstr "Codi de targeta"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Card Number"
msgstr "Número de targeta"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Could not fetch merchant details:\n"
"%s"
msgstr ""
" No s'han pogut recuperar els detalls del comerciant:\n"
"%s"

#. module: payment_authorize
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_acquirer__authorize_payment_method_type__credit_card
#: model:ir.model.fields.selection,name:payment_authorize.selection__payment_token__authorize_payment_method_type__credit_card
msgid "Credit Card"
msgstr "Targeta de crèdit"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "Credit Card (powered by Authorize)"
msgstr "Targeta de crèdit (activada per Authorize)"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Currency"
msgstr "Divisa"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_payment_method_type
msgid "Determines with what payment method the customer can pay."
msgstr "Determina amb quin mètode de pagament pot pagar el client."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Expiration"
msgstr "Caducitat"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"Failed to authenticate.\n"
"%s"
msgstr ""
"No s'ha pogut autenticar.\n"
"%s"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Generate Client Key"
msgstr "Genera la clau del client"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "How to get paid with Authorize.Net"
msgstr "Com pagar amb Authorize.Net"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Name On Account"
msgstr "Nom al compte"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No s'ha trobat cap transacció que coincideixi amb la referència %s."

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Mètode de pagament"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_account_payment_method
msgid "Payment Methods"
msgstr "Formes de pagament"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_token
msgid "Payment Token"
msgstr "Token de pagament"

#. module: payment_authorize
#: model:ir.model,name:payment_authorize.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transacció de pagament"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Checking"
msgstr "Comprovació personal"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "Personal Savings"
msgstr "Desaments personals"

#. module: payment_authorize
#: model:ir.model.fields,field_description:payment_authorize.field_payment_acquirer__provider
msgid "Provider"
msgstr "Proveïdor"

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "Received data with status code \"%(status)s\" and error code \"%(error)s\""
msgstr "Dades rebudes amb codi d'estat \"%(status)s\"i codi d'error \"%(error)s\""

#. module: payment_authorize
#: code:addons/payment_authorize/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr "S'han rebut dades de sol·licitud de pagament manipulades."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr ""
"Els mètodes de pagament desats no es poden restaurar una vegada que s'hagin "
"eliminat."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Error del servidor"

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.payment_acquirer_form
msgid "Set Account Currency"
msgstr "Estableix la moneda del compte"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_login
msgid "The ID solely used to identify the account with Authorize.Net"
msgstr "L'ID només s'utilitza per identificar el compte amb Authorize.Net"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""
"El proveïdor de serveis de pagament que s'utilitzarà amb aquesta entitat "
"adquirent"

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_acquirer__authorize_client_key
msgid ""
"The public client key. To generate directly from Odoo or from Authorize.Net "
"backend."
msgstr ""
"La clau pública del client. Per a generar directament des de Odoo o des del "
"backend d'Authorize.net."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr "La transacció no està enllaçada a un token."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_payment_method_type
msgid "The type of payment method this token is linked to."
msgstr "El tipus de mètode de pagament al qual està enllaçat aquest token."

#. module: payment_authorize
#: model:ir.model.fields,help:payment_authorize.field_payment_token__authorize_profile
msgid ""
"The unique reference for the partner/token combination in the Authorize.net "
"backend."
msgstr ""
"La referència única per a la combinació soci/token al backend "
"d'Authorize.net."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid ""
"There are active tokens linked to this acquirer. To change the payment "
"method type, please disable the acquirer and duplicate it. Then, change the "
"payment method type on the duplicated acquirer."
msgstr ""
"Hi ha fitxes actives vinculades a aquest adquirent. Per canviar el tipus de "
"mètode de pagament, inhabiliteu l'adquirent i duplica'l. Després, canvieu el"
" tipus de mètode de pagament en l'adquirent duplicat."

#. module: payment_authorize
#: code:addons/payment_authorize/models/payment_acquirer.py:0
#, python-format
msgid "This action cannot be performed while the acquirer is disabled."
msgstr ""
"Aquesta acció no pot realitzar-se mentre l'entitat adquirent estigui "
"desactivada."

#. module: payment_authorize
#. openerp-web
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#: code:addons/payment_authorize/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr "No podem processar el pagament."

#. module: payment_authorize
#: model_terms:ir.ui.view,arch_db:payment_authorize.inline_form
msgid "YY"
msgstr "YY"
