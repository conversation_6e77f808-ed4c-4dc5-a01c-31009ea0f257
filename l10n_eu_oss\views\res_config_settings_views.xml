<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.l10n.eu.service</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div name="l10n_eu_oss_right_pane" position="inside">
                <field name="l10n_eu_oss_eu_country" invisible="1"/>
                <div class="content-group" attrs="{'invisible': [('module_l10n_eu_oss', '=', False)]}">
                    <div class="mt8" attrs="{'invisible': [('l10n_eu_oss_eu_country', '=', False)]}">
                        <button type="object" name="refresh_eu_tax_mapping" icon="fa-refresh" string="Refresh tax mapping" class="btn-link"/>
                    </div>
                </div>
            </div>
        </field>
    </record>

</odoo>
