# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_tour
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-08-24 09:33+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:386
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:7
#, python-format
msgid "Disable Tours"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "Auðkenni"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Valmynd"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:17
#, python-format
msgid "Name"
msgstr "Nafn"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:18
#, python-format
msgid "Path"
msgstr "Path"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/js/tour_manager.js:76
#, python-format
msgid "Skip tour"
msgstr ""

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:8
#, python-format
msgid "Start Tour"
msgstr ""

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Þjórfé"

#. module: web_tour
#. openerp-web
#: code:addons/web_tour/static/src/xml/debug_manager.xml:25
#, python-format
msgid "Tour"
msgstr ""

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr ""

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "Tours"
