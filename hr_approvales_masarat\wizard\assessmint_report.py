# -*- coding:utf-8 -*-
from odoo import api, fields, models, _
from datetime import datetime, timedelta
from pytz import timezone
from odoo.exceptions import ValidationError


class MasaratEmployeeAssessmintaRepo(models.TransientModel):
    _name = "hr.assessment.annual.report"

    #### Date period
    @api.model
    def year_selection(self):
        year = 2010
        year_list = []
        while year != 2050:
            year_list.append((str(year), str(year)))
            year += 1
        return year_list

    year = fields.Selection(year_selection, string="السنة", required=True)
    #
    # employee_id = fields.Many2one('hr.employee', string="الموظف")
    # all_employee = fields.Boolean(string="كل الموظفين")
    # date_start = fields.Date(string='تاريخ البدأ')
    # date_end = fields.Date(string='تاريخ الانتهاء')

    def get_report_action(self):
        # if self.all_employee:
        #     ee = False
        # else:
        #     ee = self.employee_id.id
        ee = False
        all_employee = True
        data = {'model': self._name, 'employee_id': ee,'all_employee':all_employee, 'year':self.year}
        return self.sudo().env.ref('hr_approvales_masarat.assessment_annual_report_x1').report_action(self, data=data)


class MasaratEmployeeRewardaRepoAbs(models.AbstractModel):
    _name = "report.hr_approvales_masarat.assessment_annual_report_id"

    def _get_report_values_all(self,year):
        rewards = self.env['hr.contract.assessment.annual'].search([('year','=',year),('state','=','done')])
        vals = {}
        for elem in rewards:
            vals.setdefault(str(elem.employee_id.id),{'name':str(elem.employee_id.name), 'assess':[]})
            vals[str(elem.employee_id.id)]['assess'].append({
                'month':elem.month,
                'time_attendance_result':round(float(elem.time_attendance_result),2),
                'performance_result':round(float(elem.performance_result),2),
                'behaviors_result':round(float(elem.behaviors_result),2),
                'final_result':round(float(elem.final_result),2),
            })
        for employee in vals.keys():
            f_time_attendance_result = 0
            f_performance_result = 0
            f_behaviors_result = 0
            f_final_result= 0
            count = 0
            for line in vals[employee]['assess']:
                f_time_attendance_result += round(float(line['time_attendance_result']),2)
                f_performance_result += round(float(line['performance_result']),2)
                f_behaviors_result += round(float(line['behaviors_result']),2)
                f_final_result += round(float(line['final_result']),2)
                count+=1
            vals[employee]['f_time_attendance_result']=round(float(f_time_attendance_result/count),2)
            vals[employee]['f_performance_result']=round(float(f_performance_result/count),2)
            vals[employee]['f_behaviors_result']=round(float(f_behaviors_result/count),2)
            vals[employee]['f_final_result']=round(float(f_final_result/count),2)

        return {'all_employee': True, 'employees_dict': vals, 'year': year}




    def _get_report_values(self, docids, data=None):
        if data['all_employee']:
            all_employee = self._get_report_values_all(data['year'])
            return all_employee
        else:
            # one_employee = self._get_by_employee(data['date_start'],data['date_end'], data['employee_id'])
            pass


