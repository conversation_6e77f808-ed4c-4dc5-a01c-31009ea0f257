<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Add action entry in the Action Menu for Partners -->
    <record id="res_partner_view_form" model="ir.ui.view">
        <field name="name">res.partner.view.form.inherit.sms</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='mobile']" position="after">
                <field name="phone_sanitized" groups="base.group_no_one" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='phone']" position="replace">
                <field name="phone_blacklisted" invisible="1"/>
                <field name="mobile_blacklisted" invisible="1"/>
                <label for="phone" class="oe_inline"/>
                <div class="o_row o_row_readonly">
                    <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                        title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                        type="object" context="{'default_phone': phone}" groups="base.group_user"
                        attrs="{'invisible': [('phone_blacklisted', '=', False)]}"/>
                    <field name="phone" widget="phone"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='mobile']" position="replace">
                <field name="phone_blacklisted" invisible="1"/>
                <field name="mobile_blacklisted" invisible="1"/>
                <label for="mobile" class="oe_inline"/>
                <div class="o_row o_row_readonly">
                    <button name="phone_action_blacklist_remove" class="fa fa-ban text-danger"
                        title="This phone number is blacklisted for SMS Marketing. Click to unblacklist."
                        type="object" context="{'default_phone': mobile}" groups="base.group_user"
                        attrs="{'invisible': [('mobile_blacklisted', '=', False)]}"/>
                    <field name="mobile" widget="phone"/>
                </div>
            </xpath>
        </field>
    </record>

    <!-- Add action entry in the Action Menu for Partners -->
    <record id="res_partner_act_window_sms_composer_multi" model="ir.actions.act_window">
        <field name="name">Send SMS Text Message</field>
        <field name="res_model">sms.composer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_composition_mode': 'mass',
            'default_mass_keep_log': True,
            'default_res_ids': active_ids
        }</field>
        <field name="binding_model_id" ref="base.model_res_partner"/>
        <field name="binding_view_types">list</field>
    </record>
    <record id="res_partner_act_window_sms_composer_single" model="ir.actions.act_window">
        <field name="name">Send SMS Text Message</field>
        <field name="res_model">sms.composer</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_composition_mode': 'comment',
            'default_res_id': active_id,
        }</field>
        <field name="binding_model_id" ref="base.model_res_partner"/>
        <field name="binding_view_types">form</field>
    </record>

    <record id="res_partner_view_search" model="ir.ui.view">
        <field name="name">res.partner.view.search.inherit.sms</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='phone']" position="replace">
                <field name="phone_mobile_search"/>
            </xpath>
        </field>
    </record>

</odoo>
