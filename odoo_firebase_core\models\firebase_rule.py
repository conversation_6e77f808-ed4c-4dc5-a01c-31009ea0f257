import logging
import unicodedata

from odoo import models, fields, api, exceptions
from odoo.tools import safe_eval


def unaccent_string(txt):
    return unicodedata.normalize('NFD', txt).encode('ascii', 'ignore').decode('utf-8')


def get_item_dict(self, rule, item, level=0):
    if rule.result_type == 'method' and hasattr(item, rule.method):
        return getattr(item, rule.method)()
    dict_value = {
        'search_terms': []
    }
    fields_dict = [
        ('model_id.model', '=', item._name),
        ('ttype', '!=', 'binary')
    ]
    if level == 0:
        if rule.result_type == 'norel':
            fields_dict.append(('ttype', 'not in', ['many2one', 'one2many', 'many2many']))
        elif rule.result_type == 'notree':
            fields_dict.append(('ttype', 'not in', ['one2many', 'many2many']))
        elif rule.result_type == 'custom':
            fields_dict.append(('id', 'in', rule.custom_field_ids.ids))
    else:
        fields_dict.append(('ttype', 'not in', ['one2many', 'many2many']))
    fields_search = self.env['ir.model.fields'].sudo().search(fields_dict)
    for field in fields_search:
        value = getattr(item, field.name, False)
        if field.ttype == 'many2one':
            if not value:
                dict_value[field.name] = {
                    'id': False,
                    'name': ""
                }
            else:
                rec_name = value._rec_name if value._rec_name else "name"
                sub_value = getattr(value, rec_name, False) if value else ""
                dict_value[field.name] = {
                    'id': value.id if value else False,
                    'name': sub_value if isinstance(sub_value, str) else ""
                }
            if field.relation == 'ir.attachment':
                dict_value[field.name]['url'] = getattr(value, 'url', False) if value else ""
        elif field.ttype in ['one2many', 'many2many']:
            if level == 2:
                continue
            key_map = "map_{}".format(field.name)
            dict_value[key_map] = {}
            dict_value[field.name] = []
            for subitem in getattr(item, field.name, []):
                dict_value[key_map][str(subitem.id)] = True
                dict_value[field.name].append(get_item_dict(self, rule, subitem, level + 1))
        elif field.ttype in ['date']:
            if not value:
                dict_value[field.name] = False
            else:
                dict_value[field.name] = str(value)
        elif field.ttype in ['datetime']:
            if not value:
                dict_value[field.name] = False
            elif self.env.context.get('force_string', False):
                dict_value[field.name] = str(value)
            else:
                dict_value[field.name] = value
        elif field.ttype in ['boolean']:
            dict_value[field.name] = bool(value)
        elif field.ttype in ['integer']:
            dict_value[field.name] = int(value)
        elif field.ttype in ['monetary', 'float']:
            dict_value[field.name] = float(value)
        else:
            dict_value[field.name] = str(value) if value else ""

        if field.id in rule.search_field_ids.ids:
            words = str(value).lower().split(' ')
            full_words = ""
            for word in words:
                part = ""
                for letter in word:
                    part += unaccent_string(letter)
                    full_words += unaccent_string(letter)
                    dict_value['search_terms'].append(part)
                    dict_value['search_terms'].append(full_words)
                dict_value['search_terms'].append(unaccent_string(word))
                full_words += " "
            dict_value['search_terms'].append(unaccent_string(str(value)))
    return dict_value


def logic_write(self, ids, account=False, rule=False, delete=False, operation=False):
    try:
        logging.info("logic_write 96")
        if not rule:
            logging.info("logic_write 99")
            rules = self.env['firebase.rule'].sudo().search([
                ('model_name', '=', self._name),
            ])
            logging.info("logic_write 103")
        else:
            logging.info("logic_write 105")
            rules = [rule]
        for rule in rules:
            logging.info("logic_write 108")
            for item in self:
                logging.info("logic_write 110")
                dict_search = rule._get_eval_domain()
                logging.info("logic_write 112")
                dict_search.append(('id', '=', item.id))
                logging.info("logic_write 114")
                count_record = self.sudo().search_count(dict_search)
                logging.info("logic_write 116")
                if count_record <= 0:
                    continue
                logging.info("logic_write 119")
                dict_value = get_item_dict(self, rule, item)
                logging.info("logic_write 121")
                if not account:
                    logging.info("logic_write 123")
                    if delete:
                        logging.info("logic_write 125")
                        rule.account_id.delete_firebase_object(rule.path_firebase, item.id)
                    else:
                        logging.info("logic_write 128")
                        rule.account_id.update_firebase_object(rule.path_firebase, dict_value)
                else:
                    logging.info("logic_write 131")
                    if delete:
                        logging.info("logic_write 133")
                        account.delete_firebase_object(rule.path_firebase, item.id)
                        logging.info("logic_write 135")
                    else:
                        logging.info("logic_write 136")
                        account.update_firebase_object(rule.path_firebase, dict_value)
                        logging.info("logic_write 138")
    except Exception as e:
        raise exceptions.Warning(e)




class OdooFirebaseConfigLine(models.Model):
    _name = 'firebase.rule'
    _description = "Firebase: Rule of Sync"

    active = fields.Boolean(
        string="Active",
        default=True
    )
    model_id = fields.Many2one(
        comodel_name='ir.model',
        string='Model',
        required=True,
        ondelete="cascade"
    )
    model_name = fields.Char(
        string="Model Name",
        related="model_id.model",
    )
    path_firebase = fields.Char(
        string='Firebase Path',
        help='Path of firebase database where you want to write odoo info.',
        required=True
    )
    allow_create = fields.Boolean(
        string='Allow Create',
        default=True
    )
    allow_update = fields.Boolean(
        string='Allow Write',
        default=True
    )
    allow_delete = fields.Boolean(
        string='Allow Delete',
        default=True
    )
    account_id = fields.Many2one(
        comodel_name='firebase.account',
        string='Account'
    )
    domain = fields.Text(
        string="Domain",
        default='[]',
        required=False
    )
    result_type = fields.Selection(
        string="Fields to Migrate",
        selection=[
            ('method', 'Method'),
            ('norel', 'All fields except relational fields'),
            ('notree', 'All fields except relational multi-level (One2many and Many2many)'),
            ('custom', 'Add fields that you want to send'),
            ('all', 'All Fields')
        ], default="all"
    )
    method = fields.Char(
        string="Custom Method"
    )
    custom_field_ids = fields.Many2many(
        string="Custom Fields",
        comodel_name="ir.model.fields",
        domain="[('model_id','=',model_id)]"
    )
    search_field_ids = fields.Many2many(
        string="Search Fields",
        comodel_name="ir.model.fields",
        domain="[('model_id','=',model_id),('ttype','in',['char','html','selection','text'])]",
        relation="firebase_rule_search_field_ids",
        column1="rule_id", column2="field_id"
    )
    create_automation_id = fields.Many2one(
        string="Automation rule when create",
        comodel_name="base.automation"
    )
    write_automation_id = fields.Many2one(
        string="Automation rule when write",
        comodel_name="base.automation"
    )
    unlink_automation_id = fields.Many2one(
        string="Automation rule when unlink",
        comodel_name="base.automation"
    )

    def _get_eval_domain(self):
        self.ensure_one()
        return safe_eval.safe_eval(self.domain, {
            'datetime': safe_eval.datetime,
            'dateutil': safe_eval.dateutil,
            'time': safe_eval.time,
            'uid': self.env.uid,
            'user': self.env.user,
        })

    def force_sync(self):
        try:
            self.ensure_one()
            if not self.model_id:
                raise exceptions.UserError("Es obligatorio tener un modelo")
            items = self.env[self.model_id.model].sudo().search(self._get_eval_domain(), order="id DESC")
            if not items:
                raise exceptions.UserError("No hay items que migrar")
            logic_write(items, items.ids, account=self.account_id, rule=self, operation="write")
        except Exception as e:
            raise exceptions.Warning(e)


    def _get_data_base_automation(self, operation):
        return {
            'name': "Firebase {} {}".format(self.model_name, operation),
            'active': True,
            'trigger': operation,
            'model_id': self.model_id.id,
            'filter_domain': self.domain,
            'state': 'code',
            'code': "env['firebase.rule'].browse({}).base_automation_trigger('{}', records, '{}')".format(
                str(self.id),
                self.model_name,
                operation
            )
        }

    def _create_base_automation(self, operation):
        self.ensure_one()
        BaseAutoObj = self.env['base.automation'].sudo()
        data = self._get_data_base_automation(operation)
        return BaseAutoObj.create(data)

    def check_automation(self):
        self.ensure_one()
        need_create = True
        if self.allow_create:
            if self.create_automation_id and self.create_automation_id.active:
                need_create = False
        else:
            need_create = False
        if need_create:
            self.create_automation_id = self._create_base_automation('on_create')
        else:
            self.create_automation_id.write(self._get_data_base_automation('on_create'))

        need_write = True
        if self.allow_update:
            if self.write_automation_id and self.write_automation_id.active:
                need_write = False
        else:
            need_write = False
        if need_write:
            self.write_automation_id = self._create_base_automation('on_write')
        else:
            self.write_automation_id.write(self._get_data_base_automation('on_write'))

        need_unlink = True
        if self.allow_delete:
            if self.unlink_automation_id and self.unlink_automation_id.active:
                need_unlink = False
        else:
            need_unlink = False

        if need_unlink:
            self.unlink_automation_id = self._create_base_automation('on_unlink')
        else:
            self.unlink_automation_id.write(self._get_data_base_automation('on_unlink'))

    def _execute_firebase_sync(self, records, delete=False, operation=False):
        self.ensure_one()
        rule = self.sudo()
        account = rule.account_id.sudo()
        for item in records:
            dict_value = get_item_dict(self.sudo(), rule, item)
            if delete:
                if 'id' in dict_value:
                    account.delete_firebase_object(rule.path_firebase, dict_value['id'])
                else:
                    account.delete_firebase_object(rule.path_firebase, item.id)
            else:
                account.update_firebase_object(rule.path_firebase, dict_value)

    def base_automation_trigger(self, model, records, operation):
        self.ensure_one()
        if not self.active:
            return False
        if operation == 'on_create':
            self._execute_firebase_sync(records, operation='create')
        if operation == 'on_write':
            self._execute_firebase_sync(records, operation='write')
        if operation == 'on_unlink':
            self._execute_firebase_sync(records, delete=True, operation='unlink')

    @api.model
    def create(self, vals_list):
        res = super(OdooFirebaseConfigLine, self).create(vals_list)
        res.check_automation()
        return res

    def write(self, vals):
        res = super(OdooFirebaseConfigLine, self).write(vals)
        for item in self:
            item.check_automation()
        return res
