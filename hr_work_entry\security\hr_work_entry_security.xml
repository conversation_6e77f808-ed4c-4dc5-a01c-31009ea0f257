<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

        <record id="hr_user_work_entry_employee" model="ir.rule">
            <field name="name">Work entries/Employee calendar filter: only self</field>
            <field name="model_id" ref="model_hr_user_work_entry_employee"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="perm_read" eval="0"/>
        </record>

</odoo>
