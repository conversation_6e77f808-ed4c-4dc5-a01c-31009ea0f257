# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mass_mailing
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-18 13:40+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_BE/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid ""
"% <br/>\n"
"                                            <strong>Clicks</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid ""
"%<br/>\n"
"                                            <strong>Replied</strong>"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:632
#, python-format
msgid "%s (copy)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "&amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"&amp;nbsp;\n"
"                                                            <span class=\"fa "
"fa-10x fa-music\"/>\n"
"                                                            &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"&amp;nbsp;\n"
"                                            <span class=\"fa fa-10x fa-music"
"\"/>\n"
"                                            &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"&amp;nbsp;\n"
"                                            <span class=\"fa fa-check fa-3x"
"\"/>\n"
"                                            &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"&amp;nbsp;\n"
"                                            <span class=\"fa fa-truck fa-3x"
"\"/>\n"
"                                            &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"&amp;nbsp;\n"
"                                            <span class=\"fa fa-usd fa-3x\"/"
">\n"
"                                            &amp;nbsp;"
msgstr ""

#. module: mass_mailing
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_1
#: model_terms:mail.mass_mailing.list,popup_content:mass_mailing.mass_mail_list_2
msgid ""
"<div class=\"modal-header text-center\">\n"
"    <h3 class=\"modal-title mt8\">Odoo Presents</h3>\n"
"</div>\n"
"<div class=\"o_popup_message\">\n"
"    <font>7</font>\n"
"    <strong>Business Hacks</strong>\n"
"    <span> to<br/>boost your marketing</span>\n"
"</div>\n"
"<p class=\"o_message_paragraph\">Join our Marketing newsletter and get "
"<strong>this white paper instantly</strong></p>"
msgstr ""

#. module: mass_mailing
#: model:mail.template,body_html:mass_mailing.newsletter_template
msgid ""
"<div class=\"snippet_row bg-color\" style=\"padding:0px;width:600px;margin:"
"auto;background: #ffffff repeat top /100%\">\n"
"                <table cellpadding=\"0\" cellspacing=\"0\" style=\"border-"
"collapse:collapse\" width=\"100%\"><tbody><tr><td style=\"padding:10px 10px "
"10px 5px\" valign=\"center\" width=\"270\"> <a style=\"text-decoration:none"
"\" href=\"http://www.example.com\"><strong>My Company</strong></a> </td><td "
"style=\"padding:10px 15px 10px 10px;vertical-align:middle\" valign=\"center"
"\" width=\"270\"><table align=\"right\" border=\"0\" cellpadding=\"0\" "
"cellspacing=\"0\" style=\"border-collapse:collapse\"><tbody><tr><td align="
"\"right\"><a href=\"https://www.facebook.com/Odoo\"><span class=\"fa fa-"
"facebook-square fa-2x\" style=\"color:#44B7B7;\"></span></a></td><td align="
"\"right\" style=\"padding-left:5px\"><a href=\"https://www.facebook.com/Odoo"
"\"><span class=\"fa fa-google-plus-square fa-2x\" style=\"color:#44B7B7;\"></"
"span></a></td><td align=\"right\" style=\"padding-left:5px\"><a href="
"\"https://www.facebook.com/Odoo\"><span class=\"fa fa-linkedin-square fa-2x"
"\" style=\"color:#44B7B7;\"></span></a></td><td align=\"right\" style="
"\"padding-left:5px\"><a href=\"https://www.facebook.com/Odoo\"><span class="
"\"fa fa-twitter-square fa-2x\" style=\"color:#44B7B7;\"></span></a></td></"
"tr></tbody></table></td></tr></tbody></table></div><div style=\"padding:0px;"
"width:600px;margin:auto;background-color:#44B7B7;color:#168484\"><table "
"cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%;background-color:"
"inherit;border-collapse:collapse;color:inherit\"><tbody><tr><td class="
"\"col_mv bg-color\" style=\"text-align:left; padding-left:20px;vertical-"
"align:middle;font-size:14px\"><p style=\"margin:0;font-size:20px;color:#fff"
"\">You get a 20% discount for signing up!</p><p style=\"margin:0\">Discount "
"Code: <strong>45A9E77DGW8455</strong></p></td><td class=\"col_mv bg-color\" "
"style=\"padding:20px;text-align:center;vertical-align:middle\">  <span class="
"\"fa fa-6x fa-ticket\"></span>  </td></tr></tbody></table></div><div class="
"\"snippet_row bg-color\" style=\"padding:0;width:600px;max-width:600px;"
"margin:0 auto;background: #fff repeat top /100%;color:#168484\"><table style="
"\"width:100%;text-align:justify;margin:0 auto;inherit;border-collapse:"
"collapse;color:inherit\"><tbody><tr><td style=\"padding:10px 30px;font-"
"size:14px;line-height:20px\"><p style=\"margin:0\">Dear ${object.name}</p><p "
"style=\"margin:0\">Great stories have personality. Consider telling a great "
"story that provides personality. Writing a story with personality for "
"potential clients will assist with making a relationship connection. This "
"shows up in small quirks like word choices or phrases. Write from your point "
"of view, not from someone else's experience.<br></p><p style="
"\"margin:0\">Great stories are for everyone even when only written for just "
"one person. If you try to write with a wide general audience in mind, your "
"story will ring false and be bland. No one will be interested. Write for one "
"person. If it’s genuine for the one, it’s genuine for the rest.</p><p style="
"\"margin:0\">Kind Regards,</p><p style=\"margin:0\">Michael Fletcher</p></"
"td></tr></tbody></table></div><div style=\"padding:0;width:600px;max-"
"width:600px;margin:0 auto\"><table style=\"width:100%;text-align:justify;"
"margin:0 auto;background-color:inherit;border-collapse:collapse"
"\"><tbody><tr><td class=\"col_mv bg-color\" style=\"padding:10px 0;"
"background-color:#168484;color:#fff\"><table style=\"border-collapse:"
"collapse;background-color:inherit\"><tbody><tr><td rowspan=\"2\" style="
"\"padding-left:10px\">  <span class=\"fa fa-check fa-3x\"></span>  </td><td "
"style=\"padding-left:10px;color:#fff;font-size:14px\"><p style=\"font-weight:"
"bold;font-size:18px;margin:0 0 3px 0\">Step 1:</p><p style="
"\"margin:0\">Place Order</p></td></tr></tbody></table></td><td class="
"\"col_mv bg-color\" style=\"padding:10px 0;background-color:#FFFFFF;color:"
"#808080\"><table style=\"border-collapse:collapse;background-color:inherit"
"\"><tbody><tr><td rowspan=\"2\" style=\"padding-left:10px\">  <span class="
"\"fa fa-truck fa-3x\"></span>  </td><td style=\"padding-left:10px;color:"
"#808080;font-size:14px\"><p style=\"font-weight:bold;font-size:18px;margin:0 "
"0 3px 0\">Step 2:</p><p style=\"margin:0\">Shipping</p></td></tr></tbody></"
"table></td><td class=\"col_mv bg-color\" style=\"padding:10px 0;background-"
"color:#FFFFFF;color:#808080\"><table style=\"border-collapse:collapse;"
"background-color:inherit\"><tbody><tr><td rowspan=\"2\" style=\"padding-"
"left:10px\">  <span class=\"fa fa-dollar fa-3x\"></span>  </td><td style="
"\"padding-left:10px;color:#808080;font-size:14px\"><p style=\"font-weight:"
"bold;font-size:18px;margin:0 0 3px 0\">Step 3:</p><p style="
"\"margin:0\">Payment</p></td></tr></tbody></table></td></tr></tbody></"
"table></div><div class=\"snippet_row bg-color\" style=\"padding:0px;"
"width:600px;margin:auto;background: #414141 repeat top /100%;color:"
"#44B7B7\"><div style=\"padding:10px\"><table cellpadding=\"0\" cellspacing="
"\"0\" style=\"margin: 0 auto;width:100%;border-collapse:collapse;color:"
"inherit;color:inherit\"><tbody><tr><td style=\"text-align:center\"> <a style="
"\"text-decoration: none;\" href=\"https://www.facebook.com/Odoo\"><span "
"class=\"fa fa-facebook-square fa-2x\"></span></a><a style=\"text-decoration: "
"none;\" href=\"https://plus.google.com/+Odooapps\"><span class=\"fa fa-"
"google-plus-square fa-2x\"></span></a><a style=\"text-decoration: none;\" "
"href=\"https://www.linkedin.com/company/odoo\"><span class=\"fa fa-linkedin-"
"square fa-2x\"></span></a><a style=\"text-decoration: none;\" href=\"https://"
"twitter.com/Odoo\"><span class=\"fa fa-twitter-square fa-2x\"></span></a> </"
"td></tr><tr><td style=\"font-size:12px;text-align:center;padding-top:10px;"
"padding-bottom:5px\"> <a href=\"/unsubscribe_from_list\" style=\"color:"
"#44B7B7\">Unsubscribe</a>|<a href=\"/page/contactus\" style=\"color:"
"#44B7B7\">Contact</a> </td></tr></tbody></table></div>\n"
"            </div>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"<span class=\"fa-stack\">\n"
"                        <i class=\"fa fa-align-justify fa-stack-1x\" style="
"\" margin-top: 0.7em; height: 0.9em; display: inline-block; overflow: hidden;"
"\"/>\n"
"                        <i class=\"fa fa-arrow-down fa-stack-1x\" style="
"\"display: inline-block; font-size: 0.6em; margin-top: -0.15em;\"/>\n"
"                    </span> Footers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"<span class=\"fa-stack\">\n"
"                        <i class=\"fa fa-align-justify fa-stack-1x\" style="
"\"margin-top: -0.15em; height: 0.9em; display: inline-block; overflow: "
"hidden;\"/>\n"
"                        <i class=\"fa fa-arrow-up fa-stack-1x\" style="
"\"margin-top: 0.25em; display: inline-block; font-size: 0.6em;\"/>\n"
"                    </span> Headers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"<span class=\"fa-stack\">\n"
"                        <i class=\"fa fa-align-left fa-stack-1x\" style="
"\"display: inline-block; height: 0.93em; margin-top: 0.3em; overflow: hidden;"
"\"/>\n"
"                    </span> Extra"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"<span class=\"fa-stack\">\n"
"                        <i class=\"fa fa-align-left fa-stack-1x\"/>\n"
"                    </span> Body"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
msgid ""
"<span id=\"choose_template\" class=\"o_not_editable o_css_editor\">\n"
"                <span class=\"btn btn-primary\">Theme</span>\n"
"                <span class=\"btn btn-primary hidden\">Cancel selection</"
"span>\n"
"            </span>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "<strong>My Company</strong>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid "<strong>Opened</strong>"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_ab_pc
msgid "A/B Testing percentage"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_unique_ab_testing
msgid "AB Testing"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_active
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_active
msgid "Active"
msgstr "Actif"

#. module: mass_mailing
#: selection:mass.mailing.config.settings,group_website_popup_on_exit:0
msgid ""
"Allow the use of a pop-up snippet on website to encourage visitors to sign "
"up on a mass mailing list"
msgstr ""

#. module: mass_mailing
#: selection:mass.mailing.config.settings,group_mass_mailing_campaign:0
msgid "Allow using marketing campaigns (advanced)"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_account_analytic_account
msgid "Analytic Account"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mass_mailing_configuration
msgid "Apply"
msgstr "Applique"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Apps That Help You Grow Your Business"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Archived"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Attach a file"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_attachment_ids
msgid "Attachments"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_event_registration
msgid "Attendee"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mail_theme_list
msgid "Basic Theme"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_body_html
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_template_form_minimal
msgid "Body"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_message_bounce
msgid "Bounce"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_bounced
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_bounced
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: selection:mail.mail.statistics,state:0
msgid "Bounced"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_bounced_ratio
msgid "Bounced Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_id_3605
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_name
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Campaign Name"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_view_mass_mailing_stages
msgid "Campaign Stages"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_group_mass_mailing_campaign
#: model:ir.ui.menu,name:mass_mailing.menu_email_campaigns
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Campaigns"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mass_mailing_configuration
msgid "Cancel"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Check this out!"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
msgid "Choose a Template"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "Choose a background image"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid "Click here to create a new mailing list."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid "Click here to create a new mailing."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_contacts_from_list
msgid "Click to create a recipient."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_campaigns
msgid "Click to define a new mass mailing campaign."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_color
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_color
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_color
msgid "Color Index"
msgstr "Index de la couleur"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Come check out my stuff!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_test_email_to
msgid "Comma-separated list of email addresses."
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_configuration
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_configuration
msgid "Configuration"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mass_mailing_configuration
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mass_mailing_configuration
msgid "Configure Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Contact"
msgstr "Contact"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
msgid "Contact List"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Contact Us"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_contacts
msgid "Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact_message_bounce
msgid "Counter of the number of bounced emails for this contact."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_create_date
msgid "Create Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_campaigns
msgid ""
"Create a campaign to structure mass mailing and get analysis from email "
"status."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_create_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_create_date
msgid "Created on"
msgstr "Créé le"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_create_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_create_date
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Creation Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
msgid "Creation Month"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_exception
msgid "Date of technical error leading to the email not being sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_scheduled
msgid "Date when the email has been created"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_opened
msgid "Date when the email has been opened the first time"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_sent
msgid "Date when the email has been sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_replied
msgid "Date when this email has been replied for the first time."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_bounced
msgid "Date when this email has bounced."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Dear ${object.name}"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Dear Bob,"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"Dear Bob,<br/>\n"
"                                                We would like you to know "
"that your order has shipped! To track your order or make any changes please "
"click the \"my order\" button below."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_email_template_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delete"
msgstr "Supprimer"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_delivered
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_delivered
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
msgid "Delivered"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_hr_department
msgid "Department"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_2
msgid "Design"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Discount Code: <strong>45A9E77DGW8455</strong>"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_channel
msgid "Discussion channel"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_display_name
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_display_name
msgid "Display Name"
msgstr ""

#. module: mass_mailing
#: selection:mass.mailing.config.settings,group_website_popup_on_exit:0
msgid ""
"Do not add extra content on website pages to encourage visitors to sign up"
msgstr ""

#. module: mass_mailing
#: selection:mass.mailing.config.settings,group_mass_mailing_campaign:0
msgid "Do not organize and schedule mail campaigns (easy)"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_res_id
msgid "Document ID"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_model
msgid "Document model"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_mailing_domain
msgid "Domain"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:640
#: selection:mail.mass_mailing,state:0 selection:mail.statistics.report,state:0
#, python-format
msgid "Draft"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Duplicate"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_email_template_kanban
msgid "Edit"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_email
msgid "Email"
msgstr "Email"

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail_statistics
msgid "Email Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Emails Sent"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_statistics_ids
msgid "Emails Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_hr_employee
msgid "Employee"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_event_event
msgid "Event"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_exception
#: selection:mail.mail.statistics,state:0
msgid "Exception"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Exclude Dead"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
msgid "Exclude Opt Out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Extended Filters..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Facebook"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_failed
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_failed
msgid "Failed"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,name:mass_mailing.mass_mail_1
#: model:utm.source,name:mass_mailing.mass_mail_1_utm_source
msgid "First Newsletter"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,reply_to_mode:0
msgid "Followers of leads/applicants"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_email_from
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_email_from
msgid "From"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_gamification_badge
msgid "Gamification badge"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_gamification_challenge
msgid "Gamification challenge"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid ""
"Great stories are for everyone even when only written for just one person. "
"If you try to write with a wide general audience in mind, your story will "
"ring false and be bland. No one will be interested. Write for one person. If "
"it’s genuine for the one, it’s genuine for the rest."
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid ""
"Great stories have personality. Consider telling a great story that provides "
"personality. Writing a story with personality for potential clients will "
"assist with making a relationship connection. This shows up in small quirks "
"like word choices or phrases. Write from your point of view, not from "
"someone else's experience.<br>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"Great stories have personality. Consider telling a great story that provides "
"personality. Writing a story with personality for potential clients will "
"assists with making a relationship connection. This shows up in small quirks "
"like word choices or phrases. Write from your point of view, not from "
"someone else's experience.<br/>"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Group By"
msgstr "Grouper par"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Group By..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
msgid ""
"Here you can design your own template from scratch by selecting your "
"favorite theme."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_id
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_id
msgid "ID"
msgstr "ID"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_mail_mail_id_int
msgid ""
"ID of the related mail_mail. This field is an integer field because the "
"related mail_mail can be deleted separately from its statistics. However the "
"ID is needed for several action and controllers."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign_unique_ab_testing
msgid ""
"If checked, recipients will be mailed only once, allowing to send various "
"mailings in a single campaign to test the effectiveness of the mailings."
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:640
#: selection:mail.mass_mailing,state:0
#, python-format
msgid "In Queue"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_fleet_vehicle
msgid "Information on a vehicle"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_hr_job
msgid "Job Position"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_keep_archives
msgid "Keep Archives"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Kind Regards,"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report___last_update
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Last State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_write_uid
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_write_uid
msgid "Last Updated by"
msgstr "Derniere fois mis à jour par"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_write_date
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_write_date
msgid "Last Updated on"
msgstr "Dernière mis à jour le"

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mail_statistics_state_update
msgid "Last state update of the mail"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_links_click_ids
msgid "Links click"
msgstr ""

#. module: mass_mailing
#: model:ir.filters,name:mass_mailing.filter_contact_subscription
msgid "List Subscription"
msgstr ""

#. module: mass_mailing
#: model:ir.filters,name:mass_mailing.filter_contact_unsubscription
msgid "List Unsubscription"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_mail_mail_id
msgid "Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mail Body"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_mail_mail_id_int
msgid "Mail ID (tech)"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics
#: model:ir.actions.act_window,name:mass_mailing.action_view_mail_mail_statistics_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click_mail_stat_id
#: model:ir.ui.menu,name:mass_mailing.menu_email_statistics
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_tree
msgid "Mail Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_email_template
msgid "Mail Templates"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_mass_mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Mailing"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:431
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message_mailing_list_ids
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_list_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_name
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message_mailing_list_ids
#, python-format
msgid "Mailing List"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts
msgid "Mailing List Subscribers"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_lists
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_list_ids
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailing_lists
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_tree
msgid "Mailing Lists"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_graph
msgid "Mailing Lists Subscriber"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_contact_tree
msgid "Mailing Lists Subscribers"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_links_tree
msgid "Mailing Statistics of Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_mass_mailing_test
msgid "Mailing Test"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailing_lists
msgid ""
"Mailing lists allows you to to manage customers and\n"
"                    contacts easily and to send to mailings in a single "
"click."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_total_mailings
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Mailings"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_campaign
msgid "Manage Mass Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mass_mailing_config_settings_group_mass_mailing_campaign
msgid "Manage mass mailign using Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_name
msgid "Mass Mail"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_campaign
msgid "Mass Mail Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message_mass_mailing_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message_mass_mailing_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message_mass_mailing_name
#: model:ir.ui.menu,name:mass_mailing.mass_mailing_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mass_mailing_configuration
msgid "Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_mail_statistics_report
msgid "Mass Mailing Analysis"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_click_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_link_tracker_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_compose_message_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_mass_mailing_campaign_id
#: model:ir.model.fields,field_description:mass_mailing.field_survey_mail_compose_message_mass_mailing_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Mass Mailing Campaign"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_stage
msgid "Mass Mailing Campaign Stage"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_campaigns
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_tree
msgid "Mass Mailing Campaigns"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_contact
msgid "Mass Mailing Contact"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_stages
msgid "Mass Mailing Stages"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_statistics_report
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_graph
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_pivot
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Mass Mailing Statistics"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_mail_statistics_report
msgid ""
"Mass Mailing Statistics allows you to check different mailing related "
"information like number of bounced mails, opened mails, replied mails. You "
"can sort out your analysis by different groups to get accurate grained "
"analysis."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_tag
msgid "Mass Mailing Tag"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_create_mass_mailings_from_campaign
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailings_from_campaign
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_mass_mailing_ids
#: model:ir.ui.menu,name:mass_mailing.menu_email_mass_mailings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_stage_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_stage_tree
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_tree
msgid "Mass Mailings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_ab_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_create_mass_mailings_from_campaign
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings
#: model_terms:ir.actions.act_window,help:mass_mailing.action_view_mass_mailings_from_campaign
msgid ""
"Mass mailing allows you to to easily design and send mass mailings to your "
"contacts, customers or leads using mailing lists."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_module_mass_mailing_themes
msgid "Mass mailing themes"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_medium_id
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_medium_id
msgid "Medium"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_message_id
msgid "Message-ID"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Michael Fletcher"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "My Account"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_name
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_tag_name
msgid "Name"
msgstr "Nom"

#. module: mass_mailing
#: model:mail.mass_mailing.campaign,name:mass_mailing.mass_mail_campaign_1
#: model:mail.template,subject:mass_mailing.newsletter_template
#: model:utm.campaign,name:mass_mailing.mass_mail_campaign_1_utm_campaign
msgid "Newsletter"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_next_departure
msgid "Next Departure"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.snippet_options
msgid "No background image"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_clicks_ratio
msgid "Number of Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_contact_nbr
msgid "Number of Contacts"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_clicks_ratio
msgid "Number of clicks"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Open Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_opened
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_opened
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: selection:mail.mail.statistics,state:0
msgid "Opened"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_opened_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_opened_ratio
msgid "Opened Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_opt_out
msgid "Opt Out"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Options"
msgstr "Options"

#. module: mass_mailing
#: selection:mail.mail.statistics,state:0
msgid "Outgoing"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_res_partner
msgid "Partner"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Payment"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact_ab_pc
msgid ""
"Percentage of the contacts that will be mailed. Recipients will be taken "
"randomly."
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Place Order"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:764
#, python-format
msgid "Please select recipients."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
msgid "Popup Content"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_reply_to
msgid "Preferred Reply-To Address"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_template_form_minimal
msgid "Preview"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Received"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_received_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_received_ratio
msgid "Received Ratio"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_recipient
msgid "Recipient"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_contacts_from_list
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_test_email_to
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
msgid "Recipients"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_mailing_model
msgid "Recipients Model"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Related Mailing(s)"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Related Mailings"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_replied
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_replied
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_kanban
#: selection:mail.mail.statistics,state:0
msgid "Replied"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_replied_ratio
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_replied_ratio
msgid "Replied Ratio"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
msgid "Reply Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_reply_to
msgid "Reply To"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_reply_to_mode
msgid "Reply-To Mode"
msgstr ""

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_report
msgid "Reports"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_user_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Responsible"
msgstr "Responsable"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Retry"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mail_mass_mailing_test
msgid "Sample Mail Wizard"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_1
msgid "Schedule"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_schedule_date
msgid "Schedule in the Future"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_scheduled
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_scheduled
msgid "Scheduled"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Scheduled Month"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,name:mass_mailing.mass_mail_2
#: model:utm.source,name:mass_mailing.mass_mail_2_utm_source
msgid "Second Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
#: model_terms:ir.ui.view,arch_db:mass_mailing.mass_mail_theme_list
msgid "Select"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
msgid "Select a theme"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists..."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select mailing lists:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Select recipients"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send Sample Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid "Send a Sample Mail"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_test_form
msgid ""
"Send a sample of this mailing to the above of email addresses for test "
"purpose."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Send new A/B Testing Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Send new Mass Mailing"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Send to All"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:640
#: selection:mail.mass_mailing,state:0
#, python-format
msgid "Sending"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:640
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_sent
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_sent
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_sent
#: selection:mail.mail.statistics,state:0 selection:mail.mass_mailing,state:0
#: model:mail.mass_mailing.stage,name:mass_mailing.campaign_stage_3
#: selection:mail.statistics.report,state:0
#, python-format
msgid "Sent"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "Sent By"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_sent_date
msgid "Sent Date"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_sent
msgid "Sent Emails"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Sent Month"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_stage_sequence
msgid "Sequence"
msgstr "Séquence"

#. module: mass_mailing
#: model:ir.ui.menu,name:mass_mailing.menu_mass_mailing_global_settings
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_kanban
msgid "Settings"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Shipping"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_source_id
msgid "Source"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_name
msgid "Source Name"
msgstr ""

#. module: mass_mailing
#: selection:mail.mass_mailing,reply_to_mode:0
msgid "Specified Email Address"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_stage_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_search
msgid "Stage"
msgstr "Etape"

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_state
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mail_statistics_search
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_statistics_report_search
msgid "State"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_state_update
msgid "State Update"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mail_statistics_ids
msgid "Statistics"
msgstr ""

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_view_mass_mailing_links_statistics
#: model:ir.actions.act_window,name:mass_mailing.dropdb snipp
msgid "Statistics of Clicks"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_state
#: model:ir.model.fields,field_description:mass_mailing.field_mail_statistics_report_state
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_search
msgid "Status"
msgstr "Statut"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Step 1"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Step 1:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Step 2"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Step 2:"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Step 3"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Step 3:"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_source_id
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "Subject"
msgstr "Sujet"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Support"
msgstr ""

#. module: mass_mailing
#: sql_constraint:mail.mass_mailing.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_tag_ids
msgid "Tags"
msgstr "Tags"

#. module: mass_mailing
#: model:ir.actions.act_window,name:mass_mailing.action_email_template_marketing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_template_form_minimal
msgid "Templates"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/models/mass_mailing.py:716
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
#, python-format
msgid "Test Mailing"
msgstr ""

#. module: mass_mailing
#: selection:mail.statistics.report,state:0
msgid "Tested"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_contact_opt_out
msgid "The contact has chosen not to receive mails anymore from this list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign_medium_id
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_medium_id
msgid "This is the delivery method, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain, or name of "
"email list"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign_source_id
msgid ""
"This is the link source, e.g. Search Engine, another domain,or name of email "
"list"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "This mass mailing is scheduled to"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign_campaign_id
#: model:ir.model.fields,help:mass_mailing.field_mail_mass_mailing_campaign_id_3605
msgid ""
"This name helps you tracking your different campaign efforts, e.g. "
"Fall_Drive, Christmas_Special"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid ""
"This will send the email to all recipients. Do you still want to proceed ?"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_total
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_total
msgid "Total"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_campaign_form
msgid "Tracking"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Twitter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "Unsubscribe"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_contact_unsubscription_date
msgid "Unsubscription Date"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_website_popup_on_exit
msgid "Use subscription pop up on the website"
msgstr ""

#. module: mass_mailing
#: model:res.groups,name:mass_mailing.group_mass_mailing_user
msgid "User"
msgstr "Utilisateur"

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"We need you to update your account information. If there is ever a problem "
"with your account, this information will make it easier for you to log back "
"in."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid ""
"We would like you to know that your order has shipped! To track your order "
"or make any changes please click the \"my order\" button below."
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mass_mailing_config_settings_group_website_popup_on_exit
msgid "Website Pop-up"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_popup_content
msgid "Website Popup Content"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_list_popup_redirect_url
msgid "Website Popup Redirect URL"
msgstr ""

#. module: mass_mailing
#: model:mail.mass_mailing,body_html:mass_mailing.mass_mail_1
msgid "You get a 20% discount for signing up!"
msgstr ""

#. module: mass_mailing
#: code:addons/mass_mailing/controllers/main.py:25
#, python-format
msgid "You have been unsubscribed successfully"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Your Logo"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Your Order"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "Your order has shipped!"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_campaign_campaign_id
msgid "campaign_id"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_list_form
msgid "e.g. Consumer Newsletter"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "emails are in queue and will be sent soon."
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.view_mail_mass_mailing_form
msgid "emails could not be sent."
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker
msgid "link.tracker"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_link_tracker_click
msgid "link.tracker.click"
msgstr ""

#. module: mass_mailing
#: model:ir.model,name:mass_mailing.model_mass_mailing_config_settings
msgid "mass.mailing.config.settings"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "social icon"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.FieldTextHtmlInline
msgid "template"
msgstr ""

#. module: mass_mailing
#: model:ir.model.fields,field_description:mass_mailing.field_mail_mass_mailing_bounced_ratio
msgid "unknown"
msgstr ""

#. module: mass_mailing
#: model_terms:ir.ui.view,arch_db:mass_mailing.email_designer_snippets
msgid "© 2014 All Rights Reserved"
msgstr ""

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Date du dernier message posté sur l'enregistrement."

#~ msgid "Followers"
#~ msgstr "Abonnés"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si coché, les nouveaux messages requierent votre attention. "

#~ msgid "Last Message Date"
#~ msgstr "Date du dernier message"

#~ msgid "Messages"
#~ msgstr "Messages"

#~ msgid "Messages and communication history"
#~ msgstr "Messages et historique des communications"

#~ msgid "Unread Messages"
#~ msgstr "Messages non lus"
