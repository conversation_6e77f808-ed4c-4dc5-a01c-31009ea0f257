# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_iban
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-08-24 09:16+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankareikningar"

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:67
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:54
#, python-format
msgid "IBAN"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:39
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:35
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:31
#, python-format
msgid "There is no IBAN code."
msgstr ""

#. module: base_iban
#: code:addons/base_iban/models/res_partner_bank.py:45
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr ""
