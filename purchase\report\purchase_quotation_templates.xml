<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="report_purchasequotation_document">
    <t t-call="web.external_layout">
        <t t-set="o" t-value="o.with_context(lang=o.partner_id.lang)"/>
        <t t-set="forced_vat" t-value="o.fiscal_position_id.foreign_vat"/> <!-- So that it appears in the footer of the report instead of the company VAT if it's set -->
        <t t-set="address">
            <div t-field="o.partner_id"
            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
            <p t-if="o.partner_id.vat"><t t-esc="o.company_id.account_fiscal_country_id.vat_label or 'Tax ID'"/>: <span t-field="o.partner_id.vat"/></p>
        </t>
        <t t-if="o.dest_address_id">
            <t t-set="information_block">
                <strong>Shipping address:</strong>
                <div t-field="o.dest_address_id"
                    t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}' name="purchase_shipping_address"/>
            </t>
        </t>
        <div class="page">
            <div class="oe_structure"/>

            <h2>Request for Quotation <span t-field="o.name"/></h2>

            <table class="table table-sm">
                <thead style="display: table-row-group">
                    <tr>
                        <th name="th_description"><strong>Description</strong></th>
                        <th name="th_expected_date" class="text-center"><strong>Expected Date</strong></th>
                        <th name="th_quantity" class="text-right"><strong>Qty</strong></th>
                    </tr>
                </thead>
                <tbody>
                    <t t-foreach="o.order_line" t-as="order_line">
                        <tr t-att-class="'bg-200 font-weight-bold o_line_section' if order_line.display_type == 'line_section' else 'font-italic o_line_note' if order_line.display_type == 'line_note' else ''">
                            <t t-if="not order_line.display_type">
                                <td id="product">
                                    <span t-field="order_line.name"/>
                                </td>
                                <td class="text-center">
                                    <span t-field="order_line.date_planned"/>
                                </td>
                                <td class="text-right">
                                    <span t-field="order_line.product_qty"/>
                                    <span t-field="order_line.product_uom" groups="uom.group_uom"/>
                                </td>
                            </t>
                            <t t-else="">
                                <td colspan="99" id="section">
                                    <span t-field="order_line.name"/>
                                </td>
                            </t>
                        </tr>
                    </t>
                </tbody>
            </table>

            <p t-field="o.notes"/>

            <div class="oe_structure"/>
        </div>
    </t>
</template>

<template id="report_purchasequotation">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="purchase.report_purchasequotation_document" t-lang="o.partner_id.lang"/>
        </t>
    </t>
</template>
</odoo>
