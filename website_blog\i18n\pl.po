# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <martyna.jak<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON> <m.e.grz<PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <a.wis<PERSON>@hadron.eu.com>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.d<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.warcz<PERSON>@gmail.com>, 2021
# G<PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>ra<PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <zd<PERSON>chu<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <szla<PERSON><EMAIL>>, 2021
# <PERSON>in <PERSON>łynarczyk <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Dawid Prus, 2021
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_blog
#: code:addons/website_blog/models/website_blog.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopiuj)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "' nagłówek strony."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "'. Wyświetl wyniki dla '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "-- Wszystkie daty"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Lornetki są lekkie i przenośne.</b> Jeśli nie możesz sobie pozwolić na "
"obserwatorium na swoim tarasie, zapewne będziesz podróżować, aby dokonać "
"obserwacji. Lornetka jest lżejsza i łatwiej ją zabrać ze sobą w odległe "
"miejsce niż nieporęczny zestaw do montażu teleskopu."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Click on Save</b> to record your changes."
msgstr "<b>Kliknij \"Zapisz\"</b> aby zapisać zmiany"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Poradź się ekspertów</b>. Jeśli nie należysz jeszcze do żadnego "
"stowarzyszenia lub klubu astronomicznego, sprzedawcy w sklepie z teleskopami"
" będą w stanie wskazać Ci aktywne stowarzyszenia w Twojej okolicy. Po "
"nawiązaniu kontaktów z ludźmi, którzy kupili teleskopy, możesz uzyskać "
"porady dotyczące tego, co działa, a czego należy unikać, które są bardziej "
"wiarygodne niż to, czego dowiesz się z artykułu w Internecie lub od "
"sprzedawcy w Wal-Marcie."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""
"<b>Opublikuj swój post na blogu</b>, aby był widoczny dla odwiedzających."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Zaloguj się</b>"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>That's it, your blog post is published!</b> Discover more features "
"through the <i>Customize</i> menu."
msgstr ""
"<b>To wszystko, Twój blog został opublikowany!</b> Odkryj więcej funkcji za "
"pomocą menu <i>Dostosuj</i>."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Wypróbuj zanim kupisz.</b> To kolejna zaleta wyjazdu na wycieczki "
"terenowe z klubem astronomicznym. Możesz spędzić ładnych parę godzin z "
"ludźmi, którzy znają się na teleskopach i mają wypracowane kalibracje, aby "
"zbadać ich sprzęt, poznać kluczowe aspekty techniczne i wypróbować je, zanim"
" wydasz krocie na swój własny zestaw."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Napisz swoją historię tutaj.</b> Użyj górnego paska narzędzi, aby "
"ukształtować tekst: dodaj obraz lub tabelę, ustaw pogrubienie lub kursywę "
"itp. Przeciągnij i upuść bloki konstrukcyjne, aby uzyskać bardziej graficzne"
" blogi."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Oprócz rodzimej ludności, miejscowa dzika przyroda "
"również przyciąga tłumy.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">To niezwykle ważne, abyś zakupił odpowiedni "
"teleskop.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr "<em class=\"h4 my-0\">W astrologii chodzi właśnie o ten moment \"Wow\".</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Im więcej recenzji czytasz, tym bardziej zauważasz, że"
" mają one tendencję do skupiania się na skrajnych opiniach.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Jest coś ponadczasowego w kosmosie.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Twoje badania nad Księżycem, jak wszystko inne, mogą "
"zacząć się od czegoś prostego do czegoś bardzo złożonego.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Brak zdefiniowanych znaczników</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-sm\">Czytaj "
"następne</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-sm text-white\">\n"
"<i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"mr-1\">Show:</span>"
msgstr "<span class=\"mr-1\">Pokaż:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled pl-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled pl-0\">Blogi:</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Świetny sposób na odkrycie ukrytych miejsc"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Wakacje w Miedzianym Kanionie zapowiadają się jako ekscytująca mieszanka "
"relaksu, kultury, historii, dzikiej przyrody i wędrówek."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Nowy wpis"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Podróżnik może zdecydować się na zwiedzanie okolicy, wędrując wokół kanionu "
"lub zapuszczając się w jego głąb. Ci, którzy chcą zapuścić się w głąb "
"kanionu, powinni ułożyć szczegółowy plan. Istnieje wiele biur podróży, które"
" specjalizują się w organizowaniu wycieczek do tego regionu. Turyści mogą "
"przylecieć nad Miedziany Kanion korzystając z wizy turystycznej, która jest "
"ważna przez 180 dni. Podróżni mogą również jechać z dowolnego miejsca w "
"Stanach Zjednoczonych i nabyć wizę na meksykańskiej stacji celnej na "
"granicy."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "O nas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Przede wszystkim <b>nawiąż kontakt z renomowanym sklepem z teleskopami</b>, "
"w którym pracują ludzie znający się na rzeczy. Jeśli kupisz teleskop w Wal-"
"Marcie lub domu towarowym, szanse na to, że dostaniesz dobry model, są "
"znikome."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Dostęp do postu"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Aktywne"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Dodaj trochę"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Wszystko"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Wszystkie Blogi"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Wszystkie blogi"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Alone in the ocean"
msgstr "Samemu w oceanie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "Jak trudny jest montaż i demontaż?"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.js:0
#, python-format
msgid "Amazing blog article: %s! Check it live: %s"
msgstr "Niesamowity artykół: %s! Sprawdź teraz: %s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Ekscytująca mieszanka relaksu, kultury, historii, dzikiej przyrody i "
"wędrówek."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"A kiedy już wszystko będzie zostanie powiedziane<b>, przygotuj "
"się.</b> Twoje poszukiwania coraz to nowszych i lepszych teleskopów będą "
"trwały przez całe życie. Pozwól sobie na uzależnienie od astronomii, a "
"doświadczenie to wzbogaci każdy aspekt życia. Będzie to uzależnienie, "
"którego nigdy nie będziesz chciał przerwać."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Inną unikalną atrakcją Miedzianego Kanionu jest obecność kultury Indian "
"Tarahumara. Ci półkoczowniczy ludzie żyją w jaskiniach. Ich utrzymanie "
"zależy głównie od rolnictwa i hodowli bydła."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
msgid "Archive"
msgstr "Archiwizuj"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
msgid "Archives"
msgstr "Archiwa"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Artykuł"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Artykuły"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Kluby astronomiczne to tętniące życiem miejsca pełne znających się na rzeczy"
" amatorów, którzy chętnie podzielą się z Tobą swoją wiedzą. Za cenę coli i "
"przekąsek pójdą z tobą na obserwację gwiazd i przytłoczą cię ciekawostkami i"
" wielką wiedzą."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "Astronomia to \"patrzenie w gwiazdy\""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Nazwisko Autora"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Awatar"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Awesome hotel rooms"
msgstr "Świetne pokoje hotelowe"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Bądź świadomy tej rzeczy zwanej \"astronomią\""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Przynależność do stowarzyszenia oddanych astronomów amatorów da Ci dostęp do"
" tych zorganizowanych wysiłków mających na celu osiągnięcie nowych poziomów "
"w naszej zdolności do badania Księżyca Ziemi. Da Ci też przyjaciół, którzy "
"podzielają twoją pasję do astronomii i którzy mogą dzielić się swoim "
"doświadczeniem i obszarami wiedzy, gdy będziesz szukał, gdzie możesz "
"spojrzeć dalej na ogromnym nocnym niebie, na Księżyc i poza nim w swoim "
"poszukiwaniu wiedzy o pozornie nieskończonym wszechświecie nad nami."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Stawać się częścią stowarzyszenia oddanych astronomów amatorów."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Sypialnie"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Zanim jednak zdecydujesz się na tak duży wydatek, lepszym rozwiązaniem może "
"być zainwestowanie w dobry zestaw lornetek. Istnieją nawet lornetki "
"przystosowane do obserwacji gwiazd, które równie dobrze sprawdzą się w roli "
"dodatkowych urządzeń pozwalających lepiej dostrzec cuda wszechświata. Dobrze"
" zaprojektowany zestaw lornetek zapewnia również znacznie większą mobilność "
"i możliwość utrzymania \"ulepszonego widzenia\" na wyciągnięcie ręki, gdy "
"tylko pojawi się niesamowity widok."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Przed dokonaniem pierwszego zakupu..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Poza Okiem"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Nazwa bloga"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
#, python-format
msgid "Blog Post"
msgstr "Wpis bloga"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to be calling this file !"
msgstr "Post bloga <b>%s</b> wydaje się wywoływać ten plik!"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#, python-format
msgid "Blog Post <b>%s</b> seems to have a link to this page !"
msgstr "Post bloga <b>%s</b> wydaje się mieć odnośnik do tej strony!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Okładka Artykułu"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Tytuł wpisu bloga"

#. module: website_blog
#: code:addons/website_blog/models/website.py:0
#: code:addons/website_blog/models/website.py:0
#: model:ir.actions.act_window,name:website_blog.action_blog_post
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
#, python-format
msgid "Blog Posts"
msgstr "Wpisy bloga"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Podtytuł bloga"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Tag bloga"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Kategoria znaczników blogów"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Tagi bloga"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Tytuł Bloga"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Tytuł Bloga"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
msgid "Blogs"
msgstr "Blogi"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Ale jak przebrnąć przez niesamowity wybór w ofercie? A co ważniejsze, czy "
"naprawdę ufasz zdjęciom i opisom hoteli, które sami sobie przyznali w celu "
"uzyskania rezerwacji? Opinie podróżnych mogą być pomocne, ale należy "
"zachować ostrożność. Często są one stronnicze, czasami nieaktualne i mogą w "
"ogóle nie służyć twoim interesom. Skąd wiesz, że cechy, które są ważne dla "
"recenzenta, są ważne dla Ciebie?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Zakup teleskopu"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Zakup odpowiedniego teleskopu, aby przenieść swoją miłość do astronomii na "
"wyższy poziom, to duży krok w rozwoju pasji do gwiazd."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Można publikować"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Kategoria"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Udogodnienia dla dzieci"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Choose an image from the library."
msgstr "Wybierz obraz z biblioteki"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Click here to add new content to your website."
msgstr "Kliknij tutaj by dodać nową zawartość dla swojej strony."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Kliknij \"<b>Nowy</b>\" w prawym górnym rogu, aby napisać swój pierwszy wpis"
" na blogu."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Zamknij"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Komentarz"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Comments"
msgstr "Komentarz"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Zawartość"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Copper Canyon to jeden z sześciu wąwozów w okolicy. Chociaż nazwa sugeruje, "
"że wąwóz może mieć jakiś związek z wydobyciem miedzi, tak nie jest. Nazwa "
"pochodzi od miedzianych i zielonych porostów pokrywających kanion. Copper "
"Canyon ma dwie strefy klimatyczne. Region charakteryzuje się klimatem "
"alpejskim na górze i subtropikalnym na niższych poziomach. Zimy są mroźne z "
"częstymi burzami śnieżnymi na wyższych wysokościach. Lata są suche i gorące."
" Stolica, Chihuahua, to wysoko położona pustynia, gdzie pogoda waha się od "
"mroźnych zim do gorących lat. Region ten jest wyjątkowy ze względu na różne "
"ekosystemy, które w nim występują."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Właściwości okładki"

#. module: website_blog
#: model_terms:ir.actions.act_window,help:website_blog.action_blog_post
msgid "Create a new blog post"
msgstr "Utwórz nowy wpis na blogu"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (nowa do starej)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (stara do nowej)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Opis"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_edit_options
msgid "Duplicate"
msgstr "Duplikuj"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Wschodnie Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Wycieczki helikopterem na wschodnie Maui zapewnią widok na wulkan Haleakala "
"lub Dom Słońca o wysokości dziesięciu tysięcy stóp. Wulkan ten jest uśpiony,"
" a jego ostatnia erupcja miała miejsce w 1790 roku. Będziesz mógł zobaczyć "
"krater wulkanu i suchą, jałową ziemię otaczającą południową stronę zbocza "
"wulkanu podczas wycieczek helikopterem Maui."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Edytuj na backendzie"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Edytuj '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Edytuj 'Wszystkie Blogi' nagłówka strony."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Edytuj 'Filtruj Wyniki' nagłówka strony."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Fakty, o których należy pamiętać."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Wreszcie, co najważniejsze, zespół kontrolujący katalog hoteli wysokiej "
"jakości powinien regularnie odwiedzać dany hotel, spotykać się z personelem,"
" spać w sypialni i próbować jedzenia. Powinni doświadczyć hotelu tak, jak "
"może to zrobić tylko gość hotelowy i tylko wtedy mają naprawdę mocną pozycję"
" do napisania o hotelu."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Follow Us"
msgstr "Obserwuj nas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Wielu z nas, mieszkańców miast, nie zauważa tego nieba na co dzień. Światła "
"miasta dobrze maskują niesamowity widok, który przez cały czas znajduje się "
"nad naszymi głowami."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Dla wielu z nas nasze pierwsze doświadczenia związane z poznawaniem ciał "
"niebieskich rozpoczęły się, gdy zobaczyliśmy naszą pierwszą pełnię księżyca "
"na niebie. To naprawdę wspaniały widok, nawet gołym okiem."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Od najmniejszego dziecka po najbardziej zaawansowanego astrofizyka - każdy, "
"kto chce cieszyć się astronomią, znajdzie coś dla siebie. W rzeczywistości "
"jest to nauka, która jest tak przystępna, że praktycznie każdy może ją "
"uprawiać praktycznie gdziekolwiek się znajduje. Wszystko, co muszą wiedzieć,"
" jak to zrobić, to spojrzeć w górę."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Zostań geekiem"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Kup teleskop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Zapoznaj się z historią"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Rozpocznij"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Grupuj wg"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Oto kilka kluczowych faktów, o których należy pamiętać:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Wskazówki dotyczące wakacji"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Jak sprawdzić"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Jak skomplikowany jest teleskop i czy będziesz miał problemy z jego "
"konserwacją? Połącz się w sieć, aby uzyskać odpowiedzi na te i inne pytania."
" Jeśli odrobisz pracę domową w ten sposób, znajdziesz odpowiedni teleskop na"
" kolejny duży krok w ewolucji swojej pasji do astronomii."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Jak mobilny musi być teleskop?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
msgid "How to choose the right hotel"
msgstr "Jak wybrać odpowiedni hotel"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Jeśli ważne jest, aby hotel znajdował się na przykład na plaży, w pobliżu "
"parku rozrywki lub w dogodnej odległości od lotniska, to lokalizacja jest "
"najważniejsza. Każdy przyzwoity katalog powinien oferować mapę lokalizacji "
"hotelu i jego okolic. Powinny być dostępne wykresy odległości do lotniska, a"
" także jakaś forma interaktywnej mapy."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Jeśli noc jest pogodna, można dostrzec niesamowite szczegóły powierzchni Księżyca, obserwując gwiazdy na swoim podwórku.\n"
"Naturalnie, w miarę rozwoju zamiłowania do astronomii, wiele ciał niebieskich stanie się dla nas fascynujących. Ale Księżyc zawsze będzie naszą pierwszą miłością, ponieważ jest jedynym odległym obiektem kosmicznym, który ma wyjątkową zaletę latania blisko Ziemi i po którym chodził człowiek."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
msgid "In"
msgstr "W"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"Pod wieloma względami jest to duży krok od kogoś, kto po prostu bawi się "
"astronomią, do poważnego studenta tej nauki. Ale oboje wiemy, że po zakupie "
"teleskopu jest jeszcze jeden duży krok, zanim naprawdę będziesz wiedział, "
"jak go używać."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_list
msgid "Is Published"
msgstr "Opublikowane"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Islands"
msgstr "Wyspy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Świetną zabawą jest rozpoczęcie nauki konstelacji, nawigacji po nocnym "
"niebie oraz znajdowania planet i słynnych gwiazd. Dostępnych jest wiele "
"stron internetowych i książek, które mogą Cię w tym pomóc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Ważne jest, aby wybrać hotel, który sprawi, że poczujesz się komfortowo - "
"współczesne lub tradycyjne meble, lokalny wystrój lub międzynarodowy, "
"formalny lub zrelaksowany. Idealny katalog hoteli powinien informować o "
"dostępnych opcjach."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Można śmiało powiedzieć, że w pewnym momencie naszego życia, każdy z nas ma "
"ten moment, kiedy nagle jesteśmy oszołomieni, gdy stajemy twarzą w twarz z "
"ogromem wszechświata, który widzimy na nocnym niebie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"To naprawdę niesamowite, gdy pomyślisz o tym, że po prostu patrząc w górę w "
"dowolną noc, możesz zobaczyć praktycznie setki tysięcy gwiazd, układów "
"gwiezdnych, planet, księżyców, asteroid, komet, a może nawet okazjonalny "
"prom kosmiczny. Jest to jeszcze bardziej zapierające dech w piersiach, gdy "
"zdasz sobie sprawę, że niebo, na które patrzysz, jest dokładnie tym samym "
"niebem, którym cieszyli się nasi przodkowie setki i tysiące lat temu, gdy "
"tylko spojrzeli w górę."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Jungle"
msgstr "Dżungla"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Wiedzieć, na co się patrzy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Wiedzieć, kiedy szukać"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Large"
msgstr "Duży"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Ostatni Współtwórca"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_post____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag____last_update
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Latest"
msgstr "Ostatnie"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_latest_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Ostatnie artykuły"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Poznanie tła wielkich odkryć w astronomii sprawi, że chwile spędzone na "
"obserwowaniu gwiazd będą bardziej znaczące. Jest to jedna z najstarszych "
"nauk na ziemi, więc dowiedz się, którzy wielcy ludzie w historii patrzyli w "
"gwiazdy przed tobą."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Obiekty rekreacyjne"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"Lokalny koloryt jest świetny, ale hotelowe restauracje i bary mogą odegrać "
"ważną rolę w Twoim pobycie. Powinieneś być świadomy wyboru, stylu i tego, "
"czy są one eleganckie czy nieformalne. Dobry raport hotelowy powinien ci to "
"powiedzieć, a zwłaszcza o udogodnieniach śniadaniowych."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Położenie"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_main_attachment_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_main_attachment_id
msgid "Main Attachment"
msgstr "Główny załącznik"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
msgid "Maui helicopter tours"
msgstr "Wycieczki helikopterem na Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Wycieczki helikopterem na Maui to świetny sposób, aby zobaczyć wyspę z innej"
" perspektywy i przeżyć zabawną przygodę. Jeśli nigdy wcześniej nie leciałeś "
"helikopterem, jest to świetne miejsce, aby to zrobić."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Wycieczki helikopterem na Maui to świetny sposób na zwiedzanie miejsc, do "
"których nie można dotrzeć pieszo lub samochodem. Wycieczki trwają około "
"godziny i wahają się od około stu ośmiu pięciu dolarów do dwustu "
"czterdziestu dolarów za osobę. Dla wielu jest to jedyna w życiu okazja, aby "
"zobaczyć naturalną scenerię, która nie będzie ponownie dostępna. Zabranie "
"aparatów fotograficznych i nagrań wideo w celu uwiecznienia chwil pozwoli "
"również na ponowne przeżycie wycieczki i wspominanie jej przez lata."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Wycieczki helikopterem na Maui pozwolą Ci zobaczyć wszystkie te zabytki. "
"Upewnij się, że zabrałeś ze sobą aparat fotograficzny lub wideo podczas "
"wycieczek helikopterem Maui, aby uchwycić piękno scenerii i pokazać znajomym"
" i rodzinie w domu wszystkie wspaniałe rzeczy, które widziałeś podczas "
"wakacji."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Medium"
msgstr "Medium"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Opis Meta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Słowa Kluczowe Meta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Tytuł Meta"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Wycieczki helikopterem na Molokai Maui zabiorą Cię na inną wyspę, ale oddaloną o zaledwie dziewięć mil i łatwo dostępną drogą powietrzną. Wyspa ta ma bardzo małą populację z inną kulturą i scenerią. Całe wybrzeże północno-wschodnie jest wyłożone klifami i odległymi plażami. Są one całkowicie niedostępne innymi środkami transportu niż samolot.\n"
"Ludzie mieszkający na wyspie nigdy nawet nie widzieli tej niezwykłej scenerii, chyba że wybrali się na wycieczkę helikopterem Maui, aby ją zobaczyć. Kiedy pogoda jest deszczowa i jest dużo opadów w sezonie, można zobaczyć wiele zdumiewających wodospadów."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Ważniejsze dla osób podróżujących z rodziną niż w interesach jest "
"sprawdzenie w katalogu, czy hotel jest przyjazny dzieciom i podjęcie decyzji"
" na tej podstawie. Jedną z rzeczy, na które warto zwrócić uwagę, jest to, "
"czy hotel oferuje usługi opiekunki do dziecka. Dla osób podróżujących "
"służbowo, które chcą uciec od dzieci, jest to oczywiście również bardzo "
"istotne - być może hotel, który nie jest przyjazny dzieciom, byłby czymś "
"bardziej odpowiednim!"

#. module: website_blog
#: model:ir.filters,name:website_blog.dynamic_snippet_most_viewed_blog_post_filter
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Najczęściej wyświetlane artykuły"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Nazwa"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "New Blog Post"
msgstr "Nowy wpis bloga"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Brak wpisów na blogu"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Liczba wyświetleń"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Brak wyników dla \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Nie znaleziono wyników dla '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Brak zdefiniowanych znaczników."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Brak"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Nic z tego nie stoi na przeszkodzie, aby realizować swoje plany dotyczące "
"stworzenia niesamowitego systemu teleskopów. Po prostu upewnij się, że "
"otrzymałeś wysokiej jakości poradę i szkolenie na temat tego, jak "
"skonfigurować teleskop, aby spełniał twoje potrzeby. Korzystając z tych "
"wskazówek, będziesz mógł cieszyć się godzinami obserwacji fenomenalnych "
"widoków na nocnym niebie, które są poza zasięgiem gołego oka."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Nie tylko znajomość pogody sprawi, że obserwowanie gwiazd będzie "
"satysfakcjonujące, ale jeśli dowiesz się, kiedy odbędą się wielkie pokazy "
"meteorów i inne ważne wydarzenia astronomiczne, sprawi, że emocje związane z"
" astronomią ożyją."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_unread_counter
msgid "Number of unread messages"
msgstr "Liczba nieprzeczytanych wiadomości"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Oczywiście, aby wynieść swoje uwielbienie dla Księżyca na najwyższy poziom, "
"należy rozszerzyć swój sprzęt do dobrego teleskopu startowego, który zapewni"
" najbardziej oszałamiające szczegóły powierzchni Księżyca. Z każdym z tych "
"ulepszeń twoja wiedza oraz głębia i zakres tego, co będziesz w stanie "
"zobaczyć, poprawią się geometrycznie. Dla wielu amatorów astronomii, czasami"
" nie możemy się nacieszyć tym, co możemy zobaczyć na naszym najbliższym "
"obiekcie kosmicznym."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Once you have reviewed the content on mobile, close the preview."
msgstr "Po sprawdzeniu treści na urządzeniach mobilnych zamknij podgląd."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Inne"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Nasze blogi"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Zdjęcie: Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Zdjęcie Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Zdjęcie Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Zdjęcie Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Zdjęcie Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Zdjęcie Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Zdjęcie autorstwa Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Zdjęcie: Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Zdjęcie autorstwa PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Zdjęcie wykonane przez SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Zdjęcie Teddy Kelley, @teddykelley"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Wpisy"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Data publikacji"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Opublikowane ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Data publikacji"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Wpis opublikowano"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Opcje publikowania"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Data publikacji"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Read more"
msgstr "Czytaj więcej"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"fa fa-chevron-right ml-2\"/>"
msgstr "Przeczytaj więcej <i class=\"fa fa-chevron-right ml-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Restauracje, kawiarnie i bary"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict publishing to this website."
msgstr "Ogranicz publikowanie do tej strony."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Zoptymalizowane SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Próbka"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Satellites"
msgstr "Satelity"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Search for an image. (eg: type \"business\")"
msgstr "Szukaj obrazu. (np. wpisz \"biznes\")"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seaside vs mountain side"
msgstr "Nadmorska vs górska strona"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Seeing the world from above"
msgstr "Patrzenie na świat z góry"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/website_blog.editor.js:0
#, python-format
msgid "Select Blog"
msgstr "Wybierz Blog"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select the blog you want to add the post to."
msgstr "Wybierz blog do którego chcesz dodać blog."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Select this menu item to create a new blog post."
msgstr "Wybierz ten element menu, aby utworzyć nowy post na blogu."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Nazwa SEO"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Oddziel każde słowo kluczowe przecinkiem"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Set a blog post <b>cover</b>."
msgstr "Ustaw <b>okładkę</b> postu bloga."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Kilka migrujących i rodzimych ptaków, ssaków i gadów nazywa Copper Canyon "
"swoim domem. Wykwintna fauna w tej niemal dziewiczej krainie jest również "
"warta sprawdzenia."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Udostępnij na Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Udostępnij na LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Twitter"
msgstr "Udostępnij na Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Udostępnij ten artykuł"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Sierra Tarahumara, popularnie znany jako Miedziany Kanion, znajduje się w "
"Meksyku. Obszar ten jest ulubionym miejscem wśród osób poszukujących pełnych"
" przygód wakacji."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Silly-Chico"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Skies"
msgstr "Niebo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Dlatego też niezwykle ważne jest, aby kupić teleskop odpowiedni do miejsca, "
"w którym się znajdujemy i naszych preferencji w zakresie obserwacji gwiazd. "
"Na początek omówimy trzy główne rodzaje teleskopów, a następnie przedstawimy"
" kilka koncepcji \"Teleskopu 101\", aby zwiększyć szanse na zakup właściwej "
"rzeczy."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Może się więc zdarzyć, że raz w roku wybierzemy się na wakacje na kemping "
"lub wycieczkę do domu krewnego na wsi, gdzie znajdziemy się na zewnątrz, gdy"
" rozrzutnik nocnego nieba nagle zdecyduje się zaprezentować swój "
"spektakularny pokaz. Jeśli miałeś taki moment, w którym dosłownie zaparło ci"
" dech w piersiach to, co może nam pokazać nocne niebo, prawdopodobnie "
"pamiętasz dokładnie ten moment, kiedy mogłeś powiedzieć tylko \"wow\" na "
"widok tego, co zobaczyłeś."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Tak więc, aby wybrać odpowiedni rodzaj teleskopu, ważne są twoje cele w "
"korzystaniu z teleskopu. Aby naprawdę zrozumieć mocne i słabe strony nie "
"tylko soczewek i konstrukcji teleskopu, ale także tego, jak teleskop "
"sprawdza się w różnych sytuacjach obserwacyjnych, najlepiej jest wcześniej "
"odrobić pracę domową i zapoznać się z różnymi rodzajami teleskopów. Przed "
"dokonaniem pierwszego zakupu..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Wybierasz się za granicę, wybrałeś miejsce docelowe, a teraz musisz wybrać "
"hotel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Ktoś sławny w <cite title=\"Source Title\"> Tytule źródłowym</cite>"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Spotting the fauna"
msgstr "Wypatrywanie fauny"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/models/website_blog.py:0
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Start writing here..."
msgstr "Zacznij pisać tutaj..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Styl"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Podtytuł"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Podtytuł"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Kategorie znaczników"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Kategorie znaczniików"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr " Formularz kategorii znaczników "

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Formularz tagu"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Lista tagów"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists !"
msgstr "Kategoria znaczników już istnieje!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists !"
msgstr "Nazwa znacznika już istnieje!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Tagi"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Taking pictures in the dark"
msgstr "Robienie zdjęć w ciemności"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
msgid "Teaser"
msgstr "Zwiastun"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Treść zwiastuna"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Dziesięć lat temu prawdopodobnie odwiedziłbyś lokalne biuro podróży i zaufał"
" poradom udzielonym przez tak zwanych \"ekspertów\". W XXI wieku wybór i "
"rezerwacja hotelu odbywa się oczywiście przez Internet, za pośrednictwem "
"stron internetowych dla podróżnych."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Ten moment \"Wow\" jest tym, o co chodzi w astrologii. Dla niektórych ten "
"moment wow staje się pasją, która prowadzi do kariery w badaniu gwiazd. Dla "
"nielicznych szczęśliwców ten moment \"wow\" staje się pochłaniającą obsesją,"
" która prowadzi do podróży do gwiazd promem kosmicznym lub jedną z naszych "
"wczesnych misji kosmicznych. Ale dla większości z nas astrologia może stać "
"się rozrywką lub zwykłym hobby. Ale nosimy ten moment wow ze sobą przez "
"resztę naszego życia i zaczynamy szukać sposobów, aby spojrzeć głębiej i "
"dowiedzieć się więcej o spektakularnym wszechświecie, który widzimy w "
"milionach gwiazd nad nami każdej nocy."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "Piękno astronomii polega na tym, że każdy może ją uprawiać."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"Najlepszą porą na oglądanie Księżyca jest oczywiście noc, kiedy jest "
"niewiele chmur, a pogoda sprzyja długim i długotrwałym obserwacjom. Pierwsza"
" kwadra zapewnia największą szczegółowość obserwacji. I nie daj się zwieść "
"zjawisku zasłaniania części Księżyca, gdy nie jest on w fazie pełni. "
"Zjawisko znane jako \"blask ziemi\" daje możliwość zobaczenia zaciemnionej "
"części księżyca z pewnymi szczegółami, nawet jeśli księżyc jest tylko w "
"ćwiartce lub w połowie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "Najlepszy czas na oglądanie księżyca."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Wpis blogu będzie widoczny dla odwiedzających od tej daty na stronie "
"internetowej, jeśli jest ustawiony jako opublikowany."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Klify w tym regionie należą do najwyższych na świecie, a widok wody "
"spływającej kaskadami z wysokich szczytów po prostu zapiera dech w "
"piersiach. Krótka wycieczka z Maui helikopterem jest warta zobaczenia piękna"
" tego naturalnego środowiska."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "Pełny adres URL dostępu do dokumentu przez stronę."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Kolejną rzeczą, którą naturalnie chcemy nabyć, jest dobry teleskop. Być może"
" widziałeś hobbystę, który jest na zaawansowanym etapie swoich badań i "
"ustawia te naprawdę fajnie wyglądające teleskopy gdzieś na wzgórzu. To "
"ekscytuje astronoma amatora, ponieważ musi to być logiczny kolejny krok w "
"rozwoju jego hobby. Ale jak kupić dobry teleskop może być wręcz zagmatwane i"
" onieśmielające."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"Strona powinna zawierać szczegółową analizę usług rekreacyjnych w hotelu - "
"spa, basen, siłownia, sauna - a także szczegóły dotyczące wszelkich innych "
"obiektów w pobliżu, takich jak pola golfowe. 7. Specjalne potrzeby: strona "
"katalogu hoteli powinna informować gości o usługach specjalnych i polityce "
"dostępności każdego hotelu. Chociaż nie dotyczy to każdego gościa, dla "
"niektórych jest to absolutnie niezbędne."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Decyzje dotyczące statywu lub innych akcesoriów znacznie się zmienią w "
"przypadku teleskopu, który będzie mieszkał na pokładzie, w porównaniu z "
"teleskopem, który planujesz zabrać w wiele odległych miejsc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"Widok ten naprawdę zapiera dech w piersiach i jest widokiem, którego nie "
"można przegapić. Jest to również bardzo pouczająca okazja, aby zobaczyć "
"uśpiony wulkan z bliska, coś, czego nie można zobaczyć każdego dnia. Po "
"północnej i południowej stronie wulkanu zobaczysz jednak zupełnie inny "
"widok. Te strony są bujne i zielone, a będziesz mógł zobaczyć piękne "
"wodospady i wspaniałe zarośla. Tropikalne lasy deszczowe obfitują po tej "
"stronie wyspy i jest to coś, co nie jest łatwo dostępne w żaden inny sposób "
"niż drogą powietrzną."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Do tego dochodzi problem motywacji recenzenta. Im więcej recenzji czytasz, "
"tym bardziej zauważasz, że mają one tendencję do skupiania się na skrajnych "
"opiniach. Z jednej strony mamy wściekłych recenzentów, którzy mają coś do "
"ukrycia, a z drugiej zachwyconych gości, którzy nie szczędzą pochwał. Nie "
"będziesz zaskoczony, gdy dowiesz się, że hotele czasami publikują własne "
"pochlebne recenzje lub że konkurenci ustawiają się w kolejce, aby mieć "
"szansę na zniesławienie konkurencji złymi recenzjami. Przy wyborze hotelu "
"warto zastanowić się, co jest dla nas naprawdę ważne. Następnie należy "
"wybrać internetowy katalog hoteli, który zapewnia aktualne, niezależne i "
"bezstronne informacje, które naprawdę mają znaczenie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Istnieją również inne czynniki, które należy wziąć pod uwagę przy "
"podejmowaniu ostatecznej decyzji o zakupie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"W kosmosie jest coś ponadczasowego. Fakt, że planety, księżyc i gwiazdy poza"
" nimi istnieją od wieków, ma wpływ na nasze poczucie miejsca we "
"wszechświecie. W rzeczywistości wiele gwiazd, które \"widzimy\" gołym okiem,"
" to w rzeczywistości światło, które pochodzi z tej gwiazdy setki tysięcy lat"
" temu. Światło to dopiero teraz dociera do Ziemi. Tak więc w bardzo realny "
"sposób patrzenie w górę jest jak podróż w czasie."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Te rzeczy naprawdę mają znaczenie i każdy przyzwoity katalog hotelowy "
"powinien zawierać tego rodzaju porady dotyczące pokoi - a nie tylko liczbę "
"pokoi, co jest typową opcją!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "To pole nie będzie widoczne dla odwiedzających"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "This tag already exists"
msgstr "Ten znacznik już istnieje"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/options.js:0
#, python-format
msgid "Tiny"
msgstr "Malutki"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Tytuł"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Aby spojrzeć na Księżyc gołym okiem, zapoznanie się z mapą Księżyca pomoże "
"ci wybrać morza, kratery i inne zjawiska geograficzne, które inni już "
"zmapowali, aby uczynić twoje badania przyjemniejszymi. Mapy Księżyca można "
"nabyć w każdym sklepie astronomicznym lub online i są one warte swojej ceny."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Aby zacząć uczyć się, jak lepiej obserwować gwiazdy, istnieje kilka "
"podstawowych rzeczy, których możemy potrzebować, aby spojrzeć głębiej, poza "
"to, co możemy zobaczyć gołym okiem i zacząć badać gwiazdy, a także cieszyć "
"się nimi. Pierwszą rzeczą, której potrzebujesz, nie jest wcale sprzęt, ale "
"literatura. Dobra mapa gwiazd pokaże ci główne konstelacje, lokalizację "
"kluczowych gwiazd, których używamy do nawigacji po niebie oraz planety, "
"które wydają się większe niż gwiazdy. A jeśli dodamy do tego dobrze wykonane"
" materiały wprowadzające w hobby astronomii, jesteśmy na dobrej drodze."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Dobra lornetka może zdziałać cuda, jeśli chodzi o szczegóły widoczne na "
"powierzchni Księżyca. Aby uzyskać najlepsze rezultaty, ustaw lornetkę na "
"szerokie pole, aby móc podziwiać księżycowy krajobraz w całej jego krasie. A"
" ponieważ prawie niemożliwe jest utrzymanie lornetki w bezruchu przez czas, "
"w którym będziesz chciał wpatrywać się w to wspaniałe ciało w kosmosie, "
"warto dodać do swojego arsenału sprzętu dobry statyw, do którego można "
"przymocować lornetkę, aby móc wygodnie i stabilnie obserwować Księżyc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Aby wznieść się na wyższy poziom, warto skorzystać z możliwości współpracy z"
" innymi astronomami lub odwiedzić jeden z naprawdę wspaniałych teleskopów, "
"które zostały skonfigurowane przez profesjonalistów, którzy zainwestowali w "
"lepsze techniki eliminowania zakłóceń atmosferycznych, aby jeszcze lepiej "
"widzieć Księżyc. Internet umożliwia dostęp do Hubble'a i wielu innych "
"ogromnych teleskopów, które cały czas skierowane są na Księżyc. Co więcej, "
"wiele klubów astronomicznych pracuje nad sposobami łączenia wielu "
"teleskopów, starannie zsynchronizowanych z komputerami w celu uzyskania "
"najlepszego widoku księżycowego krajobrazu."

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Podróż"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Nieopublikowane ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread
msgid "Unread Messages"
msgstr "Nieprzeczytane wiadomości"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_unread_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Licznik nieprzeczytanych wiadomości"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Wpis bez tytułu"

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Użyj tej ikony, aby wyświetlić podgląd wpisu na blogu na <b>urządzeniach "
"mobilnych</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Zastosowane w:"

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "Viewpoints"
msgstr "Punkty widokowe"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Widoki"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Widoczne na stronach wszystkich blogów "

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Widoczne na obecnej stronie"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Strona internetowa"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Blogi witryny"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Wyszukiwarka szablonów"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "Adres strony internetowej"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Opis strony - meta"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Słowa kluczowe dla strony - meta"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Tytuł strony - meta"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Obrazek typu opengraph dla strony"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "Co by było, gdyby pozwolili ci uruchomić Hubble'a?"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Chociaż każdy może spojrzeć w górę i zakochać się w gwiazdach w dowolnym "
"momencie, zabawa z astronomią polega na uczeniu się, jak stawać się coraz "
"bardziej wykwalifikowanym i wyposażonym w obserwację gwiazd, aby widzieć i "
"rozumieć coraz więcej za każdym razem, gdy spojrzysz w górę. Oto kilka "
"kroków, które możesz podjąć, aby chwile, które możesz poświęcić swojemu "
"hobby, jakim jest astronomia, były o wiele przyjemniejsze."

#. module: website_blog
#: code:addons/website_blog/models/website_snippet_filter.py:0
#, python-format
msgid "With a View"
msgstr "Z widokiem"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "Napisz tekst tutaj by opisać Twój blog lub firmę."

#. module: website_blog
#. openerp-web
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
#, python-format
msgid "Write a title, the subtitle is optional."
msgstr "Napisz tytuł, podtytuł jest opcjonalny."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Powinieneś zawsze dokładnie rozważyć rodzaj udogodnień, których potrzebujesz"
" w sypialni i znaleźć hotel, który ma te, które uważasz za ważne. Strona "
"internetowa z katalogiem hoteli powinna zawierać szczegółowe informacje na "
"temat takich kwestii, jak: rozmiar łóżka, dostęp do Internetu (jego koszt, "
"czy jest WIFI lub przewodowe połączenie szerokopasmowe), bezpłatne "
"udogodnienia, widoki z pokoju i luksusowe oferty, takie jak menu poduszek "
"lub menu kąpieli, wybór pokoi dla palących lub niepalących itp."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Zobaczysz całe piękno, które Maui ma do zaoferowania i możesz spędzić miło "
"czas z całą rodziną. Wycieczki nie są zbyt drogie i trwają od czterdziestu "
"pięciu minut do ponad godziny. Dzięki wycieczkom helikopterem Maui można "
"zobaczyć miejsca, które zazwyczaj są niedostępne. Miejsca, które nie są "
"dostępne pieszo lub pojazdem, można zobaczyć z powietrza. Zapierające dech w"
" piersiach widoki czekają na tych, którzy są gotowi na zabawne wycieczki "
"helikopterem Maui. Jeśli zamierzasz pozostać na wyspie przez dłuższy czas, "
"możesz pomyśleć o odbyciu wielu wycieczek helikopterem Maui."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "przygoda"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "blog. Kliknij tutaj, aby uzyskać dostęp do bloga:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "okruszek"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "przez"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "odkrycie"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "przewodniki"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "został opublikowany na stronie"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hotele"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "w"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "in <i class=\"fa fa-folder-open text-muted\"/>"
msgstr "w <i class=\"fa fa-folder-open text-muted\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "in <i class=\"fa fa-folder-open text-white-75\"/>"
msgstr "w<i class=\"fa fa-folder-open text-white-75\"/>"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "teleskopy"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "by zostawić komentarz"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "nieopublikowane"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted mr-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Brak komentarzy na ten moment"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "⌙ Hover effect"
msgstr ""
