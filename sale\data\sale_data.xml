<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Share Button in action menu -->
        <record id="model_sale_order_action_share" model="ir.actions.server">
            <field name="name">Share</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="binding_model_id" ref="sale.model_sale_order"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">action = records.action_share()</field>
        </record>

        <!-- set default order confirmation template -->
        <record id="default_confirmation_template" model="ir.config_parameter">
            <field name="key">sale.default_confirmation_template</field>
            <field name="value" ref="sale.mail_template_sale_confirmation"/>
        </record>

        <record model="ir.cron" id="send_invoice_cron">
            <field name="name">automatic invoicing: send ready invoice</field>
            <field name="model_id" ref="payment.model_payment_transaction" />
            <field name="state">code</field>
            <field name="code">model._cron_send_invoice()</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
        </record>

    </data>
</odoo>
