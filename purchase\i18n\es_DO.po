# #-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON>, 2016
# #-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#
# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON>, 2016
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2016-06-26 20:21+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"#-#-#-#-#  es_DO.po (Odoo 9.0)  #-#-#-#-#\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
msgid ""
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p><p>\n"
"Here is, in attachment, a ${object.state in ('draft', 'sent') and 'request "
"for quotation' or 'purchase order confirmation'} <strong>${object.name}</"
"strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting in <strong>${object.amount_total} ${object.currency_id.name}</"
"strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>Do not hesitate to contact us, further you have any question.</p>\n"
"<p>Best regards,</p>\n"
"<p style=\"color:#888888;\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"\n"
"<p>Dear ${object.partner_id.name} \n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>\n"
"Please find in attachment a <strong>${object.state in ('draft', 'sent') and "
"'request for quotation' or 'purchase order confirmation'} ${object.name}</"
"strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.currency_id.name}</"
"strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>You can reply to this email if you have any questions.</p>\n"
"<p>Thank you,</p>\n"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/stock.py:79
#, python-format
msgid " Buy"
msgstr " Comprar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_count
msgid "# of Bills"
msgstr ""

#. module: purchase
#: model:mail.template,subject:purchase.mail_template_data_notification_email_purchase_order
msgid "${object.subject}"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "3-way matching"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_module_account_3way_match
msgid "3-way matching: purchases, receptions and bills"
msgstr ""

#. module: purchase
#: model:mail.template,body_html:purchase.mail_template_data_notification_email_purchase_order
msgid ""
"<html>\n"
"                <head></head>\n"
"                <body style=\"margin: 0; padding: 0;\">\n"
"                <table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor="
"\"#ededed\" style=\"padding: 20px; background-color: #ededed\" summary="
"\"o_mail_notification\">\n"
"                    <tbody>\n"
"\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding="
"\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: "
"rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\">\n"
"                                  <span style=\"font-size:20px; color:white; "
"font-weight: bold;\">\n"
"                                      ${object.record_name}\n"
"                                  </span>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                  <img src=\"/logo.png\" style=\"padding: "
"0px; margin: 0px; height: auto; width: 80px;\" alt=\"${user.company_id."
"name}\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding="
"\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: "
"rgb(255, 255, 255); padding: 20px;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,"
"Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                ${object.body | safe}\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding="
"\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: "
"rgb(135,90,123); padding: 20px;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" style="
"\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                ${user.company_id.name}<br/>\n"
"                                ${user.company_id.phone or ''}\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\" style="
"\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                % if user.company_id.email:\n"
"                                <a href=\"mailto:${user.company_id.email}\" "
"style=\"text-decoration:none; color: white;\">${user.company_id.email}</"
"a><br/>\n"
"                                % endif\n"
"                                % if user.company_id.website:\n"
"                                    <a href=\"${user.company_id.website}\" "
"style=\"text-decoration:none; color: white;\">\n"
"                                        ${user.company_id.website}\n"
"                                    </a>\n"
"                                % endif\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                        <td align=\"center\">\n"
"                            Powered by <a href=\"https://www.odoo.com"
"\">Odoo</a>.\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/> "
"Cancelled</span>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Amount</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Our Order Reference:</strong>"
msgstr "<strong>Nuestra Referencia de la Orden:</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Product</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr "<strong>Cant</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Quantity</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Dirección de envío</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Subtotal</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr "<strong>Impuestos</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "<strong>The ordered quantity has been updated.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "<strong>Total:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Precio Unitario</strong>"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference:</strong>"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_warning_purchase
msgid "A warning can be set on a product or a customer (Purchase)"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:53
#, python-format
msgid "All"
msgstr ""

#. module: purchase
#: selection:res.company,po_lock:0
msgid "Allow to edit purchase orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Allows you to specify an analytic account on purchase order lines."
msgstr "Permite escoger una cuenta analítica en órdenes de compra."

#. module: purchase
#: model:res.groups,name:purchase.group_analytic_accounting
msgid "Analytic Accounting for Purchases"
msgstr "Contabilidad Analítica para Compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_analytic_account_for_purchases
msgid "Analytic accounting for purchases"
msgstr "Contabilidad analítica para compras"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_line_purchase_id
msgid ""
"Associated Purchase Order. Filled in automatically when a PO is chosen on "
"the vendor bill."
msgstr ""
"Orden de Compra Asociada. Rellenado automáticamente cuando se elige una OC "
"en la factura del proveedor."

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_average_delivery_time
msgid "Average Delivery Time"
msgstr "Plazo Promedio de Entrega"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_average
msgid "Average Price"
msgstr "Precio Promedio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_default_purchase_method
msgid "Bill Control"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_invoice_lines
msgid "Bill Lines"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_invoiced
msgid "Billed Qty"
msgstr "Cant. Facturada"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Billed Quantity:"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_status
msgid "Billing Status"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_ids
msgid "Bills"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Bills Received"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "Blocking Message"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/stock.py:73
#: model:stock.location.route,name:purchase.route_warehouse0_buy
#, python-format
msgid "Buy"
msgstr "Comprar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_pull_id
msgid "Buy rule"
msgstr "Regla de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"By default, vendor prices can be set manually in the product detail form. If "
"your vendors provide you with pricelist files, this option allows you to "
"easily import them into the system from ‘Purchase > Vendor Pricelists’ menu."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Calls for tenders are used when you want to generate requests for quotations "
"to several vendors for a given set of products. You can configure per "
"product if you directly do a Request for Quotation to one vendor or if you "
"want a Call for Tenders to compare offers from several vendors."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase
#: code:addons/purchase/controllers/portal.py:55
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#, python-format
msgid "Cancelled"
msgstr "Cancelada"

#. module: purchase
#: code:addons/purchase/models/purchase.py:737
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr ""
"No se puede eliminar una línea de pedido de venta que esté en estado '%s'."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Click here to record a vendor bill."
msgstr "Haga clic para grabar una nueva factura de proveedor."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Click to create a quotation that will be converted into a purchase order."
msgstr ""
"Haga clic para crear una cotización que será convertida en una orden de "
"compra."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "Click to create a request for quotation."
msgstr "Haga clic para crear una solicitud de cotización."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid "Click to create a vendor bill."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid "Click to define a new product."
msgstr "Haga clic para definit un nuevo producto."

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid "Click to record a vendor bill related to this purchase."
msgstr "Haga clic para registrar una nueva factura de proveedor a esta compra."

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Compañía"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Configuración"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
msgid "Configure Purchases"
msgstr "Configurar Compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr "Confirmar Orden"

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Confirm purchase orders in one step"
msgstr "Confirmar órdenes de compra en un solo paso"

#. module: purchase
#: selection:res.company,po_lock:0
msgid "Confirmed purchase orders are not editable"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
msgid "Contact"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_control
msgid "Control"
msgstr "Control"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_method
msgid "Control Policy"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_move_created_purchase_line_id
msgid "Created Purchase Order Line"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_date
msgid "Created on"
msgstr "Creado en"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_company_currency_id
msgid "Currency"
msgstr "Divisa"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_approve
msgid "Date Approved"
msgstr "Fecha de Aprobación"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report_date_order
msgid "Date on which this document has been created"
msgstr "Fecha en la que este documento fue creado."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Define your terms and conditions ..."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_type_id
msgid "Deliver To"
msgstr "Entregar a"

#. module: purchase
#: selection:res.config.settings,default_purchase_method:0
msgid "Delivered quantities"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Deliveries & Invoices"
msgstr "Entregas y Facturas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_name
msgid "Description"
msgstr "Descripción"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Description for Vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Destination Location Type"
msgstr "Tipo de destino de la ubicacion"

#. module: purchase
#: selection:purchase.report,state:0
msgid "Done"
msgstr "Realizado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation_amount
msgid "Double validation amount"
msgstr "Doble validación en compras"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_move_dest_ids
msgid "Downstream Moves"
msgstr ""

#. module: purchase
#: selection:purchase.report,state:0
msgid "Draft RFQ"
msgstr "SdC Borrador"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_dest_address_id
msgid "Drop Ship Address"
msgstr "Dirección de Entrega"

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo electrónico."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_purchase_id
msgid ""
"Encoding help. When selected, the associated purchase order lines are added "
"to the vendor bill. Several PO can be selected."
msgstr ""
"Ayuda Codificación. Cuando se selecciona, se añaden las líneas de órdenes de "
"compra asociadas a la factura de proveedor. Varios PO se puede seleccionar."

#. module: purchase
#: code:addons/purchase/models/purchase.py:577
#, python-format
msgid "Extra line with %s "
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Future Activities"
msgstr ""

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr "Consigue 2 niveles de aprobaciones para confirmar una orden de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Get warnings in orders for products or vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_weight
msgid "Gross Weight"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr "Ocultar líneas canceladas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "How to import"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"If enabled, activates 3-way matching on vendor bills : the items must be "
"received in order to pay the invoice."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Import vendor pricelists"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_incoterm_id
msgid "Incoterm"
msgstr "Incoterm"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Los términos de comercio internacional son una serie de condiciones "
"comerciales usadas en las transacciones internacionales."

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Invoicing"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_is_installed_sale
msgid "Is Installed Sale"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_is_shipped
msgid "Is Shipped"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Late Activities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_double_validation
msgid "Levels of Approvals *"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_lock_confirmed_po
msgid "Lock Confirmed Orders"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:56
#: selection:purchase.order,state:0
#, python-format
msgid "Locked"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Logistics"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"Make sure you only pay bills for which you received the goods you ordered"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_manage_vendor_price
msgid "Manage Vendor Price"
msgstr "Gestionar Precios del Proveedor"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Manage your purchase agreements (call for tenders, blanket orders)"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Manager"
msgstr "Responsable"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Managers must approve orders"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_use_po_lead
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for reordering products,they will be scheduled that many days earlier "
"to cope with unexpected vendor delays."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_warn_msg
#: model:ir.model.fields,field_description:purchase.field_res_users_purchase_warn_msg
msgid "Message for Purchase Order"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_line_warn_msg
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_line_warn_msg
msgid "Message for Purchase Order Line"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_double_validation_amount
msgid "Minimum Amount"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Most propositions of purchase orders are created automatically\n"
"                by Odoo based on inventory needs."
msgstr ""
"La mayoría de las proposiciones de las órdenes de compra se crean "
"automatically\n"
"por Odoo basado en las necesidades de inventario."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "My Activities"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:44
#, python-format
msgid "Name"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:43
#, python-format
msgid "Newest"
msgstr ""

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "No Bill to Receive"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "No Message"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_line_product_image
msgid ""
"Non-stored related field to allow portal user to see the image of the "
"product he has ordered"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Notas"

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Nothing to Bill"
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On ordered quantities"
msgstr "En cantidades pedidas"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_method
#: model:ir.model.fields,help:purchase.field_product_template_purchase_method
msgid ""
"On ordered quantities: control bills based on ordered quantities.\n"
"On received quantities: control bills based on received quantity."
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On received quantities"
msgstr "En cantidades recibidas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_order_approval
msgid "Order Approval"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Ordered Quantity:"
msgstr ""

#. module: purchase
#: selection:res.config.settings,default_purchase_method:0
msgid "Ordered quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_orderpoint_id
msgid "Orderpoint"
msgstr ""

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
msgid "PO_${(object.name or '').replace('/','_')}"
msgstr "OC_${(object.name or '').replace('/','_')}"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_payment_term_id
msgid "Payment Terms"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category_property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template_property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Cuenta diferencia de precio"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr "Imprimir SdC"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_group_id
msgid "Procurement Group"
msgstr "Grupo de Adquisición"

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_group
msgid "Procurement Requisition"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_rule
msgid "Procurement Rule"
msgstr "Regla de Adquisición"

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Product"
msgstr "Producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_image
msgid "Product Image"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_product_action
#: model:ir.ui.menu,name:purchase.product_product_menu
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Products"
msgstr "Productos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr "Proporcionar un mecanismo de doble validación para las compras"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase"
msgstr "Compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_module_purchase_requisition
msgid "Purchase Agreements"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Purchase Analytics"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Purchase Bills"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:54
#: model:ir.actions.report,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_id
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_warn
#: model:ir.model.fields,field_description:purchase.field_res_users_purchase_warn
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#: model:res.request.link,name:purchase.req_link_purchase_order
#, python-format
msgid "Purchase Order"
msgstr "Orden de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order Confirmation #"
msgstr "Orden de Compra Confirmación #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Order Fiscal Position"
msgstr "Orden de Compra Posición Fiscal"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_lock
msgid "Purchase Order Modification"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_po_lock
msgid "Purchase Order Modification *"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_lock
#: model:ir.model.fields,help:purchase.field_res_config_settings_po_lock
msgid ""
"Purchase Order Modification used when you want to purchase order editable "
"after confirm"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.actions.act_window,name:purchase.purchase_order_action_generic
#: model:ir.model.fields,field_description:purchase.field_stock_picking_purchase_id
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_menu_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_home_purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr "Ordenes de Compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
msgid "Purchase Orders #"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Orders Statistics"
msgstr "Estadísticas Ordenes de Compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that have been invoiced."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that include lines not invoiced."
msgstr "Ordenes de compra que incluyen líneas no facturadas."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_to_resupply
msgid "Purchase to resupply this warehouse"
msgstr "Comprar para reabastecer este almacén"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_line_product_tree
#: model:ir.ui.menu,name:purchase.menu_purchase_root
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
#: model_terms:ir.ui.view,arch_db:purchase.view_warehouse_orderpoint_purchase_form
msgid "Purchases"
msgstr "Compras"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""
"Ponga una dirección si desea entregar directamente desde el proveedor al "
"cliente. De lo contrario, mantenga vacía para entregar a su propia compañía."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Quantities billed by vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Quotations"
msgstr "Presupuestos"

#. module: purchase
#: selection:purchase.order,state:0
msgid "RFQ"
msgstr ""

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_${(object.name or '').replace('/','_')}"
msgstr "SdC_${(object.name or '').replace('/','_')}"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr "SdC y Compras"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.track_po_line_template
msgid "Received Quantity:"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_open_picking
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_count
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_ids
msgid "Receptions"
msgstr "Recepciones"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Reference"
msgstr "Referencia"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a "
"sales order)"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.purchase_report
msgid "Reporting"
msgstr ""

#. module: purchase
#: model:ir.actions.report,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr "Solicitud de Cotización"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr "Solicitud de Cotización #"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid "Request your vendors to deliver to your customers"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
msgid "Requests for Quotation"
msgstr "Solicitud de Cotización"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_report_user_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Responsible"
msgstr "Responsable"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "Schedule receivings earlier to avoid delays"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr "Buscar Orden de Compra"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_use_po_lead
msgid "Security Lead Time for Purchase"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_line_warn
#: model:ir.model.fields,help:purchase.field_product_template_purchase_line_warn
#: model:ir.model.fields,help:purchase.field_res_partner_purchase_warn
#: model:ir.model.fields,help:purchase.field_res_users_purchase_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr "Enviar OC por correo electrónico"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send RFQ by Email"
msgstr "Enviar SdC por correo electrónico"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_sequence
msgid "Sequence"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_account
msgid "Set analytic accounts in purchase orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set date to all order lines"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Configuración"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Shipment"
msgstr "Envío"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Estado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_subtotal
msgid "Subtotal"
msgstr "Subtotal"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_property_purchase_currency_id
#: model:ir.model.fields,field_description:purchase.field_res_users_property_purchase_currency_id
msgid "Supplier Currency"
msgstr "Moneda del Proveedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_tax
msgid "Tax"
msgstr "Impuesto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_taxes_id
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Taxes"
msgstr "Impuestos"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Taxes:"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr "Campo técnico usado para mostrar la dirección del Drop Ship."

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_notes
msgid "Terms and Conditions"
msgstr "Términos y Condiciones"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"The product form contains detailed information to improve the\n"
"                purchase process: prices, procurement logistics, accounting "
"data,\n"
"                available vendors, etc."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:649
#, python-format
msgid ""
"The quantities on your purchase order indicate less than billed. You should "
"ask for a refund. "
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:878
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template_property_account_creditor_price_difference
msgid ""
"This account will be used to value price difference between purchase price "
"and cost price."
msgstr ""
"Esta cuenta se utilizará para valorar la diferencia de precios entre el "
"precio de compra y precio de coste."

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This adds a dropshipping route to apply on products in order to request your "
"vendors to deliver to your customers. A product to dropship will generate a "
"purchase request for quotation once the sales order confirmed. This is a on-"
"demand flow. The requested delivery address will be the customer delivery "
"address and not your warehouse."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "This changes the scheduled date of all order lines to the given date"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_default_purchase_method
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_purchase
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "This note will show up on purchase orders."
msgstr ""

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:203
#, python-format
msgid "This vendor bill has been created from: %s"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/account_invoice.py:217
#, python-format
msgid "This vendor bill has been modified from: %s"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Click to create a new RfQ."
msgstr ""
"Este proveedor no tiene ninguna orden de compra. Haga clic aquí para crear "
"una nueva SdC."

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_picking_type_id
msgid "This will determine operation type of incoming shipment"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Today Activities"
msgstr ""

#. module: purchase
#: code:addons/purchase/controllers/portal.py:45
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_total
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_orders
#, python-format
msgid "Total"
msgstr "Total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total Untaxed amount"
msgstr "Subtotal"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_picking
msgid "Transfer"
msgstr "Transferir"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Unlock"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.portal_my_purchase_order
msgid "Untaxed Amount:"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid ""
"Use this menu to control the invoices to be received from your\n"
"            vendors. When registering a new bill, set the purchase order\n"
"            and Odoo will fill the bill automatically according to ordered\n"
"            or received quantities."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Usuario"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_config_settings_company_currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr "Proveedor"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_manage_vendor_price
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Vendor Pricelists"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management_supplier_name
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Vendors"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_volume
msgid "Volume"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: selection:purchase.order,invoice_status:0
msgid "Waiting Bills"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase.field_purchase_report_picking_type_id
msgid "Warehouse"
msgstr "Almacén"

#. module: purchase
#: selection:product.template,purchase_line_warn:0
#: selection:res.partner,purchase_warn:0
msgid "Warning"
msgstr ""

#. module: purchase
#: code:addons/purchase/models/purchase.py:268
#: code:addons/purchase/models/purchase.py:808
#, python-format
msgid "Warning for %s"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
msgid "Warning on the Purchase Order"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_product_supplier_inherit
msgid "Warning when Purchasing this Product"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_config_settings_group_warning_purchase
msgid "Warnings"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_website_url
msgid "Website URL"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_product_action
msgid ""
"You must define a product for everything you purchase, whether\n"
"                it's a physical product, a consumable or services you buy "
"to\n"
"                subcontractors."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_config_settings_view_form_stock
msgid "days"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_config_settings
msgid "res.config.settings"
msgstr ""
