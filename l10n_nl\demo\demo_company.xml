<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_nl" model="res.partner">
        <field name="name">NL Company</field>
        <field name="vat">NL43603B11</field>
        <field name="street">Bloemstraat 42</field>
        <field name="city">Groningen</field>
        <field name="country_id" ref="base.nl"/>
        <field name="state_id" ref="base.state_nl_bq3"/>
        <field name="zip">9700</field>
        <field name="phone">+31 6 12345678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.nlexample.com</field>
        <field name="l10n_nl_kvk">12345678</field>
    </record>

    <record id="demo_company_nl" model="res.company">
        <field name="name">NL Company</field>
        <field name="partner_id" ref="partner_demo_company_nl"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_nl')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_nl.demo_company_nl'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_nl.l10nnl_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_nl.demo_company_nl')"/>
    </function>
</odoo>
