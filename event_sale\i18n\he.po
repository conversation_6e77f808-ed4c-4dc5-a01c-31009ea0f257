# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Lila<PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            ייתכן שיהיה צורך בפעולות ידניות."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">מכירות</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>שינוי רישום למשתתף</span>"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,help:event_sale.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "תיאור של הכרטיס שאתה רוצה להעביר ללקוחות שלך."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"מוצר מנוהל מלאי הוא מוצר שעבורו מתבצע ניהול מלאי במערכת. יש להתקין את יישום "
"המלאי.מוצר לא מנוהל מלאי הוא מוצר אשר לא מתבצע עבורו ניהול מלאי במערכת.שירות"
" הוא מוצר לא חומרי שאתה מספק."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "כל שורות הזמנת המכירה מצביעות על אירוע זה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "מספר משתתפים"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "משתתפים"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr "לפני אישור"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "קמפיין"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Cancel"
msgstr "בטל"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr "בחר אירוע ותיווצר הרשמה אוטומטית לאירוע זה."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr "בחר כרטיס לאירוע ותיווצר הרשמה אוטומטית לכרטיס אירוע זה."

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr "הגדר אירוע"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Confirm"
msgstr "אשר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__description
msgid "Description"
msgstr "תיאור"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "ערוך פרטי משתתף באישור המכירה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "ערוך שורת משתתף באישור המכירה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "עורך"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "דוא\"ל"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
msgid "Event"
msgstr "אירוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "מגדיר אירוע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ok
msgid "Event Ok"
msgstr "אירוע OK"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:product.product,name:event_sale.product_product_event
msgid "Event Registration"
msgstr "הרשמה לאירוע"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "הרשמות לאירוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type_ticket
msgid "Event Template Ticket"
msgstr "תבניות כרטיסים לארוע"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
#: model:ir.model.fields.selection,name:event_sale.selection__product_template__detailed_type__event
msgid "Event Ticket"
msgstr "כרטיס לאירוע"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "כרטיסים לאירוע"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "חריג:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__free
msgid "Free"
msgstr "פנוי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__is_paid
msgid "Is Paid"
msgstr "שולם"

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "בינוני"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr "נייד"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "שם"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__to_pay
msgid "Not Paid"
msgstr "לא שולם"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr "אישור"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Date"
msgstr "תאריך הזמנה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Ref"
msgstr "מזהה הזמנה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "הרשמה מקורית"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__paid
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Paid"
msgstr "שולם"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__payment_status
msgid "Payment Status"
msgstr "סטטוס תשלום"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "טלפון"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Price"
msgstr "מחיר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "הורדת מחיר"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "הורדת מחיר מיסים כלולים"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__product_id
msgid "Product"
msgstr "מוצר"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr "תבנית מוצר "

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "הרשמה"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "הרשמות לעריכה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Sale Order"
msgstr "הזמנת לקוח"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "מכירות"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "מכירות (ללא מיסים)"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "סיום מכירה"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
msgid "Sales Order"
msgstr "הזמנת לקוח"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "שורת הזמנת לקוח"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr "תחילת מכירות"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "מקור"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "הכרטיס שונה מ-"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "סך המכירות לאירוע זה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "עסקה"

#. module: event_sale
#: model:product.product,uom_name:event_sale.product_product_event
msgid "Units"
msgstr "יחידה"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr "אנא ספק פרטים על ההרשמות"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "ל"
