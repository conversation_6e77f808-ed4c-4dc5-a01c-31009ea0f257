<templates xml:space="preserve">
    <t t-name="iter-items">
        <t t-foreach="[3, 2, 1]" t-as="item">
[<t t-esc="item_index"/>: <t t-esc="item"/> <t t-esc="item_value"/>]</t>
    </t>
    <result id="iter-items">
[0: 3 3]
[1: 2 2]
[2: 1 1]
    </result>

    <t t-name="iter-position">
        <t t-foreach="5" t-as="item">
-<t t-if="item_first"> first</t><t t-if="item_last"> last</t> (<t t-esc="item_parity"/>)</t>
    </t>
    <result id="iter-position">
- first (even)
- (odd)
- (even)
- (odd)
- last (even)
    </result>

    <!-- test integer param -->
    <t t-name="iter-int">
        <t t-foreach="3" t-as="item">
[<t t-esc="item_index"/>: <t t-esc="item"/> <t t-esc="item_value"/>]</t>
    </t>
    <result id="iter-int">
[0: 0 0]
[1: 1 1]
[2: 2 2]
    </result>

    <!-- test dict param -->
    <t t-name="iter-dict">
        <t t-foreach="value" t-as="item">
[<t t-esc="item_index"/>: <t t-esc="item"/> <t t-esc="item_value"/> - <t t-esc="item_parity"/>]</t>
    </t>
    <params id="iter-dict">{"value": {"a": 1, "b": 2, "c": 3}}</params>
    <result id="iter-dict">
[0: a 1 - even]
[1: b 2 - odd]
[2: c 3 - even]
    </result>
</templates>
