# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product_expiry
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-12-22 17:35+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_alert_date
msgid "Alert Date"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_use_date
msgid "Best before Date"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_pain
#: model:product.template,name:product_expiry.product_product_pain_product_template
msgid "Bread"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_lait
#: model:product.template,name:product_expiry.product_product_lait_product_template
msgid "Cow milk"
msgstr ""

#. module: product_expiry
#: model_terms:ir.ui.view,arch_db:product_expiry.view_move_form_expiry
msgid "Dates"
msgstr "Fechas"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_life_date
msgid "End of Life Date"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_jambon
#: model:product.template,name:product_expiry.product_product_jambon_product_template
msgid "French cheese Camembert"
msgstr ""

#. module: product_expiry
#: model:product.product,name:product_expiry.product_product_from
#: model:product.template,name:product_expiry.product_product_from_product_template
msgid "Ham"
msgstr ""

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Lote/Nº de serie"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_template_alert_time
msgid "Product Alert Time"
msgstr "Tiempo de alerta producto"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_template_life_time
msgid "Product Life Time"
msgstr "Tiempo de vida producto"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_template_removal_time
msgid "Product Removal Time"
msgstr "Tiempo eliminación producto"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_product_template
msgid "Product Template"
msgstr "Plantilla producto"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_product_template_use_time
msgid "Product Use Time"
msgstr "Duración del producto"

#. module: product_expiry
#: model:ir.model,name:product_expiry.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: product_expiry
#: model:ir.model.fields,field_description:product_expiry.field_stock_production_lot_removal_date
#: model:ir.model.fields,field_description:product_expiry.field_stock_quant_removal_date
msgid "Removal Date"
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_alert_date
msgid ""
"This is the date on which an alert should be notified about the goods with "
"this Serial Number."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_life_date
msgid ""
"This is the date on which the goods with this Serial Number may become "
"dangerous and must not be consumed."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_removal_date
msgid ""
"This is the date on which the goods with this Serial Number should be "
"removed from the stock."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_stock_production_lot_use_date
msgid ""
"This is the date on which the goods with this Serial Number start "
"deteriorating, without being dangerous yet."
msgstr ""

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_template_alert_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before an "
"alert should be notified."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que se notifique una alerta."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_template_life_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods may become dangerous and must not be consumed."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes se conviertan en peligrosos y no deban ser consumidos."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_template_removal_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods should be removed from the stock."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes deban eliminarse del stock."

#. module: product_expiry
#: model:ir.model.fields,help:product_expiry.field_product_template_use_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods starts deteriorating, without being dangerous yet."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes se empiecen a deteriorar, sin ser peligroso aún."
