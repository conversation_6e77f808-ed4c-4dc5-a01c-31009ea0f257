# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# Wichanon Jamwutthipreecha, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2022\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
" \n"
"            อาจจำเป็นต้องดำเนินการด้วยตัวเอง"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr "<span class=\"o_stat_text\">การซื้อ</span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\">การขาย</span>"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "ข้อยกเว้นที่เกิดขึ้นในคำสั่งซื้อ:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "ข้อยกเว้นที่เกิดขึ้นในใบสั่งขาย:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "ข้อยกเว้น:"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "สร้างไลน์การซื้อ"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"ถ้าติ๊ก ทุกครั้งที่คุณขายสินค้านี้ผ่าน SO RfQ "
"จะถูกสร้างขึ้นโดยอัตโนมัติเพื่อซื้อสินค้า เคล็ดลับ: "
"อย่าลืมตั้งผู้จำหน่ายบนสินค้า"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "อาจจำเป็นต้องดำเนินการด้วยตนเอง"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr "จำนวนคำสั่งซื้อที่สร้างขึ้น"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr "จำนวนแหล่งขาย"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "จำนวนการสร้างรายการการขาย"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "จำนวนการสั่งซื้อลดลง!"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "รายการขายต้นทาง"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product Template"
msgstr "เทมเพลตสินค้า"

#. module: sale_purchase
#: model:ir.model.constraint,message:sale_purchase.constraint_product_template_service_to_purchase
msgid "Product that is not a service can not create RFQ."
msgstr "สินค้าที่ไม่ใช่บริการไม่สามารถสร้าง RFQ ได้"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "คำสั่งซื้อ"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "ไลน์คำสั่งซื้อ"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr "คำสั่งซื้อที่สร้างจาก%s"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"ไลน์การซื้อที่สร้างโดยรายการขายในการยืนยันคำสั่งนี้ หรือเมื่อจำนวนเพิ่มขึ้น"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "สร้างคำสั่งใหม่"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "คำสั่งขาย"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "คำสั่งขาย"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "ไลน์คำสั่งขาย"

#. module: sale_purchase
#: code:addons/sale_purchase/models/purchase_order.py:0
#, python-format
msgid "Sources Sale Orders %s"
msgstr "แหล่งคำสั่งขาย%s"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr "บริการสัญญาช่วง"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""
"ไม่มีผู้ขายที่เกี่ยวข้องกับสินค้า %s โปรดกำหนดผู้จำหน่ายสำหรับสินค้านี้"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr "คุณกำลังลดจำนวนการสั่งซื้อ! อย่าลืมอัปเดตคำสั่งซื้อด้วยตนเองหากจำเป็น"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "ยกเลิก"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "ของ"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "คำสั่งแทนที่ของ"
