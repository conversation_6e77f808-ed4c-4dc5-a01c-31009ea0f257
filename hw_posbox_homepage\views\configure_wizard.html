{% extends "layout.html" %}
{% from "loading.html" import loading_block_ui %}
{% block head %}
    <script>
        $(document).ready(function () {
            function changePage(key) {
                $('.progressbar li[data-key=' + key + ']').prevAll().addClass('completed');
                $('.progressbar li[data-key=' + key + ']').nextAll().removeClass('active completed');
                $('.progressbar li[data-key=' + key + ']').addClass('active').removeClass('completed');
                $('.config-steps.active').removeClass('active').addClass('o_hide');
                $('.config-steps[data-key=' + key + ']').removeClass('o_hide').addClass('active');
            }
            $('.next-btn').on('click', function (ev) {
                changePage($(ev.target).data('key'));
            });
            $('#config-form').submit(function(e){
                e.preventDefault();
                $('.loading-block').removeClass('o_hide');
                $.ajax({
                    url: '/step_configure',
                    type: 'post',
                    data: $('#config-form').serialize(),
                }).done(function (url) {
                    $('.loading-block').addClass('o_hide');
                    changePage('done');
                    if(url) {
                        if ($('#iotname')[0].defaultValue == $('#iotname')[0].value){
                            var cpt = 30;
                        }else{
                            var cpt = 100;
                        }
                        setInterval(function(){
                            if(cpt === 0){
                                window.location = url
                            } else {
                                $('.redirect-message').html('You will be redirected to <a href="'+ url +'">' + url + '</a> in <b>' + cpt + '</b> seconds');
                                --cpt;
                            }
                        } , 1000);
                    }
                }).fail(function () {
                    $('.error-message').text('Error in submitting data');
                    $('.loading-block').addClass('o_hide');
                });
            });
        });
    </script>
    <style>
        .config-steps .title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .progressbar {
            counter-reset: step;
            z-index: 1;
            position: relative;
            display: inline-block;
            width: 100%;
            padding: 0;
        }
        .progressbar li{
            list-style-type: none;
            float: left;
            width: 33.33%;
            position:relative;
            text-align: center;
            font-size: 0.8rem;
        }
        .progressbar li:before {
            content:counter(step);
            counter-increment: step;
            height:30px;
            width:30px;
            line-height: 30px;
            border: 2px solid #ddd;
            display:block;
            text-align: center;
            margin: 0 auto 6px auto;
            border-radius: 50%;
            background-color: white;
            color: #ddd;
            font-size: 1rem;
        }
        .progressbar li:after {
            content:'';
            position: absolute;
            width:100%;
            height:2px;
            background-color: #ddd;
            top: 15px;
            left: -50%;
            z-index: -1;
        }
        .progressbar li:first-child:after {
            content:none;
        }
        .progressbar li.active, .progressbar li.completed {
            color:#875A7B;
        }
        .progressbar li:last-child:before {
            content: '✔';
        }
        .progressbar li.active:before {
            border-color:#875A7B;
            background-color:#875A7B;
            color: #fff;
        }
        .progressbar li.completed:before{
            border-color:#875A7B;
            background-color: #fff;
            color: #875A7B;
        }
        .progressbar li.active + li:after{
            background-color:#875A7B;
        }
        .footer-buttons {
            display: inline-block;
            width: 100%;
            margin-top: 20px;
        }
    </style>
{% endblock %}
{% block content %}
    <h2 class="text-center">Configure IoT Box</h2>
    <ul class="progressbar">
        <li class="active" data-key="server">Connect to Odoo</li>
        <li data-key="wifi">Connect to Internet</li>
        <li data-key="done">Done</li>
    </ul>
    <form id="config-form" style="margin-top: 20px;" action='/step_configure' method='POST'>
        <div>
            <div class="config-steps active" data-key="server">
                <table align="center">
                    <tr>
                        <td>IoT Box Name</td>
                        <td><input type="text" id="iotname" name="iotname" value="{{ hostname }}"></td>
                    </tr>
                    <tr>
                        <td>Server token</td>
                        <td><input type="text" name="token" value="{{ server }}" placeholder="Paste your copied token here"></td>
                    </tr>
                </table>
                <div class="text-center font-small" style="margin: 10px auto;">
                    Server token is not mandatory for the community version.
                </div>
                <div class="footer-buttons">
                    <a class="btn next-btn" style="float: right" data-key="wifi">Next</a>
                </div>
            </div>
            <div class="config-steps wifi-step o_hide" data-key="wifi">
                <table align="center">
                    <tr>
                        <td>Wifi Network</td>
                        <td>
                            <select name="essid">
                                {% for id in ssid -%}
                                    <option value="{{ id }}">{{ id }}</option>
                                {%- endfor %}
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>Password</td>
                        <td><input type="password" name="password" placeholder="Optional"/></td>
                    </tr>
                    <input type="hidden" name="persistent" value="True"/>
                </table>
                <div class="footer-buttons">
                    <a class="btn next-btn" data-key="server">Previous</a>
                    <input class="btn" style="float: right" type="submit" value="Connect"/>
                </div>
            </div>
            <div class="config-steps o_hide" data-key="done">
                <h3 class="text-center" style="margin: 0;">✔ Nice! Your configuration is done.</h3>
                <p class="text-center redirect-message" />
            </div>
        </div>
        {{ loading_block_ui(loading_message) }}
    </form>
{% endblock %}
