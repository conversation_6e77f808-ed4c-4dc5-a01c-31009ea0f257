<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="DebugWidget" owl="1">
        <Draggable limitArea="'.pos'">
            <div class="debug-widget">
                <header class="drag-handle">
                    <h1>Debug Window</h1>
                </header>
                <div class="toggle" t-on-click="trigger('toggle-debug-widget')" title="Dismiss"
                     role="img" aria-label="Dismiss"><i class="fa fa-times" /></div>
                <div class="content">
                    <p class="category">Electronic Scale</p>
                    <ul>
                        <li>
                            <input t-model="state.weightInput" type="text" class="weight"></input>
                        </li>
                        <li class="button set_weight" t-on-click="setWeight">Set Weight</li>
                        <li class="button reset_weight" t-on-click="resetWeight">Reset</li>
                    </ul>

                    <p class="category">Barcode Scanner</p>
                    <ul>
                        <li>
                            <input t-model="state.barcodeInput" type="text" class="ean"></input>
                        </li>
                        <li class="button barcode" t-on-click="barcodeScan">Scan</li>
                        <li class="button custom_ean" t-on-click="barcodeScanEAN">Scan EAN-13</li>
                    </ul>

                    <p class="category">Orders</p>

                    <ul>
                        <li class="button" t-on-click="deleteOrders">
                            Delete Paid Orders
                        </li>
                        <li class="button" t-on-click="deleteUnpaidOrders">
                            Delete Unpaid Orders
                        </li>
                        <li t-if="!state.isPaidOrdersReady" class="button"
                            t-on-click="preparePaidOrders">
                            Export Paid Orders
                        </li>
                        <a t-else="" t-att-download="paidOrdersFilename" t-att-href="paidOrdersURL"
                           t-on-click="state.isPaidOrdersReady = !state.isPaidOrdersReady">
                            <li class="button">
                                Download Paid Orders
                            </li>
                        </a>
                        <li t-if="!state.isUnpaidOrdersReady" class="button"
                            t-on-click="prepareUnpaidOrders">
                            Export Unpaid Orders
                        </li>
                        <a t-else="" t-att-download="unpaidOrdersFilename"
                           t-att-href="unpaidOrdersURL"
                           t-on-click="state.isUnpaidOrdersReady = !state.isUnpaidOrdersReady">
                            <li class="button">
                                Download Unpaid Orders
                            </li>
                        </a>
                        <li class="button import_orders" style="position:relative">
                            Import Orders
                            <input t-on-change="importOrders" type="file"
                                   style="opacity:0;position:absolute;top:0;left:0;right:0;bottom:0;margin:0;cursor:pointer" />
                        </li>
                    </ul>

                    <p class="category">Hardware Status</p>
                    <ul>
                        <li class="status weighing">Weighing</li>
                        <li class="button display_refresh" t-on-click="refreshDisplay">
                            Refresh Display
                        </li>
                    </ul>
                    <p class="category">Hardware Events</p>
                    <ul>
                        <li class="event" t-ref="open_cashbox">Open Cashbox</li>
                        <li class="event" t-ref="print_receipt">Print Receipt</li>
                        <li class="event" t-ref="scale_read">Read Weighing Scale</li>
                    </ul>
                    <p class="category">Others</p>
                    <ul>
                        <li class="event">
                            <span>Buffer: </span>
                            <t t-esc="bufferRepr" />
                        </li>
                    </ul>
                </div>
            </div>
        </Draggable>
    </t>

</templates>
