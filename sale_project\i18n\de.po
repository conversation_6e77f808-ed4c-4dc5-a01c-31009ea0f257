# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <a.<PERSON><PERSON><PERSON>@gastronovi.com>, 2021
# <PERSON><PERSON><PERSON><PERSON>esselbosch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Create Invoice"
msgstr "Rechnung erstellen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Bei Auftrag erstellen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Projekt anzeigen"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Generiertes Projekt"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Generierte Aufgabe"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Hat abzurechnenden Verkaufsauftrag"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice as soon as this service is sold, and create a task in an existing "
"project to track the time spent."
msgstr ""
"Stellen Sie eine Rechnung aus, sobald diese Dienstleistung verkauft ist, und"
" erstellen Sie eine Aufgabe in einem bestehenden Projekt, um die "
"aufgewendete Zeit zu erfassen."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create a "
"project for the order with a task for each sales order line to track the "
"time spent."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald eine Leistung verkauft"
" wurde, und erstellen Sie ein Projekt für den Auftrag mit einer Aufgabe für "
"jede Auftragszeile, um den Zeitaufwand zu verfolgen."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold, and create an "
"empty project for the order to track the time spent."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald eine Leistung verkauft"
" wurde und erstellen Sie ein leeres Projekt für den Auftrag um den "
"Zeitaufwand zu verfolgen."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr ""
"Stellen Sie die bestellten Mengen in Rechnung, sobald diese Dienstleistung "
"verkauft ist."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__is_service
msgid "Is a Service"
msgstr "Ist eine Dienstleistung"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "Neu"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "None"
msgstr "Keine"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Nichts"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Anzahl der Projekte"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Anzahl Rechnungen"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"Bei der Auftragsbestätigung kann dieses Produkt ein Projekt und/oder eine Aufgabe erzeugen. Anhand dieser können Sie die von Ihnen verkaufte Dienstleistung verfolgen. \n"
"„Im Projekt des Verkaufsauftrags“: Verwendet das konfigurierte Projekt des Verkaufsauftrags, falls definiert, oder erstellt ein neues Projekt auf der Grundlage der ausgewählten Vorlage."

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product"
msgstr "Produkt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product Template"
msgstr "Produktvorlage"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projekt"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Projekt & Aufgabe"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Projektvorlage"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__project_id
msgid "Project generated by the sales order item"
msgstr "Von der Verkaufsauftragsposition erzeugtes Projekt"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Verkaufsauftrag des Projekts"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Projekte"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Projekte in diesem Verkaufsauftrag."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
msgid "Sale Order"
msgstr "Verkaufsauftrag"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_search
msgid "Sales Order Id"
msgstr "Auftrags-ID"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_task_view_search
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "Verkaufsauftragsposition"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added, in "
"order to be invoiced to your customer."
msgstr ""
"Verkaufsauftragsposition, zu der die für diese Aufgabe aufgewendete Zeit "
"hinzugefügt wird, um sie Ihrem Kunden in Rechnung zu stellen."

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkaufsauftragszeile"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__is_service
msgid ""
"Sales Order item should generate a task and/or a project, depending on the "
"product settings."
msgstr ""
"Die Verkaufsauftragsposition sollte je nach den Produkteinstellungen eine "
"Aufgabe und/oder ein Projekt erzeugen."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item to which the project is linked. Link the timesheet entry to"
" the sales order item defined on the project. Only applies on tasks without "
"sale order item defined, and if the employee is not in the 'Employee/Sales "
"Order Item Mapping' of the project."
msgstr ""
"Verkaufsauftragsposition, mit der das Projekt verknüpft ist. Verknüpft den "
"Zeiterfassungseintrag mit der im Projekt definierten "
"Verkaufsauftragsposition. Gilt nur für Aufgaben, für die keine "
"Verkaufsauftragsposition definiert ist, und wenn der Mitarbeiter nicht in "
"der Zuordnung 'Mitarbeiter/Verkaufsauftragsposition' des Projekts enthalten "
"ist."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Verkaufsauftrag, mit dem das Projekt verknüpft ist."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Verkaufsauftrag, mit dem die Aufgabe verknüpft ist."

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "In Rechnung suchen"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "In Verkaufsauftrag suchen"

#. module: sale_project
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "Suche in Verkaufsauftragsposition"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"Wählen Sie ein abrechenbares Projekt aus, für das Aufgaben erstellt werden "
"können. Diese Einstellung muss für jedes Unternehmen festgelegt werden."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_project.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"Wählen Sie ein abrechenbares Projekt aus, das als Grundgerüst für das neu "
"erstellte Projekt beim Verkauf des aktuellen Produkts dienen soll. Phasen "
"und Aufgaben werden dupliziert."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""
"Wählen Sie ein nichtabrechenbares Projekt aus, um Aufgaben darin zu "
"erstellen."

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Aufgabe"

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"Task Created (%s): <a href=# data-oe-model=project.task data-oe-id=%d>%s</a>"
msgstr ""
"Aufgabe erstellt (%s): <a href=# data-oe-model=project.task data-oe-"
"id=%d>%s</a>"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Aufgabenwiederholung"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__task_id
msgid "Task generated by the sales order item"
msgstr "Von der Verkaufsauftragsposition erzeugte Aufgabe"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Aufgaben"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Aufgabenanalyse"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Aufgaben für diesen Auftrag"

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""
"Das Produkt %s sollte kein globales Projekt haben, da es ein Projekt "
"erzeugen wird."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"Das Produkt %s sollte weder ein Projekt noch eine Projektvorlage haben, da "
"es kein Projekt erzeugen wird."

#. module: sale_project
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"Das Produkt %s sollte keine Projektvorlage haben, da es eine Aufgabe in "
"einem globalen Projekt erzeugen wird."

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "There is nothing to invoice in this project."
msgstr "In diesem Projekt kann nichts abgerechnet werden."

#. module: sale_project
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"This task has been created from: <a href=# data-oe-model=sale.order data-oe-"
"id=%d>%s</a> (%s)"
msgstr ""
"Diese Aufgabe wurde erstellt von: <a href=# data-oe-model=sale.order data-"
"oe-id=%d>%s</a> (%s)"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Abzurechnen"

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Sie können die Auftragsposition %(order_id)s - %(product_id)s nicht mit "
"dieser Aufgabe verknüpfen, da es sich um eine weiterberechnete Auslage "
"handelt."

#. module: sale_project
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You have to unlink the task from the sale order item in order to delete it."
msgstr ""
"Sie müssen zuerst die Verknüpfung zwischen der Aufgabe und dem "
"Verkaufsauftrag aufheben, um diese zu löschen."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order item:"
msgstr "für Auftragsposition:"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "for sale order:"
msgstr "für Verkaufsauftrag:"
