# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * note
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-02 11:26+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Aktivan"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr "Arhiviraj"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr "Po kategoriji"

#. module: note
#: model_terms:ir.actions.act_window,help:note.note_tag_action
msgid "Click to add a new tag."
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Click to add a personal note."
msgstr "Klikni da dodaš ličnu zabilješku."

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_color
#: model:ir.model.fields,field_description:note.field_note_tag_color
msgid "Color Index"
msgstr "Indeks boje"

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Postavka"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_uid
#: model:ir.model.fields,field_description:note.field_note_stage_create_uid
#: model:ir.model.fields,field_description:note.field_note_tag_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_date
#: model:ir.model.fields,field_description:note.field_note_stage_create_date
#: model:ir.model.fields,field_description:note.field_note_tag_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_date_done
msgid "Date done"
msgstr "Datum završetka"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr "Obriši"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_display_name
#: model:ir.model.fields,field_description:note.field_note_stage_display_name
#: model:ir.model.fields,field_description:note.field_note_tag_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_fold
msgid "Folded by Default"
msgstr "Podrazumjevano je uvučeno"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Future Activities"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_id
#: model:ir.model.fields,field_description:note.field_note_stage_id
#: model:ir.model.fields,field_description:note.field_note_tag_id
msgid "ID"
msgstr "ID"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note___last_update
#: model:ir.model.fields,field_description:note.field_note_stage___last_update
#: model:ir.model.fields,field_description:note.field_note_tag___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_uid
#: model:ir.model.fields,field_description:note.field_note_stage_write_uid
#: model:ir.model.fields,field_description:note.field_note_tag_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_date
#: model:ir.model.fields,field_description:note.field_note_stage_write_date
#: model:ir.model.fields,field_description:note.field_note_tag_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Late Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_03
#: model:note.stage,name:note.note_stage_03
msgid "Later"
msgstr "Kasnije"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "My Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr "Novi"

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr "Zabilješka"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_memo
msgid "Note Content"
msgstr "Sadržaj zabilješke"

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr "Faza zabilješke"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_name
msgid "Note Summary"
msgstr "Rezime zabilješke"

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr "Oznaka zabilješke"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model:note.stage,name:note.note_stage_04
msgid "Notes"
msgstr "Zabilješke"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_user_id
#: model:ir.model.fields,field_description:note.field_note_stage_user_id
msgid "Owner"
msgstr "Vlasnik"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_user_id
msgid "Owner of the note stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_sequence
#: model:ir.model.fields,field_description:note.field_note_stage_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr "Nivo"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_name
msgid "Stage Name"
msgstr "Naziv faze"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr "Faza zabilješke"

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr "Faze"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr "Faze zabilješki"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_ids
msgid "Stages of Users"
msgstr "Faze korisnika"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag_name
msgid "Tag Name"
msgstr "Naziv oznake"

#. module: note
#: sql_constraint:note.tag:0
msgid "Tag name already exists !"
msgstr "Naziv oznake već postoji !"

#. module: note
#: model:ir.actions.act_window,name:note.note_tag_action
#: model:ir.model.fields,field_description:note.field_note_note_tag_ids
#: model:ir.ui.menu,name:note.notes_tag_menu
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_form
#: model_terms:ir.ui.view,arch_db:note.note_tag_view_tree
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Oznake"

#. module: note
#: model:note.stage,name:note.note_stage_02
msgid "This Week"
msgstr "Ova sedmica"

#. module: note
#: model:note.stage,name:note.demo_note_stage_01
#: model:note.stage,name:note.note_stage_01
msgid "Today"
msgstr "Danas"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Today Activities"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_02
msgid "Tomorrow"
msgstr "Sutra"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Use notes to organize personal tasks or notes. All\n"
"            notes are private; no one else will be able to see them. However\n"
"            you can share some notes with other people by inviting followers\n"
"            on the note. (Useful for meeting minutes, especially if\n"
"            you activate the pad feature for collaborative writings)."
msgstr ""
"Koristi zabilješke za organizaciju ličnih zadataka ili misli. Sve\n"
"zabilješke su privatne; niko osim tebe ih ne može vidjeti. Ali ako\n"
"želiš, imaš mogućnost podijeliti neke zabilješke sa ostalima tako \n"
"što ih dodaš u pratioce te zabilješke. (Korisno tokom sastanaka,\n"
"posebno ako se aktivira \"pad\" za kolaborativan rad)."

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_sequence
msgid "Used to order the note stages"
msgstr "Koristi se kod sortiranja faza"

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"You can customize how you process your notes/tasks by adding,\n"
"            removing or modifying columns."
msgstr ""
"Moguće je prilagoditi proces upravljanja zabilješkama, tako što\n"
"se kolone mogu dodavati ili brisati."
