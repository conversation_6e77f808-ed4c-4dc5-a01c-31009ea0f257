<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_partnerledger">
        <t t-call="web.html_container">
            <t t-call="web.internal_layout">
                <t t-set="data_report_margin_top" t-value="12"/>
                <t t-set="data_report_header_spacing" t-value="9"/>
                <t t-set="data_report_dpi" t-value="110"/>
                <div class="page">
                    <h2>Partner Ledger</h2>
                    <div class="row">
                        <div class="col-3">
                            <strong>Company:</strong>
                            <p t-esc="env.company.name"/>
                        </div>
                        <div class="col-3">
                            <t t-if="data['form']['date_from']">
                                <strong>Date from :</strong>
                                <span t-esc="data['form']['date_from']"/>
                                <br/>
                            </t>
                            <t t-if="data['form']['date_to']">
                                <strong>Date to :</strong>
                                <span t-esc="data['form']['date_to']"/>
                            </t>
                        </div>
                        <div class="col-3">
                            <strong>Target Moves:</strong>
                            <p t-if="data['form']['target_move'] == 'all'">All Entries</p>
                            <p t-if="data['form']['target_move'] == 'posted'">All Posted Entries</p>
                        </div>
                    </div>

                    <table class="table table-sm table-reports">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>JRNL</th>
                                <th>Account</th>
                                <th>Ref</th>
                                <th>Debit</th>
                                <th>Credit</th>
                                <th>Balance</th>
                                <th t-if="data['form']['amount_currency']">Currency</th>
                            </tr>
                        </thead>
                        <t t-foreach="docs" t-as="o">
                            <tbody>
                                <tr>
                                    <td colspan="4">
                                        <strong t-esc="o.ref"/>
                                        -
                                        <strong t-esc="o.name"/>
                                    </td>
                                    <td class="text-right">
                                        <strong t-esc="sum_partner(data, o, 'debit')"
                                                t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <strong t-esc="sum_partner(data, o, 'credit')"
                                                t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <strong t-esc="sum_partner(data, o, 'debit - credit')"
                                                t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                </tr>
                                <tr t-foreach="lines(data, o)" t-as="line">
                                    <td>
                                        <span t-esc="line['date']"/>
                                    </td>
                                    <td>
                                        <span t-esc="line['code']"/>
                                    </td>
                                    <td>
                                        <span t-esc="line['a_code']"/>
                                    </td>
                                    <td>
                                        <span t-esc="line['displayed_name']"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-esc="line['debit']"
                                              t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-esc="line['credit']"
                                              t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-esc="line['progress']"
                                              t-options="{'widget': 'monetary', 'display_currency': env.company.currency_id}"/>
                                    </td>
                                    <td class="text-right" t-if="data['form']['amount_currency']">
                                        <t t-if="line['currency_id']">
                                            <span t-esc="line['amount_currency']"
                                                  t-options="{'widget': 'monetary', 'display_currency': line['currency_id']}"/>
                                        </t>
                                    </td>
                                </tr>
                            </tbody>
                        </t>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
