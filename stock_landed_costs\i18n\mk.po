# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_landed_costs
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-05-31 13:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/odoo/odoo-9/language/"
"mk/)\n"
"Language: mk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:127
#: code:addons/stock_landed_costs/stock_landed_costs.py:131
#: code:addons/stock_landed_costs/stock_landed_costs.py:147
#: code:addons/stock_landed_costs/stock_landed_costs.py:151
#, python-format
msgid " already out"
msgstr " веќе испратено"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_account_id
msgid "Account"
msgstr "Сметка"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_account_journal_id
msgid "Account Journal"
msgstr "Дневник на сметка"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_additional_landed_cost
msgid "Additional Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: selection:product.template,split_method:0
#: selection:stock.landed.cost.lines,split_method:0
msgid "By Current Cost"
msgstr ""

#. module: stock_landed_costs
#: selection:product.template,split_method:0
#: selection:stock.landed.cost.lines,split_method:0
msgid "By Quantity"
msgstr "По количина"

#. module: stock_landed_costs
#: selection:product.template,split_method:0
#: selection:stock.landed.cost.lines,split_method:0
msgid "By Volume"
msgstr "По волумен"

#. module: stock_landed_costs
#: selection:product.template,split_method:0
#: selection:stock.landed.cost.lines,split_method:0
msgid "By Weight"
msgstr "По тежина"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cancel"
msgstr "Откажи"

#. module: stock_landed_costs
#: selection:stock.landed.cost,state:0
msgid "Cancelled"
msgstr "Откажано"

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Click to create a new landed cost."
msgstr "Кликнете да креирате нов дополнителен трошок."

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.stock_landed_cost_type_action
msgid "Click to define a new kind of landed cost."
msgstr "Кликни да дефинираш нов вид на дополнителен трошок."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Compute"
msgstr "Пресметај"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_price_unit
msgid "Cost"
msgstr "Трошок"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_cost_line_id
msgid "Cost Line"
msgstr "Ставка на чинење"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_cost_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cost Lines"
msgstr "Ставки на чинење"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_create_uid
msgid "Created by"
msgstr "Креирано од"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_create_date
msgid "Created on"
msgstr "Креирано на"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_date
msgid "Date"
msgstr "Датум"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_name
msgid "Description"
msgstr "Опис"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/purchase_config_settings.py:18
#, python-format
msgid ""
"Disabling the costing methods will prevent you to use the landed costs "
"feature."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_display_name
msgid "Display Name"
msgstr "Прикажи име"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model:mail.message.subtype,name:stock_landed_costs.mt_stock_landed_cost_open
msgid "Done"
msgstr "Завршено"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: selection:stock.landed.cost,state:0
msgid "Draft"
msgstr "Нацрт"

#. module: stock_landed_costs
#: selection:product.template,split_method:0
#: selection:stock.landed.cost.lines,split_method:0
msgid "Equal"
msgstr "Еднакво"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_template_split_method
msgid ""
"Equal : Cost will be equally divided.\n"
"By Quantity : Cost will be divided according to product's quantity.\n"
"By Current cost : Cost will be divided according to product's current cost.\n"
"By Weight : Cost will be divided depending on its weight.\n"
"By Volume : Cost will be divided depending on its volume."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_final_cost
msgid "Final Cost"
msgstr "Конечна цена на чинење"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_former_cost
msgid "Former Cost"
msgstr "Претходна цена"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_former_cost_per_unit
msgid "Former Cost(Per Unit)"
msgstr "Претходна цена(по единица)"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Group By"
msgstr "Групирај по"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_id
msgid "ID"
msgstr "ID"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_type_form
msgid "Information"
msgstr "Информација"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_description
msgid "Item Description"
msgstr "Опис"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_account_move_id
msgid "Journal Entry"
msgstr "Внес во дневник"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_cost_id
msgid "Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed Cost Name"
msgstr "Име на дополнителен трошок"

#. module: stock_landed_costs
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost_type
msgid "Landed Cost Type"
msgstr "Тип на дополнителен трошок"

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.action_stock_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template_landed_cost_ok
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_type_form
msgid "Landed Costs"
msgstr "Дополнителни трошоци"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed cost are computed based on the purchase unit of measure."
msgstr ""

#. module: stock_landed_costs
#: model:mail.message.subtype,description:stock_landed_costs.mt_stock_landed_cost_open
msgid "Landed cost validated"
msgstr "Одобрен дополнителен трошок"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost___last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines___last_update
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines___last_update
msgid "Last Modified on"
msgstr "Последна промена на"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_write_uid
msgid "Last Updated by"
msgstr "Последно ажурирање од"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_write_date
msgid "Last Updated on"
msgstr "Последно ажурирање на"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Month"
msgstr "Месец"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_name
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Name"
msgstr "Име"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:203
#, python-format
msgid "Only draft landed costs can be validated"
msgstr "Само нацрт дополнителни трошоци можат да се одобрат"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_picking_ids
msgid "Pickings"
msgstr "Требувања"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:90
#, python-format
msgid "Please configure Stock Expense Account for product: %s."
msgstr ""

#. module: stock_landed_costs
#: selection:stock.landed.cost,state:0
msgid "Posted"
msgstr "Книжено"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_product_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_product_id
msgid "Product"
msgstr "Производ"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_type_form
msgid "Product Name"
msgstr "Име на производот"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_product_template
msgid "Product Template"
msgstr "Урнек на производ"

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.stock_landed_cost_type_action
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.stock_landed_cost_tree_view
msgid "Products"
msgstr "Производи"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_quantity
msgid "Quantity"
msgstr "Количина"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template_split_method
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines_split_method
msgid "Split Method"
msgstr "Метод на делење"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_state
msgid "State"
msgstr "Држава"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Status"
msgstr "Статус"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost
msgid "Stock Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost_lines
msgid "Stock Landed Cost Lines"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_move_id
msgid "Stock Move"
msgstr "Движење на залиха"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_adjustment_lines
msgid "Stock Valuation Adjustment Lines"
msgstr ""

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:52
#, python-format
msgid ""
"The selected picking does not contain any move that would be impacted by "
"landed costs. Landed costs are only possible for products configured in real "
"time valuation with real price costing method. Please make sure it is the "
"case, or you selected the correct picking"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_amount_total
msgid "Total"
msgstr "Вкупно"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Validate"
msgstr "Потврди"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:261
#, python-format
msgid ""
"Validated landed costs cannot be cancelled, but you could create negative "
"landed costs to reverse them"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_valuation_adjustment_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation Adjustments"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_volume
msgid "Volume"
msgstr "Волумен"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/purchase_config_settings.py:17
#, python-format
msgid "Warning!"
msgstr "Внимание!"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines_weight
msgid "Weight"
msgstr "Тежина"

#. module: stock_landed_costs
#: code:addons/stock_landed_costs/stock_landed_costs.py:205
#, python-format
msgid ""
"You cannot validate a landed cost which has no valid valuation adjustments "
"lines. Did you click on Compute?"
msgstr ""

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_type_form
msgid "describe the product characteristics..."
msgstr "опишете ги карактеристиките на производот..."

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_purchase_config_settings
msgid "purchase.config.settings"
msgstr "purchase.config.settings"

#~ msgid "Action Needed"
#~ msgstr "Потребна е акција"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Датум на испраќање на последната порака"

#~ msgid "Followers"
#~ msgstr "Пратители"

#~ msgid "Followers (Channels)"
#~ msgstr "Пратители (Канали)"

#~ msgid "Followers (Partners)"
#~ msgstr "Пратители (Партнери)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето внимание."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Доколку е штиклирано, новите пораки го бараат вашето вниманите."

#~ msgid "Is Follower"
#~ msgstr "е следач"

#~ msgid "Last Message Date"
#~ msgstr "Датум на последна порака"

#~ msgid "Messages"
#~ msgstr "Пораки"

#~ msgid "Messages and communication history"
#~ msgstr "Пораки и историја на комуникација"

#~ msgid "Number of Actions"
#~ msgstr "Број на акции"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Број на пораки за кои ѝм е потребна акција"

#~ msgid "Number of unread messages"
#~ msgstr "Број на непрочитани пораки"

#~ msgid "Unread Messages"
#~ msgstr "Непрочитани Пораки"

#~ msgid "Unread Messages Counter"
#~ msgstr "Тезга на непрочитаните пораки"

#~ msgid "Website Messages"
#~ msgstr "Пораки на веб сајт"

#~ msgid "Website communication history"
#~ msgstr "Историја на веб комуникација"
