# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_matrix
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nesselbosch, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_1
msgid "Blue"
msgstr "Blau"

#. module: product_matrix
#. openerp-web
#: code:addons/product_matrix/static/src/js/section_and_note_widget.js:0
#, python-format
msgid "Choose Product Variants"
msgstr "Produktvarianten wählen"

#. module: product_matrix
#. openerp-web
#: code:addons/product_matrix/static/src/js/section_and_note_widget.js:0
#, python-format
msgid "Close"
msgstr "Abschließen"

#. module: product_matrix
#. openerp-web
#: code:addons/product_matrix/static/src/js/section_and_note_widget.js:0
#, python-format
msgid "Confirm"
msgstr "Bestätigen"

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_gender
msgid "Gender"
msgstr "Geschlecht"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_m
msgid "Men"
msgstr "Herren"

#. module: product_matrix
#: model:product.template,name:product_matrix.matrix_product_template_shirt
msgid "My Company Tshirt (GRID)"
msgstr "My Company T-Shirt (RASTER)"

#. module: product_matrix
#. openerp-web
#: code:addons/product_matrix/static/src/xml/product_matrix.xml:0
#, python-format
msgid "Not available"
msgstr "Nicht verfügbar"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_2
msgid "Pink"
msgstr "Pink"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template
msgid "Product Template"
msgstr "Produktvorlage"

#. module: product_matrix
#: model:ir.model,name:product_matrix.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Attributwert der Produktvorlage"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_4
msgid "Rainbow"
msgstr "Regenbogen"

#. module: product_matrix
#: model:product.template,description_sale:product_matrix.matrix_product_template_shirt
msgid "Show your company love around you =)."
msgstr "Zeigen Sie Ihrer Umgebung, wie sehr Sie Ihr Unternehmen lieben =)."

#. module: product_matrix
#: model:product.attribute,name:product_matrix.product_attribute_size
msgid "Size"
msgstr "Größe"

#. module: product_matrix
#: model:product.template,uom_name:product_matrix.matrix_product_template_shirt
msgid "Units"
msgstr "Einheiten"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_w
msgid "Women"
msgstr "Damen"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xl
msgid "XL"
msgstr "XL"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_size_xs
msgid "XS"
msgstr "XS"

#. module: product_matrix
#: model:product.attribute.value,name:product_matrix.product_attribute_value_color_3
msgid "Yellow"
msgstr "Gelb"
