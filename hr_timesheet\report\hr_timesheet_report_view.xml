<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="act_hr_timesheet_report" model="ir.actions.act_window">
            <field name="name">Timesheets by Employee</field>
            <field name="res_model">account.analytic.line</field>
            <field name="domain">[('project_id', '!=', False)]</field>
            <field name="context">{'search_default_groupby_employee':1,}</field>
            <field name="search_view_id" ref="hr_timesheet_line_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No activities found
              </p><p>
                Track your working hours by projects every day and invoice this time to your customers.
              </p>
            </field>
        </record>

        <record model="ir.actions.act_window.view" id="act_hr_timesheet_report_pivot">
            <field name="sequence" eval="5"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_pivot"/>
            <field name="act_window_id" ref="act_hr_timesheet_report"/>
        </record>

        <record id="timesheet_action_view_report_by_employee_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="6"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_graph"/>
            <field name="act_window_id" ref="act_hr_timesheet_report"/>
        </record>

        <record model="ir.actions.act_window.view" id="act_hr_timesheet_report_tree">
            <field name="sequence" eval="10"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_tree"/>
            <field name="act_window_id" ref="act_hr_timesheet_report"/>
        </record>

        <record model="ir.actions.act_window.view" id="act_hr_timesheet_report_form">
            <field name="sequence" eval="15"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_form"/>
            <field name="act_window_id" ref="act_hr_timesheet_report"/>
        </record>

        <record id="act_hr_timesheet_report_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="20"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="hr_timesheet.view_kanban_account_analytic_line"/>
            <field name="act_window_id" ref="act_hr_timesheet_report"/>
        </record>

        <record id="timesheet_action_report_by_project" model="ir.actions.act_window">
            <field name="name">Timesheets by Project</field>
            <field name="res_model">account.analytic.line</field>
            <field name="domain">[('project_id', '!=', False)]</field>
            <field name="context">{'search_default_groupby_project': 1}</field>
            <field name="search_view_id" ref="hr_timesheet_line_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No activities found
              </p><p>
                Track your working hours by projects every day and invoice this time to your customers.
              </p>
            </field>
        </record>

        <record id="timesheet_action_view_report_by_project_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_pivot"/>
            <field name="act_window_id" ref="timesheet_action_report_by_project"/>
        </record>

        <record id="timesheet_action_view_report_by_project_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="6"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_graph"/>
            <field name="act_window_id" ref="timesheet_action_report_by_project"/>
        </record>

        <record id="timesheet_action_view_report_by_project_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="10"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_tree"/>
            <field name="act_window_id" ref="timesheet_action_report_by_project"/>
        </record>

        <record id="timesheet_action_view_report_by_project_form" model="ir.actions.act_window.view">
            <field name="sequence" eval="15"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_form"/>
            <field name="act_window_id" ref="timesheet_action_report_by_project"/>
        </record>

        <record id="timesheet_action_view_report_by_project_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="20"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="hr_timesheet.view_kanban_account_analytic_line"/>
            <field name="act_window_id" ref="timesheet_action_report_by_project"/>
        </record>

        <record id="timesheet_action_report_by_task" model="ir.actions.act_window">
            <field name="name">Timesheets by Task</field>
            <field name="res_model">account.analytic.line</field>
            <field name="domain">[('project_id', '!=', False)]</field>
            <field name="context">{'search_default_groupby_project':1,'search_default_groupby_task':1,}</field>
            <field name="search_view_id" ref="hr_timesheet_line_search"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                No activities found
              </p><p>
                Track your working hours by projects every day and invoice this time to your customers.
              </p>
            </field>
        </record>

        <record id="timesheet_action_view_report_by_task_pivot" model="ir.actions.act_window.view">
            <field name="sequence" eval="5"/>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_pivot"/>
            <field name="act_window_id" ref="timesheet_action_report_by_task"/>
        </record>

        <record id="timesheet_action_view_report_by_task_graph" model="ir.actions.act_window.view">
            <field name="sequence" eval="6"/>
            <field name="view_mode">graph</field>
            <field name="view_id" ref="hr_timesheet.view_hr_timesheet_line_graph"/>
            <field name="act_window_id" ref="timesheet_action_report_by_task"/>
        </record>

        <record id="timesheet_action_view_report_by_task_tree" model="ir.actions.act_window.view">
            <field name="sequence" eval="10"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_tree"/>
            <field name="act_window_id" ref="timesheet_action_report_by_task"/>
        </record>

        <record id="timesheet_action_view_report_by_task_form" model="ir.actions.act_window.view">
            <field name="sequence" eval="15"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="hr_timesheet.hr_timesheet_line_form"/>
            <field name="act_window_id" ref="timesheet_action_report_by_task"/>
        </record>

        <record id="timesheet_action_view_report_by_task_kanban" model="ir.actions.act_window.view">
            <field name="sequence" eval="20"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="hr_timesheet.view_kanban_account_analytic_line"/>
            <field name="act_window_id" ref="timesheet_action_report_by_task"/>
        </record>

        <menuitem id="menu_timesheets_reports"
            name="Reporting"
            parent="timesheet_menu_root"
            groups="hr_timesheet.group_timesheet_manager"
            sequence="99"/>

        <menuitem id="menu_timesheets_reports_timesheet"
            name="Timesheets"
            parent="menu_timesheets_reports"
            groups="hr_timesheet.group_timesheet_manager"
            sequence="10"/>

        <menuitem id="menu_hr_activity_analysis"
            parent="menu_timesheets_reports_timesheet"
            action="act_hr_timesheet_report"
            name="By Employee"
            sequence="10"/>

        <menuitem id="timesheet_menu_report_timesheet_by_project"
            parent="menu_timesheets_reports_timesheet"
            action="timesheet_action_report_by_project"
            name="By Project"
            sequence="15"/>

        <menuitem id="timesheet_menu_report_timesheet_by_task"
            parent="menu_timesheets_reports_timesheet"
            action="timesheet_action_report_by_task"
            name="By Task"
            sequence="20"/>

    </data>
</odoo>
