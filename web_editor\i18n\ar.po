# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# Abed <PERSON> <<EMAIL>>, 2021
# <PERSON> <mustaf<PERSON>@cubexco.com>, 2021
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-10 08:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Original)"
msgstr "%dpx (الأصلي) "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Suggested)"
msgstr "%dpx (المقترح) "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""
"تحدد 'علامة التصنيف البديلة' نصاً بديلاً للصورة، إذا تعذّر عرض الصورة (بسبب "
"بطء الاتصال، أو فقدان الصورة، أو مشكلة في مسح الشاشة،...). "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr ""
"تُعرض 'علامة تصنيف العنوان' كتلميح عندما تقوم بتمرير المؤشر على الصورة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(علامة تصنيف بديلة) "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(علامة تصنيف العنوان) "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(الرابط أو الكود المضمن)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr "1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr "2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr "3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr "4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"
msgstr "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">deg</span>"
msgstr "<span class=\"flex-grow-0 ml-1 text-white-50\">النسبة</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2 ml-3\">Y</span>"
msgstr "<span class=\"mr-2 ml-3\">Y</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2\">X</span>"
msgstr "<span class=\"mr-2\">X</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr "<span>الكتل الإنشائية</span> "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Style</span>"
msgstr "<span>التصميم</span> "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""
"حدث خطأ في الخادم. يرجى التحقق من تسجيل دخولك بشكل صحيح ومن صحة تنسيق الملف "
"الذي تحاول حفظه. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr "فوق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Accepts"
msgstr "يقبل"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Add"
msgstr "إضافة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr "إضافة عمود "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "إضافة صف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add URL"
msgstr "إضافة رابط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section."
msgstr "إضافة قسم للاقتباس. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button."
msgstr "إضافة زر. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section."
msgstr "إضافة قسم للكود. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column left"
msgstr "إضافة عمود إلى اليسار "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column right"
msgstr "إضافة عمود إلى اليمين "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link."
msgstr "إضافة رابط. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row above"
msgstr "إضافة صف إلى الأعلى "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row below"
msgstr "إضافة صف إلى الأسفل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add document"
msgstr "إضافة مستند "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "Aden"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "Airy & Zigs"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "المحاذاة في الوسط "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "المحاذاة إلى اليسار "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "المحاذاة إلى اليمين "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "المحاذاة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All"
msgstr "الكل"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr "كافة ملفات SCSS "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All images have been loaded"
msgstr "لقد تم رفع كافة الصور "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "علامة التصنيف البديلة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "الزاوية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr "متحرك "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "تطبيق"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet: %s ?"
msgstr "هل أنت متأكد من أنك ترغب من حذف هذه القصاصة: %s؟ "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr "هل أنت متأكد من أنك تريد حذف هذا الملف؟ "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "تناسب الأبعاد"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "أدوات الأصول "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "المرفق "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "رابط المرفق"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Auto"
msgstr "تلقائي"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr "التحويل التلقائي للرابط المتعلق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoplay"
msgstr "تشغيل تلقائي"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "الخلفية"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "لون الخلفية"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "تموضع الخلفية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Basic blocks"
msgstr "الكتل البرمجية القياسية "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Basics"
msgstr "الأساسيات"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr "الأسفل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading."
msgstr "ترويسة قسم كبير. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr "لطخات "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "كتلة برمجية إنشائية "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr "مكعبات ومطر "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "تمويه "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "عريض"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr "لون الحدود "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr "نمط الإطار "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr "عرض الحدود "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr "برانان "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "السطوع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr "قائمة نقاط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "الزر"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Cancel"
msgstr "إلغاء "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr "احذر!"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "وسط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr "تغيير وصف الوسائط وتلميحاتها"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr "قائمة مرجعية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr "اختر سجلاً... "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Close"
msgstr "إغلاق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "الكود"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "اللون"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr "العمود"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "ألوان دارجة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "تأكيد"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirmation"
msgstr "تأكيد"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content conflict"
msgstr "تعارض المحتوى "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "التناقض "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy Link"
msgstr "انسخ الرابط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "قم بنسخ ولصق رابطك أو الكود المضمن هنا"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module <strong>%s</strong>"
msgstr "تعذر تثبيت التطبيق <strong>%s</strong> "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "الغلاف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "إنشاء"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering."
msgstr "إنشاء قائمة مرقمة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list."
msgstr "إنشاء قائمة نقاط بسيطة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr "إنشاء رابط URL. "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr "قص الصورة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "مُخصص"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr "مخصص %s "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr "متقطع"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default"
msgstr "الافتراضي"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr "الافتراضي + دائري "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr "تحديد تدرج ألوان مخصص "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr "حذف %s "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Delete current table"
msgstr "حذف الجدول الحالي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "الوصف"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "إهمال السجل "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install the %s App?"
msgstr "هل ترغب في تثبيت تطبيق %s؟ "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Document"
msgstr "المستند"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr "منقط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr "مزدوج "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr "انقر مرتين للتحرير"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "استنساخ الحاوية"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "ألوان ديناميكية "

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr "خطأ: تعذر إحضار روابط التنزيل من مكتبة الوسائط. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr "المبكر "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Edit Link"
msgstr "تحرير الرابط "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "تحرير الصورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr "تحرير وصف الوسائط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"لا يحبذ تحرير ملف موجود كجزء من أداة التحرير هذه، حيث أن ذلك سيمنعه من أن "
"يتم تحديثه عند تحديثات التطبيق المستقبلية. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr "تضمين صورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr "تضمين مقطع فيديو اليوتيوب "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr "تضمين الصورة في المستند. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr "تضمين مقطع فيديو اليوتيوب في المستند. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr "اقتباس فارغ "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr "متوقع "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr "التمديد لأقرب زاوية "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr "التمديد لأقرب جهة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr "التمديد لأبعد زاوية "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr "التمديد لأبعد جهة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File could not be saved"
msgstr "تعذر حفظ الملف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File has been uploaded"
msgstr "لقد تم تحديث الملف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "ملء "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr "ملء + دائري "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Filter"
msgstr "تأثير فني "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr "اللوحة الأولى "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "مسطح"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "Flexible"
msgstr "مرن "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "القلب أفقياً "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "القلب رأسياً "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr "أشكال عائمة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Floats"
msgstr "فواصل عائمة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr "لون الخط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr "حجم الخط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr "لا يمكن إفلات الكتلة البنائية هنا لأسباب تقنية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "التنسيق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "ملء الشاشة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"Get the perfect image by searching in our library of copyright free photos "
"and illustrations."
msgstr ""
"احصل على الصورة المثالية عن طريق البحث في مكتبتنا التي تحتوي على صور ورسومات"
" خالية من حقوق النشر. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr "تدرج ألوان "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr "الترويسة 1 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr "الترويسة 2 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr "الترويسة 3 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr "الترويسة 4 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr "الترويسة 5 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr "الترويسة 6 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 1"
msgstr "العنوان 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 2"
msgstr "العنوان 2 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 3"
msgstr "العنوان 3 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 4"
msgstr "العنوان 4 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 5"
msgstr "العنوان 5 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 6"
msgstr "العنوان 6 "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr "إخفاء شعار Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Youtube logo"
msgstr "إخفاء شعار Youtube "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide fullscreen button"
msgstr "إخفاء زر ملء الشاشة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide player controls"
msgstr "إخفاء متحكمات التشغيل"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide sharing button"
msgstr "إخفاء زر المشاركة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#, python-format
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "المُعرف"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "الأيقونة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr "تنسيق الأيقونة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr "حجم الأيقونة 1x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr "حجم الأيقونة 2x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr "حجم الأيقونة 3x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr "حجم الأيقونة 4x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr "حجم الأيقونة 5x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"إذا قمت بتجاهل التعديلات الحالية، سوف تضيع كافة التغييرات غير المحفوظة. "
"بإمكانك إلغاء الإجراء للعودة إلى وضع التحرير. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""
"إذا أعدت تعيين هذا الملف، ستضيع كافة التخصيصات وسيعود الملف لحالته "
"الافتراضية."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Illustrations"
msgstr "الرسومات "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "صورة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr "تنسيق الصورة "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "ارتفاع الصورة "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "مصدر الصورة "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "عرض الصورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr "حشو الصورة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr "Inkwell"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr "نص مضمن "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table."
msgstr "إدراج جدول. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video."
msgstr "إدراج فيديو. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert an horizontal rule separator."
msgstr "إدراج فاصل قاعدة أفقي. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image."
msgstr "إدراج صورة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr "إدراج وسائط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr "إدراج أو تحرير رابط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert table"
msgstr "إدراج جدول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "تثبيت"

#. module: web_editor
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr "قيمة الحقل غير صالحة لـ %s: %s "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr "تثبيت %s "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install in progress"
msgstr "جاري التثبيت "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr "العناصر الخفية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr "عنصر "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr "JS"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr "ملف JS: %s "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "كبير"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "يسار"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "خطي "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Lines"
msgstr "الخطوط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "الرابط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "بطاقة عنوان الرابط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr "تم نسخ الرابط في الحافظة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Link to"
msgstr "رابط إلى"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "List"
msgstr "القائمة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Load more..."
msgstr "تحميل المزيد... "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Loop"
msgstr "تكرار "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr "اللون الرئيسي "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr "Maven"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "الإعلام "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Medias"
msgstr "الوسائط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "وسط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading."
msgstr "ترويسة قسم متوسط. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr "المزيد من المعلومات حول هذا التطبيق. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "My Images"
msgstr "صوري "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "الاسم"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "التنقل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "لا"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr "لا يوجد رابط URL محدد "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No documents found."
msgstr "لم يتم العثور على أي مستندات. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No images found."
msgstr "لم يتم العثور على صور. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr "لا يوجد موقع لوضعه "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr "لا توجد المزيد من السجلات "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/powerbox/Powerbox.js:0
#, python-format
msgid "No results"
msgstr "لا توجد نتائج "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "لا شيء"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "عادي"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr "قائمة مرقمة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr "ملفات SCSS المخصصة فقط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr "ملفات صفحة SCSS فقط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr "المشاهدات فقط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in a new tab"
msgstr "فتح في علامة تبويب جديدة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in new window"
msgstr "فتح في نافذة جديدة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Optimized"
msgstr "تم التحسين "

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr "مرفق أصلي (غير محسن، لم يتم تحجيمه) "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr "الأصول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "التحديد "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr "محدد + تحديد دائري "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "حشو"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block."
msgstr "كتلة الفقرة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr "لصق كرابط URL "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "الأنماط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Pictogram"
msgstr "صور نصية"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Position"
msgstr "الموضع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "معاينة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr "الرئيسي"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr "Qu'il n'est pas arrivé à Toronto"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr "Qu'il était supposé arriver à Toronto"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "الجودة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "اقتباس"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr "حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "جهة اتصال حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr "تاريخ حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr "وقت وتاريخ حقل Qweb "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr "مدة حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr "قيمة عشرية لحقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "حقل Qweb من النوع HTML"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr "صورة حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr "حقل Qweb صحيح"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr "حقل Qweb متعدد إلى واحد "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr "حقل Qweb نقدي"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr "حقل Qweb نسبي"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr "تحديد حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr "نص حقل Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr "حقل Qweb من النوع qweb"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr "نصف قطري "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr "حقل للقراءة فقط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr "إعادة توجيه المستخدم إلى مكان آخر عند الضغط على الوسائط. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr "إزالة (حذف) "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "إزالة الكتلة البنائية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr "إزالة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Remove Link"
msgstr "إزالة الرابط "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr "إزالة اللون المحدد "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current column"
msgstr "إزالة العمود الحالي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current row"
msgstr "إزالة الصف الحالي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr "إزالة التنسيق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr "إزالة الرابط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr "إعادة التسمية %s "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr "تكرار النمط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "استبدال"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr "استبدال الوسائط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "إعادة تعيين"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "إعادة تعيين الصورة"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr "إعادة تعيين القص "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr "إعادة تعيين التحويل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "خاصية إعادة تعيين أدوات العرض غير مدعومة بعد "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Auto"
msgstr "التحجيم تلقائياً "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr "التحجيم كاملاً "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr "التحجيم للنصف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr "التحجيم للربع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "يمين"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "التدوير لليسار"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "التدوير لليمين"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr "صف "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr "SCSS (CSS)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr "ملف SCSS: %s"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr "الإشباع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "حفظ"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr "حفظ وتثبيت "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr "حفظ وإعادة تحميل "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "حفظ السجل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search"
msgstr "بحث"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a document"
msgstr "البحث عن مستند "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a pictogram"
msgstr "البحث عن رسم تخطيطي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search an image"
msgstr "البحث عن صورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr "البحث عن السجلات... "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr "البحث عن المزيد... "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr "البحث لإظهار المزيد من السجلات "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search..."
msgstr "بحث..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr "ثانوي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Select a Media"
msgstr "اختر الوسائط"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr "اختر كتلة بنائية في صفحتك لتزيينها. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Separator"
msgstr "الفاصل"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr "داكن "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr "خطأ في الخادم "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr "الظل"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "شكل"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr "شكل: دائرة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr "شكل: دائري "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr "الشكل: الصورة المصغرة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Show optimized images"
msgstr "إظهار الصور المحسنة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "الحجم"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr "الحجم 1x "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr "الحجم 2x "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr "الحجم 3x "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr "الحجم 4x "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr "الحجم 5x "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "صغير"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading."
msgstr "ترويسة قسم صغير. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr "لون خالص "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr "ألوان خالصة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr "الخاصة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr " الشكل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Suggestions"
msgstr "الاقتراحات "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr "تبديل الاتجاه "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction."
msgstr "تبديل اتجاه النص. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "جدول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr "خيارات الجدول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table tools"
msgstr "أدوات الجدول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr "معرف القالب: %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "النص"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr "محاذاة النص "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr "شكل الخط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr "يبدو أن رابط URL لا يعمل. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL seems valid."
msgstr "يبدو رابط URL صالحاً. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""
"لم نتمكن من حذف الصورة لأنها مستخدمة في\n"
"               الصفحات أو الواجهات التالية:"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr "الرابط لا يشمل أي فيديو مدعوم"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url is not valid"
msgstr "رابط URL الذي قدمته غير صالح "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                    If you need to keep your changes, copy the content below and edit the new document."
msgstr ""
"سيتم استخدام النسخة من قاعدة البيانات.\n"
"                    إذا كان لابد من إبقاء تغييراتك، قم بنسخ المحتوى أدناه وتحرير المستند الجديد."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "السمة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr "ألوان السمة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr "هناك تعارض بين نسختك والنسخة الموجودة في قاعدة البيانات. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr "رابط URL هذا غير صالح. تعذر تحديث المعاينة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated"
msgstr "هذه الكتلة البنائية قديمة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr "هذا المستند غير محفوظ!"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is a public view attachment."
msgstr "هذا الملف هو مرفق للعرض العام. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is attached to the current record."
msgstr "هذا الملف مرفق بالسجل الحالي. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "This image is an external image"
msgstr "هذه الصورة خارجية"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""
"هذا النوع من الصور لا يدعم خاصية القص.<br/> إذا أردت قصها، يرجى تنزيلها "
"أولاً من المصدر الأصلي ثم رفعها في أودو. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "العنوان"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "علامة تصنيف العنوان "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To apply this change, we need to save all your previous modifications and "
"reload the page."
msgstr ""
"لتطبيق هذا التغيير، نحتاج إلى حفظ كافة تعديلاتك السابقة وإعادة تحميل الصفحة."
" "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid ""
"To make changes, drop this block and use the new options in the last "
"version."
msgstr ""
"لإجراء التغييرات، قم بإفلات هذه الكتلة البنائية واستخدم الخيارات الجديدة في "
"آخر إصدار. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr "لحفظ قصاصة، نحتاج إلى حفظ كافة تعديلاتك السابقة وإعادة تحميل الصفحة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "المهام المراد تنفيذها"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr "محمصة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr "التبديل لعريض "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr "تبديل القائمة المرجعية "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr "تبديل دوران الأيقونة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr "التبديل لمائل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr "تبديل القائمة المطلوبة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr "التبديل إلى نص يتوسطه خط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr "التبديل للتعليم بخط "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr "تبديل القائمة غير المطلوبة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr "تلميح "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist."
msgstr "تتبع المهام باستخدام القائمة المرجعية. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr "تحويل "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr "تحويل الصورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr "حول هذه الصورة (انقر مرتين لإعادة تعيين عملية التحويل)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "ترجمة "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "الترجمة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr "ألوان شفافة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "النوع"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr "اكتب \"/\" للأوامر "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "الرابط أو البريد الإلكتروني"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr "عدم المحاذاة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr "غير متوقع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload a document"
msgstr "رفع مستند"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload an image"
msgstr "رفع صورة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Uploaded image's format is not supported. Try with:"
msgstr "صيغة الصورة المرفوعة غير مدعومة: جرب استخدام: "

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr "صيغة الصورة المرفوعة غير مدعومة: جرب استخدام: %s "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr "فالنسيا "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video"
msgstr "فيديو"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr "تنسيق الفيديو "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video code"
msgstr "كود الفيديو"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr "يتم كتم الصوت في مقاطع الفيديو عندما يتم تمكين التشغيل التلقائي "

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "أداة العرض"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr "حزم المشاهدات والأصول "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr "Walden"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""
"تحذير: بعد إغلاق هذا الحوار، سيتم إهمال النسخة التي كنت تعمل عليها ولن تكون "
"متاحة بعد الآن. "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "مموج "

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr "الاختبار الفرعي لمحول محرر الويب "

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr "اختبار محول محرر الويب"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Width"
msgstr "العرض"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr "كبير جداً "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr "XML (HTML)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr "Xpro"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "نعم"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr "بإمكانك رفع المستندات باستخدام الزر الموجود في أعلى يمين الشاشة. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr "بإمكانك رفع الصور باستخدام الزر الموجود في أعلى يمين الشاشة. "

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr "عليك تحديد إما البيانات أو رابط URL لإنشاء مرفق. "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Your URL"
msgstr "رابط URL الخاص بك "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "تكبير"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "تصغير"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "إضافة"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "and"
msgstr "و"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr "تلقائي"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr "تغميق "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "default"
msgstr "افتراضي "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr "حصر "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr "https://www.odoo.com/logo.png"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/mydocument"
msgstr "https://www.odoo.com/mydocument"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr "تفتيح "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr "مضاعفة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr "تراكب "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "بيكسل"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "شاشة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "videos"
msgstr "مقاطع الفيديو"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "www.example.com"
msgstr "www.example.com"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Autoconvert to relative link"
msgstr "⌙ التحويل التلقائي إلى الرابط ذي الصلة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Border"
msgstr "⌙ الإطار "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Color filter"
msgstr "⌙ تأثير فني ملون "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Colors"
msgstr "⌙ ألوان "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Fill Color"
msgstr "⌙ تعبئة اللون "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Flip"
msgstr "⌙ قلب "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Height"
msgstr "⌙ الطول "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Image"
msgstr "⌙ الصورة "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Link Label"
msgstr "⌙ بطاقة عنوان الرابط "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Main Color"
msgstr "⌙ اللون الرئيسي "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Open in new window"
msgstr "⌙ فتح في نافذة جديدة "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Position"
msgstr "⌙ الموقع "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "⌙ Shape"
msgstr "⌙ الشكل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Size"
msgstr "⌙ الحجم "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Style"
msgstr "⌙ الشكل "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Text Color"
msgstr "⌙ لون النص "

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Width"
msgstr "⌙ العرض "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "⌙ Your URL"
msgstr "⌙ رابط URL الخاص بك "
