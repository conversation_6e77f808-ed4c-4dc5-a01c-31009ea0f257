# Translation of Odoo Server.
 # This file contains the translation of the following modules:
 # * hr_payroll_community_v13
 # 
 # Translators:
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> YILMAZ <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2018
 # <PERSON><PERSON> <<EMAIL>>, 2019
 # 
 msgid ""
 msgstr ""
 "Project-Id-Version: Odoo Server 13.0\n"
 "Report-Msgid-Bugs-To: \n"
 "POT-Creation-Date: 2019-01-09 10:31+0000\n"
 "PO-Revision-Date: 2018-08-24 09:19+0000\n"
 "Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2019\n"
 "Language-Team: Turkish (https://www.transifex.com/odoo/teams/41243/tr/)\n"
 "MIME-Version: 1.0\n"
 "Content-Type: text/plain; charset=UTF-8\n"
 "Content-Transfer-Encoding: \n"
 "Language: tr\n"
 "Plural-Forms: nplurals=2; plural=(n > 1);\n"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:42
 #, python-format
 msgid "%s (copy)"
 msgstr "%s (kopya)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__state
 msgid ""
 "* When the payslip is created the status is 'Draft'\n"
 "                \n"
 "* If the payslip is under verification, the status is 'Waiting'.\n"
 "                \n"
 "* If the payslip is confirmed then status is set to 'Done'.\n"
 "                \n"
 "* When user cancel payslip the status is 'Rejected'."
 msgstr ""
 "* Bordro oluşturulduğunda durum 'Taslak' dır. \n"
 "* Eğer bordro onaylanmadıysa, durum 'Beklemede' dur.\n"
 "* Eğer bordro doğrulandıysa, durum 'Biten' olarak ayarlanır.\n"
 "* Kullanıcı bordroyu iptal ederse durum 'İptal' olur."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "<span class=\"o_form_label\">Payroll Rules</span>"
 msgstr "<span class=\"o_form_label\">Ücret Kuralları </span>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid ""
 "<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all"
 " selected employee(s) based on the dates and credit note specified on "
 "Payslips Run.</span>"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Address</strong>"
 msgstr "<strong>Adres</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Authorized signature</strong>"
 msgstr "<strong>Yetkili imza</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Bank Account</strong>"
 msgstr "<strong>Banka Hesabı</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date From:</strong>"
 msgstr "<strong>Başlangıç Tarihi:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date From</strong>"
 msgstr "<strong>Başlangıç Tarihi</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Date To:</strong>"
 msgstr "<strong>Bitiş Tarihi:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Date To</strong>"
 msgstr "<strong>Bitiş Tarihi</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Designation</strong>"
 msgstr "<strong>Atama</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Email</strong>"
 msgstr "<strong>Eposta</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Identification No</strong>"
 msgstr "<strong>Kimlik No</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Name</strong>"
 msgstr "<strong>Adı</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "<strong>Reference</strong>"
 msgstr "<strong>Referans</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Register Name:</strong>"
 msgstr "<strong>Kayıt Adı:</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "<strong>Total</strong>"
 msgstr "<strong>Toplam</strong>"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid ""
 "A contribution register is a third party involved in the salary\n"
 "            payment of the employees. It can be the social security, the\n"
 "            state or anyone that collect or inject money on payslips."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_account_accountant
 msgid "Account Accountant"
 msgstr "Hesap Muhasebecisi"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting"
 msgstr "Muhasebe"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Accounting Information"
 msgstr "Muhasebe Bilgisi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid "Active"
 msgstr "Etkin"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.actions.act_window,help:hr_payroll_community_v13.action_contribution_register_form
 msgid "Add a new contribution register"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Add an internal note..."
 msgstr "Bir iç not ekle..."
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contract_advantage_template_view_form
 msgid "Advantage Name"
 msgstr "Yan Hak Adı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_children_salary_rules
 msgid "All Children Rules"
 msgstr "Tüm Alt Kurallar"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.ALW
 msgid "Allowance"
 msgstr "Allowance"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Always True"
 msgstr "Daima Gerçek"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__amount
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Amount"
 msgstr "Tutar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Amount Type"
 msgstr "Tutar Türü"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Annually"
 msgstr "Yıllık"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Appears on Payslip"
 msgstr "Bordro'da Görünsün"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid ""
 "Applied this rule for calculation if condition is true. You can specify "
 "condition like basic > 1000."
 msgstr ""
 "Bu kuralı eğer koşul doğruysa uygulayın. >1000 gibi temel bir koşul "
 "belirtebilirsiniz."
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.BASIC
 msgid "Basic"
 msgstr "Temel"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_basic
 msgid "Basic Salary"
 msgstr "Temel Maaş"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_be_hr_payroll_community
 msgid "Belgium Payroll"
 msgstr "Belçika Bordro"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-monthly"
 msgstr "İki Aylık"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Bi-weekly"
 msgstr "İki Haftalık"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Calculations"
 msgstr "Hesaplamalar"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Cancel"
 msgstr "İptal"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Cancel Payslip"
 msgstr "Bordro İptal"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:96
 #, python-format
 msgid "Cannot cancel a payslip that is done."
 msgstr "Yapılan bordrolar iptal edilemez."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__category_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__category_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Category"
 msgstr "Kategori"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Child Rules"
 msgstr "Alt Kurallar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__child_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__child_ids
 msgid "Child Salary Rule"
 msgstr "Alt Ücret Kural"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__children_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__children_ids
 msgid "Children"
 msgstr "Alt"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Children Definition"
 msgstr "Alt Tanımı"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Choose a Payroll Localization"
 msgstr "Bir Bordro Yerelleştirmesi seçiniz."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Close"
 msgstr "Kapat"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__code
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Code"
 msgstr "Kod"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payroll_community_structure_view_kanban
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_view_kanban
 msgid "Code:"
 msgstr "Kod:"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Companies"
 msgstr "Şirketler"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__company_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__company_id
 msgid "Company"
 msgstr "Şirket"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.COMP
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Company Contribution"
 msgstr "Şirket Katkısı"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Computation"
 msgstr "Hesaplama"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Compute Sheet"
 msgstr "Tabloyu Hesapla"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_select
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_select
 msgid "Condition Based on"
 msgstr "Koşul Bazında"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Conditions"
 msgstr "Koşullar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_res_config_settings
 msgid "Config Settings"
 msgstr "Konfigürasyon Ayarları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_configuration
 msgid "Configuration"
 msgstr "Yapılandırma"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Confirm"
 msgstr "Doğrula"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__contract_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "Contract"
 msgstr "Sözleşme"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.hr_contract_advantage_template_action
 #: model:ir.ui.menu,name:hr_payroll_community_v13.hr_contract_advantage_template_menu_action
 msgid "Contract Advantage Templates"
 msgstr "Sözleşme Yan Hak Şablonu"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 msgid "Contribution"
 msgstr "Katkı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contribution_register
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__register_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Contribution Register"
 msgstr "Katkı Payı Kaydı"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Contribution Register's Payslip Lines"
 msgstr "Destek Kayıtlı kullanıcısının bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_contribution_register_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_tree
 msgid "Contribution Registers"
 msgstr "Katkı Payı Kayıtları"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_convanceallowance1
 msgid "Conveyance Allowance"
 msgstr "Sevk Ödeneği"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_ca_gravie
 msgid "Conveyance Allowance For Gravie"
 msgstr "Gravie için Taşıma Ödeneği"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_uid
 msgid "Created by"
 msgstr "Oluşturan"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__create_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__create_date
 msgid "Created on"
 msgstr "Oluşturulma"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__credit_note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid "Credit Note"
 msgstr "İade/Fiyat Farkı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_from
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_start
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_from
 msgid "Date From"
 msgstr "Tarihi Itibaren"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__date_to
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__date_end
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__date_to
 msgid "Date To"
 msgstr "Date To"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.DED
 msgid "Deduction"
 msgstr "Deduction"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__default_value
 msgid "Default value for this advantage"
 msgstr "Bu yan hak için varsayılan değer"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Defines the frequency of the wage payment."
 msgstr "Maaş ödemelerinin sıklığını tanımlayınız"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid ""
 "Defines the rules that have to be applied to this payslip, accordingly to "
 "the contract chosen. If you let empty the field contract, this field isn't "
 "mandatory anymore and thus the rules applied will be all the rules set on "
 "the structure of all contracts of the employee valid for the chosen period"
 msgstr ""
 "Seçilen sözleşmeye uygun olarak bu bordroda uygulanacak kuralları "
 "tanımlayın. Sözleşme alanını boş bırakırsanız, bu alan artık zorunlu olmaz "
 "ve böylece uygulanan kurallar, personel sözleşmelerinin yapısında seçilen "
 "dönem için tanımlanan bütün kurallar  için geçerli olacaktır."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__note
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__note
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_contribution_register_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Description"
 msgstr "Açıklama"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Details By Salary Rule Category"
 msgstr "Ücret Kural Kategori Detayları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__details_by_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Details by Salary Rule Category"
 msgstr "Maaş Kuralı Kategorisine göre ayrıntılar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_contributionregister__display_name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_payslipdetails__display_name
 msgid "Display Name"
 msgstr "Görünüm Adı"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done"
 msgstr "Tamamlandı"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Done Payslip Batches"
 msgstr "Biten Bordrosu Toplu"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Done Slip"
 msgstr "Tamamlanan Fiş"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0 selection:hr.payslip.run,state:0
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft"
 msgstr "Taslak"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Draft Payslip Batches"
 msgstr "Taslak Bordro Toplu"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Draft Slip"
 msgstr "Taslak Fiş"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_employee
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__employee_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__employee_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Employee"
 msgstr "Personel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract
 msgid "Employee Contract"
 msgstr "Personel Sözleşmesi"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_structure_list_view
 msgid "Employee Function"
 msgstr "Personel Fonksiyonu"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payslip_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_department_tree
 msgid "Employee Payslips"
 msgstr "Personel Bordroları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_contract_advantage_template
 msgid "Employee's Advantage on Contract"
 msgstr "Sözleşmedeki Personel Yan Hakları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Employee's working schedule."
 msgstr "Personelin çalışma saatleri."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__employee_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Employees"
 msgstr "Personeller"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:92
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
 msgstr ""
 "Hata! Maaş Kuralı Kategorisi'nin yinelemeli hiyerarşisini oluşturamazsınız."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:179
 #, python-format
 msgid "Error! You cannot create recursive hierarchy of Salary Rules."
 msgstr "Hata! Maaş Kuralı'nın yinelemeli hiyerarşisini oluşturamazsınız."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__register_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__register_id
 msgid "Eventual third party involved in the salary payment of the employees."
 msgstr "Nihai üçüncü parti Personellerin maaş ödemeleri dahil."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_fix
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_fix
 msgid "Fixed Amount"
 msgstr "Sabit Tutar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "For example, enter 50.0 to apply a percentage of 50%"
 msgstr "Örenk, 50% yüzdesi uygulamak için 50,0 girin"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/report/report_contribution_register.py:35
 #, python-format
 msgid "Form content is missing, this report cannot be printed."
 msgstr "Form içeriği eksik, bu rapor yazdırılamıyor."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_fr_hr_payroll_community
 msgid "French Payroll"
 msgstr "Fransa Bodro"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "General"
 msgstr "Genel"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Generate"
 msgstr "Genel"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_by_employees
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 msgid "Generate Payslips"
 msgstr "Bordroları Oluştur"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_employees
 msgid "Generate payslips for all selected employees"
 msgstr "Seçilen tüm personeller için maaş bordroları oluştur"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_sales_commission
 msgid "Get 1% of sales"
 msgstr "Satışların %1'ini alın."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:182
 #, python-format
 msgid "Global Leaves"
 msgstr "Resmi Tatiller"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_taxable
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.GROSS
 msgid "Gross"
 msgstr "Brüt"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Group By"
 msgstr "Grupla"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_houserentallowance1
 msgid "House Rent Allowance"
 msgstr "Konut Kira Ödeneği"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_contributionregister__id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_payslipdetails__id
 msgid "ID"
 msgstr "ID"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_run__credit_note
 msgid ""
 "If its checked, indicates that all payslips generated from here are refund "
 "payslips."
 msgstr ""
 "Bu İşaretliyse, buradan oluşturulan tüm maaş bordroları iade olduğunu "
 "gösterir"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__active
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__active
 msgid ""
 "If the active field is set to false, it will allow you to hide the salary "
 "rule without removing it."
 msgstr ""
 "Etkin alanda yanlış ayarlanırsa, size maaş gizleme sağlayacak bu kural "
 "kaldırmadan."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_res_config_settings__module_l10n_in_hr_payroll_community
 msgid "Indian Payroll"
 msgstr "Hindistan Bordro"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip__credit_note
 msgid "Indicates this payslip has a refund of another"
 msgstr "Bu bordrosunu başka bir geri ödemesi varsa gösterir"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Input Data"
 msgstr "Veri Girişi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__input_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__input_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 msgid "Inputs"
 msgstr "Girişler"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__note
 msgid "Internal Note"
 msgstr "İç Not"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_view_kanban
 msgid "Is a Blocking Reason?"
 msgstr "Bir duruş nedeni mi?"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid ""
 "It is used in computation for percentage and fixed amount. For e.g. A rule "
 "for Meal Voucher having fixed amount of 1€ per worked day can have its "
 "quantity defined in expression like worked_days.WORK100.number_of_days."
 msgstr "Bu, yüzde ve sabitlenmiş miktarların hesabı için kullanıldı."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__amount
 msgid ""
 "It is used in computation. For e.g. A rule for sales having 1% commission of"
 " basic salary for per product can defined in expression like result = "
 "inputs.SALEURO.amount * contract.wage*0.01."
 msgstr ""
 "Bu hesaplama kullanılır. = % 1 komisyon temel maaşları ürün ifade sonucu "
 "gibi tanımlanmış satış sahip için e.g. A kuralı için girdi. SALEURO.amount *"
 " contract.wage*0.01."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_contributionregister____last_update
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_report_hr_payroll_community_report_payslipdetails____last_update
 msgid "Last Modified on"
 msgstr "Son Güncelleme"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_uid
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_uid
 msgid "Last Updated by"
 msgstr "Son Güncelleyen"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_employees__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__write_date
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_payslip_lines_contribution_register__write_date
 msgid "Last Updated on"
 msgstr "Son Güncelleme"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid ""
 "Linking a salary category to its parent is used only for the reporting "
 "purpose."
 msgstr "Amaç üst bir ücret kategori bağlantı raporlama için kullanılır."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower Bound"
 msgstr "Alt Sınır"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__lower_bound
 msgid "Lower bound authorized by the employer for this advantage"
 msgstr "Bu yan hak için işveren tarafından izin verilen alt sınır."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__paid
 msgid "Made Payment Order ? "
 msgstr "Made Payment Order ? "
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_manager
 msgid "Manager"
 msgstr "Yönetici"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "Maximum Range"
 msgstr "Maksimum Aralık"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_meal_voucher
 msgid "Meal Voucher"
 msgstr "Yemek Fişi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "Minimum Range"
 msgstr "Minimum Aralık"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Miscellaneous"
 msgstr "Diğer"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Monthly"
 msgstr "Aylık"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__name
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__name
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Name"
 msgstr "İsim"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule.category,name:hr_payroll_community_v13.NET
 msgid "Net"
 msgstr "Net"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_rule_net
 msgid "Net Salary"
 msgstr "Net Maaş"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:201
 #, python-format
 msgid "Normal Working Days paid at 100%"
 msgstr "Normal Çalışma Günleri %100 ödenmektedir"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Notes"
 msgstr "Notlar"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_days
 msgid "Number of Days"
 msgstr "Günlerin Sayısı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__number_of_hours
 msgid "Number of Hours"
 msgstr "Süre (Saat)"
 
 #. module: hr_payroll_community_v13
 #: model:res.groups,name:hr_payroll_community_v13.group_hr_payroll_community_user
 msgid "Officer"
 msgstr "Yetkili"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Other Inputs"
 msgstr "Diğer Girdiler"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__parent_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule_category__parent_id
 msgid "Parent"
 msgstr "Üst"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__parent_rule_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__parent_rule_id
 msgid "Parent Salary Rule"
 msgstr "Üst Ücret Kuralı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__partner_id
 msgid "Partner"
 msgstr "İş Ortağı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__payslip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__slip_id
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__payslip_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Pay Slip"
 msgstr "Pay Slip"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "PaySlip Batch"
 msgstr "Bordro Toplu İşlemi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.payslip_details_report
 msgid "PaySlip Details"
 msgstr "Bordrosu Detayları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_payslip_lines_contribution_register
 msgid "PaySlip Lines"
 msgstr "Bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_contribution_register
 msgid "PaySlip Lines By Conribution Register"
 msgstr "Destek Kayıtlı kullanıcısının maaş bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Lines by Contribution Register"
 msgstr "Destek Kayıtlı kullanıcısının maaş bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "PaySlip Name"
 msgstr "Bordro Adı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.open_payroll_modules
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_root
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll"
 msgstr "Bordro"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_report_contributionregister
 msgid "Payroll Contribution Register Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll Entries"
 msgstr "Bordro Kayıtları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_structure_filter
 msgid "Payroll Structures"
 msgstr "Bordro Yapısı"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Payroll rules that apply to your country"
 msgstr "Ülkenizde uygulanan bordro kuralları."
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.report,name:hr_payroll_community_v13.action_report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip"
 msgstr "Bordro"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:82
 #, python-format
 msgid "Payslip 'Date From' must be earlier 'Date To'."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_run
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_run_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Payslip Batches"
 msgstr "Toplu Bordro"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__payslip_count
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Payslip Computation Details"
 msgstr "Bordro Hesaplama Ayrıntıları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__payslip_count
 msgid "Payslip Count"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_report_hr_payroll_community_report_payslipdetails
 msgid "Payslip Details Report"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_input
 msgid "Payslip Input"
 msgstr "Bordro Girdisi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__input_line_ids
 msgid "Payslip Inputs"
 msgstr "Bordros Girdileri"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_line
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_form
 msgid "Payslip Line"
 msgstr "Bordro Satırı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_contribution_reg_payslip_lines
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__line_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Payslip Lines"
 msgstr "Bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Payslip Lines by Contribution Register"
 msgstr "Destek Kayıtlı kullanıcısının maaş bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_payslip_lines_contribution_register
 msgid "Payslip Lines by Contribution Registers"
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__name
 msgid "Payslip Name"
 msgstr "Bordro Adı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payslip_worked_days
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__worked_days_line_ids
 msgid "Payslip Worked Days"
 msgstr "Bordro Çalışılan Günler"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.act_hr_employee_payslip_list
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_employee__slip_ids
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__slip_ids
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.payroll_hr_employee_view_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_tree
 msgid "Payslips"
 msgstr "Bordroları"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payslip_run_tree
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payslip_run
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_tree
 msgid "Payslips Batches"
 msgstr "Bordroların Toplu-İşlemi"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_by_employees
 msgid "Payslips by Employees"
 msgstr "Personel Bordrolarına"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage
 msgid "Percentage (%)"
 msgstr "Yüzde (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "Percentage based on"
 msgstr "Yüzde Bazında"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Period"
 msgstr "Dönem"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.res_config_settings_view_form
 msgid "Post payroll slips in accounting"
 msgstr "Muhasebedeki bordro fişlerini postala"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_payslip_lines_contribution_register
 msgid "Print"
 msgstr "Yazdır"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_professionaltax1
 msgid "Professional Tax"
 msgstr "Meslek Vergisi"
 
 #. module: hr_payroll_community_v13
 #: model:hr.salary.rule,name:hr_payroll_community_v13.hr_salary_rule_providentfund1
 msgid "Provident Fund"
 msgstr "Tasarruf Sandığı"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,amount_select:0
 #: selection:hr.salary.rule,amount_select:0
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__amount_python_compute
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__amount_python_compute
 msgid "Python Code"
 msgstr "Python Kodu"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_python
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_python
 msgid "Python Condition"
 msgstr "Python Koşulu"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Python Expression"
 msgstr "Python Expression"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__quantity
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__quantity
 msgid "Quantity"
 msgstr "Miktar"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 msgid "Quantity/Rate"
 msgstr "Miktar/Oran"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Quantity/rate"
 msgstr "Miktar/oran"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Quarterly"
 msgstr "Üç Aylık"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip.line,condition_select:0
 #: selection:hr.salary.rule,condition_select:0
 msgid "Range"
 msgstr "Aralık"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid "Range Based on"
 msgstr "Aralık Bazında"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__rate
 msgid "Rate (%)"
 msgstr "Oran (%)"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__code
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__number
 msgid "Reference"
 msgstr "Referans"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Refund"
 msgstr "İade"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:102
 #, python-format
 msgid "Refund: "
 msgstr "İade: "
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contribution_register__register_line_ids
 msgid "Register Line"
 msgstr "Kayıt Satırı"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Rejected"
 msgstr "Reddedildi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__salary_rule_id
 msgid "Rule"
 msgstr "Kural"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_form
 msgid "Salary Categories"
 msgstr "Ücret Kategoriler"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Salary Computation"
 msgstr "Ücret Hesaplama"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule
 msgid "Salary Rule"
 msgstr "Maaş Kuralı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_salary_rule_category
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_category_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_salary_rule_category_filter
 msgid "Salary Rule Categories"
 msgstr "Ücret Kural Kategorileri"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_salary_rule_category
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Salary Rule Category"
 msgstr "Ücret Kural Kategorisi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_rule_input
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_rule_input__input_id
 msgid "Salary Rule Input"
 msgstr "Ücret Kural Girdisi"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_salary_rule_form
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payroll_community_structure__rule_ids
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_action_hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_list
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_salary_rule_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_employee_grade_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Salary Rules"
 msgstr "Ücret Kuralları"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:403
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:453
 #, python-format
 msgid "Salary Slip of %s for %s"
 msgstr "Ücret Makbuzu %s nın %s"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model,name:hr_payroll_community_v13.model_hr_payroll_community_structure
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__struct_id
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payroll_community_structure_tree
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_tree
 msgid "Salary Structure"
 msgstr "Ücret Yapısı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_view_hr_payroll_community_structure_list_form
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_structure_view
 msgid "Salary Structures"
 msgstr "Ücret Yapısı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__schedule_pay
 msgid "Scheduled Pay"
 msgstr "Ücret Ödeme Türü"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_filter
 msgid "Search Payslip Batches"
 msgstr "Bordros Toplu İşlemi Arama"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_line_filter
 msgid "Search Payslip Lines"
 msgstr "Arama Bordro Satırları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "Search Payslips"
 msgstr "Arama Bordroları"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_rule_filter
 msgid "Search Salary Rule"
 msgstr "Maaş Arama Kuralı"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Semi-annually"
 msgstr "Altı Aylık"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_input__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_worked_days__sequence
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Sequence"
 msgstr "Sıra"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.hr_payslip_run_form
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Set to Draft"
 msgstr "Taslak olarak ayarla"
 
 #. module: hr_payroll_community_v13
 #: model:ir.actions.act_window,name:hr_payroll_community_v13.action_hr_payroll_community_configuration
 #: model:ir.ui.menu,name:hr_payroll_community_v13.menu_hr_payroll_community_global_settings
 msgid "Settings"
 msgstr "Ayarlar"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_filter
 msgid "States"
 msgstr "Durum"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__state
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_run__state
 msgid "Status"
 msgstr "Durumu"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip__struct_id
 msgid "Structure"
 msgstr "Structure"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__code
 msgid ""
 "The code of salary rules can be used as reference in computation of other "
 "rules. In that case, it is case sensitive."
 msgstr ""
 "Maaş kurallarının kodu, hesaplama diğer kurallar başvuru olarak "
 "kullanılabilir. Bu durumda, büyük küçük harf duyarlı."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__code
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_rule_input__code
 msgid "The code that can be used in the salary rules"
 msgstr "Ücret kurallar için kullanılabilir kod"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_select
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_select
 msgid "The computation method for the rule amount."
 msgstr "Kural miktarı hesaplama yöntemi."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_input__contract_id
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_worked_days__contract_id
 msgid "The contract for which applied this input"
 msgstr "Sözleşme bu giriş uygulandı"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_max
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_max
 msgid "The maximum amount, applied for this rule."
 msgstr "Bu kural için uygulanan azami tutar."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range_min
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range_min
 msgid "The minimum amount, applied for this rule."
 msgstr "Bu kural için uygulanan minimum tutar,."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__condition_range
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__condition_range
 msgid ""
 "This will be used to compute the % fields values; in general it is on basic,"
 " but you can also use categories code fields in lowercase as a variable "
 "names (hra, ma, lta, etc.) and the variable basic."
 msgstr ""
 "Bu % alanlarının değerlerini hesaplamak için kullanılacaktır; Genel olarak "
 "bu temel temel, ama küçük Kategoriler kod alanları (hra, ma, lta, vb) bir "
 "değişken adları ve temel değişkeni kullanabilirsiniz."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_payslip_line__total
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_contributionregister
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslip
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.report_payslipdetails
 msgid "Total"
 msgstr "Toplam"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Total Working Days"
 msgstr "Toplam Çalışma Günleri"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper Bound"
 msgstr "Üst Sınır"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_contract_advantage_template__upper_bound
 msgid "Upper bound authorized by the employer for this advantage"
 msgstr "Bu yan hak için işveren tarafından izin verilen üst sınır."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__sequence
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__sequence
 msgid "Use to arrange calculation sequence"
 msgstr "Hesaplama sırasını düzenlemek için kullanın"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__appears_on_payslip
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__appears_on_payslip
 msgid "Used to display the salary rule on payslip."
 msgstr "Maaş bordrosu üzerinde maaş kuralı göstermek için kullanılır."
 
 #. module: hr_payroll_community_v13
 #: selection:hr.payslip,state:0
 msgid "Waiting"
 msgstr "Bekleyen"
 
 #. module: hr_payroll_community_v13
 #: selection:hr.contract,schedule_pay:0
 msgid "Weekly"
 msgstr "Haftalık"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Day"
 msgstr "Çalışılan Gün"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days"
 msgstr "Çalışılan Günler"
 
 #. module: hr_payroll_community_v13
 #: model_terms:ir.ui.view,arch_db:hr_payroll_community_v13.view_hr_payslip_form
 msgid "Worked Days & Inputs"
 msgstr "Çalışılan Gün ve Girişler"
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,field_description:hr_payroll_community_v13.field_hr_contract__resource_calendar_id
 msgid "Working Schedule"
 msgstr "Çalışma Saatleri"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:211
 #, python-format
 msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
 msgstr "Yanlış yüzde bazlı veya ücret adet kuralı %s (%s) için tanımlanan."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:217
 #, python-format
 msgid "Wrong python code defined for salary rule %s (%s)."
 msgstr "Yanlış python kodu ücret kural %s (%s) için tanımlanan."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:240
 #, python-format
 msgid "Wrong python condition defined for salary rule %s (%s)."
 msgstr "Ücret kuralı için tanımlanan Yanlış python koşulu %s (%s)."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:204
 #, python-format
 msgid "Wrong quantity defined for salary rule %s (%s)."
 msgstr "Yanlış miktar maaş kural %s (%s) için tanımlanan."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:234
 #, python-format
 msgid "Wrong range condition defined for salary rule %s (%s)."
 msgstr "Yanlış aralık koşulu ücret kural %s (%s) için tanımlama."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_salary_rule.py:36
 #, python-format
 msgid "You cannot create a recursive salary structure."
 msgstr ""
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:127
 #, python-format
 msgid "You cannot delete a payslip which is not draft or cancelled!"
 msgstr "Sen taslak veya iptal olmayan bir maaş bordrosu silemezsiniz!"
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/wizard/hr_payroll_community_payslips_by_employees.py:24
 #, python-format
 msgid "You must select employee(s) to generate payslip(s)."
 msgstr "Bu bordroyu üretmek için persone(ler) seçmelisiniz."
 
 #. module: hr_payroll_community_v13
 #: code:addons/hr_payroll_community_v13/models/hr_payslip.py:525
 #, python-format
 msgid "You must set a contract to create a payslip line."
 msgstr "Bordro satırı oluşturmak için sözleşme düzenlenmelisiniz."
 
 #. module: hr_payroll_community_v13
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_payslip_line__amount_percentage_base
 #: model:ir.model.fields,help:hr_payroll_community_v13.field_hr_salary_rule__amount_percentage_base
 msgid "result will be affected to a variable"
 msgstr "sonuç bir değişkene etkilenecektir"
 