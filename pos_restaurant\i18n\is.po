# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_restaurant
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-08-24 09:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Icelandic (https://www.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 3.12"
msgstr "$ 3.12"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.40"
msgstr "$ 4.40"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 4.50"
msgstr "$ 4.50"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "$ 8.50"
msgstr "$ 8.50"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_amount\">$ 0.86</span>"
msgstr "<span class=\"pos-change_amount\">$ 0.86</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"pos-change_title\">Change</span>"
msgstr "<span class=\"pos-change_title\">Afgangur</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span class=\"total-amount-formatting\">TOTAL</span>"
msgstr "<span class=\"total-amount-formatting\">SAMTALS</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"
msgstr "<span id=\"total-amount\" class=\"pos_total-amount\">$ 469.14</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>$ 470.00</span>"
msgstr "<span>$ 470.00</span>"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "<span>Cash (USD):</span>"
msgstr "<span>Staðgreitt (USD):</span>"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Floor Name: </strong>"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_kanban
msgid "<strong>Point of Sale: </strong>"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_image
msgid ""
"A background image used to display a floor layout in the point of sale "
"interface"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid ""
"A restaurant floor represents the place where customers are served, this is where you can\n"
"                define and position the tables."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Accept customer tips or convert their change to a tip"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__active
msgid "Active"
msgstr "Virkur"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:113
#, python-format
msgid "Add"
msgstr "Bæta við"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/notes.js:54
#, python-format
msgid "Add Note"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_floor_form
msgid "Add a new restaurant floor"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid "Add a new restaurant order printer"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:107
#, python-format
msgid "Add button"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Add notes to orderlines"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Allow custom notes on Orderlines."
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Allow to print bill before payment"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_printbill
msgid "Allows to print the Bill before payment."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__name
msgid "An internal identification of a table"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__name
msgid "An internal identification of the printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__name
msgid "An internal identification of the restaurant floor"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Appearance"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:562
#, python-format
msgid "Are you sure ?"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:9
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:61
#, python-format
msgid "Back"
msgstr "Til baka"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:82
#, python-format
msgid "Back to floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_color
msgid "Background Color"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__background_image
msgid "Background Image"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:31
#, python-format
msgid "Bill"
msgstr "Reikningur"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:12
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_printbill
#, python-format
msgid "Bill Printing"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:64
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_splitbill
#, python-format
msgid "Bill Splitting"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:136
#: code:addons/pos_restaurant/static/src/xml/floors.xml:150
#, python-format
msgid "Blue"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:39
#, python-format
msgid "CANCELLED"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:270
#: code:addons/pos_restaurant/static/src/js/floors.js:309
#: code:addons/pos_restaurant/static/src/js/floors.js:425
#, python-format
msgid "Changes could not be saved"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:131
#: code:addons/pos_restaurant/static/src/xml/floors.xml:145
#, python-format
msgid "Close"
msgstr "Loka"

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.coke
#: model:product.template,name:pos_restaurant.coke_product_template
msgid "Coca-Cola"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__color
msgid "Color"
msgstr "Color"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_uid
msgid "Created by"
msgstr "Búið til af"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__create_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__create_date
msgid "Created on"
msgstr "Stofnað þann"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:160
#, python-format
msgid "Delete"
msgstr "Eyða"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Desk Organizer"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:91
#, python-format
msgid "Discount:"
msgstr "Afsláttur:"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:140
#, python-format
msgid "Discounts"
msgstr "Afslættir"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__display_name
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__display_name
msgid "Display Name"
msgstr "Nafn"

#. module: pos_restaurant
#: model:pos.category,name:pos_restaurant.drinks
msgid "Drinks"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:116
#, python-format
msgid "Duplicate"
msgstr "Afrita"

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"                Proxy where the printer can be found, and a list of product categories.\n"
"                An Order Printer will only print updates for products belonging to one of\n"
"                its categories."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:110
#, python-format
msgid "Edit"
msgstr "Skrifa"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__iface_splitbill
msgid "Enables Bill Splitting in the Point of Sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__floor_id
msgid "Floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__name
msgid "Floor Name"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_floor_form
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_floor_all
msgid "Floor Plans"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Floors"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:135
#: code:addons/pos_restaurant/static/src/xml/floors.xml:149
#, python-format
msgid "Green"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:140
#: code:addons/pos_restaurant/static/src/xml/floors.xml:154
#, python-format
msgid "Grey"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:28
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__customer_count
#, python-format
msgid "Guests"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:946
#, python-format
msgid "Guests ?"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:10
#: code:addons/pos_restaurant/static/src/xml/floors.xml:21
#, python-format
msgid "Guests:"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__height
msgid "Height"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_h
msgid "Horizontal Position"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__id
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__id
msgid "ID"
msgstr "Auðkenni"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__active
msgid ""
"If false, the table is deactivated and will not be available in the point of"
" sale"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Er bar/veitingastaður"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer____last_update
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table____last_update
msgid "Last Modified on"
msgstr "Síðast breytt þann"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_uid
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_uid
msgid "Last Updated by"
msgstr "Síðast uppfært af"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__write_date
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__write_date
msgid "Last Updated on"
msgstr "Síðast uppfært þann"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Led Lamp"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:141
#: code:addons/pos_restaurant/static/src/xml/floors.xml:155
#, python-format
msgid "Light grey"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:39
#, python-format
msgid "Logo"
msgstr "Lógó"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Manage table orders"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.minute_maid
#: model:product.template,name:pos_restaurant.minute_maid_product_template
msgid "Minute Maid"
msgstr ""

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Monitor Stand"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:62
#, python-format
msgid "NEW"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:50
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:73
#, python-format
msgid "NOTE"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/notes.xml:8
#: code:addons/pos_restaurant/static/src/xml/notes.xml:16
#, python-format
msgid "Note"
msgstr "Athugasemd"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:495
#, python-format
msgid "Number of Seats ?"
msgstr ""

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Odoo Logo"
msgstr "Odoo táknmynd"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:13
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:137
#: code:addons/pos_restaurant/static/src/xml/floors.xml:151
#, python-format
msgid "Orange"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/multiprint.xml:6
#, python-format
msgid "Order"
msgstr "Pöntun"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.actions.act_window,name:pos_restaurant.action_restaurant_printer_form
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__printer_ids
#: model:ir.ui.menu,name:pos_restaurant.menu_restaurant_printer_all
msgid "Order Printers"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.actions.act_window,help:pos_restaurant.action_restaurant_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"                order updates in the kitchen/bar when the waiter updates the order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__iface_orderline_notes
msgid "Orderline Notes"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer_form
msgid "POS Printer"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:78
#, python-format
msgid "Payment"
msgstr "Greiðsla"

#. module: pos_restaurant
#: model:product.category,name:pos_restaurant.product_category_pos
msgid "PoS"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__pos_config_id
msgid "Point of Sale"
msgstr "Kassakerfi"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Uppsetning kassakerfis"

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pantanir kassa"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Price"
msgstr "Verð"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:20
#, python-format
msgid "Print"
msgstr "Prenta"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Print orders at the kitchen, at the bar, etc."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__name
msgid "Printer Name"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Printers"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "Proxy IP Address"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:139
#: code:addons/pos_restaurant/static/src/xml/floors.xml:153
#, python-format
msgid "Purple"
msgstr ""

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Quantity"
msgstr "Magn"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:134
#: code:addons/pos_restaurant/static/src/xml/floors.xml:148
#, python-format
msgid "Red"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:563
#, python-format
msgid "Removing a table cannot be undone"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:119
#, python-format
msgid "Rename"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_floor
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Restaurant Floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__floor_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_tree
msgid "Restaurant Floors"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_printer
msgid "Restaurant Order Printers"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_printer
msgid "Restaurant Printer"
msgstr ""

#. module: pos_restaurant
#: model:ir.model,name:pos_restaurant.model_restaurant_table
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_table_form
msgid "Restaurant Table"
msgstr ""

#. module: pos_restaurant
#: selection:restaurant.table,shape:0
msgid "Round"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:126
#, python-format
msgid "Round Shape"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:122
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__seats
#, python-format
msgid "Seats"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__sequence
msgid "Sequence"
msgstr "Runa"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:71
#, python-format
msgid "Served by"
msgstr "Afgreitt af"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__shape
msgid "Shape"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:6
#, python-format
msgid "Split"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Split total or order lines"
msgstr ""

#. module: pos_restaurant
#: selection:restaurant.table,shape:0
msgid "Square"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:125
#, python-format
msgid "Square Shape"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:118
#, python-format
msgid "Subtotal"
msgstr "Alls"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:131
#, python-format
msgid "TOTAL"
msgstr "SAMTALS"

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_order__table_id
msgid "Table"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_pos_config__is_table_management
msgid "Table Management"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__name
msgid "Table Name"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:483
#, python-format
msgid "Table Name ?"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_floor__table_ids
#: model_terms:ir.ui.view,arch_db:pos_restaurant.view_restaurant_floor_form
msgid "Tables"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/printbill.xml:54
#, python-format
msgid "Tel:"
msgstr "Sími:"

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__customer_count
msgid "The amount of customers that have been served by this order."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__background_color
msgid ""
"The background color of the floor layout, (must be specified in a html-"
"compatible format)"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__seats
msgid "The default number of customer served at this table."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__table_ids
msgid "The list of tables in this floor"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_config__floor_ids
msgid "The restaurant floors served by this point of sale."
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_pos_order__table_id
msgid "The table where this order was served"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__color
msgid ""
"The table's color, expressed as a valid 'background' CSS property value"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__height
msgid "The table's height in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_h
msgid ""
"The table's horizontal position from the left side to the table's center, in"
" pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__position_v
msgid ""
"The table's vertical position from the top to the table's center, in pixels"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_table__width
msgid "The table's width in pixels"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:106
#, python-format
msgid "This floor has no tables yet, use the"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "This product is used as reference on customer receipts."
msgstr "Þessi vara er notuð til vísunar á kvittunum viðskiptavina."

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:129
#, python-format
msgid "Tint"
msgstr ""

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Tip Product"
msgstr "Þjórfé vöru"

#. module: pos_restaurant
#: model_terms:ir.ui.view,arch_db:pos_restaurant.pos_config_view_form_inherit_restaurant
msgid "Tips"
msgstr "Þjórfé"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:37
#, python-format
msgid "Transfer"
msgstr "Transfer"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:142
#: code:addons/pos_restaurant/static/src/xml/floors.xml:156
#, python-format
msgid "Turquoise"
msgstr ""

#. module: pos_restaurant
#: model:product.product,uom_name:pos_restaurant.coke
#: model:product.product,uom_name:pos_restaurant.minute_maid
#: model:product.product,uom_name:pos_restaurant.water
#: model:product.template,uom_name:pos_restaurant.coke_product_template
#: model:product.template,uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,uom_name:pos_restaurant.water_product_template
msgid "Unit(s)"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,help:pos_restaurant.field_restaurant_floor__sequence
msgid "Used to sort Floors"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__position_v
msgid "Vertical Position"
msgstr ""

#. module: pos_restaurant
#: model:product.product,name:pos_restaurant.water
#: model:product.template,name:pos_restaurant.water_product_template
msgid "Water"
msgstr "Vatn"

#. module: pos_restaurant
#: model_terms:pos.config,customer_facing_display_html:pos_restaurant.pos_config_restaurant
msgid "Whiteboard Pen"
msgstr ""

#. module: pos_restaurant
#: model:ir.model.fields,field_description:pos_restaurant.field_restaurant_table__width
msgid "Width"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:44
#, python-format
msgid "With a"
msgstr "Með"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:138
#: code:addons/pos_restaurant/static/src/xml/floors.xml:152
#, python-format
msgid "Yellow"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/js/floors.js:271
#: code:addons/pos_restaurant/static/src/js/floors.js:310
#: code:addons/pos_restaurant/static/src/js/floors.js:426
#, python-format
msgid "You must be connected to the internet to save your changes."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:36
#, python-format
msgid "at"
msgstr "at"

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:6
#: code:addons/pos_restaurant/static/src/xml/floors.xml:17
#, python-format
msgid "at table"
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/floors.xml:107
#, python-format
msgid "button in the editing toolbar to create new tables."
msgstr ""

#. module: pos_restaurant
#. openerp-web
#: code:addons/pos_restaurant/static/src/xml/splitbill.xml:46
#, python-format
msgid "discount"
msgstr "afslætti"

#. module: pos_restaurant
#: model:product.product,weight_uom_name:pos_restaurant.coke
#: model:product.product,weight_uom_name:pos_restaurant.minute_maid
#: model:product.product,weight_uom_name:pos_restaurant.water
#: model:product.template,weight_uom_name:pos_restaurant.coke_product_template
#: model:product.template,weight_uom_name:pos_restaurant.minute_maid_product_template
#: model:product.template,weight_uom_name:pos_restaurant.water_product_template
msgid "kg"
msgstr ""
