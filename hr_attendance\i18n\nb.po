# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON>, 2020
# <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2020
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~13.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-09-01 07:28+0000\n"
"PO-Revision-Date: 2020-09-07 08:12+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: <PERSON> Bokmål (https://www.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "Tid for utsjekking kan ikke være før tid for innsjekking."

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s"
msgstr "%(empl_name)s fra %(check_in)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%(empl_name)s from %(check_in)s to %(check_out)s"
msgstr "%(empl_name)s fra %(check_in)s til %(check_out)s"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid ""
"<b>Warning! Last check in was over 12 hours ago.</b><br/>If this isn't "
"right, please contact Human Resource staff"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Employee PIN</span>"
msgstr "<span class=\"o_form_label\">Ansatt-PIN</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Month\n"
"                        </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attendance</span>"
msgstr "<span class=\"o_stat_text\">Oppmøte</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Attended Since</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Not Attended Since</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle "
"oe_hr_attendance_status_green\" role=\"img\" aria-label=\"Available\" "
"title=\"Available\"/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle oe_hr_attendance_status_orange\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "An apple a day keeps the doctor away"
msgstr "Et eple om dagen er bra for magen"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Another good day's work! See you soon!"
msgstr "Enda et godt dagsverk unnagjort! Sees snart!"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_my_attendances
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__attendance_ids
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Attendance"
msgstr "Oppmøte"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_graph_filtered
msgid "Attendance Analysis"
msgstr "Oppmøteanalyse"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action_employee
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_kiosk_mode
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Oppmøte"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#, python-format
msgid "Available"
msgstr "Tilgjengelig"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_base
msgid "Basic Employee"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Kan ikke registrere nytt oppmøte for %(empl_name)s, den ansatte har ikke "
"sjekket ut siden %(datetime)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Kan ikke registrere nytt oppmøte for %(empl_name)s, den ansatte sjekket inn "
"allerede %(datetime)s"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Kan ikke sjekke ut %(empl_name)s, fant ingen tilsvarende innsjekking. "
"Oppmøtet ditt har sannsynligvis blitt endret manuelt av HR."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check In"
msgstr "Sjekk inn"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_my_attendances
msgid "Check In / Check Out"
msgstr "Sjekk inn / sjekk ut"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Check Out"
msgstr "Sjekk ut"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Check-In/Out"
msgstr "Sjekk inn/ut"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee_base__attendance_state__checked_in
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee_public__attendance_state__checked_in
msgid "Checked in"
msgstr "Sjekket inn"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked in at"
msgstr "Sjekket inn kl."

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee_base__attendance_state__checked_out
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee_public__attendance_state__checked_out
msgid "Checked out"
msgstr "Sjekket ut kl."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Checked out at"
msgstr "Sjekket ut kl."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Click to"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Company Logo"
msgstr "Firmalogo"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurasjonsinnstillinger"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
msgid "Created on"
msgstr "Opprettet"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
msgid "Department"
msgstr "Avdeling"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Early to bed and early to rise, makes a man healthy, wealthy and wise"
msgstr "Tidlig i seng og tidlig opp er nyttig for både sjel og kropp"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Eat breakfast as a king, lunch as a merchant and supper as a beggar"
msgstr "Spis frokost som en konge, lunsj som en prins og middag som en tigger"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid "Employee"
msgstr "Ansatt"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__group_attendance_use_pin
msgid "Employee PIN"
msgstr "Ansatt-PIN"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "Ansattes oppmøte"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_employees_kanban
msgid "Employees"
msgstr "Ansatte"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_use_pin
msgid "Enable PIN use"
msgstr "Slå på bruk av PIN"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "Feil: Fant ikke tilsvarende ansatt."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "First come, first served"
msgstr "Først til mølla får først malt"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Glad to have you back, it's been a while!"
msgstr "Fint å se deg igjen, nå var det en stund siden!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Go back"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good afternoon"
msgstr "God ettermiddag"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good evening"
msgstr "God kveld"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good morning"
msgstr "God morgen"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Good night"
msgstr "Godnatt"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Goodbye"
msgstr "Ha det bra"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Grupper etter"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good afternoon"
msgstr "Ha en fin ettermiddag"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good day!"
msgstr "Ha en fin dag!"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a good evening"
msgstr "Ha en fin kveld"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "Have a nice lunch!"
msgstr "Ha en hyggelig lunsj!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Timer"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Søk i oppmøte for HR"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__id
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__id
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Identify Manually"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "If a job is worth doing, it is worth doing well!"
msgstr "Er jobben verdt å gjøre, er det verdt å gjøre den bra"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Invalid request, please return to the main menu."
msgstr "Ugyldig forespørsel, gå tilbake til hovedmenyen."

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_kiosk
msgid "Kiosk Attendance"
msgstr ""

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
msgid "Kiosk Mode"
msgstr "Kioskmodus"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Siste oppmøte"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_base____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings____last_update
#: model:ir.model.fields,field_description:hr_attendance.field_res_users____last_update
msgid "Last Modified on"
msgstr "Sist endret"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_manage_attendances
msgid "Manager"
msgstr "Leder"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance
msgid "Manual Attendance"
msgstr "Manuelt oppmøte"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Melding"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "Mitt oppmøte"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "No Check Out"
msgstr "Ingen utsjekking"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "No attendance records found"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "No attendance records to display"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_graph
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_graph_filtered
msgid "No data yet!"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Not available"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "OK"
msgstr "OK"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_user
msgid "Officer"
msgstr "Funksjonær"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "Gå tilbake til hovedmenyen."

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_report
msgid "Reporting"
msgstr "Rapportering"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Skann kortet ditt"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr "Sett PIN-koder i ansattdetaljene (under fanen HR-innstillinger)."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Innstillinger"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign in"
msgstr "Logg på"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Sign out"
msgstr "Logg av"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Such grouping is not allowed."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action_employee
msgid "The attendance records of your employees will be displayed here."
msgstr "De ansattes oppmøte vil vises her."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/js/greeting_message.js:0
#, python-format
msgid "The early bird catches the worm"
msgstr "Morgenstund har gull i munn"

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_kiosk
msgid ""
"The user will be able to open the kiosk mode and validate the employee PIN."
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance
msgid ""
"The user will gain access to the human resources attendance menu, enabling "
"him to manage his own attendance."
msgstr ""
"Brukeren får tilgang til HRs oppmøtemeny og administrasjon av eget oppmøte."

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_use_pin
msgid ""
"The user will have to enter his PIN to check in and out manually at the "
"company screen."
msgstr ""
"Brukeren må taste inn PIN for å sjekke inn og ut manuelt på firmaets skjerm."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Today's work hours:"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes to check in in Kiosk Mode"
msgstr "Bruk PIN-koder til innsjekking i kioskmodus"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "Users"
msgstr "Brukere"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Want to check out?"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid ""
"Warning : Your user should be linked to an employee to use attendance. "
"Please contact your administrator."
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome"
msgstr "Velkommen"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome to"
msgstr "Velkommen til"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "Welcome!"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
msgid "Worked Hours"
msgstr "Timer arbeidet"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours last month"
msgstr ""

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid "Wrong PIN"
msgstr "Feil PIN"

#. module: hr_attendance
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "Du kan ikke kopiere et oppmøte."

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check in"
msgstr ""

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "check out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_ids
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_base__attendance_ids
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_public__attendance_ids
msgid "list of attendances for the employee"
msgstr "liste over den ansattes oppmøte"

#. module: hr_attendance
#. openerp-web
#: code:addons/hr_attendance/static/src/xml/attendance.xml:0
#, python-format
msgid "or"
msgstr "eller"
