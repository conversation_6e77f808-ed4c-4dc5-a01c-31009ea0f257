# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-22 09:11+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Paraguay) (http://www.transifex.com/odoo/odoo-9/"
"language/es_PY/)\n"
"Language: es_PY\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase
#: model:mail.template,body_html:purchase.email_template_edi_purchase_done
msgid ""
"\n"
"<p>Dear\n"
"% if object.partner_id.is_company and object.child_ids:\n"
"    ${object.partner_id.child_ids[0].name}\n"
"% else :\n"
"    ${object.partner_id.name}\n"
"% endif\n"
",</p><p>\n"
"Here is a ${object.state in ('draft', 'sent') and 'request for quotation' or "
"'purchase order confirmation'} <strong>${object.name}</strong>\n"
"% if object.partner_ref:\n"
"    with reference: ${object.partner_ref}\n"
"% endif\n"
"% if object.origin:\n"
"    (RFQ origin: ${object.origin})\n"
"% endif\n"
"amounting <strong>${object.amount_total} ${object.currency_id.name}</"
"strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"<p>If you have any question, do not hesitate to contact us.</p>\n"
"<p>Best regards,</p>\n"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_module_stock_dropshipping
msgid ""
"\n"
"Creates the dropship Route and add more complex tests\n"
"-This installs the module stock_dropshipping."
msgstr ""

#. module: purchase
#: code:addons/purchase/stock.py:81
#, python-format
msgid " Buy"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_count
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_count
msgid "# Purchases"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_supplier_invoice_count
msgid "# Vendor Bills"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_count
msgid "# of Invoices"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_nbr_lines
msgid "# of Lines"
msgstr "Nº de líneas"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_purchase_order_count
msgid "# of Purchase Order"
msgstr ""

#. module: purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase
#: model:mail.template,subject:purchase.email_template_edi_purchase_done
msgid "${object.company_id.name} Order (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Date Req.</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Description</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Expected Date</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Net Price</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Order Date:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Our Order Reference:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Qty</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "<strong>Shipping address:</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Taxes</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total Without Taxes</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Total</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Unit Price</strong>"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "<strong>Your Order Reference</strong>"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_account_invoice_purchase_id
msgid "Add Purchase Order"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_advance_bidding
msgid "Advance bidding process"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_advance_purchase_requisition
msgid "Advanced Calls for Tenders"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_advance_purchase_requisition:0
msgid "Advanced call for tender (choose products from different RFQ)"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,module_stock_dropshipping:0
msgid "Allow suppliers to deliver directly to your customers"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_manage_vendor_price:0
msgid "Allow using and importing vendor pricelists"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,module_purchase_requisition:0
msgid ""
"Allow using call for tenders to get quotes from multiple suppliers (advanced)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_costing_method
msgid "Allows you to compute product cost price based on average cost."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_uom
msgid ""
"Allows you to select and maintain different units of measure for products."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"An administrator can set up default Terms and conditions in your Company "
"settings."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_account_analytic_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_account_analytic_id
msgid "Analytic Account"
msgstr "Cuenta Analítica"

#. module: purchase
#: model:res.groups,name:purchase.group_analytic_accounting
msgid "Analytic Accounting for Purchases"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Apply"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_approve
msgid "Approval Date"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Approve Order"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_procurement_ids
msgid "Associated Procurements"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_line_purchase_id
msgid ""
"Associated Purchase Order. Filled in automatically when a PO is chosen on "
"the vendor bill."
msgstr ""

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_average_delivery_time
msgid "Average Delivery Time"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_average
msgid "Average Price"
msgstr "Precio promedio"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_invoiced
msgid "Billed Qty"
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:717 code:addons/purchase/stock.py:75
#: model:stock.location.route,name:purchase.route_warehouse0_buy
#, python-format
msgid "Buy"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_pull_id
msgid "Buy rule"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_calendar
msgid "Calendar View"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_module_purchase_requisition
msgid "Calls for Tenders"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_module_purchase_requisition
msgid ""
"Calls for tenders are used when you want to generate requests for quotations "
"to several vendors for a given set of products.\n"
"You can configure per product if you directly do a Request for Quotation to "
"one vendor or if you want a Call for Tenders to compare offers from several "
"vendors."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_ok
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_ok
#: model_terms:ir.ui.view,arch_db:purchase.product_template_search_view_purchase
msgid "Can be Purchased"
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:733
#, python-format
msgid ""
"Can not cancel a procurement related to a purchase order. Please cancel the "
"purchase order first."
msgstr ""

#. module: purchase
#: code:addons/purchase/stock.py:78
#, python-format
msgid "Can't find any generic Buy route."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Cancel"
msgstr "Cancelar"

#. module: purchase
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: purchase
#: code:addons/purchase/purchase.py:612
#, python-format
msgid "Cannot delete a purchase order line which is in state '%s'."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid "Click here to record a vendor bill."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid "Click to create a draft invoice."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Click to create a quotation that will be converted into a purchase order."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid "Click to create a request for quotation."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid "Click to define a new product."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid "Click to record a vendor bill related to this purchase."
msgstr ""

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_1
msgid "Click to scrap products."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_company_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_company_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Company"
msgstr "Compañía"

#. module: purchase
#: code:addons/purchase/purchase.py:254
#, python-format
msgid "Compose Email"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config
msgid "Configuration"
msgstr "Configuración"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_configuration
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Configure Purchases"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Confirm Order"
msgstr ""

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Confirm purchase orders in one step"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_control
msgid "Control"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_product_purchase_method
#: model:ir.model.fields,field_description:purchase.field_product_template_purchase_method
msgid "Control Purchase Bills"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_costing_method
msgid "Costing Methods"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_create_date
msgid "Created on"
msgstr "Creado en"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_currency_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_approve
msgid "Date Approved"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_report_date_order
msgid "Date on which this document has been created"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay_pass
msgid "Days to Deliver"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_delay
msgid "Days to Validate"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_type_id
msgid "Deliver To"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Deliveries & Invoices"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_date_order
#: model:ir.model.fields,help:purchase.field_purchase_order_line_date_order
msgid ""
"Depicts the date where the Quotation should be validated and converted into "
"a purchase order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_name
msgid "Description"
msgstr "Descripción"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Destination Location Type"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_display_name
#: model:ir.model.fields,field_description:purchase.field_purchase_report_display_name
msgid "Display Name"
msgstr ""

#. module: purchase
#: selection:purchase.report,state:0
msgid "Done"
msgstr "Realizado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation_amount
msgid "Double validation amount"
msgstr ""

#. module: purchase
#: selection:purchase.order,state:0
msgid "Draft PO"
msgstr ""

#. module: purchase
#: selection:purchase.report,state:0
msgid "Draft RFQ"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_dest_address_id
msgid "Drop Ship Address"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_module_stock_dropshipping
msgid "Dropshipping"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_account_invoice_purchase_id
msgid ""
"Encoding help. When selected, the associated purchase order lines are added "
"to the vendor bill. Several PO can be selected."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Extended Filters"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_fiscal_position_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_fiscal_position_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: purchase
#: selection:res.company,po_double_validation:0
msgid "Get 2 levels of approvals to confirm a purchase order"
msgstr ""

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_3
msgid "Get all the shipments related to this order."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_weight
msgid "Gross Weight"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Group By"
msgstr "Agrupado por"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Hide cancelled lines"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_id
msgid "ID"
msgstr "ID"

#. module: purchase
#: model:web.tip,description:purchase.purchase_tip_2
msgid ""
"If a product has been broken or damaged during the transport, you can scrap "
"it with this button."
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:186
#, python-format
msgid "In order to delete a purchase order, you must cancel it first."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_advance_purchase_requisition
msgid ""
"In the process of a public tendering, you can compare the tender lines and "
"choose for each requested product which quantity you will buy from each bid."
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_action_picking_tree_in_move
msgid "Incoming  Products"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Incoming Shipments"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_incoterm_id
msgid "Incoterm"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_incoterm_id
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: purchase
#: model:ir.model,name:purchase.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea factura"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_invoice_lines
msgid "Invoice Lines"
msgstr "Líneas de factura"

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Invoice Received"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_status
msgid "Invoice Status"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Invoiced"
msgstr "Facturado"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices"
msgstr "Facturas"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Invoices and Incoming Shipments"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line___last_update
#: model:ir.model.fields,field_description:purchase.field_purchase_report___last_update
msgid "Last Modified on"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_uid
msgid "Last Updated by"
msgstr "Ultima actualización por"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_write_date
#: model:ir.model.fields,field_description:purchase.field_purchase_order_write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_double_validation
msgid "Levels of Approvals"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
msgid "Location & Warehouse"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Lock Bills"
msgstr ""

#. module: purchase
#: selection:purchase.order,state:0
msgid "Locked"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_manage_vendor_price
msgid "Manage Vendor Price"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_manage_vendor_price:0
msgid "Manage vendor price on the product form"
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_manager
msgid "Manager"
msgstr "Gerente"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Manual Invoices"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_lead
msgid ""
"Margin of error for vendor lead times. When the system generates Purchase "
"Orders for procuring products, they will be scheduled that many days earlier "
"to cope with unexpected vendor delays."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation_amount
msgid "Minimum amount for which a double validation is required"
msgstr ""

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_monthly_purchases
msgid "Monthly Purchases"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"Most propositions of purchase orders are created automatically\n"
"                by Odoo based on inventory needs."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "New Mail"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_product_variant:0
msgid "No variants on products"
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:893
#, python-format
msgid ""
"No vendor associated to product %s. Please set one to fix this procurement."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Not Invoiced"
msgstr ""

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Not purchased"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Notes"
msgstr "Notas"

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On ordered quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_purchase_method
#: model:ir.model.fields,help:purchase.field_product_template_purchase_method
msgid ""
"On ordered quantities: Invoice this product based on ordered quantities.\n"
"On received quantities: Invoice this product based on received quantity."
msgstr ""

#. module: purchase
#: selection:product.template,purchase_method:0
msgid "On received quantities"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_order
#: model:ir.model.fields,field_description:purchase.field_purchase_report_date_order
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Order Date"
msgstr "Fecha entrega"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_order_line
msgid "Order Lines"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order Month"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_order_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_name
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Order Reference"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_state
msgid "Order Status"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Order of Day"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Orders"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_mail_mail
msgid "Outgoing Mails"
msgstr ""

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase_done
msgid "PO_${(object.name or '').replace('/','_')}"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_res_partner
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_partner_id
msgid "Partner"
msgstr "Empresa"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_country_id
msgid "Partner Country"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Partner's Country"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_payment_term_id
msgid "Payment Term"
msgstr "Forma de pago"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_product_category_property_account_creditor_price_difference_categ
#: model:ir.model.fields,field_description:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,field_description:purchase.field_product_template_property_account_creditor_price_difference
msgid "Price Difference Account"
msgstr "Diferencia en el Valor de la Cuenta"

#. module: purchase
#: model:ir.filters,name:purchase.filter_purchase_order_price_per_supplier
msgid "Price Per Vendor"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_config_pricelist
msgid "Pricelists"
msgstr "Lista de precios"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Print RFQ"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_order
msgid "Procurement"
msgstr "Abastecimiento"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_group_id
msgid "Procurement Group"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_procurement_rule
msgid "Procurement Rule"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_product_product
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_order_product_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
msgid "Product"
msgstr "Producto"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_category_config_purchase
msgid "Product Categories"
msgstr "Categorías de producto"

#. module: purchase
#: model:ir.model,name:purchase.model_product_category
#: model:ir.model.fields,field_description:purchase.field_purchase_report_category_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_unit_quantity
msgid "Product Quantity"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_product_template
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_tmpl_id
msgid "Product Template"
msgstr "Plantilla de producto"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_uom
msgid "Product Unit of Measure"
msgstr "Unidades de Medida de Productos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_product_variant
msgid "Product Variants"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.product_normal_action_puchased
#: model:ir.ui.menu,name:purchase.menu_procurement_partner_contact_form
#: model:ir.ui.menu,name:purchase.menu_product_in_config_purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Products"
msgstr "Productos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_standard
msgid "Products Value"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_product_variant:0
msgid ""
"Products can have several attributes, defining variants (Example: size, "
"color,...)"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_uom:0
msgid "Products have only one unit of measure (easier)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_company_po_double_validation
msgid "Provide a double validation mechanism for purchases"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_procurement_management
msgid "Purchase"
msgstr "Compras"

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_order_report_all
msgid "Purchase Analysis"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_purchase_order_report_all
msgid ""
"Purchase Analysis allows you to easily check and analyse your company "
"purchase history and performance. From this menu you can track your "
"negotiation performance, the delivery performance of your vendors, etc."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_company_po_lead
msgid "Purchase Lead Time"
msgstr ""

#. module: purchase
#: model:ir.actions.report.xml,name:purchase.action_report_purchase_order
#: model:ir.model,name:purchase.model_purchase_order
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_id
#: model:ir.model.fields,field_description:purchase.field_procurement_order_purchase_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_pivot
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_configuration
#: selection:purchase.order,state:0 selection:purchase.report,state:0
#: model:res.request.link,name:purchase.req_link_purchase_order
msgid "Purchase Order"
msgstr "Pedido de compra"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Purchase Order Confirmation #"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Order Fiscal Position"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_order_line
#: model:ir.model.fields,field_description:purchase.field_account_invoice_line_purchase_line_id
#: model:ir.model.fields,field_description:purchase.field_procurement_order_purchase_line_id
#: model:ir.model.fields,field_description:purchase.field_stock_move_purchase_line_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Purchase Order Line"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
msgid "Purchase Order Lines"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_form_action
#: model:ir.model.fields,field_description:purchase.field_stock_picking_purchase_id
#: model:ir.ui.menu,name:purchase.menu_purchase_form_action
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Purchase Orders"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_graph
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_pivot
msgid "Purchase Orders Statistics"
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:614
#, python-format
msgid "Purchase order line deleted."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Purchase orders that include lines not invoiced."
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,module_purchase_requisition:0
msgid ""
"Purchase propositions trigger draft purchase orders to a single supplier"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_stock_warehouse_buy_to_resupply
msgid "Purchase to resupply this warehouse"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_negociation
msgid "Purchase-Standard Price"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.action_purchase_line_product_tree
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_buttons
#: model_terms:ir.ui.view,arch_db:purchase.view_product_template_purchase_buttons_from
msgid "Purchases"
msgstr "Compras"

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_report
msgid "Purchases Orders"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_dest_address_id
msgid ""
"Put an address if you want to deliver directly from the vendor to the "
"customer. Otherwise, keep empty to deliver to your own company."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_product_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Quotations"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_approved
msgid "RFQ Approved"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_confirmed
msgid "RFQ Confirmed"
msgstr ""

#. module: purchase
#: model:mail.message.subtype,name:purchase.mt_rfq_done
msgid "RFQ Done"
msgstr ""

#. module: purchase
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "RFQ Sent"
msgstr ""

#. module: purchase
#: model:mail.template,report_name:purchase.email_template_edi_purchase
msgid "RFQ_${(object.name or '').replace('/','_')}"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_purchase_order
msgid "RFQs and Purchases"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Print RFQ"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Re-Send RFQ by Email"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Receive Products"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_qty_received
msgid "Received Qty"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_open_picking
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_count
#: model:ir.model.fields,field_description:purchase.field_purchase_order_picking_ids
msgid "Receptions"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Reference"
msgstr "Referencia"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_product_uom
msgid "Reference Unit of Measure"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_origin
msgid ""
"Reference of the document that generated this purchase order request (e.g. a "
"sale order or an internal procurement request)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_partner_ref
msgid ""
"Reference of the sales order or bid sent by the vendor. It's used to do the "
"matching when you receive the products as this reference is usually written "
"on the delivery order sent by your vendor."
msgstr ""

#. module: purchase
#: model:ir.actions.report.xml,name:purchase.report_purchase_quotation
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "Request for Quotation"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Request for Quotation #"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.purchase_rfq
#: model:ir.ui.menu,name:purchase.menu_purchase_rfq
msgid "Requests for Quotation"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_move_ids
msgid "Reservation"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_create_uid
#: model:ir.model.fields,field_description:purchase.field_purchase_report_user_id
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Responsible"
msgstr "Responsable"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_date_planned
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_date_planned
msgid "Scheduled Date"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
msgid "Search Purchase Order"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send PO by Email"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Send RFQ by Email"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_costing_method:0
msgid "Set a fixed cost price on each product"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid ""
"Set a purchase order as done if you don't want to receive vendor bills "
"anymore for this purchase order."
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Set to Draft"
msgstr "Cambiar a borrador"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_general_settings
msgid "Settings"
msgstr "Configuración"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_form
msgid "Shipment"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_advance_purchase_requisition:0
msgid "Simple call for tender (only choose from one RFQ)"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,group_uom:0
msgid ""
"Some products may be sold/puchased in different units of measure (advanced)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_origin
msgid "Source Document"
msgstr "Documento origen"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_state
#: model:ir.model.fields,field_description:purchase.field_purchase_order_state
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Status"
msgstr "Estado"

#. module: purchase
#: model:ir.model,name:purchase.model_stock_move
msgid "Stock Move"
msgstr "Movimiento stock"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_form2
msgid "Stock Moves"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_subtotal
msgid "Subtotal"
msgstr "Sub-Total"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_res_partner_property_purchase_currency_id
msgid "Supplier Currency"
msgstr ""

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_product_pricelist_action2_purchase
msgid "Supplier Pricelist"
msgstr ""

#. module: purchase
#: selection:purchase.config.settings,module_stock_dropshipping:0
msgid "Suppliers always deliver to your warehouse(s)"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_tax
msgid "Tax"
msgstr "Impuestos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_tax
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_taxes_id
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
msgid "Taxes"
msgstr "Impuestos"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_default_location_dest_id_usage
msgid "Technical field used to display the Drop Ship Address"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_notes
msgid "Terms and Conditions"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"The product form contains detailed information to improve the\n"
"            purchase process: prices, procurement logistics, accounting "
"data,\n"
"            available vendors, etc."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_rfq
msgid ""
"The quotation contains the history of the discussion/negotiation\n"
"                you had with your vendor. Once confirmed, a request for\n"
"                quotation is converted into a purchase order."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid ""
"The request for quotation is the first step of the purchases flow. Once\n"
"                    converted into a purchase order, you will be able to "
"control the receipt\n"
"                    of the products and the vendor bill."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_category_property_account_creditor_price_difference_categ
msgid ""
"This account will be used to value price difference between purchase price "
"and accounting cost."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_product_product_property_account_creditor_price_difference
#: model:ir.model.fields,help:purchase.field_product_template_property_account_creditor_price_difference
msgid ""
"This account will be used to value price difference between purchase price "
"and cost price."
msgstr ""
"Esta cuenta se utilizará para valorar la diferencia de precios entre el "
"precio de compra y precio de coste"

#. module: purchase
#: model:ir.model.fields,help:purchase.field_res_partner_property_purchase_currency_id
msgid ""
"This currency will be used, instead of the default one, for purchases from "
"the current partner"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_purchase_order
msgid "This vendor has no purchase order. Click to create a new RfQ."
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_order_picking_type_id
msgid "This will determine picking type of incoming shipment"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: selection:purchase.order,state:0 selection:purchase.report,state:0
msgid "To Approve"
msgstr "Para aprobar"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_total
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_total
msgid "Total"
msgstr "Total"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_price_total
msgid "Total Price"
msgstr "Precio total"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total Untaxed amount"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Total amount"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:300
#, python-format
msgid ""
"Unable to cancel purchase order %s as some receptions have already been done."
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:303
#, python-format
msgid ""
"Unable to cancel this purchase order. You must first cancel related vendor "
"bills."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_line_price_unit
msgid "Unit Price"
msgstr "Precio Unitario"

#. module: purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_uom
#: model:ir.ui.menu,name:purchase.menu_purchase_unit_measure_purchase
#: model:ir.ui.menu,name:purchase.menu_purchase_uom_form_action
msgid "Units of Measure"
msgstr "Unidades de medida"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_tree
msgid "Untaxed"
msgstr "Sin impuestos"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Base imponible"

#. module: purchase
#: selection:purchase.config.settings,group_costing_method:0
msgid "Use a 'Fixed', 'Real' or 'Average' price costing method"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.action_invoice_pending
msgid ""
"Use this menu to control the invoices to be received from your\n"
"            vendor. When registering a new bill, set the purchase order\n"
"            and Odoo will fill the bill automatically according to ordered\n"
"            or received quantities."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_form_action
msgid ""
"Use this menu to search within your purchase orders by\n"
"                references, vendor, products, etc. For each purchase order,\n"
"                you can track the related discussion with the vendor, "
"control\n"
"                the products received and control the vendor bills."
msgstr ""

#. module: purchase
#: model:res.groups,name:purchase.group_purchase_user
msgid "User"
msgstr "Usuario"

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.report_purchaseorder_document
#: model_terms:ir.ui.view,arch_db:purchase.report_purchasequotation_document
msgid "VAT:"
msgstr "IVA:"

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_id
#: model:ir.model.fields,field_description:purchase.field_purchase_report_partner_id
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_search
#: model_terms:ir.ui.view,arch_db:purchase.purchase_order_line_tree
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_filter
#: model_terms:ir.ui.view,arch_db:purchase.view_purchase_order_search
msgid "Vendor"
msgstr ""

#. module: purchase
#: model:ir.actions.act_window,name:purchase.act_res_partner_2_supplier_invoices
#: model:ir.actions.act_window,name:purchase.action_invoice_pending
#: model:ir.actions.act_window,name:purchase.purchase_open_invoice
#: model:ir.ui.menu,name:purchase.menu_procurement_management_pending_invoice
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
msgid "Vendor Bills"
msgstr ""

#. module: purchase
#: model_terms:ir.ui.view,arch_db:purchase.res_partner_view_purchase_account_buttons
msgid "Vendor Bills."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_config_settings_group_manage_vendor_price
msgid "Vendor Price"
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_order_partner_ref
msgid "Vendor Reference"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.act_res_partner_2_supplier_invoices
msgid ""
"Vendors bills can be pre-generated based on purchase\n"
"                    orders or receipts. This allows you to control bills\n"
"                    you receive from your vendor according to the draft\n"
"                    document in Odoo."
msgstr ""

#. module: purchase
#: model:ir.model.fields,field_description:purchase.field_purchase_report_volume
msgid "Volume"
msgstr ""

#. module: purchase
#: selection:purchase.order,invoice_status:0
msgid "Waiting Invoices"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_stock_warehouse
#: model:ir.model.fields,field_description:purchase.field_purchase_report_picking_type_id
msgid "Warehouse"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_stock_warehouse_buy_to_resupply
msgid "When products are bought, they can be delivered to this warehouse"
msgstr ""

#. module: purchase
#: model:ir.model.fields,help:purchase.field_purchase_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.purchase_open_invoice
msgid ""
"You can control the invoice from your vendor according to\n"
"            what you purchased (services) or received (products)."
msgstr ""

#. module: purchase
#: model_terms:ir.actions.act_window,help:purchase.product_normal_action_puchased
msgid ""
"You must define a product for everything you purchase, whether\n"
"            it's a physical product, a consumable or services you buy to\n"
"            subcontractors."
msgstr ""

#. module: purchase
#: code:addons/purchase/purchase.py:335
#, python-format
msgid "You must set a Vendor Location for this partner %s"
msgstr ""

#. module: purchase
#: model:ir.model,name:purchase.model_purchase_config_settings
msgid "purchase.config.settings"
msgstr ""

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si marcado la nueva mensaje requiere atencion"

#~ msgid "Last Message Date"
#~ msgstr "Fecha de la ultima mensaje"

#~ msgid "Messages"
#~ msgstr "Mensajes"

#~ msgid "Messages and communication history"
#~ msgstr "Mensajes y historial de comunicación"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin leer"
