# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_cache
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-09-29 10:26+0000\n"
"PO-Revision-Date: 2016-02-18 13:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_cache
msgid "Cache"
msgstr "Caché"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_compute_user_id
msgid "Cache compute user"
msgstr "Usuario del caché"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config_cache_ids
msgid "Cache ids"
msgstr "IDS del Caché"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_config_id
msgid "Config id"
msgstr "Configuración Id "

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_create_date
msgid "Created on"
msgstr "Creado el"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_id
msgid "ID"
msgstr "ID"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_write_date
msgid "Last Updated on"
msgstr "Actualizado el"

#. module: pos_cache
#. openerp-web
#: code:addons/pos_cache/static/src/js/pos_cache.js:36
#, python-format
msgid "Loading"
msgstr "Cargando"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_config_oldest_cache_time
msgid "Oldest cache time"
msgstr "Antiguo tiempo de caché"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_product_domain
msgid "Product domain"
msgstr "Dominio del producto"

#. module: pos_cache
#: model:ir.model.fields,field_description:pos_cache.field_pos_cache_product_fields
msgid "Product fields"
msgstr "Campos del producto"

#. module: pos_cache
#: model_terms:ir.ui.view,arch_db:pos_cache.view_pos_config_form
msgid "Recompute cache"
msgstr "Recalcular caché"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_cache
msgid "pos.cache"
msgstr "pos.cache"

#. module: pos_cache
#: model:ir.model,name:pos_cache.model_pos_config
msgid "pos.config"
msgstr "pos.config"
