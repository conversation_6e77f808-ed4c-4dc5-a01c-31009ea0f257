<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_hr" model="res.partner">
        <field name="name">HR Company</field>
        <field name="vat">HR18724543544</field>
        <field name="street">Vukasi</field>
        <field name="city">Ježdovec</field>
        <field name="country_id" ref="base.hr"/>
        
        <field name="zip">10250</field>
        <field name="phone">+385 92 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.hrexample.com</field>
    </record>

    <record id="demo_company_hr" model="res.company">
        <field name="name">HR Company</field>
        <field name="partner_id" ref="partner_demo_company_hr"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_hr')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_hr.demo_company_hr'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_hr.l10n_hr_chart_template_rrif')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_hr.demo_company_hr')"/>
    </function>
</odoo>
