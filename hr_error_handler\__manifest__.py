# -*- coding: utf-8 -*-
{
    'name': 'HR Error Handler',
    'version': '********.0',
    'category': 'Human Resources',
    'summary': 'معالج أخطاء الموارد البشرية - يحل مشاكل الحقول المفقودة في hr.employee.public',
    'description': """
HR Error Handler
================

هذا المديول يعالج الأخطاء المتعلقة بالحقول المفقودة في نموذج hr.employee.public.

المشاكل التي يحلها:
- خطأ "Invalid field 'passport_issue_location' on model 'hr.employee.public'"
- خطأ "Invalid field 'connected_with_comp' on model 'hr.employee.public'"
- خطأ "Invalid field 'overtime_hour_rate' on model 'hr.employee.public'"
- خطأ "RecursionError: maximum recursion depth exceeded" في عمليات البحث
- أي حقول أخرى مفقودة من hr.employee.public والموجودة في hr.employee

الميزات:
- يضيف الحقول المفقودة إلى hr.employee.public
- يمنع التكرار اللا نهائي في عمليات البحث
- يحافظ على التوافق مع المديولات الأخرى
- لا يعدل على المديولات الموجودة
- يوفر آلية آمنة للتعامل مع الأخطاء
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': [
        'hr',
        'hr_employees_masarat',  # المديول الذي يحتوي على الحقول الإضافية
        'libya_hr_payroll',  # المديول الذي يحتوي على overtime_hour_rate
    ],
    'data': [
        'security/ir.model.access.csv',
        # 'views/hr_employee_public_views.xml',
    ],
    'demo': [
        'demo/demo_data.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
