# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_quiz
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_questions_count
msgid "# Quiz Questions"
msgstr "Número de preguntas de la prueba"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid ". Try another search."
msgstr "Intente otra búsqueda"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
msgid "<span class=\"text-muted small font-weight-bold\">Points</span>"
msgstr "<span class=\"text-muted small font-weight-bold\">Puntos</span>"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "<span class=\"text-muted\">Points</span>"
msgstr "<span class=\"text-muted\">Puntos</span>"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Add Quiz"
msgstr "Añadir prueba"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "All questions must be answered !"
msgstr "Todas las preguntas deben ser respondidas !"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "Allow multiple tries"
msgstr "Permitir varios intentos"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__text_value
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__answer_ids
msgid "Answer"
msgstr "Respuesta"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check answers"
msgstr "Revisar respuestas"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Check your answers"
msgstr "Revisar sus respuestas"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_completed
msgid "Completed"
msgstr "Completada"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_0
msgid "Concrete Blocks Wall"
msgstr "Pared de bloques de concreto"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Congratulations, you scored a total of"
msgstr "Felicidades, ha conseguido un total de"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_question
msgid "Content Quiz Question"
msgstr "Cuestionario de Contenido"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__is_correct
msgid "Correct"
msgstr "Correcto"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__correct_answer_id
msgid "Correct Answer"
msgstr "Respuesta Correcta"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Correct."
msgstr "Correcto."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__create_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__display_name
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Even if there will be some."
msgstr "Aunque habrá algunos."

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_1
msgid "Even if you have a big trunk, some long products need to be secured."
msgstr ""
"Incluso si tiene una gran cajuela, es necesario asegurar algunos productos "
"de gran tamaño."

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_event
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Event"
msgstr "Evento"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_question_action
msgid "Event Quiz Questions"
msgstr "Preguntas de la prueba del evento"

#. module: website_event_track_quiz
#: model:ir.actions.act_window,name:website_event_track_quiz.event_quiz_action
msgid "Event Quizzes"
msgstr "Pruebas del evento"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__event_track_id
msgid "Event Track"
msgstr "Sesión del evento"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__comment
msgid "Extra Comment"
msgstr "Comentario adicional"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid ""
"From here you will be able to examine all quiz questions you have linked to "
"Tracks."
msgstr ""
"Desde aquí podrá revisar todas las preguntas de la prueba que ha vinculado a"
" las grabaciones."

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid ""
"From here you will be able to overview all quizzes you have linked to "
"Tracks."
msgstr ""
"Desde aquí podrá revisar todas las pruebas que ha vinculado a las "
"grabaciones."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_view_form
msgid "Go to Quiz"
msgstr "Ir a la prueba"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer"
msgstr "Martillo"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_0
msgid "Hammer won't be of any help here!"
msgstr "El martillo no nos ayudará en esto."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__id
msgid "ID"
msgstr "ID"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_0_0
msgid ""
"In order to avoid accident, you need to secure any product of this kind "
"during transportation!"
msgstr ""
"Para evitar accidentes, es necesario asegurar cualquier producto de este "
"tipo durante el transporte."

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Incorrect."
msgstr "Incorrecto."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__is_quiz_completed
msgid "Is Quiz Done"
msgstr "Ya acabó la prueba"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer____last_update
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_uid
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__write_date
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz__repeatable
msgid "Let attendees reset the quiz and try again."
msgstr ""
"Permita que los asistentes reinicien la prueba y lo intenten de nuevo."

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Lumbers need first to be cut from trees!"
msgstr "Primero hay que cortar la madera de los árboles."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Mobile sub-nav"
msgstr "Sub-navegación móvil"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_2
msgid "Mud Wall"
msgstr "Muro de barro"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_1
msgid "Music"
msgstr "Música"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__name
msgid "Name"
msgstr "Nombre"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_1
msgid "No"
msgstr "No"

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_question_action
msgid "No Quiz Question yet!"
msgstr "Todavía no hay preguntas en la prueba."

#. module: website_event_track_quiz
#: model_terms:ir.actions.act_window,help:website_event_track_quiz.event_quiz_action
msgid "No Quiz added yet!"
msgstr "Todavía no se ha añadido ninguna prueba."

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "No user found for"
msgstr "No se encuenta ningún usuario con"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__awarded_points
msgid "Number of Points"
msgstr "Número de puntos"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Oopsie, you did not score any point on this quiz."
msgstr "¡Ups! no ha obtenido ningún punto en esta prueba."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_2
msgid "Open Source Apps"
msgstr "Aplicaciones de código abierto"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_2
msgid "OpenWood is not an Open Source congres about Apps."
msgstr "OpenWood no es un congreso de código abierto sobre aplicaciones."

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Point"
msgstr "Punto"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__awarded_points
#, python-format
msgid "Points"
msgstr "Puntos"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_13_quiz
msgid "Pretty. Ugly. Lovely."
msgstr "Bonito. Feo. Adorable."

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__question_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__name
msgid "Question"
msgstr "Pregunta"

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid ""
"Question \"%s\" must have 1 correct answer and at least 1 incorrect answer "
"to be valid."
msgstr ""
"La pregunta \"%s\" debe tener 1 respuesta correcta y por lo menos 1 "
"respuesta incorrecta para ser válida."

#. module: website_event_track_quiz
#: code:addons/website_event_track_quiz/models/event_quiz.py:0
#, python-format
msgid "Question \"%s\" must have 1 correct answer to be valid."
msgstr "La pregunta \"%s\" debe tener 1 respuesta correcta para ser válida."

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz_answer
msgid "Question's Answer"
msgstr "Respuestas"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__question_ids
msgid "Questions"
msgstr "Cuestionario"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__quiz_id
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_id
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Quiz"
msgstr "Quiz"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_points
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track_visitor__quiz_points
msgid "Quiz Points"
msgstr "Puntos de la prueba"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "Quiz Question"
msgstr "Pregunta de la prueba"

#. module: website_event_track_quiz
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_question_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_tree
msgid "Quiz Questions"
msgstr "Preguntas de la prueba"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "Quiz validation error"
msgstr "Error de validación de la prueba"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_track__quiz_ids
#: model:ir.ui.menu,name:website_event_track_quiz.event_quiz_menu
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_tree
msgid "Quizzes"
msgstr "Quizzes"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "Reset"
msgstr "Restablecer"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Scotch tape"
msgstr "Cinta adhesiva"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search"
msgstr "Búsqueda"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search Attendees"
msgstr "Buscar asistentes"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search courses"
msgstr "Buscar cursos"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.leaderboard_search_bar
msgid "Search users"
msgstr "Buscar usuarios"

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_5_quiz
msgid "Securing your Lumber during transport"
msgstr "Cómo asegurar su madera durante el transporte"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_answer__sequence
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz_question__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_13_question_0_1
msgid "Steel Wall"
msgstr "Pared de acero"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_1
msgid "Stores !"
msgstr "Tiendas"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_track_content
msgid "Take the Quiz"
msgstr "Haga la prueba"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "The correct answer was:"
msgstr "La respuesta correcta era:"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_leaderboard
msgid "There is currently no leaderboard available"
msgstr "Actualmente no hay ninguna tabla de clasificación disponible"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "Hubo un error validando este quiz"

#. module: website_event_track_quiz
#: model:ir.model.fields,help:website_event_track_quiz.field_event_quiz_answer__comment
msgid ""
"This comment will be displayed to the user if he selects this answer, after submitting the quiz.\n"
"                It is used as a small informational text helping to understand why this answer is correct / incorrect."
msgstr ""
"Si el usuario seleccionó esta respuesta, se le mostrará este comentario una vez que haya enviado la prueba.\n"
"Se utiliza como un pequeño texto informativo que ayuda a entender por qué esta respuesta es correcta/incorrecta."

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/js/event_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Este quiz ya se ha completado. No es posible volver a tomarlo."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_1_1
msgid "Tie-down straps and other wooden blocks"
msgstr "Correas de sujeción y otros bloques de madera"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_search
msgid "Track"
msgstr "Sesión"

#. module: website_event_track_quiz
#: model:ir.model,name:website_event_track_quiz.model_event_track_visitor
msgid "Track / Visitor Link"
msgstr "Grabación/visitante del enlace"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_0
msgid "Transporting lumber from stores to your house is safe."
msgstr "El transporte de madera desde los almacenes hasta su casa es seguro."

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_1_0
msgid "Trees !"
msgstr "Árboles"

#. module: website_event_track_quiz
#: model:ir.model.fields,field_description:website_event_track_quiz.field_event_quiz__repeatable
msgid "Unlimited Tries"
msgstr "Intentos ilimitados"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "User rank"
msgstr "Rango del usuario"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_5_question_1_2
msgid "Well, it could work but you will need a lot of tape!"
msgstr "Bueno, podría funcionar, pero necesitaría mucha cinta adhesiva."

#. module: website_event_track_quiz
#: model:event.quiz,name:website_event_track_quiz.event_7_track_1_quiz
msgid "What This Event Is All About"
msgstr "De qué trata este evento"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_5_question_1
msgid "What kind of tool are needed to secure your lumber ?"
msgstr "¿Qué tipo de herramienta se necesita para asegurar la madera?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_13_question_0
msgid "What kind of wall is transformed here ?"
msgstr "¿Qué tipo de muro se transforma aquí?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_0
msgid "What will we talk about during this event ?"
msgstr "¿De qué hablaremos durante este evento?"

#. module: website_event_track_quiz
#: model:event.quiz.question,name:website_event_track_quiz.event_7_track_1_question_1
msgid "Where does lumber comes from ?"
msgstr "¿De dónde viene la madera?"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_1_question_0_0
msgid "Wood"
msgstr "Madera"

#. module: website_event_track_quiz
#: model:event.quiz.answer,text_value:website_event_track_quiz.event_7_track_5_question_0_0
msgid "Yes"
msgstr "Sí"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.all_visitor_card
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.top3_visitor_card
msgid "You"
msgstr "Usted"

#. module: website_event_track_quiz
#: model:event.quiz.answer,comment:website_event_track_quiz.event_7_track_1_question_0_0
msgid "You're really smart !"
msgstr "¡Es muy inteligente!"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_view_form
msgid "e.g. Test your Knowledge"
msgstr "Por ejemplo, ponga a prueba su conocimiento"

#. module: website_event_track_quiz
#: model_terms:ir.ui.view,arch_db:website_event_track_quiz.event_quiz_question_view_form
msgid "e.g. What is Joe's favorite motto?"
msgstr "por ejemplo, ¿Cuál es el lema favorito de Joe?"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "point!"
msgstr "punto!"

#. module: website_event_track_quiz
#. openerp-web
#: code:addons/website_event_track_quiz/static/src/xml/quiz_templates.xml:0
#, python-format
msgid "points!"
msgstr "puntos!"
