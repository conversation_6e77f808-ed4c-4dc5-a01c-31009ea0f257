# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_crm
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Eiman<PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>l<PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Linas V<PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Arunas V. <<EMAIL>>, 2022\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Orders</span>"
msgstr "<span class=\"o_stat_text\"> Užsakymai</span>"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__lead_id
msgid "Associated Lead"
msgstr "Susijusios iniciatyvos"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_quotation_partner_view_form
msgid "Cancel"
msgstr "Atšaukti"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_quotation_partner_view_form
msgid "Confirm"
msgstr "Patvirtinti"

#. module: sale_crm
#: model:ir.model.fields.selection,name:sale_crm.selection__crm_quotation_partner__action__create
msgid "Create a new customer"
msgstr "Sukurkite naują klientą"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_crm_quotation_partner
msgid "Create new or use existing Customer on new Quotation"
msgstr ""
"Sukurkite naują arba naudokite esamą klientą naujame komerciniame pasiūlyme"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "Create new quotation"
msgstr ""

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__partner_id
msgid "Customer"
msgstr "Klientas"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: sale_crm
#: model:ir.model.fields.selection,name:sale_crm.selection__crm_quotation_partner__action__nothing
msgid "Do not link to a customer"
msgstr "Nesusieti su pirkėju"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__id
msgid "ID"
msgstr "ID"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_res_users__target_sales_invoiced
msgid "Invoiced in Sales Orders Target"
msgstr "Užsakymų su pateiktomis sąskaitomis tikslas"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner____last_update
msgid "Last Modified on"
msgstr "Paskutinį kartą keista"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Iniciatyva/Galimybė"

#. module: sale_crm
#: model:ir.model.fields.selection,name:sale_crm.selection__crm_quotation_partner__action__exist
msgid "Link to an existing customer"
msgstr "Susieti su esamu pirkėju"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.sale_view_inherit123
msgid "Log in the chatter from which opportunity the order originates"
msgstr "Prisijunkite prie pokalbio, iš kurio galimybės kilo užsakymas"

#. module: sale_crm
#: model:ir.ui.menu,name:sale_crm.sale_order_menu_quotations_crm
msgid "My Quotations"
msgstr "Mano komerciniai pasiūlymai"

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.crm_quotation_partner_action
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_quotation_partner_view_form
msgid "New Quotation"
msgstr "Naujas komercinis pasiūlymas "

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead__quotation_count
msgid "Number of Quotations"
msgstr "Pasiūlymų Skaičius"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead__sale_order_count
msgid "Number of Sale Orders"
msgstr "Pardavimo užsakymų skaičius"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_sale_order__opportunity_id
msgid "Opportunity"
msgstr "Pardavimo galimybė"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead__order_ids
msgid "Orders"
msgstr "Užsakymai"

#. module: sale_crm
#: model:ir.actions.act_window,name:sale_crm.sale_action_quotations_new
msgid "Quotation"
msgstr "Komercinis pasiūlymas"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_quotation_partner__action
msgid "Quotation Customer"
msgstr "Komercinio pasiūlymo klientas"

#. module: sale_crm
#: model_terms:ir.ui.view,arch_db:sale_crm.crm_case_form_view_oppor
msgid "Quotations"
msgstr "Komerciniai pasiūlymai"

#. module: sale_crm
#: code:addons/sale_crm/models/crm_team.py:0
#, python-format
msgid "Sales Analysis"
msgstr "Pardavimų analizė"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_sale_order
msgid "Sales Order"
msgstr "Pardavimo užsakymas"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_crm_team
msgid "Sales Team"
msgstr "Pardavimų komanda"

#. module: sale_crm
#: code:addons/sale_crm/models/crm_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Pardavimai: viso neapmokestinta"

#. module: sale_crm
#: model:ir.model.fields,field_description:sale_crm.field_crm_lead__sale_amount_total
msgid "Sum of Orders"
msgstr "Užsakymų suma"

#. module: sale_crm
#: model:ir.model.fields,help:sale_crm.field_crm_lead__sale_amount_total
msgid "Untaxed Total of Confirmed Orders"
msgstr "Patvirtintų užsakymų suma be mokesčių"

#. module: sale_crm
#: model:ir.model,name:sale_crm.model_res_users
msgid "Users"
msgstr "Vartotojai"

#. module: sale_crm
#: code:addons/sale_crm/wizard/crm_opportunity_to_quotation.py:0
#, python-format
msgid "You can only apply this action from a lead."
msgstr "Šį veiksmą galite taikyti tik iš iniciatyvos."
