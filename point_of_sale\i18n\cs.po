# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# J<PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:42+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Aleš Fiala <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "VRÁTIT"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (nepoužito)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s POS platba %s v %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order ?"
msgstr "%s má celkovou částku %s, opravdu chcete smazat tuto objednávku ?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "& invoice"
msgstr "& faktura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "(Both will be sent by email)"
msgstr "(Obě budou zaslány e-mailem)"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(ZÁCHRANA PRO %(session)s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(od otevření)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "(update)"
msgstr "(aktualizovat)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid ". Please contact your manager to accept the closing difference."
msgstr ". Obraťte se na svého manažera, aby přijal konečný rozdíl."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" "
"title=\"Manage\"/>Prodej"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<i class=\"fa fa-fw fa-arrow-right\"/>How to manage tax-included prices"
msgstr "<i class=\"fa fa-fw fa-arrow-right\"/>Jak spravovat ceny včetně daně"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Nákupní košík\"/>"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "<p>Dear %s,<br/>Here is your electronic ticket for the %s. </p>"
msgstr "<p>Vážený %s, <br/>tady je váš elektronický lístek na %s. </p>"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr ""
"<p>Jste připraveni se podívat na <b>POS rozhraní</b>? Pojďme začít naše "
"první sezení.</p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', "
"True)]}\">TBD</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_total_cost_computed','=', True)]}\">bude "
"rozhodnuto</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Authorized Employees</span>"
msgstr "<span class=\"o_form_label\">Oprávnění zaměstnanci</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Barcodes</span>"
msgstr "<span class=\"o_form_label\">Čárové kódy</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Default Pricelist</span>"
msgstr "<span class=\"o_form_label\">Výchozí ceník</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Journal Entries</span>"
msgstr "<span class=\"o_form_label\">Vnitřní účetní doklady</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Order Reference</span>"
msgstr "<span class=\"o_form_label\">Reference objednávky</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"o_form_label\">Payment Methods</span>"
msgstr "<span class=\"o_form_label\">Platební podmínky</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>Přeskočit obrazovku náhledu</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Cash Balance</span>"
msgstr "<span>Poslední konečný peněžní zůstatek</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Last Closing Date</span>"
msgstr "<span>Datum poslední uzávěrky</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Přehledy</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>Zobrazit</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; Platební terminály</strong>\n"
"                                    za účelem instalace platebního terminálu a provedení plně integrované platební metody."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? Kliknutím na tlačítko Potvrdit platbu potvrdíte."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "Je vyžadováno jméno zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr ""
"Globální jedinečný identifikátor pro tuto konfiguraci POS, používaný k "
"zabránění konfliktům v datech generovaných klientem."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr ""
"Pořadové číslo, které se zvyšuje pokaždé, když uživatel obnoví relaci POS"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "Pořadové číslo, které je navýšeno o každou objednávku"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr ""
"Session je časové období, obvykle jeden den, během kterého prodáváte přes "
"prodejní místo."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr ""
"Aktuálně je pro toto Místo Prodeje otevřena relace. Některá nastavení lze "
"změnit až po ukončení relace."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "Sekvenční číslo jedinečné pro relaci objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "Krátký text, který bude vložen jako zápatí do vytištěné stvrzenky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "Krátký text, který bude vložen jako hlavička do vytištěné stvrzenky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "ČÁSTKA"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "Přijmout spropitné zákazníků nebo převést jejich drobné na spropitné"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Accept payments difference and post a profit/loss journal entry"
msgstr "Přijmout rozdíl platby a zaúčtovat deník zisků/ztrát"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "Přijímejte platby pomocí platebního terminálu Six"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Přijímejte platby pomocí platebního terminálu Vantiv"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Přijímejte platby prostřednictvím platebního terminálu Adyen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "Účet"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "Účet zaokrouhlení"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "Šablona schématu účtu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "Pohyby na účtu"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr ""
"Platby na účet představující agregované platby a platby rozdělené mezi "
"banky."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "Účetnictví"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "Účetní deník používaný k vytváření faktur."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr ""
"Účetní deník používaný k účtování záznamů deníku relace POS a plateb faktur "
"POS."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "Vyžaduje akci"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "Aktivní"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "Aktivity"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorace výjimky aktivity"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#, python-format
msgid "Add"
msgstr "Přidat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.js:0
#, python-format
msgid "Add Customer Note"
msgstr "Přidat poznámku zákazníka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Add Tip"
msgstr "přidat tip"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add a custom message to header and footer"
msgstr "Přidejte vlastní zprávu do záhlaví a zápatí"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Add a customer"
msgstr "Přidat zákazníka"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "Přidat nový způsob platby"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Add notes on order lines to be printed on receipt and invoice"
msgstr "Přidat poznámky k položkám objednávky pro tisk účtenky a faktury"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Address"
msgstr "Adresa"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "Administrátor"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
msgid "Advanced Cash Control"
msgstr "Pokročilá kontrola hotovosti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Advanced Pricelists"
msgstr "Pokročilé ceníky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Platební terminál Adyen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "All active orders"
msgstr "Všechny aktivní objednávky"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"Veškeré dostupné ceníky musí být ve stejné měně jako firemní, nebo prodejní "
"ceník dostupný v tomto prodejním místě, pokud používáte aplikaci Účtování."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Všechny platební metody musí být ve stejné měně jako prodejní deník nebo "
"měna společnosti, pokud není nastavena."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "Všechny prodejní řádky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow discounts per line"
msgstr "Povolit slevy na řádek"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Allow global discounts on orders"
msgstr "Povolit globální slevy na objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Allow the use of coupon and promotion programs in PoS."
msgstr "Povolit používání kupónových a propagačních programů v Místě Prodeje."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Allow the use of gift card"
msgstr "Umožnit používat dárkový poukaz"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid ""
"Allow to write notes for customer on Orderlines. This will be shown in the "
"receipt."
msgstr ""
"Umožnit psát zákazníkům poznámky k položkám objednávky. Ty budou zobrazeny "
"na účtence."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "Allowed Pricelists"
msgstr "Povolené ceníky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr ""
"Umožňuje zjistit, zda již byly spočítány celkové náklady všech položek "
"objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "Umožňuje zjistit, zda již byly spočítány celkové náklady nebo ne"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
msgid "Amount"
msgstr "Částka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "Částka schváleného rozdílu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "Částka k vyrovnání"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "Celková částka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"Při pokusu o ukončení relace došlo k chybě. Budete přesměrováni na back-end,"
" kde můžete relaci ručně ukončit."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr ""
"Při načítání cen produktu došlo k chybě. Ujistěte se, že všechny ceníky jsou"
" k dispozici v Prodejním místě."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Interní identifikace Prodejního místa."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "Pro toto Prodejní místo je již otevřeno další sezení."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "Archivováno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "Jste si jisti, že zákazník chce platit"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "Co nejdříve"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (recommended)"
msgstr "Při uzavření relace (doporučeno)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"Při uzavření relace: výběr je vytvořen pro celou relaci, když je uzavřena\n"
"V reálném čase: každá objednávka odeslaná na server vytvoří svůj vlastní výběr"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "Vlastnosti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Authorized Difference"
msgstr "Autorizovaný rozdíl"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr ""
"Automaticky generovaná relace pro osiřelé příkazy, ignorovaná v omezeních"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "Automatický tisk stvrzenky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "Automaticky otevřete pokladnu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "Dostupné kategorie produktů místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Available Pricelists"
msgstr "Dostupné ceníky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "K dispozici v POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "Průměrná cena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ControlButtonPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Back"
msgstr "Zpět"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/NumberPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenNumpad.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Backspace"
msgstr "Backspace"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "Banka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "Bankovní platby"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement
msgid "Bank Statement"
msgstr "Výpis z bankovního účtu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Barcode"
msgstr "Čárový kód"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenklatura čárového kódu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "Pravidlo čárového kódu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "Čtečka čárových kódů"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "Čtečka čárových kódů / čtečka platebních karet"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "Základní částka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "Před uzavřením rozdílu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "Přijaté faktury"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Bills &amp; Receipts"
msgstr "Přijaté faktury &amp; Příjmy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Buffer:"
msgstr "Vyrovnávací paměť:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_partners_loading
msgid ""
"By default, 100 partners are loaded.\n"
"When the session is open, we keep on loading all remaining partners in the background.\n"
"In the meantime, you can use the 'Load Customers' button to load partners from database."
msgstr ""
"Ve výchozím nastavení se načte 100 partnerů.\n"
"Když je relace otevřená, načítáme všechny zbývající partnery na pozadí.\n"
"Mezitím můžete použít tlačítko 'Načíst zákazníky' k načtení partnerů z databáze."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""
"Vynechat tisk prohlížeče a vytisknout pomocí hardwarového proxy serveru ."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "CASH"
msgstr "HOTOVOST"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "ZMĚNA"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "CLOSING CONTROL"
msgstr "UZAVŘENÁ KONTROLA"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Can't change customer"
msgstr "Nelze změnit zákazníka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Can't mix order with refund products with new products."
msgstr "Nelze kombinovat objednávku s refundací s novými produkty."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductConfiguratorPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "Zrušit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "Zrušit výzvu k platbě"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "Zrušeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot Invoice"
msgstr "Nelze fakturovat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#, python-format
msgid "Cannot access product information screen if offline."
msgstr "Nelze otevřít obrazovku informací o produktu, pokud jste offline."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Cannot close the session when offline."
msgstr "Nelze zrušit relaci, když jste offline."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Cannot invoice order from closed session."
msgstr "Nelze fakturovat objednávku z uzavřené relace."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "Spropitné nelze upravit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "Drobné nelze vrátit bez způsobu platby v hotovosti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "Jméno držitele karty"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#, python-format
msgid "Cash"
msgstr "Hotovost"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_cash_box_out
msgid "Cash Box Out"
msgstr "Výdaj pokladny"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash In"
msgstr "Hotovost"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/CashMoveButton.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "Příjem / Výdej hotovosti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "Deník hotovosti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Cash Out"
msgstr "Proplatit"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Cash Register"
msgstr "Pokladny"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
msgid "Cash Rounding"
msgstr "Zaokrouhlování hotovosti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "Zaokrouhlování hotovosti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_ids
msgid "Cash Statements"
msgstr "Výpisy z hotovosti"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "Příjem/Výdej hotovosti z %s byl ignorován. "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register for %s"
msgstr "Pokladny pro %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
msgid "Cash rounding"
msgstr "Zaokrouhlování hotovosti"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.account_cashbox_line_view_tree
msgid "Cashbox balance"
msgstr "Stav hotovosti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Cashdrawer"
msgstr "Pokladní zásuvka"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Cashier"
msgstr "Pokladní"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"Kategorie se používají k procházení vašich produktů \n"
"prostřednictvím dotykového displeje."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "Kategorie"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "Název kategorie"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Category Pictures"
msgstr "Obrázky kategorie"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Kategorie používané v místě prodeje."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "Židle"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Change"
msgstr "Změnit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Change Customer"
msgstr "Změnit zákazníka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Change Tip"
msgstr "Změnit spropitné"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "Zaškrtněte, zda má být produkt vážen pomocí zabudované váhy."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""
"Zaškrtněte, zda chcete, aby se tento výrobek zobrazoval v místě prodeje."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr ""
"Zkontrolujte, zda chcete produkty této kategorie seskupit v objednávkách"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr ""
"Při otevírání a zavírání pokladny zkontrolujte množství peněz v pokladně."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Check the internet connection then try again."
msgstr "Zkontrolujte připojení k internetu a zkuste to znovu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr ""
"Zkontrolujte připojení k internetu a poté se pokuste znovu synchronizovat "
"kliknutím na červené tlačítko wifi (vpravo nahoře na obrazovce)."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "Podřízené kategorie"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr ""
"Vyberte konkrétní fiskální pozici v objednávce v závislosti na druhu "
"zákazníka (osvobozená od daně, na místě vs. s sebou atd.)."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Choose among fiscal positions when processing an order"
msgstr "Pri spracovaní objednávky si vyberte z mapování daní"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "City"
msgstr "Město"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Click here to close the session"
msgstr "Kliknutím zde relaci ukončíte"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "Klient"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Connected"
msgstr "Obrazovka klienta je připojena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Disconnected"
msgstr "Obrazovka klienta je odpojena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Client Screen Unsupported. Please upgrade the IoT Box"
msgstr "Obrazovka klienta není podporována. Prosím aktualizujte IoT Box"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ClientScreenButton.xml:0
#, python-format
msgid "Client Screen Warning"
msgstr "Varování na klientské obrazovce"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/HeaderButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "Zavřít relaci"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "Zavřít relaci a odeslat příspěvky"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "Průvodce zavřením relace"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "Uzavřené a schváleno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Closing ..."
msgstr "Uzavírání ..."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "Uzavření"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "Datum uzavření"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "Konečný rozdíl v %s (%s)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Closing session error"
msgstr "Chyba konečného rozdílu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "Hodnota mince / bankovky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Coins/Bills"
msgstr "Mince/bankovky"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "kombinovat %s platby POS z %s"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "Firma"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
msgid "Company has chart of accounts"
msgstr "Společnost má účtové osnovy"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "Konfigurace"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Configuration for journal entries of PoS orders"
msgstr "Konfigurace záznamů deníku objednávek místa prodeje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "Konfigurace a nastavení"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Configure at least one Point of Sale."
msgstr "Nakonfigurujte alespoň jedno místo prodeje."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Potvrdit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#, python-format
msgid "Confirm ?"
msgstr "Potvrdit ?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices to your PoS directly without an IoT Box"
msgstr "Připojte zařízení k vašemu místu prodeje přímo bez IoT boxu"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "Připojit zařízení k Místu prodeje bez IoT Boxu."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connect devices using an IoT Box"
msgstr "Připojit zařízení pomocí IoT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Connected Devices"
msgstr "Připojená zařízení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "Připojeno, nevlastněno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "Připojování k Proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Connecting to the IoT Box"
msgstr "Připojování k IoT Boxu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Connection Error"
msgstr "Chyba připojení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Connection error"
msgstr "Chyba připojení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is aborted"
msgstr "Připojení je přerušeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Connection is lost"
msgstr "Připojení je ztraceno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "Připojení k IoT Boxu selhalo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "Připojení k tiskárně selhalo"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Continue Selling"
msgstr "Pokračovat v prodeji"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Continue selling"
msgstr "Pokračovat v prodeji"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "Konverzní poměr"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "Přepočítací koeficient z měny společnosti na měnu objednávky."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Cost:"
msgstr "Náklady:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Counted"
msgstr "Počítané"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Country"
msgstr "Stát"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_coupon
msgid "Coupon and Promotion Programs"
msgstr "Kupónové a propagační programy"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Coupons & Promotions"
msgstr "Kupóny a propagace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Create"
msgstr "Vytvořit"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "Vytvořte novou objednávku místa prodeje"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
msgid "Create a new PoS"
msgstr "Vytvořit nové místo prodeje"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "Vytvořit novou variantu produktu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "Měna"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Kurz měny"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "Aktuální relace"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "Odpovědní za aktuální relaci"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "Stav Aktuální relace"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "Vlastní"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "Zákazník"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "Zákaznický účet"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Customer Display"
msgstr "Zákaznický displej"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "Zákaznický displej"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "Zákaznická faktura"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/OrderlineCustomerNoteButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "Poznámka zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_orderline_customer_notes
msgid "Customer Notes"
msgstr "Poznámky zákazníka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Customer Required"
msgstr "Vyžadován zákazník"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "Pro %s platební metodu je vyžadován zákazník."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "Spropitné zákazníka, nelze přímo upravovat"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "Zákazníci"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "Ovládací panel"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#, python-format
msgid "Date"
msgstr "Datum"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Days"
msgstr "Dny"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Debug Window"
msgstr "Okno ladění"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__account_id
msgid "Default Account"
msgstr "Výchozí účet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "Výchozí účet pohledávek prodejních míst"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "Výchozí fiskální pozice"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Intermediary Account"
msgstr "Výchozí zprostředkující účet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
msgid "Default Pricelist"
msgstr "Výchozí ceník"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Výchozí daň na výstupu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "Výchozí daň "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "Výchozí daň pro produkty"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Výchozí měrná jednotka používaná pro všechny akciové operace."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "Definovat novou kategorii"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Define the smallest coinage of the currency used to pay"
msgstr "Definujte nejmenší minci v měně použité k platbě"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "Definujte nejmenší ražení mincí používaných k platbě v hotovosti"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr ""
"Definuje jméno platební metody, které se zobrazí v prodejním místě při "
"výběru plateb."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__barcode_nomenclature_id
msgid ""
"Defines what kind of barcodes are available and how they are assigned to "
"products, customers and cashiers."
msgstr ""
"Určuje, jaké typy čárových kódů jsou k dispozici a jak jsou přiřazeny k "
"produktům, zákazníkům a pokladnám."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "Ověření zpoždění"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenPaymentLines.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Delete"
msgstr "Smazat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "Smazat placené objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Paid Orders ?"
msgstr "Smazat placené objednávky?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "Smazat neplacené objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid "Delete Unpaid Orders ?"
msgstr "Smazat neplacené objednávky?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Deselect Customer"
msgstr "Zrušit výběr zákazníka"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_organizer
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "Organizátor stolu"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.desk_pad
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "Deska stolu"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "Stoly"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "Cílový účet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "Cílový účet je pouze ke čtení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_difference
#, python-format
msgid "Difference"
msgstr "Rozdíl"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "Rozdíl při uzavírání relace místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr ""
"Rozdíl mezi teoretickým konečným zůstatkem a reálnou konečnou bilancí."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "Přehled"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Direct Devices"
msgstr "Přímá zařízení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Disc"
msgstr "Sleva"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "Sleva %"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "Zřeknutí se odpovědnosti:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Discard"
msgstr "Zrušit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ClientScreenButton.js:0
#, python-format
msgid "Disconnected"
msgstr "Odpojeno"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_consumable
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
msgid "Discount"
msgstr "Sleva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "Sleva (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "Poznámka slevy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discount:"
msgstr "Sleva:"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "Zvýhodněný produkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Discounts"
msgstr "Slevy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Dismiss"
msgstr "Zamítnout"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_display_categ_images
msgid "Display Category Pictures"
msgstr "Zobrazit obrázky kategorií"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Display pictures of product categories"
msgstr "Zobrazit obrázky kategorií produktů"

#. module: point_of_sale
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "Nemáte přístup, přeskočte tato data pro e-mail uživatele"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "Chcete otevřít seznam zákazníků a vybrat zákazníka?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Do you want to print using the web printer?"
msgstr "Chcete tisknout pomocí webové tiskárny?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Don't show again"
msgstr "Nezobrazovat znovu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "Done by"
msgstr "Hotovo od"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "Stáhnout placené objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "Stáhnout neplacené objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "Stažení zpětné chyby"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientLine.xml:0
#, python-format
msgid "EDIT"
msgstr "UPRAVIT"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Either the server is inaccessible or browser is not connected online."
msgstr "Buď server není dostupný nebo prohlížeč není připojen online."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Electronic Scale"
msgstr "Elektronická váha"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Email"
msgstr "E-mail "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "Email Receipt"
msgstr "Účtenka e-mailem"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Email sent."
msgstr "Odeslán e-mail."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Employee"
msgstr "Zaměstnanec"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"Zaměstnanci se mohou přihlásit k relaci místa prodeje naskenováním svého "
"odznaku nebo zadáním kódu PIN. Tyto přihlašovací údaje lze konfigurovat na "
"kartě *Nastavení HR* ve formuláři zaměstnance."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Empty Order"
msgstr "Prázdná objednávka"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"Povolte skenování čárových kódů pomocí dálkově připojeného skeneru čárových "
"kódů a přetahování karet pomocí čtečky karet Vantiv."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "Umožňuje integraci elektronické váhy."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,help:point_of_sale.field_pos_order__invoice_group
msgid "Enables invoice generation from the Point of Sale."
msgstr "Umožňuje generování faktur z prodejního místa."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "Při načítání obrázku došlo k chybě. Prosím zkuste to znovu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "Datum ukončení"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "Konečný zůstatek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error ! You cannot create recursive categories."
msgstr "Chyba! Nelze vytvořit rekurzivní kategorie."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "Chyba s hláškou"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "Chyba: není internetové připojení."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "Existující řádky objednávek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "Exit Pos"
msgstr "Ukončit Pos"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_expected
#, python-format
msgid "Expected"
msgstr "Očekávaný"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "Export placených objednávek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "Export nezaplacených objednávek"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "Další informace"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "Tkanina"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "Neúspěšné vychystávání"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Financials"
msgstr "Finanční služby"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "Dokončené importování objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position"
msgstr "Daňová pozice"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "Fiskální pozice nenalezena"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Position per Order"
msgstr "Fiskální pozice za objednávku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Fiscal Positions"
msgstr "Mapování daní"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Fiscal data module error"
msgstr "Chyba modulu fiskálních dat"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "Sledující"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sledující (partneři)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skvělá ikona písma, např. fa-úkoly"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Footer"
msgstr "Zápatí"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "Pro nepřesné průmyslové dotykové obrazovky."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "Vynutit ukončení relace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force Done"
msgstr "Vynutit hotovo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Force done"
msgstr "Vynutit hotovo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "Vynucený účet čekajících plateb"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr ""
"Vynutí nastavení zákazníka při použití této platební metody a rozdělí "
"položky deníku pro každého zákazníka. Mohlo by to zpomalit proces zavírání."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "Z fakturačních plateb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "Celý název produktu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Generation of your order references"
msgstr "Generování odkazů na objednávku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_gift_card
msgid "Gift Cards"
msgstr "Dárkové poukazy"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Gift card"
msgstr "Dárkový poukaz"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Give customer rewards, free samples, etc."
msgstr "Dejte zákazníkům odměny, bezplatné vzorky apod."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "Určuje pořadí posloupnosti při zobrazení seznamu kategorií výrobků."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
msgid "Global Discounts"
msgstr "Globální slevy"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "Jít na"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "Větší než je povoleno"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "Skupina produktů v prodejním místě"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "Připojení HTTPS k IoT Boxu se nezdařilo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "Události hardwaru"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "Stav hardwaru"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
msgid "Has Active Session"
msgstr "Má aktivní relaci"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "Má pokladnu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "Má řady k refundaci"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Header"
msgstr "Záhlaví"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
msgid "Header & Footer"
msgstr "Záhlaví a zápatí"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "Skrýt Použít platební terminál"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/HomeCategoryBreadcrumb.xml:0
#, python-format
msgid "Home"
msgstr "Úvod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#, python-format
msgid "How would you like to receive your receipt"
msgstr "Jak chcete obdržet stvrzenku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "DŮLEŽITÉ: Chybová hlášení z prodejního místa Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
msgid "IP Address"
msgstr "IP adresa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona označuje vyjímečnou aktivitu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "Identita zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread
msgid "If checked, new messages require your attention."
msgstr "Pokud je zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud je zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr ""
"Pokud je tato položka objednávky refundace, je v tomto poli uveden řádek "
"objednávky s refundací."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Pokud dodáte všechny produkty najednou, bude objednávka na dodávku "
"naplánována na základě největší dodací lhůty produktu. V opačném případě "
"bude vycházet z nejkratší doby."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "Displej Iface orientovaný na zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
msgid "Image"
msgstr "Obrázek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Import Orders"
msgstr "Import objednávek"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "Zlepšit navigaci pro nepřesné průmyslové dotykové obrazovky"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "Probíhá"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "Prodej musí být nový nebo stornovaný aby bylo možné jej smazat."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time"
msgstr "V reálném čase"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "Nesprávná adresa pro přepravu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "Nesprávné zaokrouhlování"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/ProductInfoButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductItem.xml:0
#, python-format
msgid "Info"
msgstr "Informace"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "Informační zpráva"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
msgid "Initial Category"
msgstr "Počáteční kategorie"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"Instalací účtové osnovy z obecných nastavení aplikace Fakturace/Účetníctví "
"se automaticky vytvoří bankovní a hotovostní platební metody."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Integrované platy kartou"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "Zprostředkující účet"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "Interní kategorie"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "Interní poznámky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Invalid action"
msgstr "Neplatná akce"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Invalid amount"
msgstr "Neplatná částka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Invalid email."
msgstr "Neplatný e-mail."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Invalid product lot"
msgstr "Neplatné šarže číslo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "Inventář"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "Řízení zásob"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "Faktura"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Invoice Journal"
msgstr "Deník faktur"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "Fakturační platba pro %s (%s) pomocí %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Invoiced"
msgstr "Fakturováno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_account
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__invoice_group
msgid "Invoicing"
msgstr "Fakturace"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box"
msgstr "IoT Box"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "IoT Box IP Address"
msgstr "IP adresa IoT Boxu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "Je sledující"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "Je fakturováno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "Je refundováno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "Je spočítaná celková částka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "Používá firemní měnu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "Je bar / restaurace"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "Je nainstalováno úplné účetnictví"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "Je to už včetně spropitného?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "Jedná se o změnu platby?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"Je zakázáno upravovat daň používanou v objednávce POS, která není "
"zveřejněna. Před úpravou daně musíte ukončit relace POS."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS testy"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
msgid "Journal"
msgstr "Deník"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "Položka deníku"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "Položka deníku"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Journal Items"
msgstr "Účetní deník"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Keep Session Open"
msgstr "Udržet relaci otevřenou"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Kpi POS hodnota celkem"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.led_lamp
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED Lampa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "Označení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Language"
msgstr "Jazyk"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "Velké posuvníky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session____last_update
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "Závěrečná hotovost poslední relace"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "Datum ukončení poslední relace"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "Kůže"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr "Ponechte prázdné pro použití výchozího účtu z firemního nastavení"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"Ponechte prázdné pro použití výchozího účtu z firemního nastavení.\n"
"Učet použitý jako účet čekajících plateb při vytváření záznamů účetních plateb pro bankovní platby."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"Ponechte prázdné pro použití výchozího účtu z firemního nastavení.\n"
"Přepíše účet pohledávek společnosti (pro prodejní místo) použitý v účetních zápisech."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "Ponechte prázdné pro použití účtu pohledávek zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"Ponechte prázdné pro použití účtu pohledávek zákazníka.\n"
"Definuje deník, do kterého se mají zaúčtovat akumulované platby (nebo jednotlivá platba, pokud je Identifikace zákazníka pravdivá) po uzavření relace.\n"
"U peněžního deníku zapisujeme přímo na výchozí účet v deníku přes položky výpisu.\n"
"U bankovního deníku zapisujeme na dlužný účet uvedený v tomto způsobu platby.\n"
"Povoleny jsou pouze peněžní a bankovní deníky."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.letter_tray
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "Zásobník na listy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_amount
msgid "Limited Partners Amount"
msgstr "Účet komanditních partnerů"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_partners_loading
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Partners Loading"
msgstr "Načítání komanditních partnerů"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_loading
msgid "Limited Product Loading"
msgstr "Načítání limitovaného produktu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limited_products_amount
msgid "Limited Products Amount"
msgstr "Účet limitovaných produktů"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Limited Products Loading"
msgstr "Načítání limitovaných produktů"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "Řádek č."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "List of Cash Registers"
msgstr "Seznam pokladen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Load Customers"
msgstr "Načíst zákazníky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining partners in the background"
msgstr "Načíst všechny zbývající partnery na pozadí"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Load all remaining products in the background"
msgstr "Načíst všechny zbývající produkty na pozadí"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Loading"
msgstr "načítání"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Loading Image Error"
msgstr "Chyba načítání obrázku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "Místní displej pro zákazníky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "Pořadové číslo přihlášení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Logo"
msgstr "Logo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "Název šarže"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/OrderWidget.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "Šarže / Sériové číslo(a) je vyžadováno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "Šarže / Seriové číslo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_loyalty
msgid "Loyalty Program"
msgstr "Věrnostní program"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Loyalty program to use for this point of sale."
msgstr "Věrnostní program pro toto Prodejní místo."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.magnetic_board
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "Magnetická tabule"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "Zaplatit"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"Uveďte v prodejním místě několik ceníků. Ceník můžete také použít pro "
"konkrétní zákazníky prostřednictvím kontaktního formuláře (na kartě Prodej)."
" Chcete-li aby byl platný, musí být tento ceník uveden zde jako dostupný "
"ceník. Jinak se použije výchozí ceník."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr ""
"Ujistěte se, že používáte IoT box v18.12 nebo vyšší. Přejděte na %s a "
"přijměte certifikát vašeho IoT boxu."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card"
msgstr "Načíst dárkový poukaz"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage gift card."
msgstr "Spravovat dárkový poukaz."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr "Správa propagace &amp; kupónové programy"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion and coupon programs."
msgstr "Spravujte propagační a kupónové programy."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
msgid "Manual Discounts"
msgstr "Ruční slevy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "Marže"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "Marže (%)"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Margin:"
msgstr "Marže:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "Maximum překročen"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "Maximální dosažená hodnota"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zpráv"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "Metoda"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Miscellaneous"
msgstr "Účetní zápisy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
msgid "Module Pos Hr"
msgstr "Hr modul místa prodeje"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.monitor_stand
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "Stojan na monitor"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#, python-format
msgid "More..."
msgstr "Více..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mé aktivity"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "Moje relace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Name"
msgstr "Jméno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "Potřeba zákazníka fakturovat"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr ""
"Pro zaúčtování ztracené částky potřebujete účet ztrát pro následující "
"deníky: %s\n"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr ""
"Pro zaúčtování získané částky potřebujete účet zisků pro následující deníky:"
" %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ProductInfoPopup.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Network Error"
msgstr "Chyba sítě"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "Nové"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "New Order"
msgstr "Nová objednávka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "Nové sezení"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.newspaper_rack
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "Stojan na noviny"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Další aktivita z kalendáře"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Termín další aktivity"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "Souhrn další aktivity"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "Další typ aktivity"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Next Order List"
msgstr "Seznam dalších objednávek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "Ne"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "No Taxes"
msgstr "Žádné daně"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr ""
"Pro tuto relaci nebyl nalezen žádný peněžní výpis. Nelze zaznamenat vrácenou"
" hotovost."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "No customer found"
msgstr "Nenalezen žádný zákazník"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: point_of_sale
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "Žádný odkaz na fakturu pro %s."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
#, python-format
msgid "No orders found"
msgstr "Nebyly nalezeny žádné objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid "No product found"
msgstr "Nebyl nalezen žádný produkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "No results found for \""
msgstr "Nebyly nalezeny žádné výsledky pro „"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid "No sequence defined on the journal"
msgstr "V deníku není definována žádná sekvence"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid "No sessions found"
msgstr "Nenalezeny žádné relace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "None"
msgstr "Nic"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "Není fakturováno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Notes"
msgstr "Poznámky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_opened_session
msgid "Number of Opened Session"
msgstr "Počet otevřených relací"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Partners Loaded"
msgstr "Počet načtených partnerů"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "Počet výtisků"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Number of Products Loaded"
msgstr "Počet načtených produktů"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "Počet refundovaných objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "Počet položek refundovaných v této objednávce."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Počet zpráv, které vyžadují akci"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of partners loaded can not be 0"
msgstr "Počet načtených partnerů nesmí být 0"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Number of product loaded can not be 0"
msgstr "Počet načtených produktů nesmí být 0"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_unread_counter
msgid "Number of unread messages"
msgstr "Počet nepřečtených zpráv"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "OPENING CASH CONTROL"
msgstr "KONTROLA POČÁTEČNÍ HOTOVOSTI"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Offline"
msgstr "Nepřipojeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#, python-format
msgid "Offline Error"
msgstr "Chyba offline"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Offline Orders"
msgstr "Offline objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ConfirmPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/EditListPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/ErrorTracebackPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/NumberPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OfflineErrorPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/OrderImportPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextAreaPopup.js:0
#: code:addons/point_of_sale/static/src/js/Popups/TextInputPopup.js:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OfflineErrorPopup.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Ongoing"
msgstr "Pokračující"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr ""
"Pro tuto položku refundace je povoleno pouze záporné množství. Kliknutím na "
"+/- upravíte množství, které má být vráceno."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "Zaokrouhlení použijte pouze v hotovosti"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr ""
"Pouze deníky typu 'Peněžní' a 'Bankovní' mohou být použity v platebních "
"metodách."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load a limited number of customers at the opening of the PoS."
msgstr "Načíst pouze omezený počet zákazníků během otevření prodejního místa."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only load most common products at the opening of the PoS."
msgstr "Načíst pouze nejčastější produkty během otevření prodejního místa."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Only on cash methods"
msgstr "Pouze u hotovostních metod"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr ""
"Pouze uživatelé s přístupovými právy Správce pro aplikaci PoS mohou měnit "
"ceny produktů na objednávkách."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ""
"Podporovány jsou pouze formáty obrázků kompatibilní s webem, například .png "
"nebo .jpeg."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "Otevřít pokladnu"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Open POS Menu"
msgstr "Otevřít menu prod. místa"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "Otevřete relace PoS, které používají tuto platební podmínku."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "Otevřít sezení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Open session"
msgstr "Otevřít relaci"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "Otevřít dialog s podrobnostmi o penězích"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "Otevřeno"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Opened Sessions"
msgstr "Otevřené relace"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "Otevřeno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Opening"
msgstr "Otevírání"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "Otevření"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "Datum otevření"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "Otevírání poznámek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashOpeningPopup.xml:0
#, python-format
msgid "Opening cash"
msgstr "Otevření hotovosti"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.account_cashbox_line_action
msgid "Opening/Closing Values"
msgstr "Počáteční / uzavírací hodnoty"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation Type"
msgstr "Typ operace"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Operation type used to record product pickings <br/>\n"
"                                    Products will be taken from the default source location of this operation type"
msgstr ""
"Typ operace používaný k evidenci odběrů produktů <br/>\n"
"                                    Produkty budou převzaty z výchozího zdrojového umístění tohoto typu operace"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "Typy operací se zobrazují na ovládacím panelu inventáře."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ScaleScreen/ScaleScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "Objednávka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Order %s"
msgstr "Objednávka %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "Objednávka %s není plně zaplacena."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "Počet objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "Datum objednání"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
msgid "Order IDs Sequence"
msgstr "Posloupnost ID objednávky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "Posloupnost řádků ID objednávky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "Položky objednávky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
msgid "Order Ref"
msgstr "Čís. objednávky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "Pořadové číslo objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Order is empty"
msgstr "Objednávka je prázdná"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Order is not synced. Check your internet connection"
msgstr "Objednávka není synchronizována. Zkontrolujte připojení k internetu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "Řádky objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr ""
"Položky objednávky v tomto poli jsou položky, které vrátily peníze za tuto "
"položku objednávky."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/TicketButton.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "Objednávky"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "Analýza objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
msgid "Other Devices"
msgstr "Další zařízení"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "Jiné informace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Others"
msgstr "Ostatní"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "Nesplacený účet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "Objednávka prod. místa"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "POS Objednávka %s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "POS řádek objednávky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "POS řádky objednávky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "Objednávky prod. místa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "POS řádky objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "Platební metoda prod. místa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "Kategorie produktu prod. místa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "Prodeje v místě prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "Relace prod. místa"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "POS error"
msgstr "Chyba prod. místa"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "Řádek objednávky POS %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "Zaplaceno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "Nadřazená kategorie"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__partner_load_background
msgid "Partner Load Background"
msgstr "Načítání partnera na pozadí"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ActionpadWidget.xml:0
#, python-format
msgid "Pay"
msgstr "Zaplatit"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "Zaplatit objednávku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductScreen.xml:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "Platba"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "Datum platby"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "Platební podmínky"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
msgid "Payment Methods"
msgstr "Platební podmínky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "Info o potvrzení platby"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "Číslo platby"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "stav platby"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment Successful"
msgstr "Platba byla úspěšná"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "Platební terminály"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "ID platební transakce"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Payment methods available"
msgstr "Dostupné platební podmínky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment request pending"
msgstr "Žádost o platbu čeká na vyřízení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Payment reversed"
msgstr "Platba přijata"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "Platby"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "Platební podmínky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "Payments in"
msgstr "Platby v"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#, python-format
msgid "Payments:"
msgstr "Platby:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Pending Electronic Payments"
msgstr "Nevyřízené elektronické platby"

#. module: point_of_sale
#: model:ir.filters,name:point_of_sale.filter_orders_per_session
msgid "Per session"
msgstr "Za relaci"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Osoba, která používá pokladnu. Může to být brigádník, student nebo dočasný "
"zaměstnanec."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Pick which product categories are available"
msgstr "Vyberte, které kategorie produktů jsou k dispozici"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "Dodání"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "Množství na výběr"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "Dodání POS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "Vyzvednutí"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Picture"
msgstr "Obrázek"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "Plast"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "Potvrďte prosím velkou částku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "Zkontrolujte, zda je IoT Box stále připojen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid "Please check if the printer is still connected."
msgstr "Zkontrolujte, zda je tiskárna stále připojená."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/printers.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue"
msgstr ""
"Zkontrolujte prosím, že je tiskárna stále připojena.\n"
"Některé prohlížeče nepovolují volání HTTP z webových stránek do zařízení v síti (z bezpečnostních důvodů). V takovém případě budete muset vyřešit problém podle dokumentace Odoo 'Certifikát s vlastním podpisem pro tiskárny ePOS' a 'Zabezpečené připojení (HTTPS)'."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ClosePosPopup.js:0
#, python-format
msgid "Please check your internet connection and try again."
msgstr "Zkontrolujte prosím připojení k internetu a zkuste to znovu."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr ""
"Před uzavřením prosím zavřete všechny relace prodejního místa v tomto "
"období. Otevřená sezení jsou: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "Nakonfigurujte si prosím platební metodu ve svém POS."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "Nastavte prosím výnosový účet pro tento produkt: \"%s\" (id: %d)."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "Vytiskněte prosím fakturu z objednávky"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "Prosím zadejte partnera pro prodej."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "Vyberte prosím platební podmínku."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "Zvolte prosím zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pos_categ_id
msgid "PoS Category"
msgstr "Kategorie místa prodeje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Interface"
msgstr "Rozhraní PoS"

#. module: point_of_sale
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "Objednávky prod. místa"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "PoS Product Categories"
msgstr "Kategorie produktu místa prodeje"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "Kategorie výrobku PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "Prodejní místo"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_pos
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model:ir.ui.menu,name:point_of_sale.menu_pos_config_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "Prodejní místo"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "Analýza prodejního místa"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_id
msgid "Point of Sale Category"
msgstr "Kategorie Prodejního místa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "Nastavení prodejního místa"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "Nastavení prodejního místa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
msgid "Point of Sale Configurations"
msgstr "Konfigurace prodejních míst"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "Podrobnosti o místu prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "Detaily Zprávy o místech prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "Zpráva o fakturách z místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
msgid "Point of Sale Journal"
msgstr "Deník prodejního místa"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "Průvodce provedením platby v místě prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "Správcovská skupina místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "Typ operace místa prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Objednávkové linky v místě prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "Objednávky Prodejního místa"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Zpráva o objednávkách z místa prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "Platební podmíky v místě prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Platby v místě prodeje"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "Sezení Prodejního místa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "Testy místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "Skupina uživatelů Místa Prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "Konfigurace místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Objednávka prod. místa"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "Počet objednávek místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "Řádek objednávky místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "Platba prod. místa"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "Kategorie produktu místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "Relace Místa Prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "Trvání relace místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "Stav relace místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "Uživatel místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "Relace místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
msgid "PosBox"
msgstr "Poštovní schránka"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "Kladné množství není povoleno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Postcode"
msgstr "PSČ"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "Schváleno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Předchozí seznam objednávek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Price"
msgstr "Cena"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Price Control"
msgstr "Řízení cen"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Price Unit"
msgstr "Jednotka ceny"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "Sleva ceny z %s -> %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Price excl. VAT:"
msgstr "Cena bez DPH:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetPricelistButton.xml:0
#, python-format
msgid "Price list"
msgstr "Ceník"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "Naceněný produkt"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "Ceník"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Ceníky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Ceny"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "Tisk"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/ReceiptScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ControlButtons/ReprintReceiptButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/ReprintReceiptScreen.xml:0
#, python-format
msgid "Print Receipt"
msgstr "Tisk účtenky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SaleDetailsButton.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "Tisk sestavy se všemi prodeji aktuální relace PoS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print invoices on customer request"
msgstr "Tisknout faktury na žádost zákazníka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "Automatický tisk účtenek po zaregistrování platby"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Tisk přes proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Printer"
msgstr "Tiskárna"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "Tisk není v některých prohlížečích podporován"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Misc/AbstractReceiptScreen.js:0
#, python-format
msgid ""
"Printing is not supported on some browsers due to no default printing "
"protocol is available. It is possible to print your tickets by making use of"
" an IoT Box."
msgstr ""
"Tisk není v některých prohlížečích podporován, protože není k dispozici "
"žádný výchozí tiskový protokol. Vstupenky je možné vytisknout pomocí IoT "
"Boxu."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "Skupina zásobování"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product"
msgstr "Produkt"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "Kategorie výrobku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_configurator
msgid "Product Configurator"
msgstr "Konfigurátor produktu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__product_load_background
msgid "Product Load Background"
msgstr "Načítání produktu na pozadí"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product Prices"
msgstr "Ceny produktu"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "Produktové kategorie produktu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "Množství výrobku"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "Šablona produktu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Měrná jednotka výrobku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "MJ produktu"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "Kategorie měrné jednotky produktu"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "Varianty výrobku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Product information"
msgstr "Informace o výrobku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductsWidgetControlPanel.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr ""
"Produkt není načten. Pokusili jste se načíst produkt ze serveru, ale došlo k"
" chybě sítě."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Product prices on receipts"
msgstr "Ceny produktů na účtenkách"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
msgid "Product tips"
msgstr "Spropitné k produktu"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "Produkty"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "Proxy připojené"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "Proxy odpojené"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/ProxyStatus.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "Varování proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/NumpadWidget.xml:0
#, python-format
msgid "Qty"
msgstr "Množství"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "Množství"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "REASON"
msgstr "DŮVOD"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "Čtení měrky váhy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/js/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "Jste připraveni spustit vaše <b>místo prodeje</b>?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMovePopup.xml:0
#, python-format
msgid "Reason"
msgstr "Důvod"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid "Receipt"
msgstr "Účtenka"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "Účtenka %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
msgid "Receipt Footer"
msgstr "Zápatí potvrzenky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
msgid "Receipt Header"
msgstr "Záhlaví účtenky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "Číslo účtenky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Receipt Printer"
msgstr "Tiskárna účtenek"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "V tomto deníku zaznamenávejte platby pomocí terminálu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "Obnovení relace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "Obnovit zobrazení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/RefundButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Refund"
msgstr "Vrátit peníze"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "Refundovat položky objednávky"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "Refundovat objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderlineDetails.xml:0
#, python-format
msgid "Refunded"
msgstr "Refundováno"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "Refundovaná objednávka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "Refundovaná položka objednávky"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "Refundované objednávky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "Počet refundovaných objednávek"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "Refundované množství"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "Refunding %s in "
msgstr "Refundace %s v"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "Přijaté dobropisy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Remaining"
msgstr "Zbývá"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "Zbývající nesynchronizované objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Remove"
msgstr "Odebrat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Replenishment"
msgstr "Doplňování"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "Přehledy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "Vytisknout fakturu znovu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Request sent"
msgstr "Požadavek odeslán"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Reset"
msgstr "Resetovat"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "Odpovědný"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "Zodpovědný uživatel"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "Omezte úpravy cen pro manažery"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
msgid "Restrict Product Categories"
msgstr "Omezit kategorie produktů"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Restrict price modification to managers"
msgstr "Omezení úpravy cen na manažery"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Retry"
msgstr "Znovu"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "Vrátit výrobky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "Vráceno"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "Storno: %s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "Požadavek na zrušení byl odeslán do terminálu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse"
msgstr "Konvertovat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "Reverzní platba"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Review"
msgstr "Recenze"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Rounding"
msgstr "Zaokrouhlování"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Rounding Method"
msgstr "Zaokrouhlovací metoda"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "Chyba zaokrouhlování v řádcích plateb"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "Spustit JS testy místa prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "SN"
msgstr "SN"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "Počet řádků prodeje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "Řádek prodeje"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "Podrobnosti prodeje"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sales Journal"
msgstr "Deník prodejů"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Save"
msgstr "Uložit"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "Tuto stránku uložte a nastavte funkci."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scale"
msgstr "Měřítko"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan"
msgstr "Skenování"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "Skenování EAN-13"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Skenování přes proxy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/ProxyStatus.js:0
#, python-format
msgid "Scanner"
msgstr "Skener"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "Search Customers"
msgstr "Hledat zákazníky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Search Orders..."
msgstr "Hledat objednávky..."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductsWidgetControlPanel.xml:0
#, python-format
msgid "Search Products..."
msgstr "Hledat produkty..."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "Hledat zakázku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/SelectionPopup.js:0
#, python-format
msgid "Select"
msgstr "Vybrat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "Vybrat fiskální pozici"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select an order"
msgstr "Vyberte objednávku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/CashMovePopup.js:0
#, python-format
msgid "Select either Cash In or Cash Out before confirming."
msgstr "Před potvrzením vyberte buď příjem nebo výdej hotovosti."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Select product attributes"
msgstr "Výběr atributů produktu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/HomeCategoryBreadcrumb.js:0
#, python-format
msgid "Select the category"
msgstr "Vyberte kategorii"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetPricelistButton.js:0
#, python-format
msgid "Select the pricelist"
msgstr "Vyberte ceník"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "Vyberte produkt(y) k refundaci a nastavte množství"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__selectable_categ_ids
msgid "Selectable Categ"
msgstr "Volitelná kategorie"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Sell products and deliver them later."
msgstr "Prodávejte produkty a dodávejte je později."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send"
msgstr "Odeslat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "Odeslat žádost o platbu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorTracebackPopup.xml:0
#, python-format
msgid "Send by email"
msgstr "Odeslat e-mailem"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "Odeslání e-mailu selhalo. Zkuste to znovu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
msgid "Sequence"
msgstr "Číselná řada"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "Pořadové číslo"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/EditListInput.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "Seriové číslo / šarže"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Served by"
msgstr "Obsluhováno od"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Server Error"
msgstr "Chyba serveru"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "Sezení"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "ID sezení"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "Záznam relace do deníku"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Session ids:"
msgstr "ID relace:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Session is closed"
msgstr "Relace je uzavřena"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "Relace otevřena po dobu 7 dnů"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "Sezení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Set Customer"
msgstr "Nastavit zákazníka"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
msgid "Set Maximum Difference"
msgstr "Nastavit maximální rozdíl"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Set Weight"
msgstr "Nastavit hmotnost"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr ""
"Nastavte maximální povolený rozdíl mezi očekávanými a započítanými penězi "
"během ukončení relace"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr ""
"Nastavte maximální povolený rozdíl mezi očekávanými a započítanými penězi "
"během ukončení relace."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "Nastavení fiskální pozice"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Nastavit více cen za produkt, automatizované slevy atd."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set of coins/bills that will be used in opening and closing control"
msgstr ""
"Sada mincí/bankovek, které budou použity při ovládání otevírání a zavírání"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Set shop-specific prices, seasonal discounts, etc."
msgstr "Nastavit ceny specifické pro obchod, sezonní slevy atd."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "Nastavení nového množství"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Settings"
msgstr "Nastavení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Ship Later"
msgstr "Odeslat později"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
msgid "Shipping Policy"
msgstr "Přepravní politika"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#, python-format
msgid "Shopping cart"
msgstr "Nákupní košík"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "Zobrazit zákazníkům pokladnu pomocí vzdáleně připojené obrazovky."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Show customers checkout in a pop-up window. Can be moved to a second screen."
msgstr ""
"Zobrazit zákazníkům pokladnu ve vyskakovacím okně. Lze přesunout na druhou "
"obrazovku."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
msgid ""
"Show customers checkout in a pop-up window. Recommend to be moved to a "
"second screen visible to the client."
msgstr ""
"Zobrazit zákazníkům pokladnu ve vyskakovacím okně. Doporučujeme přesunout na"
" druhou obrazovku viditelnou pro klienta."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
msgid "Show employee login screen"
msgstr "Zobrazit přihlašovací obrazovku zaměstnance"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Šest"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Platební terminál Six"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "Velikost"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Chrome.xml:0
#, python-format
msgid "Skip"
msgstr "Přeskočit"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "Přeskočit obrazovku náhledu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/CategoryBreadcrumb.xml:0
#, python-format
msgid "Slash"
msgstr "Rozřezat"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.small_shelf
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "Malá police"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Some Cash Registers are already posted. Please reset them to new in order to close the session.\n"
"Cash Registers: %r"
msgstr ""
"Některé pokladny jsou již zveřejněny. Chcete-li relaci ukončit, resetujte je na nové.\n"
"Registrační pokladny: %r"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "Některá sériová čísla/čísla šarží chybí"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Některé objednávky nebylo možné odeslat na server kvůli chybám konfigurace. "
"Můžete opustit prodejní místo, ale relaci nezavírejte, dokud nebude problém "
"vyřešen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"Některé objednávky nebylo možné odeslat na server kvůli problémům s "
"připojením k internetu. Můžete opustit Místo Prodeje, ale relaci "
"nezavírejte, dokud nebude problém vyřešen."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr ""
"Některé, pokud ne všechny, následné zpracování po synchronizaci objednávky "
"selhalo."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Specific route"
msgstr "Specifická trasa"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "Určete šarži/sériové číslo na řádku objednávky PoS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
msgid "Spefic route for products delivered later."
msgstr "Určete trasu pro produkty doručené později."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
msgid "Start Category"
msgstr "Počáteční kategorie"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "Počáteční datum"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Start selling from a default product category"
msgstr "Začněte prodej z přednastavené kategorie produktu"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "Počáteční zůstatek"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "State"
msgstr "Stav"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "Stav"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stav na základě aktivit\n"
"Vypršeno: Datum již uplynulo\n"
"Dnes: Datum aktivity je dnes\n"
"Plánováno: Budoucí aktivity."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "Pohyb zásob"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "Pravidlo zásob"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "Zásoby by měly být aktualizovány při uzávěrce"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Street"
msgstr "Ulice"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
#, python-format
msgid "Subtotal"
msgstr "Mezisoučet"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "Mezisoučet bez daně"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "Mezisoučet bez slevy"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "Úspěšně importováno"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "Úspěšně vytvořena hotovost %s z %s."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Sum of opening balance and transactions."
msgstr "Součet počátečního zůstatku a transakcí."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "Suma mezisoučtů"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "Synchronizace je připojena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "Synchronizace připojení"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "Synchronizace odpojena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/SyncNotification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "Chyba synchronizace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/CustomerFacingDisplay/CustomerFacingDisplayOrder.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "CELKEM"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_box_out
msgid "Take Money In/Out"
msgstr "Vezměte peníze dovnitř / ven"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ControlButtons/SetFiscalPositionButton.js:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "Daň"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "Výše daně"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
msgid "Tax Display"
msgstr "Zobrazení daně"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#, python-format
msgid "Tax ID"
msgstr "DIČ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime
msgid "Tax Regime"
msgstr "Daňový režim"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "Hodnota výběru daňového režimu"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "Cena bez daně"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "Cena včetně daně"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "Daně"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "Daně k uplatnění"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#, python-format
msgid "Taxes:"
msgstr "Daně:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid ""
"Technical field which is used to hide use_payment_terminal when no payment "
"interfaces are installed."
msgstr ""
"Technické pole používané ke skrytí use_payment_terminal, pokud nejsou "
"nainstalována žádná platební rozhraní."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Tel:"
msgstr "Tel:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Popups/ErrorBarcodePopup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, client, employee or action "
"associated with the scanned barcode."
msgstr ""
"Prodejní místo nemohlo najít žádný produkt, klienta, zaměstnance nebo akci "
"spojenou s naskenovaným čárovým kódem."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr ""
"Místo Prodeje podporuje pouze strategii zaokrouhlování \"přidat "
"zaokrouhlovací řádek\"."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr ""
"Částka nemůže být vyšší než splatná částka, pokud nemáte nakonfigurován "
"způsob platby v hotovosti."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the "
"transaction."
msgstr ""
"Pro potvrzení transakce je nutné zaokrouhlit částku na řádcích s platbami."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr ""
"Strategie zaokrouhlování hotovosti v prodejním místě %(pos)s musí být: "
"'%(value)s'"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "Výchozí ceník musí být zahrnut v dostupných cenících."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr ""
"Výchozí ceník nesmí patřit žádné společnosti ani společnosti prodejního "
"místa."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The fiscal data module encountered an error while receiving your order."
msgstr "Modul fiskálních dat zaznamenal chybu při příjmu objednávky."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr ""
"Není načtena fiskální pozice použitá v původní objednávce. Ujistěte se, že "
"je načtena přidáním do nastavení pozice."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr ""
"Název hostitele nebo adresa IP hardwarového proxy, bude automaticky "
"detekován, pokud zůstane prázdný."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr ""
"Deník faktur musí být ve stejné měně jako Deník prodeje nebo v měně "
"společnosti, pokud není nastavena."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal of the point of sale %s must belong to the same company."
msgstr "Deník faktur prodejního místa %s musí patřit stejné společnosti."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "The maximum difference allowed is"
msgstr "Maximální povolený rozdíl je"

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique !"
msgstr "Název sezení tohoto prodejního místa musí být unikátní!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr ""
"Počet objednávek v místě prodeje vztahujících se k tomuto zákazníkovi."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "Objednávka nemohla být odeslána na server z důvodu neznámé chyby"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr ""
"Objednávka byla synchronizována dříve. Vytvořte prosím fakturu z backendu "
"pro tuto objednávku: "

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr ""
"Vybraný způsob platby není v konfiguraci relace Místa Prodeje povolen."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "Platební metody prodejního místa %s musí patřit stejné společnosti."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,help:point_of_sale.field_pos_session__config_id
msgid "The physical point of sale you will use."
msgstr "Fyzické prodejní místo, které budete používat."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr ""
"Prodejní místo bude ve výchozím nastavení zobrazovat tuto kategorii "
"produktů. Není-li zadána žádná kategorie, zobrazí se všechny dostupné "
"produkty."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"Prodejní místo zobrazí pouze produkty, které spadají do jednoho ze zvolených"
" stromů kategorií. Pokud není zadána žádná kategorie, zobrazí se všechny "
"dostupné produkty."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured."
msgstr ""
"Ceník je použitý, pokud není vybrán žádný zákazník nebo pokud zákazník nemá "
"nakonfigurován žádný prodejní ceník."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_display_categ_images
msgid "The product categories will be displayed with pictures."
msgstr "Kategorie produktů budou zobrazeny s obrázky."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Kurz měny k měně sazby použitelné ke dni objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr ""
"Obrazovka potvrzení bude přeskočena, pokud se potvrzení automaticky "
"vytiskne."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "Potvrzení bude automaticky vytištěno na konci každé objednávky."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/TicketScreen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr ""
"Požadované množství k refundaci je větší než objednané množství. %s je "
"vyžadováno pokud pouze %s bude refundováno."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr "Požadované množství k refundaci je větší než refundované množství %s."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The sales journal of the point of sale %s must belong to its company."
msgstr "Deník prodeje prodejního místa %s musí patřit stejné společnosti."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "Vybraný zákazník potřebuje adresu."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr ""
"Vybrané ceníky nesmí patřit žádné společnosti ani společnosti prodejního "
"místa."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "Server zaznamenal chybu při příjmu objednávky."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr ""
"Relace byla otevřena po neobvykle dlouhou dobu. Zvažte prosím její uzavření."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr ""
"Transakce zpracovává Adyen. Nastavte přihlašovací údaje Adyen na související"
" platební metodu."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr ""
"Transakce zpracovává společnost Six. Nastavte IP adresu terminálu v "
"souvisejícím způsobu platby."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr ""
"Transakce zpracovává společnost Vantiv. Nastavte své přihlašovací údaje "
"Vantiv na související platební metodu."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "Teoretický konečný zůstatek"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "There are"
msgstr "Existují"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/ProductList.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "V této kategorii není žádné zboží."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"V relaci jsou stále objednávky ve stavu konceptu. Zaplaťte nebo zrušte následující objednávky pro ověření relace:\n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr ""
"Existují nesynchronizované objednávky. Chcete tyto objednávky "
"synchronizovat?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr ""
"Mezi částkami k zaúčtování a částkami objednávek je rozdíl, který je "
"pravděpodobně způsoben změnami daní nebo účetních konfigurací."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "Již probíhá elektronická platba."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is at least one pending electronic payment.\n"
"Please finish the payment with the terminal or cancel it then remove the payment line."
msgstr ""
"Existuje alespoň jedna nevyřízená elektronická platba.\n"
"Dokončete platbu pomocí terminálu nebo ji zrušte a poté odstraňte položku platby."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr ""
"Ve společnosti není nakonfigurována žádná účtová osnova. Přejděte do "
"nastavení fakturace a nainstalujte účtovou osnovu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please add a cash payment method in the point of sale configuration."
msgstr ""
"V tomto prodejním místě není k dispozici žádná hotovostní platební metoda, která by přijímala drobné. \n"
"\n"
" Přidejte prosím platbu v hotovosti v místě prodeje."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"V tomto prodejním místě není k dispozici žádná hotovostní platební metoda, která by přijímala drobné. \n"
"\n"
" Zaplaťte prosím přesnou částku, nebo přidejte platbu v hotovosti v místě prodeje"

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_box.py:0
#, python-format
msgid "There is no cash register for this PoS Session"
msgstr "K tomu sezení není přiřazena žádná pokladna"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "V této relaci není přiřazená žádná pokladna."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr ""
"Musí existovat nejméně jeden výrobek v objednávce před jeho validací a "
"fakturací."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"This account is used as intermediary account when nothing is set in a "
"payment method."
msgstr ""
"Tento účet je použit jako zprostředkující účet, když není v platební metodě "
"nic nastaveno."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"Toto pole zobrazuje maximální povolený rozdíl mezi konečným zůstatkem a "
"teoretickou hotovostí při uzavření relace u manažerů mimo prodejní místo. "
"Pokud bude dosaženo tohoto maxima, uživatel při ukončení své relace zobrazí "
"chybovou zprávu, že musí kontaktovat svého manažera."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr ""
"Toto pole slouží k předání ID skupiny správců Místa Prodeje klientovi v "
"místě prodeje."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr ""
"Toto pole slouží k předání id skupiny uživatelů pos klientovi prodejního "
"místa."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"This invoice has been created from the point of sale session: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"
msgstr ""
"Tato faktura byla vytvořena z následující relace místa prodeje: <a href=# "
"data-oe-model=pos.order data-oe-id=%d>%s</a>"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__customer_note
msgid "This is a note destined to the customer"
msgstr "Toto je poznámka určená zákazníkovi"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__allowed_pricelist_ids
msgid "This is a technical field used for the domain of pricelist_id."
msgstr "Toto je technické pole používané pro doménu pricelist_id."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr ""
"To je užitečné pro restaurace s místními a rozvážkovými službami , které "
"zahrnují konkrétní daňové sazby."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr "Tento deník je spojen se způsobem platby. Nemůžete změnit jeho typ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"Tato operace zničí všechny nezaplacené příkazy v prohlížeči. Ztratíte "
"všechny neuložené údaje a opustíte prodejní místo. Tuto operaci nelze vrátit"
" zpět."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/DebugWidget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"Tato operace natrvalo zničí všechny placené příkazy z místního úložiště. "
"Ztratíte všechna data. Tuto operaci nelze vrátit zpět."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"Tato objednávka již má položky refundace pro %s. Nemůžeme změnit zákazníka, "
"který je k ní přidružen. Vytvořte novou objednávku pro nového zákazníka."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderWidget.xml:0
#, python-format
msgid "This order is empty"
msgstr "Tato objednávka je prázdná"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr ""
"Tato objednávka ještě není synchronizována se serverem. Ujistěte se, že je "
"synchronizována, a zkuste to znovu."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "This product is used as reference on customer receipts."
msgstr "Tento produkt se používá jako odkaz na stvrzenku zákazníka."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr ""
"Tato sekvence je automaticky vytvořena společností Odoo, ale můžete ji "
"změnit tak, abyste přizpůsobili referenční čísla vašich řádků objednávek."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr ""
"Tato sekvence je automaticky vytvořena společností Odoo, ale můžete ji "
"změnit, abyste přizpůsobili referenční čísla vašich objednávek."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "Tato relace je již uzavřena."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "Tato daň se vztahuje na každý nový produkt vytvořený v katalogu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Tip"
msgstr "Tip"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "Výše spropitného"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tip Product"
msgstr "Produktový tip"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.product_product_tip
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Tips"
msgstr "Spropitné"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "Spropitné:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "K uzavření"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "To Pay"
msgstr "K platbě"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/OrderlineDetails.js:0
#, python-format
msgid "To Refund: %s"
msgstr "K refundaci: %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "Pro vážení s měrkou"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "Fakturovat"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "Pro nové objednávky začněte novou relaci."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "Vrácené produkt(y), které potřebují otevřít relaci v místě prodeje %s"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_ship
msgid "To ship"
msgstr "K odeslání"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Popups/MoneyDetailsPopup.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/TicketScreen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "Celkem"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "Hotovostní operace celkem"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "Celkové náklady:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "Celková sleva"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenStatus.xml:0
#, python-format
msgid "Total Due"
msgstr "Konečná cena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "Celková marže:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "Celkem zaplaceno (zaokrouhleně)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "Celková částka plateb"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "Celková cena"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "Total Price excl. VAT:"
msgstr "Celková cena bez DPH:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ReceiptScreen/OrderReceipt.xml:0
#, python-format
msgid "Total Taxes"
msgstr "Daně celkem"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "Celková částka platby."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "Celkové náklady"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total of all paid sales orders"
msgstr "Celkem zaplacené prodejní objednávky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Total of closing cash control lines."
msgstr "Součet uzavíracích řádků kontroly hotovosti."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Total of opening cash control lines."
msgstr "Součet počátečních řádků kontroly hotovosti."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "Celkový počet"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/SaleDetailsReport.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/OrderSummary.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/TicketScreen/OrderDetails.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "Celkem:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "Transakce"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "Transakce byla uzavřena"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "Převod"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
msgid "Type"
msgstr "Typ"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "Typ použité karty"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ zaznamenané výjimečné aktivity."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"Nelze zavřít a ověřit relaci.\n"
"Nastavte prosím odpovídající daňový účet v každém řádku přerozdělení následujících daní: \n"
"%s"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "Nelze stáhnout fakturu."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/TicketScreen/ControlButtons/InvoiceButton.js:0
#, python-format
msgid "Unable to invoice order."
msgstr "Nelze fakturovat objednávku."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr ""
"Tuto konfiguraci PoS nelze upravit, protože nemůžete upravovat %s, když je "
"relace otevřená."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientListScreen.js:0
#, python-format
msgid "Unable to save changes."
msgstr "Nelze uložit změny."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "Nelze zobrazit informace o této chybě."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "Nelze synchronizovat objednávku"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "Jednotková cena"

#. module: point_of_sale
#: model:product.product,uom_name:point_of_sale.desk_organizer
#: model:product.product,uom_name:point_of_sale.desk_pad
#: model:product.product,uom_name:point_of_sale.led_lamp
#: model:product.product,uom_name:point_of_sale.letter_tray
#: model:product.product,uom_name:point_of_sale.magnetic_board
#: model:product.product,uom_name:point_of_sale.monitor_stand
#: model:product.product,uom_name:point_of_sale.newspaper_rack
#: model:product.product,uom_name:point_of_sale.product_product_consumable
#: model:product.product,uom_name:point_of_sale.product_product_tip
#: model:product.product,uom_name:point_of_sale.small_shelf
#: model:product.product,uom_name:point_of_sale.wall_shelf
#: model:product.product,uom_name:point_of_sale.whiteboard
#: model:product.product,uom_name:point_of_sale.whiteboard_pen
#: model:product.template,uom_name:point_of_sale.desk_organizer_product_template
#: model:product.template,uom_name:point_of_sale.desk_pad_product_template
#: model:product.template,uom_name:point_of_sale.led_lamp_product_template
#: model:product.template,uom_name:point_of_sale.letter_tray_product_template
#: model:product.template,uom_name:point_of_sale.magnetic_board_product_template
#: model:product.template,uom_name:point_of_sale.monitor_stand_product_template
#: model:product.template,uom_name:point_of_sale.newspaper_rack_product_template
#: model:product.template,uom_name:point_of_sale.product_product_consumable_product_template
#: model:product.template,uom_name:point_of_sale.product_product_tip_product_template
#: model:product.template,uom_name:point_of_sale.small_shelf_product_template
#: model:product.template,uom_name:point_of_sale.wall_shelf_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_pen_product_template
#: model:product.template,uom_name:point_of_sale.whiteboard_product_template
msgid "Units"
msgstr "Jednotky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ErrorBarcodePopup.xml:0
#, python-format
msgid "Unknown Barcode"
msgstr "Neznámý čárový kód"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/custom_hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "Neznámá chyba"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread
msgid "Unread Messages"
msgstr "Nepřečtené zprávy"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo nepřečtených zpráv"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ClientListScreen/ClientDetailsEdit.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "Nepodporovaný formát souboru"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ReceiptScreen/ReceiptScreen.js:0
#, python-format
msgid "Unsynced order"
msgstr "Nesynchronizovaná objednávka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "UoM"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "Aktualizovat množství na skladě"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "Použijte platební terminál"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use a default specific tax regime"
msgstr "Použijte výchozí daňový režim"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
msgid "Use a pricelist."
msgstr "Použijte ceník."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "Pomocí čárových kódů můžete skenovat produkty, zákaznické karty atd."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Use employee credentials to log in to the PoS session and switch cashier"
msgstr ""
"Přihlášení k relaci místa prodeje a přepnutí pokladny pomocí pověření "
"zaměstnance."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "Uživatel"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "Uživatelské štítky"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/CashMoveReceipt.xml:0
#, python-format
msgid "VAT:"
msgstr "DPH:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "Valid product lot"
msgstr "Platná šarže produktu"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreen.xml:0
#, python-format
msgid "Validate"
msgstr "Ověřit"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (US & Canada)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Vantiv platební terminál"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/PaymentScreen/PaymentScreenElectronicPayment.xml:0
#, python-format
msgid "Waiting for card"
msgstr "Čekání na kartu"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.wall_shelf
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "Jednotka nástěnné police"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "Warehouse"
msgstr "Sklad"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "Zprávy webstránky"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "Historie komunikace webstránky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/ChromeWidgets/DebugWidget.xml:0
#, python-format
msgid "Weighing"
msgstr "Vážení"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "Vážený produkt"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "Když jsou všechny produkty připraveny"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"Při každém uzavření relace se v následujícím účetním deníku vytvoří jeden "
"záznam pro všechny nevyfakturované zakázky. Faktury jsou v účetnictví "
"evidovány odděleně."

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "Bílá tabule"

#. module: point_of_sale
#: model:product.product,name:point_of_sale.whiteboard_pen
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "Fix na bílou tabuli"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "With a"
msgstr "S"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "Would you like to load demo data?"
msgstr "Chcete načíst demo data?"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Ano"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr ""
"Není povoleno měnit konfiguraci zaokrouhlování hotovosti, když je již "
"otevřena pos relace která ji používá."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "Toto množství nesmíte měnit"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/ProductScreen/ProductScreen.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr ""
"Pokoušíte se prodávat produkty se sériovými čísly/čísly šarží, ale některá z nich nejsou nastavena.\n"
"Chcete přesto pokračovat?"

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid ""
"You can't validate a bank statement that is used in an opened Session of a "
"Point of Sale."
msgstr ""
"Nelze ověřit platnost bankovního výpisu, který je použit v otevřené relaci "
"prodejního místa."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"Nemůžete: vytvořit pos objednávku z rozhraní backendu, zrušit nastavení "
"ceníku nebo vytvořit pos.objednávku v python testu pomocí nástroje Form, "
"nebo upravit zobrazení formuláře ve studiu, pokud žádná PoS objednávka "
"neexistuje."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"Nemůžete zavřít místo prodeje, pokud nejsou faktury odeslány.\n"
"Faktury: %s"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "Nemůžete zavřít POS, pokud jsou objednávky stále v konceptu"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "Relace nelze vytvořit před datem uzamčení účetnictví."

#. module: point_of_sale
#: code:addons/point_of_sale/models/account_bank_statement.py:0
#, python-format
msgid "You cannot delete a bank statement linked to Point of Sale session."
msgstr "Bankovní výpis spojený s relací Místa prodeje nelze vymazat."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr ""
"Když je relace stále otevřená, nemůžete odstranit kategorii místa prodeje."

#. module: point_of_sale
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"You cannot delete a product saleable in point of sale while a session is "
"still opened."
msgstr ""
"Když je relace stále otevřená, nemůžete odstranit produkt prodávaný v místě "
"prodeje."

#. module: point_of_sale
#: code:addons/point_of_sale/models/res_partner.py:0
#, python-format
msgid ""
"You cannot delete contacts while there are active PoS sessions. Close the "
"session(s) %s first."
msgstr ""
"Když jsou aktivní relace místa prodeje, nelze kontakty smazat. Nejprve "
"relaci (relace) %s zavřete."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Chrome.js:0
#, python-format
msgid "You do not have any products"
msgstr "Nemáte žádné produkty"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr ""
"K získání kontrolních dat uzávěrky v prodejním místě nemáte přístupová "
"práva."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You don't have the access rights to set the point of sale cash box."
msgstr "K nastavení pokladny v místě prodeje nemáte přístupová práva."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr ""
"Pro %s způsob platby jste povolili možnost \"Identifikovat zákazníka\", ale "
"objednávka %s neobsahuje zákazníka."

#. module: point_of_sale
#: code:addons/point_of_sale/wizard/pos_open_statement.py:0
#, python-format
msgid ""
"You have to define which payment method must be available in the point of "
"sale by reusing existing bank and cash through \"Accounting / Configuration "
"/ Journals / Journals\". Select a journal and check the field \"PoS Payment "
"Method\" from the \"Point of Sale\" tab. You can also create new payment "
"methods directly from menu \"PoS Backend / Configuration / Payment "
"Methods\"."
msgstr ""
"Musíte určit, která platební metoda bude dostupná na prodejním místě "
"znovupoužitím banky a hotovosti prostřednictvím \"Účetnictví / Nastavení / "
"Účetní deníky / Účetní deníky\". Zvolte deník a zaškrtněte pole \"Způsob "
"platby prodejního místa\" na záložce \"Prodejní místo\". Můžete též vytvořit"
" nové platební metody přímo z menu \"Backend prodejního místa / Nastavení / "
"Způsoby platby\"."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "Řádky plateb musíte zaokrouhlit. Není zaokrouhleno."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You have to select a pricelist in the sale form !\n"
"Please set one before choosing a product."
msgstr ""
"Musíte ve formuláři prodeje zvolit ceník!\n"
"Prosím zvolte nějaký před výběrem výrobku."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You have to select a pricelist in the sale form."
msgstr "Musíte zvolit ceník ve formuláři prodeje."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr ""
"Pro vše, co prodáváte prostřednictvím rozhraní prodejního místa, musíte "
"definovat produkt."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr ""
"Pro spuštění relace musíte mít nakonfigurovanou alespoň jednu platební "
"metodu."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "Vo svojom peňažnom denníku potrebujete účet náklady a výnosy."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr ""
"Než budete moci fakturovat nebo odeslat objednávku, musíte vybrat zákazníka."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "Měli byste ke svému sezení přiřadit prodejní místo."

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr ""
"Vaše relace PoS je otevřena od %(date)s, doporučujeme vám ji zavřít a "
"vytvořit novou."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientDetailsEdit.xml:0
#: code:addons/point_of_sale/static/src/xml/Screens/ClientListScreen/ClientListScreen.xml:0
#, python-format
msgid "ZIP"
msgstr "PSČ"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "at"
msgstr "at"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "available,"
msgstr "dostupný,"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "patří k jiné relaci:"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Screens/ProductScreen/Orderline.xml:0
#, python-format
msgid "discount"
msgstr "sleva"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "např. hotovost"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Company Address, Website"
msgstr "např. adresa společnosti, webstránky"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "např. NYC Shop"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "např. Podmínky vrácení zboží, díky že nakupujete u nás."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "např. nealkoholické nápoje"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/Screens/PaymentScreen/PaymentScreen.js:0
#, python-format
msgid "for an order of"
msgstr "pro objednávku z"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ProductInfoPopup.xml:0
#, python-format
msgid "forecasted"
msgstr "předpokládané"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "in"
msgstr "v "

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Misc/MobileOrderWidget.xml:0
#, python-format
msgid "items"
msgstr "položky"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "poznámka"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "open sessions"
msgstr "otevřít relace"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/ClosePosPopup.xml:0
#, python-format
msgid "orders"
msgstr "objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/js/ChromeWidgets/CashMoveButton.js:0
#, python-format
msgid "out"
msgstr "výdej"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "paid orders"
msgstr "placené příkazy"

#. module: point_of_sale
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "návrat"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "nezaplacené objednávky"

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "nezaplacené objednávky nelze importovat"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "s použitím"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__limited_products_loading
msgid ""
"we load all starred products (favorite), all services, recent inventory movements of products, and the most recently updated products.\n"
"When the session is open, we keep on loading all remaining products in the background.\n"
"In the meantime, you can click on the 'database icon' in the searchbar to load products from database."
msgstr ""
"načteme všechny produkty označené hvězdičkou (oblíbené), všechny služby, poslední pohyby zásob produktů a nejnovější aktualizované produkty.\n"
"Když je relace otevřena, načítáme všechny zbývající produkty na pozadí.\n"
"Mezitím můžete kliknout na 'ikonu databáze' ve vyhledávacím panelu a načíst produkty z databáze."

#. module: point_of_sale
#. openerp-web
#: code:addons/point_of_sale/static/src/xml/Popups/OrderImportPopup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "byly duplikáty existujících objednávek"
