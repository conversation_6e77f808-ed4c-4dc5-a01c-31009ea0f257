<?xml version="1.0" encoding="UTF-8" ?>
<!--
    Copyright 2021 ITerra - <PERSON>
    License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl.html).
-->
<templates id="template" xml:space="preserve">
    <t t-inherit="web.NavBar.SectionsMenu" t-inherit-mode="extension" owl="1">
        <xpath
            expr="//t[@t-foreach='sections']//t[@t-set='hotkey']"
            position="attributes"
        >
            <attribute
                name="t-value"
                t-translation="off"
            >'shift+' + ((section_index + 1) % 10).toString()</attribute>
        </xpath>
        <xpath
            expr="//t[@t-if='currentAppSectionsExtra.length']//t[@t-set='hotkey']"
            position="attributes"
        >
            <attribute
                name="t-value"
                t-translation="off"
            >'shift+' + (sectionsVisibleCount + 1 % 10).toString()</attribute>
        </xpath>
    </t>
    <t t-inherit="web.Pager" t-inherit-mode="extension" owl="1">
        <xpath expr="//button[hasclass('o_pager_previous')]" position="attributes">
            <attribute
                name="t-att-accesskey"
            >props.withAccessKey ? 'z' : false</attribute>
        </xpath>
        <xpath expr="//button[hasclass('o_pager_next')]" position="attributes">
            <attribute
                name="t-att-accesskey"
            >props.withAccessKey ? 'x' : false</attribute>
        </xpath>
    </t>
    <t t-inherit="web.UserMenu.shortcutsTable" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('row')]" position="attributes">
            <attribute name="class" separator=" " add="justify-content-center" />
        </xpath>
        <xpath expr="//div[hasclass('row')]/div" position="attributes">
            <attribute name="class" />
        </xpath>
        <xpath expr="//span[text()='a']" position="replace">
            <span class="o_key">e</span>
        </xpath>
        <xpath expr="//span[text()='a']" position="replace">
            <span class="o_key">e</span>
        </xpath>
        <xpath expr="//span[text()='j']" position="replace">
            <span class="o_key">d</span>
        </xpath>
        <xpath expr="//span[text()='j']" position="replace">
            <span class="o_key">d</span>
        </xpath>
        <xpath expr="//span[text()='p']" position="replace">
            <span class="o_key">z</span>
        </xpath>
        <xpath expr="//span[text()='p']" position="replace">
            <span class="o_key">z</span>
        </xpath>
        <xpath expr="//span[text()='n']" position="replace">
            <span class="o_key">x</span>
        </xpath>
        <xpath expr="//span[text()='n']" position="replace">
            <span class="o_key">x</span>
        </xpath>
    </t>
</templates>
