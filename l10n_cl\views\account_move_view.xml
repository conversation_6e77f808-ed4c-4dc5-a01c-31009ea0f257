<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_form_inherit_l10n_cl" model="ir.ui.view">
        <field name="name">account.move.form.inherit.l10n.cl</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <form>
                <field name="l10n_latam_internal_type" invisible="1"/>
            </form>
        </field>
    </record>

    <record id="view_latam_form_inherit_l10n_cl" model="ir.ui.view">
        <field name="name">account.move.latam.form.inherit.l10n.cl</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="l10n_latam_invoice_document.view_move_form"/>
        <field name="arch" type="xml">
            <field name="l10n_latam_document_number" position="attributes">
                <attribute name="attrs">{
                    'invisible': [
                    '|',
                        ('l10n_latam_use_documents', '=', False),
                        ('l10n_latam_manual_document_number', '=', False),
                    '|', '|',
                        ('l10n_latam_use_documents', '=', False),
                        ('highest_name', '!=', False),
                        ('state', '!=', 'draft'),
                    '|', '|', '|',
                        ('l10n_latam_use_documents', '=', False),
                        ('posted_before', '=', False),
                        ('state', '!=', 'draft'),
                        ('country_code', '!=', 'CL')
                    ],
                    'readonly': [('posted_before', '=', True), ('state', '!=', 'draft')],
                    'required': ['|', ('l10n_latam_manual_document_number', '=', True), ('highest_name', '=', False)]}
                </attribute>
            </field>
        </field>
    </record>

    <record id="view_complete_invoice_refund_tree" model="ir.ui.view">
        <field name="name">account.move.tree2</field>
        <field name="model">account.move</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" default_order="create_date" string="Invoices and Refunds" decoration-muted="state == 'cancel'" js_class="account_tree">
                <field name="l10n_latam_document_type_id_code"/>
                <field name="l10n_latam_document_number" string="Folio"/>
                <field name="partner_id_vat"/>
                <field name="partner_id"/>
                <field name="invoice_date" optional="show"/>
                <field name="invoice_date_due" optional="show"/>
                <field name="date" string="Accounting Date" optional="show"/>
                <field name="payment_reference" optional="hide"/>
                <field name="invoice_user_id" optional="show" invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund','out_receipt')" string="Sales Person"/>
                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="show"/>
                <field name="invoice_origin" optional="show" string="Source Document"/>
                <field name="amount_untaxed_signed" string="Amount Untaxed" sum="Total" optional="show"/>
                <field name="amount_tax_signed" string="Tax" sum="Total" optional="show"/>
                <field name="amount_total_signed" string="Total" sum="Total" optional="show"/>
                <field name="amount_residual_signed" string="Amount Due" sum="Amount Due" optional="show"/>
                <field name="currency_id" invisible="1"/>
                <field name="company_currency_id" invisible="1"/>
                <field name="state" optional="show"/>
                <field name="payment_state" optional="hide"/>
                <field name="move_type" invisible="context.get('default_move_type', True)"/>
            </tree>
        </field>
    </record>

    <record model="ir.actions.act_window" id="sale_invoices_credit_notes">
        <field name="name">Sale Invoices and Credit Notes</field>
        <field name="view_id" ref="view_complete_invoice_refund_tree"/>
        <field name="res_model">account.move</field>
        <field name="domain">[('move_type', 'in', ['out_invoice', 'out_refund'])]</field>
        <field name="context">{'default_move_type': 'out_invoice'}</field>
        <field name="target">current</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record model="ir.actions.act_window" id="vendor_bills_and_refunds">
        <field name="name">Vendor Bills and Refunds</field>
        <field name="view_id" ref="view_complete_invoice_refund_tree"/>
        <field name="res_model">account.move</field>
        <field name="domain">[('move_type', 'in', ['in_invoice', 'in_refund'])]</field>
        <field name="context">{'default_move_type': 'in_invoice'}</field>
        <field name="target">current</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_sale_invoices_credit_notes" parent="account.menu_finance_receivables" sequence="3" action="sale_invoices_credit_notes" name="Sale Invoices and Credit Notes (CL)"/>
    <menuitem id="menu_vendor_bills_and_refunds" parent="account.menu_finance_payables" sequence="3" action="vendor_bills_and_refunds" name="Vendor Bills and Refunds (CL)"/>

</odoo>
