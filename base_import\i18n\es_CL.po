# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/es_CL/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_CL\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:535
#, python-format
msgid "(%d more)"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:309
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:89
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import
#: code:addons/base_import/models/base_import.py:596
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:618
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:176
#: code:addons/base_import/static/src/js/import_action.js:187
#: code:addons/base_import/static/src/js/import_action.js:194
#: code:addons/base_import/static/src/js/import_action.js:206
#, python-format
msgid "Comma"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_create_date
msgid "Created on"
msgstr "Creado en"

#. module: base_import
#: code:addons/base_import/models/base_import.py:145
#: code:addons/base_import/models/base_import.py:150
#, python-format
msgid "Database ID"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:55
#, python-format
msgid "Date Format:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:56
#, python-format
msgid "Datetime Format:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:58
#, python-format
msgid "Decimal Separator:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:353
#, python-format
msgid "Don't import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:195
#: code:addons/base_import/static/src/js/import_action.js:213
#, python-format
msgid "Dot"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:50
#, python-format
msgid "Encoding:"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:620
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:227
#, python-format
msgid "Error cell found while reading XLS/XLSX file: %s"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:512
#, python-format
msgid "Everything seems valid."
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:110
#: code:addons/base_import/models/base_import.py:144
#, python-format
msgid "External ID"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_file
msgid "File"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_file_name
msgid "File Name"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_file_type
msgid "File Type"
msgstr ""

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import_file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:115
#, python-format
msgid "For CSV files, the issue could be an incorrect encoding."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:559
#, python-format
msgid "Get all possible values"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:11
#, python-format
msgid "Help"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:546
#, python-format
msgid "Here are the possible values:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:116
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_id
msgid "ID"
msgstr "ID (identificación)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:73
#, python-format
msgid ""
"If the file contains\n"
"                the column names, Odoo can try auto-detecting the\n"
"                field corresponding to the column. This makes imports\n"
"                simpler especially when the file has many columns."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:60
#, python-format
msgid ""
"If the model uses openchatter, history tracking                             "
"will set up subscriptions and send notifications"
"                             during the import, but lead to a slower import."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:88
#: code:addons/base_import/static/src/xml/base_import.xml:147
#, python-format
msgid "Import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:111
#, python-format
msgid "Import a File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:114
#, python-format
msgid "Import preview failed due to:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child___last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:19
#, python-format
msgid "Load File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:56
#, python-format
msgid "Map your columns to import"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import_res_model
msgid "Model"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_name
msgid "Name"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:15
#, python-format
msgid "No file chosen..."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:418
#, python-format
msgid "Normal Fields"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:30
#, python-format
msgid "Options…"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_othervalue
msgid "Other Variable"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_parent_id
msgid "Parent"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:419
#, python-format
msgid "Relation Fields"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:24
#, python-format
msgid "Reload File"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:11
#, python-format
msgid "Select a CSV or Excel file to import."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:177
#, python-format
msgid "Semicolon"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:51
#, python-format
msgid "Separator:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:72
#, python-format
msgid "Show fields of relation fields (advanced)"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview_somevalue
msgid "Some Value"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:179
#, python-format
msgid "Space"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:178
#, python-format
msgid "Tab"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:86
#, python-format
msgid "Test Import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:52
#, python-format
msgid "Text Delimiter:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:68
#, python-format
msgid ""
"The first row\n"
"                 contains the label of the column"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:57
#, python-format
msgid "Thousands Separator:"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/xml/base_import.xml:62
#, python-format
msgid "Track history during import"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:192
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:193
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child_value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_value
msgid "Value"
msgstr ""

#. module: base_import
#: code:addons/base_import/models/base_import.py:534
#, python-format
msgid "You must configure at least one field to import"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:529
#, python-format
msgid "at row %d"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "base_import.import"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char
msgid "base_import.tests.models.char"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_noreadonly
msgid "base_import.tests.models.char.noreadonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_readonly
msgid "base_import.tests.models.char.readonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_required
msgid "base_import.tests.models.char.required"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_states
msgid "base_import.tests.models.char.states"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_stillreadonly
msgid "base_import.tests.models.char.stillreadonly"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o
msgid "base_import.tests.models.m2o"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_related
msgid "base_import.tests.models.m2o.related"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required
msgid "base_import.tests.models.m2o.required"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required_related
msgid "base_import.tests.models.m2o.required.related"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m
msgid "base_import.tests.models.o2m"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m_child
msgid "base_import.tests.models.o2m.child"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_preview
msgid "base_import.tests.models.preview"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/js/import_action.js:531
#, python-format
msgid "between rows %d and %d"
msgstr ""
