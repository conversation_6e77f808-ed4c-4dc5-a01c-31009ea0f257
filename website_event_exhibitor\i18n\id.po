# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_exhibitor
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# Febrasari <PERSON>nia <<EMAIL>>, 2022
# <PERSON><PERSON> Useful <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# whenwesober, 2022
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# arfa simon<PERSON>i, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON> Many<PERSON>, 2022\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid ""
")\n"
"                    to meet them !"
msgstr ""
")\n"
"                    untuk bertemu mereka !"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Country"
msgstr ""
"<i class=\"fa fa-folder-open\"/>\n"
"                Berdasarkan Negara"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Level"
msgstr ""
"<i class=\"fa fa-folder-open\"/>\n"
"                Berdasarkan Level"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Panah\"/>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<small class=\"badge badge-danger\">Unpublished</small>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_search_tag
msgid "<span class=\"btn border-0 py-1\">×</span>"
msgstr "<span class=\"btn border-0 py-1\">×</span>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<span class=\"h5 mb-0 pt-0 pt-md-3 pb-0 pb-md-2\">Other exhibitors</span>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""
"<span>Oops! Ruangan ini saat ini tutup</span><br/>\n"
"                    Mohon kembali lagi di antara"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""
"<span>Oops! Kamar ini penuh</span><br/>Mohon kembali lagi nanti untuk untuk "
"mengobrol dengan kami!"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "About"
msgstr "Tentang"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "Action Needed"
msgstr "Perlu Tindakan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__active
msgid "Active"
msgstr "Aktif"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "Add some exhibitors to get started !"
msgstr "Tambahkan beberapa exhibitor untuk memulai !"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr "Semua Negara"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "All Levels"
msgstr "Semua Level"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "As a team, we are happy to contribute to this event."
msgstr "Sebagai tim, kami senang karena bisa berkontribusi untuk acara ini."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Attendees will be able to join to meet"
msgstr "Peserta akan dapat mengikuti meet"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Available from"
msgstr "Tersedia dari"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "Perunggu"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr "Dapat Dipublikasikan"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Catchy marketing sentence for promote"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr "Chat Room"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "Come back between"
msgstr "Kembali lagi di antara"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Come see us live, we hope to meet you !"
msgstr "Lihat kami secara langsung, kami berharap dapat bertemu Anda !"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Connect"
msgstr "Connect"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr "Negara"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr "Bendera Negara"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr "Buat Sponsor / Exhibitor"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid "Create a Sponsor Level"
msgstr "Buat Level Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr "Deskripsi"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Discover more"
msgstr "Temukan lebih lanjut"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Display exhibitors on website"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Display in footer"
msgstr "Tampilkan di footer"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Email"
msgstr "Email"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr "Waktu tutup"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model:ir.model,name:website_event_exhibitor.model_event_event
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Event"
msgstr "Acara"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr "Sponsor Acara"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_tree
msgid "Event Sponsor Level"
msgstr "Level Sponsor Acara"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_form
msgid "Event Sponsor Levels"
msgstr "Level-Level Sponsor Acara"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Event Sponsors"
msgstr "Sponsor-Sponsor Acara"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_type
msgid "Event Template"
msgstr "Templat Acara"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__exhibitor
msgid "Exhibitor"
msgstr "Exhibitor"

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#, python-format
msgid "Exhibitors"
msgstr "Exhibitor-Exhibitor"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_type_view_form
msgid "Exhibitors Menu Item"
msgstr "Item Menu Exhibitor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr "Menu Exhibitor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_follower_ids
msgid "Followers"
msgstr "Pengikut"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Rekanan)"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "Emas"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Happy to be Sponsor"
msgstr "Bahagia untuk menjadi Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__id
msgid "ID"
msgstr "ID"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_unread
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_128
msgid "Image 128"
msgstr "Gambar 128"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_256
msgid "Image 256"
msgstr "Gambar 256"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_image_url
msgid "Image URL"
msgstr "URL Gambar"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_is_follower
msgid "Is Follower"
msgstr "Pengikut"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr "Dipublikasikan"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr "Nama Jitsi"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Join us next time to meet"
msgstr "Bergabung dengan kami lain kali untuk bertemu"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr "Bergabunglah dengan kami di sana untuk bertemu dengan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr "Bahasa"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type____last_update
msgid "Last Modified on"
msgstr "Terakhir diubah pada"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_uid
msgid "Last Updated by"
msgstr "Terakhir diperbarui oleh"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_date
msgid "Last Updated on"
msgstr "Terakhir diperbarui pada"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr "Aktivitas terakhir"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Level"
msgstr "Tingkatan"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Level:"
msgstr "Level:"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Live"
msgstr "Live"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_512
msgid "Logo"
msgstr "Logo"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_main_attachment_id
msgid "Main Attachment"
msgstr "Lampiran Utama"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr "Kapasitas maksimal"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Jumlah maksimum peserta telah dipenuhi di ruangan pada saat yang sama"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Jenis Menu"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Mobile"
msgstr "Ponsel"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "More info"
msgstr "Info lebih lanjut"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_name
msgid "Name"
msgstr "Nama"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr "Tidak ada Pita"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr "Tidak ada exhibitor yang ditemukan."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Tindakan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Jumlah pesan yang butuh tindakan"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah dari pesan dengan kesalahan pengiriman"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_unread_counter
msgid "Number of unread messages"
msgstr "Jumlah pesan yang belum dibaca"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Online"
msgstr "Daring"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__online
msgid "Online Exhibitor"
msgstr "Exhibitor Online"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr "Jam Buka"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr "Jam Buka"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr "Jumlah peserta"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Partner"
msgstr "Rekanan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr "Puncak jumlah peserta"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Phone"
msgstr "Telepon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Published"
msgstr "Terpublikasi"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid ""
"Rank your sponsors based on your own grading system (e.g. \"Gold, Silver, "
"Bronze\")."
msgstr ""
"Urutkan sponsor berdasarkan sistem penilaian Anda sendiri (contohnya \"Emas,"
" Perak, Perunggu\")."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Register"
msgstr "Daftar"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr "Style Pita"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr "Ruangan Penuh"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr "Nama Ruangan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr "Pamerkan Exhibitor"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "Perak"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr "Slogan"

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_sponsor.py:0
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__sponsor
#, python-format
msgid "Sponsor"
msgstr "Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr "Jumlah Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__email
msgid "Sponsor Email"
msgstr "Email Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__name
msgid "Sponsor Level"
msgstr "Level Sponsor"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_exhibitor.menu_event_sponsor_type
msgid "Sponsor Levels"
msgstr "Level-Level Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr "Nomor Mobile Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Sponsor Name"
msgstr "Nama Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr "Nomor Telepon Sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__exhibitor_type
msgid "Sponsor Type"
msgstr "Jenis sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__url
msgid "Sponsor Website"
msgstr "Website Sponsor"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr "Gambar sponsor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sponsor_type_id
msgid "Sponsoring Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_event_view_form
msgid "Sponsors"
msgstr "Sponsor-Sponsor"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid ""
"Sponsors are advertised on your event pages.<br>\n"
"    Exhibitors have a dedicated page a with chat room for people to connect with them."
msgstr ""
"Sponsor diiklankan pada halaman acara Anda.<br>\n"
"    Exhibitor memiliki halaman khusus dengan chat room agar orang dapat connect dengan mereka."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to meet %s !"
msgstr ""
"Acara %s dimulai pada %s (%s). \n"
"Bergabunglah dengan kami di sana untuk bertemu %s !"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr "URL lengkap untuk mengakses dokumen melalui website."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr "Zona Waktu"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_thumb_details
msgid "Unpublished"
msgstr "Belum dipublikasikan"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_unread
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Penghitung Pesan yang Belum Dibaca"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr "Terlihat pada website saat ini"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We did not find any exhibitor matching your"
msgstr "Kami tidak menemukan exhibitor apapun yang sesuai dengan"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_tree
msgid "Website"
msgstr "Website"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr "Menu Acara Website"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website Messages"
msgstr "Pesan Website"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr "URL Website"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi website"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr "Dalam jam buka"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr "misalnya Dekorasi OpenWood"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : https://www.odoo.com"
msgstr "contohnya : https://www.odoo.com"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr "contohnya : <EMAIL>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. Your best choice for your home"
msgstr "contohnya Pilihan terbaik untuk rumah Anda"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is not available right now."
msgstr "tidak tersedia saat ini."

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is over."
msgstr "sudah habis."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"sudah selesai.\n"
"                <br/>"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "minutes"
msgstr "menit"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "search."
msgstr "cari."

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts in"
msgstr "dimulai di"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts on"
msgstr "dimulai pada"
