<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="tax_report_104" model="account.tax.report">
            <field name="name">104</field>
            <field name="country_id" ref="base.ec"/>
        </record>
        <record id="tax_report_line_parent_line_report_1" model="account.tax.report.line">
            <field name="name">RESUMEN DE VENTAS Y OTRAS OPERACIONES DEL PERÍODO QUE DECLARA</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_2" model="account.tax.report.line">
            <field name="name">Ventas locales (excluye activos fijos) gravadas tarifa diferente de cero</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_401" model="account.tax.report.line">
            <field name="name">Valor bruto(401)</field>
            <field name="code">c401</field>
            <field name="tag_name">401 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_2"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_411" model="account.tax.report.line">
            <field name="name">Valor neto(411)</field>
            <field name="code">c411</field>
            <field name="tag_name">411 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_2"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_421" model="account.tax.report.line">
            <field name="name">Impuesto generado(421)</field>
            <field name="code">c421</field>
            <field name="tag_name">421 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_2"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_6" model="account.tax.report.line">
            <field name="name">Ventas de activos fijos gravadas tarifa diferente de cero</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_402" model="account.tax.report.line">
            <field name="name">Valor bruto(402)</field>
            <field name="code">c402</field>
            <field name="tag_name">402 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_6"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_412" model="account.tax.report.line">
            <field name="name">Valor neto(412)</field>
            <field name="code">c412</field>
            <field name="tag_name">412 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_6"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_422" model="account.tax.report.line">
            <field name="name">Impuesto generado(422)</field>
            <field name="code">c422</field>
            <field name="tag_name">422 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_6"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_10" model="account.tax.report.line">
            <field name="name">IVA generado en la diferencia entre ventas y notas de crédito con distinta tarifa (ajuste
                a pagar)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_423" model="account.tax.report.line">
            <field name="name">Impuesto generado(423)</field>
            <field name="code">c423</field>
            <field name="tag_name">423 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_10"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_12" model="account.tax.report.line">
            <field name="name">IVA generado en la diferencia entre ventas y notas de crédito con distinta tarifa (ajuste
                a favor)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_424" model="account.tax.report.line">
            <field name="name">Impuesto generado(424)</field>
            <field name="code">c424</field>
            <field name="tag_name">424 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_12"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_14" model="account.tax.report.line">
            <field name="name">Ventas locales (excluye activos fijos) gravadas tarifa 0% que no dan derecho a crédito
                tributario
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_403" model="account.tax.report.line">
            <field name="name">Valor bruto(403)</field>
            <field name="code">c403</field>
            <field name="tag_name">403 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_14"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_413" model="account.tax.report.line">
            <field name="name">Valor neto(413)</field>
            <field name="code">c413</field>
            <field name="tag_name">413 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_14"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_17" model="account.tax.report.line">
            <field name="name">Ventas de activos fijos gravadas tarifa 0% que no dan derecho a crédito tributario
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_404" model="account.tax.report.line">
            <field name="name">Valor bruto(404)</field>
            <field name="code">c404</field>
            <field name="tag_name">404 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_17"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_414" model="account.tax.report.line">
            <field name="name">Valor neto(414)</field>
            <field name="code">c414</field>
            <field name="tag_name">414 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_17"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_20" model="account.tax.report.line">
            <field name="name">Ventas locales (excluye activos fijos) gravadas tarifa 0% que dan derecho a crédito
                tributario
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_405" model="account.tax.report.line">
            <field name="name">Valor bruto(405)</field>
            <field name="code">c405</field>
            <field name="tag_name">405 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_20"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_415" model="account.tax.report.line">
            <field name="name">Valor neto(415)</field>
            <field name="code">c415</field>
            <field name="tag_name">415 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_20"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_23" model="account.tax.report.line">
            <field name="name">Ventas de activos fijos gravadas tarifa 0% que dan derecho a crédito tributario</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_406" model="account.tax.report.line">
            <field name="name">Valor bruto(406)</field>
            <field name="code">c406</field>
            <field name="tag_name">406 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_23"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_416" model="account.tax.report.line">
            <field name="name">Valor neto(416)</field>
            <field name="code">c416</field>
            <field name="tag_name">416 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_23"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_26" model="account.tax.report.line">
            <field name="name">Exportaciones de bienes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_407" model="account.tax.report.line">
            <field name="name">Valor bruto(407)</field>
            <field name="code">c407</field>
            <field name="tag_name">407 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_26"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_417" model="account.tax.report.line">
            <field name="name">Valor neto(417)</field>
            <field name="code">c417</field>
            <field name="tag_name">417 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_26"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_29" model="account.tax.report.line">
            <field name="name">Exportaciones de servicios y/o derechos</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_408" model="account.tax.report.line">
            <field name="name">Valor bruto(408)</field>
            <field name="code">c408</field>
            <field name="tag_name">408 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_29"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_418" model="account.tax.report.line">
            <field name="name">Valor neto(418)</field>
            <field name="code">c418</field>
            <field name="tag_name">418 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_29"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_32" model="account.tax.report.line">
            <field name="name">TOTAL VENTAS Y OTRAS OPERACIONES</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_409" model="account.tax.report.line">
            <field name="name">Valor bruto(409)</field>
            <field name="code">c409</field>
            <field name="tag_name">409 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_32"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_419" model="account.tax.report.line">
            <field name="name">Valor neto(419)</field>
            <field name="code">c419</field>
            <field name="tag_name">419 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_32"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_429" model="account.tax.report.line">
            <field name="name">Impuesto generado(429)</field>
            <field name="code">c429</field>
            <field name="tag_name">429 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_32"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_36" model="account.tax.report.line">
            <field name="name">Transferencias no objeto o exentas de IVA</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_431" model="account.tax.report.line">
            <field name="name">Valor bruto(431)</field>
            <field name="code">c431</field>
            <field name="tag_name">431 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_36"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_441" model="account.tax.report.line">
            <field name="name">Valor neto(441)</field>
            <field name="code">c441</field>
            <field name="tag_name">441 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_36"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_39" model="account.tax.report.line">
            <field name="name">Notas de crédito tarifa 0% por compensar próximo mes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_442" model="account.tax.report.line">
            <field name="name">Valor neto(442)</field>
            <field name="code">c442</field>
            <field name="tag_name">442 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_39"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_41" model="account.tax.report.line">
            <field name="name">Notas de crédito tarifa diferente de cero por compensar próximo mes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_443" model="account.tax.report.line">
            <field name="name">Valor neto(443)</field>
            <field name="code">c443</field>
            <field name="tag_name">443 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_41"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_453" model="account.tax.report.line">
            <field name="name">Impuesto generado(453)</field>
            <field name="code">c453</field>
            <field name="tag_name">453 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_41"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_44" model="account.tax.report.line">
            <field name="name">Ingresos por reembolso como intermediario / valores facturados por operadoras de
                transporte (informativo)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_1"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_434" model="account.tax.report.line">
            <field name="name">Valor bruto(434)</field>
            <field name="code">c434</field>
            <field name="tag_name">434 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_44"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_444" model="account.tax.report.line">
            <field name="name">Valor neto(444)</field>
            <field name="code">c444</field>
            <field name="tag_name">444 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_44"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_454" model="account.tax.report.line">
            <field name="name">Impuesto generado(454)</field>
            <field name="code">c454</field>
            <field name="tag_name">454 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_44"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_48" model="account.tax.report.line">
            <field name="name">LIQUIDACIÓN DEL IVA EN EL MES</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_480" model="account.tax.report.line">
            <field name="name">Total transferencias gravadas tarifa diferente de cero a contado este mes(480)</field>
            <field name="code">c480</field>
            <field name="tag_name">480 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_481" model="account.tax.report.line">
            <field name="name">Total transferencias gravadas tarifa diferente de cero a crédito este mes(481)</field>
            <field name="code">c481</field>
            <field name="tag_name">481 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_482" model="account.tax.report.line">
            <field name="name">Total impuesto generado(482)</field>
            <field name="code">c482</field>
            <field name="tag_name">482 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_483" model="account.tax.report.line">
            <field name="name">Impuesto a liquidar del mes anterior(483)</field>
            <field name="code">c483</field>
            <field name="tag_name">483 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_484" model="account.tax.report.line">
            <field name="name">Impuesto a liquidar en este mes(484)</field>
            <field name="code">c484</field>
            <field name="tag_name">484 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_495" model="account.tax.report.line">
            <field name="name">Impuesto a liquidar en el próximo mes(495)</field>
            <field name="code">c495</field>
            <field name="tag_name">495 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_499" model="account.tax.report.line">
            <field name="name">TOTAL IMPUESTO A LIQUIDAR EN ESTE MES(499)</field>
            <field name="code">c499</field>
            <field name="tag_name">499 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_48"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_56" model="account.tax.report.line">
            <field name="name">RESUMEN DE ADQUISICIONES Y PAGOS DEL PERÍODO QUE DECLARA</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_57" model="account.tax.report.line">
            <field name="name">Adquisiciones y pagos (excluye activos fijos) gravados tarifa diferente de cero (con
                derecho a crédito tributario)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_500" model="account.tax.report.line">
            <field name="name">Valor bruto(500)</field>
            <field name="code">c500</field>
            <field name="tag_name">500 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_57"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_510" model="account.tax.report.line">
            <field name="name">Valor neto(510)</field>
            <field name="code">c510</field>
            <field name="tag_name">510 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_57"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_520" model="account.tax.report.line">
            <field name="name">Impuesto generado(520)</field>
            <field name="code">c520</field>
            <field name="tag_name">520 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_57"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_61" model="account.tax.report.line">
            <field name="name">Adquisiciones locales de activos fijos gravados tarifa diferente de cero (con derecho a
                crédito tributario)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_501" model="account.tax.report.line">
            <field name="name">Valor bruto(501)</field>
            <field name="code">c501</field>
            <field name="tag_name">501 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_61"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_511" model="account.tax.report.line">
            <field name="name">Valor neto(511)</field>
            <field name="code">c511</field>
            <field name="tag_name">511 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_61"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_521" model="account.tax.report.line">
            <field name="name">Impuesto generado(521)</field>
            <field name="code">c521</field>
            <field name="tag_name">521 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_61"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_65" model="account.tax.report.line">
            <field name="name">Otras adquisiciones y pagos gravados tarifa diferente de cero (sin derecho a crédito
                tributario)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_502" model="account.tax.report.line">
            <field name="name">Valor bruto(502)</field>
            <field name="code">c502</field>
            <field name="tag_name">502 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_65"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_512" model="account.tax.report.line">
            <field name="name">Valor neto(512)</field>
            <field name="code">c512</field>
            <field name="tag_name">512 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_65"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_522" model="account.tax.report.line">
            <field name="name">Impuesto generado(522)</field>
            <field name="code">c522</field>
            <field name="tag_name">522 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_65"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_69" model="account.tax.report.line">
            <field name="name">Importaciones de servicios y/o derechos gravados tarifa diferente de cero</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_503" model="account.tax.report.line">
            <field name="name">Valor bruto(503)</field>
            <field name="code">c503</field>
            <field name="tag_name">503 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_69"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_513" model="account.tax.report.line">
            <field name="name">Valor neto(513)</field>
            <field name="code">c513</field>
            <field name="tag_name">513 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_69"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_523" model="account.tax.report.line">
            <field name="name">Impuesto generado(523)</field>
            <field name="code">c523</field>
            <field name="tag_name">523 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_69"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_73" model="account.tax.report.line">
            <field name="name">Importaciones de bienes (excluye activos fijos) gravados tarifa diferente de cero</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_504" model="account.tax.report.line">
            <field name="name">Valor bruto(504)</field>
            <field name="code">c504</field>
            <field name="tag_name">504 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_73"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_514" model="account.tax.report.line">
            <field name="name">Valor neto(514)</field>
            <field name="code">c514</field>
            <field name="tag_name">514 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_73"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_524" model="account.tax.report.line">
            <field name="name">Impuesto generado(524)</field>
            <field name="code">c524</field>
            <field name="tag_name">524 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_73"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_77" model="account.tax.report.line">
            <field name="name">Importaciones de activos fijos gravados tarifa diferente de cero</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_505" model="account.tax.report.line">
            <field name="name">Valor bruto(505)</field>
            <field name="code">c505</field>
            <field name="tag_name">505 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_77"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_515" model="account.tax.report.line">
            <field name="name">Valor neto(515)</field>
            <field name="code">c515</field>
            <field name="tag_name">515 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_77"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_525" model="account.tax.report.line">
            <field name="name">Impuesto generado(525)</field>
            <field name="code">c525</field>
            <field name="tag_name">525 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_77"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_81" model="account.tax.report.line">
            <field name="name">IVA generado en la diferencia entre adquisiciones y notas de crédito con distinta tarifa
                (ajuste en positivo al crédito tributario)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_526" model="account.tax.report.line">
            <field name="name">Impuesto generado(526)</field>
            <field name="code">c526</field>
            <field name="tag_name">526 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_81"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_83" model="account.tax.report.line">
            <field name="name">IVA generado en la diferencia entre adquisiciones y notas de crédito con distinta tarifa
                (ajuste en negativo al crédito tributario)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_527" model="account.tax.report.line">
            <field name="name">Impuesto generado(527)</field>
            <field name="code">c527</field>
            <field name="tag_name">527 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_83"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_85" model="account.tax.report.line">
            <field name="name">Importaciones de bienes (incluye activos fijos) gravados tarifa 0%</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_506" model="account.tax.report.line">
            <field name="name">Valor bruto(506)</field>
            <field name="code">c506</field>
            <field name="tag_name">506 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_85"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_516" model="account.tax.report.line">
            <field name="name">Valor neto(516)</field>
            <field name="code">c516</field>
            <field name="tag_name">516 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_85"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_88" model="account.tax.report.line">
            <field name="name">Adquisiciones y pagos (incluye activos fijos) gravados tarifa 0%</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_507" model="account.tax.report.line">
            <field name="name">Valor bruto(507)</field>
            <field name="code">c507</field>
            <field name="tag_name">507 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_88"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_517" model="account.tax.report.line">
            <field name="name">Valor neto(517)</field>
            <field name="code">c517</field>
            <field name="tag_name">517 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_88"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_91" model="account.tax.report.line">
            <field name="name">Adquisiciones realizadas a contribuyentes RISE</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_508" model="account.tax.report.line">
            <field name="name">Valor bruto(508)</field>
            <field name="code">c508</field>
            <field name="tag_name">508 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_91"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_518" model="account.tax.report.line">
            <field name="name">Valor neto(518)</field>
            <field name="code">c518</field>
            <field name="tag_name">518 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_91"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_94" model="account.tax.report.line">
            <field name="name">TOTAL ADQUISICIONES Y PAGOS</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_509" model="account.tax.report.line">
            <field name="name">Valor bruto(509)</field>
            <field name="code">c509</field>
            <field name="tag_name">509 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_94"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_519" model="account.tax.report.line">
            <field name="name">Valor neto(519)</field>
            <field name="code">c519</field>
            <field name="tag_name">519 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_94"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_529" model="account.tax.report.line">
            <field name="name">Impuesto generado(529)</field>
            <field name="code">c529</field>
            <field name="tag_name">529 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_94"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_98" model="account.tax.report.line">
            <field name="name">Adquisiciones no objeto de IVA</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_531" model="account.tax.report.line">
            <field name="name">Valor bruto(531)</field>
            <field name="code">c531</field>
            <field name="tag_name">531 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_98"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_541" model="account.tax.report.line">
            <field name="name">Valor neto(541)</field>
            <field name="code">c541</field>
            <field name="tag_name">541 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_98"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_101" model="account.tax.report.line">
            <field name="name">Adquisiciones exentas del pago de IVA</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_532" model="account.tax.report.line">
            <field name="name">Valor bruto(532)</field>
            <field name="code">c532</field>
            <field name="tag_name">532 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_101"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_542" model="account.tax.report.line">
            <field name="name">Valor neto(542)</field>
            <field name="code">c542</field>
            <field name="tag_name">542 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_101"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_104" model="account.tax.report.line">
            <field name="name">Notas de crédito tarifa 0% por compensar próximo mes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_543" model="account.tax.report.line">
            <field name="name">Valor neto(543)</field>
            <field name="code">c543</field>
            <field name="tag_name">543 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_104"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_106" model="account.tax.report.line">
            <field name="name">Notas de crédito tarifa diferente de cero por compensar próximo mes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_544" model="account.tax.report.line">
            <field name="name">Valor neto(544)</field>
            <field name="code">c544</field>
            <field name="tag_name">544 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_106"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_554" model="account.tax.report.line">
            <field name="name">Impuesto generado(554)</field>
            <field name="code">c554</field>
            <field name="tag_name">554 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_106"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_109" model="account.tax.report.line">
            <field name="name">Pagos netos por reembolso como intermediario / valores facturados por socios a operadoras
                de transporte (informativo)
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_56"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_535" model="account.tax.report.line">
            <field name="name">Valor bruto(535)</field>
            <field name="code">c535</field>
            <field name="tag_name">535 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_109"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_545" model="account.tax.report.line">
            <field name="name">Valor neto(545)</field>
            <field name="code">c545</field>
            <field name="tag_name">545 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_109"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_555" model="account.tax.report.line">
            <field name="name">Impuesto generado(555)</field>
            <field name="code">c555</field>
            <field name="tag_name">555 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_109"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_113" model="account.tax.report.line">
            <field name="name">RESUMEN IMPOSITIVO: AGENTE DE PERCEPCIÓN DEL IMPUESTO AL VALOR AGREGADO</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_601" model="account.tax.report.line">
            <field name="name">Impuesto causado (si la diferencia de los campos 499-564 es mayor que cero)(601)</field>
            <field name="code">c601</field>
            <field name="tag_name">601 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_602" model="account.tax.report.line">
            <field name="name">Crédito tributario aplicable en este período (si la diferencia de los campos 499-564 es
                menor que cero)(602)
            </field>
            <field name="code">c602</field>
            <field name="tag_name">602 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_604" model="account.tax.report.line">
            <field name="name">(-) Compensación de IVA por ventas efectuadas en zonas afectadas - Ley de solidaridad,
                restitución de crédito tributario en resoluciones(604)
            </field>
            <field name="code">c604</field>
            <field name="tag_name">604 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_117" model="account.tax.report.line">
            <field name="name">(-) Saldo crédito tributario del mes anterior</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_605" model="account.tax.report.line">
            <field name="name">Por adquisiciones e importaciones (trasládese el campo 615 de la declaración del período
                anterior)(605)
            </field>
            <field name="code">c605</field>
            <field name="tag_name">605 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_117"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_606" model="account.tax.report.line">
            <field name="name">Por retenciones en la fuente de IVA que le han sido efectuadas (trasládese el campo 617
                de la declaración del período anterior)(606)
            </field>
            <field name="code">c606</field>
            <field name="tag_name">606 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_117"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_608" model="account.tax.report.line">
            <field name="name">Por compensación de IVA por ventas efectuadas en zonas afectadas - Ley de solidaridad
                (trasládese el campo 619 de la declaración del período anterior)(608)
            </field>
            <field name="code">c608</field>
            <field name="tag_name">608 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_117"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_609" model="account.tax.report.line">
            <field name="name">(-) Retenciones en la fuente de IVA que le han sido efectuadas en este período(609)
            </field>
            <field name="code">c609</field>
            <field name="tag_name">609 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_610" model="account.tax.report.line">
            <field name="name">(+) Ajuste por IVA devuelto o descontado por adquisiciones efectuadas con medio
                electrónico(610)
            </field>
            <field name="code">c610</field>
            <field name="tag_name">610 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_612" model="account.tax.report.line">
            <field name="name">(+) Ajuste por IVA devuelto e IVA rechazado (por concepto de devoluciones de IVA), ajuste
                de IVA por procesos de control y otros (adquisiciones en importaciones), imputables al crédito
                tributario(612)
            </field>
            <field name="code">c612</field>
            <field name="tag_name">612 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_613" model="account.tax.report.line">
            <field name="name">(+) Ajuste por IVA devuelto e IVA rechazado, ajuste de IVA por procesos de control y
                otros (por concepto retenciones en la fuente de IVA), imputables al crédito tributario(613)
            </field>
            <field name="code">c613</field>
            <field name="tag_name">613 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_614" model="account.tax.report.line">
            <field name="name">(+) Ajuste por IVA devuelto por otras instituciones del sector público imputable al
                crédito tributario en el mes(614)
            </field>
            <field name="code">c614</field>
            <field name="tag_name">614 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_126" model="account.tax.report.line">
            <field name="name">Saldo crédito tributario para el próximo mes</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_615" model="account.tax.report.line">
            <field name="name">Por adquisiciones e Importaciones(615)</field>
            <field name="code">c615</field>
            <field name="tag_name">615 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_126"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_617" model="account.tax.report.line">
            <field name="name">Por retenciones en la fuente de IVA que le han sido efectuadas(617)</field>
            <field name="code">c617</field>
            <field name="tag_name">617 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_126"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_619" model="account.tax.report.line">
            <field name="name">Por compensación de IVA por ventas efectuadas en zonas afectadas - Ley de solidaridad,
                restitución de crédito tributario en resoluciones(619)
            </field>
            <field name="code">c619</field>
            <field name="tag_name">619 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_126"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_620" model="account.tax.report.line">
            <field name="name">SUBTOTAL A PAGAR Si (***********-***********-***********+610+611+612+613+614) > 0(620)
            </field>
            <field name="code">c620</field>
            <field name="tag_name">620 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_621" model="account.tax.report.line">
            <field name="name">IVA presuntivo de salas de juego (bingo mecánicos) y otros juegos de azar (aplica para
                ejercicios anteriores al 2013)(621)
            </field>
            <field name="code">c621</field>
            <field name="tag_name">621 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_699" model="account.tax.report.line">
            <field name="name">TOTAL IMPUESTO A PAGAR POR PERCEPCIÓN(699)</field>
            <field name="code">c699</field>
            <field name="tag_name">699 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_113"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_133" model="account.tax.report.line">
            <field name="name">IMPUESTO A LA SALIDA DE DIVISAS A EFECTOS DE DEVOLUCIÓN A EXPORTADORES HABITUALES DE
                BIENES
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_134" model="account.tax.report.line">
            <field name="name">Importaciones liquidadas de materias primas, insumos y bienes de capital que sean
                incorporadas en procesos productivos de bienes que se exporten
            </field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_133"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_700" model="account.tax.report.line">
            <field name="name">Valor(700)</field>
            <field name="code">c700</field>
            <field name="tag_name">700 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_134"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_701" model="account.tax.report.line">
            <field name="name">ISD pagado(701)</field>
            <field name="code">c701</field>
            <field name="tag_name">701 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_134"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_137" model="account.tax.report.line">
            <field name="name">AGENTE DE RETENCIÓN DEL IMPUESTO AL VALOR AGREGADO</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_721" model="account.tax.report.line">
            <field name="name">Retención del 10%(721)</field>
            <field name="code">c721</field>
            <field name="tag_name">721 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_723" model="account.tax.report.line">
            <field name="name">Retención del 20%(723)</field>
            <field name="code">c723</field>
            <field name="tag_name">723 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_725" model="account.tax.report.line">
            <field name="name">Retención del 30%(725)</field>
            <field name="code">c725</field>
            <field name="tag_name">725 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_727" model="account.tax.report.line">
            <field name="name">Retención del 50%(727)</field>
            <field name="code">c727</field>
            <field name="tag_name">727 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_729" model="account.tax.report.line">
            <field name="name">Retención del 70%(729)</field>
            <field name="code">c729</field>
            <field name="tag_name">729 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_104_731" model="account.tax.report.line">
            <field name="name">Retención del 100%(731)</field>
            <field name="code">c731</field>
            <field name="tag_name">731 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_144" model="account.tax.report.line">
            <field name="name">TOTAL IMPUESTO RETENIDO</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_799" model="account.tax.report.line">
            <field name="name">(799)</field>
            <field name="code">c799</field>
            <field name="tag_name">799 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_144"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_146" model="account.tax.report.line">
            <field name="name">Devolución provisional de IVA mediante compensación con retenciones efectuadas</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_800" model="account.tax.report.line">
            <field name="name">(800)</field>
            <field name="code">c800</field>
            <field name="tag_name">800 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_146"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_148" model="account.tax.report.line">
            <field name="name">TOTAL IMPUESTO A PAGAR POR RETENCIÓN</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_137"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_104_801" model="account.tax.report.line">
            <field name="name">(801)</field>
            <field name="code">c801</field>
            <field name="tag_name">801 (Reporte 104)</field>
            <field name="report_id" ref="tax_report_104"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_148"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_103" model="account.tax.report">
            <field name="name">103</field>
            <field name="country_id" ref="base.ec"/>
        </record>
        <record id="tax_report_line_parent_line_report_150" model="account.tax.report.line">
            <field name="name">POR PAGOS EFECTUADOS A RESIDENTES Y ESTABLECIMIENTOS PERMANENTES</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_151" model="account.tax.report.line">
            <field name="name">En relación de dependencia que supera o no la base desgravada</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_302" model="account.tax.report.line">
            <field name="name">Base imponible(302)</field>
            <field name="code">c302</field>
            <field name="tag_name">302 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_352" model="account.tax.report.line">
            <field name="name">Valor retenido(352)</field>
            <field name="code">c352</field>
            <field name="tag_name">352 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_154" model="account.tax.report.line">
            <field name="name">Servicios</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_155" model="account.tax.report.line">
            <field name="name">Honorarios profesionales</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_303" model="account.tax.report.line">
            <field name="name">Base imponible(303)</field>
            <field name="code">c303</field>
            <field name="tag_name">303 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_155"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_353" model="account.tax.report.line">
            <field name="name">Valor retenido(353)</field>
            <field name="code">c353</field>
            <field name="tag_name">353 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_155"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_158" model="account.tax.report.line">
            <field name="name">Predomina el intelecto</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_304" model="account.tax.report.line">
            <field name="name">Base imponible(304)</field>
            <field name="code">c304</field>
            <field name="tag_name">304 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_158"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_354" model="account.tax.report.line">
            <field name="name">Valor retenido(354)</field>
            <field name="code">c354</field>
            <field name="tag_name">354 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_158"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_161" model="account.tax.report.line">
            <field name="name">Predomina la mano de obra</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_307" model="account.tax.report.line">
            <field name="name">Base imponible(307)</field>
            <field name="code">c307</field>
            <field name="tag_name">307 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_161"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_357" model="account.tax.report.line">
            <field name="name">Valor retenido(357)</field>
            <field name="code">c357</field>
            <field name="tag_name">357 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_161"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_164" model="account.tax.report.line">
            <field name="name">Utilización o aprovechamiento de la imagen o renombre</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_308" model="account.tax.report.line">
            <field name="name">Base imponible(308)</field>
            <field name="code">c308</field>
            <field name="tag_name">308 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_164"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_358" model="account.tax.report.line">
            <field name="name">Valor retenido(358)</field>
            <field name="code">c358</field>
            <field name="tag_name">358 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_164"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_167" model="account.tax.report.line">
            <field name="name">Publicidad y comunicación</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_309" model="account.tax.report.line">
            <field name="name">Base imponible(309)</field>
            <field name="code">c309</field>
            <field name="tag_name">309 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_167"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_359" model="account.tax.report.line">
            <field name="name">Valor retenido(359)</field>
            <field name="code">c359</field>
            <field name="tag_name">359 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_167"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_170" model="account.tax.report.line">
            <field name="name">Transporte privado de pasajeros o servicio público o privado de carga</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_154"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_310" model="account.tax.report.line">
            <field name="name">Base imponible(310)</field>
            <field name="code">c310</field>
            <field name="tag_name">310 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_170"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_360" model="account.tax.report.line">
            <field name="name">Valor retenido(360)</field>
            <field name="code">c360</field>
            <field name="tag_name">360 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_170"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_173" model="account.tax.report.line">
            <field name="name">A través de liquidaciones de compra (nivel cultural o rusticidad)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_311" model="account.tax.report.line">
            <field name="name">Base imponible(311)</field>
            <field name="code">c311</field>
            <field name="tag_name">311 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_173"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_361" model="account.tax.report.line">
            <field name="name">Valor retenido(361)</field>
            <field name="code">c361</field>
            <field name="tag_name">361 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_173"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_176" model="account.tax.report.line">
            <field name="name">Transferencia de bienes muebles de naturaleza corporal</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_312" model="account.tax.report.line">
            <field name="name">Base imponible(312)</field>
            <field name="code">c312</field>
            <field name="tag_name">312 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_176"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_362" model="account.tax.report.line">
            <field name="name">Valor retenido(362)</field>
            <field name="code">c362</field>
            <field name="tag_name">362 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_176"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_179" model="account.tax.report.line">
            <field name="name">Compra de bienes de origen agrícola, avícola, pecuario, apícola, cunícula, bioacuático,
                forestal y carnes en estado natural
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_3210" model="account.tax.report.line">
            <field name="name">Base imponible(3210)</field>
            <field name="code">c3210</field>
            <field name="tag_name">3210 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_179"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_3620" model="account.tax.report.line">
            <field name="name">Valor retenido(3620)</field>
            <field name="code">c3620</field>
            <field name="tag_name">3620 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_179"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_182" model="account.tax.report.line">
            <field name="name">Por regalías, derechos de autor, marcas, patentes y similares</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_314" model="account.tax.report.line">
            <field name="name">Base imponible(314)</field>
            <field name="code">c314</field>
            <field name="tag_name">314 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_182"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_364" model="account.tax.report.line">
            <field name="name">Valor retenido(364)</field>
            <field name="code">c364</field>
            <field name="tag_name">364 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_182"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_185" model="account.tax.report.line">
            <field name="name">Arrendamiento</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_186" model="account.tax.report.line">
            <field name="name">Mercantil</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_185"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_319" model="account.tax.report.line">
            <field name="name">Base imponible(319)</field>
            <field name="code">c319</field>
            <field name="tag_name">319 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_186"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_369" model="account.tax.report.line">
            <field name="name">Valor retenido(369)</field>
            <field name="code">c369</field>
            <field name="tag_name">369 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_186"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_189" model="account.tax.report.line">
            <field name="name">Bienes inmuebles</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_185"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_320" model="account.tax.report.line">
            <field name="name">Base imponible(320)</field>
            <field name="code">c320</field>
            <field name="tag_name">320 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_189"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_370" model="account.tax.report.line">
            <field name="name">Valor retenido(370)</field>
            <field name="code">c370</field>
            <field name="tag_name">370 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_189"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_192" model="account.tax.report.line">
            <field name="name">Seguros y reaseguros (primas y cesiones)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_322" model="account.tax.report.line">
            <field name="name">Base imponible(322)</field>
            <field name="code">c322</field>
            <field name="tag_name">322 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_192"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_372" model="account.tax.report.line">
            <field name="name">Valor retenido(372)</field>
            <field name="code">c372</field>
            <field name="tag_name">372 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_192"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_195" model="account.tax.report.line">
            <field name="name">Rendimientos financieros</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_323" model="account.tax.report.line">
            <field name="name">Base imponible(323)</field>
            <field name="code">c323</field>
            <field name="tag_name">323 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_195"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_373" model="account.tax.report.line">
            <field name="name">Valor retenido(373)</field>
            <field name="code">c373</field>
            <field name="tag_name">373 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_195"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_198" model="account.tax.report.line">
            <field name="name">Rendimientos financieros entre instituciones del sistema financiero y entidades economía
                popular y solidaria
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_324" model="account.tax.report.line">
            <field name="name">Base imponible(324)</field>
            <field name="code">c324</field>
            <field name="tag_name">324 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_198"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_374" model="account.tax.report.line">
            <field name="name">Valor retenido(374)</field>
            <field name="code">c374</field>
            <field name="tag_name">374 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_198"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_201" model="account.tax.report.line">
            <field name="name">Anticipo dividendos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_325" model="account.tax.report.line">
            <field name="name">Base imponible(325)</field>
            <field name="code">c325</field>
            <field name="tag_name">325 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_201"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_375" model="account.tax.report.line">
            <field name="name">Valor retenido(375)</field>
            <field name="code">c375</field>
            <field name="tag_name">375 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_201"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_204" model="account.tax.report.line">
            <field name="name">Dividendos distribuidos que correspondan al impuesto a la renta único establecido en el
                art. 27 de la LRTI
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_326" model="account.tax.report.line">
            <field name="name">Base imponible(326)</field>
            <field name="code">c326</field>
            <field name="tag_name">326 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_204"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_376" model="account.tax.report.line">
            <field name="name">Valor retenido(376)</field>
            <field name="code">c376</field>
            <field name="tag_name">376 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_204"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_207" model="account.tax.report.line">
            <field name="name">Dividendos distribuidos a personas naturales residentes</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_327" model="account.tax.report.line">
            <field name="name">Base imponible(327)</field>
            <field name="code">c327</field>
            <field name="tag_name">327 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_207"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_377" model="account.tax.report.line">
            <field name="name">Valor retenido(377)</field>
            <field name="code">c377</field>
            <field name="tag_name">377 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_207"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_210" model="account.tax.report.line">
            <field name="name">Dividendos distribuidos a sociedades residentes</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_328" model="account.tax.report.line">
            <field name="name">Base imponible(328)</field>
            <field name="code">c328</field>
            <field name="tag_name">328 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_210"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_378" model="account.tax.report.line">
            <field name="name">Valor retenido(378)</field>
            <field name="code">c378</field>
            <field name="tag_name">378 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_210"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_213" model="account.tax.report.line">
            <field name="name">Dividendos distribuidos a fideicomisos residentes</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_329" model="account.tax.report.line">
            <field name="name">Base imponible(329)</field>
            <field name="code">c329</field>
            <field name="tag_name">329 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_213"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_379" model="account.tax.report.line">
            <field name="name">Valor retenido(379)</field>
            <field name="code">c379</field>
            <field name="tag_name">379 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_213"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_216" model="account.tax.report.line">
            <field name="name">Dividendos en acciones (capitalización de utilidades)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_331" model="account.tax.report.line">
            <field name="name">Base imponible(331)</field>
            <field name="code">c331</field>
            <field name="tag_name">331 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_216"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_218" model="account.tax.report.line">
            <field name="name">Pagos de bienes y servicios no sujetos a retención</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_332" model="account.tax.report.line">
            <field name="name">Base imponible(332)</field>
            <field name="code">c332</field>
            <field name="tag_name">332 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_218"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_220" model="account.tax.report.line">
            <field name="name">Enajenación de derechos representativos de capital y otros derechos cotizados en bolsa
                ecuatoriana
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_333" model="account.tax.report.line">
            <field name="name">Base imponible(333)</field>
            <field name="code">c333</field>
            <field name="tag_name">333 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_220"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_383" model="account.tax.report.line">
            <field name="name">Valor retenido(383)</field>
            <field name="code">c383</field>
            <field name="tag_name">383 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_220"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_223" model="account.tax.report.line">
            <field name="name">Enajenación de derechos representativos de capital y otros derechos no cotizados en bolsa
                ecuatoriana
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_334" model="account.tax.report.line">
            <field name="name">Base imponible(334)</field>
            <field name="code">c334</field>
            <field name="tag_name">334 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_223"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_384" model="account.tax.report.line">
            <field name="name">Valor retenido(384)</field>
            <field name="code">c384</field>
            <field name="tag_name">384 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_223"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_226" model="account.tax.report.line">
            <field name="name">Loterías, rifas, apuestas y similares</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_151"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_335" model="account.tax.report.line">
            <field name="name">Base imponible(335)</field>
            <field name="code">c335</field>
            <field name="tag_name">335 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_226"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_385" model="account.tax.report.line">
            <field name="name">Valor retenido(385)</field>
            <field name="code">c385</field>
            <field name="tag_name">385 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_226"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_229" model="account.tax.report.line">
            <field name="name">Venta de combustibles</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_230" model="account.tax.report.line">
            <field name="name">A comercializadoras</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_229"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_336" model="account.tax.report.line">
            <field name="name">Base imponible(336)</field>
            <field name="code">c336</field>
            <field name="tag_name">336 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_230"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_386" model="account.tax.report.line">
            <field name="name">Valor retenido(386)</field>
            <field name="code">c386</field>
            <field name="tag_name">386 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_230"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_233" model="account.tax.report.line">
            <field name="name">A distribuidores</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_229"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_337" model="account.tax.report.line">
            <field name="name">Base imponible(337)</field>
            <field name="code">c337</field>
            <field name="tag_name">337 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_233"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_387" model="account.tax.report.line">
            <field name="name">Valor retenido(387)</field>
            <field name="code">c387</field>
            <field name="tag_name">387 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_233"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_236" model="account.tax.report.line">
            <field name="name">Producción y venta local de banano producido o no por el mismo sujeto pasivo</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_3380" model="account.tax.report.line">
            <field name="name">Base imponible(3380)</field>
            <field name="code">c3380</field>
            <field name="tag_name">3380 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_236"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_3880" model="account.tax.report.line">
            <field name="name">Valor retenido(3880)</field>
            <field name="code">c3880</field>
            <field name="tag_name">3880 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_236"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_239" model="account.tax.report.line">
            <field name="name">Impuesto único a la exportación de banano</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_3400" model="account.tax.report.line">
            <field name="name">Base imponible(3400)</field>
            <field name="code">c3400</field>
            <field name="tag_name">3400 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_239"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_3900" model="account.tax.report.line">
            <field name="name">Valor retenido(3900)</field>
            <field name="code">c3900</field>
            <field name="tag_name">3900 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_239"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_242" model="account.tax.report.line">
            <field name="name">Otras retenciones</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_243" model="account.tax.report.line">
            <field name="name">Aplicables el 1%</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_242"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_343" model="account.tax.report.line">
            <field name="name">Base imponible(343)</field>
            <field name="code">c343</field>
            <field name="tag_name">343 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_243"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_393" model="account.tax.report.line">
            <field name="name">Valor retenido(393)</field>
            <field name="code">c393</field>
            <field name="tag_name">393 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_243"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_246" model="account.tax.report.line">
            <field name="name">Aplicables el 2%</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_242"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_344" model="account.tax.report.line">
            <field name="name">Base imponible(344)</field>
            <field name="code">c344</field>
            <field name="tag_name">344 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_246"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_394" model="account.tax.report.line">
            <field name="name">Valor retenido(394)</field>
            <field name="code">c394</field>
            <field name="tag_name">394 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_246"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_249" model="account.tax.report.line">
            <field name="name">Aplicables el 2,75%</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_242"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_3440" model="account.tax.report.line">
            <field name="name">Base imponible(3440)</field>
            <field name="code">c3440</field>
            <field name="tag_name">3440 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_249"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_3940" model="account.tax.report.line">
            <field name="name">Valor retenido(3940)</field>
            <field name="code">c3940</field>
            <field name="tag_name">3940 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_249"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_252" model="account.tax.report.line">
            <field name="name">Aplicables el 8%</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_242"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_345" model="account.tax.report.line">
            <field name="name">Base imponible(345)</field>
            <field name="code">c345</field>
            <field name="tag_name">345 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_252"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_395" model="account.tax.report.line">
            <field name="name">Valor retenido(395)</field>
            <field name="code">c395</field>
            <field name="tag_name">395 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_252"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_255" model="account.tax.report.line">
            <field name="name">Aplicables a otros porcentajes (incluye régimen Microempresarial)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_242"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_346" model="account.tax.report.line">
            <field name="name">Base imponible(346)</field>
            <field name="code">c346</field>
            <field name="tag_name">346 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_255"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_396" model="account.tax.report.line">
            <field name="name">Valor retenido(396)</field>
            <field name="code">c396</field>
            <field name="tag_name">396 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_255"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_258" model="account.tax.report.line">
            <field name="name">Impuesto único a ingresos provenientes de actividades agropecuarias en etapa de
                producción / comercialización local o exportación
            </field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_348" model="account.tax.report.line">
            <field name="name">Base imponible(348)</field>
            <field name="code">c348</field>
            <field name="tag_name">348 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_258"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_398" model="account.tax.report.line">
            <field name="name">Valor retenido(398)</field>
            <field name="code">c398</field>
            <field name="tag_name">398 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_258"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_261" model="account.tax.report.line">
            <field name="name">Otras autoretenciones</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_350" model="account.tax.report.line">
            <field name="name">Base imponible(350)</field>
            <field name="code">c350</field>
            <field name="tag_name">350 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_261"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_400" model="account.tax.report.line">
            <field name="name">Valor retenido(400)</field>
            <field name="code">c400</field>
            <field name="tag_name">400 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_261"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_264" model="account.tax.report.line">
            <field name="name">SUBTOTAL OPERACIONES EFECTUADAS EN EL PAÍS</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_150"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_349" model="account.tax.report.line">
            <field name="name">Base imponible(349)</field>
            <field name="code">c349</field>
            <field name="tag_name">349 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_264"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_399" model="account.tax.report.line">
            <field name="name">Valor retenido(399)</field>
            <field name="code">c399</field>
            <field name="tag_name">399 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_264"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_267" model="account.tax.report.line">
            <field name="name">POR PAGOS A NO RESIDENTES</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_268" model="account.tax.report.line">
            <field name="name">Con convenio de doble tributación</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_267"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_269" model="account.tax.report.line">
            <field name="name">Intereses por financiamiento de proveedores</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_402" model="account.tax.report.line">
            <field name="name">Base imponible(402)</field>
            <field name="code">c402</field>
            <field name="tag_name">402 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_269"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_452" model="account.tax.report.line">
            <field name="name">Valor retenido(452)</field>
            <field name="code">c452</field>
            <field name="tag_name">452 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_269"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_272" model="account.tax.report.line">
            <field name="name">Intereses de créditos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_403" model="account.tax.report.line">
            <field name="name">Base imponible(403)</field>
            <field name="code">c403</field>
            <field name="tag_name">403 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_272"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_453" model="account.tax.report.line">
            <field name="name">Valor retenido(453)</field>
            <field name="code">c453</field>
            <field name="tag_name">453 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_272"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_275" model="account.tax.report.line">
            <field name="name">Anticipo de dividendos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_404" model="account.tax.report.line">
            <field name="name">Base imponible(404)</field>
            <field name="code">c404</field>
            <field name="tag_name">404 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_275"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_454" model="account.tax.report.line">
            <field name="name">Valor retenido(454)</field>
            <field name="code">c454</field>
            <field name="tag_name">454 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_275"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_278" model="account.tax.report.line">
            <field name="name">Dividendos sin beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4050" model="account.tax.report.line">
            <field name="name">Base imponible(4050)</field>
            <field name="code">c4050</field>
            <field name="tag_name">4050 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_278"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4550" model="account.tax.report.line">
            <field name="name">Valor retenido(4550)</field>
            <field name="code">c4550</field>
            <field name="tag_name">4550 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_278"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_281" model="account.tax.report.line">
            <field name="name">Dividendos con beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4060" model="account.tax.report.line">
            <field name="name">Base imponible(4060)</field>
            <field name="code">c4060</field>
            <field name="tag_name">4060 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_281"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4560" model="account.tax.report.line">
            <field name="name">Valor retenido(4560)</field>
            <field name="code">c4560</field>
            <field name="tag_name">4560 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_281"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_284" model="account.tax.report.line">
            <field name="name">Dividendos incumpliendo el deber de informar la composición societaria</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4070" model="account.tax.report.line">
            <field name="name">Base imponible(4070)</field>
            <field name="code">c4070</field>
            <field name="tag_name">4070 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_284"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4570" model="account.tax.report.line">
            <field name="name">Valor retenido(4570)</field>
            <field name="code">c4570</field>
            <field name="tag_name">4570 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_284"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_287" model="account.tax.report.line">
            <field name="name">Enajenación de derechos representativos de capital y otros derechos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_408" model="account.tax.report.line">
            <field name="name">Base imponible(408)</field>
            <field name="code">c408</field>
            <field name="tag_name">408 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_287"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_458" model="account.tax.report.line">
            <field name="name">Valor retenido(458)</field>
            <field name="code">c458</field>
            <field name="tag_name">458 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_287"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_290" model="account.tax.report.line">
            <field name="name">Seguros y reaseguros (primas y cesiones)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_409" model="account.tax.report.line">
            <field name="name">Base imponible(409)</field>
            <field name="code">c409</field>
            <field name="tag_name">409 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_290"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_459" model="account.tax.report.line">
            <field name="name">Valor retenido(459)</field>
            <field name="code">c459</field>
            <field name="tag_name">459 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_290"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_293" model="account.tax.report.line">
            <field name="name">Servicios técnicos, administrativos o de consultoría y regalías</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_410" model="account.tax.report.line">
            <field name="name">Base imponible(410)</field>
            <field name="code">c410</field>
            <field name="tag_name">410 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_293"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_460" model="account.tax.report.line">
            <field name="name">Valor retenido(460)</field>
            <field name="code">c460</field>
            <field name="tag_name">460 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_293"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_296" model="account.tax.report.line">
            <field name="name">Otros conceptos de ingresos gravados</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_411" model="account.tax.report.line">
            <field name="name">Base imponible(411)</field>
            <field name="code">c411</field>
            <field name="tag_name">411 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_296"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_461" model="account.tax.report.line">
            <field name="name">Valor retenido(461)</field>
            <field name="code">c461</field>
            <field name="tag_name">461 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_296"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_299" model="account.tax.report.line">
            <field name="name">Otros pagos al exterior no sujetos a retención</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_268"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_412" model="account.tax.report.line">
            <field name="name">Base imponible(412)</field>
            <field name="code">c412</field>
            <field name="tag_name">412 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_299"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_301" model="account.tax.report.line">
            <field name="name">Sin convenio de doble tributación</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_267"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_302" model="account.tax.report.line">
            <field name="name">Intereses por financiamiento de proveedores</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_413" model="account.tax.report.line">
            <field name="name">Base imponible(413)</field>
            <field name="code">c413</field>
            <field name="tag_name">413 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_302"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_463" model="account.tax.report.line">
            <field name="name">Valor retenido(463)</field>
            <field name="code">c463</field>
            <field name="tag_name">463 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_302"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_305" model="account.tax.report.line">
            <field name="name">Intereses de créditos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_414" model="account.tax.report.line">
            <field name="name">Base imponible(414)</field>
            <field name="code">c414</field>
            <field name="tag_name">414 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_305"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_464" model="account.tax.report.line">
            <field name="name">Valor retenido(464)</field>
            <field name="code">c464</field>
            <field name="tag_name">464 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_305"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_308" model="account.tax.report.line">
            <field name="name">Anticipo de dividendos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_415" model="account.tax.report.line">
            <field name="name">Base imponible(415)</field>
            <field name="code">c415</field>
            <field name="tag_name">415 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_308"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_465" model="account.tax.report.line">
            <field name="name">Valor retenido(465)</field>
            <field name="code">c465</field>
            <field name="tag_name">465 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_308"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_311" model="account.tax.report.line">
            <field name="name">Dividendos sin beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4160" model="account.tax.report.line">
            <field name="name">Base imponible(4160)</field>
            <field name="code">c4160</field>
            <field name="tag_name">4160 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_311"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4660" model="account.tax.report.line">
            <field name="name">Valor retenido(4660)</field>
            <field name="code">c4660</field>
            <field name="tag_name">4660 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_311"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_314" model="account.tax.report.line">
            <field name="name">Dividendos con beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4170" model="account.tax.report.line">
            <field name="name">Base imponible(4170)</field>
            <field name="code">c4170</field>
            <field name="tag_name">4170 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_314"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4670" model="account.tax.report.line">
            <field name="name">Valor retenido(4670)</field>
            <field name="code">c4670</field>
            <field name="tag_name">4670 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_314"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_317" model="account.tax.report.line">
            <field name="name">Dividendos incumpliendo el deber de informar la composición societaria</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4180" model="account.tax.report.line">
            <field name="name">Base imponible(4180)</field>
            <field name="code">c4180</field>
            <field name="tag_name">4180 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_317"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4680" model="account.tax.report.line">
            <field name="name">Valor retenido(4680)</field>
            <field name="code">c4680</field>
            <field name="tag_name">4680 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_317"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_320" model="account.tax.report.line">
            <field name="name">Enajenación de derechos representativos de capital y otros derechos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_419" model="account.tax.report.line">
            <field name="name">Base imponible(419)</field>
            <field name="code">c419</field>
            <field name="tag_name">419 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_320"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_469" model="account.tax.report.line">
            <field name="name">Valor retenido(469)</field>
            <field name="code">c469</field>
            <field name="tag_name">469 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_320"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_323" model="account.tax.report.line">
            <field name="name">Seguros y reaseguros (primas y cesiones)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_420" model="account.tax.report.line">
            <field name="name">Base imponible(420)</field>
            <field name="code">c420</field>
            <field name="tag_name">420 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_323"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_470" model="account.tax.report.line">
            <field name="name">Valor retenido(470)</field>
            <field name="code">c470</field>
            <field name="tag_name">470 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_323"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_326" model="account.tax.report.line">
            <field name="name">Servicios técnicos, administrativos o de consultoría y regalías</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_421" model="account.tax.report.line">
            <field name="name">Base imponible(421)</field>
            <field name="code">c421</field>
            <field name="tag_name">421 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_326"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_471" model="account.tax.report.line">
            <field name="name">Valor retenido(471)</field>
            <field name="code">c471</field>
            <field name="tag_name">471 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_326"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_329" model="account.tax.report.line">
            <field name="name">Otros conceptos de ingresos gravados</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_422" model="account.tax.report.line">
            <field name="name">Base imponible(422)</field>
            <field name="code">c422</field>
            <field name="tag_name">422 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_329"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_472" model="account.tax.report.line">
            <field name="name">Valor retenido(472)</field>
            <field name="code">c472</field>
            <field name="tag_name">472 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_329"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_332" model="account.tax.report.line">
            <field name="name">Otros pagos al exterior no sujetos a retención</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_301"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_423" model="account.tax.report.line">
            <field name="name">Base imponible(423)</field>
            <field name="code">c423</field>
            <field name="tag_name">423 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_332"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_334" model="account.tax.report.line">
            <field name="name">En paraísos fiscales o regímenes fiscales preferentes</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_267"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>
        <record id="tax_report_line_parent_line_report_335" model="account.tax.report.line">
            <field name="name">Intereses</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_424" model="account.tax.report.line">
            <field name="name">Base imponible(424)</field>
            <field name="code">c424</field>
            <field name="tag_name">424 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_335"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_474" model="account.tax.report.line">
            <field name="name">Valor retenido(474)</field>
            <field name="code">c474</field>
            <field name="tag_name">474 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_335"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_338" model="account.tax.report.line">
            <field name="name">Anticipo de dividendos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_425" model="account.tax.report.line">
            <field name="name">Base imponible(425)</field>
            <field name="code">c425</field>
            <field name="tag_name">425 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_338"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_475" model="account.tax.report.line">
            <field name="name">Valor retenido(475)</field>
            <field name="code">c475</field>
            <field name="tag_name">475 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_338"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_341" model="account.tax.report.line">
            <field name="name">Dividendos sin beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4260" model="account.tax.report.line">
            <field name="name">Base imponible(4260)</field>
            <field name="code">c4260</field>
            <field name="tag_name">4260 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_341"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4760" model="account.tax.report.line">
            <field name="name">Valor retenido(4760)</field>
            <field name="code">c4760</field>
            <field name="tag_name">4760 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_341"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_344" model="account.tax.report.line">
            <field name="name">Dividendos con beneficiario efectivo persona natural residente en Ecuador</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4270" model="account.tax.report.line">
            <field name="name">Base imponible(4270)</field>
            <field name="code">c4270</field>
            <field name="tag_name">4270 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_344"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4770" model="account.tax.report.line">
            <field name="name">Valor retenido(4770)</field>
            <field name="code">c4770</field>
            <field name="tag_name">4770 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_344"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_347" model="account.tax.report.line">
            <field name="name">Dividendos incumpliendo el deber de informar la composición societaria</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_4280" model="account.tax.report.line">
            <field name="name">Base imponible(4280)</field>
            <field name="code">c4280</field>
            <field name="tag_name">4280 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_347"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_4780" model="account.tax.report.line">
            <field name="name">Valor retenido(4780)</field>
            <field name="code">c4780</field>
            <field name="tag_name">4780 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_347"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_350" model="account.tax.report.line">
            <field name="name">Enajenación de derechos representativos de capital y otros derechos</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_429" model="account.tax.report.line">
            <field name="name">Base imponible(429)</field>
            <field name="code">c429</field>
            <field name="tag_name">429 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_350"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_479" model="account.tax.report.line">
            <field name="name">Valor retenido(479)</field>
            <field name="code">c479</field>
            <field name="tag_name">479 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_350"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_353" model="account.tax.report.line">
            <field name="name">Seguros y reaseguros (primas y cesiones)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_430" model="account.tax.report.line">
            <field name="name">Base imponible(430)</field>
            <field name="code">c430</field>
            <field name="tag_name">430 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_353"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_480" model="account.tax.report.line">
            <field name="name">Valor retenido(480)</field>
            <field name="code">c480</field>
            <field name="tag_name">480 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_353"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_356" model="account.tax.report.line">
            <field name="name">Servicios técnicos, administrativos o de consultoría y regalías</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_431" model="account.tax.report.line">
            <field name="name">Base imponible(431)</field>
            <field name="code">c431</field>
            <field name="tag_name">431 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_356"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_481" model="account.tax.report.line">
            <field name="name">Valor retenido(481)</field>
            <field name="code">c481</field>
            <field name="tag_name">481 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_356"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_359" model="account.tax.report.line">
            <field name="name">Otros conceptos de ingresos gravados</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_432" model="account.tax.report.line">
            <field name="name">Base imponible(432)</field>
            <field name="code">c432</field>
            <field name="tag_name">432 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_359"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_482" model="account.tax.report.line">
            <field name="name">Valor retenido(482)</field>
            <field name="code">c482</field>
            <field name="tag_name">482 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_359"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_362" model="account.tax.report.line">
            <field name="name">Otros pagos al exterior no sujetos a retención</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_334"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_433" model="account.tax.report.line">
            <field name="name">Base imponible(433)</field>
            <field name="code">c433</field>
            <field name="tag_name">433 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_362"/>
            <field name="sequence">1</field>
        </record>
        <record id="tax_report_line_parent_line_report_364" model="account.tax.report.line">
            <field name="name">SUBTOTAL OPERACIONES EFECTUADAS CON EL EXTERIOR</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="sequence">1</field>
            <field name="formula">None</field>
        </record>

        <record id="tax_report_line_103_487" model="account.tax.report.line">
            <field name="name">Base imponible(487)</field>
            <field name="code">c487</field>
            <field name="tag_name">487 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_364"/>
            <field name="sequence">1</field>
        </record>

        <record id="tax_report_line_103_498" model="account.tax.report.line">
            <field name="name">Valor retenido(498)</field>
            <field name="code">c498</field>
            <field name="tag_name">498 (Reporte 103)</field>
            <field name="report_id" ref="tax_report_103"/>
            <field name="parent_id" ref="tax_report_line_parent_line_report_364"/>
            <field name="sequence">1</field>
        </record>

    </data>
</odoo>