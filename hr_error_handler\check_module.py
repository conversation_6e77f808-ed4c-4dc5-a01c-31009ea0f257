#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت للتحقق من صحة المديول قبل التثبيت
"""

import os
import sys
import ast


def check_python_syntax(file_path):
    """فحص صحة بناء الجملة في ملف Python"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return True, "OK"
    except SyntaxError as e:
        return False, f"Syntax Error: {e}"
    except Exception as e:
        return False, f"Error: {e}"


def check_manifest():
    """فحص ملف __manifest__.py"""
    manifest_path = '__manifest__.py'
    if not os.path.exists(manifest_path):
        return False, "ملف __manifest__.py غير موجود"
    
    is_valid, message = check_python_syntax(manifest_path)
    if not is_valid:
        return False, f"خطأ في __manifest__.py: {message}"
    
    # فحص محتوى الملف
    try:
        with open(manifest_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود العناصر المطلوبة
        required_keys = ['name', 'version', 'depends', 'installable']
        for key in required_keys:
            if f"'{key}'" not in content and f'"{key}"' not in content:
                return False, f"المفتاح المطلوب '{key}' غير موجود في __manifest__.py"
        
        return True, "ملف __manifest__.py صحيح"
    except Exception as e:
        return False, f"خطأ في قراءة __manifest__.py: {e}"


def check_python_files():
    """فحص جميع ملفات Python في المديول"""
    errors = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                is_valid, message = check_python_syntax(file_path)
                if not is_valid:
                    errors.append(f"{file_path}: {message}")
    
    return len(errors) == 0, errors


def check_required_files():
    """فحص وجود الملفات المطلوبة"""
    required_files = [
        '__init__.py',
        '__manifest__.py',
        'models/__init__.py',
        'models/hr_employee_public.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files


def main():
    """الدالة الرئيسية للفحص"""
    print("🔍 فحص مديول HR Error Handler...")
    print("=" * 50)
    
    # فحص الملفات المطلوبة
    print("1. فحص الملفات المطلوبة...")
    files_ok, missing_files = check_required_files()
    if files_ok:
        print("   ✅ جميع الملفات المطلوبة موجودة")
    else:
        print("   ❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"      - {file}")
        return False
    
    # فحص ملف __manifest__.py
    print("2. فحص ملف __manifest__.py...")
    manifest_ok, manifest_msg = check_manifest()
    if manifest_ok:
        print(f"   ✅ {manifest_msg}")
    else:
        print(f"   ❌ {manifest_msg}")
        return False
    
    # فحص ملفات Python
    print("3. فحص بناء الجملة في ملفات Python...")
    python_ok, python_errors = check_python_files()
    if python_ok:
        print("   ✅ جميع ملفات Python صحيحة")
    else:
        print("   ❌ أخطاء في ملفات Python:")
        for error in python_errors:
            print(f"      - {error}")
        return False
    
    print("=" * 50)
    print("🎉 المديول جاهز للتثبيت!")
    print("\nخطوات التثبيت:")
    print("1. انسخ مجلد hr_error_handler إلى مجلد addons")
    print("2. أعد تشغيل Odoo")
    print("3. حدث قائمة التطبيقات")
    print("4. ثبت المديول من قائمة التطبيقات")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
