# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_discount
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:49+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr "계산원이 전체 주문에 대해 할인을 제공하도록 허용하십시오."

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "할인"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount %"
msgstr "할인 %"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "할인 (%)"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.pos_config_view_form_inherit_pos_discount
msgid "Discount Product"
msgstr "할인 상품"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr "할인 상품이 없습니다."

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr "주문 할인"

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr "점포판매시스템 환경 설정"

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
msgid "The default discount percentage"
msgstr "기본 할인율"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be"
" Sold' and 'Available in Point of Sale'."
msgstr "할인 상품이 잘못 구성된 것 같습니다. '판매 가능' 및 'POS에서 사용 가능'으로 표시되어 있는지 확인하십시오."

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to model the discount."
msgstr "할인 모델을 만드는 데 사용되는 상품입니다."
