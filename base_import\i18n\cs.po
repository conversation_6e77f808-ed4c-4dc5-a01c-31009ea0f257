# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <PERSON>, 2021
# <PERSON>, 2021
# <PERSON> <jan.ho<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <brenci<PERSON><PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# karol<PERSON>a schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "%d records successfully imported"
msgstr "%dzáznamy úspěšně importovány"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect"
msgstr ""
"V souboru byl nalezen jeden sloupec, což často znamená, že oddělovač souborů"
" je nesprávný"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Additional Fields"
msgstr "Další pole"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Advanced"
msgstr "Pokročilý"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr "Povolit určení shody s podřízenými poli"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Během importu došlo k neznámému problému (pravděpodobně ztracené připojení, "
"překročen datový limit nebo překročeny paměťové limity). Zkuste prosím "
"problém, pokud jde o přechodný problém. Pokud problém přetrvává, zkuste "
"soubor rozdělit a ne importovat najednou."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Základní část"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Základní import"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapování základního importu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch Import"
msgstr "Dávkový import"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Batch limit"
msgstr "Dávkový limit"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Cancel"
msgstr "Zrušit"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line"
msgstr ""
"Kliknutím na tlačítko „Pokračovat“ pokračujte v importu a pokračujte na "
"řádku"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr "Sloupec %s obsahuje nesprávné hodnoty (hodnota: %s)"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr "Sloupec %s obsahuje nesprávné hodnoty. Chyba v řádku %d: %s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Název sloupce"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Comma"
msgstr "Čárka"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Comments"
msgstr "Komentáře"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"URL se nepodařilo načíst: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Create new values"
msgstr "Vytvořit nové hodnoty"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__currency_id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__currency_id
msgid "Currency"
msgstr "Měna"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "ID databáze"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Date Format:"
msgstr "Formát data:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Datetime Format:"
msgstr "Datum Formát:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Decimal Separator:"
msgstr "Oddělovač desetinných míst:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Dot"
msgstr "Tečka"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download"
msgstr "Stáhnout"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Download Template"
msgstr "Stáhnout šablonu"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__dt
msgid "Dt"
msgstr "Dt"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Encoding:"
msgstr "Kódování:"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr "Chyba při analýze data [%s:L%d]: %s"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr "Chyba při importu záznamů: oddělovač textu by měl být jediný znak."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %d entries while the first row has %d. You may need to change "
"the separator character."
msgstr ""
"Chyba při importu záznamů: všechny řádky by měly stejný počet pozic, ale "
"řádek hlavičky má %d pozic, zatímco první řádek má %d. Možná byste měli "
"změnit znak oddělovače."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Estimated time left:"
msgstr "Odhad zbývajícího času:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Everything seems valid."
msgstr "Všechno se zdá platné."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr "Doporučujeme Excel soubory, neboť formátování je automatické."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "Externí ID"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Pole Název"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Soubor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "File Column"
msgstr "Sloupec souboru"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Název souboru"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Typ Souboru"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Velikost souboru překračuje nakonfigurované maximum (%s bajtů)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""
"Soubor ke kontrole a / nebo importu, surový binární soubor (ne base64)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr "Před přerušením dokončuji aktuální dávku...."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr "U souborů CSV bude možná nutné vybrat správný oddělovač."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Formatting"
msgstr "Formátování"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Nalezena neplatná obrazová data, obrázky by měly být importovány buď jako "
"adresy URL, nebo jako data kódovaná pomocí base64."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr "Přejděte na časté otázky k importům"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Help"
msgstr "Pomoc"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "Zde je začátek souboru, který jsme nemohli importovat:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__id
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Pokud soubor obsahuje\n"
"                    názvy sloupců, může Odoo zkusit automatickou detekci\n"
"                    pole odpovídajícího sloupci. Díky tomu je import jednodušší,\n"
"                    zejména pokud má soubor mnoho sloupců."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Pokud model používá openchatter, sledování historie nastaví předplatné a "
"odešle oznámení během importu, ale povede k pomalejšímu importu."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Nadměrná velikost obrázku, nahrané obrázky musí být menší než 42 milionů "
"pixelu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import"
msgstr "Import"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import FAQ"
msgstr "Časté dotazy k importu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "Importovat soubor"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr "Importní soubor nemá obsah nebo je poškozen"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Import preview failed due to:"
msgstr "Náhled importu se nezdařil z důvodu:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "Importujte záznamy"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid ""
"Import timed out. Please retry. If you still encounter this issue, the file "
"may be too big for the system's configuration, try to split it (import less "
"records per file)."
msgstr ""
"Import vypršel. Zkuste to prosím znovu. Pokud se stále setkáváte s tímto "
"problémem, soubor může být příliš velký pro konfiguraci systému, zkuste jej "
"rozdělit (import méně záznamů na soubor)."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Imported file"
msgstr "Importovaný soubor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Importing"
msgstr "Importuji"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Neplatná hodnota buňky na řádku %(row)s, sloupec %(col)s: %(cell_value)s"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Label"
msgstr "Označení"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child____last_update
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_complex__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Load File"
msgstr "Načíst soubor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr "Načíst soubor..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
#, python-format
msgid "Model"
msgstr "Model"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr "Vyskytlo se vícero chyb"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__name
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__name
#, python-format
msgid "Name"
msgstr "Jméno"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Need Help?"
msgstr "Potřebujete pomoc?"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "No Separator"
msgstr "Žádný oddělovač"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for"
msgstr "Žádné odpovídající záznamy pro"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "No matching records found for the following"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Odoo Field"
msgstr "Pole Odoo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__othervalue
msgid "Other Variable"
msgstr "Jiná proměnná"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__parent_id
msgid "Parent"
msgstr "Nadřazený"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Prevent import"
msgstr "Zabránit importu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Preview"
msgstr "Náhled"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Relation Fields"
msgstr "Vztahová pole"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Res model"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Resume"
msgstr "Shrnutí"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Search for a field..."
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "See possible values"
msgstr "Podívejte se na možné hodnoty"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Selected Sheet:"
msgstr "Vybraný list:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Semicolon"
msgstr "Středník"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Separator:"
msgstr "Oddělovač:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to:"
msgstr "Nastavit na:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: False"
msgstr "Nastavit na: Nepravda"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set to: True"
msgstr "Nastavit na: Pravda"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Set value as empty"
msgstr "Nastavit hodnotu jako prázdnou"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Sheet:"
msgstr "List:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Skip record"
msgstr "Přeskočit záznam"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_preview__somevalue
msgid "Some Value"
msgstr "Nějaká hodnota"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Space"
msgstr "Prostor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Standard Fields"
msgstr "Standartní pole"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Start at line"
msgstr "Začněte na řádku"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Stop Import"
msgstr "Zastavit import"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Suggested Fields"
msgstr "Navrhovaná pole"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Tab"
msgstr "Tab"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Testing"
msgstr "Testování"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_preview
msgid "Tests : Base Import Model Preview"
msgstr "Testy: náhled modelu základního importu"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char
msgid "Tests : Base Import Model, Character"
msgstr "Testy: základní model importu, znak"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_noreadonly
msgid "Tests : Base Import Model, Character No readonly"
msgstr "Testy: základní model importu, znak pouze pro čtení"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_readonly
msgid "Tests : Base Import Model, Character readonly"
msgstr "Testy: základní model importu, znak pouze pro čtení"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_required
msgid "Tests : Base Import Model, Character required"
msgstr "Testy: základní model importu, požadovaný znak"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_states
msgid "Tests : Base Import Model, Character states"
msgstr "Testy: základní model importu, stavy znaků"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_char_stillreadonly
msgid "Tests : Base Import Model, Character still readonly"
msgstr "Testy: základní model importu, znak stále jen pro čtení"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o
msgid "Tests : Base Import Model, Many to One"
msgstr "Testy: základní model importu, mnoho k jednomu"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_related
msgid "Tests : Base Import Model, Many to One related"
msgstr "Testy: základní model importu, mnoho k jednomu"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required
msgid "Tests : Base Import Model, Many to One required"
msgstr "Testy: základní model importu, vyžaduje se více než jeden"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_m2o_required_related
msgid "Tests : Base Import Model, Many to One required related"
msgstr "Testy: základní model importu, více než jeden požadovaný související"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m
msgid "Tests : Base Import Model, One to Many"
msgstr "Testy: základní model importu, jeden k mnoha"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_o2m_child
msgid "Tests : Base Import Model, One to Many child"
msgstr "Testy: základní model importu, jeden pro více dětí"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_complex
msgid "Tests: Base Import Model Complex"
msgstr "Testy: základní model importu komplex"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_tests_models_float
msgid "Tests: Base Import Model Float"
msgstr "Zkoušky: základní import modelu float"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "Oddělovač textu:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr "Soubor obsahuje kritické chyby (viz níže)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "The file contains non-blocking warnings (see below)"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr "Soubor bude importován po dávkách"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "This column will be concatenated in field"
msgstr "Tento sloupec bude sloučen do pole"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "This file has been successfully imported up to line %d."
msgstr "Tento soubor byl úspěšně importován až po řádek %d."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Thousands Separator:"
msgstr "Tisíce oddělovač:"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "To import multiple values, separate them by a comma"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "To import, select a field..."
msgstr "Pro import, vyberte pole..."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Track history during import"
msgstr "Sledovat historii během importu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/js/import_action.js:0
#, python-format
msgid "Type"
msgstr "Typ"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr "Nelze načíst soubor „{extension}“: vyžaduje modul Python „{modname}“"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Nepodporovaný formát souboru „{}“, import podporuje pouze CSV, ODS, XLS a "
"XLSX"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Untitled"
msgstr "Bez názvu"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload File"
msgstr "Nahrát soubor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "Pro import nahrajte Excel nebo CSV soubor"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "Use first row as header"
msgstr "Použít první žádek pro záhlaví"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "Users"
msgstr "Uživatelé"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_noreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_readonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_states__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_char_stillreadonly__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_m2o_required_related__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m__value
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_o2m_child__value
msgid "Value"
msgstr "Hodnota"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_tests_models_float__value2
msgid "Value2"
msgstr "Hodnota2"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr "Pokud hodnotu nelze dohledat:"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Nelze importovat obrázky pomocí adresy URL, zeptejte se u správce nebo "
"podpory."

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr "Před obnovením importu můžete soubor otestovat nebo znovu načíst."

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "Musíte importovat alespoň jedno pole"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at multiple rows"
msgstr "ve více řádcích"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "at row"
msgstr "v řádku"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "batch"
msgstr "šarže"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "in field"
msgstr "v poli"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "minutes"
msgstr "minut"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "more)"
msgstr "více)"

#. module: base_import
#. openerp-web
#: code:addons/base_import/static/src/legacy/xml/base_import.xml:0
#, python-format
msgid "out of"
msgstr "z"

#. module: base_import
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr "neznámý kód chyby%s"
