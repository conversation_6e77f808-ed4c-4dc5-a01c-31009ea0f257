# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Rasaree<PERSON> Lappiam, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid "%s: Time Off"
msgstr "%s: การลา"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%s\n"
"\n"
"Contracts:\n"
"%s"
msgstr ""
"ไม่สามารถกำหนดวันลาให้กับสัญญาหลายฉบับที่มีตารางการทำงานต่างกันได้\n"
"\n"
"โปรดสร้างวันหยุดหนึ่งครั้งสำหรับแต่ละสัญญา\n"
"\n"
"ระบบการลา:\n"
"%s\n"
"\n"
"ข้อมูลติดต่อ:\n"
"%s"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "สัญญาของพนักงาน"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid ""
"Every new time off type in this list will be reported as select work entry "
"in payslip."
msgstr ""
"ทุกประเภทเวลาหยุดใหม่ในรายการนี้จะถูกรายงานเป็นรายการการเข้างานในสลิปเงินเดือน"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR การเข้างาน"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR ประเภทการเข้างาน"

#. module: hr_work_entry_holidays
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays.work_entry_type_leave_form_inherit
msgid "Payroll"
msgstr "เงินเดือน"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid "Status"
msgstr "สถานะ"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"สถานะถูกตั้งค่าเป็น 'เพื่อส่ง' เมื่อมีการสร้างคำขอลาหยุด\n"
"สถานะคือ 'เพื่ออนุมัติ' เมื่อผู้ใช้ยืนยันคำขอลาหยุด\n"
"สถานะคือ 'ปฏิเสธ' เมื่อผู้จัดการปฏิเสธคำขอลาหยุด\n"
"สถานะคือ 'อนุมัติ' เมื่อคำขอลาหยุดได้รับการอนุมัติโดยผู้จัดการ"

#. module: hr_work_entry_holidays
#: code:addons/hr_work_entry_holidays/models/hr_leave.py:0
#, python-format
msgid ""
"There is no employee set on the time off. Please make sure you're logged in "
"the correct company."
msgstr ""
"ไม่มีพนักงานตั้งเวลาการลา "
"โปรดตรวจสอบให้แน่ใจว่าคุณได้เข้าสู่ระบบบริษัทที่ถูกต้อง"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_id
msgid "Time Off"
msgstr "การลา"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Time Off Type"
msgstr "ประเภทการลา"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_leave_type__work_entry_type_id
msgid "Work Entry Type"
msgstr "ประเภทการเข้างาน"
