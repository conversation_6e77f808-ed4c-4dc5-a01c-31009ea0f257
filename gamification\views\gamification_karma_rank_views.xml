<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- ACTIONS -->
        <record id="action_current_rank_users" model="ir.actions.act_window">
            <field name="name">Users</field>
            <field name="res_model">res.users</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('rank_id', '=', active_id)]</field>
        </record>

        <!-- Ranks views -->
        <record id="gamification_karma_ranks_action" model="ir.actions.act_window">
            <field name="name">Ranks</field>
            <field name="res_model">gamification.karma.rank</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new rank
                </p><p>
                    A rank correspond to a fixed karma level. The more you have karma, the more your rank is high.
                    This is used to quickly know which user is new or old or highly or not active.
                </p>
            </field>
        </record>

        <record id="gamification_karma_ranks_view_search" model="ir.ui.view">
            <field name="name">gamification.karma.ranks.view.search</field>
            <field name="model">gamification.karma.rank</field>
            <field name="arch" type="xml">
                <search string="Search Ranks">
                    <field name="name"/>
                    <field name="karma_min"/>
                    <field name="description"/>
                    <field name="user_ids"/>
                </search>
            </field>
        </record>

        <record id="gamification_karma_ranks_view_tree" model="ir.ui.view">
            <field name="name">gamification.karma.ranks.view.tree</field>
            <field name="model">gamification.karma.rank</field>
            <field name="arch" type="xml">
                <tree string="Ranks List">
                    <field name="name"/>
                    <field name="karma_min"/>
                    <field name="rank_users_count"/>
                </tree>
            </field>
        </record>

        <record id="gamification_karma_rank_view_form" model="ir.ui.view">
            <field name="name">gamification.karma.rank.view.form</field>
            <field name="model">gamification.karma.rank</field>
            <field name="arch" type="xml">
                <form string="Rank">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button type="action" class="oe_stat_button" icon="fa-users" name="%(action_current_rank_users)d">
                                <div class="o_form_field o_stat_info">
                                    <span class="o_stat_value">
                                        <field name="rank_users_count" />
                                    </span>
                                    <span class="o_stat_text">Users</span>
                                </div>
                            </button>
                        </div>
                        <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="e.g. Master Chief"/>
                            </h1>
                        </div>
                        <group>
                            <field name="karma_min"/>
                            <field name="create_date" invisible="1"/>
                        </group>
                        <notebook>
                            <page string="Description" name="description">
                                <field name="description" placeholder="e.g. A Master Chief knows quite everything on the forum! You cannot beat him!"/>
                            </page>
                            <page string="Motivational" name="motivational">
                                <field name="description_motivational" placeholder="e.g. Reach this rank to gain a free mug !"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <menuitem id="gamification_karma_ranks_menu" parent="gamification_menu" action="gamification_karma_ranks_action" sequence="40"/>
    </data>
</odoo>
