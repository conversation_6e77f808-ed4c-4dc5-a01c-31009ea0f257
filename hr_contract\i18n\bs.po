# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_contract
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-18 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 09:49+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span class=\"text-muted\">(If fixed-term contract)</span>"
msgstr "<span class=\"text-muted\">(ako je ugovor na određeno vrijeme)</span>"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ mjesec</span>"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "Aktivan"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__advantages
msgid "Advantages"
msgstr "Prednosti"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Advantages..."
msgstr "Prednosti..."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
msgid "Company"
msgstr "Kompanija"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
msgid "Company Vehicle"
msgstr "Vozilo kompanije"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Details"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "Referenca ugovora"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Terms"
msgstr "Uslovi ugovora"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Contract Type"
msgstr "Tip ugovora"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract_type
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_type
msgid "Contract Types"
msgstr "Tipovi ugovora"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "Ugovori koji će isteći"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "Ugovor istekao"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:99
#, python-format
msgid "Contract start date must be earlier than contract end date."
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "Ugovor za obnovu"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.act_hr_employee_2_hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Contracts"
msgstr "Ugovori"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Current Contract"
msgstr "Trenutni ugovor"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Current Employee"
msgstr "Trenutni zaposleni"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "Obriši"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
msgid "Department"
msgstr "Odjeljenje"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Dropdown menu"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "Uredi ugovor"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "Zaposleni"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__type_id
msgid "Employee Category"
msgstr "Kategorija uposlenika"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Ugovor zaposlenog"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "Ugovori zaposlinih"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
msgid "Employee's monthly gross wage."
msgstr "Mjesečna bruto plata zaposlenog"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__resource_calendar_id
msgid "Employee's working schedule."
msgstr "Raspored rada zaposlenog"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
msgid "End Date"
msgstr "Datum Završetka"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "Datum kraja ugovora (ako je ugovor na određeno vrijeme)."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "Datum kraja probnog rada (ako postoji)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "Kraj probnog rada"

#. module: hr_contract
#: selection:hr.contract,state:0
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "Istekao"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract_type__sequence
msgid "Gives the sequence when displaying a list of Contract."
msgstr "Daje sekvencu kada se prikazuje lista ugovora."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__reported_to_secretariat
msgid ""
"Green this button when the contract information has been transfered to the "
"social secretariat."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Grupiši po"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
#: model:ir.cron,cron_name:hr_contract.ir_cron_data_contract_update_state
#: model:ir.cron,name:hr_contract.ir_cron_data_contract_update_state
msgid "HR Contract: update state"
msgstr "Ugovor o radu: ažuriraj status"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Important Messages"
msgstr "Važne poruke"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__manager
msgid "Is a Manager"
msgstr "Je upravitelj"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job"
msgstr "Posao"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
msgid "Job Position"
msgstr "Radno mjesto"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Latest contract of the employee"
msgstr "Zadnji ugovor zaposlenog"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Manager"
msgstr "Upravitelj"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid "Medical Exam"
msgstr "Medicinski pregled"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__medic_exam
msgid "Medical Examination Date"
msgstr "Datum medicinskog pregleda"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Monthly Advantages in Cash"
msgstr "Mjesečne prednosti u gotovini"

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "New"
msgstr "Novi"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "Zabilješke"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: hr_contract
#: selection:hr.contract,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: hr_contract
#: selection:hr.contract,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: hr_contract
#: selection:hr.contract,state:0
msgid "Running"
msgstr "Izvodi se"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "Podatci o plati"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "Pretraži ugovor"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Search Contract Type"
msgstr "Pretraži tip ugovora"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__sequence
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_type_view_search
msgid "Sequence"
msgstr "Sekvenca"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__reported_to_secretariat
msgid "Social Secretariat"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
msgid "Start Date"
msgstr "Datum početka"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_start
msgid "Start date of the contract."
msgstr "Datum početka validnosti ugovora."

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "State"
msgstr "Rep./Fed."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
msgid "Status"
msgstr "Status"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "Status ugovora"

#. module: hr_contract
#: selection:hr.contract,state:0
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "Za obnovu"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "To Report to Social Secretariat"
msgstr ""

#. module: hr_contract
#: selection:hr.contract,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_expire
msgid "Visa Expire Date"
msgstr "Datum isteka vize"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "Broj Vize"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
msgid "Wage"
msgstr "Plata"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "Broj radne dozvole"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
msgid "Working Schedule"
msgstr "Raspored rada"
