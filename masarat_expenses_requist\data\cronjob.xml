<?xml version="1.0"?>
<odoo noupdate="1">
	<record forcecreate="True" id="masarat_expenses_closer_notification" model="ir.cron">
		<field name="name">Masarat Expenses Closer</field>
		<field eval="True" name="active"/>
		<field name="user_id" ref="base.user_admin"/>
		<field name="interval_number">1</field>
		<field name="interval_type">days</field>
		<field name="numbercall">-1</field>
		<field name="model_id" ref="masarat_expenses_requist.model_hr_masarat_expense"/>
		<field name="state">code</field>
		<field name="code">model.check_for_closer_notification()</field>
	</record>
</odoo>
