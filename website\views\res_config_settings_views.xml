<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.website</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="20"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('settings')]" position="inside">
                    <div class="app_settings_block" data-string="Website" string="Website" data-key="website" groups="website.group_website_designer">
                        <h2 groups="website.group_multi_website">Select the Website to Configure</h2>
                        <div class="row mt16 o_settings_container" id="website_selection_settings" groups="website.group_multi_website">
                            <div class="col-xs-12 col-md-6 o_setting_box" id="website">
                                <div class="o_setting_right_pane">
                                    <label string="Website" for="website_id"/>
                                    <div class="text-muted">
                                        Settings on this page will apply to this website
                                    </div>
                                    <div class="mt16">
                                        <field name="website_id" options="{'no_open': True, 'no_create': True}"/>
                                    </div>
                                    <div>
                                        <button name="action_website_create_new" type="object" string="Create a New Website" class="btn-secondary" icon="fa-arrow-right"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2>Website <button name="action_website_create_new" type="object" string="New" class="ml-2 btn btn-link" icon="fa-plus" groups="!website.group_multi_website"/></h2>
                        <div class="row mt16 o_settings_container" id="website_settings_placeholder" attrs="{'invisible': [('website_id', '!=', False)]}">
                            <div class="col-12 o_setting_box">
                                <div class="text-muted" groups="website.group_multi_website">
                                    Select a website to load its settings.
                                </div>
                                <div class="text-muted" groups="!website.group_multi_website">
                                    There is no website available for this company. You could create a new one.
                                </div>
                            </div>
                        </div>
                        <!-- !! Every fields inside this container should be website specific (related to website record) !! -->
                        <div class="row mt16 o_settings_container" id="website_settings" attrs="{'invisible': [('website_id', '=', False)]}">
                            <div class="col-12 o_setting_box" id="website_action_setting" style="margin-left: 30px; margin-bottom: 16px;">
                                <button name="website_go_to" type="object" string="Go to Website" class="btn btn-primary" icon="fa-globe"/>
                                <button name="install_theme_on_current_website" type="object" string="Pick a Theme" class="ml-2 btn btn-secondary" icon="fa-paint-brush"/>
                                <button name="%(website.action_website_add_features)d" type="action" string="Add features" class="ml-2 btn btn-secondary" icon="fa-plus"/>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="domain_setting">
                                <div class="o_setting_right_pane">
                                    <label for="website_name" string="Website Title"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Name and favicon of your website
                                    </div>
                                    <div class="content-group">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" string="Name" for="website_name"/>
                                            <field name="website_name" attrs="{'required': [('website_id', '!=', False)]}"/>
                                        </div>
                                        <div class="row">
                                            <label class="col-lg-3 o_light_label" for="favicon" />
                                            <field name="favicon" widget="image" class="float-left oe_avatar"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="company_settings" groups="base.group_multi_company">
                                <div class="o_setting_right_pane">
                                    <label string="Company" for="website_company_id"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        The company this website belongs to
                                    </div>
                                    <field name="website_company_id" attrs="{'required': [('website_id', '!=', False)]}" />
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="languages_setting">
                                <div class="o_setting_right_pane">
                                    <label for="language_ids"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Languages available on your website
                                    </div>
                                    <div class="content-group">
                                        <div class="mt16">
                                            <field name="language_ids" widget="many2many_tags" options="{'no_create': True, 'no_open': True}"
                                                attrs="{'required': [('website_id', '!=', False)]}"/>
                                        </div>
                                        <field name="website_language_count" invisible="1"/>
                                        <div class="mt8" attrs="{'invisible':[('website_language_count', '&lt;', 2)]}">
                                            <label class="o_light_label mr8" string="Default" for="website_default_lang_id"/>
                                            <field name="website_default_lang_id" options="{'no_open': True, 'no_create': True}" attrs="{'required': [('website_id', '!=', False)]}"/>
                                        </div>
                                    </div>
                                    <div class="mt8">
                                        <button type="action" name="%(base.action_view_base_language_install)d" string="Install new language" class="btn-link" icon="fa-arrow-right"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="domain_settings">
                                <div class="o_setting_right_pane">
                                    <label string="Domain" for="website_domain"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Display this website when users visit this domain
                                    </div>
                                    <div class="mt8">
                                        <field name="website_domain" placeholder="https://www.odoo.com"/>
                                    </div>
                                    <div groups="base.group_no_one">
                                        <div class="mt8 text-muted" title="You can have 2 websites with same domain AND a condition on country group to select wich website use.">
                                            Once the selection of available websites by domain is done, you can filter by country group.
                                        </div>
                                        <div class="mt8">
                                            <field name="website_country_group_ids" widget="many2many_tags"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="specific_user_account_setting" groups="website.group_multi_website">
                                <div class="o_setting_left_pane">
                                    <field name="specific_user_account"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="specific_user_account"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Force your user to create an account per website
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="website_cookies_bar_setting">
                                <div class="o_setting_left_pane">
                                    <field name="website_cookies_bar" widget="website_cookiesbar_field"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="website_cookies_bar"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Display a customizable cookies bar on your website.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="cdn_setting" title="A CDN helps you serve your website’s content with high availability and high performance to any visitor wherever they are located." groups="base.group_no_one">
                                <div class="o_setting_left_pane">
                                    <field name="cdn_activated"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="cdn_activated"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Use a CDN to optimize the availability of your website's content
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('cdn_activated', '=', False)]}">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" for="cdn_url"/>
                                            <field name="cdn_url"
                                                   attrs="{'required': [('cdn_activated', '=', True)]}"
                                                   placeholder="//mycompany.mycdn.com/"
                                                   t-translation="off"/>
                                        </div>
                                        <div class="row" >
                                            <label class="col-lg-3 o_light_label" for="cdn_filters"/>
                                            <field name="cdn_filters" class="oe_inline"
                                                   attrs="{'required': [('cdn_activated', '=', True)]}"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="social_media_settings">
                                <div class="o_setting_left_pane">
                                    <field name="has_social_network"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Social Media" for="has_social_network"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Add links to social media on your website
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('has_social_network', '=', False)]}">
                                        <div class="row">
                                            <label for="social_twitter" string="Twitter" class="col-md-3 o_light_label"/>
                                            <field name="social_twitter"/>
                                        </div>
                                        <div class="row">
                                            <label for="social_facebook" string="Facebook" class="col-md-3 o_light_label"/>
                                            <field name="social_facebook"/>
                                        </div>
                                        <div class="row">
                                            <label for="social_github" string="GitHub" class="col-md-3 o_light_label"/>
                                            <field name="social_github"/>
                                        </div>
                                        <div class="row">
                                            <label for="social_linkedin" string="LinkedIn" class="col-md-3 o_light_label"/>
                                            <field name="social_linkedin"/>
                                        </div>
                                        <div class="row">
                                            <label for="social_youtube" string="YouTube" class="col-md-3 o_light_label"/>
                                            <field name="social_youtube"/>
                                        </div>
                                        <div class="row">
                                            <label for="social_instagram" string="Instagram" class="col-md-3 o_light_label"/>
                                            <field name="social_instagram"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="social_default_image_setting">
                                <div class="o_setting_left_pane">
                                    <field name="has_default_share_image"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Default Social Share Image" for="has_default_share_image"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted" attrs="{'invisible': [('has_default_share_image', '=', False)]}">
                                        If set, replaces the website logo as the default social share image.
                                    </div>
                                    <field name="social_default_image" widget="image" class="w-25 mt-2" attrs="{'invisible': [('has_default_share_image', '=', False)]}"/>
                                </div>
                            </div>

                        </div>
                        <h2>Features</h2>
                        <div class="row mt16 o_settings_container" id="webmaster_settings">
                            <div class="col-12 col-lg-6 o_setting_box" id="google_maps_setting">
                                <div class="o_setting_left_pane">
                                    <field name="has_google_maps"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="has_google_maps"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Use Google Map on your website (<a href="/contactus">Contact Us</a> page, snippets, ...)
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('has_google_maps', '=', False)]}">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" string="API Key" for="google_maps_api_key"/>
                                            <field name="google_maps_api_key" attrs="{'required': [('has_google_maps', '=', True)]}" />
                                        </div>
                                    </div>
                                    <div class="mt8" attrs="{'invisible': [('has_google_maps', '=', False)]}">
                                        <a role="button" class="btn-link" target="_blank"
                                           href="https://console.developers.google.com/flows/enableapi?apiid=maps_backend,static_maps_backend&amp;keyType=CLIENT_SIDE&amp;reusekey=true">
                                            <i class="fa fa-arrow-right"/>
                                            Create a Google Project and Get a Key
                                        </a>
                                        <a role="button" class="btn-link" target="_blank"
                                           href="https://cloud.google.com/maps-platform/pricing">
                                            <i class="fa fa-arrow-right"/>
                                            Enable billing on your Google Project
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="auth_signup_uninvited_setting"
                            title=" To send invitations in B2B mode, open a contact or select several ones in list view and click on 'Portal Access Management' option in the dropdown menu *Action*.">
                                <div class="o_setting_left_pane">
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="auth_signup_uninvited"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Let your customers log in to see their documents
                                    </div>
                                    <div class="mt8">
                                        <field name="auth_signup_uninvited" class="o_light_label" widget="radio" options="{'horizontal': true}" required="True"/>
                                    </div>
                                    <div class="mt8 content-group">
                                        <button type="object" name="open_template_user" string="Default Access Rights" icon="fa-arrow-right" class="btn-link"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h2>SEO</h2>
                        <div class="row mt16 o_settings_container" id="seo_settings">
                            <div class="col-12 col-lg-offset-6 col-lg-6 o_setting_box" id="google_analytics_setting">
                                <div class="o_setting_left_pane">
                                    <field name="has_google_analytics"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="has_google_analytics"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Track visits in Google Analytics
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('has_google_analytics', '=', False)]}">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" string="Measurement ID" for="google_analytics_key"/>
                                            <field name="google_analytics_key" placeholder="G-XXXXXXXXXX"
                                                attrs="{'required': [('has_google_analytics', '=', True)]}"/>
                                        </div>
                                    </div>
                                    <div attrs="{'invisible': [('has_google_analytics', '=', False)]}">
                                        <a href="https://www.odoo.com/documentation/15.0/applications/websites/website/optimize/google_analytics.html"
                                                class="oe_link" target="_blank">
                                            <i class="fa fa-arrow-right"/>
                                            How to get my Measurement ID
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-offset-6 col-lg-6 o_setting_box" id="google_console_setting">
                                <div class="o_setting_left_pane">
                                    <field name="has_google_search_console"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="has_google_search_console"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Monitor Google Search results data
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('has_google_search_console', '=', False)]}">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" string="Code" for="google_search_console"/>
                                            <field name="google_search_console" placeholder="google1234567890123456.html"
                                                attrs="{'required': [('has_google_search_console', '=', True)]}"/>
                                        </div>
                                    </div>
                                    <div attrs="{'invisible': [('has_google_search_console', '=', False)]}">
                                        <small class='text-muted'>
                                            <i class="fa fa-info"/>: type some of the first chars after 'google' is enough, we'll guess the rest.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="google_analytics_dashboard_setting" attrs="{'invisible': [('has_google_analytics', '=', False)]}">
                                <div class="o_setting_left_pane">
                                    <field name="has_google_analytics_dashboard"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="has_google_analytics_dashboard"/>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Follow your website traffic in Odoo.
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('has_google_analytics_dashboard', '=', False)]}">
                                        <div class="row mt16">
                                            <label class="col-lg-3 o_light_label" string="Client ID" for="google_management_client_id"/>
                                            <field name="google_management_client_id" attrs="{'required': [('has_google_analytics_dashboard', '=', True)]}"/>
                                        </div>
                                        <div class="row">
                                            <label class="col-lg-3 o_light_label" string="Client Secret" for="google_management_client_secret"/>
                                            <field name="google_management_client_secret" attrs="{'required': [('has_google_analytics_dashboard', '=', True)]}"/>
                                        </div>
                                    </div>
                                    <div attrs="{'invisible': [('has_google_analytics_dashboard', '=', False)]}">
                                        <a href="https://www.odoo.com/documentation/15.0/applications/websites/website/optimize/google_analytics_dashboard.html"
                                            class="oe_link" target="_blank">
                                            <i class="fa fa-arrow-right"/>
                                            How to get my Client ID
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="robots_setting">
                                <div class="o_setting_right_pane">
                                    <span class="o_form_label">Robots.txt</span>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted">
                                        Robots.txt: This file tells to search engine crawlers which pages or files they can or can't request from your site.<br/>
                                    </div>
                                    <div class="mt4">
                                        <button type="object" name="action_open_robots" string="Edit robots.txt" class="btn-link" icon="fa-android"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="robots_setting">
                                <div class="o_setting_right_pane">
                                    <span class="o_form_label">Sitemap</span>
                                    <span class="fa fa-lg fa-globe" title="Values set here are website-specific." groups="website.group_multi_website"/>
                                    <div class="text-muted mt8">
                                        Sitemap.xml: Help search engine crawlers to find out what pages are present and which have recently changed, and to crawl your site accordingly. This file is automatically generated by Odoo.
                                    </div>
                                    <div class="mt4">
                                        <button type="object" name="action_ping_sitemap" string="Submit sitemap to Google" class='btn-link' icon="fa-google"/>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="action_website_configuration" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'website', 'bin_size': False}</field>
        </record>

        <menuitem id="menu_website_global_configuration" parent="menu_website_configuration"
            sequence="100" name="Configuration" groups="base.group_system"/>

        <menuitem name="Settings"
            id="menu_website_website_settings"
            action="action_website_configuration"
            parent="menu_website_global_configuration"
            groups="base.group_system"
            sequence="10"/>

        <menuitem id="menu_website_add_features" parent="website.menu_website_global_configuration"
            sequence="20" groups="base.group_system" action="action_website_add_features"/>

        <menuitem name="Websites"
            id="menu_website_websites_list"
            action="action_website_list"
            parent="menu_website_global_configuration"
            groups="base.group_no_one"
            sequence="10"
            />

        <menuitem name="Pages"
            id="menu_website_pages_list"
            action="action_website_pages_list"
            parent="menu_website_global_configuration"
            sequence="30"
            />

        <menuitem name="Menus"
            id="menu_website_menu_list"
            action="action_website_menu"
            parent="menu_website_global_configuration"
            sequence="45"
            groups="base.group_no_one"/>

</odoo>
