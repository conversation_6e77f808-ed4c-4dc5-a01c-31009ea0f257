# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_adyen
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-10 15:12+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Nurbahyt Kh <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_api_key
msgid "API Key"
msgstr "API түлхүүр"

#. module: payment_adyen
#: model:account.payment.method,name:payment_adyen.payment_method_adyen
#: model:ir.model.fields.selection,name:payment_adyen.selection__payment_acquirer__provider__adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid ""
"An error occurred during the processing of your payment. Please try again."
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "An error occurred when displayed this payment form."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_checkout_api_url
msgid "Checkout API URL"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_client_key
msgid "Client Key"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_acquirer.py:0
#, python-format
msgid "Could not establish the connection to the API."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_hmac_key
msgid "HMAC Key"
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Incorrect Payment Details"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_merchant_account
msgid "Merchant Account"
msgstr "Худалдагчийн Бүртгэл"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_acquirer
msgid "Payment Acquirer"
msgstr "Төлбөрийн хэрэгсэл"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_account_payment_method
msgid "Payment Methods"
msgstr "Төлбөрийн аргууд"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_token
msgid "Payment Token"
msgstr "Төлбөрийн Токен"

#. module: payment_adyen
#: model:ir.model,name:payment_adyen.model_payment_transaction
msgid "Payment Transaction"
msgstr "Төлбөрийн гүйлгээ"

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Please verify your payment details."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__provider
msgid "Provider"
msgstr "Үйлчилгээ үзүүлэгч"

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with invalid payment state: %s"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing merchant reference"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Received refund data with missing transaction values"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/controllers/main.py:0
#, python-format
msgid "Received tampered payment request data."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_acquirer__adyen_recurring_api_url
msgid "Recurring API URL"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_token.py:0
#, python-format
msgid "Saved payment methods cannot be restored once they have been deleted."
msgstr ""

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "Server Error"
msgstr "Серверийн алдаа"

#. module: payment_adyen
#: model:ir.model.fields,field_description:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "Shopper Reference"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_api_key
msgid "The API key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_hmac_key
msgid "The HMAC key of the webhook"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_checkout_api_url
msgid "The base URL for the Checkout API endpoints"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_recurring_api_url
msgid "The base URL for the Recurring API endpoints"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_client_key
msgid "The client key of the webservice user"
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_acquirer__adyen_merchant_account
msgid "The code of the merchant account to use with this acquirer"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_acquirer.py:0
#, python-format
msgid "The communication with the API failed."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_adyen
#: model:ir.model.fields,help:payment_adyen.field_payment_token__adyen_shopper_reference
msgid "The unique reference of the partner owning this token"
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/utils.py:0
#, python-format
msgid "Unrecognized field %s in street format."
msgstr "Гудамжны форматын танигдаагүй талбар %s"

#. module: payment_adyen
#. openerp-web
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#: code:addons/payment_adyen/static/src/js/payment_form.js:0
#, python-format
msgid "We are not able to process your payment."
msgstr ""

#. module: payment_adyen
#: code:addons/payment_adyen/models/payment_transaction.py:0
#, python-format
msgid "Your payment was refused. Please try again."
msgstr ""
