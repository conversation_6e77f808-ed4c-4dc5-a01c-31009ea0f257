<?xml version="1.0"?>
<odoo>

    <record id="exit_permission_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.exit.permission.form</field>
        <field name="model">hr.masarat.exit.permission</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="Manager Approval" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_approval" type="object" class="oe_highlight"/>
                    <button string="Manager Refuse" attrs="{'invisible': ['|',('is_manager','!=','1'),('state','!=','draft')]}" name="make_manager_refused" type="object"/>
                    <button string="HR Approval" name="make_hr_approval" type="object" class="oe_highlight" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="HR Refuse" name="make_hr_refused" type="object" groups="hr_approvales_masarat.group_hr_approvales_masarat"/>
                    <button string="Cancel" attrs="{'invisible': [('state','=','draft')]}" name="make_cancel_approval" type="object"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee" attrs="{'readonly': [('is_hr_group', '!=', 'yes')]}"/>
                            <field name="is_hr_group" invisible="1"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Request Info">
                            <field name="is_manager" invisible="1"/>
                            <field name="manager_id"  options='{"no_open": True}'/>
                            <field name="request_date"/>
                        </group>

                        <group string="Leave Info">
                            <field name="start_time"/>
                            <field name="end_time"/>
                            <field name="total_leave_minutes"/>
                        </group>
                        <group>
                            <field name="Note" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="exit_permission_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.exit.permission.tree</field>
        <field name="model">hr.masarat.exit.permission</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="total_leave_minutes"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_exit_permission_view" model="ir.actions.act_window">
        <field name="name">طلبات اذن خروج</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.exit.permission</field>
        <field name="view_mode">tree,form</field>
    </record>


    <menuitem
            id="menu_exit_permission"
            name="طلبات اذن خروج"
            parent="hr_masarat_approvals"
            action="action_exit_permission_view"
            sequence="4"/>


</odoo>

