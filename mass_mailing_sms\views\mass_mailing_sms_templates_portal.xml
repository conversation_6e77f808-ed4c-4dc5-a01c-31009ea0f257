<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="blacklist_main" name="Unsubscribed">
        <t t-call="portal.frontend_layout">
            <div class="container mb64">
                <div class="row">
                    <div class="col-lg-6 offset-lg-3">
                        <h3>SMS Subscription</h3>

                        <form t-att-action="'/sms/%s/unsubscribe/%s' % (mailing_id, trace_code)" method="post">
                            <p>Please enter your phone number</p>
                            <div class="form-group row">
                                <label for="sms_number" class="col-sm-2 col-form-label">Number</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" name="sms_number" id="sms_number" t-att-required="true"/>
                                </div>
                            </div>
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="trace_code" t-att-value="trace_code"/>
                            <input type="hidden" name="mailing_id" t-att-value="mailing_id"/>
                            <div class="form-group row">
                                <div class="col-sm-10 offset-sm-2">
                                    <button type="submit" class="btn btn-primary">Unsubscribe me</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="blacklist_number" name="Unsubscribed">
        <t t-call="portal.frontend_layout">
            <div class="container mb64">
                <div class="row">
                    <div class="col-lg-6 offset-lg-3">
                        <h3>SMS Subscription</h3>

                        <div t-if="unsubscribe_error" class="alert alert-danger text-center" role="alert">
                            <p>There was an error when trying to unsubscribe <strong t-esc="sms_number"/></p>
                            <p t-esc="unsubscribe_error"/>
                        </div>
                        <div t-else="" class="alert alert-success text-center" role="status">
                            <t t-if="lists_optout">
                                <p><strong t-esc="sms_number"/> has been successfully removed from</p>
                                <t t-foreach="lists_optout" t-as="list_id">
                                    <strong t-esc="list_id.name"/><br />
                                </t>
                            </t>
                            <p t-else="">
                                <strong t-esc="sms_number"/> has been successfully blacklisted
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
