# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_forum
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-11-27 08:46+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_forum
#: model:mail.template,body_html:website_forum.validation_email
msgid ""
"\n"
"<div summary=\"o_mail_notification\" style=\"padding:0px; width:600px; "
"margin:0 auto; background: #FFFFFF repeat top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px; border-"
"collapse:collapse; background:inherit; color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"center\" width=\"200\" style=\"padding:0px 10px 5px "
"5px; font-size: 12px\">\n"
"                <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; "
"height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div summary=\"o_mail_notification\" style=\"padding:0px; width:600px; "
"margin:0 auto; background: #FFFFFF repeat top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"vertical-align:top; "
"padding:0px; border-collapse:collapse; background:inherit; color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"top\" style=\"width:600px; padding:0px 10px 5px 5px;"
"\">\n"
"                <div>\n"
"                    <hr width=\"100%\" style=\"background-color:"
"rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;"
"min-height:1px;line-height:0;margin:0px auto;padding:0\">\n"
"                </div>\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div style=\"padding:0px; width:600px; max-width:600px; margin:0 auto; "
"background: #FFFFFF repeat top /100%; color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px ;text-"
"align:justify; margin:0 auto; border-collapse:collapse; background:inherit; "
"color:inherit\">\n"
"        <tbody><tr>\n"
"            <td style=\"padding:0px 10px 5px 5px;font-size: 14px;\">\n"
"                <p>Dear ${object.name},</p>\n"
"                <p>You have been invited to validate your email in order to "
"get access to\n"
"                \"${object.company_id.name}\" Q/A Forums.</p>\n"
"                <p>To validate your email, please click on the following "
"link:</p>\n"
"                <a style=\"margin-left: 15px; padding:5px 10px; border-"
"radius: 3px; background-color:#a24689; text-align:center; text-decoration:"
"none; color:#F7FBFD;\" href=\"${ctx.get('token_url')}\">\n"
"                    Validate my account for \"${object.company_id.name}\" Q/"
"A Forums\n"
"                </a>\n"
"                <p>Thanks for your participation!</p>\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:23
#, python-format
msgid ""
" karma is required to perform this action. You can earn karma by having your "
"answers upvoted by the community."
msgstr ""

#. module: website_forum
#: model:mail.template,subject:website_forum.validation_email
msgid "${object.company_id.name} Forums validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "&amp;nbsp;&amp;nbsp;<i class=\"fa fa-times\"/>&amp;nbsp;&amp;nbsp;"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "&amp;times;"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "45% of questions shared"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:30
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Closed]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Deleted]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Offensive]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.users
msgid "<b> badges:</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead\n"
"    either edit the question or add a comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead\n"
"    add a comment on the other answers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not start debates</b>\n"
"    This community Q&amp;A is not a discussion group. Please avoid holding "
"debates in\n"
"    your answers as they tend to dilute the essence of questions and "
"answers. For\n"
"    brief discussions please use commenting facility."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional "
"information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other questions</b>.\n"
"    Instead add a comment indicating <i>\"Possible duplicate\n"
"    of...\"</i>. However, it's fine to include links to other\n"
"    questions or answers providing relevant additional\n"
"    information."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just provide a\n"
"    link a solution</b>. Instead provide the solution\n"
"    description text in your answer, even if it's just a\n"
"    copy/paste. Links are welcome, but should be complementary to\n"
"    answer, referring sources or additional reading."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the "
"solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can\n"
"    search questions by their title or tags.  It’s also OK to\n"
"    answer your own question."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<b>No badge yet!</b><br/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<b>No vote given by you yet!</b>"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Please avoid asking questions that are too subjective\n"
"    and argumentative</b> or not relevant to this community."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on "
"the question or answer, just\n"
"        <b>use the commenting tool.</b> Please remember that you can always "
"<b>revise your answers</b>\n"
"        - no need to answer the same question twice. Also, please <b>don't "
"forget to vote</b>\n"
"        - it really helps to select the best questions and answers!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "<b>Share</b> Something Awesome."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<i class=\"text-muted\">awarded users</i>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<small>profile</small>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"<span class=\"caret\"/>\n"
"                                <span class=\"sr-only\">Select Post</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"<span class=\"sr-only\">Toggle navigation</span>\n"
"                        <span class=\"icon-bar\"/>\n"
"                        <span class=\"icon-bar\"/>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span class=\"text-muted\">(only one answer per question is allowed)</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">bio</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted\">or</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">stats</span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span> on </span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span> • </span>\n"
"                Flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>By </span>"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#, fuzzy
msgid "A new question"
msgstr "Question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
#, fuzzy
msgid ""
"A tag is a label that categorizes your question with other,\n"
"            similar questions. Using the right tags makes it easier for\n"
"            others to find and answer your question. (Hover the mouse to "
"follow/unfollow tag(s))"
msgstr ""
"A tag is a label that categorises your question with other,\n"
"            similar questions. Using the right tags makes it easier for\n"
"            others to find and answer your question."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "About This Community"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Accept <i class=\"fa fa-check\"/>"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:114
#, python-format
msgid "Accept Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_accept
msgid "Accepting an answer"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_active
msgid "Active"
msgstr "Active"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Activity"
msgstr "Activity"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:19
#, python-format
msgid "Add Content"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Add a Comment"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:25
#, python-format
msgid "Add page in menu"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:187
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "All"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_bump
msgid "Allow Bump"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:94
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#, python-format
msgid "Answer"
msgstr "Answer"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:498
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:108
#, python-format
msgid "Answer Posted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_accepted
msgid "Answer accepted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_flagged
msgid "Answer flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer
msgid "Answer questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_child_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Answers"

#. module: website_forum
#: selection:forum.post,post_type:0
msgid "Article"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask Your Question"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:53
#: model_terms:ir.ui.view,arch_db:website_forum.header
#, python-format
msgid "Ask a Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_ask
msgid "Ask questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_post
msgid "Ask questions without validation"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:56
#, python-format
msgid "Ask the question in this forum by clicking on the button."
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_create_date
msgid "Asked on"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Asked:"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_new
msgid "Asking a question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Author"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Avoid unnecessary introductions (Hi,... Please... Thanks...),"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Back to"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:322
#: code:addons/website_forum/controllers/main.py:389
#, python-format
msgid "Bad Request"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "Badge \""
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_badge_ids
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Badges"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"            you receive badges for being especially helpful. Badges\n"
"            appear on your profile page, and your posts."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Biography"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_bronze_badge
msgid "Bronze badges count"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_bump_date
msgid "Bumped on"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid "By sharing you answer, you will get additional"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_accept
msgid "Can Accept"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_answer
msgid "Can Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_ask
msgid "Can Ask"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_post
msgid "Can Automatically be Validated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_close
msgid "Can Close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_comment
msgid "Can Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_comment_convert
msgid "Can Convert to Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_downvote
msgid "Can Downvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_edit
msgid "Can Edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_flag
msgid "Can Flag"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_moderate
msgid "Can Moderate"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_unlink
msgid "Can Unlink"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_upvote
msgid "Can Upvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_view
msgid "Can View"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_retag
msgid "Change question tags"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Check available badges"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "City"
msgstr "City"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:44
#, python-format
msgid "Click <em>Continue</em> to create the forum."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:117
#, python-format
msgid "Click here to accept this answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate to the "
"forum."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get bad question samples"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get good question titles"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:105
#, python-format
msgid "Click to post your answer."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:85
#, python-format
msgid "Click to post your question."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:46
#: selection:forum.post,state:0
#, python-format
msgid "Close"
msgstr "Close"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:123
#, python-format
msgid "Close Tutorial"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_close_all
msgid "Close all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_close_own
msgid "Close own posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_uid
msgid "Closed by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_date
msgid "Closed on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_name
msgid "Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Comment"
msgstr "Comment"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_all
msgid "Comment all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_own
msgid "Comment own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "Comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_website_message_ids
msgid "Comments on forum post"
msgstr ""

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_1
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
msgid "Completed own biography"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:120
#, python-format
msgid "Congratulations"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:122
#, python-format
msgid ""
"Congratulations! You just created and post your first question and answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Congratulations! Your email has just been validated. You may now participate "
"to our forums."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:50
#: code:addons/website_forum/static/src/js/website_tour_forum.js:91
#: code:addons/website_forum/static/src/js/website_tour_forum.js:111
#, python-format
msgid "Continue"
msgstr "Continue"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_accept
msgid "Convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_is_correct
msgid "Correct"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_is_correct
msgid "Correct answer or answer accepted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country"
msgstr "Country"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country..."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_create_date
msgid "Create Date"
msgstr "Create Date"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:40
#, python-format
msgid "Create Forum"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:14
#, python-format
msgid "Create a Question!"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:11
#, python-format
msgid "Create a question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_tag_create
#, fuzzy
msgid "Create new tags"
msgstr "Created on"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_create_uid
msgid "Created by"
msgstr "Created by"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_create_date
msgid "Created on"
msgstr "Created on"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_default_order
msgid "Default Order"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_default_post_type
msgid "Default Post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_unlink_all
msgid "Delete all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_unlink_own
msgid "Delete own posts"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_description
msgid "Description"
msgstr "Description"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Design Welcome Message"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
msgid "Discussion"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_discussion
msgid "Discussions"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_display_name
msgid "Display Name"
msgstr "Display Name"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_user_bio
msgid "Display detailed user biography"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_downvote
msgid "Downvote"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit"
msgstr "Edit"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Edit Profile"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Edit Your Bio"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Edit Your Previous Answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_all
msgid "Edit all posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_own
msgid "Edit own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Edit your Link"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_editor
msgid "Editor Features: image and links"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Email"
msgstr "E-mail"

#. module: website_forum
#: model:ir.model,name:website_forum.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:37
#, python-format
msgid "Enter a name for your new forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_favourite_count
msgid "Favorite Count"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_favourite_ids
msgid "Favourite"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Favourite Questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Filter on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_relevancy_post_vote
msgid "First Relevance Parameter"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_flag
msgid "Flag a post as offensive"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Flagged"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_flag_user_id
msgid "Flagged by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Followed"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Followed Questions"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your\n"
"    input will be upvoted. On the other hand if the answer is misleading - "
"it will\n"
"    be downvoted. Each vote in favor will generate 10 points, each vote "
"against\n"
"    will subtract 10 points. There is a limit of 200 points that can be "
"accumulated\n"
"    for a question or answer per day. The table given at the end explains "
"reputation point\n"
"    requirements for each type of moderation task."
msgstr ""

#. module: website_forum
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#: model:website.menu,name:website_forum.menu_questions
msgid "Forum"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge_level
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge_user_level
msgid "Forum Badge Level"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_users_form_forum
msgid "Forum Karma"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:33
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_name
#, python-format
msgid "Forum Name"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Forum Settings"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header_footer_custom
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge
msgid "Gamification badge"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification challenge"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge_user
msgid "Gamification user badge"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:74
#, python-format
msgid "Give Tag"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:63
#, python-format
msgid "Give your question title."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_gold_badge
msgid "Gold badges count"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Group By"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_faq
msgid "Guidelines"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_uid_has_answered
msgid "Has Answered"
msgstr ""

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Help"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If this approach is not for you, please respect the community and use Google"
"+\n"
"    communities instead."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If you fit in one of these example or if your motivation for asking the\n"
"    question is “I would like to participate in a discussion about ______”, "
"then\n"
"    you should not be asking here but on our mailing lists.\n"
"    However, if your motivation is “I would like others to explain ______ to "
"me”,\n"
"    then you are probably OK."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:78
#, python-format
msgid "Insert tags related to your question."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_user_favourite
msgid "Is Favourite"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_has_validated_answer
msgid "Is answered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_display_biography
msgid "Is the author's biography visible from his post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "It appears your email has not been verified."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_karma
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Karma"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_close
msgid "Karma to close"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_comment
msgid "Karma to comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_edit
msgid "Karma to edit"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_unlink
msgid "Karma to unlink"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Keep Informed"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag___last_update
msgid "Last Modified on"
msgstr "Last Modified on"

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Last Updated"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_write_uid
msgid "Last Updated by"
msgstr "Last Updated by"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_write_date
msgid "Last Updated on"
msgstr "Last Updated on"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Last updated:"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:15
#, python-format
msgid "Let's go through the first steps to create a new question."
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_post_type:0
msgid "Link"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_link
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Links"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_mail_message
#, fuzzy
msgid "Message"
msgstr "Messages"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_moderate
msgid "Moderate posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation Tools"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "More over:"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Most Voted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_user_vote
msgid "My Vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_name
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Name"
msgstr "Name"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Discussion"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:14
#: code:addons/website_forum/static/src/js/website_tour_forum.js:26
#: model_terms:ir.ui.view,arch_db:website_forum.content_new_forum
#, python-format
msgid "New Forum"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:47
#, python-format
msgid "New Forum Created"
msgstr ""

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:88
#, python-format
msgid "New Question Created"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "New Topic"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Newest"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_dofollow
msgid "Nofollow links"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:833
#: code:addons/website_forum/models/forum.py:853
#, python-format
msgid "Not allowed to vote for its own post"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:897
#, python-format
msgid "Not enough karma to create a new Tag"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:488
#, python-format
msgid "Not enough karma to retag."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_posts_count
msgid "Number of Posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_views
msgid "Number of Views"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_child_count
msgid "Number of answers"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_count_flagged_posts
msgid "Number of flagged posts"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "On"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "On average,"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Options"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Orders"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "Our forums"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Pending"
msgstr "Pending"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "People"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_plain_content
msgid "Plain Content"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Please enter a descriptive question (should finish by a '?')"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Post"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:101
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#, python-format
msgid "Post Answer"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Comment"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_website_message_ids
msgid "Post Messages"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:81
#, python-format
msgid "Post Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Post Title"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Post Types"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Post Your Topic"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:432
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Provide enough details and, if possible, give an example."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Public profile"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:48
#, python-format
msgid ""
"Put this question back in the top list by sharing it on social networks."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:98
#, python-format
msgid "Put your answer here."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:71
#, python-format
msgid "Put your question here."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header_footer_custom
msgid "Q&amp;amp;A"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:66
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_parent_id
#, python-format
msgid "Question"
msgstr "Question"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:501
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:59
#, python-format
msgid "Question Title"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_downvote
msgid "Question downvoted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Question has accepted answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Question tools"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_upvote
msgid "Question upvoted"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr ""

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Questions"
msgstr "Questions"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:515
#, python-format
msgid "Re: %s"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Read Guidelines"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Real name"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_reason_id
msgid "Reason"
msgstr "Reason"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_reason_type
msgid "Reason Type"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "Reason:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Redirect to external link"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reject <i class=\"fa fa-times\"/>"
msgstr ""

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_relevancy
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Relevance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reply"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_self_reply
msgid "Reply to own question"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the question list."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_moderator_id
msgid "Reviewed by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save"
msgstr "Save"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#, fuzzy
msgid "See question"
msgstr "Question"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Seen:"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:29
#, python-format
msgid "Select this menu item to create a new forum."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Set a clear, explicit and concise question title\n"
"                (check"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"Share an awesome link. Your post will appear in the 'Newest' top-menu.\n"
"            If the community vote on your post, it will get traction by "
"being promoted\n"
"            in the homepage."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:39
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page "
"and attract more visitors."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_share
msgid "Sharing Options"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_silver_badge
msgid "Silver badges count"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:16
#, python-format
msgid "Skip It"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:66
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:114
#, python-format
msgid "Sorry you must be logged to vote"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:145
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:109
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sort by"
msgstr "Sort by"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:16
#, python-format
msgid "Start Tutorial"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Stats"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_state
msgid "Status"
msgstr "Status"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Submit a Link"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr ""

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.ui.menu,name:website_forum.menu_forum_tag
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr ""

#. module: website_forum
#: sql_constraint:forum.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_tag_ids
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Tags"
msgstr "Labels"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:9
#, python-format
msgid "Thanks for posting!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The"
msgstr "The"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The flagged queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"The goal of this site is create a relevant knowledge base that would answer\n"
"    questions related to Odoo."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The offensive queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The validation queue is empty."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced "
"users of\n"
"    this site in order to improve the overall quality of the knowledge base "
"content.\n"
"    Such privileges are granted based on user karma level: you will be able "
"to do the same\n"
"    once your karma gets high enough."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"This community is for professional and enthusiast users,\n"
"    partners and programmers. You can ask questions about:"
msgstr ""

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:408
#, python-format
msgid "This forum does not allow %s"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:49
#, python-format
msgid "This page contains all the information related to the new forum."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:110
#, python-format
msgid "This page contains the newly created questions and its answers."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:90
#, python-format
msgid "This page contains the newly created questions."
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:76
#, python-format
msgid "This post can not be flagged"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:71
#, python-format
msgid "This post is already flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "This profile is private!"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Threads"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_name
msgid "Title"
msgstr "Title"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:322
#: code:addons/website_forum/controllers/main.py:389
#, python-format
msgid "Title should not be empty."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_recipient_id
msgid "To"
msgstr "To"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "To improve your chance getting an answer:"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking\n"
"    subjective questions where …"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Trending"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_post_type
msgid "Type"
msgstr "Type"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_content_link
msgid "URL"
msgstr "URL"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_content_link
msgid "URL of Link Articles"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "URL to Share"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_unlink_all
msgid "Unlink all comments"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_unlink_own
msgid "Unlink own comments"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Update"
msgstr "Update"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_write_date
msgid "Update on"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_write_uid
msgid "Updated by"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_upvote
msgid "Upvote"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr ""

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:22
#, python-format
msgid ""
"Use this button to create a new forum like any other document (page, menu, "
"products, event, ...)."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_user_id
msgid "User"
msgstr "User"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "Users"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_question
msgid ""
"Users can answer only once per question. Contributors can edit answers and "
"mark the right ones."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:777
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#, python-format
msgid "View"
msgstr "View"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "View Your Badges"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_vote
msgid "Vote"
msgstr ""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Votes"
msgstr ""

#. module: website_forum
#: selection:forum.post,state:0
msgid "Waiting Validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"We keep a high level of quality in showcased posts, around 20% of the "
"submited\n"
"            posts will be featured."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Website"
msgstr "Website"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_welcome_message
msgid "Welcome Message"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What kinds of questions can I ask here?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my answers?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my questions?"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some\n"
"    points, which are called \"karma points\". These points serve as a "
"rough\n"
"    measure of the community trust to him/her. Various moderation tasks are\n"
"    gradually assigned to the users based on those points."
msgstr ""

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_link
msgid "When clicking on the post, it redirects to an external link"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "Why can other people edit my questions/answers?"
msgstr ""

#. module: website_forum
#: code:addons/website_forum/models/forum.py:141
#, python-format
msgid "You cannot choose %s as default post since the forum does not allow it."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"You should only ask practical, answerable questions based\n"
"    on actual problems that you face. Chatty, open-ended\n"
"    questions diminish the usefulness of this site and push\n"
"    other questions off the front page."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Your Discussion Title..."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Your Question Title..."
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "accept any answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "and"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "back to post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "bad examples"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "bronze"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by activity date"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most answered"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most voted"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by newest"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by relevance"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "close any posts"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "contains offensive or malicious remarks"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any question or answer"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete own comment"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "downvote"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "duplicate post"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "e.g. https://www.odoo.com"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "edit any post, view offensive flags"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "flag offensive, close own questions"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "flagged"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "follower(s)"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "gold"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "good examples"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "has been closed"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:36
#, python-format
msgid "here"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to configure or customize Odoo to specific business needs,"
msgstr "how to configure or customise Odoo to specific business needs,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to develop modules for your own need,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to install Odoo on a specific infrastructure,"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "inappropriate and unacceptable statements"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "insert text link, upload files"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "insulting and offensive language"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "karma"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid "karma points"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "last connection"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "location"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "member since"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "not a real post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "not relevant or out dated"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "off-topic or not relevant"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "post"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "racist and hate speech"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "received this badge:"
msgstr ""

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "silver"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "spam or advertising"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "specific questions about Odoo service offers, etc."
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "threatening language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "time"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "times"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "too localized"
msgstr "too localised"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "too subjective and argumentative"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "upvote, add comments"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "user"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "users"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "views"
msgstr ""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "violent language"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "votes"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "waiting for validation"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "website"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "your biography can be seen as tooltip"
msgstr ""

#~ msgid "Action Needed"
#~ msgstr "Action Needed"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Date of the last message posted on the record."

#~ msgid "Followers"
#~ msgstr "Followers"

#~ msgid "Followers (Channels)"
#~ msgstr "Followers (Channels)"

#~ msgid "Followers (Partners)"
#~ msgstr "Followers (Partners)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "If checked new messages require your attention."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "If checked, new messages require your attention."

#~ msgid "Is Follower"
#~ msgstr "Is Follower"

#~ msgid "Last Message Date"
#~ msgstr "Last Message Date"

#~ msgid "Messages and communication history"
#~ msgstr "Messages and communication history"

#~ msgid "Number of Actions"
#~ msgstr "Number of Actions"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Number of messages which requires an action"

#~ msgid "Number of unread messages"
#~ msgstr "Number of unread messages"

#~ msgid "Unread Messages"
#~ msgstr "Unread Messages"

#~ msgid "Unread Messages Counter"
#~ msgstr "Unread Messages Counter"

#~ msgid "Website Messages"
#~ msgstr "Website Messages"

#~ msgid "Website communication history"
#~ msgstr "Website communication history"

#~ msgid "Website meta description"
#~ msgstr "Website meta description"

#~ msgid "Website meta keywords"
#~ msgstr "Website meta keywords"

#~ msgid "Website meta title"
#~ msgstr "Website meta title"
