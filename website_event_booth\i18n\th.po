# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# <PERSON>, 2021
# <PERSON>ichano<PERSON> Jamwutthipreecha, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Wichanon Jamwutthipreecha, 2021\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                    <span>ขออภัยหลายบูธขายหมดแล้ว โปรดเปลี่ยนตัวเลือกของคุณก่อนที่จะยืนยันอีกครั้ง</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event booths\"/><em>Configure Booths</em>"
msgstr ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"กำหนดค่าบูธอีเวนต์\"/><em>กำหนดค่าบูธ</em>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">ขายหมดแล้ว</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booths</span>"
msgstr "<span>จองบูธของฉัน</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>อีเมล</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>ชื่อ</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid ""
"<span>This event is finished. It's no longer possible to book a "
"booth.</span>"
msgstr "<span>อีเวนต์นี้เสร็จสิ้น ไม่สามารถจองบูธได้อีกต่อไป</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<strong>Contact Details</strong>"
msgstr "<strong>รายละเอียดการติดต่อ</strong>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Book my Booths"
msgstr "จองบูธของฉัน"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "รายการเมนูบูธ"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "ลงทะเบียนบูธ"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_complete
msgid "Booth Registration completed!"
msgstr "ลงทะเบียนบูธเสร็จแล้ว!"

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booth registration failed. Please try again."
msgstr "การลงทะเบียนบูธล้มเหลว กรุณาลองอีกครั้ง"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Booths"
msgstr "บูธ"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "บูธบนเว็บไซต์"

#. module: website_event_booth
#: code:addons/website_event_booth/controllers/event_booth.py:0
#, python-format
msgid "Booths should belong to the same category."
msgstr "บูธควรอยู่ในประเภทเดียวกัน"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "ติดต่อเรา"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "อีเวนต์"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "เมนูบูธอีเวนต์"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "เมนูบูธอีเวนต์"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "เทมเพลตอีเวนต์"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "แผนที่จัดแสดงนิทรรศการ"

#. module: website_event_booth
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
#, python-format
msgid "Get A Booth"
msgstr "รับบูธ"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "ประเภทเมนู"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Mobile"
msgstr "มือถือ"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "โทรศัพท์"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "ขออภัย บูธขายหมดแล้ว"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration,"
msgstr "อีเวนต์นี้ไม่เปิดให้ลงทะเบียนเข้าร่วมงาน"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "View Floor Plan"
msgstr "ดูแผนผังชั้น"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "เมนูเว็บไซต์อีเวนต์"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "check our"
msgstr "ตรวจสอบ"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "for this event."
msgstr "สำหรับอีเวนต์นี้"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "หากคุณมีคำถามใด ๆ"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "รายการอีเวนต์ในอนาคต"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "you can"
msgstr "คุณสามารถ"
