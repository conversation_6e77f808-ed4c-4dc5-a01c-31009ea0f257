# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:20+0000\n"
"Last-Translator: ILMIR <<EMAIL>>, 2021\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Группа налогов: налог представляет собой вложенный набор налогов.\n"
"- Фиксированный: сумма налога остается неизменной независимо от цены.\n"
"- Процент от цены: сумма налога составляет % от цены:\n"
"например, 100 * (1 + 10%) = 110 (не включая цену)\n"
"например, 110 / (1 + 10%) = 100 (включая цену)\n"
"- Процент от включенной суммы налога: Сумма налога представляет собой деление цены:\n"
"например, 180 / (1-10%) = 200 (не включая цену)\n"
"например, 200 * (1-10%) = 180 (включая цену)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_applicable
msgid "Applicable Code"
msgstr "Применимый код"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Вычислите сумму налога, установив переменную 'результат'.\n"
"\n"
": параметр base_amount: float, фактическая сумма, на которую применяется налог\n"
": параметр price_unit: float\n"
": параметр количество: float\n"
": параметр компании: res.company набор записей singleton\n"
": параметр продукта: product.product набор записей singleton или Нет\n"
": параметр партнера: res.partner набор записей singleton или Нет"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Вычислите сумму налога, установив переменную 'результат'.\n"
"\n"
": параметр base_amount: float, фактическая сумма, на которую применяется налог\n"
": параметр price_unit: float\n"
": параметр количество: float\n"
": параметр продукта: product.product набор записей singleton или Нет\n"
": параметр партнера: res.partner набор записей singleton или Нет"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Определите, будет ли налог применяться, установив переменную 'результат' в значение Истина или Ложь.\n"
"\n"
": параметр price_unit: float\n"
": параметр продукта: float\n"
": параметр компании: res.company набор записей singleton\n"
": параметр продукта: product.product набор записей singleton или Нет\n"
": параметр партнера: res.partner набор записей singleton или Нет"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax_template__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Определите, будет ли налог применяться, установив переменную 'результат' в значение Истина или Ложь.\n"
"\n"
": параметр price_unit: float\n"
": параметр продукта: float\n"
": параметр продукта: product.product набор записей singleton или Нет\n"
": параметр партнера: res.partner набор записей singleton или Нет"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax_template__amount_type__code
msgid "Python Code"
msgstr "Код на Python"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Налог"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax_template__amount_type
msgid "Tax Computation"
msgstr "Расчет Налога"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax_template
msgid "Templates for Taxes"
msgstr "Шаблоны для налогов"
