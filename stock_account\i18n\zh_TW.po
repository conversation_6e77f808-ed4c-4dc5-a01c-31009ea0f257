# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " 產品成本從 %(previous)s 更新為 %(new_cost)s。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(product)s"
msgstr "%(user)s 已將成本從 %(previous)s 更改為 %(new_price)s - %(product)s"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - "
"%(product)s"
msgstr "%(user)s 將庫存估值從 %(previous)s 更改為 %(new_value)s - %(product)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 font-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 font-italic\">使用負的增加值來記錄產品價值的減少</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr "<b>設置特定的其他輸入/輸出帳戶</b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>產品</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>數量</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>序號/批次</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "項目表模版"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "會計憑證"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "會計庫存屬性"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "會計日期"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "會計分錄"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "會計資訊"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "添加手動評價"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr "在產品價值中添加額外費用（運輸、海關等）。"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "價值調整"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "價值調整"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr "影響接收操作的落地成本, 並在產品之間拆分以更新成本價。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_id
msgid "Analytic Account Line"
msgstr "分析帳戶項目"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "自動"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "平均成本(AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "取消"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr "找不到產品%s的庫存入庫項目。 在處理此操作之前，您必須在產品類別或產品上進行一個定義。"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr "找不到產品%s的庫存出庫庫項目。 在處理此操作之前，您必須在產品類別或產品上進行定義。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr "更改您的成本方法是一個重要的更改, 將影響您的庫存評估。是否確實要進行此更改？"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
msgid "Company"
msgstr "公司"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr "配置錯誤。 請在產品或其類別上配置價格差異項目來處理此操作。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
msgid "Costing Method"
msgstr "成本方法"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Costing method change for product category %s: from %s to %s."
msgstr "產品類別 %s 的成本計算方法更改：從 %s 更改為 %s。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "對方科目"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"所有進貨庫存移動的對應日記帳項目將在此科目中過帳，除非有特定的評價科目\n"
"                設置在來源位置。這是此類別中所有產品的預設值。也可以直接設置在每個產品上。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "國家/地區代碼"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "創立者"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "建立於"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "幣別"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "目前數量"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "目前值"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Date"
msgstr "日期"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr "在自動清點估價的情況下建立會計分錄的日期。如果為空，將使用庫存日期。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "所有庫存商品的預設單位。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "說明"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "在發票上顯示批次和序號"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "在發票上顯示序號和批號"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Documentation"
msgstr "系統使用說明"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Due to a change of product category (from %s to %s), the costing method"
"                                has changed for product template %s: from %s"
" to %s."
msgstr "由於產品類別變更（從 %s 更改為 %s），產品範本 %s 的成本計算方法已更改：從 %s 更改為 %s。"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "先進先出(FIFO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "分組由..."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "盤點位置"

#. module: stock_account
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/__init__.py:0
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "庫存計價"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Is Anglo Saxon Line"
msgstr "為盎格魯撒克遜明細"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "日記帳"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
msgid "Journal Entry"
msgstr "日記帳分錄"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "日記帳明細"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "落地成本"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer____last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "已連結至"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots &amp; Serial numbers will appear on the invoice"
msgstr "批號和序號將顯示在發票上"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "手動"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: %s."
msgstr "手動庫存估值：%s。"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: No Reason Given."
msgstr "手動庫存估值：沒有給出任何理由。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"手動：盤點存貨的會計傳票不會自動過帳。\n"
"自動：當產品進入或離開公司時，自動建立一個會計傳票來評估庫存。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "新值"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "按數量計算的新值"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_product_product_replenishment
msgid "On Hand Value"
msgstr "目前庫存價值"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "其他資訊"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "商品"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
msgid "Product Category"
msgstr "產品分類"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "產品移動(移庫明細)"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
#, python-format
msgid "Product Revaluation"
msgstr "產品重估"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "產品模板"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Product value manually modified (from %s to %s)"
msgstr "手動修改產品價值（從 %s 到 %s）"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__quantity
msgid "Quantity"
msgstr "數量"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
msgid "Quantity Svl"
msgstr "Svl數量"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "數量"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "原因"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "重估原因"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "相關產品"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "剩餘數量"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "剩餘價值"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "退回揀貨"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "退料明細"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Revaluation of %s"
msgstr "%s 的重估"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "重估"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__categ_id
msgid "Select category for the current product"
msgstr "為目前產品選擇一個類別"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "標準價格"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"標準價格：產品按照產品定義的標準成本進行估價。\n"
"         平均成本（AVCO）：產品以加權平均成本估值。\n"
"         先進先出（First In First Out，簡稱FIFO）：假設那些先進入公司的產品估值，它們也會先離開。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "庫存進貨項目"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "庫存日記帳"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "庫存移動"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "庫存出貨項目"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "庫存數量歷史"

#. module: stock_account
#: model:ir.model,name:stock_account.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "庫存補貨報告"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "庫存請求進行盤點"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
msgid "Stock Valuation"
msgstr "庫存計價"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "庫存計價項目"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "庫存計價項目（入向）"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "庫存計價項目（出向）"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "庫存估值層"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_account_move_line__is_anglo_saxon_line
msgid "Technical field used to retrieve the anglo-saxon lines."
msgstr "用於檢索盎格魯-薩克遜明細的技術欄位。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO 國家代碼使用兩個字元。\n"
" 您可以使用此欄位進行快速搜尋。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr "庫存輸入和/或輸出科目不能與庫存估價科目相同。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr "該操作會導致建立您沒有存取權限的日記帳分錄。"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "價值調整對庫存估值沒有任何影響"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr "移動明細的狀態不一致: 有些正在進貨, 其他的則在出貨。"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr "移動線處於不一致的狀態：他們在一個單一的步驟中作為一個公司的內部公司，而他們應該通過公司間中轉地點。"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr "移動名細處於不一致的狀態:它們不屬於同一個來源或目的地之公司。"

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
msgid ""
"There is no valuation layers. Valuation layers are created when some product"
" moves should impact the valuation of the stock."
msgstr "沒有估價層。當某些產品移動會影響庫存的估值時，將建立估價層。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Value"
msgstr "總值"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "調撥"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr "觸發相關銷售訂單/採購訂單中已交貨/已收貨數量的減少"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "單位價值"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "單位"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "更新 SO/PO 上的數量"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"用於及時庫存計價。當設定在一個虛擬庫位（非內部類型）時，這項目將用於記錄從內部庫位調撥產品的值，而非替代此產品的一般出庫項目。它不影響內部庫位。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"用於及時庫存計價。當設定在一個虛擬庫位（非內部類型）時，這項目將用於記錄從內部庫位調撥產品的值，而非替代此產品的一般出庫項目。它不影響內部庫位。"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
msgid "Valuation"
msgstr "計價"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Valuation method change for product category %s: from %s to %s."
msgstr "產品類別 %s 的庫存計價方法更改：從 %s 更改為 %s。"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "值"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
msgid "Value Svl"
msgstr "Svl值"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr "在產品上啟用自動庫存估價時，此帳戶將保留產品的當前價值。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"執行自動庫存評估時，所有傳出庫存變動的對應日記帳物料將過帳在此項目中，\n"
"                除非在目標位置設置了特定的估價帳戶。這是此類別中所有產品的預設值。\n"
"                也可以直接在每個產品上設置。"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr "執行自動庫存評估時，這是會計日誌，其中處理庫存移動時將自動過帳分錄。"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr "用於重新評估產品庫存的作業模型"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with a standard cost method."
msgstr "您不能使用標準成本方法重估產品。"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "您不能重估具有空庫存或負庫存的產品。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr "您無法在自動評價中更新產品成本，因為這會導致建立您沒有存取權限的日記帳分錄。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr "您沒有在產品類別上定義任何輸入估價項目。在處理此操作之前，必須定義一個。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr "您沒有在您的產品類別中定義任何庫存輸入帳戶。您必須在處理此操作之前定義一個。"

#. module: stock_account
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr "您沒有在產品類別上定義的任何庫存日記帳, 請檢查是否已安裝了會計項目表。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr "您沒有在您的產品類別中定義任何庫存評估帳戶。 處理此操作之前，您必須定義一個。"

#. module: stock_account
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "You must set a counterpart account on your product category."
msgstr "您必須在您的產品類別上設置一個對應賬戶。"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "by"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "為"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "位置"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"'The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
