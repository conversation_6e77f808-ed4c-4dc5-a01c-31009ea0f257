# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract
# 
# Translators:
# Bayarkhuu Bataa, 2021
# hish, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 486aab72cd7cb5d98a82d40b0db4c5f2_4397374 <25b041e3e1ecd3f217ca34118196bd3e_836923>, 2021
# <AUTHOR> <EMAIL>, 2021
# Otgonchi<PERSON><PERSON> <<EMAIL>>, 2021
# tumenjargal had<PERSON>r <<EMAIL>>, 2021
# <PERSON>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-02 15:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Baskhuu Lodoikhuu <<EMAIL>>, 2023\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_count
msgid "# Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_resource_calendar__contracts_count
msgid "# Contracts using it"
msgstr ""

#. module: hr_contract
#: code:addons/hr_contract/report/hr_contract_history.py:0
#, python-format
msgid "%s's Contracts History"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('calendar_mismatch', '=', False), ('state', '!=', 'open')]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                                    </span>\n"
"                                    <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                        Calendar Mismatch : The employee's calendar does not match this contract's calendar. This could lead to unexpected behaviors.\n"
"                                    </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span attrs=\"{'invisible': [('calendar_mismatch', '=', False)]}\" class=\"fa fa-exclamation-triangle text-danger o_calendar_warning pl-3\">\n"
"                            </span>\n"
"                            <span class=\"o_calendar_warning_tooltip text-danger\">\n"
"                                Calendar Mismatch : The employee's calendar does not match its current contract calendar. This could lead to unexpected behaviors.\n"
"                            </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\" attrs=\"{'invisible' : [('contract_warning', '=', False)]}\" title=\"In Contract Since\">\n"
"                                    In Contract Since\n"
"                                </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-danger\">\n"
"                                    Contracts\n"
"                                </span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_hr_employee_view_form2
msgid ""
"<span class=\"o_stat_text text-success\" attrs=\"{'invisible' : "
"[('contract_warning', '=', True)]}\" title=\"In Contract Since\"> In "
"Contract Since</span>"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/ сар</span>"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction
msgid "Action Needed"
msgstr "Үйлдэл шаардсан"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__active
msgid "Active"
msgstr "Идэвхитэй"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__active_employee
msgid "Active Employee"
msgstr "Идэвхитэй ажилчид"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Active Employees"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_ids
msgid "Activities"
msgstr "Ажилбар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Ажилбарын тайлбар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__activity_state
msgid "Activity State"
msgstr "Ажилбарын төлөв"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ажилбарын төрлийн зураг"

#. module: hr_contract
#: model:res.groups,name:hr_contract.group_hr_contract_manager
msgid "Administrator"
msgstr "Администратор"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"An employee can only have one contract at the same time. (Excluding Draft and Cancelled contracts).\n"
"\n"
"Employee: %(employee_name)s"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Archived"
msgstr "Архивласан"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_attachment_count
msgid "Attachment Count"
msgstr "Хавсралтын тоо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_res_users__bank_account_id
msgid "Bank Account Number"
msgstr "Банкны дансны дугаар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__calendar_mismatch
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__calendar_mismatch
msgid "Calendar Mismatch"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__cancel
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__cancel
msgid "Cancelled"
msgstr "Цуцлагдсан"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_id
msgid "Company"
msgstr "Компани"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__vehicle
#: model:ir.model.fields,field_description:hr_contract.field_res_users__vehicle
msgid "Company Vehicle"
msgstr "Компанийн тээврийн хэрэгсэл"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__company_country_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__company_country_id
msgid "Company country"
msgstr "Компаны улс"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract"
msgstr "Гэрээ"

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid ""
"Contract %(contract)s: start date (%(start)s) must be earlier than contract "
"end date (%(end)s)."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contracts_count
msgid "Contract Count"
msgstr "Гэрээний тоо"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Details"
msgstr "Гэрээний дэлгэрэнгүй"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_departure_wizard_view_form
msgid "Contract End Date"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract History"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Contract Name"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Reference"
msgstr "Гэрээний код"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Contract Start Date"
msgstr "Гэрээ эхлэх огноо"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_type_id
msgid "Contract Type"
msgstr "Гэрээний төрөл"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__contract_wage
msgid "Contract Wage"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_warning
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_view_search
msgid "Contract Warning"
msgstr "Гэрээний анхааруулга"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_pending
#: model:mail.message.subtype,description:hr_contract.mt_department_contract_pending
msgid "Contract about to expire"
msgstr "Хугацаа нь дуусах дөхсөн гэрээ"

#. module: hr_contract
#: model:mail.message.subtype,description:hr_contract.mt_contract_close
msgid "Contract expired"
msgstr "Хугацаа нь дууссан гэрээ"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_contract_history
msgid "Contract history"
msgstr "Гэрээний түүх"

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_department_contract_pending
msgid "Contract to Renew"
msgstr "Сунгах шаардлагатай гэрээ"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.action_hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_form_action
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__contract_ids
#: model:ir.ui.menu,name:hr_contract.hr_menu_contract_history
#: model:ir.ui.menu,name:hr_contract.menu_human_resources_configuration_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_activity
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_tree
#: model_terms:ir.ui.view,arch_db:hr_contract.resource_calendar_view_form
msgid "Contracts"
msgstr "Гэрээнүүд"

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_to_review_view_list_action
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Contracts to Review"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__under_contract_state
msgid "Contractual Status"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__country_id
msgid "Country"
msgstr "Улс"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__country_code
msgid "Country Code"
msgstr "Улсын код"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Create"
msgstr "Үүсгэх"

#. module: hr_contract
#: model_terms:ir.actions.act_window,help:hr_contract.action_hr_contract
msgid "Create a new contract"
msgstr "Шинэ гэрээ бүртгэх"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__currency_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__currency_id
msgid "Currency"
msgstr "Валют"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Current Contract"
msgstr "Идэвхитэй гэрээ"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Current Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_employee__contract_id
msgid "Current contract of the employee"
msgstr "Ажилчдын одоогийн гэрээ"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Currently Under Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__default_resource_calendar_id
msgid "Default Working Hours"
msgstr "Анхдагч ажлын цаг"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Delete"
msgstr "Устгах"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__department_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__department_id
msgid "Department"
msgstr "Алба нэгж"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Ажлаас чөлөөлөлт"

#. module: hr_contract
#: code:addons/hr_contract/wizard/hr_departure_wizard.py:0
#, python-format
msgid ""
"Departure date can't be earlier than the start date of current contract."
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Dropdown menu"
msgstr "Эвхэгддэг цэс"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_kanban
msgid "Edit Contract"
msgstr "Гэрээ засварлах"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employed"
msgstr "Ажилтай"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__employee_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__employee_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Employee"
msgstr "Ажилтан"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__contract_ids
msgid "Employee Contracts"
msgstr "Ажилтны хаяг"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Employee Information"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_res_users__bank_account_id
msgid "Employee bank salary account"
msgstr " Ажилтны цалингийн данс"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__wage
msgid "Employee's monthly gross wage."
msgstr "Ажилтны сарын үндсэн цалин."

#. module: hr_contract
#: model:ir.actions.act_window,name:hr_contract.hr_contract_history_view_list_action
msgid "Employees"
msgstr "Хүний нөөц"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_end
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_end
msgid "End Date"
msgstr "Дуусах огноо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_end
msgid "End date of the contract (if it's a fixed-term contract)."
msgstr "Гэрээний дуусах хугацаа (хэрэв байгын хугацаат гэрээ биш бол)."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__trial_date_end
msgid "End date of the trial period (if there is one)."
msgstr "Туршилтын хугацааны дуусах огноо (хэрэв туршилтын нөхцөлтэй бол)."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__trial_date_end
msgid "End of Trial Period"
msgstr "Туршилтын хугацааны төгсгөл"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__close
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__close
#: model:mail.message.subtype,name:hr_contract.mt_contract_close
msgid "Expired"
msgstr "Хугацаа дууссан"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee__first_contract_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_employee_public__first_contract_date
msgid "First Contract Date"
msgstr "Анхны гэрээний огноо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_follower_ids
msgid "Followers"
msgstr "Дагагчид"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_partner_ids
msgid "Followers (Partners)"
msgstr "Дагагчид (Харилцагчид)"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon ж.ш. fa-tasks"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Future Activities"
msgstr "Ирээдүйн үйл ажиллагаанууд"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__done
msgid "Green"
msgstr "Ногоон"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__normal
msgid "Grey"
msgstr "Саарал"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: hr_contract
#: model:ir.actions.server,name:hr_contract.ir_cron_data_contract_update_state_ir_actions_server
#: model:ir.cron,cron_name:hr_contract.ir_cron_data_contract_update_state
#: model:ir.cron,name:hr_contract.ir_cron_data_contract_update_state
msgid "HR Contract: update state"
msgstr "HR гэрээ: төлөв шинэчлэх"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__hr_responsible_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__hr_responsible_id
msgid "HR Responsible"
msgstr "ХН ажлын хэсэг хариуцагч"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__has_message
msgid "Has Message"
msgstr "Мессежтэй"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_hired
msgid "Hire Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__id
msgid "ID"
msgstr "ID"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon"
msgstr "Дүрс"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ажилбар дээр сануулга гарсныг илэрхийлэх зураг."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread
msgid "If checked, new messages require your attention."
msgstr "Хэрэв сонгогдсон бол, шинэ зурвасууд таны анхаарлыг шаардана."

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Үүнийг сонговол алдаа үүсэх үед зурвасууд ирнэ."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__is_under_contract
msgid "Is Currently Under Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_is_follower
msgid "Is Follower"
msgstr "Дагагч эсэх"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__job_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__job_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Job Position"
msgstr "Албан тушаал"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__kanban_state
msgid "Kanban State"
msgstr "Канбан Төлөв"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type____last_update
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Late Activities"
msgstr "Хоцорсон ажилбар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_main_attachment_id
msgid "Main Attachment"
msgstr "Үндсэн хавсралт"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error
msgid "Message Delivery error"
msgstr "Алдаа үүссэн талаарх зурвас"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_ids
msgid "Messages"
msgstr "Зурвасууд"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_form
msgid "Monthly Wage"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Миний ажилбарын эцсийн огноо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_type__name
msgid "Name"
msgstr "Нэр"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__draft
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__draft
msgid "New"
msgstr "Шинэ"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дараагийн ажилбарын эцсийн огноо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_summary
msgid "Next Activity Summary"
msgstr "Дараагийн ажилбарын гарчиг"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_type_id
msgid "Next Activity Type"
msgstr "Дараагийн ажилбарын төрөл"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "No Contracts"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Not Running"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__blocked
msgid "Not Under Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__notes
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Notes"
msgstr "Тэмдэглэлүүд"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of Actions"
msgstr "Үйлдлийн тоо"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of errors"
msgstr "Алдааны тоо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Үйлдэл шаардсан зурвасын тоо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Алдааны мэдэгдэл бүхий зурвасын тоо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__message_unread_counter
msgid "Number of unread messages"
msgstr "Уншаагүй зурвасын тоо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__hr_responsible_id
msgid "Person responsible for validating the employee's contracts."
msgstr ""

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_employee_public
msgid "Public Employee"
msgstr "Ажилтны нээлттэй мэдээлэл"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__kanban_state__blocked
msgid "Red"
msgstr "Улаан"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_list
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Reference Working Time"
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Related User"
msgstr "Холбоотой хэрэглэгч"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "Ажлын цагийн хуваарь"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__activity_user_id
msgid "Responsible User"
msgstr "Эд хариуцагч"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract__state__open
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__state__open
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Running"
msgstr "Ажиллаж байгаа"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Running Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS илгээлтийн алдаа"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_form
msgid "Salary Information"
msgstr "Цалингийн мэдээлэл"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__structure_type_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Salary Structure Type"
msgstr "Цалингийн бүтцийн төрөл"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Search Contract"
msgstr "Гэрээг хайх"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
msgid "Search Reference Contracts"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set Contract End Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_departure_wizard__set_date_end
msgid "Set the end date on the current contract."
msgstr ""

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Show all records which have a next action date before today"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__date_start
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__date_start
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Start Date"
msgstr "Эхлэх огноо"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__date_start
msgid "Start date of the contract."
msgstr "Гэрээний эхлэх огноо."

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__state
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__state
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_history_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_employee_public_view_form
msgid "Status"
msgstr "Төлөв"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_state
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Ажилбаруудын төлөв байдал\n"
"Хоцорсон: Гүйцэтгэх огноо нь аль хэдий нь өнгөрсөн\n"
"Өнөөдөр: Өнөөдөр гүйцэтгэх ёстой\n"
"Төлөвлөгдсөн: Ирээдүйд гүйцэтгэх ажилбарууд"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__state
msgid "Status of the contract"
msgstr "Гэрээний төлөв"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__country_code
#: model:ir.model.fields,help:hr_contract.field_hr_contract_history__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Улсын ISO код нь хоёр тэмдэгтэй байна.\n"
"Та үүнийг хурдан хайлтад хэрэглэх боломжтой."

#. module: hr_contract
#: code:addons/hr_contract/models/hr_contract.py:0
#, python-format
msgid "The contract of %s is about to expire."
msgstr ""

#. module: hr_contract
#: model:mail.message.subtype,name:hr_contract.mt_contract_pending
msgid "To Renew"
msgstr "Сунгах шаардлагатай"

#. module: hr_contract
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Today Activities"
msgstr "Өнөөдрийн ажилбар"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Бичлэг дээрх асуудал бүхий ажилбарын төрөл"

#. module: hr_contract
#: model:ir.model.fields.selection,name:hr_contract.selection__hr_contract_history__under_contract_state__done
msgid "Under Contract"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread
msgid "Unread Messages"
msgstr "Уншаагүй зурвас"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Уншаагүй зурвасын тоолуур"

#. module: hr_contract
#: model:ir.model,name:hr_contract.model_res_users
msgid "Users"
msgstr "Хэрэглэгчид"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_expire
msgid "Visa Expiration Date"
msgstr ""

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__visa_no
msgid "Visa No"
msgstr "Визний дугаар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__wage
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__wage
msgid "Wage"
msgstr "Цалин"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__website_message_ids
msgid "Website Messages"
msgstr "Вебсайтын зурвас"

#. module: hr_contract
#: model:ir.model.fields,help:hr_contract.field_hr_contract__website_message_ids
msgid "Website communication history"
msgstr "Вебсайтын харилцааны түүх"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__permit_no
msgid "Work Permit No"
msgstr "Ажлын үнэмлэхний дугаар"

#. module: hr_contract
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract__resource_calendar_id
#: model:ir.model.fields,field_description:hr_contract.field_hr_contract_history__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:hr_contract.hr_contract_view_search
msgid "Working Schedule"
msgstr "Ажлын цагийн хуваарь"
