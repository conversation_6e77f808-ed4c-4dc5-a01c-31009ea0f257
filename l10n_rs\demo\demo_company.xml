<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_rs" model="res.partner">
        <field name="name">RS Company</field>
        <field name="vat"></field>
        <field name="street">Pasterova 2</field>
        <field name="city">Beograd</field>
        <field name="country_id" ref="base.rs"/>
        <field name="zip">110000</field>
        <field name="phone">+381 01 2345678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.rsexample.com</field>
    </record>

    <record id="demo_company_rs" model="res.company">
        <field name="name">RS Company</field>
        <field name="partner_id" ref="partner_demo_company_rs"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_rs')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('demo_company_rs'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_rs.l10n_rs_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_rs.demo_company_rs')"/>
    </function>
</odoo>
