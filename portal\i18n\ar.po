# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* portal
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:55+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "%1d days overdue"
msgstr "%1d أيام تأخير "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "%s is not the reference of a report"
msgstr "%s ليس مرجع تقرير "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"fa fa-arrow-right mr-1\"/>Back to edit mode"
msgstr "<i class=\"fa fa-arrow-right mr-1\"/>العودة إلى وضع التحرير "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid ""
"<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"Previous\"/>"
msgstr ""
"<i class=\"fa fa-chevron-left\" role=\"img\" aria-label=\"Previous\" "
"title=\"السابق \"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.record_pager
msgid "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr "<i class=\"fa fa-chevron-right\" role=\"img\" aria-label=\"Next\" title=\"التالي \"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil mx-1\"/>Edit Security Settings"
msgstr "<i class=\"fa fa-pencil mx-1\"/>تحرير إعدادات الأمان "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> تحرير"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"
msgstr "<i title=\"التوثيق \" class=\"fa fa-fw o_button_icon fa-info-circle\"/>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">الدولة...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "<option value=\"\">select...</option>"
msgstr "<option value=\"\">حدد...</option>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"<small class=\"form-text text-muted\">Changing company name or VAT number is"
" not allowed once document(s) have been issued for your account. <br/>Please"
" contact us directly for this operation.</small>"
msgstr ""
"<small class=\"form-text text-muted\">لا يسمح بتغيير اسم شركتك أو رقم ضريبة "
"القيمة المضافة بمجرد أن يتم إصدار مستند (مستندات) لحسابك. <br/>يرجى التواصل "
"معنا مباشرة لهذه العملية.</small> "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Filter By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">التصفية حسب:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Group By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">التجميع حسب:</span>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Sort By:</span>"
msgstr "<span class=\"small mr-1 navbar-text\">الفرز حسب:</span> "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr "<strong>فتح </strong>"

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">Marc Demo</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.user_id.name or ''\">Marc Demo</t>,<br/> <br/>\n"
"                        Welcome to <t t-out=\"object.user_id.company_id.name\">YourCompany</t>'s Portal!<br/><br/>\n"
"                        An account has been created for you with the following login: <t t-out=\"object.user_id.login\">demo</t><br/><br/>\n"
"                        Click on the button below to pick a password and activate your account.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>Activate Account</strong>\n"
"                            </a>\n"
"                            <a href=\"/web/login\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px;\">\n"
"                                <strong>Log in</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">Welcome to our company's portal.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">حسابك</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.user_id.name or ''\">مارك ديمو</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.user_id.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.user_id.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        عزيزي <t t-out=\"object.user_id.name or ''\">مارك ديمو</t>،<br/> <br/>\n"
"                        مرحباً بك في بوابة <t t-out=\"object.user_id.company_id.name\">شركتك!</t><br/><br/>\n"
"                        لقد تم إنشاء حساب لك باسم المستخدم التالي: <t t-out=\"object.user_id.login\">ديمو</t><br/><br/>\n"
"                        اضغط على الزر أدناه لاختيار كلمة مرور وتفعيل حسابك.\n"
"                        <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                            <a t-att-href=\"object.user_id.signup_url\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px; background-color: #875A7B; color: #fff; border-radius: 5px;\">\n"
"                                <strong>تفعيل الحساب</strong>\n"
"                            </a>\n"
"                            <a href=\"/web/login\" style=\"display: inline-block; padding: 10px; text-decoration: none; font-size: 12px;\">\n"
"                                <strong>تسجيل الدخول</strong>\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-out=\"object.wizard_id.welcome_message or ''\">مرحباً بك في بوابة شركتنا.</t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.user_id.company_id.name or ''\">شركتك</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.user_id.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.user_id.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.user_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.user_id.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.user_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.user_id.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        مشغل بواسطة <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">أودو</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "API Key Ready"
msgstr "مفتاح الواجهة البرمجية للتطبيق جاهز "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_signature.js:0
#, python-format
msgid "Accept & Sign"
msgstr "القبول والتوقيع "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr "تحذير من خطأ بالوصول"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Account Security"
msgstr "أمن الحساب "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr "إضافة ملاحظة"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Add attachment"
msgstr "إضافة مرفق "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr "إضافة جهات اتصال لمشاركتهم هذا المستند..."

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr "إضافة محتوى إضافي لعرضه في البريد الإكتروني"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Added On"
msgstr "تمت إضافته في "

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "يجب تقديم رمز وصول لكل مرفق. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Avatar"
msgstr "الصورة الرمزية"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Cancel"
msgstr "إلغاء "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Change Password"
msgstr "تغيير كلمة المرور"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
#, python-format
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"لا يسمح بتغيير رقم ضريبة القيمة المضافة بمجرد أن يتم إصدار مستند (مستندات) "
"لحسابك. يرجى التواصل معنا مباشرة لإتمام هذه العملية. "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"لا يسمح بتغيير اسم الشركة بمجرد أن يتم إصدار مستند (مستندات) لحسابك. يرجى "
"التواصل معنا مباشرة لإتمام هذه العملية. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Check failed"
msgstr "فشلت عملية التحقق "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "City"
msgstr "المدينة"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Click here to see your document."
msgstr "انقر هنا لرؤية مستندك."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Close"
msgstr "إغلاق "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Company Name"
msgstr "اسم الشركة "

#. module: portal
#: model:ir.model,name:portal.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm"
msgstr "تأكيد"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Confirm\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"تأكيد\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Confirm Password"
msgstr "تأكيد كلمة المرور"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
msgid "Contact"
msgstr "جهة الاتصال"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Contact Details"
msgstr "تفاصيل جهة الاتصال"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "جهات الاتصال"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid "Could not save file <strong>%s</strong>"
msgstr "تعذر حفظ الملف <strong>%s</strong> "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Country"
msgstr "الدولة"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"متاح حالياً لجميع من يقومون بعرض هذا المستند. اضغط لتقييد الوصول ليُسمَح "
"للموظفين الداخليين فقط. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""
"وحدهم الموظفون الداخليون يُسمَح لهم بالوصول. اضغط لجعله متاحاً لجميع من "
"يقومون بعرض هذا المستند. "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_res_config_settings__portal_allow_api_keys
msgid "Customer API Keys"
msgstr "مفاتيح API العميل "

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr "رابط URL لبوابة العميل "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "عزيزنا"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Delete"
msgstr "حذف"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Description"
msgstr "الوصف"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Details"
msgstr "التفاصيل"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Developer API Keys"
msgstr "مفاتيح API للمطور "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Discard"
msgstr "إهمال "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Documents"
msgstr "المستندات"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due in %1d days"
msgstr "مستحق في %1d يوم "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:0
#, python-format
msgid "Due today"
msgstr "مستحق اليوم"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "المحادثة البريدية"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Employees Only"
msgstr "الموظفين فقط "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Enter a description of and purpose for the key."
msgstr "إدخال وصف المفتاح والغرض منه. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Forgot password?"
msgstr "هل نسيت كلمة المرور؟ "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Access"
msgstr "منح إذن الوصول "

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard
msgid "Grant Portal Access"
msgstr "منح صلاحية الوصول لبوابة العملاء"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.actions.server,name:portal.partner_wizard_action_create_and_open
msgid "Grant portal access"
msgstr "منح صلاحية الوصول إلى البوابة "

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"Here is your new API key, use it instead of a password for RPC access.\n"
"                Your login is still necessary for interactive usage."
msgstr ""
"إليك مفتاح API الجديد الخاص بك، استخدمه عوضاً عن كلمة المرور للوصول إلى RPC.\n"
"                لا تزال بحاجة إلى تسجيل الدخول للاستخدام التفاعلي. "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "الرئيسية"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "المُعرف"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Important:"
msgstr "مهم: "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Internal User"
msgstr "مستخدم داخلي"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal Note"
msgstr "ملاحظة داخلية"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Internal notes are only displayed to internal users."
msgstr "يتم عرض الملاحظات الداخلية فقط للمستخدمين الداخليين. "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "البريد الإلكتروني غير صالح! يرجى إدخال عنوان بريد إلكتروني صالح. "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Invalid report type: %s"
msgstr "نوع التقرير غير صالح: %s "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr "رسالة الدعوة"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_internal
msgid "Is Internal"
msgstr "داخلي "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__is_portal
msgid "Is Portal"
msgstr "بوابة "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"It is very important that this description be clear\n"
"                and complete,"
msgstr ""
"من المهم جداً أن يكون هذا الوصف واضحاً\n"
"                وكاملاً، "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__login_date
msgid "Latest Authentication"
msgstr "آخر مصادقة "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Leave a comment"
msgstr "اترك تعليقًا"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.res_config_settings_view_form
msgid "Let your customers create developer API keys"
msgstr "أتح لعملائك إنشاء مفاتيح API للمطورين "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "الرابط"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_dropdown
msgid "Logout"
msgstr "تسجيل الخروج"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "الرسالة"

#. module: portal
#: code:addons/portal/models/mail_thread.py:0
#, python-format
msgid ""
"Model %(model_name)s does not support token signature, as it does not have "
"%(field_name)s field."
msgstr ""
"لا يدعم النموذج %(model_name)s توقيع الرمز، حيث أنه لا يحتوي على حقل "
"%(field_name)s. "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid "My Account"
msgstr "حسابي"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Name"
msgstr "الاسم"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Name your key"
msgstr "قم بتسمية مفتاحك "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
#, python-format
msgid "New API Key"
msgstr "مفتاح API جديد "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "New Password:"
msgstr "كلمة السر الجديدة: "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.pager
#, python-format
msgid "Next"
msgstr "التالي"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "الملاحظات"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr "شعار أودو"

#. module: portal
#: code:addons/portal/models/res_users_apikeys_description.py:0
#, python-format
msgid "Only internal and portal users can create API keys"
msgstr "وحدهم المستخدمون الداخليون ومستخدمي البوابة بوسعهم إنشاء مفاتيح API "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "عفوًا! حدث خطأ ما. حاول إعادة تحميل الصفحة وتسجيل الدخول."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__partner_ids
msgid "Partners"
msgstr "الشركاء"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password Updated!"
msgstr "تم تحديث كلمة المرور! "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Password:"
msgstr "كلمة المرور: "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Phone"
msgstr "رقم الهاتف"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Please enter your password to confirm you own this account"
msgstr "يرجى إدخال كلمة السر الخاصة بك لتأكيد أنك تملك هذا الحساب "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
#, python-format
msgid "Portal Access Management"
msgstr "إدراة الوصول إلى البوابة "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr "رابط الوصول إلى البوابة "

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr "مجموعة مخصصات البوابة "

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr "مشاركة البوابة "

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr "تهيئة مستخدم البوابة "

#. module: portal
#: model:mail.template,name:portal.mail_template_data_portal_welcome
msgid "Portal: User Invite"
msgstr "البوابة: دعوة المستخدم "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "مشغل بواسطة "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "Prev"
msgstr "السابق"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Previous"
msgstr "السابق"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_chatter.js:0
#, python-format
msgid "Published on %s"
msgstr "تم النشر في %s "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Re-Invite"
msgstr "إعادة إرسال الدعوة "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "المستلمين "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__resource_ref
msgid "Related Document"
msgstr "المستند ذو الصلة "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "معرف المستند ذو الصلة "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "نموذج المستند ذو الصلة "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Revoke Access"
msgstr "إبطال الوصول "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Scope"
msgstr "النطاق "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "بحث"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Security"
msgstr "الأمان "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal.js:0
#, python-format
msgid "Security Control"
msgstr "ضبط الأمان "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "رمز الحماية"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""
"اختر أي جهات الاتصال يجب أن تنتمي إلى بوابة العملاء في القائمة أدناه.\n"
"                        يجب أن يكون عنوان البريد الإلكتروني الخاص بكل جهة اتصال صالحاً وفريداً.\n"
"                        إذا لزم الأمر، يمكنك تعديل أي عنوان بريد إلكتروني مباشرة في القائمة. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Send"
msgstr "إرسال"

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr "مشاركة المستند"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_ir_ui_view__customize_show
msgid "Show As Optional Inherit"
msgstr "عرضه كتوريث اختياري"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.user_sign_in
msgid "Sign in"
msgstr "تسجيل الدخول"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_composer.js:0
#, python-format
msgid ""
"Some fields are required. Please make sure to write a message or attach a "
"document"
msgstr "بعض الحقول مطلوبة. يرجى التأكد من كتابة رسالة أو إرفاق مستند "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "Some required fields are empty."
msgstr "بعض الحقول المطلوبة فارغة."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "State / Province"
msgstr "الولاية/المنطقة "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Street"
msgstr "الشارع"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:0
#, python-format
msgid "Thank You!"
msgstr "شكرا لك! "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The attachment %s cannot be removed because it is linked to a message."
msgstr "لا يمكن إزالة المرفق %s لأنه مرتبط برسالة. "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment %s cannot be removed because it is not in a pending state."
msgstr "لا يمكن إزالة المرفق %s لأن حالته معلّقة. "

#. module: portal
#: code:addons/portal/controllers/mail.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "المرفق %s غير موجود أو إنك لا تملك حق الوصول إليه. "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The attachment does not exist or you do not have the rights to access it."
msgstr "المرفق غير موجود أو إنك لا تملك حق الوصول إليه. "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" does not have a valid email."
msgstr "ليس لجهة الاتصال \"%s: عنوان بريد إلكتروني صالح. "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The contact \"%s\" has the same email has an existing user (%s)."
msgstr ""
"لدى جهة الاتصال \"%s\" نفس عنوان البريد الإلكتروني كمستخدم موجود بالفعل "
"(%s). "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "المستند غير موجود أو إنك لا تملك حق الوصول إليه. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "The key cannot be retrieved later and provides"
msgstr "لا يمكن استعادة المفتاح لاحقاً ويقدم "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "يجب أن تتطابق كلمة المرور وتأكيدها."

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr "كلمة المرور القديمة غير صحيحة. لم يتم تغيير كلمة المرور."

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" already has the portal access."
msgstr "لدى الشريك \"%s\" صلاحية الوصول إلى البوابة بالفعل. "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "The partner \"%s\" has no portal access."
msgstr "لا يملك الشريك \"%s\" صلاحية الوصول إلى البوابة. "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid ""
"The template \"Portal: new user\" not found for sending email to the portal "
"user."
msgstr ""
"لم يتم العثور على القالب \"البوابة: مستخدم جديد\" لإرسال بريد إلكتروني إلى "
"مستخدم البوابة. "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "There are no comments for now."
msgstr "لا توجد تعليقات حالياً. "

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "This document does not exist."
msgstr "هذا المستند غير موجود."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr "هذه معاينة لبوابة العميل."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This partner is linked to an internal User and already has access to the "
"Portal."
msgstr "الشريك مرتبط بمستخدم داخلي ولديه صلاحية الوصول إلى البوابة بالفعل. "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"This text is included at the end of the email sent to new portal users."
msgstr ""
"هذا النص متضمن في نهاية البريد الإلكتروني الذي يتم إرساله إلى مستخدمي "
"البوابة الجدد. "

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr "يتم إدراج هذا النص في الرسالة المرسلة لمستخدمي بوابة العملاء الجدد."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr "تبديل عوامل التصفية "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "User"
msgstr "المستخدم"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "المستخدمين "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "VAT Number"
msgstr "رقم ضريبة القيمة المضافة"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "Verify New Password:"
msgstr "التحقق من كلمة السر الجديدة: "

#. module: portal
#: model:ir.model,name:portal.model_ir_ui_view
msgid "View"
msgstr "عرض "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Visible"
msgstr "مرئي "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,field_description:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,field_description:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,field_description:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,field_description:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,field_description:portal.field_note_note__website_message_ids
#: model:ir.model.fields,field_description:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_product__website_message_ids
#: model:ir.model.fields,field_description:portal.field_product_template__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team__website_message_ids
#: model:ir.model.fields,help:portal.field_crm_team_member__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_fleet_vehicle_log_services__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_badge__website_message_ids
#: model:ir.model.fields,help:portal.field_gamification_challenge__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_contract__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_department__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_job__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave__website_message_ids
#: model:ir.model.fields,help:portal.field_hr_leave_allocation__website_message_ids
#: model:ir.model.fields,help:portal.field_lunch_supplier__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_cc__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread_phone__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_equipment_category__website_message_ids
#: model:ir.model.fields,help:portal.field_maintenance_request__website_message_ids
#: model:ir.model.fields,help:portal.field_note_note__website_message_ids
#: model:ir.model.fields,help:portal.field_phone_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_product_product__website_message_ids
#: model:ir.model.fields,help:portal.field_product_template__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "What's this key for?"
msgstr "ما الغرض من هذا المفتاح؟ "

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "المعالج"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "Write a message..."
msgstr "اكتب رسالة..."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "Write down your key"
msgstr "اكتب مفتاحك الخاصة "

#. module: portal
#: code:addons/portal/wizard/portal_share.py:0
#: code:addons/portal/wizard/portal_share.py:0
#, python-format
msgid "You are invited to access %s"
msgstr "تمت دعوتك للوصول إلى %s"

#. module: portal
#: code:addons/portal/controllers/portal.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "لا يمكنك ترك أي كلمة مرور فارغة."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "You have been invited to access the following document:"
msgstr "تمت دعوتك للوصول للمستند التالي:"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "You must be"
msgstr "لابد أنك "

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:0
#, python-format
msgid "You should first grant the portal access to the partner \"%s\"."
msgstr "عليك أولاً منح حق الوصول إلى البوابة إلى الشريك \"%s\". "

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your account at {{ object.user_id.company_id.name }}"
msgstr "حسابك على {{ object.user_id.company_id.name }} "

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr "جهة اتصالك"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Zip / Postal Code"
msgstr "الرمز البريدي"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "avatar"
msgstr "الصورة الرمزية"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comment"
msgstr "تعليق"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "comments"
msgstr "تعليقات"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "full access"
msgstr "الوصول الكامل "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid ""
"it will be the only way to\n"
"                identify the key once created"
msgstr "ستكون الطريقة الوحيدة للتعرف على المفتاح بمجرد إنشائه "

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "logged in"
msgstr "تم تسجيل الدخول"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr "أودو"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_security
msgid "password"
msgstr "كلمة السر "

#. module: portal
#: model:ir.model,name:portal.model_res_users_apikeys_description
msgid "res.users.apikeys.description"
msgstr "res.users.apikeys.description"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:0
#, python-format
msgid "to post a comment."
msgstr "لكتابة تعليق."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_security.xml:0
#, python-format
msgid "to your user account, it is very important to store it securely."
msgstr "لحساب المستخدم الخاص بك، من المهم جداً تخزينه بأمان. "
