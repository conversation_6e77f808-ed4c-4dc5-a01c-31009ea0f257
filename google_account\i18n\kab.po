# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * google_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Kabyle (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_create_date
msgid "Created on"
msgstr "Yerna di"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_display_name
msgid "Display Name"
msgstr ""

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_id
msgid "ID"
msgstr "Asulay"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: google_account
#: model:ir.model.fields,field_description:google_account.field_google_service_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: google_account
#: code:addons/google_account/models/google_service.py:171
#, python-format
msgid "Method not supported [%s] not in [GET, POST, PUT, PATCH or DELETE]!"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:119
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:55
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:149
#, python-format
msgid ""
"Something went wrong during your token generation. Maybe your Authorization "
"Code is invalid or already expired [%s]"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:192
#, python-format
msgid "Something went wrong with your request to google"
msgstr ""

#. module: google_account
#: code:addons/google_account/models/google_service.py:130
#, python-format
msgid "The account for the Google service '%s' is not configured"
msgstr ""

#. module: google_account
#: model:ir.model,name:google_account.model_google_service
msgid "google.service"
msgstr ""
