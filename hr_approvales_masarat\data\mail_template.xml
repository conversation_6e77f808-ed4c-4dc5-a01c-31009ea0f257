<?xml version="1.0" ?>
<odoo>
    <data>
        <record id="car_allowance_request_template" model="mail.template">
            <field name="name">Car Allowance Request Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_car"/>
            <field name="subject">${object.name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested car allowance approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>

    <data>
        <record id="exit_permission_approval_template" model="mail.template">
            <field name="name">Exit Permission Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_latency"/>
            <field name="subject">${object.name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested Exit permission approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>

    <data>
        <record id="latency_approval_template" model="mail.template">
            <field name="name">Latency Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_latency"/>
            <field name="subject">${object.latency_name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested latency approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>
    <data>
        <record id="work_assignment_approval_template" model="mail.template">
            <field name="name">Work Assignment Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_work_assignment"/>
            <field name="subject">${object.assignment_name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested Work Assignment approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>

    <data>
        <record id="absence_approval_template" model="mail.template">
            <field name="name">Absence Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_absence"/>
            <field name="subject">${object.absence_name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested absence approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>

    <data>
        <record id="overtime_approval_template" model="mail.template">
            <field name="name">Overtime Approval</field>
            <field name="model_id" ref="hr_approvales_masarat.model_hr_masarat_overtime"/>
            <field name="subject">${object.absence_name}</field>
            <field name="email_from">${user.email_formatted |safe}</field>
            <field name="email_to">${object.manager_id.work_email |safe}</field>
            <field name="body_html" type="xml">
                <p><font style="font-size: 14px;">Your Employee ${object.employee_id.name}, requested absence approval, </font></p>
                <p><font style="font-size: 14px;">Please login to Odoo in order to proceed.</font></p>
            </field>
        </record>
    </data>
</odoo>


