<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
  <record id="account_account_tag_a_1_1" model="account.account.tag">
    <field name="name">A.1.1. Assets arising from development</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_1_2" model="account.account.tag">
    <field name="name">A.1.2. Goodwill</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_1_3" model="account.account.tag">
    <field name="name">A.1.3. Software</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_1_4" model="account.account.tag">
    <field name="name">A.1.4. Concessions, patents, licenses, trade marks and similar rights</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_1_5" model="account.account.tag">
    <field name="name">A.1.5. Other intangible assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_1_6" model="account.account.tag">
    <field name="name">A.1.6. Advance payments</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_1" model="account.account.tag">
    <field name="name">A.2.1. Land</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_2" model="account.account.tag">
    <field name="name">A.2.2. Buildings and structures</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_3" model="account.account.tag">
    <field name="name">A.2.3. Machinery and plant</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_4" model="account.account.tag">
    <field name="name">A.2.4. Vehicles</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_5" model="account.account.tag">
    <field name="name">A.2.5. Other equipment, fittings and tools</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_6_1" model="account.account.tag">
    <field name="name">A.2.6.1. Land</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_6_2" model="account.account.tag">
    <field name="name">A.2.6.2. Buildings</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_2_7" model="account.account.tag">
    <field name="name">A.2.7. Advance payments and tangible assets under construction (production)</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_1" model="account.account.tag">
    <field name="name">A.3.1. Shares in entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_2" model="account.account.tag">
    <field name="name">A.3.2. Loans to entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_3" model="account.account.tag">
    <field name="name">A.3.3. Amounts receivable from entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_4" model="account.account.tag">
    <field name="name">A.3.4. Shares in associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_5" model="account.account.tag">
    <field name="name">A.3.5. Loans to associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_6" model="account.account.tag">
    <field name="name">A.3.6. Amounts receivable from the associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_7" model="account.account.tag">
    <field name="name">A.3.7. Long-term investments</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_8" model="account.account.tag">
    <field name="name">A.3.8. Amounts receivable after one year</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_3_9" model="account.account.tag">
    <field name="name">A.3.9. Other financial assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_4_1" model="account.account.tag">
    <field name="name">A.4.1. Assets of the deferred tax on profit</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_4_2" model="account.account.tag">
    <field name="name">A.4.2. Biological assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_a_4_3" model="account.account.tag">
    <field name="name">A.4.3. Other assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_1" model="account.account.tag">
    <field name="name">B.1.1. Raw materials, materials and consumables</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_2" model="account.account.tag">
    <field name="name">B.1.2. Production and work in progress</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_3" model="account.account.tag">
    <field name="name">B.1.3. Finished goods</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_4" model="account.account.tag">
    <field name="name">B.1.4. Goods for resale</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_5" model="account.account.tag">
    <field name="name">B.1.5. Biological assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_6" model="account.account.tag">
    <field name="name">B.1.6. Fixed tangible assets held for sale</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_1_7" model="account.account.tag">
    <field name="name">B.1.7. Advance payments</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_2_1" model="account.account.tag">
    <field name="name">B.2.1. Trade debtors</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_2_2" model="account.account.tag">
    <field name="name">B.2.2. Amounts owed by entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_2_3" model="account.account.tag">
    <field name="name">B.2.3. Amounts owed by associates entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_2_4" model="account.account.tag">
    <field name="name">B.2.4. Other debtors</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_3_1" model="account.account.tag">
    <field name="name">B.3.1. Shares in entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_3_2" model="account.account.tag">
    <field name="name">B.3.2. Other investments</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_b_4" model="account.account.tag">
    <field name="name">B.4. CASH AND CASH EQUIVALENTS</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_c_prepayments_accrued_income" model="account.account.tag">
    <field name="name">C. PREPAYMENTS AND ACCRUED INCOME</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_1_1" model="account.account.tag">
    <field name="name">D.1.1. Authorized (subscribed) or primary capital</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_1_2" model="account.account.tag">
    <field name="name">D.1.2. Subscribed capital unpaid (–)</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_1_3" model="account.account.tag">
    <field name="name">D.1.3. Own shares (–)</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_2_share_premium" model="account.account.tag">
    <field name="name">D.2. SHARE PREMIUM ACCOUNT</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_3_revaluation_reserve" model="account.account.tag">
    <field name="name">D.3. REVALUATION RESERVE</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_4_1" model="account.account.tag">
    <field name="name">D.4.1. Compulsory reserve or emergency (reserve) capital</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_4_2" model="account.account.tag">
    <field name="name">D.4.2. Reserve for acquiring own shares</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_4_3" model="account.account.tag">
    <field name="name">D.4.3. Other reserves</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_5_1" model="account.account.tag">
    <field name="name">D.5.1. Profit (loss) for the reporting year </field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_d_5_2" model="account.account.tag">
    <field name="name">D.5.2. Profit (loss) brought forward</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_e_grants_subsidies" model="account.account.tag">
    <field name="name">E. GRANTS, SUBSIDIES</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_f_1" model="account.account.tag">
    <field name="name">F.1. Provisions for pensions and similar obligations</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_f_2" model="account.account.tag">
    <field name="name">F.2. Provisions for taxation</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_f_3" model="account.account.tag">
    <field name="name">F.3. Other provisions</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_1" model="account.account.tag">
    <field name="name">G.1.1. Debenture loans</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_2" model="account.account.tag">
    <field name="name">G.1.2. Amounts owed to credit institutions</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_3" model="account.account.tag">
    <field name="name">G.1.3. Payments received on account</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_4" model="account.account.tag">
    <field name="name">G.1.4. Trade creditors</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_5" model="account.account.tag">
    <field name="name">G.1.5. Amounts payable under the bills and checks </field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_6" model="account.account.tag">
    <field name="name">G.1.6. Amounts payable to the entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_7" model="account.account.tag">
    <field name="name">G.1.7. Amounts payable to the associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_1_8" model="account.account.tag">
    <field name="name">G.1.8. Other amounts payable and long-term liabilities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_1" model="account.account.tag">
    <field name="name">G.2.1. Debenture loans</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_2" model="account.account.tag">
    <field name="name">G.2.2. Amounts owed to credit institutions</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_3" model="account.account.tag">
    <field name="name">G.2.3. Payments received on account</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_4" model="account.account.tag">
    <field name="name">G.2.4. Trade creditors</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_5" model="account.account.tag">
    <field name="name">G.2.5. Amounts payable under the bills and checks </field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_6" model="account.account.tag">
    <field name="name">G.2.6. Amounts payable to the entities of the entities group</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_7" model="account.account.tag">
    <field name="name">G.2.7. Amounts payable to the associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_8" model="account.account.tag">
    <field name="name">G.2.8. Liabilities of tax on profit</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_9" model="account.account.tag">
    <field name="name">G.2.9. Liabilities related to employment relations</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_g_2_10" model="account.account.tag">
    <field name="name">G.2.10. Other amounts payable and short-term liabilities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_h_accruals_deferred_income" model="account.account.tag">
    <field name="name">H. ACCRUALS AND DEFERRED INCOME</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_1_net_turnover" model="account.account.tag">
    <field name="name">1. Net turnover</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_2_cost_of_sales" model="account.account.tag">
    <field name="name">2. Cost of sales</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_3_adjustments_of_biological_assets" model="account.account.tag">
    <field name="name">3. Fair value adjustments of the biological assets</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_4_selling_expenses" model="account.account.tag">
    <field name="name">4. Selling expenses</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_5_general_administrative_expenses" model="account.account.tag">
    <field name="name">5. General and administrative expenses</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_6_other_operating_results" model="account.account.tag">
    <field name="name">6. Other operating results</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_7_income_investments_parent" model="account.account.tag">
    <field name="name">7. Income from investments in the shares of parent, subsidiaries and associated entities</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_8_income_other_longterm_investments_loans" model="account.account.tag">
    <field name="name">8. Income from other long-term investments and loans</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_9_other_interest_similar_income" model="account.account.tag">
    <field name="name">9. Other interest and similar income</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_10_impaired_fin_assets_short_investments" model="account.account.tag">
    <field name="name">10. The impairment of the financial assets and short-term investments</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_11_interest_other_similar_expenses" model="account.account.tag">
    <field name="name">11. Interest and other similar expenses</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
  <record id="account_account_tag_12_tax_on_profit" model="account.account.tag">
    <field name="name">12. Tax on profit</field>
    <field name="applicability">accounts</field>
    <field name="color" eval="8"/>
  </record>
</odoo>
