# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* coupon
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <brencic<PERSON><EMAIL>>, 2022
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(amount)s %(currency)s discount on total amount"
msgstr "%(amount)s %(currency)s zľava z celkovej sumy"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%(percentage)s%% discount on %(product_name)s"
msgstr "%(percentage)s%% zľava na %(product_name)s"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on cheapest product"
msgstr "%s%%zľava na najlacnejšie produkt "

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on products"
msgstr "%s%%zľava na výrobky"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "%s%% discount on total amount"
msgstr "%s%% zľava z celkovej sumy"

#. module: coupon
#: code:addons/coupon/wizard/coupon_generate.py:0
#, python-format
msgid "%s, a coupon has been generated for you"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "*Valid for following products:"
msgstr "* Platí pre nasledujúce produkty:"

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_coupon
msgid "10% Discount"
msgstr "10% Zľava"

#. module: coupon
#: model:product.product,name:coupon.product_product_10_percent_discount
#: model:product.template,name:coupon.product_product_10_percent_discount_product_template
msgid "10.0% discount on total amount"
msgstr "Zľava 10,0% z celkovej sumy"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid ""
"<span attrs=\"{'invisible': [('discount_type', '!=', "
"'percentage')],'required': [('discount_type', '=', 'percentage')]}\" "
"class=\"oe_inline\">%</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid ""
"<span class=\"o_form_label oe_inline\"> Days</span> <span "
"class=\"oe_grey\">if 0, infinite use</span>"
msgstr ""
"<span class=\"o_form_label oe_inline\"> Dni</span> <span "
"class=\"oe_grey\">ak 0, nekonečné použitie</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "<span class=\"oe_grey\"> if 0, no limit</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Days</span>\n"
"                    <span class=\"oe_grey\"> if 0, coupon doesn't expire</span>"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid ""
"<span> Orders</span>\n"
"                    <span class=\"oe_grey\"> if 0, infinite use</span>"
msgstr ""
"<span> Objednávky</span>\n"
"                    <span class=\"oe_grey\">pokial  0, nekonečné použitie</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Minimum purchase of</span>"
msgstr "<span>Minimálny nákup</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>Valid for purchase above</span>"
msgstr "<span>Platí pre nákup výššie</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "<span>products</span>"
msgstr "<span>produkty</span>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid ""
"<strong style=\"font-size: 55px; color: #875A7B\">get free shipping</strong>"
msgstr ""
"<strong style=\"font-size: 55px; color: #875A7B\">získajte dopravu "
"zdarma</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Active</strong>"
msgstr "<strong>Aktívny</strong>"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.view_coupon_program_kanban
msgid "<strong>Coupons</strong>"
msgstr "<strong>Kupóny</strong>"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__active
msgid "A program is available for the customers when active"
msgstr "Pokiaľ je zákazník aktívny, program je k dispozícii"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code
msgid ""
"A promotion code is a code that is associated with a marketing discount. For"
" example, a retailer might tell frequent customers to enter the promotion "
"code 'THX001' to receive a 10%% discount on their whole order."
msgstr ""
"Propagačný kód je kód, ktorý je spojený s marketingovou zľavou. Napríklad "
"maloobchodník môže častým zákazníkom povedať, aby na získanie 10 zadali "
"propagačný kód \"THX001\"%%zľava na celú ich objednávku."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__program_type
msgid ""
"A promotional program can be either a limited promotional offer without code (applied automatically)\n"
"                or with a code (displayed on a magazine for example) that may generate a discount on the current\n"
"                order or create a coupon for a next order.\n"
"\n"
"                A coupon program generates coupons with a code that can be used to generate a discount on the current\n"
"                order or create a coupon for a next order."
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__active
msgid "Active"
msgstr "Aktívne"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_applicability
msgid "Applicability"
msgstr "Použiteľnosť"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Apply Discount"
msgstr "Uplatniť zľavu"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_current_order
msgid "Apply On Current Order"
msgstr "Podať žiadosť o aktuálnu objednávku"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Apply on First"
msgstr "Použite najprv"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_search
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Archived"
msgstr "Archivovaný"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__no_code_needed
msgid "Automatically Applied"
msgstr "Automaticky použité"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__promo_code_usage
msgid ""
"Automatically Applied - No code is required, if the program rules are met, the reward is applied (Except the global discount or the free shipping rewards which are not cumulative)\n"
"Use a code - If the program rules are met, a valid code is mandatory for the reward to be applied\n"
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_partners_domain
msgid "Based on Customers"
msgstr "Na základe zákazníkov"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_products_domain
msgid "Based on Products"
msgstr "Na základe produktov"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid ""
"Build up promotion programs to attract more customers with discounts, free products, free delivery, etc.\n"
"                You can share promotion codes or grant the promotions automatically if some conditions are met."
msgstr ""

#. module: coupon
#: model:coupon.program,name:coupon.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "Kupte si 3 velké skrinky, jednu získáťe zdarma"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Cancel"
msgstr "Zrušené"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__cancel
msgid "Cancelled"
msgstr "Zrušené"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__code
msgid "Code"
msgstr "Kód"

#. module: coupon
#: model:coupon.program,name:coupon.10_percent_auto_applied
msgid "Code for 10% on orders"
msgstr "Kód pre 10% na objednávky"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__company_id
msgid "Company"
msgstr "Spoločnosť"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Compose Email"
msgstr "Vytvoriť email"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Conditions"
msgstr "Podmienky"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Congratulations"
msgstr "Gratulujeme"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_coupon
msgid "Coupon"
msgstr "Kupón"

#. module: coupon
#: model:ir.actions.report,name:coupon.report_coupon_code
msgid "Coupon Code"
msgstr "Kód kupónu"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_count
msgid "Coupon Count"
msgstr "Počet kupónov"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_program
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__coupon_program
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Coupon Program"
msgstr "Kupónový program"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_coupon_program
msgid "Coupon Programs"
msgstr "Kuponové programy"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_reward
msgid "Coupon Reward"
msgstr "Odmena za kupón"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_rule
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_id
msgid "Coupon Rule"
msgstr "Pravidlo kupónu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Coupon Validity"
msgstr ""

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_to
msgid "Coupon program end date"
msgstr "Datum ukončenia kupónového programu"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_date_from
msgid "Coupon program start date"
msgstr "Datum zahájenia kupónového programu"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__sequence
msgid ""
"Coupon program will be applied based on given sequence if multiple programs "
"are defined on same condition(For minimum amount)"
msgstr ""
"Kupónový program bude použitý na základe danej sekvencie, pokial je "
"definovaných viacej programov za rovnakých podmienok (pre minimálnu čiastku)"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_partners_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_partners_domain
msgid "Coupon program will work for selected customers only"
msgstr "Kupónový program bude fungovať iba pre vybraných zákazníkov"

#. module: coupon
#: model:ir.actions.server,name:coupon.expire_coupon_cron_ir_actions_server
#: model:ir.cron,cron_name:coupon.expire_coupon_cron
#: model:ir.cron,name:coupon.expire_coupon_cron
msgid "Coupon: expire coupon based on date"
msgstr "Kupón: platnosť kupónu vyprší na základe dát"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_action
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Coupons"
msgstr "Kupóny"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid "Create a new coupon program"
msgstr "Vytvorte nový kupónový program"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_promo_program
msgid "Create a new promotion program"
msgstr "Vytvořte nový propagační program"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__create_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__currency_id
msgid "Currency"
msgstr "Mena"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__partners_domain
msgid "Customer"
msgstr "Zákazník"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Predvolená merná jednotka používaná pre všetky skladové operácie."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_percentage
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__discount
msgid "Discount"
msgstr "Zľava"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_type
msgid ""
"Discount - Reward will be provided as discount.\n"
"Free Product - Free product will be provide as reward \n"
"Free Shipping - Free shipping will be provided as reward (Need delivery module)"
msgstr ""
"Zľava - Odmena bude poskytnutá ako zľava.\n"
"Bezplatný produkt - bezplatný produkt bude poskytnutý ako odmena\n"
"Doprava zadarmo - Doprava zdarma bude poskytnutá ako odmena (potrebujete doručovací modul)"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_apply_on
msgid "Discount Apply On"
msgstr "Zľava sa vztahuje na"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_max_amount
msgid "Discount Max Amount"
msgstr "Maximálna čiastka zľavy"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_type
msgid "Discount Type"
msgstr "Typ slevy"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Discount percentage should be between 1-100"
msgstr "Percento zľavy by malo byť medzi 1-100"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_program__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__display_name
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: coupon
#: model:ir.model,name:coupon.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Sprievodca zostavovaním emailov"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_to
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_to
msgid "End Date"
msgstr "Dátum ukončenia"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__expiration_date
msgid "Expiration Date"
msgstr "Dátum spotreby"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__expired
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired"
msgstr "Ukončená platnosť"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_search
msgid "Expired Programs"
msgstr "Programy, ktorých platnosť vypršala"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_fixed_amount
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__fixed_amount
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Fixed Amount"
msgstr "Pevná suma"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__partner_id
msgid "For Customer"
msgstr "Pre zákazníka"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_id
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__reward_type__product
msgid "Free Product"
msgstr "Produkt zadarmo"

#. module: coupon
#: code:addons/coupon/models/coupon_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr " Produkt zdarma - %s"

#. module: coupon
#: model:product.product,name:coupon.product_product_free_large_cabinet
#: model:product.template,name:coupon.product_product_free_large_cabinet_product_template
msgid "Free Product - Large Cabinet"
msgstr "Produkt zdarma - veľká skrinka"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate"
msgstr "Všeobecné"

#. module: coupon
#: model:ir.model,name:coupon.model_coupon_generate_wizard
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Generate Coupon"
msgstr "Vytvoriť kupón"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Generate Coupons"
msgstr "Vytvoriť kupóny"

#. module: coupon
#: model_terms:ir.actions.act_window,help:coupon.coupon_program_action_coupon_program
msgid ""
"Generate and share coupon codes with your customers to get discounts or free"
" products."
msgstr ""
"Generujte a zdieľajte kódy kupónov so svojimi zákazníkmi a získajte zľavy "
"alebo produkty zadarmo."

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__coupon_ids
msgid "Generated Coupons"
msgstr "Vygenerovaný kupón"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__generation_type
msgid "Generation Type"
msgstr "Typ generácie"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__has_partner_email
msgid "Has Partner Email"
msgstr "Má email partnera"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Here is your reward from"
msgstr "Tu je vaša odmena od"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__id
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__id
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__id
msgid "ID"
msgstr "ID"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "Invalid partner."
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_program____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_reward____last_update
#: model:ir.model.fields,field_description:coupon.field_coupon_rule____last_update
msgid "Last Modified on"
msgstr "Posledná úprava"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_uid
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_program__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__write_date
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Logo"
msgstr "Logo"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Max Discount Amount"
msgstr "Maximální čiastka zľavy"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__maximum_use_number
msgid "Maximum Use Number"
msgstr "Maximálny počet použitia"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_max_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_max_amount
msgid "Maximum amount of discount that can be provided"
msgstr "Maximálna výška zľavy, ktorú možno poskytnúť"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__maximum_use_number
msgid "Maximum number of sales orders in which reward can be provided"
msgstr ""
"Maximálny počet predajných objednávok, v ktorých možno poskytnúť odmenu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Minimum Purchase Of"
msgstr "Minimálny nákup"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum Quantity"
msgstr "Minimálne množstvo"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum purchased amount should be greater than 0"
msgstr "Minimálna zakúpená suma by mala byť väčšia ako 0"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "Minimum quantity should be greater than 0"
msgstr "Minimálne množstvo by malo byť väčšie ako 0"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_minimum_amount
msgid "Minimum required amount to get the reward"
msgstr "Minimálna požadovaná časť pre získanie odmeny"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_min_quantity
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_min_quantity
msgid "Minimum required product quantity to get the reward"
msgstr "Minimálne požadované množstvo produktu pre získanie odmeny"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__name
msgid "Name"
msgstr "Meno"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__nbr_coupons
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_coupon
msgid "Number of Coupons"
msgstr "Počet kupónov"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_generate_action
msgid "Number of Coupons To Generate"
msgstr "Počet generovaných kupónov"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_generate_wizard__generation_type__nbr_customer
msgid "Number of Selected Customers"
msgstr "Počet vybraných zákazníkov"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_generate_wizard__nbr_coupons
msgid "Number of coupons"
msgstr "Počet kupónov"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__cheapest_product
msgid "On Cheapest Product"
msgstr "Najlacnejší produkt"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__on_order
msgid "On Order"
msgstr "Na objednávku"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_apply_on
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_apply_on
msgid ""
"On Order - Discount on whole order\n"
"Cheapest product - Discount on cheapest product of the order\n"
"Specific products - Discount on selected specific products"
msgstr ""
"Na objednávku - Zľava na celú objednávku\n"
"Najlacnejší produkt - Zľava na najlacnejší produkt objednávky\n"
"Špecifické produkty - Zľava na vybrané konkrétne produkty"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__rule_products_domain
#: model:ir.model.fields,help:coupon.field_coupon_rule__rule_products_domain
msgid "On Purchase of selected product, reward will be given"
msgstr "Pri nákupe vybraného produktu bude udelená odmena"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_apply_on__specific_products
msgid "On Specific Products"
msgstr "O konkrétních produktech"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__reserved
msgid "Pending"
msgstr "Nevykonané"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_reward__discount_type__percentage
msgid "Percentage"
msgstr "Percentá"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_type
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_type
msgid ""
"Percentage - Entered percentage discount will be provided\n"
"Amount - Entered fixed amount discount will be provided"
msgstr ""
"Procento - zadaná procentní sleva bude poskytnuta\n"
"Částka - bude poskytnuta sleva na pevnou částku"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_coupon__discount_line_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "Produkt použitý v predajnej objednávke na uplatnenie zľavy."

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each coupon program "
"has its own reward product for reporting purpose"
msgstr ""
"Produkt použitý v predajnej objednávke na uplatnenie zľavy. Každý kupónový "
"program má na účely hlásenia svoj vlastný produkt odmeny"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_specific_product_ids
msgid "Products"
msgstr "Produkty"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_specific_product_ids
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_specific_product_ids
msgid ""
"Products that will be discounted if the discount is applied on specific "
"products"
msgstr ""
"Produkty, ktoré budú zlacnené, pokiaľ sa zľava uplatní na konkrétne produkty"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__program_id
msgid "Program"
msgstr "Program"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Program Name"
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__program_type
msgid "Program Type"
msgstr "Typ programu"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code_usage
msgid "Promo Code Usage"
msgstr "Použitie propagačného kódu"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__promo_code
msgid "Promotion Code"
msgstr "Propagační kód"

#. module: coupon
#: model:ir.actions.act_window,name:coupon.coupon_program_action_promo_program
msgid "Promotion Programs"
msgstr "Propagační programy"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__program_type__promotion_program
msgid "Promotional Program"
msgstr "Propagačný program"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_quantity
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Quantity"
msgstr "Množstvo"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_id
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Reward"
msgstr "Odmena"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_description
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_description
msgid "Reward Description"
msgstr "Popis odmeny"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_program__discount_line_product_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__discount_line_product_id
msgid "Reward Line Product"
msgstr "Produktový rad odmien"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_id
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_id
msgid "Reward Product"
msgstr "Produkt odmeny"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_type
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_type
msgid "Reward Type"
msgstr "Typ odmeny"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__reward_product_quantity
#: model:ir.model.fields,help:coupon.field_coupon_reward__reward_product_quantity
msgid "Reward product quantity"
msgstr "Odmeňte množstvo produktu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Rewards"
msgstr "Odmeny"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount
msgid "Rule Minimum Amount"
msgstr "Minimálna suma pravidla"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_minimum_amount_tax_inclusion
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_minimum_amount_tax_inclusion
msgid "Rule Minimum Amount Tax Inclusion"
msgstr "Pravidlo minimálna suma vrátane dane"

#. module: coupon
#: model:ir.model,name:coupon.model_report_coupon_report_coupon
msgid "Sales Coupon Report"
msgstr "Správa o predajnom kupóne"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select company"
msgstr "Výber firmy"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "Select customer"
msgstr "Vybrať zákazníka"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select product"
msgstr "Výber produktu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select products"
msgstr "Výber produktov"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Select reward product"
msgstr "Výber oceneného produktu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_tree
msgid "Send"
msgstr "Poslať"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_applicability__on_next_order
msgid "Send a Coupon"
msgstr "Pošlite kupón"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_view_form
msgid "Send by Email"
msgstr "Poslať emailom"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__sent
msgid "Sent"
msgstr "Poslané"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid ""
"Some selected customers do not have an email address and will not receive "
"the coupon."
msgstr ""
"Niektorí vybraní zákazníci ktorý nemajú e-mailovú adresu a kupón nedostanú."

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_generate_view_form
msgid "Specify a mail template to send the generated coupons as email."
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__rule_date_from
#: model:ir.model.fields,field_description:coupon.field_coupon_rule__rule_date_from
msgid "Start Date"
msgstr "Dátum začiatku"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_coupon__state
msgid "State"
msgstr "Štát"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_excluded
msgid "Tax Excluded"
msgstr "Bez dane"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_rule__rule_minimum_amount_tax_inclusion__tax_included
msgid "Tax Included"
msgstr "Vrátane dane"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Thank you,"
msgstr "Ďakujeme,"

#. module: coupon
#: model:ir.model.constraint,message:coupon.constraint_coupon_coupon_unique_coupon_code
msgid "The coupon code must be unique!"
msgstr "Kód kupónu musí byť jedinečný!"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "The coupon program for %s is in draft or closed state"
msgstr ""

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_fixed_amount
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_fixed_amount
msgid "The discount in fixed amount"
msgstr "Zľava v pevnej výške"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__discount_percentage
#: model:ir.model.fields,help:coupon.field_coupon_reward__discount_percentage
msgid "The discount in percentage, between 1 and 100"
msgstr "Zľava v percentách medzi 1 a 100"

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "The program code must be unique!"
msgstr "Programový kód musí byť jedinečný!"

#. module: coupon
#: code:addons/coupon/models/coupon_rules.py:0
#, python-format
msgid "The start date must be before the end date"
msgstr "Dátum začatia musí byť pred dátumom ukončenia"

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon %s exists but the origin sales order is not validated yet."
msgstr ""

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has already been used (%s)."
msgstr ""

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon has been cancelled (%s)."
msgstr ""

#. module: coupon
#: code:addons/coupon/models/coupon.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__total_order_count
msgid "Total Order Count"
msgstr ""

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__reward_product_uom_id
#: model:ir.model.fields,field_description:coupon.field_coupon_reward__reward_product_uom_id
msgid "Unit of Measure"
msgstr "Merná jednotka"

#. module: coupon
#: model:product.product,uom_name:coupon.product_product_10_percent_discount
#: model:product.product,uom_name:coupon.product_product_free_large_cabinet
#: model:product.template,uom_name:coupon.product_product_10_percent_discount_product_template
#: model:product.template,uom_name:coupon.product_product_free_large_cabinet_product_template
msgid "Units"
msgstr "Jednotky"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_program__promo_code_usage__code_needed
msgid "Use a code"
msgstr "Použite kód"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_generate_wizard__template_id
msgid "Use template"
msgstr "Použiť šablónu"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "Use this promo code before"
msgstr "Predtým použite tento promo kód"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__used
msgid "Used"
msgstr "Použité"

#. module: coupon
#: model:ir.model.fields.selection,name:coupon.selection__coupon_coupon__state__new
msgid "Valid"
msgstr "Platný"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_form_common
msgid "Validity"
msgstr "Platnosť"

#. module: coupon
#: model:ir.model.fields,field_description:coupon.field_coupon_program__validity_duration
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_coupon_program_form
msgid "Validity Duration"
msgstr "Trvanie platnosti"

#. module: coupon
#: model:ir.model.fields,help:coupon.field_coupon_program__validity_duration
msgid "Validity duration for a coupon after its generation"
msgstr "Trvanie platnosti kupónu po jeho vygenerovaní"

#. module: coupon
#: code:addons/coupon/models/coupon_program.py:0
#, python-format
msgid "You can not delete a program in active state"
msgstr "Program nemôžete vymazať v aktívnom stave"

#. module: coupon
#: code:addons/coupon/models/product_product.py:0
#, python-format
msgid ""
"You cannot delete a product that is linked with Coupon or Promotion program."
" Archive it instead."
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.coupon_program_view_promo_program_form
msgid "e.g. 10% Discount"
msgstr ""

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off %s"
msgstr "vypnutý%s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on %s"
msgstr "vypnutý zapnutý%s"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on some products*"
msgstr "vypnuté na niektorých výrobkoch *"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "off on the cheapest product"
msgstr "off na najlacnejší produkt"

#. module: coupon
#: model_terms:ir.ui.view,arch_db:coupon.report_coupon
msgid "on your next order"
msgstr "na vašu ďalšiu objednávku"
