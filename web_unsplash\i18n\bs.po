# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * web_unsplash
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: web_unsplash
#: model_terms:ir.ui.view,arch_db:web_unsplash.res_config_settings_view_form
msgid "<i class=\"fa fa-arrow-right\"/> Generate an Access Key"
msgstr ""

#. module: web_unsplash
#: model:ir.model.fields,field_description:web_unsplash.field_res_config_settings__unsplash_access_key
msgid "Access Key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:36
#, python-format
msgid "Access key is not set"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:66
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:44
#, python-format
msgid "Apply"
msgstr "Primjeni"

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:40
#, python-format
msgid "Generate an access key"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:43
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:65
#, python-format
msgid "Paste your access key here"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:27
#, python-format
msgid "Photos not found"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:75
#, python-format
msgid "Please check your internet connection or contact administrator."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:61
#, python-format
msgid "Please check your unsplash api key."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:8
#, python-format
msgid "Search from Unsplash"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:50
#, python-format
msgid "Search is temporary unavailable"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:72
#, python-format
msgid "Something went wrong"
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:53
#, python-format
msgid ""
"The max number of searches is exceeded. Please retry in an hour or extend to"
" a better account."
msgstr ""

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:58
#, python-format
msgid "Unauthorized Key"
msgstr ""

#. module: web_unsplash
#: model:ir.model,name:web_unsplash.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: web_unsplash
#. openerp-web
#: code:addons/web_unsplash/static/src/xml/unsplash_image_widget.xml:7
#, python-format
msgid "— or —"
msgstr ""
