<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_rule_sms_template_hr_manager" model="ir.rule">
        <field name="name">SMS Template: hr manager CUD on employee templates</field>
        <field name="model_id" ref="sms.model_sms_template"/>
        <field name="groups" eval="[(4, ref('hr.group_hr_manager'))]"/>
        <field name="domain_force">[('model_id.model', '=', 'hr.employee')]</field>
        <field name="perm_read" eval="False"/>
    </record>
</odoo>
