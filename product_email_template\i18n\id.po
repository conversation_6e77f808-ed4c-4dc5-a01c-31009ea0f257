# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_email_template
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# whenwesober, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <wahyuse<PERSON><EMAIL>>, 2022
# Abe <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Automatic Email at Invoice"
msgstr "Email Otomatis pada Faktur"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Badan"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Email Template"
msgstr "Template Email"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_move
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,field_description:product_email_template.field_product_template__email_template_id
msgid "Product Email Template"
msgstr "Templete Email Produk"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product Template"
msgstr "Templete Produk"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Send a product-specific email once the invoice is validated"
msgstr "Kirim email spesifik ke produk setelah faktur divalidasi"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,help:product_email_template.field_product_template__email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to"
" an email template."
msgstr ""
"Saat memvalidasi faktur, email akan dikirim ke pelanggan berdasarkan templat"
" ini. Pelanggan akan menerika email untuk setiap produk yang di-link ke "
"templat email."
