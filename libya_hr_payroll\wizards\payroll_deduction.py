# -*- coding: utf-8 -*-

from odoo import fields, models
from dateutil.relativedelta import relativedelta
from datetime import date, datetime


class CreateWizard(models.TransientModel):
    _name = 'payroll.deduction'
    _description = 'Payroll Deduction'

    date_from = fields.Date("Date From",default=lambda self: fields.Date.to_string(date.today().replace(day=1)),required=True)
    date_to = fields.Date("Date To",default=lambda self: fields.Date.to_string((datetime.now() + relativedelta(months=+1, day=1, days=-1)).date()),required=True)

    def generate_payroll_deduction(self):

        data = {'model': self._name, 'ids': self.ids, 'date_from': self.date_from, 'date_to': self.date_to}
        return self.env.ref('libya_hr_payroll.report_payroll_libya_deduction_pdf').report_action(self, data=data)




class PayrollDeductionReport_x1(models.AbstractModel):
    _name = 'report.libya_hr_payroll.report_libya_deduction'
    _description = 'Payroll Deduction'

    def _get_report_values(self, docids, data=None):
        exist_payslip = self.env['hr.payslip'].search(
            [('date_from', '>=', data['date_from']), ('date_to', '<=', data['date_to']),('struct_id.name', '=','يخضع للضمان والضرائب')])

        date_month = datetime.strptime(data['date_from'],'%Y-%m-%d')

        payslip_ids = []
        for payslip in exist_payslip:
            payslip_ids.append({
                'employee_name': payslip.employee_id.name,
                'basic':payslip.line_ids.filtered(lambda line: line.code == 'TOBASIC').total,
                'employee_insurance':payslip.line_ids.filtered(lambda line: line.code == 'TOBASIC').total * 0.0375,
                'company_insurance':payslip.line_ids.filtered(lambda line: line.code == 'TOBASIC').total * 0.02,
                'TASTG': payslip.line_ids.filtered(lambda line: line.code == 'TASTG').total,
                'basic_insurance':payslip.line_ids.filtered(lambda line: line.code == 'WDED').total,
                'gehad_tax':payslip.line_ids.filtered(lambda line: line.code == 'GDED').total,
                'deduction_tax':payslip.line_ids.filtered(lambda line: line.code == 'ADED').total,
                'total_tax1':payslip.line_ids.filtered(lambda line: line.code == 'TXDED1').total,
                'total_tax2':payslip.line_ids.filtered(lambda line: line.code == 'TXDED2').total,
                'total_tax':payslip.line_ids.filtered(lambda line: line.code == 'TXDED').total,
                'total_taxes':payslip.line_ids.filtered(lambda line: line.code == 'RTXDED').total,
                'net_salary':payslip.line_ids.filtered(lambda line: line.code == 'texdrab').total,
                'dm8a_tax':payslip.line_ids.filtered(lambda line: line.code == 'DMGHA').total,




            })

        return {
            'payslip_ids': payslip_ids,
            'date_month':date_month.month,
            'date_year' : date_month.year
        }




