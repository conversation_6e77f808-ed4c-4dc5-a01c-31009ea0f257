<?xml version="1.0"?>
<odoo>

    <record id="reward_view_form" model="ir.ui.view">
        <field name="name">hr.masarat.reward.form</field>
        <field name="model">hr.masarat.reward</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                    <button string="موافقة المدير العام" attrs="{'invisible': [('state','!=','draft')]}" name="set_gm_approval" type="object" class="oe_highlight" groups="hr_approvales_masarat.group_gm_reward_masarat"/>
                    <button string="رفض"
                            attrs="{'invisible': [('state','!=','draft')]}"
                            name="set_gm_refuse"
                            type="object"
                            groups="hr_approvales_masarat.group_gm_reward_masarat"/>
                    <button string="موافقة مدير الشؤون المالية"
                            attrs="{'invisible': [('state','!=','gm_approval')]}"
                            name="set_fm_approval"
                            type="object"
                            class="oe_highlight"
                            groups="hr_approvales_masarat.group_fm_reward_masarat"/>
                </header>
                <sheet>
                    <div>
                        <h2>
                            <field name="user_id"/>
                        </h2>
                    </div>
                    <group>
                        <group string="Employee Info">
                            <field name="employee_id" attrs="{'readonly': [('state','!=','draft')]}" options="{'no_create': True, 'no_create_edit':True, 'no_open':True}"/>
                            <field name="department_id" options="{'no_open':True}" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="request_date" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                        <group>
                            <field name="reward_reason" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="reward_reason_other" attrs="{'readonly': [('state','!=','draft')], 'invisible':[('reward_reason','!=','d')], 'required':[('reward_reason','=','d')]}"/>
                        </group>
                        <group>
                            <field name="employee_assigned_task" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="non_employee_assigned_task" attrs="{'readonly': [('state','!=','draft')]}"/>
                        </group>
                        <group>
                            <field name="reward_amount" attrs="{'readonly': [('state','!=','draft')]}"/>
                            <field name="is_paid"/>
                            <field name="paid_date" attrs="{'readonly': [('state','!=','gm_approval')]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="reward_view_tree" model="ir.ui.view">
        <field name="name">hr.masarat.reward.tree</field>
        <field name="model">hr.masarat.reward</field>
        <field name="arch" type="xml">
            <tree>
                <field name="employee_id"/>
                <field name="user_id"/>
                <field name="request_date"/>
            </tree>
        </field>
    </record>

    <record id="action_reward_view" model="ir.actions.act_window">
        <field name="name">نموذج مكافأة</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.masarat.reward</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem
            id="menu_reward_view"
            name="نموذج مكافأة"
            parent="hr_employees_masarat.hr_assessment_section1"
            action="action_reward_view"
            sequence="10"/>


</odoo>

