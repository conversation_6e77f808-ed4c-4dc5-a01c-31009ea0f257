# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ca
# 
# Translators:
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-04-05 15:45+0000\n"
"PO-Revision-Date: 2022-04-08 12:41+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Italian (https://www.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_ab_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_ab_en
msgid "Alberta (AB)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512102_en
#: model:account.account.template,name:l10n_ca.chart512102_en
msgid "Bonus"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2542_en
#: model:account.account.template,name:l10n_ca.chart2542_en
msgid "Bonus Accrued"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2183_en
#: model:account.account.template,name:l10n_ca.chart2183_en
msgid "Bonus to pay"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_bc_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_bc_en
msgid "British Columbia (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2141_en
#: model:account.account.template,name:l10n_ca.chart2141_en
msgid "CANADA REVENUE AGENCY"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214131_en
#: model:account.account.template,name:l10n_ca.chart214131_en
msgid "CPP - Employees Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214132_en
#: model:account.account.template,name:l10n_ca.chart214132_en
msgid "CPP - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.chart.template,name:l10n_ca.ca_en_chart_template_en
msgid "Canada - Chart of Accounts"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512301_en
#: model:account.account.template,name:l10n_ca.chart512301_en
msgid "Canada Pension Plan"
msgstr ""

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_base_document_layout
msgid "Company Document Layout"
msgstr "Struttura documenti azienda"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254102_en
#: model:account.account,name:l10n_ca.4_chart512202_en
#: model:account.account.template,name:l10n_ca.chart254102_en
#: model:account.account.template,name:l10n_ca.chart512202_en
msgid "Compensatory Days Accrued"
msgstr ""

#. module: l10n_ca
#: model:ir.model,name:l10n_ca.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1151_en
#: model:account.account.template,name:l10n_ca.chart1151_en
msgid "Customers Account"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11511_en
#: model:account.account.template,name:l10n_ca.chart11511_en
msgid "Customers Account (PoS)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214111_en
#: model:account.account.template,name:l10n_ca.chart214111_en
msgid "EI - Employees Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214112_en
#: model:account.account.template,name:l10n_ca.chart214112_en
msgid "EI - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218601_en
#: model:account.account.template,name:l10n_ca.chart218601_en
msgid "Employee Benefits Provision - Employees Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218602_en
#: model:account.account.template,name:l10n_ca.chart218602_en
msgid "Employee Benefits Provision - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512304_en
#: model:account.account.template,name:l10n_ca.chart512304_en
msgid "Employee benefits expense"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512302_en
#: model:account.account.template,name:l10n_ca.chart512302_en
msgid "Employment Insurance"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21412_en
#: model:account.account.template,name:l10n_ca.chart21412_en
msgid "Federal Income Tax"
msgstr ""

#. module: l10n_ca
#: model:ir.model.fields,field_description:l10n_ca.field_base_document_layout__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Nazione fiscale"

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gstpst_bc_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_bc_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_mb_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_mb_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_sk_purc_en
#: model:account.tax,description:l10n_ca.4_gstpst_sk_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_bc_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_bc_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_mb_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_mb_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_sk_purc_en
#: model:account.tax.template,description:l10n_ca.gstpst_sk_sale_en
msgid "GST + PST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_bc_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_bc_purc_en
msgid "GST + PST for purchases (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_mb_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_mb_purc_en
msgid "GST + PST for purchases (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sk_purc_en
#: model:account.tax.template,name:l10n_ca.gstpst_sk_purc_en
msgid "GST + PST for purchases (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_bc_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_bc_sale_en
msgid "GST + PST for sales (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_mb_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_mb_sale_en
msgid "GST + PST for sales (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sk_sale_en
#: model:account.tax.template,name:l10n_ca.gstpst_sk_sale_en
msgid "GST + PST for sales (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gstqst_purc_en
#: model:account.tax,description:l10n_ca.4_gstqst_sale_en
#: model:account.tax.template,description:l10n_ca.gstqst_purc_en
#: model:account.tax.template,description:l10n_ca.gstqst_sale_en
msgid "GST + QST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_purc_en
#: model:account.tax.template,name:l10n_ca.gstqst_purc_en
msgid "GST + QST for purchases"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_sale_en
#: model:account.tax.template,name:l10n_ca.gstqst_sale_en
msgid "GST + QST for sales"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_gst_purc_en
#: model:account.tax,description:l10n_ca.4_gst_sale_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_bc_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_mb_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_purc_sk_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_bc_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_mb_gst_en
#: model:account.tax,description:l10n_ca.4_gstpst_sale_sk_gst_en
#: model:account.tax,description:l10n_ca.4_gstqst_purc_gst_en
#: model:account.tax,description:l10n_ca.4_gstqst_sale_gst_en
#: model:account.tax.group,name:l10n_ca.tax_group_gst_5
#: model:account.tax.template,description:l10n_ca.gst_purc_en
#: model:account.tax.template,description:l10n_ca.gst_sale_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_bc_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_mb_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_purc_sk_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_bc_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_mb_gst_en
#: model:account.tax.template,description:l10n_ca.gstpst_sale_sk_gst_en
#: model:account.tax.template,description:l10n_ca.gstqst_purc_gst_en
#: model:account.tax.template,description:l10n_ca.gstqst_sale_gst_en
msgid "GST 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_gst_7
msgid "GST 7%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_gst_8
msgid "GST 8%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gst_purc_en
#: model:account.tax.template,name:l10n_ca.gst_purc_en
msgid "GST for purchases - 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_bc_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_bc_gst_en
msgid "GST for purchases - 5% (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_mb_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_mb_gst_en
msgid "GST for purchases - 5% (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_purc_gst_en
#: model:account.tax.template,name:l10n_ca.gstqst_purc_gst_en
msgid "GST for purchases - 5% (QC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_purc_sk_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_purc_sk_gst_en
msgid "GST for purchases - 5% (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gst_sale_en
#: model:account.tax.template,name:l10n_ca.gst_sale_en
msgid "GST for sales - 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_bc_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_bc_gst_en
msgid "GST for sales - 5% (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_mb_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_mb_gst_en
msgid "GST for sales - 5% (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstqst_sale_gst_en
#: model:account.tax.template,name:l10n_ca.gstqst_sale_gst_en
msgid "GST for sales - 5% (QC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_gstpst_sale_sk_gst_en
#: model:account.tax.template,name:l10n_ca.gstpst_sale_sk_gst_en
msgid "GST for sales - 5% (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1181_en
#: model:account.account.template,name:l10n_ca.chart1181_en
msgid "GST receivable"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2131_en
#: model:account.account.template,name:l10n_ca.chart2131_en
msgid "GST to pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512303_en
#: model:account.account.template,name:l10n_ca.chart512303_en
msgid "Group Pension Plan"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218501_en
#: model:account.account.template,name:l10n_ca.chart218501_en
msgid "Group Pension Plan to pay - Employees Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart218502_en
#: model:account.account.template,name:l10n_ca.chart218502_en
msgid "Group Pension Plan to pay - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_hst13_purc_en
#: model:account.tax,description:l10n_ca.4_hst13_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_hst_13
#: model:account.tax.template,description:l10n_ca.hst13_purc_en
#: model:account.tax.template,description:l10n_ca.hst13_sale_en
msgid "HST 13%"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_hst_14
msgid "HST 14%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_hst15_purc_en
#: model:account.tax,description:l10n_ca.4_hst15_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_hst_15
#: model:account.tax.template,description:l10n_ca.hst15_purc_en
#: model:account.tax.template,description:l10n_ca.hst15_sale_en
msgid "HST 15%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst13_purc_en
#: model:account.tax.template,name:l10n_ca.hst13_purc_en
msgid "HST for purchases - 13%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst15_purc_en
#: model:account.tax.template,name:l10n_ca.hst15_purc_en
msgid "HST for purchases - 15%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst13_sale_en
#: model:account.tax.template,name:l10n_ca.hst13_sale_en
msgid "HST for sales - 13%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_hst15_sale_en
#: model:account.tax.template,name:l10n_ca.hst15_sale_en
msgid "HST for sales - 15%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11831_en
#: model:account.account.template,name:l10n_ca.chart11831_en
msgid "HST receivable - 13%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11832_en
#: model:account.account.template,name:l10n_ca.chart11832_en
msgid "HST receivable - 14%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart11833_en
#: model:account.account.template,name:l10n_ca.chart11833_en
msgid "HST receivable - 15%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21331_en
#: model:account.account.template,name:l10n_ca.chart21331_en
msgid "HST to pay - 13%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21332_en
#: model:account.account.template,name:l10n_ca.chart21332_en
msgid "HST to pay - 14%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21333_en
#: model:account.account.template,name:l10n_ca.chart21333_en
msgid "HST to pay - 15%"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart412_en
#: model:account.account.template,name:l10n_ca.chart412_en
msgid "Harmonized Provinces Sales"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512314_en
#: model:account.account.template,name:l10n_ca.chart512314_en
msgid "Health Service Fund"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21421_en
#: model:account.account.template,name:l10n_ca.chart21421_en
msgid "Health Services Fund to pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5111_en
#: model:account.account.template,name:l10n_ca.chart5111_en
msgid "Inside Purchases"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart411_en
#: model:account.account.template,name:l10n_ca.chart411_en
msgid "Inside Sales"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_intl_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_intl_en
msgid "International (INTL)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5114_en
#: model:account.account.template,name:l10n_ca.chart5114_en
msgid "International Purchases"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart414_en
#: model:account.account.template,name:l10n_ca.chart414_en
msgid "International Sales"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512312_en
#: model:account.account.template,name:l10n_ca.chart512312_en
msgid "Labour Health and Safety"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21424_en
#: model:account.account.template,name:l10n_ca.chart21424_en
msgid "Labour Health and Safety to pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512313_en
#: model:account.account.template,name:l10n_ca.chart512313_en
msgid "Labour Standards"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21425_en
#: model:account.account.template,name:l10n_ca.chart21425_en
msgid "Labour Standards to pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_ca_en_chart_template_en_liquidity_transfer
#: model:account.account.template,name:l10n_ca.ca_en_chart_template_en_liquidity_transfer
msgid "Liquidity Transfer"
msgstr "Trasferimento di liquidità"

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_mb_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_mb_en
msgid "Manitoba (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart55_en
#: model:account.account.template,name:l10n_ca.chart55_en
msgid "NON-OPERATING EXPENSES"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart42_en
#: model:account.account.template,name:l10n_ca.chart42_en
msgid "NON-OPERATING INCOMES"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nb_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nb_en
msgid "New Brunswick (NB)"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nl_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nl_en
msgid "Newfoundland and Labrador (NL)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart413_en
#: model:account.account.template,name:l10n_ca.chart413_en
msgid "Non-Harmonized Provinces Sales"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nt_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nt_en
msgid "Northwest Territories (NT)"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_ns_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_ns_en
msgid "Nova Scotia (NS)"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_nu_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_nu_en
msgid "Nunavut (NU)"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_on_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_on_en
msgid "Ontario (ON)"
msgstr ""

#. module: l10n_ca
#: model:ir.model.fields,field_description:l10n_ca.field_base_document_layout__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_company__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_partner__l10n_ca_pst
#: model:ir.model.fields,field_description:l10n_ca.field_res_users__l10n_ca_pst
msgid "PST"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_sk_purc_en
#: model:account.tax,description:l10n_ca.4_pst_sk_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_pst_5
#: model:account.tax.template,description:l10n_ca.pst_sk_purc_en
#: model:account.tax.template,description:l10n_ca.pst_sk_sale_en
msgid "PST 5%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_bc_purc_en
#: model:account.tax,description:l10n_ca.4_pst_bc_sale_en
#: model:account.tax.template,description:l10n_ca.pst_bc_purc_en
#: model:account.tax.template,description:l10n_ca.pst_bc_sale_en
msgid "PST 7%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_pst_mb_purc_en
#: model:account.tax,description:l10n_ca.4_pst_mb_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_pst_8
#: model:account.tax.template,description:l10n_ca.pst_mb_purc_en
#: model:account.tax.template,description:l10n_ca.pst_mb_sale_en
msgid "PST 8%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_sk_purc_en
#: model:account.tax.template,name:l10n_ca.pst_sk_purc_en
msgid "PST for purchases - 5% (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_bc_purc_en
#: model:account.tax.template,name:l10n_ca.pst_bc_purc_en
msgid "PST for purchases - 7% (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_mb_purc_en
#: model:account.tax.template,name:l10n_ca.pst_mb_purc_en
msgid "PST for purchases - 8% (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_sk_sale_en
#: model:account.tax.template,name:l10n_ca.pst_sk_sale_en
msgid "PST for sales - 5% (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_bc_sale_en
#: model:account.tax.template,name:l10n_ca.pst_bc_sale_en
msgid "PST for sales - 7% (BC)"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_pst_mb_sale_en
#: model:account.tax.template,name:l10n_ca.pst_mb_sale_en
msgid "PST for sales - 8% (MB)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1182_en
#: model:account.account.template,name:l10n_ca.chart1182_en
msgid "PST/QST receivable"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2132_en
#: model:account.account.template,name:l10n_ca.chart2132_en
msgid "PST/QST to pay"
msgstr ""

#. module: l10n_ca
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_bold
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_boxed
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_standard
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_external_layout_striped
#: model_terms:ir.ui.view,arch_db:l10n_ca.l10n_ca_report_invoice_document_inherit
msgid "PST:"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214231_en
#: model:account.account.template,name:l10n_ca.chart214231_en
msgid "Parental Insurance Plan - Employee Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214232_en
#: model:account.account.template,name:l10n_ca.chart214232_en
msgid "Parental Insurance Plan - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_pe_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_pe_en
msgid "Prince Edward Islands (PE)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart21426_en
#: model:account.account.template,name:l10n_ca.chart21426_en
msgid "Provincial Income Tax"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512311_en
#: model:account.account.template,name:l10n_ca.chart512311_en
msgid "Provincial Parental Insurance Plan"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512310_en
#: model:account.account.template,name:l10n_ca.chart512310_en
msgid "Provincial Pension Plan"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214221_en
#: model:account.account.template,name:l10n_ca.chart214221_en
msgid "Provincial Pension Plan - Employees Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart214222_en
#: model:account.account.template,name:l10n_ca.chart214222_en
msgid "Provincial Pension Plan - Employer Contribution"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2521_en
#: model:account.account.template,name:l10n_ca.chart2521_en
msgid "Provision for pension plans"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5112_en
#: model:account.account.template,name:l10n_ca.chart5112_en
msgid "Purchases in harmonized provinces"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart5113_en
#: model:account.account.template,name:l10n_ca.chart5113_en
msgid "Purchases in non-harmonized provinces"
msgstr ""

#. module: l10n_ca
#: model:account.tax,description:l10n_ca.4_qst_purc_en
#: model:account.tax,description:l10n_ca.4_qst_sale_en
#: model:account.tax.group,name:l10n_ca.tax_group_qst_9975
#: model:account.tax.template,description:l10n_ca.qst_purc_en
#: model:account.tax.template,description:l10n_ca.qst_sale_en
msgid "QST 9.975%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_qst_purc_en
#: model:account.tax.template,name:l10n_ca.qst_purc_en
msgid "QST for purchases - 9.975%"
msgstr ""

#. module: l10n_ca
#: model:account.tax,name:l10n_ca.4_qst_sale_en
#: model:account.tax.template,name:l10n_ca.qst_sale_en
msgid "QST for sales - 9.975%"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_qc_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_qc_en
msgid "Quebec (QC)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512101_en
#: model:account.account.template,name:l10n_ca.chart512101_en
msgid "Regular Salaries"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart512103_en
#: model:account.account.template,name:l10n_ca.chart512103_en
msgid "Retroactive Pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2184_en
#: model:account.account.template,name:l10n_ca.chart2184_en
msgid "Retroactive Payment to pay"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2181_en
#: model:account.account.template,name:l10n_ca.chart2181_en
msgid "Salaries to pay"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_sk_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_sk_en
msgid "Saskatchewan (SK)"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254103_en
#: model:account.account,name:l10n_ca.4_chart512203_en
#: model:account.account.template,name:l10n_ca.chart254103_en
#: model:account.account.template,name:l10n_ca.chart512203_en
msgid "Sick Leaves Accrued"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1145_en
#: model:account.account.template,name:l10n_ca.chart1145_en
msgid "Stock Delivered But Not Billed"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart1141_en
#: model:account.account.template,name:l10n_ca.chart1141_en
msgid "Stock In Hand"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2171_en
#: model:account.account.template,name:l10n_ca.chart2171_en
msgid "Stock Received But Not Billed"
msgstr ""

#. module: l10n_ca
#: model:account.tax.group,name:l10n_ca.tax_group_fix
msgid "Taxes"
msgstr "Imposte"

#. module: l10n_ca
#: model:ir.model.fields,help:l10n_ca.field_base_document_layout__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr "Il paese da cui utilizzare i rapporti fiscali per questa azienda"

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart254101_en
#: model:account.account,name:l10n_ca.4_chart512201_en
#: model:account.account.template,name:l10n_ca.chart254101_en
#: model:account.account.template,name:l10n_ca.chart512201_en
msgid "Vacations Accrued"
msgstr ""

#. module: l10n_ca
#: model:account.account,name:l10n_ca.4_chart2111_en
#: model:account.account.template,name:l10n_ca.chart2111_en
msgid "Vendors Account"
msgstr ""

#. module: l10n_ca
#: model:account.fiscal.position,name:l10n_ca.4_fiscal_position_template_yt_en
#: model:account.fiscal.position.template,name:l10n_ca.fiscal_position_template_yt_en
msgid "Yukon (YT)"
msgstr ""
