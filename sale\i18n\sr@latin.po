# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
# 
# Translators:
# <PERSON><PERSON><PERSON> <neman<PERSON><PERSON><PERSON><EMAIL>>, 2017
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jo<PERSON>v <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-24 09:00+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"\n"
"<p>Dear ${object.partner_id.name}\n"
"% set access_action = object.with_context(force_website=True).get_access_action()\n"
"% set is_online = access_action and access_action['type'] == 'ir.actions.act_url'\n"
"% set doc_name = 'quotation' if object.state in ('draft', 'sent') else 'order confirmation'\n"
"% set pay_sign_name =  object.get_portal_confirmation_action()\n"
"% set access_name = is_online and object.state in ('draft', 'sent') and pay_sign_name in ('pay', 'sign') and 'Accept and %s online' % pay_sign_name or 'View %s' % doc_name\n"
"% set access_url = is_online and object.get_mail_url() or ''\n"
"\n"
"% if object.partner_id.parent_id:\n"
"    (<i>${object.partner_id.parent_id.name}</i>)\n"
"% endif\n"
",</p>\n"
"<p>\n"
"Here is\n"
"% if ctx.get('proforma')\n"
"in attachment, your pro-forma invoice\n"
"% else\n"
"the ${doc_name} <strong>${object.name}</strong>\n"
"% endif\n"
"% if object.origin:\n"
"(with reference: ${object.origin} )\n"
"% endif\n"
"amounting in <strong>${object.amount_total} ${object.pricelist_id.currency_id.name}</strong>\n"
"from ${object.company_id.name}.\n"
"</p>\n"
"\n"
"% if is_online and not ctx.get('proforma'):\n"
"    <br/><br/>\n"
"    <center>\n"
"        <a href=\"${access_url}\" style=\"background-color: #1abc9c; padding: 20px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 16px;\" class=\"o_default_snippet_text\">${access_name}</a>\n"
"        <br/><br/><br/>\n"
"        <span style=\"color:#888888\">(or view attached PDF)</span>\n"
"    </center>\n"
"    <br/>\n"
"% endif\n"
"\n"
"<p>You can reply to this email if you have any questions.</p>\n"
"<p>Thank you,</p>\n"
"\n"
"<p style=\"color:#888888;\">\n"
"% if object.user_id and object.user_id.signature:\n"
"    ${object.user_id.signature | safe}\n"
"% endif\n"
"</p>\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_sales_count
#: model:ir.model.fields,field_description:sale.field_product_template_sales_count
msgid "# Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_count
msgid "# of Invoices"
msgstr "# faktura"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_nbr
msgid "# of Lines"
msgstr "# linija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_count
msgid "# of Orders"
msgstr "# naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users_sale_order_count
msgid "# of Sales Order"
msgstr "# prodajnih naloga"

#. module: sale
#: model:mail.template,report_name:sale.email_template_edi_sale
msgid ""
"${(object.name or '').replace('/','_')}${object.state == 'draft' and "
"'_draft' or ''}"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"${object.company_id.name} ${object.state in ('draft', 'sent') and "
"'Quotation' or 'Order'} (Ref ${object.name or 'n/a' })"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_data_notification_email_sale_order
msgid "${object.subject}"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "&amp;bull;"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "&amp;nbsp;<span>on</span>&amp;nbsp;"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_data_notification_email_sale_order
msgid ""
"<html>\n"
"                <head></head>\n"
"                % set record = ctx.get('record')\n"
"                % set company = record and record.company_id or user.company_id\n"
"                <body style=\"margin: 0; padding: 0;\">\n"
"                <table border=\"0\" width=\"100%\" cellpadding=\"0\" bgcolor=\"#ededed\" style=\"padding: 20px; background-color: #ededed; border-collapse:separate;\" summary=\"o_mail_notification\">\n"
"                    <tbody>\n"
"\n"
"                      <!-- HEADER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\">\n"
"                                  <span style=\"font-size:20px; color:white; font-weight: bold;\">\n"
"                                      ${object.record_name}\n"
"                                  </span>\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\">\n"
"                                  <img src=\"/logo.png?company=${company.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${company.name}\">\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- CONTENT -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\" style=\"min-width: 590px; background-color: rgb(255, 255, 255); padding: 20px; border-collapse:separate;\">\n"
"                            <tbody>\n"
"                              <td valign=\"top\" style=\"font-family:Arial,Helvetica,sans-serif; color: #555; font-size: 14px;\">\n"
"                                ${object.body | safe}\n"
"                              </td>\n"
"                            </tbody>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"\n"
"                      <!-- FOOTER -->\n"
"                      <tr>\n"
"                        <td align=\"center\" style=\"min-width: 590px;\">\n"
"                          <table width=\"590\" border=\"0\" cellpadding=\"0\" bgcolor=\"#875A7B\" style=\"min-width: 590px; background-color: rgb(135,90,123); padding: 20px; border-collapse:separate;\">\n"
"                            <tr>\n"
"                              <td valign=\"middle\" align=\"left\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                ${company.name}<br/>\n"
"                                ${company.phone or ''}\n"
"                              </td>\n"
"                              <td valign=\"middle\" align=\"right\" style=\"color: #fff; padding-top: 10px; padding-bottom: 10px; font-size: 12px;\">\n"
"                                % if company.email:\n"
"                                <a href=\"mailto:${company.email}\" style=\"text-decoration:none; color: white;\">${company.email}</a><br/>\n"
"                                % endif\n"
"                                % if company.website:\n"
"                                    <a href=\"${company.website}\" style=\"text-decoration:none; color: white;\">\n"
"                                        ${company.website}\n"
"                                    </a>\n"
"                                % endif\n"
"                              </td>\n"
"                            </tr>\n"
"                          </table>\n"
"                        </td>\n"
"                      </tr>\n"
"                      <tr>\n"
"                        <td align=\"center\">\n"
"                            Powered by <a target=\"_blank\" href=\"https://www.odoo.com\">Odoo</a>.\n"
"                        </td>\n"
"                      </tr>\n"
"                    </tbody>\n"
"                </table>\n"
"                </body>\n"
"                </html>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<i class=\"fa fa-arrow-circle-right\"/> Accept &amp; Sign"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<i class=\"fa fa-check-circle\"/> Signed"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"<i>Example: pre-paid service offers for which the customer have\n"
"                to buy an extra pack of hours, because he used all his support\n"
"                hours.</i>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"hidden-xs\">Sales Order #</span>\n"
"                      <span class=\"visible-xs\">Ref.</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"label label-danger\"><i class=\"fa fa-fw fa-warning\"/><span "
"class=\"hidden-xs\"> Problem</span></span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"label label-default hidden-xs\"><i class=\"fa fa-fw fa-"
"check\"/><span class=\"hidden-xs\"> Done</span></span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-clock-o\"/> "
"Expired</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/> "
"Cancelled</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid ""
"<span class=\"label label-default\"><i class=\"fa fa-fw fa-remove\"/> "
"Done</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid ""
"<span class=\"label label-info orders_label_text_align\"><i class=\"fa fa-fw"
" fa-clock-o\"/> Waiting</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"label label-info\"><i class=\"fa fa-fw fa-clock-o\"/><span "
"class=\"hidden-xs\"> Invoiced</span></span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid ""
"<span class=\"label label-success orders_label_text_align\"><i class=\"fa "
"fa-fw fa-check\"/> Paid</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<span>Pro-Forma Invoice # </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Contact</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Date Ordered:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Expiration Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Fiskalna pozicija:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Invoices</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Invoicing Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Payment Terms:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Product</strong>"
msgstr "<strong>Proizvod</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Quantity</strong>"
msgstr "<strong>Količina</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Quotation Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Prodavač:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Shipping Address</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_document_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping address:</strong>"
msgstr "<strong>Adresa isporuke:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Subtotal: </strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Jedinična cijena</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Vaša oznaka:</strong>"

#. module: sale
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "A single sales price per product"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "Accept &amp; Sign"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Account used for deposits"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_pagebreak
msgid "Add pagebreak"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_subtotal
msgid "Add subtotal"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_delivery_invoice_address
msgid "Addresses in Sales Orders"
msgstr "Adrese na prodajnim nalozima"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:156
#, python-format
msgid "Advance: %s"
msgstr "Unaprijed: %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model:ir.ui.menu,name:sale.report_all_channels_sales
msgid "All Channels Sales Orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_pivot
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
msgid "All Channels Sales Orders Analysis"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_report_all_channels_sales
msgid "All sales orders grouped by sales channels"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allow manual discounts on order lines"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings_group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Amount"
msgstr "Iznos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_amt_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_report_amt_invoiced
msgid "Amount Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_amt_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report_amt_to_invoice
msgid "Amount To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_quotations_amount
msgid "Amount of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_order_analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report_analytic_account_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: sale
#: model:res.groups,name:sale.group_analytic_accounting
msgid "Analytic Accounting for Sales"
msgstr "Analitičko računovodstvo za prodaju"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analiticki red"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: sale
#: selection:product.template,expense_policy:0
msgid "At cost"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_variants_action
msgid "Attribute Values"
msgstr "Vrijednosti atributa"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atributi"

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "Blocking Message"
msgstr "Blokirajuća Poruka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with two kinds of discount programs: promotions and coupon "
"codes. Specific conditions can be set (products, customers, minimum purchase"
" amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_search_view
msgid "Breaks Page"
msgstr ""

#. module: sale
#: model:ir.filters,name:sale.filter_isale_report_product
msgid "By Product"
msgstr "Po proizvodu"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salespersons
msgid "By Salespersons"
msgstr "Po prodavaču"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_salesteam
msgid "By Salesteam"
msgstr "Po timu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered_updateable
msgid "Can Edit Delivered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_updatable
msgid "Can Edit Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Odustani"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Cancelled"
msgstr "Poništeno"

#. module: sale
#: model:ir.ui.menu,name:sale.product_menu_catalog
msgid "Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_quotations
msgid ""
"Check this box if you send quotations to your customers rather than "
"confirming orders straight away. This will add specific action buttons to "
"your dashboard."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_use_invoices
msgid "Check this box to set an invoicing target for this sales channel."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Click to define a target"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_company_id
#: model:ir.model.fields,field_description:sale.field_sale_report_company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Preduzeće"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Postavka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm Sale"
msgstr "Potvrdi prodaju"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_confirmation_date
#: model:ir.model.fields,field_description:sale.field_sale_report_confirmation_date
msgid "Confirmation Date"
msgstr "Datum Potvrde"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_sale_coupon
msgid "Coupons & Promotions"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Invoices"
msgstr "Kreiraj fakture"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid "Create a Quotation, the first step of a new sale."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create and View Invoices"
msgstr "Kreiraj i pregledaj fakture"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_create_date
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_create_date
msgid "Creation Date"
msgstr "Kreiran dana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
msgid "Current Year Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Kupac"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_sale_delivery_address
msgid "Customer Addresses"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_client_order_ref
msgid "Customer Reference"
msgstr "Oznaka kupca"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Customer Taxes"
msgstr "Porezi kod prodaje"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Kupci"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_date_order
#: model:ir.model.fields,field_description:sale.field_sale_report_date
msgid "Date Order"
msgstr "Naručeno dana"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_create_date
msgid "Date on which sales order is created."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_confirmation_date
msgid "Date on which the sales order is confirmed."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_use_sale_note
msgid "Default Terms & Conditions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company_sale_note
msgid "Default Terms and Conditions"
msgstr "Podrazumjevani uslovi"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings_default_deposit_product_id
msgid "Default product used for payment advances"
msgstr "Podrazumjevani proizvod korišten kod avansnog plaćanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_delivered
msgid "Delivered"
msgstr "Dostavljeno"

#. module: sale
#: code:addons/sale/models/sale.py:824
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Delivered Quantity"
msgstr "Dostavljena količina"

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Delivered quantities"
msgstr "Dostavljene količine"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice_partner_shipping_id
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_shipping_id
msgid "Delivery Address"
msgstr "Adresa dostave"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_customer_lead
msgid "Delivery Lead Time"
msgstr "Vreme trajanja isporuke"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_invoice_partner_shipping_id
msgid "Delivery address for current invoice."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_shipping_id
msgid "Delivery address for current sales order."
msgstr "Adresa dostave za trenutni prodajni nalog"

#. module: sale
#: model:product.product,name:sale.advance_product_0
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_default_deposit_product_id
msgid "Deposit Product"
msgstr "Proizvod za depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_name
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Description"
msgstr "Opis"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Design standardized offers"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_website_sale_digital
msgid "Digital Content"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Disc.(%)"
msgstr "Popust (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_pricelist_discount_policy
msgid "Discount Policy"
msgstr "Strategija kod popusta"

#. module: sale
#: selection:product.pricelist,discount_policy:0
msgid "Discount included in the price"
msgstr "Popust uključen u cijenu"

#. module: sale
#: model:res.groups,name:sale.group_discount_per_so_line
msgid "Discount on lines"
msgstr "Popust po stavkama"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_discount_per_so_line
msgid "Discounts"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_display_name
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma_display_name
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_display_name
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line_display_name
#: model:ir.model.fields,field_description:sale.field_sale_report_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:84
#, python-format
msgid "Down Payment"
msgstr "Avansno plaćanje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_amount
msgid "Down Payment Amount"
msgstr "Iznos avansnog plaćanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_product_id
msgid "Down Payment Product"
msgstr "Proizvod za avansno plaćanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Down Payments"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (fixed amount)"
msgstr "Avansno plaćanje (fiksni iznos)"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Down payment (percentage)"
msgstr "Avansno plaćanje (procenat)"

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:81
#, python-format
msgid "Down payment of %s%%"
msgstr "Avansno plaćanje u iznosu od %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
msgid "Download"
msgstr "Preuzmi"

#. module: sale
#: selection:sale.report,state:0
msgid "Draft Quotation"
msgstr "Ponuda u nacrtu"

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_validity_date
msgid "Expiration Date"
msgstr "Datum isteka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Prošireni filteri"

#. module: sale
#: code:addons/sale/models/sale.py:807
#, python-format
msgid "Extra line with %s "
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Fully Invoiced"
msgstr "Potpuno fakturisano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_weight
msgid "Gross Weight"
msgstr "Ukupna težina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_search_view
msgid "Group By Name"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_id
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma_id
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_id
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_id
#: model:ir.model.fields,field_description:sale.field_sale_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_sale_report_id
msgid "ID"
msgstr "ID"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false)."
" It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_deposit_account_id
msgid "Income Account"
msgstr "Konto prihoda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Insert your terms & conditions here..."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:216
#, python-format
msgid "Invalid order"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: sale
#: code:addons/sale/models/account_invoice.py:52
#, python-format
msgid "Invoice %s paid"
msgstr "Faktura %s plaćena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_partner_invoice_id
msgid "Invoice Address"
msgstr "Adresa fakture"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Faktura potvrdjena"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Faktura kreirana"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka računa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_lines
msgid "Invoice Lines"
msgstr "Stavke fakture"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Invoice Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line_invoice_status
msgid "Invoice Status"
msgstr "Status fakture"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_partner_invoice_id
msgid "Invoice address for current sales order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""

#. module: sale
#: selection:res.config.settings,default_invoice_policy:0
msgid "Invoice what is delivered"
msgstr ""

#. module: sale
#: selection:res.config.settings,default_invoice_policy:0
msgid "Invoice what is ordered"
msgstr ""

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines"
msgstr "Stavke koje se mogu fakturisati"

#. module: sale
#: selection:sale.advance.payment.inv,advance_payment_method:0
msgid "Invoiceable lines (deduct down payments)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_invoiced
msgid "Invoiced"
msgstr "Fakturisano"

#. module: sale
#: code:addons/sale/models/sale.py:825
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Invoiced Quantity"
msgstr "Fakturisana količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced
msgid "Invoiced This Month"
msgstr "Fakturisano ovaj mjesec"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Fakture"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Analiza faktura"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Statistika faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"Invoices will be created in draft so that you can review\n"
"                        them before validation."
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:96
#, python-format
msgid "Invoices: Untaxed Total"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_invoice_policy
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template_invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings_default_invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_invoiced_target
msgid "Invoicing Target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing address:"
msgstr "Adresa fakturisanja:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and shipping address:"
msgstr "Adresa fakturisanja i dostave:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_is_downpayment
msgid "Is a down payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_is_expired
msgid "Is expired"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales___last_update
#: model:ir.model.fields,field_description:sale.field_report_sale_report_saleproforma___last_update
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv___last_update
#: model:ir.model.fields,field_description:sale.field_sale_layout_category___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order___last_update
#: model:ir.model.fields,field_description:sale.field_sale_order_line___last_update
#: model:ir.model.fields,field_description:sale.field_sale_report___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_write_date
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line_write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_layout_category_sequence
#: model:ir.model.fields,field_description:sale.field_sale_order_line_layout_category_sequence
msgid "Layout Sequence"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers sign &amp; pay online"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Line subtotals in sales orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_auto_done_setting
msgid "Lock Confirmed Orders"
msgstr ""

#. module: sale
#: selection:sale.order,state:0
msgid "Locked"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage promotion &amp; coupon programs"
msgstr ""

#. module: sale
#: selection:product.template,service_type:0
msgid "Manually set quantities on order"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_service_type
#: model:ir.model.fields,help:sale.field_product_template_service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_validity_date
msgid ""
"Manually set the expiration date of your quotation (offer), or it will set "
"the date automatically based on the template if online quotation is "
"installed."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_sale_margin
msgid "Margins"
msgstr "Marže"

#. module: sale
#: model:sale.layout_category,name:sale.sale_layout_cat_2
msgid "Material"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users_sale_warn_msg
msgid "Message for Sales Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template_sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_multi_sales_price
msgid "Multiple Sales Prices per Product"
msgstr ""

#. module: sale
#: selection:res.config.settings,multi_sales_price_method:0
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "Multiple prices per product (e.g. customer segments, currencies)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Activities"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Moji prodajni nalozi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Moje stavke naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_name
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_search_view
msgid "Name"
msgstr "Naziv"

#. module: sale
#: code:addons/sale/models/sale.py:115 code:addons/sale/models/sale.py:275
#: code:addons/sale/models/sale.py:277 code:addons/sale/models/sale.py:279
#, python-format
msgid "New"
msgstr "Novi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr ""

#. module: sale
#: selection:product.template,expense_policy:0
msgid "No"
msgstr "Ne"

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "No Message"
msgstr "Nema Poruke"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Note that once a Quotation becomes a Sales Order, it will be moved\n"
"                from the Quotations list to the Sales Order list."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
msgid ""
"Note that once a Quotation becomes a Sales Order, it will be moved\n"
"            from the Quotations list to the Sales Order list."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Note that once a Quotation becomes a Sales Order, it will be moved from the "
"Quotations list to the Sales Order list."
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Nothing to Invoice"
msgstr "Ništa za fakturisati"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line_customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_quotations_count
msgid "Number of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.\n"
"                    You'll be able to invoice it and collect payments.\n"
"                    From the <i>Sales Orders</i> menu, you can track delivery\n"
"                    orders or services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_sale_payment
#: model:ir.model.fields,field_description:sale.field_res_config_settings_portal_confirmation
msgid "Online Signature & Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_portal_confirmation_options
msgid "Online Signature & Payment options"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:32
#, python-format
msgid "Only Integer Value should be valid."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:209
#, python-format
msgid "Operation not allowed"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Nalog"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Nalog #"

#. module: sale
#: code:addons/sale/controllers/portal.py:86
#: code:addons/sale/controllers/portal.py:138
#: model:ir.model.fields,field_description:sale.field_sale_order_date_order
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#, python-format
msgid "Order Date"
msgstr "Datum naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Order Line Sections"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Stavke narudžbe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Month"
msgstr "Mjesec naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
msgid "Order Number"
msgstr "Broj naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line_order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_name
#: model:ir.model.fields,field_description:sale.field_sale_report_name
msgid "Order Reference"
msgstr "Oznaka naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_state
msgid "Order Status"
msgstr "Status narudžbe"

#. module: sale
#: code:addons/sale/controllers/portal.py:218
#, python-format
msgid "Order is not in a state requiring customer validation."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:225
#, python-format
msgid "Order signed by %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Order to Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Ordered Qty"
msgstr "Naručena količina"

#. module: sale
#: code:addons/sale/models/sale.py:821
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Ordered Quantity"
msgstr "Naručena količina"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_invoice_policy
#: model:ir.model.fields,help:sale.field_product_template_invoice_policy
msgid ""
"Ordered Quantity: Invoice based on the quantity the customer ordered.\n"
"Delivered Quantity: Invoiced based on the quantity the vendor delivered (time or deliveries)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Ordered date of the sales order"
msgstr ""

#. module: sale
#: selection:product.template,invoice_policy:0
msgid "Ordered quantities"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Narudžbe"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
msgid "Orders to Invoice"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"Orders to upsell are orders having products with an invoicing\n"
"                policy based on <i>ordered quantities</i> for which you have\n"
"                delivered more than what have been ordered."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Information"
msgstr "Ostale informacije"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report_partner_id
msgid "Partner"
msgstr "Partner"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_country_id
#: model:ir.model.fields,field_description:sale.field_sale_report_country_id
msgid "Partner Country"
msgstr "Država partnera"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Partner's Country"
msgstr "Država partnera"

#. module: sale
#: selection:res.config.settings,portal_confirmation_options:0
msgid "Payment"
msgstr "Plaćanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Acquirers"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_payment_term_id
msgid "Payment Terms"
msgstr "Uslovi plaćanja"

#. module: sale
#: model:res.groups,name:sale.group_sale_layout
msgid "Personalize sales order and invoice report"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:353
#, python-format
msgid "Please define an accounting sales journal for this company."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:916
#, python-format
msgid ""
"Please define income account for this product: \"%s\" (id:%d) - or for its "
"category: \"%s\"."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
msgid "Price"
msgstr "Cijena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce
msgid "Price Reduce"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_price_subtotal
msgid "Price Subtotal"
msgstr ""

#. module: sale
#: selection:res.config.settings,sale_pricelist_setting:0
msgid "Price computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_pricelist
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_order_pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report_pricelist_id
msgid "Pricelist"
msgstr "Cjenovnik"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_pricelist_id
msgid "Pricelist for current sales order."
msgstr "Cjenovnik za trenutni prodajni nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_multi_sales_price_method
#: model:ir.model.fields,field_description:sale.field_res_config_settings_sale_pricelist_setting
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnici"

#. module: sale
#: selection:res.config.settings,multi_sales_price_method:0
msgid "Prices computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Odredjivanje cijene"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Print"
msgstr "Štampaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Predračuni"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_id
#: model:ir.model.fields,field_description:sale.field_sale_order_product_id
#: model:ir.model.fields,field_description:sale.field_sale_report_product_id
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Proizvod"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_categ_id
#: model:ir.model.fields,field_description:sale.field_sale_report_categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Grupa proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_image
msgid "Product Image"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_product_tmpl_id
#: model:ir.model.fields,field_description:sale.field_sale_report_product_tmpl_id
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
msgid "Products"
msgstr "Proizvodi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Kol."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_delivered
msgid "Qty Delivered"
msgstr "Dostavljena količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_invoiced
msgid "Qty Invoiced"
msgstr "Fakturisana količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom_qty
msgid "Qty Ordered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_qty_to_invoice
msgid "Qty To Invoice"
msgstr "Količina za fafkturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quantity"
msgstr "Količina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_page
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
#: selection:sale.order,state:0
msgid "Quotation"
msgstr "Ponuda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Ponuda #"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "Ponuda / Nalog"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotation Number"
msgstr "Broj ponude"

#. module: sale
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Quotation Sent"
msgstr "Ponuda poslata"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Ponuda potvrdjena"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Ponuda poslata"

#. module: sale
#: code:addons/sale/models/sales_team.py:101
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.model.fields,field_description:sale.field_crm_team_use_quotations
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Quotations"
msgstr "Ponude"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations &amp; Orders"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Analiza ponuda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Quotations Sent"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_website_quote
msgid "Quotations Templates"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Ponude i prodaje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template_expense_policy
msgid "Re-Invoice Expenses"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:87
#: code:addons/sale/controllers/portal.py:139
#, python-format
msgid "Reference"
msgstr "Šifra"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_origin
msgid "Reference of the document that generated this sales order request."
msgstr "Oznaka dokumenta koji je generisao prodajni nalog"

#. module: sale
#: model:ir.actions.act_window,name:sale.report_configuration_action
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_form_view
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_tree_view
msgid "Report Configuration"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.Report_configuration
msgid "Report Layout Categories"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Reporting"
msgstr "Izvještavanje"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_report_product_all
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales"
msgstr "Prodaja"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Analiza prodaje"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_invoice_report_team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_team_id
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_team_id
#: model:ir.model.fields,field_description:sale.field_sale_order_team_id
#: model:ir.model.fields,field_description:sale.field_sale_report_team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_search
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Channel"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Channels"
msgstr ""

#. module: sale
#: selection:sale.report,state:0
msgid "Sales Done"
msgstr "Prodaja završena"

#. module: sale
#: model:ir.filters,name:sale.filter_sale_report_sales_funnel
msgid "Sales Funnel"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Information"
msgstr "Informacije o prodaji"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_partner_sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users_sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users_sale_warn
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model:res.request.link,name:sale.req_link_sale_order
#: selection:sale.order,state:0 selection:sale.report,state:0
msgid "Sales Order"
msgstr "Prodajni Nalog"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Prodajni nalog potvrdjen"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_account_analytic_line_so_line
#: model:ir.model.fields,field_description:sale.field_product_product_sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template_sale_line_warn
msgid "Sales Order Line"
msgstr "Stavka naloga za prodaju"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_product_sale_list
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Stavke prodajnog naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:102
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
#, python-format
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Statistika Prodajnih Naloga"

#. module: sale
#: selection:product.template,expense_policy:0
msgid "Sales price"
msgstr ""

#. module: sale
#: code:addons/sale/models/sales_team.py:94
#, python-format
msgid "Sales: Untaxed Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_order_user_id
#: model:ir.model.fields,field_description:sale.field_sale_report_user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Prodavač"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_search_view
msgid "Search Name"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Pretrazi Prodajne Naloge"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_invoice_line_layout_category_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line_layout_category_id
msgid "Section"
msgstr "Sekcija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_sale_layout
msgid "Sections on Sales Orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_access_token
msgid "Security Token"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product_sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template_sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner_sale_warn
#: model:ir.model.fields,help:sale.field_res_users_sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Izbor opcije \"Upozorenje\" ce obavestiti korisnika poruko, Izbor "
"\"Blokiranje poruke\" ce generisati gresku sa porukom i blokirati tok. "
"Poruka mora da bude napisana u sledecem polju"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is paid"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Pošalji e-mail"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Sales tab)."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_layout_category_sequence
#: model:ir.model.fields,field_description:sale.field_sale_order_line_sequence
msgid "Sequence"
msgstr "Prioritet"

#. module: sale
#: model:sale.layout_category,name:sale.sale_layout_cat_1
msgid "Services"
msgstr "Usluge"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team_use_invoices
msgid "Set Invoicing Target"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:25
#, python-format
msgid "Set an invoicing target: "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to sell variants"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set specific billing and shipping addresses"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Postavi kao ponudu"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Podešavanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Setup default terms and conditions in your sales settings ..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_delivery
msgid "Shipping Costs"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: sale
#: model:res.groups,comment:sale.group_show_price_total
msgid "Show line subtotals with taxes included (B2C)"
msgstr ""

#. module: sale
#: model:res.groups,comment:sale.group_show_price_subtotal
msgid "Show line subtotals without taxes (B2B)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr ""

#. module: sale
#: selection:product.pricelist,discount_policy:0
msgid "Show public price & discount to the customer"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show standard terms &amp; conditions on orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_show_price_subtotal
msgid "Show subtotal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_show_price_total
msgid "Show total"
msgstr ""

#. module: sale
#: selection:res.config.settings,portal_confirmation_options:0
msgid "Signature"
msgstr "Potpis"

#. module: sale
#: code:addons/sale/controllers/portal.py:211
#, python-format
msgid "Signature is missing."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sort products in sections with subtotals and page-breaks"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_module_product_email_template
msgid "Specific Email"
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:88
#: code:addons/sale/controllers/portal.py:140
#, python-format
msgid "Stage"
msgstr "Nivo"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_state
#: model:ir.model.fields,field_description:sale.field_sale_report_state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_subtotal
#: model_terms:ir.ui.view,arch_db:sale.invoice_form_inherit_sale
msgid "Subtotal"
msgstr "Međuzbir"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team_invoiced_target
msgid ""
"Target of invoice revenue for the current month. This is the amount the "
"sales channel estimates to be able to invoice this month."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_sale_show_tax
msgid "Tax Display"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_show_price_subtotal
msgid "Tax display B2B"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_show_price_total
msgid "Tax display B2C"
msgstr ""

#. module: sale
#: selection:res.config.settings,sale_show_tax:0
msgid "Tax-Excluded Prices"
msgstr ""

#. module: sale
#: selection:res.config.settings,sale_show_tax:0
msgid "Tax-Included Prices"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line_tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Porezi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_deposit_taxes_id
msgid "Taxes used for deposits"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings_module_sale_payment
msgid "Technical field implied by user choice of online_confirmation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_sale_note
msgid "Terms & Conditions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_note
msgid "Terms and conditions"
msgstr "Uslovi"

#. module: sale
#: code:addons/sale/models/analytic.py:128
#: code:addons/sale/models/analytic.py:140
#, python-format
msgid ""
"The Sales Order %s linked to the Analytic Account must be validated before "
"registering expenses."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv_amount
msgid "The amount to be invoiced in advance, taxes excluded."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_analytic_account_id
msgid "The analytic account related to a sales order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:149
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:147
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policy set"
" to \"Ordered quantities\". Please update your deposit product to be able to"
" create a deposit invoice."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:78
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"There are two ways to manage pricelists: 1) Multiple prices per product: "
"must be set in the Sales tab of the product detail form. 2) Price computed "
"from formulas: must be set in the pricelist form."
msgstr ""

#. module: sale
#: code:addons/sale/wizard/sale_make_invoice_advance.py:74
#, python-format
msgid ""
"There is no income account defined for this product: \"%s\". You may have to"
" install a chart of account from Accounting app, settings menu."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:430 code:addons/sale/models/sale.py:434
#, python-format
msgid "There is no invoicable line."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_error
msgid "There was an error processing this page."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "This Year"
msgstr "Ova godina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: selection:product.template,service_type:0
msgid "Timesheets on project (one fare per SO/Project)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_qty_to_invoice
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "To Invoice"
msgstr "Za fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_report_all_channels_sales_price_total
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_total
#: model:ir.model.fields,field_description:sale.field_sale_report_price_total
#: model_terms:ir.ui.view,arch_db:sale.invoice_form_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_configuration_search_view
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Ukupno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_all_channels_sales_view_pivot
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Total Price"
msgstr "Ukupna cijena"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Total Tax Included"
msgstr "Ukupno uključenih poreza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product_service_type
#: model:ir.model.fields,field_description:sale.field_product_template_service_type
msgid "Track Service"
msgstr "Prati servise"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_invoice_layouted
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Unit Price"
msgstr "Jed. cena"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line_product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report_product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Jedinica mere"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr "Kategorija jedinice mjere"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Jedinice mjere"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_amount_untaxed
msgid "Untaxed Amount"
msgstr "Iznos bez poreza"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_price_subtotal
msgid "Untaxed Total"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "Upselling"
msgstr ""

#. module: sale
#: selection:sale.order,invoice_status:0
#: selection:sale.order.line,invoice_status:0
msgid "Upselling Opportunity"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Vredi do"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report_volume
msgid "Volume"
msgstr "Zapremina"

#. module: sale
#: selection:product.template,sale_line_warn:0
#: selection:res.partner,sale_warn:0
msgid "Warning"
msgstr "Upozorenje"

#. module: sale
#: code:addons/sale/models/sale.py:1006
#, python-format
msgid "Warning for %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Upozorenej pri Prodaji ovog Proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings_group_warning_sale
msgid "Warnings"
msgstr "Upozorenja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv_advance_payment_method
msgid "What do you want to invoice?"
msgstr ""

#. module: sale
#. openerp-web
#: code:addons/sale/static/src/js/sale.js:32
#, python-format
msgid "Wrong value entered!"
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:188
#, python-format
msgid ""
"You can not delete a sent quotation or a sales order! Try to cancel it "
"before."
msgstr ""

#. module: sale
#: code:addons/sale/models/sale.py:1069
#, python-format
msgid ""
"You can not remove a sales order line.\n"
"Discard changes and try setting the quantity to 0."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch, or check\n"
"                every order and invoice them one by one."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "You will find here all orders that are ready to be invoiced."
msgstr ""

#. module: sale
#: code:addons/sale/controllers/portal.py:229
#, python-format
msgid "Your Order has been confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation\n"
"                to a Sales Order, then create the Invoice and collect the Payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_quotations
msgid ""
"Your next actions should flow efficiently: confirm the Quotation\n"
"            to a Sales Order, then create the Invoice and collect the Payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
msgid ""
"Your next actions should flow efficiently: confirm the Quotation to a Sales "
"Order, then create the Invoice and collect the Payment."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_order_error
#: model_terms:ir.ui.view,arch_db:sale.portal_order_success
msgid "close"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dana"

#. module: sale
#: model:ir.model,name:sale.model_report_sale_report_saleproforma
msgid "report.sale.report_saleproforma"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_layout_category
msgid "sale.layout_category"
msgstr ""
