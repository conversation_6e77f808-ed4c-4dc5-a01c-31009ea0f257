<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pos_category_furniture" model="pos.category">
        <field name="name">Office furniture</field>
    </record>

    <record id="pos_category_miscellaneous" model="pos.category">
        <field name="name">Miscellaneous</field>
    </record>

    <record id="wall_shelf" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.98</field>
        <field name="name">Wall Shelf Unit</field>
        <field name="default_code">FURN_0009</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="barcode">2100002000003</field>
        <field name="taxes_id" eval='[(5,)]'/>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="pos_categ_id" ref="pos_category_furniture"/>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/wall_shelf_unit.png"/>
    </record>

    <record id="small_shelf" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">2.83</field>
        <field name="name">Small Shelf</field>
        <field name="default_code">FURN_0008</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="taxes_id" eval='[(5,)]'/>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="pos_categ_id" ref="pos_category_furniture"/>
        <field name="to_weight">True</field>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/small_shelf.png"/>
    </record>

    <record id="monitor_stand" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">3.19</field>
        <field name="name">Monitor Stand</field>
        <field name="default_code">FURN_0006</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="pos_categ_id" ref="pos_category_miscellaneous"/>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/monitor_stand.png"/>
    </record>

    <record id="desk_organizer" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">5.10</field>
        <field name="name">Desk Organizer</field>
        <field name="default_code">FURN_0001</field>
        <field name="to_weight">True</field>
        <field name="barcode">2300001000008</field>
        <field name="type">product</field>
        <field name="weight">0.01</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="pos_categ_id" ref="pos_category_miscellaneous"/>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/desk_organizer.png"/>
    </record>

    <record id="whiteboard_pen" model="product.product">
        <field name="available_in_pos">True</field>
        <field name="list_price">1.20</field>
        <field name="name">Whiteboard Pen</field>
        <field name="weight">0.01</field>
        <field name="default_code">CONS_0001</field>
        <field name="to_weight">True</field>
        <field name="categ_id" ref="product.product_category_1"/>
        <field name="pos_categ_id" ref="pos_category_miscellaneous"/>
        <field name="uom_id" ref="uom.product_uom_unit" />
        <field name="uom_po_id" ref="uom.product_uom_unit" />
        <field name="image_1920" type="base64" file="point_of_sale/static/img/whiteboard_pen.png"/>
    </record>
</odoo>
