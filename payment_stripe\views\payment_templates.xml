<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="checkout" inherit_id="payment.checkout">
        <xpath expr="." position="inside">
            <!-- As the following link does not end with '.js', it's not loaded when
                 placed in __manifest__.py. The following declaration fix this problem -->
            <script type="text/javascript" src="https://js.stripe.com/v3/"></script>
        </xpath>
    </template>

    <template id="manage" inherit_id="payment.manage">
        <xpath expr="." position="inside">
            <!-- As the following link does not end with '.js', it's not loaded when
                 placed in __manifest__.py. The following declaration fix this problem -->
            <script type="text/javascript" src="https://js.stripe.com/v3/"></script>
        </xpath>
    </template>

</odoo>
