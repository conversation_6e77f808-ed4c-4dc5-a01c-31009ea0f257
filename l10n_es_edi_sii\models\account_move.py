# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models


class AccountMove(models.Model):
    _inherit = 'account.move'

    l10n_es_edi_is_required = fields.Boolean(
        string="Is the Spanish EDI needed",
        compute='_compute_l10n_es_edi_is_required'
    )
    l10n_es_edi_csv = fields.Char(string="CSV return code", copy=False)
    l10n_es_registration_date = fields.Date(
        string="Registration Date", copy=False,
        help="Technical field to keep the date the invoice was sent the first time as the date the invoice was "
             "registered into the system.",
    )

    # -------------------------------------------------------------------------
    # COMPUTE METHODS
    # -------------------------------------------------------------------------

    @api.depends('move_type', 'company_id')
    def _compute_l10n_es_edi_is_required(self):
        for move in self:
            move.l10n_es_edi_is_required = move.is_invoice() \
                                           and move.country_code == 'ES' \
                                           and move.company_id.l10n_es_edi_tax_agency

    @api.depends('l10n_es_edi_is_required')
    def _compute_edi_show_cancel_button(self):
        super()._compute_edi_show_cancel_button()
        for move in self.filtered('l10n_es_edi_is_required'):
            move.edi_show_cancel_button = False
