# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * note
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2015-09-19 08:22+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Kazakh (http://www.transifex.com/odoo/odoo-9/language/kk/)\n"
"Language: kk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_open
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Active"
msgstr "Белсенді"

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Archive"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "By sticky note Category"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid "Click to add a personal note."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_color
#: model:ir.model.fields,field_description:note.field_note_tag_color
msgid "Color Index"
msgstr ""

#. module: note
#: model:ir.ui.menu,name:note.menu_note_configuration
msgid "Configuration"
msgstr "Баптау"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_uid
#: model:ir.model.fields,field_description:note.field_note_stage_create_uid
#: model:ir.model.fields,field_description:note.field_note_tag_create_uid
msgid "Created by"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_create_date
#: model:ir.model.fields,field_description:note.field_note_stage_create_date
#: model:ir.model.fields,field_description:note.field_note_tag_create_date
msgid "Created on"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_date_done
msgid "Date done"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_kanban
msgid "Delete"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_display_name
#: model:ir.model.fields,field_description:note.field_note_stage_display_name
#: model:ir.model.fields,field_description:note.field_note_tag_display_name
msgid "Display Name"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_fold
msgid "Folded by Default"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Group By"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_id
#: model:ir.model.fields,field_description:note.field_note_stage_id
#: model:ir.model.fields,field_description:note.field_note_tag_id
msgid "ID"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note___last_update
#: model:ir.model.fields,field_description:note.field_note_stage___last_update
#: model:ir.model.fields,field_description:note.field_note_tag___last_update
msgid "Last Modified on"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_uid
#: model:ir.model.fields,field_description:note.field_note_stage_write_uid
#: model:ir.model.fields,field_description:note.field_note_tag_write_uid
msgid "Last Updated by"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_write_date
#: model:ir.model.fields,field_description:note.field_note_stage_write_date
#: model:ir.model.fields,field_description:note.field_note_tag_write_date
msgid "Last Updated on"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_03
#: model:note.stage,name:note.note_stage_03
msgid "Later"
msgstr ""

#. module: note
#: model:note.stage,name:note.note_stage_00
msgid "New"
msgstr ""

#. module: note
#: code:addons/note/note.py:165
#, python-format
msgid "New Note"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_note
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Note"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_memo
msgid "Note Content"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_stage
msgid "Note Stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_name
msgid "Note Summary"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_note_tag
msgid "Note Tag"
msgstr ""

#. module: note
#: model:ir.actions.act_window,name:note.action_note_note
#: model:ir.ui.menu,name:note.menu_note_notes
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
#: model:note.stage,name:note.note_stage_04
msgid "Notes"
msgstr "Жазбалар"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_user_id
#: model:ir.model.fields,field_description:note.field_note_stage_user_id
msgid "Owner"
msgstr "Иесі"

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_user_id
msgid "Owner of the note stage."
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_sequence
#: model:ir.model.fields,field_description:note.field_note_stage_sequence
msgid "Sequence"
msgstr "Тізбек"

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_id
#: model_terms:ir.ui.view,arch_db:note.view_note_note_filter
msgid "Stage"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_stage_name
msgid "Stage Name"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_form
msgid "Stage of Notes"
msgstr ""

#. module: note
#: model:ir.actions.act_window,name:note.action_note_stage
#: model:ir.ui.menu,name:note.menu_notes_stage
#: model_terms:ir.ui.view,arch_db:note.view_note_note_tree
msgid "Stages"
msgstr ""

#. module: note
#: model_terms:ir.ui.view,arch_db:note.view_note_stage_tree
msgid "Stages of Notes"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_stage_ids
msgid "Stages of Users"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_tag_name
msgid "Tag Name"
msgstr ""

#. module: note
#: sql_constraint:note.tag:0
msgid "Tag name already exists !"
msgstr ""

#. module: note
#: model:ir.model.fields,field_description:note.field_note_note_tag_ids
#: model_terms:ir.ui.view,arch_db:note.view_note_note_form
msgid "Tags"
msgstr "Жарлықтар"

#. module: note
#: model:note.stage,name:note.demo_note_stage_04
#: model:note.stage,name:note.note_stage_02
msgid "This Week"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_01
#: model:note.stage,name:note.note_stage_01
msgid "Today"
msgstr ""

#. module: note
#: model:note.stage,name:note.demo_note_stage_02
msgid "Tomorrow"
msgstr ""

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"Use notes to organize personal tasks or notes. All\n"
"            notes are private; no one else will be able to see them. "
"However\n"
"            you can share some notes with other people by inviting "
"followers\n"
"            on the note. (Useful for meeting minutes, especially if\n"
"            you activate the pad feature for collaborative writings)."
msgstr ""

#. module: note
#: model:ir.model.fields,help:note.field_note_stage_sequence
msgid "Used to order the note stages"
msgstr ""

#. module: note
#: model:ir.model,name:note.model_res_users
msgid "Users"
msgstr "Пайдаланушылар"

#. module: note
#: model_terms:ir.actions.act_window,help:note.action_note_note
msgid ""
"You can customize how you process your notes/tasks by adding,\n"
"            removing or modifying columns."
msgstr ""
