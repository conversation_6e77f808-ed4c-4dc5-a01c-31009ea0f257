# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_hr_recruitment
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-05-19 06:01+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Spanish (Dominican Republic) (http://www.transifex.com/odoo/"
"odoo-9/language/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"btn btn-primary btn-lg o_website_form_send\">Submit</span>\n"
"                                <span id=\"o_website_form_result\"/>"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_countries
msgid "All Countries"
msgstr "Todos los países"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_departments
msgid "All Departments"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_offices
msgid "All Offices"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply"
msgstr "Aplicar"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Click on \"Content\" to define a new job offer or \"Help\" for more "
"informations."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou
msgid "Continue To Our Website"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Job Detail"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_footer_custom
#: model:website.menu,name:website_hr_recruitment.menu_jobs
msgid "Jobs"
msgstr "Puestos de trabajo"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Join us and help disrupt the enterprise market!"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Join us, we offer you an extraordinary chance to learn, to\n"
"                                    develop and to be part of an exciting "
"experience and\n"
"                                    team."
msgstr ""

#. module: website_hr_recruitment
#: code:addons/website_hr_recruitment/controllers/main.py:74
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.content_new_job_offer
#, python-format
msgid "New Job Offer"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "No job offer found"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Our Job Offers"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Our Jobs"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Resume"
msgstr "Reanudar"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Short Introduction"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou
msgid "Thank you!"
msgstr "Gracias!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "There isn't job offer published now, click"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source_url
msgid "Url Parameters"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr ""

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job_website_description
msgid "Website description"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"With a small team of smart people, we released the most\n"
"                                    disruptive enterprise management "
"software in the world.\n"
"                                    Odoo is fully open source, super easy, "
"full featured\n"
"                                    (3000+ apps) and its online offer is 3 "
"times cheaper than\n"
"                                    traditional competitors like SAP and Ms "
"Dynamics."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Your Email"
msgstr "Su Usuario"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Your Name"
msgstr "Su Nombre"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Your Phone Number"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou
msgid ""
"Your job application has been successfully registered,\n"
"                        we will get back to you soon."
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "here"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "not published"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr ""

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "to contact us"
msgstr ""

#~ msgid "Submit"
#~ msgstr "Enviar"

#~ msgid "The full URL to access the document through the website."
#~ msgstr ""
#~ "La dirección URL completa para acceder al documento a través de la Web."

#~ msgid "Visible in Website"
#~ msgstr "Visible en sitio web"

#~ msgid "Website URL"
#~ msgstr "URL de la página web"

#~ msgid "Website meta description"
#~ msgstr "Meta-descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta-Palabras clave de sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta-Título de página web "
