# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <zab<PERSON><EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <rast<PERSON>.<EMAIL>>, 2021
# karol<PERSON><PERSON> schustero<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"Možná bude potřeba provést ruční zásah."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Preparation</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Příprava</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/> <b>Zrušeno</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> <b>Shipped</b>"
msgstr "<i class=\"fa fa-fw fa-truck\"/> <b>Odesláno</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Prodeje</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Prodáno</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Reference zákazníka:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Doručení objednávek</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Incoterm: </strong>"
msgstr "<strong>Incoterm: </strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Incoterm:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Skladovatelný produkt je výrobek, pro který spravujete sklad. Musí být nainstalována aplikace Inventory. \n"
"Spotřební materiál je výrobek, pro který není spravován sklad.\n"
"Služba je nehmotný produkt, který poskytujete."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Podle konfigurace produktu může být dodané množství automaticky vypočítáno pomocí mechanismu:\n"
"- Ruční: množství je nastaveno ručně na lince\n"
"- Analytické z nákladů: množství je množství z účtovaných nákladů\n"
"- Časový rozvrh: množství je součtem hodin zaznamenaných na úkolech spojených s touto prodejní linkou\n"
"- Skladové pohyby: množství pochází z potvrzených vychystávání\n"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr ""

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Zahrnuty jsou všechny plánované operace"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Použijte konkrétní trasy"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Co nejdříve"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/js/qty_at_date_widget.js:0
#, python-format
msgid "Availability"
msgstr "Dostupnost"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available"
msgstr "Dostupný"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available in stock"
msgstr "K dispozici na skladě"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr "Vyberte, zda chcete použít konkrétní trasy linek SO."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Datum dokončení první objednávky dodávky."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Datum:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Výchozí sklad"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Dodání"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Příkaz dodání"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Datum dodání, které můžete zákazníkovi slíbit, vypočteno z minimální dodací "
"lhůty na objednávkových linkách v případě produktů služeb. V případě "
"přepravy bude brána v úvahu přepravní politika objednávky, aby bylo možné "
"použít buď minimální nebo maximální dodací lhůtu na objednávkových řádcích."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Zobrazit widget množství"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr "Zobrazit Incoterms na prodejní objednávce a souvisejících fakturách"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr ""
"Nezapomeňte změnit partnera u následujících objednávek na doručení: %s"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Dokumentace"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Datum účinnosti"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Při vyzvednutí došlo k výjimce (vyjímkám):"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Na prodejní objednávce (objednávkách) došlo k výjimkám (vyjímce):"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Výjimka(y):"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Očekávaný datum"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Očekávané doručení"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Očekáváno:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Předpokládané datum prognózy"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Předpokládané zásoby"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Volné množství dnes"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Má pozdní vyzvednutí"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Pokud dodáte všechny produkty najednou, bude objednávka na dodávku "
"naplánována na základě největší dodací lhůty produktu. V opačném případě "
"bude vycházet z nejkratší doby."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Zasažené transfer(y)"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Incoterm"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__group_display_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr "Incoterms"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Mezinárodní obchodní podmínky jsou série předem definovaných obchodních "
"termínů používaných v mezinárodních transakcích."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Inventář"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Trasy inventáře"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Fakturace"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "Data JSON pro widget popover"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Položka deníku"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Položka deníku"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Šarže / seriové číslo"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Make To Order"
msgstr "Na objednávku - MTO"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Mohlo by být nutné provést ruční akce."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Časová rezerva pro termíny slíbené zákazníkům. Výrobky budou naplánovány k "
"dodání do termínu, který je mnohem dřívější, než skutečně slíbené datum, aby"
" se vypořádaly s neočekávaným zpožděním v dodavatelském řetězci."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""
"Rozpětí chyb pro data přislíbená zákazníkům. Produkty budou naplánovány na "
"pořízení a dodání o mnoho dní dříve, než je skutečné slíbené datum, aby bylo"
" možné čelit neočekávaným prodlevám v dodavatelském řetězci."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Metoda aktualizace dodaného množství"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Přesunutí očekávaných termínů dodání na později o"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No enough future availaibility"
msgstr "Nedostatek budoucí dostupnosti"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No future availaibility"
msgstr "Není budoucí dostupnost"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "On"
msgstr "Na"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Objednané množství se snížilo!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Zásady pro vyzvednutí"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Skupina zásobování"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product Template"
msgstr "Šablona produktu"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Typ výrobku"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "Množství dostupné dnes"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Množství k dodání"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_report_product_product_replenishment
msgid "Quotations"
msgstr "Nabídky"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "Zbývající poptávka je k dispozici na adrese"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Reserved"
msgstr "Rezervováno"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Pracovní postup"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Prodejní objednávka"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Prodejní objednávky"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_count
msgid "Sale order count"
msgstr "Počet objednávek"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Zpráva analýzy prodeje"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Storno prodejní objednávky"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Řádek zakázky"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Položky zakázky"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Zakázky"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Naplánujte doručení dříve, abyste zabránili zpoždění"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Plánované datum"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Bezpečnostní dodací lhůta "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Doručit všechny produkty najednou"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Odeslat produkty, jakmile jsou k dispozici, se zpětnými objednávkami"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Přepravní politika"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sold in the last 365 days"
msgstr "Prodáno za posledních 365 dní"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"Some products have already been delivered. Returns can be created from the "
"Delivery Orders."
msgstr ""
"Některé produkty již byly dodány. Vrácení lze vytvořit z objednávek na "
"dodání."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Pohyb zásob"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Pohyby zásob"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Zpráva o doplnění zásob"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Pravidlo zásob"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Výkaz zásobovacích pravidel"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Přehled skladových pravidel"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "The delivery"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""
"Na objednávce odběratele byla změněna adresa dodání <br/>\n"
"Z <strong>\"%s\"</strong> Na <strong>\"%s\"</strong>,\n"
"Pravděpodobně byste měli aktualizovat partnera v tomto dokumentu."

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Tento produkt je doplňován na vyžádání."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Převod"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Přesuny"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "Users"
msgstr "Uživatelé"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "View Forecast"
msgstr "Zobrazit předpověď"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Virtuální dostupnost k datu"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Sklad"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Varování!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Když jsou všechny produkty připraveny"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Kdy začít expedovat"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"Snižujete objednané množství! Nezapomeňte v případě potřeby manuálně "
"aktualizovat objednávku."

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity of a sale order line below its delivered quantity.\n"
"Create a return in your inventory first."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "zrušeno"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "dnů"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "z "

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "objednané namísto"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "zpracováno namísto "

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "will be late."
msgstr "přijde pozdě."
