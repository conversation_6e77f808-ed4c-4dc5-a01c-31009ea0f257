<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- EU lande Business -->
    <record id="position_tax_salgvare_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax110"/>
        <field name="tax_dest_id" ref="tax210"/>
    </record>
    <record id="position_tax_salgydelser_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax120"/>
        <field name="tax_dest_id" ref="tax220"/>
    </record>
    <record id="position_tax_koebvare_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax400"/>
        <field name="tax_dest_id" ref="tax510"/>
    </record>
    <record id="position_tax_koebvare_indeholt_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax410"/>
        <field name="tax_dest_id" ref="tax510"/>
    </record>
    <record id="position_tax_koebydelser_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax420"/>
        <field name="tax_dest_id" ref="tax520"/>
    </record>
    <record id="position_tax_koebydelser_indeholdt_eu" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_eu_taxid"/>
        <field name="tax_src_id" ref="tax425"/>
        <field name="tax_dest_id" ref="tax520"/>
    </record>

    <!-- 3. lande -->
    <record id="position_tax_salgvare_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax110"/>
        <field name="tax_dest_id" ref="tax310"/>
    </record>
    <record id="position_tax_salgydelser_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax120"/>
        <field name="tax_dest_id" ref="tax310"/>
    </record>
    <record id="position_tax_koebvare_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax400"/>
        <field name="tax_dest_id" ref="tax610"/>
    </record>
    <record id="position_tax_koebvare_indeholdt_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax410"/>
        <field name="tax_dest_id" ref="tax610"/>
    </record>
    <record id="position_tax_koebydelser_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax420"/>
        <field name="tax_dest_id" ref="tax620"/>
    </record>
    <record id="position_tax_koebydelser_indeholdt_3" model="account.fiscal.position.tax.template">
        <field name="position_id" ref="fiscal_position_template_3lande"/>
        <field name="tax_src_id" ref="tax425"/>
        <field name="tax_dest_id" ref="tax620"/>
    </record>
</odoo>
