<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.base.setup</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="0"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('settings')]" position="inside">
                    <div class="app_settings_block" data-string="General Settings" string="General Settings" data-key="general_settings">

                        <div id="invite_users">
                            <h2>Users</h2>
                            <div class="row mt16 o_settings_container" name="users_setting_container">
                                <div class="col-12 col-lg-6 o_setting_box" id="invite_users_setting">
                                    <div class="o_setting_right_pane">
                                        <widget name='res_config_invite_users'/>
                                    </div>
                                </div>
                                <div class="col-12 col-lg-6 o_setting_box" id="active_user_setting">
                                    <div class="o_setting_right_pane">
                                        <span class="fa fa-lg fa-users" aria-label="Number of active users"/>
                                        <field name='active_user_count' class="w-auto pl-3 font-weight-bold"/>
                                        <span class='o_form_label' attrs="{'invisible':[('active_user_count', '&gt;', '1')]}">
                                            Active User
                                        </span>
                                        <span class='o_form_label' attrs="{'invisible':[('active_user_count', '&lt;=', '1')]}">
                                            Active Users
                                        </span>
                                        <a href="https://www.odoo.com/documentation/15.0/applications/general/users.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                        <br/>
                                        <button name="%(base.action_res_users)d" icon="fa-arrow-right" type="action" string="Manage Users" class="btn-link o_web_settings_access_rights"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="languages">
                            <h2>Languages</h2>
                            <div class='row mt16 o_settings_container' name="languages_setting_container">
                                <div class='col-xs-12 col-md-6 o_setting_box' id="languages_setting">
                                    <div class='o_setting_right_pane'>
                                        <!-- TODO This is not an ideal solution but it looks ok on the interface -->
                                        <div class="w-50">
                                            <field name="language_count" class="w-auto pl-1 font-weight-bold"/>
                                            <span class='o_form_label' attrs="{'invisible':[('language_count', '&gt;', '1')]}">
                                                Language
                                            </span>
                                            <span class='o_form_label' attrs="{'invisible':[('language_count', '&lt;=', '1')]}">
                                                Languages
                                            </span>
                                        </div>
                                        <div class="mt8">
                                            <button name="%(base.action_view_base_language_install)d" icon="fa-arrow-right" type="action" string="Add Language" class="btn-link"/>
                                        </div>
                                        <div class="mt8" groups="base.group_no_one">
                                            <button name="%(base.res_lang_act_window)d" icon="fa-arrow-right" type="action" string="Manage Languages" class="btn-link"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="companies">
                            <h2>Companies</h2>
                            <div class="row mt16 o_settings_container" name="companies_setting_container">
                                <div class="col-12 col-lg-6 o_setting_box" id="company_details_settings">
                                    <field name="company_id" invisible="1"/>
                                    <div class="o_setting_right_pane">
                                        <field name="company_name" class="font-weight-bold"/>
                                        <br/>
                                        <field name="company_informations" class="text-muted" style="width: 90%;"/>
                                        <br/>
                                        <button name="open_company" icon="fa-arrow-right" type="object" string="Update Info" class="btn-link"/>
                                    </div>
                                    <br/>
                                    <div class="o_setting_right_pane">
                                        <span class="o_form_label">Document Layout</span>
                                        <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                        <div class="text-muted">
                                            Choose the layout of your documents
                                        </div>
                                        <div class="content-group">
                                            <div class="mt16" groups="base.group_no_one">
                                                <label for="external_report_layout_id" string="Layout" class="col-3 col-lg-3 o_light_label"/>
                                                <field name="external_report_layout_id" domain="[('type','=', 'qweb')]" class="oe_inline"/>
                                            </div>
                                            <div class="mt8">
                                                <button name="%(web.action_base_document_layout_configurator)d" string="Configure Document Layout" type="action" class="oe_link" icon="fa-arrow-right"/>
                                                <button name="edit_external_header" string="Edit Layout" type="object" class="oe_link" groups="base.group_no_one"/>
                                                <button name="%(web.action_report_externalpreview)d" string="Preview Document" type="action" class="oe_link" groups="base.group_no_one"/>
                                            </div>
                                        </div>
                                    </div> 
                                </div>
                                <div class="col-12 col-lg-6 o_setting_box" id="companies_setting">
                                    <div class="o_setting_right_pane">
                                        <field name='company_count' class="w-auto pl-1 font-weight-bold"/>
                                        <span class='o_form_label' attrs="{'invisible':[('company_count', '&gt;', '1')]}">
                                            Company
                                        </span>
                                        <span class='o_form_label' attrs="{'invisible':[('company_count', '&lt;=', '1')]}">
                                            Companies
                                        </span>
                                        <br/>
                                        <div class="mt8">
                                            <button name="%(base.action_res_company_form)d" icon="fa-arrow-right" type="action" string="Manage Companies" class="btn-link"/>
                                        </div>
                                    </div>
                                    <br/>
                                    <div id="inter_company" groups="base.group_multi_company" title="Configure company rules to automatically create SO/PO when one of your company sells/buys to another of your company.">
                                        <field name="company_id" invisible="1"/>
                                        <div class="o_setting_left_pane">
                                            <field name="module_account_inter_company_rules" widget="upgrade_boolean"/>
                                        </div>
                                        <div class="o_setting_right_pane">
                                            <label string="Inter-Company Transactions" for="module_account_inter_company_rules"/>
                                            <span class="fa fa-lg fa-building-o" title="Values set here are company-specific." aria-label="Values set here are company-specific." groups="base.group_multi_company" role="img"/>
                                            <div class="text-muted">
                                                Automatically generate counterpart documents for orders/invoices between companies
                                            </div>
                                            <div class="content-group" attrs="{'invisible': [('module_account_inter_company_rules','=',False)]}" id="inter_companies_rules">
                                                <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="emails"/>

                        <div id="contacts_settings">
                            <h2>Contacts</h2>
                            <div class="row mt16 o_settings_container" name="contacts_setting_container">
                                <div class="col-xs-12 col-md-6 o_setting_box" id="sms">
                                        <div class="o_setting_right_pane" id="sms_settings">
                                            <div class="o_form_label">
                                            Send SMS
                                            <a href="https://www.odoo.com/documentation/15.0/applications/marketing/sms_marketing/pricing/pricing_and_faq.html" title="Documentation" class="ml-1 o_doc_link" target="_blank"></a>
                                            </div>
                                            <div class="text-muted">
                                                Send texts to your contacts
                                            </div>
                                        </div>
                                </div>
                                <div class="col-xs-12 col-md-6 o_setting_box" title="When populating your address book, Odoo provides a list of matching companies. When selecting one item, the company data and logo are auto-filled." id="partner_autocomplete">
                                    <div class="o_setting_left_pane">
                                        <field name="module_partner_autocomplete"/>
                                    </div>
                                    <div class="o_setting_right_pane" id="partner_autocomplete_settings">
                                        <label for="module_partner_autocomplete"/>
                                        <div class="text-muted">
                                            Automatically enrich your contact base with company data
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>

                    <h2>Permissions</h2>
                    <div class="row mt16 o_settings_container" id="user_default_rights">
                        <div class="col-12 col-lg-6 o_setting_box"  title="By default, new users get highest access rights for all installed apps." id="access_rights">
                            <div class="o_setting_left_pane">
                                <field name="user_default_rights"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label string="Default Access Rights" for="user_default_rights"/>
                                <div class="text-muted">
                                    Set custom access rights for new users
                                </div>
                                <div class="content-group" attrs="{'invisible': [('user_default_rights','=',False)]}">
                                    <div class="mt8">
                                        <button type="object" name="open_default_user" string="Default Access Rights" icon="fa-arrow-right" class="btn-link"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box"
                             groups="base.group_system">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <button type="action" name="%(base.action_apikeys_admin)d" string="Manage API Keys" icon="fa-arrow-right" class="btn-link"/>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" groups="base.group_no_one" id="allow_import">
                            <div class="o_setting_left_pane">
                                <field name="module_base_import" />
                            </div>
                            <div class="o_setting_right_pane">
                                <label string="Import &amp; Export" for="module_base_import"/>
                                <a href="https://www.odoo.com/documentation/15.0/applications/general/export_import_data.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                <div class="text-muted">
                                    Allow users to import data from CSV/XLS/XLSX/ODS files
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="feedback_motivate_setting" groups="base.group_no_one">
                            <div class="o_setting_left_pane">
                                <field name="show_effect"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="show_effect"/>
                                <div class="text-muted">
                                    Add fun feedback and motivate your employees
                                </div>
                            </div>
                        </div>
                        </div>

                        <h2>Integrations</h2>
                        <div class="row mt16 o_settings_container" name="integration">
                            <div class="col-12 col-lg-6 o_setting_box" id="mail_pluggin_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_mail_plugin" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Mail Plugin" for="module_mail_plugin"/>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/productivity/mail_plugins.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <div class="text-muted">
                                        Integrate with mail client plugins
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="sync_outlook_calendar_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_microsoft_calendar" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Outlook Calendar" for="module_microsoft_calendar"/>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/general/calendars/outlook/outlook_calendar.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <div class="text-muted">
                                        Synchronize your calendar with Outlook
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_microsoft_calendar', '=', False)]}" id="msg_module_microsoft_calendar">
                                        <div class="text-warning mt16"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="external_pads_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_pad"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_pad"/>
                                    <div class="text-muted">
                                        Use external pads in Odoo Notes
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_pad','=',False)]}" id="msg_module_pad">
                                        <div class="text-warning mt16"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="sync_google_calendar_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_google_calendar" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Google Calendar" for="module_google_calendar"/>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/general/calendars/google/google_calendar_credentials.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <div class="text-muted">
                                        Synchronize your calendar with Google Calendar
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_google_calendar','=',False)]}" id="msg_module_google_calendar">
                                        <div class="text-warning mt16"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="google_drive_documents_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_google_drive" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="Google Drive" for="module_google_drive"/>
                                    <div class="text-muted">
                                        Create and attach Google Drive documents to any record
                                    </div>
                                    <div class="content-group mt16" attrs="{'invisible': [('module_google_drive','=',False)]}" id="msg_module_google_drive">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div id="product_get_pic_setting"/>
                            <div class="col-12 col-lg-6 o_setting_box" id="google_spreadsheet_setting">
                                <div class="o_setting_left_pane">
                                    <field name="module_google_spreadsheet" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_google_spreadsheet"/>
                                    <div class="text-muted">
                                        Extract and analyze Odoo data from Google Spreadsheet
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_google_spreadsheet','=',False)]}" id="msg_module_google_spreadsheet">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="module_auth_oauth">
                                <div class="o_setting_left_pane">
                                    <field name="module_auth_oauth" />
                                </div>
                                <div class="o_setting_right_pane">
                                    <label string="OAuth Authentication" for="module_auth_oauth"/>
                                    <div class="text-muted">
                                       Use external accounts to log in (Google, Facebook, etc.)
                                    </div>
                                    <div class="content-group mt16" attrs="{'invisible': [('module_auth_oauth','=',False)]}" id="msg_module_auth_oauth">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="oauth">
                                <div class="o_setting_left_pane">
                                    <field name="module_auth_ldap"/>
                                </div>
                                <div class="o_setting_right_pane" name="auth_ldap_right_pane">
                                    <label string="LDAP Authentication" for="module_auth_ldap"/>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/general/auth/ldap.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <div class="text-muted">
                                       Use LDAP credentials to log in
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_auth_ldap','=',False)]}" id="auth_ldap_warning">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="unsplash">
                                <div class="o_setting_left_pane">
                                    <field name="module_web_unsplash"/>
                                </div>
                                <div class="o_setting_right_pane" id="web_unsplash_settings">
                                    <label for="module_web_unsplash"/>
                                    <a href="https://www.odoo.com/documentation/15.0/applications/websites/website/optimize/unsplash.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <div class="text-muted">
                                        Find free high-resolution images from Unsplash
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_web_unsplash', '=', False)]}" id="web_unsplash_warning">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="base_geolocalize">
                                <div class="o_setting_left_pane">
                                    <field name="module_base_geolocalize"/>
                                </div>
                                <div class="o_setting_right_pane" id="web_geolocalize_settings">
                                    <label string="Geo Localization" for="module_base_geolocalize"/>
                                    <div class="text-muted">
                                       GeoLocalize your partners
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_base_geolocalize','=', False)]}" name="base_geolocalize_warning">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to choose your Geo Provider.</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" id="recaptcha">
                                <div class="o_setting_left_pane">
                                    <field name="module_google_recaptcha"/>
                                </div>
                                <div class="o_setting_right_pane" id="website_recaptcha_settings">
                                    <label for="module_google_recaptcha"/>
                                    <div class="text-muted">
                                        Protect your forms from spam and abuse.
                                    </div>
                                    <div class="content-group" attrs="{'invisible': [('module_google_recaptcha', '=', False)]}" id="recaptcha_warning">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up reCaptcha.</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h2 groups="base.group_no_one">Performance</h2>
                        <div groups="base.group_no_one" class="row mt16 o_settings_container" name="performance">
                            <div class="col-12 col-lg-6 o_setting_box" id="profiling_enabled_until">
                            <label for="profiling_enabled_until"/>
                            <field name="profiling_enabled_until"/>
                            <div class="text-muted">
                                Enable the profiling tool. Profiling may impact performance while being active.
                            </div>
                            </div>
                        </div>

                        <widget name='res_config_dev_tool'/>
                        <div id='about'>
                            <h2>About</h2>
                            <div class="row mt16 o_settings_container" name="about_setting_container">
                                <div class='col-12 col-lg-6 o_setting_box' id='appstore'>
                                    <div class="d-flex">
                                        <div class="o_setting_right_pane">
                                            <!-- FIXME Those links are defined directly in the template which means that we will have to
                                            update the template code is the link ever changes -->
                                            <a class="d-block mx-auto" href="https://play.google.com/store/apps/details?id=com.odoo.mobile" target="blank">
                                                <img alt="On Google Play" class="d-block mx-auto img img-fluid" src="/base_setup/static/src/img/google_play.png"/>
                                            </a>
                                        </div>
                                        <div>
                                            <a class='d-block mx-auto' href="https://itunes.apple.com/us/app/odoo/id1272543640" target="blank">
                                                <img alt="On Apple Store" class="d-block mx-auto img img-fluid" src="/base_setup/static/src/img/app_store.png"/>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <widget name='res_config_edition'/>
                            </div>
                        </div>
                    </div>
                </xpath>
            </field>
        </record>

        <record id="action_general_configuration" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'general_settings', 'bin_size': False}</field>
        </record>

        <menuitem
            id="menu_config"
            name="General Settings"
            parent="base.menu_administration"
            sequence="0"
            action="action_general_configuration"
            groups="base.group_system"/>

</odoo>
