<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report_vat" model="account.tax.report">
        <field name="name">VAT Report</field>
        <field name="country_id" ref="base.be"/>
    </record>

    <record id="tax_report_title_operations" model="account.tax.report.line">
        <field name="name">Opérations</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_title_operations_sortie" model="account.tax.report.line">
        <field name="name">II A la sortie</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_00" model="account.tax.report.line">
        <field name="name">00 - Opérations soumises à un régime particulier</field>
        <field name="code">c00</field>
        <field name="tag_name">00</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_01" model="account.tax.report.line">
        <field name="name">01 - Opérations avec TVA à 6%</field>
        <field name="code">c01</field>
        <field name="tag_name">01</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_02" model="account.tax.report.line">
        <field name="name">02 - Opérations avec TVA à 12%</field>
        <field name="code">c02</field>
        <field name="tag_name">02</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_03" model="account.tax.report.line">
        <field name="name">03 - Opérations avec TVA à 21%</field>
        <field name="code">c03</field>
        <field name="tag_name">03</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_line_44" model="account.tax.report.line">
        <field name="name">44 - Services intra-communautaires</field>
        <field name="code">c44</field>
        <field name="tag_name">44</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">5</field>
    </record>

    <record id="tax_report_line_45" model="account.tax.report.line">
        <field name="name">45 - Opérations avec TVA due par le cocontractant</field>
        <field name="code">c45</field>
        <field name="tag_name">45</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">6</field>
    </record>

    <record id="tax_report_title_operations_sortie_46" model="account.tax.report.line">
        <field name="name">46 - Livraisons intra-communautaires exemptées</field>
        <field name="code">c46</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">7</field>
        <field name="formula"></field>
    </record>

    <record id="tax_report_line_46L" model="account.tax.report.line">
        <field name="name">46L - Livraisons biens intra-communautaires exemptées</field>
        <field name="code">c46L</field>
        <field name="tag_name">46L</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie_46"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_46T" model="account.tax.report.line">
        <field name="name">46T - Livraisons biens intra-communautaire exemptées</field>
        <field name="code">c46T</field>
        <field name="tag_name">46T</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie_46"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_47" model="account.tax.report.line">
        <field name="name">47 - Autres opérations exemptées</field>
        <field name="code">c47</field>
        <field name="tag_name">47</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">8</field>
    </record>

    <record id="tax_report_title_operations_sortie_48" model="account.tax.report.line">
        <field name="name">48 - Notes de crédit aux opérations grilles [44] et [46]</field>
        <field name="code">c48</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">9</field>
        <field name="formula"></field>
    </record>

    <record id="tax_report_line_48s44" model="account.tax.report.line">
        <field name="name">48s44 - Notes de crédit aux opérations grilles [44]</field>
        <field name="code">c48s44</field>
        <field name="tag_name">48s44</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie_48"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_48s46L" model="account.tax.report.line">
        <field name="name">48s46L - Notes de crédit aux opérations grilles [46L]</field>
        <field name="code">c48s46L</field>
        <field name="tag_name">48s46L</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie_48"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_48s46T" model="account.tax.report.line">
        <field name="name">48s46T - Notes de crédit aux opérations grilles [46T]</field>
        <field name="code">c48s46T</field>
        <field name="tag_name">48s46T</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie_48"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_49" model="account.tax.report.line">
        <field name="name">49 - Notes de crédit aux opérations du point II</field>
        <field name="code">c49</field>
        <field name="tag_name">49</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_sortie"/>
        <field name="sequence">10</field>
    </record>

    <record id="tax_report_title_operations_entree" model="account.tax.report.line">
        <field name="name">III A l'entrée</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_81" model="account.tax.report.line">
        <field name="name">81 - Marchandises, matières premières et auxiliaires</field>
        <field name="code">c81</field>
        <field name="tag_name">81</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">1</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_line_82" model="account.tax.report.line">
        <field name="name">82 - Services et biens divers</field>
        <field name="code">c82</field>
        <field name="tag_name">82</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">2</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_line_83" model="account.tax.report.line">
        <field name="name">83 - Biens d'investissement</field>
        <field name="code">c83</field>
        <field name="tag_name">83</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">3</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_line_84" model="account.tax.report.line">
        <field name="name">84 - Notes de crédits sur opérations case [86] et [88]</field>
        <field name="code">c84</field>
        <field name="tag_name">84</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_line_85" model="account.tax.report.line">
        <field name="name">85 - Notes de crédits autres opérations</field>
        <field name="code">c85</field>
        <field name="tag_name">85</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">5</field>
    </record>

    <record id="tax_report_line_86" model="account.tax.report.line">
        <field name="name">86 - Acquisition intra-communautaires et ventes ABC</field>
        <field name="code">c86</field>
        <field name="tag_name">86</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">6</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_line_87" model="account.tax.report.line">
        <field name="name">87 - Autres opérations</field>
        <field name="code">c87</field>
        <field name="tag_name">87</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">7</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_line_88" model="account.tax.report.line">
        <field name="name">88 - Acquisition services intra-communautaires</field>
        <field name="code">c88</field>
        <field name="tag_name">88</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_operations_entree"/>
        <field name="sequence">8</field>
        <field name="carry_over_condition_method">no_negative_amount_carry_over_condition</field>
    </record>

    <record id="tax_report_title_taxes" model="account.tax.report.line">
        <field name="name">Taxes</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_title_taxes_dues" model="account.tax.report.line">
        <field name="name">IV Dues</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes"/>
        <field name="sequence">1</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_54" model="account.tax.report.line">
        <field name="name">54 - TVA sur opérations des grilles [01], [02], [03]</field>
        <field name="code">c54</field>
        <field name="tag_name">54</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_55" model="account.tax.report.line">
        <field name="name">55 - TVA sur opérations des grilles [86] et [88]</field>
        <field name="code">c55</field>
        <field name="tag_name">55</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_56" model="account.tax.report.line">
        <field name="name">56 - TVA sur opérations de la grille [87]</field>
        <field name="code">c56</field>
        <field name="tag_name">56</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_line_57" model="account.tax.report.line">
        <field name="name">57 - TVA relatives aux importations</field>
        <field name="code">c57</field>
        <field name="tag_name">57</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">4</field>
    </record>

    <record id="tax_report_line_61" model="account.tax.report.line">
        <field name="name">61 - Diverses régularisations en faveur de l'Etat</field>
        <field name="code">c61</field>
        <field name="tag_name">61</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">5</field>
    </record>

    <record id="tax_report_line_63" model="account.tax.report.line">
        <field name="name">63 - TVA à reverser sur notes de crédit recues</field>
        <field name="code">c63</field>
        <field name="tag_name">63</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_dues"/>
        <field name="sequence">6</field>
    </record>

    <record id="tax_report_title_taxes_deductibles" model="account.tax.report.line">
        <field name="name">V Déductibles</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes"/>
        <field name="sequence">2</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_59" model="account.tax.report.line">
        <field name="name">59 - TVA déductible</field>
        <field name="code">c59</field>
        <field name="tag_name">59</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_deductibles"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_62" model="account.tax.report.line">
        <field name="name">62 - Diverses régularisations en faveur du déclarant</field>
        <field name="code">c62</field>
        <field name="tag_name">62</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_deductibles"/>
        <field name="sequence">2</field>
    </record>

    <record id="tax_report_line_64" model="account.tax.report.line">
        <field name="name">64 - TVA à récupérer sur notes de crédit delivrées</field>
        <field name="code">c64</field>
        <field name="tag_name">64</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_deductibles"/>
        <field name="sequence">3</field>
    </record>

    <record id="tax_report_title_taxes_soldes" model="account.tax.report.line">
        <field name="name">VI Soldes</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes"/>
        <field name="sequence">3</field>
        <field name="formula">None</field>
    </record>

    <record id="tax_report_line_71" model="account.tax.report.line">
        <field name="name">71 - Taxes dues à l'état</field>
        <field name="formula">(c54 + c55 + c56 + c57 + c61 + c63)&gt;(c59 + c62 + c64) and (c54 + c55 + c56 + c57 + c61 + c63)-(c59 + c62 + c64) or 0</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_soldes"/>
        <field name="sequence">1</field>
    </record>

    <record id="tax_report_line_72" model="account.tax.report.line">
        <field name="name">72 - Somme due par l'état</field>
        <field name="formula">(c54 + c55 + c56 + c57 + c61 + c63)&lt;(c59 + c62 + c64) and (c59 + c62 + c64)-(c54 + c55 + c56 + c57 + c61 + c63) or 0</field>
        <field name="report_id" ref="tax_report_vat"/>
        <field name="parent_id" ref="tax_report_title_taxes_soldes"/>
        <field name="sequence">2</field>
    </record>

</odoo>
