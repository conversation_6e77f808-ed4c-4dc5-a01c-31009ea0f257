<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_il" model="res.partner">
        <field name="name">IL Company</field>
        <field name="vat"></field>
        <field name="street">500 </field>
        <field name="city">תל אביב-יפו</field>
        <field name="country_id" ref="base.il"/>
        
        <field name="zip">no</field>
        <field name="phone">+972 50-234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ilexample.com</field>
    </record>

    <record id="demo_company_il" model="res.company">
        <field name="name">IL Company</field>
        <field name="partner_id" ref="partner_demo_company_il"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_il')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_il.demo_company_il'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_il.il_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_il.demo_company_il')"/>
    </function>
</odoo>
