<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fi_chart_template" model="account.chart.template">
        <field name="code_digits">4</field>
        <field name="property_account_receivable_id" ref="account_1700"/>
        <field name="property_account_payable_id" ref="account_2870"/>
        <field name="property_account_expense_categ_id" ref="account_4000"/>
        <field name="property_account_income_categ_id" ref="account_3000"/>
        <field name="property_account_expense_id" ref="account_4000"/>
        <field name="property_account_income_id" ref="account_3000"/>
        <field name="expense_currency_exchange_account_id" ref="account_4380"/>
        <field name="income_currency_exchange_account_id" ref="account_3500"/>
        <field name="property_tax_payable_account_id" ref="account_2930"/>
        <field name="property_tax_receivable_account_id" ref="account_1765"/>
        <field name="default_pos_receivable_account_id" ref="account_1701"/>
    </record>
</odoo>
