.sale-order-info td {
    padding-right: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.order-management-screen .flex-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.order-management-screen .control-panel {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem;
}

.order-management-screen .control-panel .item {
    font-size: medium;
}

.order-management-screen .control-panel .search-box {
    flex: 1;
    position: relative;
    text-align: center;
    margin: 0.2rem;
}

.order-management-screen .control-panel .search-box .clear {
    position: relative;
    right: 25px;
    cursor: pointer;
    color: #808080;
    font-size: 13px;
}

.order-management-screen .control-panel .search-box .icon {
    position: relative;
    left: 25px;
    color: #808080;
    font-size: 13px;
}

.order-management-screen .control-panel .search-box input {
    border: 1px solid #cecbcb;
    padding: 10px 30px;
    margin: auto;
    background-color: white;
    border-radius: 20px;
    font-family: "La<PERSON>","Lucida Grande", Helvetica, Verdana, Arial;
    font-size: 13px;
}

.order-management-screen .control-panel .search-box input:focus {
    outline: none;
    box-shadow: 0px 0px 0px 2px rgb(153, 153, 255) inset;
    color: rgb(153, 153, 255);
}

.order-management-screen .control-panel .button {
    line-height: 32px;
    padding: 3px 13px;
    font-size: 20px;
    background: rgb(230, 230, 230);
    border-radius: 3px;
    border: solid 1px rgb(209, 209, 209);
    cursor: pointer;
    transition: all 150ms linear;
}

.order-management-screen .control-panel .button:hover {
    background: #efefef;
}

.order-management-screen .back-to-list {
    font-size: large;
    padding: 10px;
    background-color: #6EC89B;
    color: white;
}
