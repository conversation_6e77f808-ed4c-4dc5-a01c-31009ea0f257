# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_forum
#
# Translators:
# Le<PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-02-18 13:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Colombia) (http://www.transifex.com/odoo/odoo-9/"
"language/es_CO/)\n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_forum
#: model:mail.template,body_html:website_forum.validation_email
msgid ""
"\n"
"<div summary=\"o_mail_notification\" style=\"padding:0px; width:600px; "
"margin:0 auto; background: #FFFFFF repeat top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px; border-"
"collapse:collapse; background:inherit; color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"center\" width=\"200\" style=\"padding:0px 10px 5px "
"5px; font-size: 12px\">\n"
"                <img src=\"/logo.png\" style=\"padding: 0px; margin: 0px; "
"height: auto; width: 80px;\" alt=\"${user.company_id.name}\">\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div summary=\"o_mail_notification\" style=\"padding:0px; width:600px; "
"margin:0 auto; background: #FFFFFF repeat top /100%;color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"vertical-align:top; "
"padding:0px; border-collapse:collapse; background:inherit; color:inherit\">\n"
"        <tbody><tr>\n"
"            <td valign=\"top\" style=\"width:600px; padding:0px 10px 5px 5px;"
"\">\n"
"                <div>\n"
"                    <hr width=\"100%\" style=\"background-color:"
"rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;"
"min-height:1px;line-height:0;margin:0px auto;padding:0\">\n"
"                </div>\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>\n"
"<div style=\"padding:0px; width:600px; max-width:600px; margin:0 auto; "
"background: #FFFFFF repeat top /100%; color:#777777\">\n"
"    <table cellspacing=\"0\" cellpadding=\"0\" style=\"width:600px ;text-"
"align:justify; margin:0 auto; border-collapse:collapse; background:inherit; "
"color:inherit\">\n"
"        <tbody><tr>\n"
"            <td style=\"padding:0px 10px 5px 5px;font-size: 14px;\">\n"
"                <p>Dear ${object.name},</p>\n"
"                <p>You have been invited to validate your email in order to "
"get access to\n"
"                \"${object.company_id.name}\" Q/A Forums.</p>\n"
"                <p>To validate your email, please click on the following "
"link:</p>\n"
"                <a style=\"margin-left: 15px; padding:5px 10px; border-"
"radius: 3px; background-color:#a24689; text-align:center; text-decoration:"
"none; color:#F7FBFD;\" href=\"${ctx.get('token_url')}\">\n"
"                    Validate my account for \"${object.company_id.name}\" Q/"
"A Forums\n"
"                </a>\n"
"                <p>Thanks for your participation!</p>\n"
"            </td>\n"
"        </tr></tbody>\n"
"    </table>\n"
"</div>"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:23
#, python-format
msgid ""
" karma is required to perform this action. You can earn karma by having your "
"answers upvoted by the community."
msgstr ""
"karma es requerido para ejecutar esta acción. Usted puede ganar karma "
"teniendo respuestas votadas positivamente por la comunidad."

#. module: website_forum
#: model:mail.template,subject:website_forum.validation_email
msgid "${object.company_id.name} Forums validation"
msgstr "${object.company_id.name} Validación de los foros"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "&amp;nbsp;&amp;nbsp;<i class=\"fa fa-times\"/>&amp;nbsp;&amp;nbsp;"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(La sección anterior fue adaptada de las PF de Stackoverflow.)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "(votes - 1) **"
msgstr "(votos - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "/ (days + 2) **"
msgstr "/ (días + 2) **"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "45% of questions shared"
msgstr "45% de preguntas compartidas"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:30
#, python-format
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr "65% más posibilidades de obtener una respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Closed]</b>"
msgstr "<b> [Cerrado]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Deleted]</b>"
msgstr "<b> [Borrado]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<b> [Offensive]</b>"
msgstr "<b> [Ofensivo]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.users
msgid "<b> badges:</b>"
msgstr "<b> insignias:</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not add or expand questions</b>. Instead\n"
"    either edit the question or add a comment."
msgstr ""
"<b>Responder no debería añadir o expandir preguntas</b>. En su lugar se "
"modifica la pregunta o se deja un comentario."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#, fuzzy
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr ""
"<b>Las respuestas no deberían añadir o expandir las preguntas</b>. En vez de "
"esto, edite la pregunta o añada un comentario de pregunta."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead\n"
"    add a comment on the other answers."
msgstr ""
"<b> Las respuestas no se deben comentar sobre otras respuestas </b>. En su "
"lugar\n"
"añada un comentario sobre las otras respuestas."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr ""
"<b> Las respuestas no se deben comentar sobre otras respuestas </b>. Mejor\n"
"añada un comentario sobre las otras respuestas."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers should not start debates</b>\n"
"    This community Q&amp;A is not a discussion group. Please avoid holding "
"debates in\n"
"    your answers as they tend to dilute the essence of questions and "
"answers. For\n"
"    brief discussions please use commenting facility."
msgstr ""
"<b>Las respuestas no deben iniciar debates </b>\n"
"Esta comunidad de Preguntas y Respuestas no es un grupo de discusión. Por "
"favor, evitar la celebración de debates en sus respuestas ya que tienden a "
"diluir la esencia de preguntas y respuestas. Para breves discusiones por "
"favor utilice comentando las instalaciones."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>Las respuestas no debe apuntar a otras Preguntas </b>. En su lugar mejor "
"añada un comentario indicando pregunta \"Posible duplicado de ...\". Sin "
"embargo, está bien incluir enlaces a otras preguntas o respuestas que "
"proporcionan información adicional relevante."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Answers shouldn't just point to other questions</b>.\n"
"    Instead add a comment indicating <i>\"Possible duplicate\n"
"    of...\"</i>. However, it's fine to include links to other\n"
"    questions or answers providing relevant additional\n"
"    information."
msgstr ""
"<b>Las respuestas no deben apuntar a otras preguntas </b>.\n"
"En su lugar mejor añada un comentario indicando <i>\"Posible duplicado de ..."
"\"</i>. Sin embargo, está bien incluir enlaces a otras preguntas o "
"respuestas que proporcionan información adicional relevante."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#, fuzzy
msgid ""
"<b>Answers shouldn't just provide a\n"
"    link a solution</b>. Instead provide the solution\n"
"    description text in your answer, even if it's just a\n"
"    copy/paste. Links are welcome, but should be complementary to\n"
"    answer, referring sources or additional reading."
msgstr ""
"<b>Las respuestas no sólo deben proporcionar un enlace de una solución</b>. "
"En lugar proporcione la descripción de la solución en su respuesta, incluso "
"si es sólo un copiar / pegar. Los enlaces son bienvenidos, pero deben "
"complementarse con la respuesta, refiriéndose a fuentes o lectura adicional."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#, fuzzy
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the "
"solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>Las respuestas no sólo deben proporcionar un enlace de una solución</b>. "
"En lugar  proporcione la descripción de la solución en su respuesta, incluso "
"si es sólo un copiar / pegar. Los enlaces son bienvenidos, pero deben "
"complementarse con la respuesta, refiriéndose a fuentes o lectura adicional."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can\n"
"    search questions by their title or tags.  It’s also OK to\n"
"    answer your own question."
msgstr ""
"<b> Antes de preguntar - por favor asegúrese de buscar una pregunta similar "
"</b> Puede\n"
"hacer búsqueda de preguntas por su título o etiquetas. También está bien "
"para responder a su propia pregunta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<b>No badge yet!</b><br/>"
msgstr "<b>Aún sin insignias!</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<b>No vote given by you yet!</b>"
msgstr "<b>Usted no ha votado aún!</b>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"<b>Please avoid asking questions that are too subjective\n"
"    and argumentative</b> or not relevant to this community."
msgstr ""
"<b>Por favor, evite las preguntas que son demasiado subjetivas y "
"argumentativas </b> o no relevantes para esta comunidad."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on "
"the question or answer, just\n"
"        <b>use the commenting tool.</b> Please remember that you can always "
"<b>revise your answers</b>\n"
"        - no need to answer the same question twice. Also, please <b>don't "
"forget to vote</b>\n"
"        - it really helps to select the best questions and answers!"
msgstr ""
"<b>Por favor, intente dar una respuesta sustancial. </b> Si desea hacer "
"comentarios sobre la pregunta o respuesta, simplemente <b> utilice la "
"herramienta de comentarios. </b> Por favor, recuerde que siempre puede <b> "
"revisar su respuestas </b>\n"
"- No hay necesidad de responder a la misma pregunta dos veces. También, por "
"favor <b> no se olvide de votar </b>\n"
"- ¡Esto realmente ayuda a seleccionar las mejores preguntas y respuestas!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "<b>Share</b> Something Awesome."
msgstr "<b>Comparta</b> algo impresionante."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "<b>[Answer]</b>"
msgstr "<b>[Respuesta]</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> usuarios premiados</i>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "<i class=\"text-muted\">awarded users</i>"
msgstr "<i class=\"text-muted\"> usuarios premiados</i>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid ""
"<small class=\"text-muted\">\n"
"                    Flagged\n"
"                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                    Reportada\n"
"                </small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<small>profile</small>"
msgstr "<small>perfil</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"<span class=\"caret\"/>\n"
"                                <span class=\"sr-only\">Select Post</span>"
msgstr ""
"<span class=\"caret\"/>\n"
"                                <span class=\"sr-only\">Seleccionar Mensaje</"
"span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"<span class=\"sr-only\">Toggle navigation</span>\n"
"                        <span class=\"icon-bar\"/>\n"
"                        <span class=\"icon-bar\"/>"
msgstr ""
"<span class=\"sr-only\">Alternar navegación</span>\n"
"                        <span class=\"icon-bar\"/>\n"
"                        <span class=\"icon-bar\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span class=\"text-muted\">(only one answer per question is allowed)</span>"
msgstr ""
"<span class=\"text-muted\">(sólo se permite una respuesta por pregunta)</"
"span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">bio</span>"
msgstr "<span class=\"text-muted\">bio</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "<span class=\"text-muted\">or</span>"
msgstr "<span class=\"text-muted\">o</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "<span class=\"text-muted\">stats</span>"
msgstr "<span class=\"text-muted\">estadísticas</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span> on </span>"
msgstr "<span> en </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid ""
"<span> • </span>\n"
"                Flagged"
msgstr ""
"<span> • </span>\n"
"                Reportada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>By </span>"
msgstr "<span>Por </span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
#, fuzzy
msgid "A new answer on"
msgstr "Nueva Respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#, fuzzy
msgid "A new question"
msgstr "Responder preguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.tag
#, fuzzy
msgid ""
"A tag is a label that categorizes your question with other,\n"
"            similar questions. Using the right tags makes it easier for\n"
"            others to find and answer your question. (Hover the mouse to "
"follow/unfollow tag(s))"
msgstr ""
"Una etiqueta es un rótulo que categoriza su pregunta con otras preguntas "
"similares. Usando las etiquetas correctas le facilita a otros encontrar y "
"responder sus preguntas."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "About This Community"
msgstr "Sobre Esta Comunidad"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Accept <i class=\"fa fa-check\"/>"
msgstr "Aceptar <i class=\"fa fa-check\"/>"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:114
#, python-format
msgid "Accept Answer"
msgstr "Aceptar Respuesta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "Aceptar una respuesta en preguntas propias"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "Aceptar una respuesta en todas las preguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Accepted Answer"
msgstr "Respuesta Aceptada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "Aceptando una respuesta"

#. module: website_forum
#: selection:forum.post,state:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_active
msgid "Active"
msgstr "Activo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Activity"
msgstr "Actividad"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:19
#, python-format
msgid "Add Content"
msgstr "Añadir Contenido"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Add a Comment"
msgstr "Añadir un Comentario"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:25
#, python-format
msgid "Add page in menu"
msgstr "Añadir página en el menú"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr ""
"Después de publicar el usuario será propuesto para compartir su pregunta o "
"la respuesta en las redes sociales, permitiendo el uso de la red social para "
"la propagación del contenido del foro."

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:187
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#, python-format
msgid "All"
msgstr "Todo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_bump
msgid "Allow Bump"
msgstr "Permitir Críticas"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:94
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#, python-format
msgid "Answer"
msgstr "Responder"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:498
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
#, python-format
msgid "Answer Edited"
msgstr "Respuesta Editada"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:108
#, python-format
msgid "Answer Posted"
msgstr "Respuesta Publicada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "Respuesta aceptada"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "Respuesta aceptada con 15 o más votos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "Respuesta votada en contra"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "Respuesta reportada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_answer
msgid "Answer questions"
msgstr "Responder preguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "Respuesta votada a favor"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "Respuesta votada a favor 15 veces"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "Respuesta votada a favor 4 veces"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "Respuesta votada a favor 6 veces"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "La respuesta fue aceptada con 3 o más votos"

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Answered"
msgstr "Respondidas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answered Questions"
msgstr "Responder Preguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Answered by"
msgstr "Respondida por"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "Pregunta propia contestada con al menos 4 votos a favor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_child_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Answers"
msgstr "Respuestas"

#. module: website_forum
#: selection:forum.post,post_type:0
msgid "Article"
msgstr "Artículo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask Your Question"
msgstr "Haga Su Pregunta"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:53
#: model_terms:ir.ui.view,arch_db:website_forum.header
#, python-format
msgid "Ask a Question"
msgstr "Hacer una Pregunta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_ask
msgid "Ask questions"
msgstr "Hacer preguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_post
msgid "Ask questions without validation"
msgstr "Hacer preguntas sin validación"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:56
#, python-format
msgid "Ask the question in this forum by clicking on the button."
msgstr "Haga una pregunta en este foro haciendo clic en este botón."

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "Formuló una pregunta y aceptó una respuesta"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "Hizo una pregunta con por lo menos 150 lecturas"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "Hizo una pregunta con por lo menos 250 lecturas"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "Hizo una pregunta con por lo menos 500 lecturas"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "Primera pregunta con al menos un voto a favor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_create_date
msgid "Asked on"
msgstr "Preguntó en"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Asked:"
msgstr "Preguntó:"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_new
msgid "Asking a question"
msgstr "Haciendo una pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Author"
msgstr "Autor"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "Autobiógrafo/a"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Avoid unnecessary introductions (Hi,... Please... Thanks...),"
msgstr ""
"Evite introducciones no documentales (Hola,... Por favor... Gracias...),"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Back to"
msgstr "Volver a"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:322
#: code:addons/website_forum/controllers/main.py:389
#, python-format
msgid "Bad Request"
msgstr "Petición Errónea"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "Badge \""
msgstr "Insignia \""

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_badge_ids
#: model_terms:ir.ui.view,arch_db:website_forum.badge
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Badges"
msgstr "Insignias"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge
msgid ""
"Besides gaining reputation with your questions and answers,\n"
"            you receive badges for being especially helpful. Badges\n"
"            appear on your profile page, and your posts."
msgstr ""
"Además de ganar reputación con sus preguntas y respuestas, recibirá "
"insignias por ser especialmente útil. Las insignias aparecen en su página de "
"perfil, y sus mensajes."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Biography"
msgstr "Biografía"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_bronze_badge
msgid "Bronze badges count"
msgstr "Insignias de bronce cuentan"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_bump_date
msgid "Bumped on"
msgstr "Retomada el"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, fuzzy, python-format
msgid "By sharing you answer, you will get additional"
msgstr "Compartiendo su respuesta, usted obtendrá adiciones de"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_accept
msgid "Can Accept"
msgstr "Puede Aceptar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_answer
msgid "Can Answer"
msgstr "Puede Responder"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_ask
msgid "Can Ask"
msgstr "Puede Preguntar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_post
msgid "Can Automatically be Validated"
msgstr "Puede ser Validado Automáticamente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_close
msgid "Can Close"
msgstr "Puede Cerrar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_comment
msgid "Can Comment"
msgstr "Puede Comentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_comment_convert
msgid "Can Convert to Comment"
msgstr "Puede Convertir a Comentario"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_downvote
msgid "Can Downvote"
msgstr "Puede Votar en Contra"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_edit
msgid "Can Edit"
msgstr "Puede Editar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_flag
msgid "Can Flag"
msgstr "Puede Reportar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_moderate
msgid "Can Moderate"
msgstr "Puede Moderar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_unlink
msgid "Can Unlink"
msgstr "Puede Desvincular"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_upvote
msgid "Can Upvote"
msgstr "Puede Votar a Favor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_view
msgid "Can View"
msgstr "Puede Ver"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_retag
msgid "Change question tags"
msgstr "Cambiar las etiquetas de la pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_badges
msgid "Check available badges"
msgstr "Ver insignias disponibles"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_bump
msgid ""
"Check this box to display a popup for posts older than 10 days without any "
"given answer. The popup will offer to share it on social networks. When "
"shared, a question is bumped at the top of the forum."
msgstr ""
"Marque esta casilla para mostrar un popup con mensajes sin respuesta de hace "
"más de 10 días. El popup tendrá la opción de compartirlos en redes sociales. "
"Cuando sea compartida, una pregunta es retomada al principio del foro."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "Comentador Jefe"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "City"
msgstr "Ciudad"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:44
#, python-format
msgid "Click <em>Continue</em> to create the forum."
msgstr "Clic en <em>Siguiente</em> para crear el foro."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:117
#, python-format
msgid "Click here to accept this answer."
msgstr "Clic aquí para aceptar esta respuesta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Click here to send a verification email allowing you to participate to the "
"forum."
msgstr ""
"Haz clic aquí pare enviarte un E-mail de verificación para permitirte "
"participar en el foro."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get bad question samples"
msgstr "Clic para ver ejemplos de malas preguntas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Click to get good question titles"
msgstr "Clic para ver buenos títulos de preguntas"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:105
#, python-format
msgid "Click to post your answer."
msgstr "Clic para publicar su respuesta."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:85
#, python-format
msgid "Click to post your question."
msgstr "Clic para publicar su pregunta."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:46
#: selection:forum.post,state:0
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:123
#, python-format
msgid "Close Tutorial"
msgstr "Cerrar Tutorial"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_close_all
msgid "Close all posts"
msgstr "Cerrar todos los mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_close_own
msgid "Close own posts"
msgstr "Cerrar mensajes propios"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_uid
msgid "Closed by"
msgstr "Cerrado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_date
msgid "Closed on"
msgstr "Cerrado el"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_name
msgid "Closing Reason"
msgstr "Razón de Cierre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Comment"
msgstr "Comentario"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_all
msgid "Comment all posts"
msgstr "Comentar todos los mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_own
msgid "Comment own posts"
msgstr "Comentar mensajes propios"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post..."
msgstr "Comentar este mensaje..."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "Comentador"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_display_post_answer
msgid "Comments"
msgstr "Comentarios"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_website_message_ids
msgid "Comments on forum post"
msgstr "Comentarios en mensaje del foro"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "Completar la biografía propia"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_1
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
msgid "Completed own biography"
msgstr "Completar la biografía propia"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:120
#, python-format
msgid "Congratulations"
msgstr "Felicitaciones"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:122
#, python-format
msgid ""
"Congratulations! You just created and post your first question and answer."
msgstr ""
"Felicitaciones! Acaba de crear y publicar su primera pregunta y respuesta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid ""
"Congratulations! Your email has just been validated. You may now participate "
"to our forums."
msgstr ""
"Felicitaciones! Su correo ha sido validado. Ahora puede participar de "
"nuestros foros."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_content
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Content"
msgstr "Contenido"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:50
#: code:addons/website_forum/static/src/js/website_tour_forum.js:91
#: code:addons/website_forum/static/src/js/website_tour_forum.js:111
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_convert_all
msgid "Convert all answers to comments and vice versa"
msgstr "Convertir todas las respuestas en comentarios y viceversa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_accept
msgid "Convert comment to answer"
msgstr "Convertir comentario en respuesta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_convert_own
msgid "Convert own answers to comments and vice versa"
msgstr "Convertir respuestas propias en comentarios y viceversa"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_is_correct
msgid "Correct"
msgstr "Correcto"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_is_correct
msgid "Correct answer or answer accepted"
msgstr "Respuesta correcta o aceptada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country"
msgstr "País"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Country..."
msgstr "País..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_create_date
msgid "Create Date"
msgstr "Fecha de Creación"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:40
#, python-format
msgid "Create Forum"
msgstr "Crear Foro"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:14
#, python-format
msgid "Create a Question!"
msgstr "Crear una Pregunta!"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:11
#, python-format
msgid "Create a question"
msgstr "Crear una pregunta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_tag_create
#, fuzzy
msgid "Create new tags"
msgstr "Creado"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "Creó una tag usada por 15 preguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_create_date
msgid "Created on"
msgstr "Creado"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "Pregunta Creíble"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "Crítico/a"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_default_order
msgid "Default Order"
msgstr "Orden Predeterminado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_default_post_type
msgid "Default Post"
msgstr "Mensaje Predeterminado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_unlink_all
msgid "Delete all posts"
msgstr "Borrar todos los mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_unlink_own
msgid "Delete own posts"
msgstr "Borrar mensajes propios"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "Borró mensaje propio con 3 o más votos en contra"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "Borró mensaje propio con 3 o más votos a favor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_description
msgid "Description"
msgstr "Descripción"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Design Welcome Message"
msgstr "Diseñe el Mensaje de Bienvenida"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "Disciplinado/a"

#. module: website_forum
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
msgid "Discussion"
msgstr "Charla"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_discussion
msgid "Discussions"
msgstr "Charlas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_display_name
msgid "Display Name"
msgstr "Nombre Público"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_user_bio
msgid "Display detailed user biography"
msgstr "Mostrar una detallada biografía del usuario"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_downvote
msgid "Downvote"
msgstr "Votar en contra"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit"
msgstr "Editar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Edit Profile"
msgstr "Editar Perfil"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Edit Your Bio"
msgstr "Editar Su Bio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Edit Your Previous Answer"
msgstr "Editar Su Respuesta Anterior"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_all
msgid "Edit all posts"
msgstr "Editar todos los mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_edit_own
msgid "Edit own posts"
msgstr "Editar mensajes propios"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit reply"
msgstr "Editar respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Edit your Link"
msgstr "Editar su Enlace"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "Editar su Mensaje"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "Editor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_editor
msgid "Editor Features: image and links"
msgstr "Características del Editor: imagen y enlaces"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Email"
msgstr "Correo"

#. module: website_forum
#: model:ir.model,name:website_forum.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: website_forum
#: model:ir.model,name:website_forum.model_survey_mail_compose_message
msgid "Email composition wizard for Survey"
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "Iluminado/a"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:37
#, fuzzy, python-format
msgid "Enter a name for your new forum."
msgstr "Escriba el nombre para su nuevo foro y de clic en 'Siguiente'."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "Pregunta Popular"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_favourite_count
msgid "Favorite Count"
msgstr "Cuenta Favorita"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "Pregunta Favorita"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_favourite_ids
msgid "Favourite"
msgstr "Favorita"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "Pregunta Favorita (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "Pregunta Favorita (25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "Pregunta Favorita (5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Favourite Questions"
msgstr "Preguntas Favoritas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Filter on"
msgstr "Filtrar en"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "Primer Parámetro de Relevancia"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "Primer voto negativo"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "Primera edición"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "Primer voto a favor"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_flag
msgid "Flag a post as offensive"
msgstr "Reportar un mensaje como ofensivo"

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Flagged"
msgstr "Reportado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_flag_user_id
msgid "Flagged by"
msgstr "Reportado por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Followed"
msgstr "Con Seguimiento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Followed Questions"
msgstr "Preguntas con Seguimiento"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your\n"
"    input will be upvoted. On the other hand if the answer is misleading - "
"it will\n"
"    be downvoted. Each vote in favor will generate 10 points, each vote "
"against\n"
"    will subtract 10 points. There is a limit of 200 points that can be "
"accumulated\n"
"    for a question or answer per day. The table given at the end explains "
"reputation point\n"
"    requirements for each type of moderation task."
msgstr ""
"Por ejemplo, si usted genera una pregunta interesante o da una respuesta "
"útil, su entrada será valorada positivamente. Por otro lado, si la respuesta "
"es engañosa - que será valorada negativamente. Cada voto a favor generará 10 "
"puntos, cada voto en contra restará 10 puntos. Hay un límite de 200 puntos "
"que se pueden acumular para una pregunta o una respuesta por día. El cuadro "
"que figura al final explica requisitos de puntos de reputación para cada "
"tipo de tarea de moderación."

#. module: website_forum
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
#: model:website.menu,name:website_forum.menu_questions
msgid "Forum"
msgstr "Foro"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge_level
#: model:ir.model.fields,field_description:website_forum.field_gamification_badge_user_level
msgid "Forum Badge Level"
msgstr "Nivel de Insignias del Foro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_users_form_forum
msgid "Forum Karma"
msgstr "Karma del Foro"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:33
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_name
#, python-format
msgid "Forum Name"
msgstr "Nombre del Foro"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Forum Post"
msgstr "Mensaje del Foro"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_list
msgid "Forum Posts"
msgstr "Mensajes del Foro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Forum Settings"
msgstr "Ajustes del Foro"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "Etiquetas del Foro"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.action_forum_forum
#: model:ir.ui.menu,name:website_forum.menu_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header_footer_custom
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_list
msgid "Forums"
msgstr "Foros"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge
msgid "Gamification badge"
msgstr "Insignia de ludificación"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification challenge"
msgstr "Desafío de ludificación"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_badge_user
msgid "Gamification user badge"
msgstr "Insignia de ludificación del usuario"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:74
#, python-format
msgid "Give Tag"
msgstr "Obtener Etiqueta"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:63
#, python-format
msgid "Give your question title."
msgstr "Dele un título a su pregunta."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_gold_badge
msgid "Gold badges count"
msgstr "Conteo de Insignias de oro"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "Buena Respuesta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "Buena Respuesta (6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "Buena Pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_graph
msgid "Graph of Posts"
msgstr "Gráfico de Publicaciones"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "Gran Respuesta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "Gran Respuesta (15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "Gran Pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_faq
msgid "Guidelines"
msgstr "Guía"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "Buena Respuesta"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "Gurú (15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_uid_has_answered
msgid "Has Answered"
msgstr "Ha Respondido"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "Ayuda"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr ""
"Si el autor no tiene suficiente karma, un atributo nofollow es añadido a los "
"enlaces"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If this approach is not for you, please respect the community and use Google"
"+\n"
"    communities instead."
msgstr ""
"Si esta aproximación no es para usted, por favor respete la comunidad y "
"mejor use las comunidades Google+."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"Si cierra esta publicación, se oculta para la mayoría de usuarios. Sólo los "
"usuarios que tienen un alto Karma pueden ver las publicaciones cerradas y "
"moderadas."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"If you fit in one of these example or if your motivation for asking the\n"
"    question is “I would like to participate in a discussion about ______”, "
"then\n"
"    you should not be asking here but on our mailing lists.\n"
"    However, if your motivation is “I would like others to explain ______ to "
"me”,\n"
"    then you are probably OK."
msgstr ""
"Si usted encaja en uno de estos ejemplos o si su motivación para hacer la "
"pregunta es \"Me gustaría participar en una discusión acerca de ______\", "
"entonces no se debería hacer aquí, pero si en nuestras listas de correo.\n"
"Sin embargo, si su motivación es \"Me gustaría explicar a los demás que  "
"_____ para mí\", entonces usted está probablemente bien."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"Si marca este post como ofensivo, estará oculto para la mayoría de usuarios. "
"Solamente\n"
"los usuarios que tienen un alto Karma pueden ver los mensajes ofensivos a "
"moderarlos."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:78
#, python-format
msgid "Insert tags related to your question."
msgstr "Inserte etiquetas relacionadas a su pregunta."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_user_favourite
msgid "Is Favourite"
msgstr "Es Favorito"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_has_validated_answer
msgid "Is answered"
msgstr "Está respondido"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "Es la biografía del autor visible desde su publicación"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "It appears your email has not been verified."
msgstr "Parece ser que su correo electrónico no ha sido verificado."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_karma
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Karma"
msgstr "Karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Gains"
msgstr "Ganancias de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Karma Related Rights"
msgstr "Derechos Relacionados karma"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_close
msgid "Karma to close"
msgstr "Karma para cerrar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_comment
msgid "Karma to comment"
msgstr "Karma para comentar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "Karma para convertir comentario a respuesta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_edit
msgid "Karma to edit"
msgstr "Karma a editar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_karma_unlink
msgid "Karma to unlink"
msgstr "Karma para no vincular"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Keep Informed"
msgstr "Mantenerme Informado"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote___last_update
#: model:ir.model.fields,field_description:website_forum.field_forum_tag___last_update
msgid "Last Modified on"
msgstr "Última Modificación el"

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Last Updated"
msgstr "Última Actualización"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_write_uid
msgid "Last Updated by"
msgstr "Actualizado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_write_date
msgid "Last Updated on"
msgstr "Actualizado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Last activity date"
msgstr "Fecha de la última actividad"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Last updated:"
msgstr "Última actualización:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "Dejó 10 respuestas con puntaje de 10 o más"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:15
#, python-format
msgid "Let's go through the first steps to create a new question."
msgstr ""
"Lo guiamos a través de los primeros pasos para crear una nueva pregunta."

#. module: website_forum
#: selection:forum.forum,default_post_type:0
msgid "Link"
msgstr "Enlace"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_link
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Links"
msgstr "Enlaces"

#. module: website_forum
#: model:ir.model,name:website_forum.model_mail_message
#, fuzzy
msgid "Message"
msgstr "Mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_moderate
msgid "Moderate posts"
msgstr "Moderar mensajes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Moderation Tools"
msgstr "Herramientas de Moderación"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "More over:"
msgstr "Además:"

#. module: website_forum
#: selection:forum.forum,default_order:0
msgid "Most Voted"
msgstr "Más Votada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most answered"
msgstr "Más respondida"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Most voted"
msgstr "Más votada"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_user_vote
msgid "My Vote"
msgstr "Mi Voto"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_name
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_form
msgid "Name"
msgstr "Nombre"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "Nueva Respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "New Discussion"
msgstr "Nuevo Tema"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.editor.js:14
#: code:addons/website_forum/static/src/js/website_tour_forum.js:26
#: model_terms:ir.ui.view,arch_db:website_forum.content_new_forum
#, python-format
msgid "New Forum"
msgstr "Nuevo Foro"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:47
#, python-format
msgid "New Forum Created"
msgstr "Nuevo Foro Creado"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "Nueva Pregunta"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:88
#, python-format
msgid "New Question Created"
msgstr "Nueva Pregunta Creada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "New Topic"
msgstr "Nuevo Tema"

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Newest"
msgstr "Reciente"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "Respuesta Chévere"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "Respuesta Chévere (4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "Pregunta Chévere"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_dofollow
msgid "Nofollow links"
msgstr "Enlaces nofollow"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:833
#: code:addons/website_forum/models/forum.py:853
#, python-format
msgid "Not allowed to vote for its own post"
msgstr "No se les permite votar para su propia publicación"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:897
#, fuzzy, python-format
msgid "Not enough karma to create a new Tag"
msgstr "Seleccione esta opción para crear un nuevo foro."

#. module: website_forum
#: code:addons/website_forum/models/forum.py:488
#, python-format
msgid "Not enough karma to retag."
msgstr ""

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "Pregunta Notable"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_posts_count
msgid "Number of Posts"
msgstr "Número de Mensajes"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_views
msgid "Number of Views"
msgstr "Número de Vistas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_child_count
msgid "Number of answers"
msgstr "Número de respuestas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_count_flagged_posts
msgid "Number of flagged posts"
msgstr "Número de mensajes reportados"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "Número de publicaciones esperando por validación"

#. module: website_forum
#: selection:forum.post,state:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr "Ofensivo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
msgid "On"
msgstr "En"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid "On average,"
msgstr "En promedio,"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Options"
msgstr "Opciones"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Orders"
msgstr "Órdenes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "Our forums"
msgstr "Nuestros foros"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "Presión de Pares"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Pending"
msgstr "Pendiente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "People"
msgstr "Personas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_plain_content
msgid "Plain Content"
msgstr "Contenido Plano"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Please enter a descriptive question (should finish by a '?')"
msgstr "Introduzca una pregunta descriptiva (debería finalizar con un '?')"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid ""
"Please enter a valid email address in order to receive notifications from "
"answers or comments."
msgstr ""
"Introduzca un correo electrónico válido para recibir notificaciones de las "
"respuestas o comentarios."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "Pregunta Popular"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "Pregunta Popular (150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "Pregunta Popular (250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "Pregunta Popular (500)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Post"
msgstr "Mensaje"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:101
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#, python-format
msgid "Post Answer"
msgstr "Publicar Respuestas"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "Publicar Razón de Cierre"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Post Comment"
msgstr "Publicar Comentario"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_website_message_ids
msgid "Post Messages"
msgstr "Publicar Mensajes"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:81
#, python-format
msgid "Post Question"
msgstr "Publicar Pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Post Title"
msgstr "Titulo de la entrada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Post Types"
msgstr "Tipos de Publicaciones"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "Publicar Su Pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Post Your Topic"
msgstr "Publicar Su Tema"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Post:"
msgstr "Mensaje:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "Publicó 10 comentarios"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "Publicó 100 comentarios"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:432
#, python-format
msgid "Posting answer on a [Deleted] or [Closed] question is not possible"
msgstr ""
"No es posible publicar una respuesta en una pregunta [Borrada] o [Cerrada]"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag_post_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_posts
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Posts"
msgstr "Mensajes"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Provide enough details and, if possible, give an example."
msgstr "Proporcionar suficientes detalles y, si es posible, dar un ejemplo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Public profile"
msgstr "Perfil público"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "Experto"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:48
#, python-format
msgid ""
"Put this question back in the top list by sharing it on social networks."
msgstr ""
"Ponga esta pregunta al principio de la lista compartiéndola en las redes "
"sociales."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:98
#, python-format
msgid "Put your answer here."
msgstr "Ponga su respuesta aquí."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:71
#, python-format
msgid "Put your question here."
msgstr "Ponga su pregunta aquí."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header_footer_custom
msgid "Q&amp;amp;A"
msgstr "P y R"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:66
#: selection:forum.forum,default_post_type:0 selection:forum.post,post_type:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_parent_id
#, python-format
msgid "Question"
msgstr "Pregunta"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:501
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
#, python-format
msgid "Question Edited"
msgstr "Pregunta Editada"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:59
#, python-format
msgid "Question Title"
msgstr "Título de la Pregunta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_downvote
msgid "Question downvoted"
msgstr "Pregunta votada en contra"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Question has accepted answer"
msgstr "Pregunta con respuesta aceptada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "Pregunta no encontrada!"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "Pregunta marcada como favorita por 1 usuario"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "Pregunta marcada como favorita por 25 usuario"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "Pregunta marcada como favorita por 5 usuario"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Question tools"
msgstr "Herramientas de las preguntas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_gen_question_upvote
msgid "Question upvoted"
msgstr "Pregunta votada a favor"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "Pregunta votada a favor 15 veces"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "Pregunta votada a favor 4 veces"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "Pregunta votada a favor 6 veces"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Questions"
msgstr "Preguntas"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:515
#, python-format
msgid "Re: %s"
msgstr "Re: %s"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Read Guidelines"
msgstr "Lea la Guía"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Real name"
msgstr "Nombre real"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_closed_reason_id
msgid "Reason"
msgstr "Razón"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason_reason_type
msgid "Reason Type"
msgstr "Tipo de Razón"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "Reason:"
msgstr "Razón:"

#. module: website_forum
#: model:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr ""
"Recibió por lo menos 3 votos positivos para una respuesta la primera vez"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Redirect to external link"
msgstr "Redirigir a enlace externo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reject <i class=\"fa fa-times\"/>"
msgstr "Rechazar <i class=\"fa fa-times\"/>"

#. module: website_forum
#: selection:forum.forum,default_order:0
#: model:ir.model.fields,field_description:website_forum.field_forum_post_relevancy
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Relevance"
msgstr "Relevancia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_forum_form
msgid "Relevance Computation"
msgstr "Cálculo de la Relevancia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answers
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Reply"
msgstr "Responder"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_self_reply
msgid "Reply to own question"
msgstr "Responder preguntas propias"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "Return to the question list."
msgstr "Regresar a la lista de preguntas."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_moderator_id
msgid "Reviewed by"
msgstr "Revisado por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save"
msgstr "Guardar"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "Erudito/a"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Search in Post"
msgstr "Buscar en Mensaje"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "Segundo Parámetro de Relevancia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
#, fuzzy
msgid "See post"
msgstr "mensaje"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#, fuzzy
msgid "See question"
msgstr "Crear una pregunta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Seen:"
msgstr "Visto/a:"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:29
#, python-format
msgid "Select this menu item to create a new forum."
msgstr "Seleccione esta opción para crear un nuevo foro."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "Auto-Didacta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Set a clear, explicit and concise question title\n"
"                (check"
msgstr "Fije un título claro, explícito y conciso (revise"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Share"
msgstr "Compartir"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"Share an awesome link. Your post will appear in the 'Newest' top-menu.\n"
"            If the community vote on your post, it will get traction by "
"being promoted\n"
"            in the homepage."
msgstr ""
"Comparta un enlace increible. Su mensaje aparecerá en el menú superior "
"'Reciente'\n"
"Si la comunidad vota en su mensaje, generará tendencia y será promovido en "
"la página principal."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:39
#, python-format
msgid ""
"Share this content to increase your chances to be featured on the front page "
"and attract more visitors."
msgstr ""
"Comparta este contenido para incrementar sus posibilidades de ser destacado "
"en la página frontal y atraer más visitantes."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_allow_share
msgid "Sharing Options"
msgstr "Opciones para Compartir"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_res_users_silver_badge
msgid "Silver badges count"
msgstr "Cuenta de insignias de plata"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:16
#, python-format
msgid "Skip It"
msgstr "Omitirlo"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:66
#, python-format
msgid "Sorry you must be logged to flag a post"
msgstr "Lo sentimos, usted debe haber iniciado sesión para reportar un mensaje"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:114
#, python-format
msgid "Sorry you must be logged to vote"
msgstr "Lo sentimos, usted debe tener sesión iniciada para poder votar"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:145
#, python-format
msgid "Sorry, anonymous users cannot choose correct answer."
msgstr ""
"Lo sentimos, los usuarios anónimos no pueden escoger la respuesta correcta."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "Lo sentimos, esta pregunta ya no está disponible."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:109
#, python-format
msgid "Sorry, you cannot vote for your own posts"
msgstr "Lo sentimos, no puede votar sus propias publicaciones"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Sort by"
msgstr "Ordenar por"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:16
#, python-format
msgid "Start Tutorial"
msgstr "Comenzar Tutorial"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Stats"
msgstr "Estadísticas"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_state
msgid "Status"
msgstr "Estado"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "Pregunta Excelente"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "Estudiante"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "Submit a Link"
msgstr "Enviar un Enlace"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "Seguidor"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
#: model:ir.ui.menu,name:website_forum.menu_forum_tag
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.view_forum_post_search
msgid "Tag"
msgstr "Etiqueta"

#. module: website_forum
#: sql_constraint:forum.tag:0
msgid "Tag name already exists !"
msgstr "Ese nombre de etiqueta ya existe!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_tag_ids
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.header
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.tag
msgid "Tags"
msgstr "Etiquetas"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "Taxonomista"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "Profesor"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_bump_date
msgid ""
"Technical field allowing to bump a question. Writing on this field will "
"trigger a write on write_date and therefore bump the post. Directly writing "
"on write_date is currently not supported and this field is a workaround."
msgstr ""
"Campos técnicos que permiten impulsar una pregunta. Escribiendo en este "
"campo se realiza la escritura en write_date y por lo tanto impulsar la "
"publicación. La escritura directa en el campo write_date actualmente no está "
"soportado y este campo es una solución."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:9
#, python-format
msgid "Thanks for posting!"
msgstr "Gracias por participar!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The"
msgstr "El"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The flagged queue is empty."
msgstr "La cola de reportados está vacía."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"The goal of this site is create a relevant knowledge base that would answer\n"
"    questions related to Odoo."
msgstr ""
"La meta de este sitio es crear conocimiento relevante que pueda responder "
"preguntas relacionadas con Odoo."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The offensive queue is empty."
msgstr "La cola de ofensivos está vacía."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "The validation queue is empty."
msgstr "La cola de validación está vacía."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced "
"users of\n"
"    this site in order to improve the overall quality of the knowledge base "
"content.\n"
"    Such privileges are granted based on user karma level: you will be able "
"to do the same\n"
"    once your karma gets high enough."
msgstr ""
"Por lo tanto, las preguntas y respuestas se pueden editar en páginas wiki "
"por usuarios experimentados en este sitio con el fin de mejorar la calidad "
"general del contenido de la base de conocimientos.\n"
"Estos privilegios se conceden en base al nivel de usuario Karma: usted será "
"capaz de hacer lo mismo una vez que su karma se eleva lo suficiente."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"This community is for professional and enthusiast users,\n"
"    partners and programmers. You can ask questions about:"
msgstr ""
"Esta comunidad es para usuarios profesionales y entusiastas, asociados y "
"programadores. Usted puede hacer preguntas acerca de:"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"Esta comunidad es para los profesionales y aficionados de nuestros productos "
"y servicios. Compartir y discutir las mejores y nuevas ideas de contenido de "
"marketing, construir su perfil profesional y convertirse en un mejor "
"comercializador juntos."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"Esta fórmula es usada para ordenar por relevancia. La variable 'votes' "
"representa el número de votos de un mensaje, y 'días' es el número de días "
"desde la creación del mensaje"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:408
#, python-format
msgid "This forum does not allow %s"
msgstr "Este foro no permite %s"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:49
#, python-format
msgid "This page contains all the information related to the new forum."
msgstr ""
"Esta página contiene toda la información relacionada con el nuevo foro."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:110
#, fuzzy, python-format
msgid "This page contains the newly created questions and its answers."
msgstr "Esta página contiene una nueva pregunta creada y su respuesta."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:90
#, fuzzy, python-format
msgid "This page contains the newly created questions."
msgstr "Esta página contiene una nueva pregunta creada."

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:76
#, python-format
msgid "This post can not be flagged"
msgstr "Este mensaje no puede ser reportado"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_forum.js:71
#, python-format
msgid "This post is already flagged"
msgstr "Este mensaje ya está reportado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.private_profile
msgid "This profile is private!"
msgstr "Este perfil es privado!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Threads"
msgstr "Hilos"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_name
msgid "Title"
msgstr "Título"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "El título no debe estar vacío"

#. module: website_forum
#: code:addons/website_forum/controllers/main.py:322
#: code:addons/website_forum/controllers/main.py:389
#, python-format
msgid "Title should not be empty."
msgstr "El título no debería estar vacío."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_recipient_id
msgid "To"
msgstr "A"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "To improve your chance getting an answer:"
msgstr "Para mejorar su posibilidad de obtener una respuesta:"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking\n"
"    subjective questions where …"
msgstr ""
"Para evitar que su pregunta sea reportada y posiblemente removida, no "
"pregunte algo subjetivo donde..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "Trending"
msgstr "Tendencias"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_post_type
msgid "Type"
msgstr "Tipo"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_content_link
msgid "URL"
msgstr "URL"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post_content_link
msgid "URL of Link Articles"
msgstr "URL de Vínculo de Artículos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "URL to Share"
msgstr "URL a Compartir"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "Unanswered"
msgstr "Sin respuesta"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_unlink_all
msgid "Unlink all comments"
msgstr "Borrar todos los comentarios"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_comment_unlink_own
msgid "Unlink own comments"
msgstr "Borrar sus propios comentarios"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Update"
msgstr "Actualizar"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_write_date
msgid "Update on"
msgstr "Actualizar en"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_write_uid
msgid "Updated by"
msgstr "Actualizado por"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_karma_upvote
msgid "Upvote"
msgstr "Votar a Favor"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "Pregunta votada a favor (1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "Pregunta votada a favor (15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "Pregunta votada a favor (4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "Pregunta votada a favor (6)"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/js/website_tour_forum.js:22
#, fuzzy, python-format
msgid ""
"Use this button to create a new forum like any other document (page, menu, "
"products, event, ...)."
msgstr ""
"Use este menú de <em>'Contenido'</em> para crear un nuevo foro como "
"cualquier otro documento (página, menú, productos, evento, ...)."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_user_id
msgid "User"
msgstr "Usuario"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
msgid "Users"
msgstr "Usuarios"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_question
msgid ""
"Users can answer only once per question. Contributors can edit answers and "
"mark the right ones."
msgstr ""
"Los usuarios pueden responder solamente una vez por pregunta. Los "
"colaboradores pueden editar respuestas y marcar las correctas."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#, fuzzy
msgid "Validate question"
msgstr "Crear una pregunta"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:777
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#, python-format
msgid "View"
msgstr "Ver"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "View Your Badges"
msgstr "Ver Sus Insignias"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_vote
msgid "Vote"
msgstr "Voto"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "Votes"
msgstr "Votos"

#. module: website_forum
#: selection:forum.post,state:0
msgid "Waiting Validation"
msgstr "Esperando Validación"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "Waiting for validation"
msgstr "Esperando por validación"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid ""
"We keep a high level of quality in showcased posts, around 20% of the "
"submited\n"
"            posts will be featured."
msgstr ""
"Nosotros mantenemos un alto nivel de calidad en los mensajes exhibidos, "
"alrededor de 20% de los mensajes enviados serán destacados."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_profile
msgid "Website"
msgstr "Sitio Web"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum_welcome_message
msgid "Welcome Message"
msgstr "Mensaje de Bienvenida"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What kinds of questions can I ask here?"
msgstr "Qué tipo de preguntas puedo hacer aquí?"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my answers?"
msgstr "¿Qué debo evitar en mis respuestas?"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "What should I avoid in my questions?"
msgstr "Qué debo evitar en mis preguntas?"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some\n"
"    points, which are called \"karma points\". These points serve as a "
"rough\n"
"    measure of the community trust to him/her. Various moderation tasks are\n"
"    gradually assigned to the users based on those points."
msgstr ""
"Cuando una pregunta o respuesta es votada positivamente, el usuario que la "
"publicó ganará algunos puntos, que son llamados \"puntos de karma\". Esos "
"puntos sirven como una medida aproximada de la confianza que le tiene la "
"comunidad a ese usuario. Varias tareas de moderación son asignadas "
"gradualmente a los usuarios basadas en esos puntos."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum_allow_link
msgid "When clicking on the post, it redirects to an external link"
msgstr "Haciendo clic en el mensaje, se redirecciona a un enlace externo"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "Why can other people edit my questions/answers?"
msgstr "Por qué otras personas pueden editar mis preguntas/respuestas?"

#. module: website_forum
#: code:addons/website_forum/models/forum.py:141
#, python-format
msgid "You cannot choose %s as default post since the forum does not allow it."
msgstr ""
"No puede escoger %s como mensaje por defecto pues el foro no lo permite."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr ""

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"You should only ask practical, answerable questions based\n"
"    on actual problems that you face. Chatty, open-ended\n"
"    questions diminish the usefulness of this site and push\n"
"    other questions off the front page."
msgstr ""
"Sólo debe hacer preguntas prácticas que puedan responderse sobre la base de "
"los problemas reales que enfrentan. Preguntas sin sentido o demasiado "
"abiertas disminuyen la utilidad de este sitio y empujan a otras preguntas "
"más importantes de los primeros sitios."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "Su Respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_discussion
msgid "Your Discussion Title..."
msgstr "Título de su Tema..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Your Question Title..."
msgstr "El Título de Su Pregunta..."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_reply
msgid "Your Reply"
msgstr "Su Respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "accept any answer"
msgstr "aceptar cualquier respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "and"
msgstr "y"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.close_post
msgid "back to post"
msgstr "volver al mensaje"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "bad examples"
msgstr "malos ejemplos"

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "bronze"
msgstr "bronce"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "by"
msgstr "por"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by activity date"
msgstr "por fecha de actividad"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most answered"
msgstr "por más respondidas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by most voted"
msgstr "por más votadas"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by newest"
msgstr "por más reciente"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "by relevance"
msgstr "por relevancia"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "close any posts"
msgstr "cerrar cualquier publicación"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "contains offensive or malicious remarks"
msgstr "ontiene comentarios ofensivos o maliciosos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any comment"
msgstr "borrar cualquier comentario"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete any question or answer"
msgstr "borrar cualquier pregunta o respuesta"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "delete own comment"
msgstr "borrar sus propios comentarios"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "downvote"
msgstr "votar en contra"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "duplicate post"
msgstr "duplicar mensaje"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_link
msgid "e.g. https://www.odoo.com"
msgstr "ej. https://www.odoo.com"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "edit any post, view offensive flags"
msgstr "editar cualquier mensaje, ver reportes ofensivos"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "cada respuesta es igualmente válida: \"¿Cuál es su favorito ______?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "flag offensive, close own questions"
msgstr "reportar ofensivos, cerrar preguntas propias"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "flagged"
msgstr "reportada"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "follower(s)"
msgstr "seguidor(es)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "por motivo:"

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "gold"
msgstr "oro"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "good examples"
msgstr "buenos ejemplos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "has been closed"
msgstr "ha sido cerrado"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#, fuzzy
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr ""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr ""

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:36
#, python-format
msgid "here"
msgstr "aquí"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to configure or customize Odoo to specific business needs,"
msgstr ""
"cómo configurar o personalizar Odoo a las necesidades específicas del "
"negocio,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to develop modules for your own need,"
msgstr "cómo desarrollar módulos para su propia necesidad,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "how to install Odoo on a specific infrastructure,"
msgstr "cómo instalar Odoo en una infraestructura específica,"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"si su respuesta\n"
"es seleccionada como la más adecuada. Vea lo que puede hacer con el karma"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "inappropriate and unacceptable statements"
msgstr "expresiones inapropiadas e inaceptables"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "insert text link, upload files"
msgstr "insertar enlace de texto, subir archivos"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "insulting and offensive language"
msgstr "lenguaje insultante y ofensivo"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr ""
"es una queja encubierta de pregunta: \"______ apesta, estoy en lo cierto?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "karma"
msgstr "karma"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:34
#, python-format
msgid "karma points"
msgstr "puntos de karma"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "last connection"
msgstr "última conexión"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "location"
msgstr "ubicación"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "member since"
msgstr "miembro desde"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "not a real post"
msgstr "no en un comentario real"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "not relevant or out dated"
msgstr "no relevante o desactualizado"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "off-topic or not relevant"
msgstr "fuera-de-tema o no relevante"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "en"

#. module: website_forum
#. openerp-web
#: code:addons/website_forum/static/src/xml/website_forum_share_templates.xml:29
#, python-format
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"en las redes sociales recibirá respuesta en 5 horas. Preguntas compartidas "
"en dos redes sociales tienen"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "post"
msgstr "mensaje"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "racist and hate speech"
msgstr "discurso racista y de odio"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "received this badge:"
msgstr "recibió esta insignia:"

#. module: website_forum
#: selection:gamification.badge,level:0
msgid "silver"
msgstr "plata"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "spam or advertising"
msgstr "spam o publicidad"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "specific questions about Odoo service offers, etc."
msgstr "preguntas específicas acerca del servicio que ofrece Odoo, etc."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr ""
"no hay ningún problema real que haya que resolver: \"Estoy ansioso por ver "
"si otras personas se sienten como yo.\""

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "threatening language"
msgstr "lenguaje amenazante"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "time"
msgstr "vez"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "times"
msgstr "veces"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "too localized"
msgstr "muy localizada"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "too subjective and argumentative"
msgstr "muy subjetivo y argumentativo"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "upvote, add comments"
msgstr "votar a favor, comentar"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "user"
msgstr "usuario"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.badge_user
msgid "users"
msgstr "usuarios"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "views"
msgstr "vistas"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "violent language"
msgstr "lenguaje violento"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "votes"
msgstr "votos"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "waiting for validation"
msgstr "esperando validación"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr ""
"se nos está pidiendo realizar una pregunta hipotética de composición "
"abierta: \"¿Qué pasa si ______?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_detail_full
msgid "website"
msgstr "sitio web"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid "what's the best way to use Odoo for a specific business need,"
msgstr ""
"cual es la mejor manera de usar Odoo para necesidades específicas de negocio,"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"su respuesta se proporciona junto con la pregunta, y se puede esperar más "
"respuestas: \"Yo uso _______ para ______, ¿qué uso le doy?\""

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "your biography can be seen as tooltip"
msgstr "su biografía puede ser vista como un tooltip"

#~ msgid ""
#~ "\n"
#~ "<p>\n"
#~ "    Hello ${object.name},\n"
#~ "</p>\n"
#~ "<p>\n"
#~ "    You have been invited to validate your email in order to get access "
#~ "to \"${object.company_id.name}\" Q/A Forums.\n"
#~ "</p>\n"
#~ "<p>\n"
#~ "    To validate your email, please click on the following link:\n"
#~ "</p>\n"
#~ "<ul>\n"
#~ "    <li><a href=\"${ctx.get('token_url')}\">Validate my account for "
#~ "\"${object.company_id.name}\" Q/A Forums</a></li>\n"
#~ "</ul>\n"
#~ "<p>\n"
#~ "    Thanks,\n"
#~ "</p>\n"
#~ "<pre>\n"
#~ "--\n"
#~ "${object.company_id.name or ''}\n"
#~ "${object.company_id.email or ''}\n"
#~ "${object.company_id.phone or ''}\n"
#~ "</pre>"
#~ msgstr ""
#~ "\n"
#~ "<p>\n"
#~ "    Hola ${object.name},\n"
#~ "</p>\n"
#~ "<p>\n"
#~ "    Ha sido invitado a validar su correo electrónico para poder obtener "
#~ "acceso a los foros de preguntas y respuestas de \"${object.company_id."
#~ "name}\".\n"
#~ "</p>\n"
#~ "<p>\n"
#~ "    Para validar su correo electrónico, pulse por favor en el siguiente "
#~ "enlace:\n"
#~ "</p>\n"
#~ "<ul>\n"
#~ "    <li><a href=\"${ctx.get('token_url')}\">Validar mi cuenta para los "
#~ "foros de P/R de \"${object.company_id.name}\"</a></li>\n"
#~ "</ul>\n"
#~ "<p>\n"
#~ "    Gracias,\n"
#~ "</p>\n"
#~ "<pre>\n"
#~ "--\n"
#~ "${object.company_id.name or ''}\n"
#~ "${object.company_id.email or ''}\n"
#~ "${object.company_id.phone or ''}\n"
#~ "</pre>"

#~ msgid ""
#~ "<p>A new answer for <i>%s</i> has been posted. <a href=\"%s/forum/%s/"
#~ "question/%s\">Click here to access the post.</a></p>"
#~ msgstr ""
#~ "<p>Se ha publicado una nueva respuesta para <i>%s</i>. <a href=\"%s/forum/"
#~ "%s/question/%s\">Clic aquí para verla.</a></p>"

#~ msgid ""
#~ "<p>A new question <i>%s</i> has been asked on %s. <a href=\"%s/forum/%s/"
#~ "question/%s\">Click here to access the question.</a></p>"
#~ msgstr ""
#~ "<p>Se ha añadido una nueva pregunta <i>%s</i> en %s. <a href=\"%s/forum/"
#~ "%s/question/%s\">Click aquí para verla.</a></p>"

#~ msgid "Action Needed"
#~ msgstr "Acción Requerida"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del último mensaje publicado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Asociados)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, los nuevos mensajes requerirán su atención."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del Último Mensaje"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de mensajes y de comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"

#~ msgid "Post type in \"Default post\" must be activated"
#~ msgstr ""
#~ "Tipo de mensaje en \"Publicación predeterminada\" debe estar activado"

#~ msgid "Send a message to the group"
#~ msgstr "Enviar un mensaje al grupo"

#~ msgid "Unread Messages"
#~ msgstr "Mensajes sin Leer"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes no Leídos"

#~ msgid "Website Messages"
#~ msgstr "Mensajes del Sitio Web"

#~ msgid "Website communication history"
#~ msgstr "Historial de comunicación del sitio web"

#~ msgid "Website meta description"
#~ msgstr "Meta descripción del sitio web"

#~ msgid "Website meta keywords"
#~ msgstr "Meta palabras clave del sitio web"

#~ msgid "Website meta title"
#~ msgstr "Meta título del sitio web"
