<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_pt" model="res.partner">
        <field name="name">PT Company</field>
        <field name="vat">PT552091561</field>
        <field name="street">25 Avenida da Liberdade</field>
        <field name="city">Lisboa</field>
        <field name="country_id" ref="base.pt"/>

        <field name="zip">1000-001</field>
        <field name="phone">+35162871912</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ptexample.com</field>
    </record>

    <record id="demo_company_pt" model="res.company">
        <field name="name">PT Company</field>
        <field name="company_registry">123456</field>
        <field name="partner_id" ref="partner_demo_company_pt"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_pt')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_pt.demo_company_pt'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_pt.pt_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_pt.demo_company_pt')"/>
    </function>
</odoo>
