# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_editor
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# NoaFarkash, 2022
# Ha <PERSON>tem <<EMAIL>>, 2022
# <PERSON>, 2023
# MichaelHadar, 2023
# yael terner, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-10 08:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: yael terner, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Original)"
msgstr "%dpx (מקור)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%dpx (Suggested)"
msgstr "%dpx (מוצע)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "%spx"
msgstr "%spx"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid ""
"'Alt tag' specifies an alternate text for an image, if the image cannot be "
"displayed (slow connection, missing image, screen reader ...)."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "'Title tag' is shown as a tooltip when you hover the picture."
msgstr "'תגית כותרת' מופיעה כחלונית כשמרחפים מעל התמונה עם העכבר."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(ALT Tag)"
msgstr "(תווית חלופית)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(TITLE Tag)"
msgstr "(תווית כותרת)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "(URL or Embed)"
msgstr "(קישור URL או מוטמע)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "100%"
msgstr "100%"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "1977"
msgstr "1977"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "1x"
msgstr "1x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "25"
msgstr "25"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "25%"
msgstr "25%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "2x"
msgstr "2x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "3x"
msgstr "3x"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "4x"
msgstr "4x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "50%"
msgstr "50%"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "5x"
msgstr "5x"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "90"
msgstr "90"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"
msgstr "<span class=\"flex-grow-0 ml-1 text-white-50\">%</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"flex-grow-0 ml-1 text-white-50\">deg</span>"
msgstr "<span class=\"flex-grow-0 ml-1 text-white-50\">מעלות</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2 ml-3\">Y</span>"
msgstr "<span class=\"mr-2 ml-3\">Y</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "<span class=\"mr-2\">X</span>"
msgstr "<span class=\"mr-2\">X</span>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Blocks</span>"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "<span>Style</span>"
msgstr "<span>סגנון</span>"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"A server error occured. Please check you correctly signed in and that the "
"file you are saving is correctly formatted."
msgstr ""
"אירעה שגיאת שרת. אנא בדוק שהתחברת כראוי וכי הקובץ שאתה שומר מעוצב כהלכה."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Above"
msgstr "מעל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Accepts"
msgstr "מתקבל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Add"
msgstr "הוסף"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Column"
msgstr "הוסף עמודה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add Row"
msgstr "הוסף שורה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add URL"
msgstr "הוסף כתובת אתר אינטרנט"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a blockquote section."
msgstr "הוסף קטע ציטוט."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a button."
msgstr "הוספת כפתור."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a code section."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column left"
msgstr "הוסף עמודה לשמאל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a column right"
msgstr "הוסף עמודה לימין"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Add a link."
msgstr "הוסף קישור."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row above"
msgstr "הוסף שורה מעל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Add a row below"
msgstr "הוסף שורה מתחת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Add document"
msgstr "הוסף מסמך"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Aden"
msgstr "עדן"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Airy & Zigs"
msgstr "ארי וזקס"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Center"
msgstr "יישור למרכז"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Left"
msgstr "יישור לשמאל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Align Right"
msgstr "יישור לימין"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alignment"
msgstr "יישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All"
msgstr "הכל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "All SCSS Files"
msgstr "קבצי All SCSS"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "All images have been loaded"
msgstr "כל התמונות נטענו"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Alt tag"
msgstr "תווית חלופית"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Angle"
msgstr "זווית"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Animated"
msgstr "מונפש"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Apply"
msgstr "החל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Are you sure you want to delete the snippet: %s ?"
msgstr "בטוח שברצונך למחור את הקטע: %s?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "Are you sure you want to delete this file ?"
msgstr "האם אתה בטוח שברצונך למחוק קובץ זה?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Aspect Ratio"
msgstr "יחס גובה-רוחב"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_assets
msgid "Assets Utils"
msgstr "נכסים שימושיים"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_attachment
msgid "Attachment"
msgstr "קובץ מצורף"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__local_url
msgid "Attachment URL"
msgstr "URL כתובת הקובץ להעלאה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Auto"
msgstr "אוטומטי"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Autoplay"
msgstr "ניגון אוטומטי"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background"
msgstr "רקע"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Background Color"
msgstr "צבע רקע"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Background Position"
msgstr "מיקום רקע"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Basic blocks"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Basics"
msgstr "בסיסי"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Below"
msgstr "למטה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Big section heading."
msgstr "כותרת גדולה"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blobs"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Block"
msgstr "חסום"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Blocks & Rainy"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Blur"
msgstr "טשטוש"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Bold"
msgstr "מודגש"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Color"
msgstr "צבע גבול"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Style"
msgstr "סגנון גבול"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Border Width"
msgstr "רוחב גבול"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brannan"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Brightness"
msgstr "בהירות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Bulleted list"
msgstr "רשימת תבליטים"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Button"
msgstr "כפתור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Cancel"
msgstr "בטל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Careful !"
msgstr "זהירות !"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Center"
msgstr "מרכז"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/alt_dialog.js:0
#, python-format
msgid "Change media description and tooltip"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Checklist"
msgstr "רשימת תיוג"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Choose a record..."
msgstr "בחירת רשומה..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Close"
msgstr "סגור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Code"
msgstr "קוד"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_color_widget
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Color"
msgstr "צבע"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Column"
msgstr "עמודה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Common colors"
msgstr "צבעים נפוצים"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirm"
msgstr "אשר"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Confirmation"
msgstr "אישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Content conflict"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Contrast"
msgstr "ניגודיות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy Link"
msgstr "העתק קישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Copy-paste your URL or embed code here"
msgstr "כאן עושים העתקה-הדבקה של קישור URL או של קוד הטמעה "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Could not install module <strong>%s</strong>"
msgstr "לא הצלחנו להתקין את המודול <strong>%s</strong>"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Cover"
msgstr "שער"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Create"
msgstr "צור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a list with numbering."
msgstr "יצירת רשימה ממוספרת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create a simple bulleted list."
msgstr "יצירת רשימת תבליטים פשוטה."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Create an URL."
msgstr "יצירת קישור"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Crop Image"
msgstr "חתוך תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Custom"
msgstr "מותאם"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Custom %s"
msgstr "%s מותאם"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dailymotion"
msgstr "Dailymotion"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dashed"
msgstr "מקווקו"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default"
msgstr "ברירת מחדל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Default + Rounded"
msgstr "ברירת מחדל + מעוגל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Define a custom gradient"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Delete %s"
msgstr "מחיקת %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Delete current table"
msgstr "מחיקת טבלא נוכחית"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Description"
msgstr "תיאור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Discard record"
msgstr "ביטול הרשומה"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Do you want to install the %s App?"
msgstr "להתקין את היישום %s?"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Document"
msgstr "מסמך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Dotted"
msgstr "מנוקד"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Double"
msgstr "כפול"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Double-click to edit"
msgstr "לחץ פעמיים כדי לערוך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Duplicate Container"
msgstr "שכפול בלוק"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Dynamic Colors"
msgstr "צבעים דינמיים"

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "ERROR: couldn't get download urls from media library."
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "EarlyBird"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Edit Link"
msgstr "עריכת קישור"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Edit image"
msgstr "עריכת תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Edit media description"
msgstr "עריכת תיאור מדיה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Image"
msgstr "הטמעת תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed Youtube Video"
msgstr "הטמעת וידאו מיוטיוב"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the image in the document."
msgstr "הטמעת הדימוי במסמך."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Embed the youtube video in the document."
msgstr "הטמעת הוידאו מיוטיוב במסמך."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Empty quote"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Expected "
msgstr "צפוי"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the closest side"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest corner"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Extend to the farthest side"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File could not be saved"
msgstr "בעיה בשמירת קובץ."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "File has been uploaded"
msgstr "הקובץ הועלה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill"
msgstr "למלא"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Fill + Rounded"
msgstr "מלא ומעוגל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Filter"
msgstr "מסנן"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "First Panel"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flat"
msgstr "שטוח"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "Flexible"
msgstr "גמישה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Horizontal"
msgstr "היפוך אופקי"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Flip Vertical"
msgstr "היפוך אנכי"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Floating shapes"
msgstr "צורות מרחפות"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Floats"
msgstr "מרחפת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font Color"
msgstr "צבע גופן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Font size"
msgstr "גודל גופן(פונט)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "For technical reasons, this block cannot be dropped here"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Format"
msgstr "תבנית"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Fullscreen"
msgstr "מסך מלא"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"Get the perfect image by searching in our library of copyright free photos "
"and illustrations."
msgstr ""
"קבל את התמונה המושלמת על ידי חיפוש בספרייה שלנו של תמונות ואיורים ללא זכויות"
" יוצרים."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Gradient"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_http
msgid "HTTP Routing"
msgstr "ניתוב HTTP"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 1"
msgstr "כותרת עליונה 1"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 2"
msgstr "כותרת עליונה 2"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 3"
msgstr "כותרת עליונה 3"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 4"
msgstr "כותרת עליונה 4"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 5"
msgstr "כותרת עליונה 5"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Header 6"
msgstr "כותרת עליונה 6"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 1"
msgstr "כותרת בגודל גדול"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 2"
msgstr "כותרת בגודל בינוני"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 3"
msgstr "כותרת בגודל קטן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 4"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 5"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Heading 6"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Dailymotion logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide Youtube logo"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide fullscreen button"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide player controls"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Hide sharing button"
msgstr "הסתר את כפתור השיתוף"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/backend/field_html.js:0
#, python-format
msgid "Html"
msgstr "Html"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon"
msgstr "סמל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Icon Formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 1x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 2x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 3x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 4x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Icon size 5x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"אם תבטל את העריכות הנוכחיות, כל השינויים שלא נשמרו יאבדו. אתה יכול לבטל כדי "
"לחזור למצב עריכה."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr ""
"אם תאפס קובץ זה, כל ההתאמות האישיות שלך יאבדו מכיוון שהוא יוחזר לקובץ ברירת "
"המחדל."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Illustrations"
msgstr "איורים"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "Image"
msgstr "תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Image Formatting"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_height
msgid "Image Height"
msgstr "גובה תמונה"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_src
msgid "Image Src"
msgstr "מקור תמונה"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__image_width
msgid "Image Width"
msgstr "רוחב תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Image padding"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Inkwell"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Inline Text"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert a table."
msgstr "הכנס לטבלה."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert a video."
msgstr "הכנס סרטון"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Insert an horizontal rule separator."
msgstr "הכנס קו מפריד אופקי."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Insert an image."
msgstr "הכנס תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert media"
msgstr "הוספת מדיה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert or edit link"
msgstr "הוספה או עריכת קישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Insert table"
msgstr "הכנס טבלה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install"
msgstr "להתקין"

#. module: web_editor
#: code:addons/web_editor/models/ir_ui_view.py:0
#, python-format
msgid "Invalid field value for %s: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install %s"
msgstr "התקנת %s"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Install in progress"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invisible Elements"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Item"
msgstr "פריט"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "JS"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "JS file: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Large"
msgstr "גדול"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Left"
msgstr "שמאל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Linear"
msgstr "ליניארי"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Lines"
msgstr "שורות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Link"
msgstr "קישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Link Label"
msgstr "קשר תווית"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "Link copied to clipboard."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Link to"
msgstr "קישור אל"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "List"
msgstr "רשימה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Load more..."
msgstr "טען עוד..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Loop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Main Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Maven"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Media"
msgstr "מדיה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Medias"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Medium"
msgstr "בינוני"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Medium section heading."
msgstr "כותרת בגודל בינוני"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "More info about this app."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "My Images"
msgstr "הדימויים שלי"

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_web_editor_converter_test_sub__name
msgid "Name"
msgstr "שם"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Navigation"
msgstr "ניווט"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No"
msgstr "לא"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "No URL specified"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No documents found."
msgstr "לא נמצאו מסמכים."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "No images found."
msgstr "לא נמצאו תמונות."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "No location to drop in"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "No more records"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/powerbox/Powerbox.js:0
#, python-format
msgid "No results"
msgstr "אין תוצאות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "None"
msgstr "אף אחד"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
#, python-format
msgid "Normal"
msgstr "נורמלי "

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Numbered list"
msgstr "רשימה ממוספרת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Page SCSS Files"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Only Views"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in a new tab"
msgstr "פתיחה בלשונית חדשה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Open in new window"
msgstr "פתיחה בחלון חדש"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Optimized"
msgstr ""

#. module: web_editor
#: model:ir.model.fields,field_description:web_editor.field_ir_attachment__original_id
msgid "Original (unoptimized, unresized) attachment"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Origins"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline"
msgstr "קווי מתאר"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Outline + Rounded"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Padding"
msgstr "מרווח"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paragraph block."
msgstr "בלוק פסקה."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Paste as URL"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Patterns"
msgstr "תבניות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Pictogram"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Position"
msgstr "מעמד"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Preview"
msgstr "תצוגה מקדימה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Primary"
msgstr "ראשי"

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__c
msgid "Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__a
msgid "Qu'il n'est pas arrivé à Toronto"
msgstr ""

#. module: web_editor
#: model:ir.model.fields.selection,name:web_editor.selection__web_editor_converter_test__selection_str__b
msgid "Qu'il était supposé arriver à Toronto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Quality"
msgstr "איכות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Quote"
msgstr "ציטוט"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field
msgid "Qweb Field"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "איש קשר בשדה Qweb"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_date
msgid "Qweb Field Date"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_datetime
msgid "Qweb Field Datetime"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_duration
msgid "Qweb Field Duration"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_float
msgid "Qweb Field Float"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_image
msgid "Qweb Field Image"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_integer
msgid "Qweb Field Integer"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_many2one
msgid "Qweb Field Many to One"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_monetary
msgid "Qweb Field Monetary"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_relative
msgid "Qweb Field Relative"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_selection
msgid "Qweb Field Selection"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_text
msgid "Qweb Field Text"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_qweb_field_qweb
msgid "Qweb Field qweb"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Radial"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "Readonly field"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "Redirect the user elsewhere when he clicks on the media."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove (DELETE)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Remove Block"
msgstr "הסרת בלוק"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove Current"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Remove Link"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
msgid "Remove Selected Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current column"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove current row"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove format"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Remove link"
msgstr "הסרת קישור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Rename %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Repeat pattern"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Replace"
msgstr "החלף"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Replace media"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Reset"
msgstr "אפס"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Reset Image"
msgstr "אפס תמונה"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset crop"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Reset transformation"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "עדיין לא תומך באיפוס תצוגות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Auto"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Full"
msgstr "שינוי גודל מלא"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Half"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Resize Quarter"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Right"
msgstr "ימין"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Left"
msgstr "סובב שמאלה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Rotate Right"
msgstr "סובב ימינה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Row"
msgstr "שורה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "SCSS (CSS)"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "SCSS file: %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Saturation"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/dialog.js:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
#, python-format
msgid "Save"
msgstr "שמור"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Save and Install"
msgstr "שמירה והתקנה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Save and Reload"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Save record"
msgstr "שמירת רשומה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search"
msgstr "חיפוש"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a document"
msgstr "חפש מסמך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search a pictogram"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Search an image"
msgstr "חפש תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search for records..."
msgstr "חפש רשומות..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search more..."
msgstr "חפש עוד..."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Search to show more records"
msgstr "חפש כדי להראות עוד רשומות"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippets
msgid "Search..."
msgstr "חיפוש…"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link.js:0
#, python-format
msgid "Secondary"
msgstr "משני"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media_dialog.js:0
#, python-format
msgid "Select a Media"
msgstr "בחירת מדיה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Select a block on your page to style it."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Separator"
msgstr "מפריד"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Sepia"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Server error"
msgstr "שגיאת שרת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shadow"
msgstr "צללית"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Shape"
msgstr "צורה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Circle"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Rounded"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Shape: Thumbnail"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Show optimized images"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Size"
msgstr "גודל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 1x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 2x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 3x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 4x"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Size 5x"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Small"
msgstr "קטן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Small section heading."
msgstr "כותרת בגודל קטן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Solid"
msgstr "מוצק"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Solids"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Specials"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Style"
msgstr "סגנון"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Suggestions"
msgstr "הצעות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch direction"
msgstr "החלפת כיוון"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Switch the text's direction."
msgstr "החלף את כיוון הטקסט."

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Table"
msgstr "שולחן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table Options"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Table tools"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Template ID: %s"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Text"
msgstr "טקסט"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text align"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Text style"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL does not seem to work."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "The URL seems valid."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"The image could not be deleted because it is used in the\n"
"               following pages or views:"
msgstr ""
"לא ניתן למחוק את התמונה כי היא נמצאת בשימוש\n"
"               הדפים והתצוגות הבאים:"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url does not reference any supported video"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/media.js:0
#, python-format
msgid "The provided url is not valid"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"The version from the database will be used.\n"
"                    If you need to keep your changes, copy the content below and edit the new document."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Theme"
msgstr "ערכת נושא"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Theme colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "There is a conflict between your version and the one in the database."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/link_popover_widget.js:0
#, python-format
msgid "This URL is invalid. Preview couldn't be updated."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "This block is outdated"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#, python-format
msgid "This document is not saved!"
msgstr "המסמך לא נשמר!"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is a public view attachment."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "This file is attached to the current record."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid "This image is an external image"
msgstr "זאת תמונה ממקור חיצוני"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/wysiwyg/widgets/image_crop_widget.js:0
#, python-format
msgid ""
"This type of image is not supported for cropping.<br/>If you want to crop "
"it, please first download it from the original source and upload it in Odoo."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid "Title"
msgstr "שם"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Title tag"
msgstr "תווית כותרת"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To apply this change, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/snippets.xml:0
#, python-format
msgid ""
"To make changes, drop this block and use the new options in the last "
"version."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"To save a snippet, we need to save all your previous modifications and "
"reload the page."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "To-do"
msgstr "מטלה"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Toaster"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle bold"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle checklist"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle icon spin"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle italic"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle ordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle strikethrough"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle underline"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Toggle unordered list"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#, python-format
msgid "Tooltip"
msgstr "הסבר"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Track tasks with a checklist."
msgstr "עקוב אחר משימות בעזרת צ'ק ליסט."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Transform the picture"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr "שנה את התמונה (לחץ פעמיים כדי לאפס את השינוי)"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/backend.xml:0
#: code:addons/web_editor/static/src/xml/backend.xml:0
#, python-format
msgid "Translate"
msgstr "תרגם"

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_translation
msgid "Translation"
msgstr "תרגום"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg_colorpicker.xml:0
#, python-format
msgid "Transparent colors"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.colorpicker
#, python-format
msgid "Type"
msgstr "סוג"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/odoo-editor/src/OdooEditor.js:0
#, python-format
msgid "Type \"/\" for commands"
msgstr "הקלד \"/\" לשימוש בפקודות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "URL or Email"
msgstr "כתובת אתר או דוא\"ל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
msgid "Unalign"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/common/ace.js:0
#, python-format
msgid "Unexpected "
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload a document"
msgstr "העלה מסמך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Upload an image"
msgstr "העלאת תמונה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Uploaded image's format is not supported. Try with:"
msgstr ""

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "Uploaded image's format is not supported. Try with: %s"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Valencia"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#: code:addons/web_editor/static/src/js/wysiwyg/wysiwyg.js:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video"
msgstr "וידאו"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Video Formatting"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Video code"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Videos are muted when autoplay is enabled"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_ir_ui_view
msgid "View"
msgstr "תצוגה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "Views and Assets bundles"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Walden"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid ""
"Warning: after closing this dialog, the version you were working on will be "
"discarded and will never be available anymore."
msgstr ""
"אזהרה: לאחר סגירת דו-שיח זה, הגרסה שעבדת עליה תימחק ולעולם לא תהיה זמינה "
"יותר."

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "Wavy"
msgstr "גלי"

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test_sub
msgid "Web Editor Converter Subtest"
msgstr ""

#. module: web_editor
#: model:ir.model,name:web_editor.model_web_editor_converter_test
msgid "Web Editor Converter Test"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Width"
msgstr "רוחב"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "XL"
msgstr "XL"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/ace.xml:0
#: code:addons/web_editor/static/src/xml/ace.xml:0
#, python-format
msgid "XML (HTML)"
msgstr "XML (HTML)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "Xpro"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Yes"
msgstr "כן"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload documents with the button located in the top left of the "
"screen."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid ""
"You can upload images with the button located in the top left of the screen."
msgstr ""
"ניתן להעלות תמונות באמצעות הכפתור הממוקם בפינה השמאלית העליונה של המסך."

#. module: web_editor
#: code:addons/web_editor/controllers/main.py:0
#, python-format
msgid "You need to specify either data or url to create an attachment."
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youku"
msgstr "Youku"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Your URL"
msgstr "הקישור שלך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Youtube"
msgstr "Youtube"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom In"
msgstr "זום פנימה"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "Zoom Out"
msgstr "להקטין את התצוגה(זום אאוט)"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "add"
msgstr "הוסף"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "and"
msgstr "וגם"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "auto"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "darken"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/editor.xml:0
#, python-format
msgid "default"
msgstr "ברירת מחדל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "exclusion"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/logo.png"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "https://www.odoo.com/mydocument"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "lighten"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "multiply"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "overlay"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "px"
msgstr "פיקסל"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_image_optimization_widgets
msgid "screen"
msgstr "מסך"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "videos"
msgstr "וידאות"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "www.example.com"
msgstr "www.example.com"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Autoconvert to relative link"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Border"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Color filter"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Colors"
msgstr "⌙ צבעים"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Fill Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Flip"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Height"
msgstr "⌙ גובה"

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Image"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Link Label"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Main Color"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Open in new window"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Position"
msgstr "⌙ מיקום"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
#, python-format
msgid "⌙ Shape"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Size"
msgstr ""

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Style"
msgstr "⌙ סגנון"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/wysiwyg.xml:0
#, python-format
msgid "⌙ Text Color"
msgstr ""

#. module: web_editor
#: model_terms:ir.ui.view,arch_db:web_editor.snippet_options_background_options
msgid "⌙ Width"
msgstr "⌙ רוחב"

#. module: web_editor
#. openerp-web
#: code:addons/web_editor/static/src/xml/image_link_tools.xml:0
#, python-format
msgid "⌙ Your URL"
msgstr ""
