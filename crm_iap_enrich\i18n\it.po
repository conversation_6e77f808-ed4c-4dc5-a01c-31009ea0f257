# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <andronic<PERSON>uan<PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> Fasterling-Nesselbosch, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: Friederike Fasterling-Nesselbosch, 2022\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""
"<span>Non è stata trovata nessuna informazione sull'azienda sulla base della"
" mail fornita, o l'email è quella di un email provider. Non sono stati "
"consumati credit.</span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""
"<span>Non è stato possibile effettuare l'arricchimento perché l'indirizzo "
"e-mail non sembra valido.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Consenti arricchimento manuale"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: arricchimento contatti (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Arricchisci"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "Arricchire il lead con i dati dell'azienda"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "Arricchire l'opportunità con i dati dell'azienda"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "Arricchimento effettuato"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "IAP account"
msgstr "account acquisti integrati (IAP)"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "Arricchimento del lead (basato sull'indirizzo e-mail)"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Lead enriched (based on email address)"
msgstr "Lead arricchito (in base all'indirizzo e-mail)"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Lead enriched based on email address"
msgstr "Contatto arricchito in base all'indirizzo e-mail"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Contatto/Opportunità"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""
"Indica se su questo contatto è stato eseguito il servizio acquisti in-app "
"per l'arricchimento. "

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Your balance for Lead Enrichment is insufficient. Please go to your"
msgstr "Saldo non sufficiente per l'arricchimento contatti. Andare nell'"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "to buy credits."
msgstr "per acquistare crediti."
