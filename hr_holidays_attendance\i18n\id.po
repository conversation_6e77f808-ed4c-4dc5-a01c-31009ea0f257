# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON>y Useful <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Available"
msgstr "Tersedia"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "Hitung Jam Tambahan"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr "Kurangi Jam Tambahan"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "Abaikan"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "Jam Tambaha"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr "Jam Tambahan Tersisa"

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "HOURS AVAILABLE"
msgstr ""

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "Hours"
msgstr "Jam"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__hr_attendance_overtime
msgid "Hr Attendance Overtime"
msgstr "Absensi Lembur HR"

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "Permintaan Alokasi Baru"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""
"Setelah cuti untuk tipe ini disetujui, jam tambahan di absensi akan "
"dikurangi."

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr "Overtime Deductible"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr "Permintaan Lembur"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "Simpan"

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""
"Karyawan tidak memiliki jam tambahan yang mencukupi untuk memperpanjang "
"alokasi ini."

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""
"Karyawan ini tidak memiliki jam tambahan untuk memperpanjang cuti ini."

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough extra hours to request this allocation."
msgstr "Karyawan tidak memiliki jam tambahan untuk meminta alokasi ini."

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "The employee does not have enough extra hours to request this leave."
msgstr "Karyawan tidak memiliki jam tambahan untuk meminta cuti ini."

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
#, python-format
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""
"Karyawan tidak memiliki jam lembur yang mencukupi untuk meminta cuti ini."

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "Cuti"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "Alokasi Cuti"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "Tipe Cuti"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "Jam Lembur Total"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "Users"
msgstr "Pengguna"

#. module: hr_holidays_attendance
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
#, python-format
msgid "You do not have enough extra hours to request this leave"
msgstr ""
"Anda tidak memiliki jam tambahan yang mencukupi untuk meminta cuti ini"

#. module: hr_holidays_attendance
#. openerp-web
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
#, python-format
msgid "requires_allocation"
msgstr ""
