<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <!-- MEMBERSHIP -->

        <!-- MEMBERSHIP/MEMBERSHIP PRODUCTS -->

        <record model="ir.ui.view" id="membership_product_search_form_view">
            <field name="name">membership.product.search.form</field>
            <field name="model">product.template</field>
            <field name="priority">50</field>
            <field name="arch" type="xml">
                <search string="Membership Products">
                    <field name="name" string="Membership Product"/>
                    <filter string="Inactive" name="inactive" domain="[('active','=',False)]"/>
                    <field name="categ_id" operator="child_of"/>
                    <group  expand='0' string='Group by...'>
                        <filter string='Category' name="category" domain="[]" context="{'group_by' : 'categ_id'}"/>
                        <filter string='Start Date' name="from_month" domain="[]" context="{'group_by' : 'membership_date_from'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record model="ir.ui.view" id="membership_products_tree">
            <field name="name">Membership products</field>
            <field name="model">product.template</field>
            <field name="priority">50</field>
            <field name="arch" type="xml">
                <tree string="Membership products">
                    <field name="name"/>
                    <field name="membership_date_from"/>
                    <field name="membership_date_to"/>
                    <field name="list_price" string="Membership Fee"/>
                    <field name="categ_id" invisible="1"/>
                    <field name="uom_id" invisible="1"/>
                    <field name="type" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="membership_products_kanban" model="ir.ui.view">
            <field name="name">product.template.kanban</field>
            <field name="model">product.template</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="membership_date_from"/>
                    <field name="membership_date_to"/>
                    <field name="list_price"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title"><span class="mt4"><field name="name"/></span></strong>
                                    </div>
                                    <span class="badge badge-pill"><i class="fa fa-money" role="img" aria-label="Price" title="Price"/> <field name="list_price"/></span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <i class="fa fa-clock-o" role="img" aria-label="Period" title="Period"></i><strong> From: </strong><field name="membership_date_from"/><strong> To:</strong> <field name="membership_date_to"/>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.ui.view" id="membership_products_form">
            <field name="name">Membership Products</field>
            <field name="model">product.template</field>
            <field name="priority">50</field>
            <field name="arch" type="xml">
                <form string="Membership products">
                    <sheet>
                        <label for="name" string="Product Name"/>
                        <h1>
                            <field name="name"/>
                        </h1>
                        <group>
                            <group name="product_details">
                                <field name="default_code"/>
                                <field name="categ_id"/>
                                <field name="membership" invisible="1"/>
                                <field name="company_id"
                                    groups="base.group_multi_company"
                                    options="{'no_create': True}"/>
                                <field name="active" widget="boolean_toggle"/>
                            </group>
                            <group name="membership_details">
                                <label for="membership_date_from" string="Membership Duration"/>
                                <div class="o_row">
                                    <field name="membership_date_from" required="1"/> -
                                    <field name="membership_date_to" required="1"/>
                                </div>
                                <field name="list_price" string="Membership Fee"/>
                                <field
                                    name="property_account_income_id"/>
                                <field name="taxes_id" widget="many2many_tags" string="Taxes"/>
                            </group>
                        </group>
                        <label for="description"/>
                        <field colspan="4" name="description" placeholder="Add a description..."/>
                        <label for="description_sale"/>
                        <field colspan="4" name="description_sale" placeholder="This note will be displayed on quotations..."/>
                    </sheet>
                 </form>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_membership_products">
            <field name="name">Membership Products</field>
            <field name="res_model">product.template</field>
            <field name="domain">[('membership','=',True), ('type', '=', 'service')]</field>
            <field name="context">{'membership':True, 'type':'service', 'default_membership': True, 'default_detailed_type': 'service'}</field>
            <field name="search_view_id" ref="membership_product_search_form_view"/>
        </record>

        <record model="ir.actions.act_window.view" id="action_membership_product_view_tree">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="membership_products_tree"/>
            <field name="act_window_id" ref="action_membership_products"/>
        </record>

        <record model="ir.actions.act_window.view" id="action_membership_product_view_form">
            <field name="sequence" eval="2"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="membership_products_form"/>
            <field name="act_window_id" ref="action_membership_products"/>
        </record>

        <record model="ir.actions.act_window.view" id="action_membership_product_view_kanban">
            <field name="sequence" eval="3"/>
            <field name="view_mode">kanban</field>
            <field name="view_id" ref="membership_products_kanban"/>
            <field name="act_window_id" ref="action_membership_products"/>
        </record>

        <menuitem name="Members" id="menu_association" sequence="15" web_icon="membership,static/description/icon.png"/>
        <menuitem name="Configuration" id="menu_marketing_config_association"
            parent="menu_association" sequence="100"/>
        <menuitem name="Membership Products" id="menu_membership_products" parent="menu_marketing_config_association"
            action="action_membership_products"/>

</odoo>
