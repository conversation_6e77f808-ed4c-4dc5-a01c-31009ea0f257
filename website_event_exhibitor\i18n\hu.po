# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_exhibitor
# 
# Translators:
# <PERSON>, 2021
# krn<PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>ib<PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# sixsix six, 2022
# <PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2023
# Szabolcs Rádi, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Szabolcs Rádi, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid ""
")\n"
"                    to meet them !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>Nem közzétett"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Country"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Level"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<small class=\"badge badge-danger\">Unpublished</small>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_search_tag
msgid "<span class=\"btn border-0 py-1\">×</span>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<span class=\"h5 mb-0 pt-0 pt-md-3 pb-0 pb-md-2\">Other exhibitors</span>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "About"
msgstr "Névjegy"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "Action Needed"
msgstr "Művelet szükséges"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__active
msgid "Active"
msgstr "Aktív"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_ids
msgid "Activities"
msgstr "Tevékenységek"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tevékenység kivétel dekoráció"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_state
msgid "Activity State"
msgstr "Tevékenység állapota"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tevékenység típus ikon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "Add some exhibitors to get started !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr "Összes ország"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "All Levels"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Archived"
msgstr "Archivált"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "As a team, we are happy to contribute to this event."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Attendees will be able to join to meet"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Available from"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "Bronz"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr "Publikálhat"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Catchy marketing sentence for promote"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "Come back between"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Come see us live, we hope to meet you !"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Connect"
msgstr "Bejelentkezés"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr "Ország"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr "Országzászló"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid "Create a Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr "Leírás"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Discover more"
msgstr "Fedezzen fel még többet!"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_name
msgid "Display Name"
msgstr "Név megjelenítése"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Display exhibitors on website"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Display in footer"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Email"
msgstr "E-mail"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model:ir.model,name:website_event_exhibitor.model_event_event
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Event"
msgstr "Esemény"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_tree
msgid "Event Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_form
msgid "Event Sponsor Levels"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Event Sponsors"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_type
msgid "Event Template"
msgstr "Esemény sablon"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__exhibitor
msgid "Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#, python-format
msgid "Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_type_view_form
msgid "Exhibitors Menu Item"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikon pld: fa-tasks"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "Arany"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Group By"
msgstr "Csoportosítás"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Happy to be Sponsor"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__id
msgid "ID"
msgstr "Azonosító"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kivétel tevékenységet jelző ikon"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_unread
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_128
msgid "Image 128"
msgstr "128-as kép"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_256
msgid "Image 256"
msgstr "256-os kép"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_image_url
msgid "Image URL"
msgstr "Kép elérési út"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr "Közzétett"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "Join us next time to meet"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr "Nyelv"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type____last_update
msgid "Last Modified on"
msgstr "Legutóbb módosítva"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_date
msgid "Last Updated on"
msgstr "Frissítve "

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Level"
msgstr "Szint"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Level:"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Live"
msgstr "Élő"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_512
msgid "Logo"
msgstr "Logó"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_main_attachment_id
msgid "Main Attachment"
msgstr "Fő melléklet"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Mobile"
msgstr "Mobil"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "More info"
msgstr "Tivábbi információ"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Tevékenységeim határideje"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_name
msgid "Name"
msgstr "Név"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Következő tevékenység naptár esemény"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Következő tevékenység határideje"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_summary
msgid "Next Activity Summary"
msgstr "Következő tevékenység összegzés"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_id
msgid "Next Activity Type"
msgstr "Következő tevékenység típusa"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of Actions"
msgstr "Műveletek száma"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Üzenetek száma, melyek műveletet igényelnek"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_unread_counter
msgid "Number of unread messages"
msgstr "Olvasatlan üzenetek száma"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Online"
msgstr "Online"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__online
msgid "Online Exhibitor"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Partner"
msgstr "Partner"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Phone"
msgstr "Telefon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Published"
msgstr "Közzétett"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid ""
"Rank your sponsors based on your own grading system (e.g. \"Gold, Silver, "
"Bronze\")."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Register"
msgstr "Regisztráció"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_user_id
msgid "Responsible User"
msgstr "Felelős felhasználó"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS kézbesítési hiba"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr ""

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "Ezüst"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr ""

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/models/event_sponsor.py:0
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__sponsor
#, python-format
msgid "Sponsor"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__email
msgid "Sponsor Email"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__name
msgid "Sponsor Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_exhibitor.menu_event_sponsor_type
msgid "Sponsor Levels"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Sponsor Name"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__exhibitor_type
msgid "Sponsor Type"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__url
msgid "Sponsor Website"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sponsor_type_id
msgid "Sponsoring Level"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_event_view_form
msgid "Sponsors"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid ""
"Sponsors are advertised on your event pages.<br>\n"
"    Exhibitors have a dedicated page a with chat room for people to connect with them."
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tevékenységeken alapuló állapot\n"
"Lejárt: A tevékenység határideje lejárt\n"
"Ma: A határidő ma van\n"
"Tervezett: Jövőbeli határidő."

#. module: website_event_exhibitor
#: code:addons/website_event_exhibitor/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to meet %s !"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"A teljes elérési út/URL a dokumentum weboldalon keresztüli eléréséhez."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr "Időzóna"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kivétel tevékenység típusa a rekordon."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_thumb_details
msgid "Unpublished"
msgstr "Nem közzétett"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_unread
msgid "Unread Messages"
msgstr "Olvasatlan üzenetek"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Olvasatlan üzenetek száma"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr "Látható ezen a weboldalon"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We did not find any exhibitor matching your"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_tree
msgid "Website"
msgstr "Honlap"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr ""

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website Messages"
msgstr "Honlap üzenetek"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr "Honlap címe"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website communication history"
msgstr "Weboldali kommunikációs előzmények"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : https://www.odoo.com"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. Your best choice for your home"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is not available right now."
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is over."
msgstr ""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "minutes"
msgstr "perc"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "search."
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts in"
msgstr ""

#. module: website_event_exhibitor
#. openerp-web
#: code:addons/website_event_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
#, python-format
msgid "starts on"
msgstr ""
