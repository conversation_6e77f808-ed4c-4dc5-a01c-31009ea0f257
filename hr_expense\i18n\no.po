# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Language-Team: Norwegian (https://www.transifex.com/odoo/teams/41243/no/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: no\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"%(user)s confirms this expense is not a duplicate with similar expense."
msgstr ""

#. module: hr_expense
#: model:ir.actions.report,print_report_name:hr_expense.action_report_hr_expense_sheet
msgid ""
"'Expenses - %s - %s' % (object.employee_id.name, (object.name).replace('/', "
"''))"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "1 %(exp_cur)s = %(rate)s %(comp_cur)s"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"<i class=\"text-muted oe_edit_only\">Use this reference as a subject prefix "
"when submitting by email.</i>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Approve the report here.</p><p>Tip: if you refuse, don’t forget to give "
"the reason thanks to the hereunder message tool</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>Click on <b> Action Create Report </b> to submit selected expenses to "
"your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Click on <b> Create Report </b> to create the report.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Once your <b> Expense </b> is ready, you can save it.</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "<p>Select expenses to submit them to your manager</p>"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"<p>The accountant receive approved expense reports.</p><p>He can post "
"journal entries in one click if taxes and accounts are right.</p>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "<span>@</span>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Date:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Description:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Employee:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Payment By:</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Total</strong>"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "<strong>Validated By:</strong>"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.accomodation_expense_product
#: model:product.template,name:hr_expense.accomodation_expense_product_product_template
msgid "Accomodation"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__account_id
msgid "Account"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Accounting"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__accounting_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__accounting_date
msgid "Accounting Date"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_ids
msgid "Activities"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_state
msgid "Activity State"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.mail_activity_type_action_config_hr_expense
#: model:ir.ui.menu,name:hr_expense.hr_expense_menu_config_activity_type
msgid "Activity Types"
msgstr ""

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_manager
msgid "Administrator"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Alias"
msgstr ""

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_user
msgid "All Approver"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_all
msgid "All Expense Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_all
msgid "All My Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all
msgid "All Reports"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__amount_residual
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__amount_residual
msgid "Amount Due"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount
msgid "Amount paid"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/upload_mixin.js:0
#, python-format
msgid "An error occurred during the upload"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__account_id
msgid "An expense account is expected"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "An expense of same category, amount and date already exists."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "An expense report must contain only lines from the same company."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_account_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Analytic Account"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Apple App Store"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__approval_date
msgid "Approval Date"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Approve Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
msgid "Approve the new expense reports submitted by the employees you manage."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__approved
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__approve
#: model:mail.message.subtype,name:hr_expense.mt_expense_approved
msgid "Approved"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_by
msgid "Approved By"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Approved Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__approved_on
msgid "Approved On"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Archived"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Attach Receipt"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Attach your receipt here."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_attachment_count
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Attachments"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "Bank Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__reference
msgid "Bill Reference"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_approve
msgid "Can Approve"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__can_reset
msgid "Can Reset"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,field_description:hr_expense.field_product_template__can_be_expensed
#: model_terms:ir.ui.view,arch_db:hr_expense.product_template_search_view_inherit_hr_expense
msgid "Can be Expensed"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#, python-format
msgid "Cancel"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Category"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Category: not found"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Certified honest and conform,<br/>(Date and signature).<br/><br/>"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__company_id
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__company_account
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Company"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_configuration
msgid "Configuration"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Confirmed Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Create Report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
msgid "Create a new expense report"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Create expenses from incoming emails"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "Create new expenses to get statistics."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_uid
msgid "Created by"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__create_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__create_date
msgid "Created on"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__currency_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__currency_id
msgid "Currency"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.allowance_expense_product
#: model:product.template,name:hr_expense.allowance_expense_product_product_template
msgid "Daily Allowance"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Date"
msgstr ""

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.allowance_expense_product
#: model:product.template,uom_name:hr_expense.allowance_expense_product_product_template
msgid "Days"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Dear"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__expense_alias_prefix
msgid "Default Alias Name for Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_department
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__department_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Department"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__name
msgid "Description"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Did you try the mobile app?"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Digitalize your receipts with OCR and Artificial Intelligence"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__display_name
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__display_name
msgid "Display Name"
msgstr ""

#. module: hr_expense
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid ""
"Do not keep your expense tickets in your pockets any longer. Just snap a "
"picture of your receipt and let Odoo digitalizes it for you. The OCR and "
"Artificial Intelligence will fill the data automatically."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Documents"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__done
msgid "Done"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__draft
msgid "Draft"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/wizard/hr_expense_approve_duplicate.py:0
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__duplicate_expense_ids
#, python-format
msgid "Duplicate Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__employee_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__employee_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Employee"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__payment_mode__own_account
msgid "Employee (to reimburse)"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_account
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_account_employee_expenses
msgid "Employee Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__address_id
msgid "Employee Home Address"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_account_move_line__expense_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__expense_ids
#: model:ir.model.fields,field_description:hr_expense.field_res_users__expense_manager_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Expense"
msgstr ""

#. module: hr_expense
#: model:mail.activity.type,name:hr_expense.mail_act_expense_approval
msgid "Expense Approval"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_approve_duplicate
msgid "Expense Approve Duplicate"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_employee_tree_inherit_expense
msgid "Expense Approver"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__date
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expense Date"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expense Digitalization (OCR)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__journal_id
msgid "Expense Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__expense_line_ids
msgid "Expense Lines"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_employee_public__expense_manager_id
msgid "Expense Manager"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_product
#: model:ir.ui.menu,name:hr_expense.menu_hr_product
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Expense Products"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_refuse_wizard
msgid "Expense Refuse Reason Wizard"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_expense_sheet
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sheet_id
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__state
msgid "Expense Report State"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__name
msgid "Expense Report Summary"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_report
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Expense Reports"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_filtered
msgid "Expense Reports Analysis"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_pay
msgid "Expense Reports To Pay"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_post
msgid "Expense Reports To Post"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_all_to_approve
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_department_to_approve
msgid "Expense Reports to Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid "Expense Validate Duplicate"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "Expense products can be reinvoiced to your customers."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Expense refuse reason"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_approved
msgid "Expense report approved"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_paid
msgid "Expense report paid"
msgstr ""

#. module: hr_expense
#: model:mail.message.subtype,description:hr_expense.mt_expense_refused
msgid "Expense report refused"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid ""
"Expense reports regroup all the expenses incurred during a specific event."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_account_move_line__expense_id
msgid "Expense where the move line come from"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_root
#: model:product.product,name:hr_expense.product_product_fixed_cost
#: model:product.template,name:hr_expense.product_product_fixed_cost_product_template
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_activity
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_all_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_graph
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_pivot
msgid "Expenses Analysis"
msgstr ""

#. module: hr_expense
#: model:ir.actions.report,name:hr_expense.action_report_hr_expense_sheet
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Expenses Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_department__expense_sheets_to_approve_count
msgid "Expenses Reports to Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses by Date"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Expenses must be paid by the same entity (Company or employee)."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Expenses of Your Team Member"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Expenses to Invoice"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.account_journal_dashboard_kanban_view_inherit_hr_expense
msgid "Expenses to Process"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_refused
msgid "Explicitly Refused by manager or accountant"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.trans_expense_product
#: model:product.template,name:hr_expense.trans_expense_product_product_template
msgid "Flights, train, bus, taxi, parking"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_follower_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_partner_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_type_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.food_expense_product
#: model:product.template,name:hr_expense.food_expense_product_product_template
msgid "Food & beverages"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Former Employees"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Future Activities"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "General Information"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generated Expense"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Generated Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid "Google Play Store"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Group By"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__is_multiple_currency
msgid "Handle lines with different currencies"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__has_message
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__has_message
msgid "Has Message"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_ids
msgid "Hr Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__hr_expense_sheet_id
msgid "Hr Expense Sheet"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__id
msgid "ID"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_icon
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__in_payment
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "In Payment"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Incoming Emails"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Invalid attachments!"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__invoicing_legacy
msgid "Invoicing App Legacy"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_editable
msgid "Is Editable By Current User"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_is_follower
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__same_currency
msgid "Is currency_id different from the company_currency_id"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_has_cost
msgid "Is product with non zero cost selected"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_journal
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "Journal"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__account_move_id
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Journal Entry"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_convert_rate
msgid "Label Convert Rate"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__label_total_amount_company
msgid "Label Total Amount Company"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard____last_update
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet____last_update
msgid "Last Modified on"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_uid
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__write_date
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Late Activities"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__use_mailgateway
msgid "Let your employees record expenses by email"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__user_id
msgid "Manager"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Managers can get all reports to approve from this menu."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_ids
msgid "Messages"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.mileage_expense_product
#: model:product.template,name:hr_expense.mileage_expense_product_product_template
msgid "Mileage"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_actions_my_unsubmitted
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_my_expenses_to_submit
msgid "My Expenses to Report"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_sheet_my_all
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_my_reports
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "My Reports"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "My Team"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Name"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "New Expense Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_date_deadline
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_summary
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_type_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"No Expense account found for the product %s (or for its category), please "
"configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No Home Address found for the employee %s, please configure one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "No attachment was provided"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_department_filtered
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_all
msgid "No data yet!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_product
msgid "No expense products found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid "No expense report found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_approve
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid "No expense reports found"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all
msgid "No expense reports found. Let's create one!"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__not_paid
msgid "Not Paid"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__description
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Notes..."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__attachment_number
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_needaction_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_has_error_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Odoo"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_account
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_all
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_my_all
msgid ""
"Once you have created your expense, submit it to your manager who will "
"validate it."
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"Once your <b>Expense report</b> is ready, you can submit it to your manager "
"and wait for the approval from your manager."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only HR Officers or the concerned employee can reset to draft."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Only Managers and HR Officers can approve expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Other Info"
msgstr ""

#. module: hr_expense
#: model:product.product,name:hr_expense.other_expense_product
#: model:product.template,name:hr_expense.other_expense_product_product_template
msgid "Other expenses"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__done
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__paid
#: model:mail.message.subtype,name:hr_expense.mt_expense_paid
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Paid"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__payment_mode
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_mode
msgid "Paid By"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Partial"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__partial
msgid "Partially Paid"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/account_move.py:0
#, python-format
msgid "Payment Cancelled"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__payment_state
msgid "Payment Status"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Please configure Default Expense account for Category expense: "
"`property_account_expense_categ_id`."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Post Entries"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Post Journal Entries"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_post
msgid ""
"Post the journal entries of the new expense reports approved by the "
"employees' manager."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__post
msgid "Posted"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register_no_user
msgid "Powered by"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Price in Company Currency"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Price:"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "Product Name"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_product_template
msgid "Product Template"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_tree_view
msgid "Product Variants"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Qty"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__quantity
msgid "Quantity"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_refuse_wizard__reason
msgid "Reason"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Reason :"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
msgid "Reason to refuse Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "Receipts"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Ref."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__is_ref_editable
msgid "Reference Is Editable By Current User"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_refuse_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Refuse"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_refuse_wizard_action
msgid "Refuse Expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__refused
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__cancel
#: model:mail.message.subtype,name:hr_expense.mt_expense_refused
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Refused Expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#: model:ir.model,name:hr_expense.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
#, python-format
msgid "Register Payment"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_payroll_expense
msgid "Reimburse Expenses in Payslip"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse expenses in payslips"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Reimburse in Payslip"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.action_hr_expense_sheet_all_to_pay
msgid ""
"Reimburse the employees who incurred these costs or simply register the "
"corresponding payments."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__company_currency_id
msgid "Report Company Currency"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_reports
msgid "Reporting"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_approve
msgid "Reports to Approve"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_pay
msgid "Reports to Pay"
msgstr ""

#. module: hr_expense
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_sheet_all_to_post
msgid "Reports to Post"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reset to Draft"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__activity_user_id
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__payment_state__reversed
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Reversed"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_has_sms_error
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__sample
msgid "Sample"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/expense_form_view.js:0
#, python-format
msgid "Save Anyways"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/expense_qr_modal_template.xml:0
#, python-format
msgid "Scan this QR code to get the Odoo app:"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_employee__expense_manager_id
#: model:ir.model.fields,help:hr_expense.field_res_users__expense_manager_id
msgid ""
"Select the user responsible for approving \"Expenses\" of this employee.\n"
"If empty, the approval is done by an Administrator or Approver (determined in settings/users)."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Selected Unit of Measure for expense %(expense)s does not belong to the same"
" category as the Unit of Measure of product %(product)s."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid ""
"Send an email to this email alias with the receipt in attachment to create "
"an expense in one click. If the first word of the mail subject contains the "
"category's internal reference or the category name, the corresponding "
"category will automatically be set. Type the expense amount in the mail "
"subject to set it on the expense too."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_res_config_settings__module_hr_expense_extract
msgid "Send bills to OCR to generate expenses"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.action_hr_expense_configuration
#: model:ir.ui.menu,name:hr_expense.menu_hr_expense_global_settings
msgid "Settings"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "Setup your domain alias"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_approve_duplicate__sheet_ids
msgid "Sheet"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: hr_expense
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_all
#: model_terms:ir.actions.act_window,help:hr_expense.hr_expense_actions_my_unsubmitted
msgid ""
"Snap pictures of your receipts and let Odoo<br> automatically create "
"expenses for you."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"Specify expense journal in tab Other Info to generate accounting entries."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_product_product__can_be_expensed
#: model:ir.model.fields,help:hr_expense.field_product_template__can_be_expensed
msgid "Specify whether the product can be selected in an expense."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__state
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__state
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Status"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_state
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__state
msgid "Status of the expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "Submit to Manager"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__reported
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense_sheet__state__submit
msgid "Submitted"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__untaxed_amount
msgid "Subtotal"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__tax_ids
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Taxes"
msgstr ""

#. module: hr_expense
#: model:res.groups,name:hr_expense.group_hr_expense_team_approver
msgid "Team Approver"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid ""
"The accountant can register a payment to reimburse the employee directly."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The current user has no related employee. Please, create one."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "The expense reports were successfully approved."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid ""
"The first word of the email subject did not correspond to any category code."
" You'll have to set the category manually on the expense."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_approve_duplicate_view_form
msgid ""
"The following approved expenses have similar employee, amount and category "
"than some expenses of this report. Please verify this report does not "
"contain duplicates."
msgstr ""

#. module: hr_expense
#: model:ir.model.constraint,message:hr_expense.constraint_hr_expense_sheet_journal_id_required_posted
msgid "The journal must be set on posted expense"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__journal_id
msgid "The journal used when the expense is done."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__bank_journal_id
msgid "The payment method used when the expense is paid by the company."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "There are no expense reports to approve."
msgstr ""

#. module: hr_expense
#: model:digest.tip,name:hr_expense.digest_tip_hr_expense_0
#: model_terms:digest.tip,tip_description:hr_expense.digest_tip_hr_expense_0
msgid "Tip: Snap pictures of your receipts with the remote app"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Approve"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Pay"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
msgid "To Post"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "To Report"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields.selection,name:hr_expense.selection__hr_expense__state__draft
msgid "To Submit"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_sheet_view_search
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_search
msgid "Today Activities"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Total %s"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__total_amount_company
msgid "Total (Company Currency)"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__total_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_tree
msgid "Total Amount"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_expenses_analysis_tree
msgid "Total Company Currency"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__activity_exception_decoration
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_expense.report_expense_sheet
msgid "Unit Price"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.accomodation_expense_product
#: model:product.product,uom_name:hr_expense.food_expense_product
#: model:product.product,uom_name:hr_expense.other_expense_product
#: model:product.product,uom_name:hr_expense.product_product_fixed_cost
#: model:product.product,uom_name:hr_expense.trans_expense_product
#: model:product.template,uom_name:hr_expense.accomodation_expense_product_product_template
#: model:product.template,uom_name:hr_expense.food_expense_product_product_template
#: model:product.template,uom_name:hr_expense.other_expense_product_product_template
#: model:product.template,uom_name:hr_expense.product_product_fixed_cost_product_template
#: model:product.template,uom_name:hr_expense.trans_expense_product_product_template
msgid "Units"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__message_unread_counter
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__product_uom_category_id
msgid "UoM Category"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#: code:addons/hr_expense/static/src/xml/documents_upload_views.xml:0
#, python-format
msgid "Upload"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "Uploaded Attachment"
msgstr ""

#. module: hr_expense
#: model:ir.model,name:hr_expense.model_res_users
msgid "Users"
msgstr ""

#. module: hr_expense
#: model:ir.actions.act_window,name:hr_expense.hr_expense_approve_duplicate_action
msgid "Validate Duplicate Expenses"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "View Attachments"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "View Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "View Report"
msgstr ""

#. module: hr_expense
#. openerp-web
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#: code:addons/hr_expense/static/src/js/tours/hr_expense.js:0
#, python-format
msgid "Want to manage your expenses? It starts here."
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,field_description:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hr_expense
#: model:ir.model.fields,help:hr_expense.field_hr_expense__website_message_ids
#: model:ir.model.fields,help:hr_expense.field_hr_expense_sheet__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid ""
"When the cost of an expense product is different than 0, then the user using"
" this product won't be able to change the amount of the expense, only the "
"quantity. Use a cost different than 0 for expense categories funded by the "
"company at fixed cost like allowances for mileage, per diem, accomodation or"
" meal."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit the reference of this expense report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You are not authorized to edit this expense report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can not create report without category."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "You can now submit it to the manager from the following link."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only approve your department expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only generate accounting entry for approved expense(s)."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can only refuse your department expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You can't mix sample expenses and regular ones"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot add expenses of another employee."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot approve your own expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or approved expense."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot delete a posted or paid expense."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot refuse your own expenses"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report expenses for different employees in the same report."
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "You cannot report twice the same line!"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid ""
"You need to have at least one category that can be expensed in your database"
" to proceed!"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "Your Expense"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_register
msgid "Your expense has been successfully registered."
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.product_product_expense_form_view
msgid "e.g. Lunch"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_view_form
msgid "e.g. Lunch with Customer"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.view_hr_expense_sheet_form
msgid "e.g. Trip to NY"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.hr_expense_template_refuse_reason
msgid "has been refused"
msgstr ""

#. module: hr_expense
#: model:product.product,uom_name:hr_expense.mileage_expense_product
#: model:product.template,uom_name:hr_expense.mileage_expense_product_product_template
msgid "km"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to be reimbursed"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "to report"
msgstr ""

#. module: hr_expense
#: code:addons/hr_expense/models/hr_expense.py:0
#, python-format
msgid "under validation"
msgstr ""

#. module: hr_expense
#: model_terms:ir.ui.view,arch_db:hr_expense.res_config_settings_view_form
msgid "use OCR to fill data from a picture of the bill"
msgstr ""
