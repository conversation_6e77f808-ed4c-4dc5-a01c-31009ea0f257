<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="fleet_vehicle_odometer_view_tree" model="ir.ui.view">
        <field name="name">fleet.vehicle.odometer.view.tree.inherit.hr.fleet</field>
        <field name="model">fleet.vehicle.odometer</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_odometer_view_tree" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='driver_id']" position="after">
                <field name="driver_employee_id" widget="many2one_avatar" optional="hide"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_assignation_log_view_list" model="ir.ui.view">
        <field name="name">fleet.vehicle.assignation.log.view.tree.inherit.hr.fleet</field>
        <field name="model">fleet.vehicle.assignation.log</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_assignation_log_view_list" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='date_end']" position="after">
                <field name="attachment_number" string=" "/>
                <button name="action_get_attachment_view" string="Attachments" type="object" icon="fa-paperclip"/>
            </xpath>
            <xpath expr="//field[@name='driver_id']" position="after">
                <field name="driver_employee_id" widget="many2one_avatar" optional="hide"/>
            </xpath>
        </field>
    </record>

    <record id="fleet_vehicle_view_form_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.form.inherit.hr</field>
        <field name="model">fleet.vehicle</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='driver_id']" position="after">
                <field name="driver_employee_id" invisible="1"/>
                <field name="mobility_card" readonly="1"/>
            </xpath>
            <xpath expr="//field[@name='future_driver_id']" position="after">
                <field name="future_driver_employee_id" invisible="1"/>
            </xpath>
            <button name="open_assignation_logs" position="before">
                <button name="action_open_employee" type="object" class="oe_stat_button" icon="fa-id-card-o" groups="hr.group_hr_user" attrs="{'invisible': [('driver_employee_id', '=', False)]}">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value"><field name="driver_employee_name"/></span>
                        <span class="o_stat_text">Employee</span>
                    </div>
                </button>
            </button>
        </field>
    </record>

    <record id="fleet_vehicle_view_search_inherit_hr" model="ir.ui.view">
        <field name="name">fleet.vehicle.search.inherit.hr</field>
        <field name="model">fleet.vehicle</field>
        <field name="inherit_id" ref="fleet.fleet_vehicle_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='driver_id']" position="after">
                <field name="mobility_card"/>
                <field name="driver_employee_id" string="Current Driver (Employee)"/>
                <field name="future_driver_employee_id" string="Future Driver (Employee)"/>
            </xpath>
        </field>
    </record>
</odoo>
