# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_account
#
# Translators:
# <PERSON> <aju<PERSON><EMAIL>>, 2015
# <PERSON>, 2016
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:06+0000\n"
"PO-Revision-Date: 2016-02-22 05:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Ecuador) (http://www.transifex.com/odoo/odoo-9/"
"language/es_EC/)\n"
"Language: es_EC\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
msgid "# of Products"
msgstr "Nº de productos"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "Propiedades de cuenta de existencias"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_config_settings_inherit
msgid "Accounting"
msgstr "Contabilidad"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "Información contable"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_change_standard_price.py:62
#, python-format
msgid "Active ID is not set in Context."
msgstr "El id. activo no se ha establecido en el contexto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_config_settings_group_stock_inventory_valuation
msgid ""
"Allows to configure inventory valuations on products and product categories."
msgstr ""
"Permite configurar valoraciones de inventario en los productos y categorías "
"de producto."

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Average Price"
msgstr "Precio medio"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_can_be_expensed
msgid "Can be expensed"
msgstr "Puede ser tratado como gasto"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:278
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on "
"the product category, or on the location, before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/stock_account.py:280
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Price"
msgstr "Cambiar precio"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_view_change_standard_price
#: model:ir.model,name:stock_account.model_stock_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Standard Price"
msgstr "Cambiar precio estándar"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Choose a date in the past to get the inventory at that date."
msgstr ""
"Seleccione una fecha en el pasado para obtener el inventario desde esa fecha."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_inventory_accounting_date
msgid ""
"Choose the accounting date at which you want to value the stock moves "
"created by the inventory instead of the default one (the inventory end date)"
msgstr ""
"Escriba la fecha de contabilidad en la que quiera valorar los movimientos de "
"stock creados por el inventario en vez de la predeterminada (la fecha de fin "
"de inventario)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Choose your date"
msgstr "Escoja su fecha"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_uos_coeff
msgid ""
"Coefficient to convert default Unit of Measure to Unit of Sale uos = uom * "
"coeff"
msgstr ""
"Coeficiente para convertir la unidad de medida predeterminada (UdM) en la "
"unidad de venta UdV = UdM * coeff "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Company"
msgstr "Compañía"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Compute from average price"
msgstr "Calculado desde el precio promedio"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:351
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Cost"
msgstr "Coste"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_cost_method
msgid "Costing Method"
msgstr "Método de coste"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_counterpart_account_id
#, fuzzy
msgid "Counter-Part Account"
msgstr "Cuenta de salida de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_uid
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_date
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_create_date
msgid "Created on"
msgstr "Creado en"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_date
msgid "Date"
msgstr "Fecha"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_history_display_name
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_inventory_accounting_date
msgid "Force Accounting Date"
msgstr "Fecha de contabilización forzoza"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_id
#: model:ir.model.fields,field_description:stock_account.field_stock_history_id
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_id
msgid "ID"
msgstr "ID (identificación)"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_change_standard_price_new_price
msgid ""
"If cost price is increased, stock variation account will be debited and "
"stock output account will be credited with the value = (difference of amount "
"* quantity available).\n"
"If cost price is decreased, stock variation account will be creadited and "
"stock input account will be debited."
msgstr ""
"Si el precio de coste se incrementa, la cuenta la variación de existencias "
"irá al debe y la cuenta de salida de existencias irá al haber con el valor = "
"(diferencia de cantidad * cantidad disponible).\n"
"Si el precio de coste se reduce, la cuenta la variación de existencias irá "
"al haber y la cuenta de entrada de existencias irá al debe."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_valuation
msgid ""
"If perpetual valuation is enabled for a product, the system will "
"automatically create journal entries corresponding to stock moves, with "
"product price as specified by the 'Costing Method'. The inventory variation "
"account set on the product category will represent the current inventory "
"value, and the stock input and stock output account will hold the "
"counterpart moves for incoming and outgoing products."
msgstr ""
"Si la valoración perpetua se activa para un producto, el sistema creará "
"automáticamente asientos contables correspondientes a movimientos de stock, "
"con el precio de producto indicado según el \"método de coste\". La cuenta "
"de valoración de inventario establecida en la categoría de producto "
"representará la cuenta de inventario actual, y las cuentas de entrada y "
"salida de mercancía contendrán las contrapartidas de movimiento para los "
"productos entrantes y salientes."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,help:stock_account.field_product_template_property_valuation
msgid ""
"If perpetual valuation is enabled for a product, the system will "
"automatically create journal entries corresponding to stock moves, with "
"product price as specified by the 'Costing Method'The inventory variation "
"account set on the product category will represent the current inventory "
"value, and the stock input and stock output account will hold the "
"counterpart moves for incoming and outgoing products."
msgstr ""
"Si la valoración perpetua se activa para un producto, el sistema creará "
"automáticamente asientos contables correspondientes a movimientos de stock, "
"con el precio de producto indicado según el \"método de costo\". La cuenta "
"de valoración de inventario establecida en la categoría de producto "
"representará la cuenta de inventario actual, y las cuentas de entrada y "
"salida de mercancía contendrán las contrapartidas de movimiento para los "
"productos entrantes y salientes."

#. module: stock_account
#: selection:stock.config.settings,module_stock_landed_costs:0
msgid "Include landed costs in product costing computation"
msgstr "Incluye costos adicionales en el cálculo del costo del producto"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_config_settings_module_stock_landed_costs
msgid ""
"Install the module that allows to affect landed costs on pickings, and split "
"them onto the different products."
msgstr ""
"Instala el módulo que permite imputar costes en destino en los Movimientos "
"de Inventario, y separarlos entre los diferentes productos."

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_inventory
msgid "Inventory"
msgstr "Inventario"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicaciones de inventario"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_config_settings_group_stock_inventory_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Inventory Valuation"
msgstr "Valoración del inventario"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_inventory_value
msgid "Inventory Value"
msgstr "Valor del inventario"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_wizard_stock_valuation_history
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_choose_date
#: model:ir.ui.menu,name:stock_account.menu_action_wizard_valuation_history
msgid "Inventory at Date"
msgstr "Inventario a la fecha"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice
msgid "Invoice"
msgstr "Factura"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice_line
msgid "Invoice Line"
msgstr "Línea factura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_config_settings_module_stock_landed_costs
msgid "Landed Costs"
msgstr "Costes en destino"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price___last_update
#: model:ir.model.fields,field_description:stock_account.field_stock_history___last_update
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history___last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_uid
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_date
#: model:ir.model.fields,field_description:stock_account.field_wizard_valuation_history_write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_location_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Location"
msgstr "Ubicación"

#. module: stock_account
#: model:res.groups,name:stock_account.group_inventory_valuation
msgid "Manage Inventory Valuation and Costing Methods"
msgstr "Gestionar valoración de inventario y métodos de coste"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Move"
msgstr "Movimiento"

#. module: stock_account
#: code:addons/stock_account/product.py:125
#: code:addons/stock_account/product.py:187
#, python-format
msgid "No difference between standard price and new price!"
msgstr "¡No hay diferencias entre el precio estándar y el nuevo precio!"

#. module: stock_account
#: selection:stock.config.settings,module_stock_landed_costs:0
msgid "No landed costs"
msgstr "Sin costos adicionales"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_date
msgid "Operation Date"
msgstr "Fecha de la operación"

#. module: stock_account
#: selection:product.category,property_valuation:0
#: selection:product.template,property_valuation:0
msgid "Periodic (manual)"
msgstr "Períodico (manual)"

#. module: stock_account
#: selection:stock.config.settings,group_stock_inventory_valuation:0
msgid "Periodic inventory valuation (recommended)"
msgstr "Valoración de inventario periódico (recomendado)"

#. module: stock_account
#: selection:product.category,property_valuation:0
#: selection:product.template,property_valuation:0
msgid "Perpetual (automated)"
msgstr "Perpetuo (automático)"

#. module: stock_account
#: selection:stock.config.settings,group_stock_inventory_valuation:0
msgid "Perpetual inventory valuation (stock move generates accounting entries)"
msgstr ""
"Valoración de inventario perpetuo (movimientos de inventario generan "
"asientos contables)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_new_price
msgid "Price"
msgstr "Precio"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product"
msgstr "Producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_alert_time
msgid "Product Alert Time"
msgstr "Tiempo de alerta producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_life_time
msgid "Product Life Time"
msgstr "Tiempo de vida producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_quantity
msgid "Product Quantity"
msgstr "Cantidad producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_removal_time
msgid "Product Removal Time"
msgstr "Tiempo eliminación producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_history_product_template_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Product Template"
msgstr "Plantilla producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_use_time
msgid "Product Use Time"
msgstr "Duración del producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Real Price"
msgstr "Precio real"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Retrieve the Inventory Value"
msgstr "Obtener el valor de inventario"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_wizard_valuation_history
msgid "Retrieve the curent stock valuation."
msgstr "Recupera la actual valoración de inventario."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_serial_number
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
msgid "Serial Number"
msgstr "Nº de serie"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
#, fuzzy
msgid "Set standard price"
msgstr "Precio estándar"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_source
msgid "Source"
msgstr "Texto original"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_uos_id
msgid ""
"Specify a unit of measure here if invoicing is made in another unit of "
"measure than inventory. Keep empty to use the default unit of measure."
msgstr ""
"Especifique aquí una unidad de medida si la factura se realizará en otra "
"unidad distinta a la del inventario. Déjelo vacío para usar la de por "
"defecto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_can_be_expensed
msgid "Specify whether the product can be selected in an HR expense."
msgstr ""
"Especifica si el producto puede ser seleccionado como un gasto de RRHH."

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Standard Price"
msgstr "Precio estándar"

#. module: stock_account
#: code:addons/stock_account/product.py:138
#: code:addons/stock_account/product.py:145
#: code:addons/stock_account/product.py:199
#: code:addons/stock_account/product.py:205
#, python-format
msgid "Standard Price changed"
msgstr "Precio estándar cambiado"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_template_property_cost_method
msgid ""
"Standard Price: The cost price is manually updated at the end of a specific "
"period (usually once a year).\n"
"                    Average Price: The cost price is recomputed at each "
"incoming shipment and used for the product valuation.\n"
"                    Real Price: The cost price displayed is the price of the "
"last outgoing product (will be use in case of inventory loss for example)."
msgstr ""
"Precio estándar: El precio de costo se actualiza manualmente al final de un "
"período determinado (por lo general una vez al año).\n"
"Precio medio: El precio de costo se recalcula en cada movimiento de "
"inventario entrante y se utiliza para la valoración del producto.\n"
"Precio Real: El precio de costo es el precio del último producto de salida "
"(se puede utilizar en caso de pérdida de inventario, por ejemplo)."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_cost_method
msgid ""
"Standard Price: The cost price is manually updated at the end of a specific "
"period (usually once a year).\n"
"Average Price: The cost price is recomputed at each incoming shipment and "
"used for the product valuation.\n"
"Real Price: The cost price displayed is the price of the last outgoing "
"product (will be used in case of inventory loss for example)."
msgstr ""
"Precio estándar: El precio de costo se actualiza manualmente al final de un "
"período determinado (por lo general una vez al año).\n"
"Precio medio: El precio de costo se recalcula en cada movimiento de "
"inventario entrante y se utiliza para la valoración del producto.\n"
"Precio Real: El precio de costo es el precio del último producto de salida "
"(se puede utilizar en caso de pérdida de inventario, por ejemplo)."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_input
msgid "Stock Input Account"
msgstr "Cuenta de entrada de existencias"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:464
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_journal
#, python-format
msgid "Stock Journal"
msgstr "Diario de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_stock_history_move_id
msgid "Stock Move"
msgstr "Movimiento de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_output
msgid "Stock Output Account"
msgstr "Cuenta de salida de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Cuenta de valoración de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "Cuenta de valoracion de existencias (entrada)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "Cuenta de valoracion de existencias (salida)"

#. module: stock_account
#: code:addons/stock_account/wizard/stock_valuation_history.py:31
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_graph
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_pivot
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_search
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
#, python-format
msgid "Stock Value At Date"
msgstr "Valor de inventario para una fecha"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "Plantillas para el plan contable"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:306
#, python-format
msgid ""
"The found valuation amount for product %s is zero. Which means there is "
"probably a configuration error. Check the costing method and the standard "
"price"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_history_report_tree
msgid "Total Value"
msgstr "Valor total"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_uos_coeff
msgid "Unit of Measure -> UOS Coeff"
msgstr "Unidad de medida -> Coeficiente UdV"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_uos_id
msgid "Unit of Sale"
msgstr "Unidad de venta"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Usado para una valoración en tiempo real del inventario. Cuando está "
"establecido en una ubicación virtual (no de tipo interno), esta cuenta se "
"usará para mantener el valor de los productos que son movidos de una "
"ubicación interna a esta ubicación, en lugar de la cuenta de salida de "
"existencias genérica establecida en el producto. No tiene efecto para "
"ubicaciones internas."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Usado para una valoración en tiempo real del inventario. Cuando está "
"establecido en una ubicación virtual (no de tipo interno), esta cuenta se "
"usará para mantener el valor de los productos que son movidos fuera de la "
"ubicación a una ubicación interna, en lugar de la cuenta de salida de "
"existencias genérica establecida en el producto. No tiene efecto para "
"ubicaciones internas."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_history_price_unit_on_quant
msgid "Value"
msgstr "Valor"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_alert_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before an "
"alert should be notified."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que se notifique una alerta."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_life_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods may become dangerous and must not be consumed."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes se conviertan en peligrosos y no deban ser consumidos."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_removal_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods should be removed from the stock."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes deban eliminarse del stock."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_use_time
msgid ""
"When a new a Serial Number is issued, this is the number of days before the "
"goods starts deteriorating, without being dangerous yet."
msgstr ""
"Cuando un nuevo nº de serie se asigna, éste es el número de días antes de "
"que los bienes se empiecen a deteriorar, sin ser peligroso aún."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_input_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. This is the default "
"value for all products in this category. It can also directly be set on each "
"product"
msgstr ""
"Cuando se realiza una valoración de inventario en tiempo real, la "
"contrapartida para todos los movimientos de entrada serán imputados en esta "
"cuenta, a menos que se haya establecido una cuenta de valoración específica "
"en la ubicación fuente. Éste es el valor por defecto para todos los "
"productos en esta categoría. También se puede establecer directamente en "
"cada producto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_input
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. When not set on the "
"product, the one from the product category is used."
msgstr ""
"Cuando se realiza una valoración de inventario en tiempo real, la "
"contrapartida para todos los movimientos de entrada serán imputados en esta "
"cuenta, a menos que se haya establecido una cuenta de valoración específica "
"en la ubicación fuente. Cuando no se establece en el producto, se usa la "
"establecida en la categoría."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_output_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. This is the "
"default value for all products in this category. It can also directly be set "
"on each product"
msgstr ""
"Cuando se realiza una valoración de inventario en tiempo real, la "
"contrapartida para todos los movimientos de salida serán imputados en esta "
"cuenta, a menos que se haya establecido una cuenta de valoración específica "
"en la ubicación destino. Éste es el valor por defecto para todos los "
"productos en esta categoría. También se puede establecer directamente en "
"cada producto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_output
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. When not set on "
"the product, the one from the product category is used."
msgstr ""
"Cuando se realiza una valoración de inventario en tiempo real, la "
"contrapartida para todos los movimientos de salida serán imputados en esta "
"cuenta, a menos que se haya establecido una cuenta de valoración específica "
"en la ubicación destino. Cuando no se establece en el producto, se usa la "
"establecida en la categoría."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_journal
msgid ""
"When doing real-time inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"Al hacer la valoración de inventario en tiempo real, éste es el diario "
"contable donde los asientos se crearán automáticamente cuando los "
"movimientos de existencias se procesen."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_valuation_account_id
msgid ""
"When real-time inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"Cuando está activada una valoración de inventario en tiempo real de un "
"producto, esta cuenta contiene el valor actual de los productos."

#. module: stock_account
#: model:ir.model,name:stock_account.model_wizard_valuation_history
msgid "Wizard that opens the stock valuation history table"
msgstr "Asistente que abre la tabla de histórico de valoración de inventario"

#. module: stock_account
#: code:addons/stock_account/stock_account.py:276
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts"
msgstr ""
"Usted no tiene ningún diario de inventario definido en la categoría del "
"producto, compruebe si ha instalado un plan de cuentas."

#. module: stock_account
#: code:addons/stock_account/stock_account.py:282
#, fuzzy, python-format
msgid ""
"You don't have any stock valuation account defined on your product category. "
"You must define one before processing this operation."
msgstr ""
"Usted no tiene ningún diario de inventario definido en la categoría del "
"producto, compruebe si ha instalado un plan de cuentas."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "_Apply"
msgstr "_Aplicar"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_config_settings
msgid "stock.config.settings"
msgstr "Parámetros de configuración de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_history
msgid "stock.history"
msgstr "Inventario Histórico"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_valuation
msgid "unknown"
msgstr "desconocido"

#~ msgid "Asset Type"
#~ msgstr "Tipo de ingreso"

#~ msgid "Deferred Revenue Type"
#~ msgstr "Tipo de cuenta de ingresos a plazos"
