<templates>
    <t t-name="_basic-callee">ok</t>
    <t t-name="_callee-printsbody"><t t-esc="0"/></t>
    <t t-name="_callee-uses-foo"><t t-esc="foo"/></t>

    <t t-name="basic-caller">
        <t t-call="_basic-callee"/>
    </t>
    <result id="basic-caller">ok</result>

    <t t-name="with-unused-body">
        <t t-call="_basic-callee">WHEEE</t>
    </t>
    <result id="with-unused-body">ok</result>

    <t t-name="with-unused-setbody">
        <t t-call="_basic-callee">
            <t t-set="qux" t-value="3"/>
        </t>
    </t>
    <result id="with-unused-setbody">ok</result>

    <t t-name="with-used-body">
        <t t-call="_callee-printsbody">ok</t>
    </t>
    <result id="with-used-body">ok</result>

    <t t-name="with-used-setbody">
        <t t-call="_callee-uses-foo">
            <t t-set="foo" t-value="'ok'"/>
        </t>
    </t>
    <result id="with-used-setbody">ok</result>

    <t t-name="_call-with-body-arch-lookup">
        <section t-raw="0"/>
    </t>

    <t t-name="call-without-body-arch-lookup">
        <t t-call="_call-with-body-arch-lookup"/>
    </t>
    <result id="call-without-body-arch-lookup"><![CDATA[<section></section>]]></result>

    <t t-name="call-with-body-arch-lookup">
        <t t-call="_call-with-body-arch-lookup">
            <div><span class="toto" t-esc="value"/></div>
        </t>
    </t>
    <params id="call-with-body-arch-lookup">
        {"value": "ok"}
    </params>
    <result id="call-with-body-arch-lookup"><![CDATA[<section>
            <div><span class="toto">ok</span></div>
        </section>]]></result>

    <!--
        postfix to call removed because Python impl appends all whitespace
        following called template's root to template result (+= element.tail)
        -> ends up with bunch of extra whitespace in the middle of the
        generated content. Could normalize, not sure current impl can be
        fixed as-is
    -->
    <t t-name="inherit-context">
        <t t-set="foo" t-value="1"/>
        <t t-call="_callee-uses-foo"/><!-- - <t t-esc="foo"/> -->
    </t>
    <result id="inherit-context">1<!-- - 1 --></result>

    <t t-name="scoped-parameter">
        <t t-call="_basic-callee">
            <t t-set="foo" t-value="42"/>
        </t>
        <!-- should not print anything -->
        <t t-esc="foo"/>
    </t>
    <result id="scoped-parameter">
        ok
    </result>

    <t t-name="expression-caller">
        <t t-call="{{True and '_basic-callee' or 'other'}}"/>
    </t>
    <result id="expression-caller">ok</result>
</templates>
