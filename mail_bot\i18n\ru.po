# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail_bot
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <k<PERSON><PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2022
# Серге<PERSON>н <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: Сергей <PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Aaaaaw that's really cute but, you know, bots don't work that way. You're "
"too human for me! Let's keep it professional ❤️"
msgstr ""
"В это действительно мило, но, вы знаете, боты не работают следующим образом."
" Вы слишком человеческие для меня! Давайте оставим это в профессиональном "
"напрямку❤️"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__disabled
msgid "Disabled"
msgstr "Отключен"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_channel
msgid "Discussion Channel"
msgstr "Канал обсуждения"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_thread
msgid "Email Thread"
msgstr "Цепочка эл.почты"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Great! 👍<br/>To access special commands, <b>start your sentence with</b> "
"<span class=\"o_odoobot_command\">/</span>. Try getting help."
msgstr ""
"Отлично! 👍<br/>Список специальных команд появится, если вы<b>начнете фразу "
"с</b> <span class=\"o_odoobot_command\">/</span>. Получите помощь этим "
"способом."

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизация HTTP"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_channel.py:0
#, python-format
msgid ""
"Hello,<br/>Odoo's chat helps employees collaborate efficiently. I'm here to "
"help you discover its features.<br/><b>Try to send me an emoji</b> <span "
"class=\"o_odoobot_command\">:)</span>"
msgstr ""
"Привет, <br/> этот чат помогает сотрудникам эффективно сотрудничать. Я здесь"
" для того, чтобы рассказать вам об этих функциях. <br/> <b>Попробуйте "
"прислать мне смайлик</b><span class=\"o_odoobot_command\">:)</span> "

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "Hmmm..."
msgstr "Хммм ..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I am a simple bot, but if that's a dog, he is the cutest 😊 "
"<br/>Congratulations, you finished this tour. You can now <b>close this chat"
" window</b>. Enjoy discovering Odoo."
msgstr ""
"Я обычный бот, но если бы я был котом, то он был бы миленьким "
"😊<br/>Поздравляю, вы завершили обучение. Теперь вы можете <b>закрыть этот "
"чат</b>. Удачи вам в освоении системы."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "I'm afraid I don't understand. Sorry!"
msgstr "Кажется, я не понимаю. Извините!"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"I'm not smart enough to answer your question.<br/>To follow my guide, ask: "
"<span class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"Моих знаний не хватает, чтобы ответить на ваш вопрос.<br/>Чтобы начать "
"обучение, напишите <span class=\"o_odoobot_command\">начать тур</span>."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__idle
msgid "Idle"
msgstr "Бездействует"

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_mail_bot
msgid "Mail Bot"
msgstr "Почтовый бот"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not exactly. To continue the tour, send an emoji: <b>type</b> <span "
"class=\"o_odoobot_command\">:)</span> and press enter."
msgstr ""
"Не совсем. Чтобы продолжить обучение, отправьте мне смайлик: <b>введите</b> "
"<span class=\"o_odoobot_command\">:)</span> и затем нажмите ввод."

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__not_initialized
msgid "Not initialized"
msgstr "НЕ инициализирован"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Not sure what you are doing. Please, type <span "
"class=\"o_odoobot_command\">/</span> and wait for the propositions. Select "
"<span class=\"o_odoobot_command\">help</span> and press enter"
msgstr ""
"Не понимаю, что вы делаете. Пожалуйста, введите <span "
"class=\"o_odoobot_command\">/</span> и подождите пока появятся команды. "
"Выберите <span class=\"o_odoobot_command\">help</span> и нажмите ввод"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_state
msgid "OdooBot Status"
msgstr "Статус OdooBot"

#. module: mail_bot
#: model:ir.model.fields,field_description:mail_bot.field_res_users__odoobot_failed
msgid "Odoobot Failed"
msgstr ""

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_attachement
msgid "Onboarding attachement"
msgstr "приобщено прикрепления"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_command
msgid "Onboarding command"
msgstr "входная команда"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_emoji
msgid "Onboarding emoji"
msgstr "встроенные emoji"

#. module: mail_bot
#: model:ir.model.fields.selection,name:mail_bot.selection__res_users__odoobot_state__onboarding_ping
msgid "Onboarding ping"
msgstr "встроенные ping"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry I'm sleepy. Or not! Maybe I'm just trying to hide my unawareness of "
"human language...<br/>I can show you features if you write: <span "
"class=\"o_odoobot_command\">start the tour</span>."
msgstr ""
"Что-то я сегодня сонный. Или нет! А может я просто пытаюсь скрыть, что не "
"понимаю человеческий язык...<br/>Я могу рассказать вам о функциях, если вы "
"наберете: <span class=\"o_odoobot_command\">начать тур</span>."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Sorry, I am not listening. To get someone's attention, <b>ping him</b>. "
"Write <span class=\"o_odoobot_command\">@OdooBot</span> and select me."
msgstr ""
"Простите, я не слушал. Чтобы привлечь чье-то внимание <b>упомяните его</b>. "
"Наберите <span class=\"o_odoobot_command\">@</span> и выберите меня."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "That's not nice! I'm a bot but I have feelings... 💔"
msgstr "Это не хорошо! Я бот, но у меня есть чувство ... 💔"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"To <b>send an attachment</b>, click on the <i class=\"fa fa-paperclip\" "
"aria-hidden=\"true\"></i> icon and select a file."
msgstr ""
"Чтобы <b>отправить вложение</b> нажмите на иконку <i class=\"fa fa-"
"paperclip\" aria-hidden=\"true\"></i> и выберите файл."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "To start, try to send me an emoji :)"
msgstr "Чтобы начать, попробуйте отправить смайлик :)"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Unfortunately, I'm just a bot 😞 I don't understand! If you need help "
"discovering our product, please check <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">our "
"documentation</a> or <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">our videos</a>."
msgstr ""
"К сожалению я всего лишь бот 😞 и не понимаю! Если вам нужна помощь в "
"освоении системы, пожалуйста изучите <a "
"href=\"https://www.odoo.com/documentation\" target=\"_blank\">нашу "
"документацию</a> или <a href=\"https://www.odoo.com/slides\" "
"target=\"_blank\">наши видеоуроки</a>."

#. module: mail_bot
#: model:ir.model,name:mail_bot.model_res_users
msgid "Users"
msgstr "Пользователи"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Wow you are a natural!<br/>Ping someone with @username to grab their "
"attention. <b>Try to ping me using</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> in a sentence."
msgstr ""
"Круто, вы справились!<br/>Упомяните кого-нибудь при помощи @username, чтобы "
"привлечь его внимание. <b>Упомяните меня, набрав</b> <span "
"class=\"o_odoobot_command\">@OdooBot</span> в строке."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid ""
"Yep, I am here! 🎉 <br/>Now, try <b>sending an attachment</b>, like a picture"
" of your cute dog..."
msgstr ""
"Ага, я тут! 🎉 <br/>Теперь попробуйте <b>прикрепить файл</b>, например фотку "
"вашей любимой собачки..."

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "fuck"
msgstr "хуй"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "help"
msgstr "помощь"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "i love you"
msgstr "Я люблю тебя"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "love"
msgstr "люблю"

#. module: mail_bot
#: code:addons/mail_bot/models/mail_bot.py:0
#, python-format
msgid "start the tour"
msgstr "начать тур"
