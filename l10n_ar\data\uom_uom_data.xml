<?xml version="1.0" encoding="UTF-8"?>
<odoo noupdate="True">

    <record model='uom.uom' id='uom.product_uom_kgm'>
        <field name='l10n_ar_afip_code'>01</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_meter'>
        <field name='l10n_ar_afip_code'>02</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_litre'>
        <field name='l10n_ar_afip_code'>05</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_unit'>
        <field name='l10n_ar_afip_code'>07</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_dozen'>
        <field name='l10n_ar_afip_code'>09</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_cm'>
        <field name='l10n_ar_afip_code'>20</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_day'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_floz'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_foot'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_gram'>
        <field name='l10n_ar_afip_code'>14</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_gal'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_hour'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_inch'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_km'>
        <field name='l10n_ar_afip_code'>17</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_lb'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_mile'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_oz'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_qt'>
        <field name='l10n_ar_afip_code'>98</field>
    </record>
    <record model='uom.uom' id='uom.product_uom_ton'>
        <field name='l10n_ar_afip_code'>29</field>
    </record>
</odoo>
