# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * portal
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:43
#, python-format
msgid "%d days overdue"
msgstr "%d dan(a) prekoračenja"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_show_sign_in
msgid "<b>Sign in</b>"
msgstr "<b>Prijava</b>"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "<i class=\"fa fa-arrow-right\"/> Back to edit mode"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Filter By:</span>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Group By:</span>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "<span class=\"small mr-1 navbar-text\">Sort By:</span>"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "<strong>Open </strong>"
msgstr ""

#. module: portal
#: model:mail.template,body_html:portal.mail_template_data_portal_welcome
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        ${object.user_id.name}\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img src=\"/logo.png?company=${object.user_id.company_id.id}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" alt=\"${object.user_id.company_id.name}\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear ${object.user_id.name or ''},<br/> <br/>\n"
"                        You have been given access to ${object.user_id.company_id.name}'s portal.<br/>\n"
"                        Your login account data is:\n"
"                        <ul>\n"
"                            <li>Username: ${object.user_id.login or ''}</li>\n"
"                            <li>Portal: <a href=\"${'portal_url' in ctx and ctx['portal_url'] or ''}\">${'portal_url' in ctx and ctx['portal_url'] or ''}</a></li>\n"
"                            <li>Database: ${'dbname' in ctx and ctx['dbname'] or ''}</li>\n"
"                        </ul>\n"
"                        You can set or change your password via the following url:\n"
"                        <ul>\n"
"                            <li><a href=\"${object.user_id.signup_url}\">${object.user_id.signup_url}</a></li>\n"
"                        </ul>\n"
"                        ${object.wizard_id.welcome_message or ''}\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    ${object.user_id.company_id.name}\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    ${object.user_id.company_id.phone}\n"
"                    % if object.user_id.company_id.email\n"
"                        | <a href=\"'mailto:%s' % ${object.user_id.company_id.email}\" style=\"text-decoration:none; color: #454748;\">${object.user_id.company_id.email}</a>\n"
"                    % endif\n"
"                    % if object.user_id.company_id.website\n"
"                        | <a href=\"'%s' % ${object.user_id.company_id.website}\" style=\"text-decoration:none; color: #454748;\">\n"
"                        ${object.user_id.company_id.website}\n"
"                    </a>\n"
"                    % endif\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=portalinvite\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_warning
#: model:ir.model.fields,field_description:portal.field_portal_share__access_warning
msgid "Access warning"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add a note"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Add contacts to share the document..."
msgstr ""

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_share__note
msgid "Add extra content to display in the email"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Apply"
msgstr "Primjeni"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_archive_groups
msgid "Archives"
msgstr "Arhive"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:26
#, python-format
msgid "Avatar"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Cancel"
msgstr "Otkaži"

#. module: portal
#: code:addons/portal/controllers/portal.py:220
#, python-format
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "City"
msgstr "Grad"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:12
#, python-format
msgid "Clear"
msgstr "Očisti"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:27
#, python-format
msgid "Click here to see your document."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
msgid "Close"
msgstr "Zatvori"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Company Name"
msgstr "Naziv firme"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid ""
"Confirm\n"
"                                <span class=\"fa fa-long-arrow-right\"/>"
msgstr ""
"Potvrdi\n"
" <span class=\"fa fa-long-arrow-right\"/>"

#. module: portal
#: model:ir.model,name:portal.model_res_partner
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__partner_id
msgid "Contact"
msgstr "Kontakt"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Contacts"
msgstr "Kontakti"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Country"
msgstr "Država"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Country..."
msgstr "Država..."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__create_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_mixin__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "Dear"
msgstr "Dragi"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__display_name
#: model:ir.model.fields,field_description:portal.field_portal_share__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard__display_name
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:14
#, python-format
msgid "Draw your signature"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:41
#, python-format
msgid "Due in %d days"
msgstr "Zakazano za %d dana"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_sidebar.js:39
#, python-format
msgid "Due today"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__email
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Email"
msgstr "E-Mail"

#. module: portal
#: model:ir.model,name:portal.model_mail_thread
msgid "Email Thread"
msgstr "Nit e-pošte"

#. module: portal
#: model:ir.actions.act_window,name:portal.partner_wizard_action
#: model:ir.model,name:portal.model_portal_wizard
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "Grant Portal Access"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
msgid "Home"
msgstr "Početna"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__id
#: model:ir.model.fields,field_description:portal.field_portal_share__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard__id
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__id
msgid "ID"
msgstr "ID"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__in_portal
msgid "In Portal"
msgstr ""

#. module: portal
#: code:addons/portal/controllers/portal.py:201
#, python-format
msgid "Invalid Email! Please enter a valid email address."
msgstr "Nepravilan email! Molimo unesite validnu email adresu."

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__welcome_message
msgid "Invitation Message"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin____last_update
#: model:ir.model.fields,field_description:portal.field_portal_share____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard____last_update
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_uid
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard__write_date
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:21
#, python-format
msgid "Leave a comment"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__share_link
msgid "Link"
msgstr "Veza"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__user_id
msgid "Login User"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Logout"
msgstr "Odjava"

#. module: portal
#: model:ir.model,name:portal.model_mail_message
msgid "Message"
msgstr "Poruka"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.my_account_link
msgid "My Account"
msgstr "Moj nalog"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:92
#: model_terms:ir.ui.view,arch_db:portal.pager
#, python-format
msgid "Next"
msgstr "Slijedeće"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__note
msgid "Note"
msgstr "Zabilješka"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
msgid "Odoo"
msgstr "Odoo"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Odoo Logo"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:39
#, python-format
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Phone"
msgstr "Telefon"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_url
msgid "Portal Access URL"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_mixin
msgid "Portal Mixin"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_share
msgid "Portal Sharing"
msgstr ""

#. module: portal
#: model:ir.model,name:portal.model_portal_wizard_user
msgid "Portal User Config"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.frontend_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "Powered by"
msgstr "Podržano od strane"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.pager
msgid "Prev"
msgstr "Prethodno"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:84
#, python-format
msgid "Previous"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/js/portal_chatter.js:109
#, python-format
msgid "Published on %s"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__partner_ids
msgid "Recipients"
msgstr "Primaoci"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_id
msgid "Related Document ID"
msgstr "Povezani ID dokumenta"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_share__res_model
msgid "Related Document Model"
msgstr "Povezani model dokumenta"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Search"
msgstr "Pretraži"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_mixin__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid ""
"Select which contacts should belong to the portal in the list below.\n"
"                        The email address of each selected contact must be valid and unique.\n"
"                        If necessary, you can fix any contact's email address directly in the list."
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:42
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
#, python-format
msgid "Send"
msgstr "Pošalji"

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:100
#, python-format
msgid "Several contacts have the same email: "
msgstr ""

#. module: portal
#: model:ir.actions.act_window,name:portal.portal_share_action
#: model_terms:ir.ui.view,arch_db:portal.portal_share_wizard
msgid "Share Document"
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:97
#, python-format
msgid "Some contacts don't have a valid email: "
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:103
#, python-format
msgid "Some contacts have the same email as an existing portal user:"
msgstr ""

#. module: portal
#: code:addons/portal/controllers/portal.py:224
#, python-format
msgid "Some required fields are empty."
msgstr "Neka od obaveznih polja su prazna."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "State / Province"
msgstr "Rep./Fed."

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Street"
msgstr "Ulica"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:25
#, python-format
msgid "Thank You !"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:12
#, python-format
msgid "There are no comments for now."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_back_in_edit_mode
msgid "This is a preview of the customer portal."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.wizard_view
msgid "This text is included in the email sent to new portal users."
msgstr ""

#. module: portal
#: model:ir.model.fields,help:portal.field_portal_wizard__welcome_message
msgid "This text is included in the email sent to new users of the portal."
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:106
#, python-format
msgid ""
"To resolve this error, you can: \n"
"- Correct the emails of the relevant contacts\n"
"- Grant access only to contacts with unique emails"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_searchbar
msgid "Toggle filters"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard__user_ids
msgid "Users"
msgstr "Korisnici"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "VAT Number"
msgstr "PDV Broj"

#. module: portal
#: model:ir.model.fields,field_description:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,field_description:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,field_description:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,field_description:portal.field_res_users__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: portal
#: model:ir.model.fields,help:portal.field_account_analytic_account__website_message_ids
#: model:ir.model.fields,help:portal.field_calendar_event__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_blacklist__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_channel__website_message_ids
#: model:ir.model.fields,help:portal.field_mail_thread__website_message_ids
#: model:ir.model.fields,help:portal.field_res_partner__website_message_ids
#: model:ir.model.fields,help:portal.field_res_users__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: portal
#: model:ir.model.fields,field_description:portal.field_portal_wizard_user__wizard_id
msgid "Wizard"
msgstr "Čarobnjak"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:31
#, python-format
msgid "Write a message..."
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_share.py:59
#: code:addons/portal/wizard/portal_share.py:71
#, python-format
msgid "You are invited to access %s"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_share_template
msgid "You have been invited to access the following document:"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:22
#, python-format
msgid "You must be"
msgstr ""

#. module: portal
#: code:addons/portal/wizard/portal_wizard.py:175
#, python-format
msgid ""
"You must have an email address in your User Preferences to send emails."
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Your Contact Details"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_breadcrumbs
#: model_terms:ir.ui.view,arch_db:portal.portal_layout
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Your Details"
msgstr "Vaši detalji"

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_home
msgid "Your Documents"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_signature.xml:6
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
#, python-format
msgid "Your Name"
msgstr "Vaše ime"

#. module: portal
#: model:mail.template,subject:portal.mail_template_data_portal_welcome
msgid "Your Odoo account at ${object.user_id.company_id.name}"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_contact
msgid "Your contact"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "Zip / Postal Code"
msgstr "Poštanski broj"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:56
#, python-format
msgid "avatar"
msgstr ""

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:9
#, python-format
msgid "comment"
msgstr "komentar"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:10
#, python-format
msgid "comments"
msgstr "komentari"

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:22
#, python-format
msgid "logged in"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_record_sidebar
msgid "odoo"
msgstr ""

#. module: portal
#: model_terms:ir.ui.view,arch_db:portal.portal_my_details
msgid "select..."
msgstr "odaberi..."

#. module: portal
#. openerp-web
#: code:addons/portal/static/src/xml/portal_chatter.xml:22
#, python-format
msgid "to post a comment."
msgstr ""
