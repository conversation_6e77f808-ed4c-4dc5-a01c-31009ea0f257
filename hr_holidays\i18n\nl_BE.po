# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_holidays
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important &gt;&lt;/td&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important /&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important/&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 10px\" &gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "!important; font-size: 8px; min-width: 18px\"&gt;"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:123
#, python-format
msgid "%g remaining out of %g"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:333
#, python-format
msgid "%s : %.2f day(s)"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:335
#, python-format
msgid "%s on %s : %.2f day(s)"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/td&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;/th&gt;"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid ""
"&lt;td class=\"text-center oe_leftfit oe_rightfit\" style=\"background-"
"color:"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;td style=background-color:"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "&lt;th class=\"text-center\" colspan="
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_kanban
msgid "<small class=\"text-muted\">from</small>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_kanban
msgid "<small class=\"text-muted\">to</small>"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "<strong>Departments and Employees</strong>"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_show_leaves
msgid "Able to see Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid "Absence"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department_absence_of_today
msgid "Absence by Today"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
msgid ""
"Absent Employee(s), Whose leaves request are either confirmed or validated "
"on today"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_employee_action_from_department
msgid "Absent Employees"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_is_absent_totay
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_employee_view_search
msgid "Absent Today"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_active
msgid "Active"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active Leaves and Allocations"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Active Types"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Add a reason..."
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_dashboard
msgid "All Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_holidays_leaves_assign_legal
msgid "Allocate Leaves for Employees"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree_customize
msgid "Allocated Days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_number_of_days_temp
msgid "Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_holiday_type
msgid "Allocation Mode"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,type:0
#: model:ir.actions.act_window,name:hr_holidays.open_allocation_holidays
#: model:ir.actions.act_window,name:hr_holidays.request_approve_allocation
#: model:ir.ui.menu,name:hr_holidays.menu_open_allocation_holidays
msgid "Allocation Request"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree_customize
msgid "Allocation Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_holidays_action_allocation_from_department
msgid "Allocation Requests to Approve"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr.py:129
#, python-format
msgid "Allocation for %s"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:337
#, python-format
msgid "Allocation of %s : %.2f day(s) To %s"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department_allocation_to_approve_count
msgid "Allocation to Approve"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Allocations"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_limit
msgid "Allow to Override Limit"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Analyze from"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_double_validation
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_double_validation
msgid "Apply Double Validation"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_evaluation_report_graph
msgid "Appraisal Analysis"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:580
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
#, python-format
msgid "Approve"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0 selection:hr.holidays,state:0
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
#: model:mail.message.subtype,name:hr_holidays.mt_holidays_approved
msgid "Approved"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Approved Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_leaves_assign_tree_view
msgid "Assign Leaves"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Black"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Blue"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
msgid "Both Approved and Confirmed"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Brown"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,holiday_type:0
msgid "By Employee"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,holiday_type:0
msgid "By Employee Tag"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_holiday_type
msgid ""
"By Employee: Allocation/Request for individual Employee, By Employee Tag: "
"Allocation/Request for group of employees in category"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_can_reset
msgid "Can reset"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Cancel"
msgstr "Annuleren"

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0 selection:hr.holidays,state:0
msgid "Cancelled"
msgstr "Geannuleerd"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Category"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_category_id
msgid "Category of Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_type
msgid ""
"Choose 'Leave Request' if someone wants to take an off-day. \n"
"Choose 'Allocation Request' if you want to increase the number of leaves available for someone"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.open_allocation_holidays
msgid "Click here to create a new leave allocation request."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.open_department_holidays_allocation_approve
msgid "Click here to create a new leave allocation."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.open_company_allocation
#: model_terms:ir.actions.act_window,help:hr_holidays.open_department_holidays_approve
#: model_terms:ir.actions.act_window,help:hr_holidays.open_employee_leaves
msgid "Click here to create a new leave request."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.open_ask_holidays
msgid "Click to create a new leave request."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Color"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_color_name
msgid "Color in Report"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Comment by Manager"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_company_id
msgid "Company"
msgstr "Bedrijf"

#. module: hr_holidays
#: model:hr.holidays.status,name:hr_holidays.holiday_status_comp
msgid "Compensatory Days"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_configuration
msgid "Configuration"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Confirm"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.summary.dept,holiday_type:0
#: selection:hr.holidays.summary.employee,holiday_type:0
#: model:mail.message.subtype,name:hr_holidays.mt_holidays_confirmed
msgid "Confirmed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_create_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_create_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_current_leave_state
msgid "Current Leave Status"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_current_leave_id
msgid "Current Leave Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Current Year"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_dashboard
msgid "Dashboard"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_department_id
msgid "Department"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_department_holidays_approve
msgid "Department Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_department_holidays_allocation_approve
msgid "Department Leaves Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_depts
msgid "Department(s)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_name
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Description"
msgstr "Omschrijving"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_display_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_display_name
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Duration"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_employee
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_name
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_leaves_assign_tree_view
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_category_id
msgid "Employee Tag"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_employee
msgid "Employee's Leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_emp
msgid "Employee(s)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_date_to
msgid "End Date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid ""
"Filters only on allocations and requests that belong to an leave type that "
"is 'active' (active field is True)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_first_approver_id
msgid "First Approval"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/report/holidays_summary_report.py:112
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_date_from
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_date_from
msgid "From"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_leave_date_from
msgid "From Date"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_payslip_status
msgid ""
"Green this button when the leave has been taken into account in the payslip."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_report_note
msgid "HR Comments"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_dept
msgid "HR Leaves Summary Report By Department"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_summary_employee
msgid "HR Leaves Summary Report By Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_id
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary_id
msgid "ID"
msgstr "ID"

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_active
msgid ""
"If the active field is set to false, it will allow you to hide the leave "
"type without removing it."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_limit
msgid ""
"If you select this check box, the system allows the employees to take more "
"leaves than the available ones for this type and will not take them into "
"account for the \"Remaining Legal Leaves\" defined on the employee form."
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Ivory"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays___last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user___last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status___last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept___last_update
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee___last_update
#: model:ir.model.fields,field_description:hr_holidays.field_report_hr_holidays_report_holidayssummary___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_write_uid
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_write_date
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Lavender"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays
msgid "Leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_resource_calendar_leaves
msgid "Leave Detail"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_employee_leaves
#: model:ir.ui.menu,name:hr_holidays.menu_open_employee_leave
msgid "Leave Details"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,type:0
#: model:ir.model.fields,field_description:hr_holidays.field_resource_calendar_leaves_holiday_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_new_calendar
msgid "Leave Request"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.hr_holidays_action_request_from_department
msgid "Leave Request to Approve"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday
msgid "Leave Requests"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_status
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_holiday_status_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_leave_type
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_name
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_dept_holiday_type
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_normal_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_status_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_status_search
msgid "Leave Type"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_view_holiday_status
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_status_search
msgid "Leave Types"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:439
#, python-format
msgid "Leave request must be confirmed (\"To Approve\") in order to approve it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:475
#, python-format
msgid "Leave request must be confirmed in order to approve it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:535
#, python-format
msgid "Leave request must be confirmed or validated in order to refuse it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:422
#, python-format
msgid ""
"Leave request must be in Draft state (\"To Submit\") in order to confirm it."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:407
#, python-format
msgid ""
"Leave request state must be \"Refused\" or \"To Approve\" in order to reset "
"to Draft."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department_leave_to_approve_count
msgid "Leave to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.act_hr_employee_holiday_request
#: model:ir.ui.menu,name:hr_holidays.menu_hr_available_holidays_report_tree
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_root
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_approve
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_open_department_leave_allocation_approve
msgid "Leaves Allocation"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_leaves_taken
msgid "Leaves Already Taken"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_available_holidays_report
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_leaves_analysis
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_leaves_analysis_filtered
msgid "Leaves Analysis"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Leaves Left"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.open_ask_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_open_ask_holidays_new
msgid "Leaves Request"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_employee
#: model:ir.actions.act_window,name:hr_holidays.open_company_allocation
#: model:ir.actions.report,name:hr_holidays.action_report_holidayssummary
#: model:ir.ui.menu,name:hr_holidays.menu_open_company_allocation
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_pivot
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_simple
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Leaves Summary"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Leaves Taken:"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.action_hr_holidays_summary_dept
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
msgid "Leaves by Department"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_remaining_leaves_user_graph
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_remaining_leaves_user_tree
msgid "Leaves by Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Leaves of Your Team Member"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_approvals
msgid "Leaves to Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.act_hr_employee_holiday_request_approved
msgid "Leaves to be reported in Payslip"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Leaves."
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_holidays_approved
msgid "Leaves/Allocation Approved"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_holidays_refused
msgid "Leaves/Allocation Refused"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_holidays_confirmed
msgid "Leaves/Allocations Confirmed"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_department_holidays_first_validated
msgid "Leaves/Allocations First Approval"
msgstr ""

#. module: hr_holidays
#: model:hr.holidays.status,name:hr_holidays.holiday_status_cl
msgid "Legal Leaves 2017"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Blue"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Coral"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Cyan"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Green"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Pink"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Salmon"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Light Yellow"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_linked_request_ids
msgid "Linked Requests"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Magenta"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_manager_id
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
#: model:res.groups,name:hr_holidays.group_hr_holidays_manager
msgid "Manager"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holiday_status_view_kanban
msgid "Max Leaves:"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_max_leaves
msgid "Maximum Allowed"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_remaining_leaves
msgid "Maximum Leaves Allowed - Leaves Already Taken"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_virtual_remaining_leaves
msgid ""
"Maximum Leaves Allowed - Leaves Already Taken - Leaves Waiting Approval"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_meeting_id
msgid "Meeting"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_categ_id
msgid "Meeting Type"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday
msgid "Mode"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Month"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Department Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_my_leaves
msgid "My Leaves"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Requests"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "My Team Leaves"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "New"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_number_of_days
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_simple
msgid "Number of Days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_leaves_count
msgid "Number of Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_number_of_days_temp
msgid ""
"Number of days of the leave request according to your working schedule."
msgstr ""

#. module: hr_holidays
#: model:res.groups,name:hr_holidays.group_hr_holidays_user
msgid "Officer"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_categ_id
msgid ""
"Once a leave is validated, Odoo will create a corresponding meeting of this "
"type in the calendar."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.open_ask_holidays
msgid ""
"Once you have recorded your leave request, it will be sent\n"
"                to a manager for validation. Be sure to set the right leave\n"
"                type (recuperation, legal leaves, sickness) and the exact\n"
"                number of open days related to your leave."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:477
#, python-format
msgid "Only an HR Manager can apply the second approval on leave requests."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:405
#, python-format
msgid "Only an HR Manager or the concerned employee can reset to draft."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:428
#: code:addons/hr_holidays/models/hr_holidays.py:466
#, python-format
msgid "Only an HR Officer or Manager can approve leave requests."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:552
#, python-format
msgid "Only an HR Officer or Manager can refuse leave requests."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_status_form
msgid "Options"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_parent_id
msgid "Parent"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_dept
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_summary_employee
msgid "Print"
msgstr "Afdrukken"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_notes
msgid "Reasons"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Red"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:583
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
#, python-format
msgid "Refuse"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0 selection:hr.holidays,state:0
#: model:mail.message.subtype,name:hr_holidays.mt_holidays_refused
msgid "Refused"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_allocation_tree_customize
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_employee
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holiday_simple
msgid "Remaining Days"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_remaining_leaves
msgid "Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_remaining_leaves
#: model_terms:ir.ui.view,arch_db:hr_holidays.hr_holidays_leaves_assign_tree_view
msgid "Remaining Legal Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_no_of_leaves
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
msgid "Remaining leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_payslip_status
msgid "Reported in last payslips"
msgstr ""

#. module: hr_holidays
#: model:ir.ui.menu,name:hr_holidays.menu_hr_holidays_report
msgid "Reporting"
msgstr "Rapportages"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_type
msgid "Request Type"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_holidays_approved
msgid "Request approved"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_holidays_confirmed
msgid "Request created and waiting confirmation"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_holidays_refused
msgid "Request refused"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,description:hr_holidays.mt_holidays_first_validated
msgid "Request validated, waiting second validation"
msgstr ""

#. module: hr_holidays
#: model:ir.actions.act_window,name:hr_holidays.request_approve_holidays
msgid "Requests to Approve"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Reset to Draft"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Search Leave"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_holidays_status_filter
msgid "Search Leave Type"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,state:0
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_second_approver_id
msgid "Second Approval"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:596
#, python-format
msgid "See Allocation"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:596
#, python-format
msgid "See Leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_summary_employee_holiday_type
msgid "Select Leave Type"
msgstr ""

#. module: hr_holidays
#: model:hr.holidays.status,name:hr_holidays.holiday_status_sl
msgid "Sick Leaves"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_date_from
msgid "Start Date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Start Month"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_state
msgid "Status"
msgstr "Status"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "Sum"
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.holidays:0
msgid ""
"The employee or employee category of this request is missing. Please make "
"sure that your user login is linked to an employee."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr.py:117
#, python-format
msgid ""
"The feature behind the field 'Remaining Legal Leaves' can only be used when there is only one leave type with the option 'Allow to Override Limit' unchecked. (%s Found). Otherwise, the update is ambiguous as we cannot decide on which leave type the update has to be done. \n"
" You may prefer to use the classic menus 'Leave Requests' and 'Allocation Requests' located in Leaves Application to manage the leave days of the employees if the configuration does not allow to use this field."
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.holidays:0
msgid "The number of days must be greater than 0."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:259
#, python-format
msgid ""
"The number of remaining leaves is not sufficient for this leave type.\n"
"Please verify also the leaves waiting for validation."
msgstr ""

#. module: hr_holidays
#: sql_constraint:hr.holidays:0
msgid "The start date must be anterior to the end date."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_state
msgid ""
"The status is set to 'To Submit', when a leave request is created.\n"
"The status is 'To Approve', when leave request is confirmed by user.\n"
"The status is 'Refused', when leave request is refused by manager.\n"
"The status is 'Approved', when leave request is approved by manager."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_first_approver_id
msgid "This area is automatically filled by the user who validate the leave"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_second_approver_id
msgid ""
"This area is automaticly filled by the user who validate the leave with "
"second level (If Leave type need second validation)"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_color_name
msgid ""
"This color will be used in the leaves summary located in Reporting > Leaves "
"by Department."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_leaves_taken
msgid ""
"This value is given by the sum of all leaves requests with a negative value."
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_max_leaves
msgid ""
"This value is given by the sum of all leaves requests with a positive value."
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,state:0
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "To Approve"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_employee_leave_date_to
msgid "To Date"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "To Do"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "To Report in Payslip"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays,state:0
msgid "To Submit"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_department_total_employee
msgid "Total Employee"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_hr_holidays_remaining_leaves_user
msgid "Total holidays by type"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_employee_remaining_leaves
msgid ""
"Total number of legal leaves allocated to this employee, change this value "
"to create allocation/leave request. Total based on all the leave types "
"without overriding limit."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Type"
msgstr ""

#. module: hr_holidays
#: model:hr.holidays.status,name:hr_holidays.holiday_status_unpaid
msgid "Unpaid"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_remaining_leaves_user_user_id
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_user_id
msgid "User"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "Validate"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_filter
msgid "Validator"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Violet"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,field_description:hr_holidays.field_hr_holidays_status_virtual_remaining_leaves
msgid "Virtual Remaining Leaves"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "Waiting Approval"
msgstr ""

#. module: hr_holidays
#: selection:hr.employee,current_leave_state:0
msgid "Waiting Second Approval"
msgstr ""

#. module: hr_holidays
#: model:mail.message.subtype,name:hr_holidays.mt_holidays_first_validated
msgid "Waiting Second Validation"
msgstr ""

#. module: hr_holidays
#: selection:hr.holidays.status,color_name:0
msgid "Wheat"
msgstr ""

#. module: hr_holidays
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_double_validation
#: model:ir.model.fields,help:hr_holidays.field_hr_holidays_status_double_validation
msgid ""
"When selected, the Allocation/Leave Requests for this type require a second "
"validation to be approved."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.actions.act_window,help:hr_holidays.hr_holidays_leaves_assign_legal
msgid ""
"You can assign remaining Legal Leaves for each employee, Odoo\n"
"               will automatically create and validate allocation requests."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:249
#, python-format
msgid "You can not have 2 leaves that overlaps on same day!"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:375
#, python-format
msgid "You cannot delete a leave which is in %s state."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr.py:140
#, python-format
msgid "You cannot reduce validated allocation requests"
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/models/hr_holidays.py:356
#: code:addons/hr_holidays/models/hr_holidays.py:367
#, python-format
msgid ""
"You cannot set a leave request as '%s'. Contact a human resource manager."
msgstr ""

#. module: hr_holidays
#: code:addons/hr_holidays/wizard/hr_holidays_summary_department.py:27
#, python-format
msgid "You have to select at least one Department. And try again."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_employee_form_leave_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays.view_hr_holidays_kanban
msgid "days"
msgstr "dagen"

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.edit_holiday_new
msgid "e.g. Report to the next month..."
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "of the"
msgstr ""

#. module: hr_holidays
#: model:ir.model,name:hr_holidays.model_report_hr_holidays_report_holidayssummary
msgid "report.hr_holidays.report_holidayssummary"
msgstr ""

#. module: hr_holidays
#: model_terms:ir.ui.view,arch_db:hr_holidays.report_holidayssummary
msgid "to"
msgstr ""
