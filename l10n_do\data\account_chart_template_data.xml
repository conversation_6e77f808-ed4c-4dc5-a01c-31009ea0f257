<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Chart Template for Defaults -->
    <record id="do_chart_template" model="account.chart.template">
        <field eval="True" name="use_anglo_saxon"/>
        <field name="property_account_receivable_id" ref="do_niif_11030201"/>
        <field name="property_account_payable_id" ref="do_niif_21010200"/>
        <field name="property_account_income_categ_id" ref="do_niif_41010100"/>
        <field name="property_account_expense_categ_id" ref="do_niif_51010100"/>
        <field name="property_stock_account_input_categ_id" ref="do_niif_21021200"/>
        <field name="property_stock_account_output_categ_id" ref="do_niif_11050600"/>
        <field name="property_stock_valuation_account_id" ref="do_niif_11050100"/>
        <field name="expense_currency_exchange_account_id" ref="do_niif_52070800"/>
        <field name="income_currency_exchange_account_id" ref="do_niif_42040100"/>
        <field name="default_pos_receivable_account_id" ref="do_niif_11030210" />
    </record>
</odoo>
