<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="view_tax_form_inherited" model="ir.ui.view">
            <field name="name">account.tax.form.inherited</field>
            <field name="model">account.tax</field>
            <field name="inherit_id" ref="account.view_tax_form" />
            <field name="arch" type="xml">
                 <xpath expr="//field[@name='children_tax_ids']" position="before">
                    <group attrs="{'invisible': [('amount_type','!=','code')]}">
                        <group>
                            <field name="python_compute" attrs="{'required':[('amount_type','=','code')]}" />
                        </group>
                        <group>
                            <field name="python_applicable" attrs="{'required':[('amount_type','=','code')]}" />
                        </group>
                    </group>
                </xpath>
            </field>
        </record>
</odoo>
