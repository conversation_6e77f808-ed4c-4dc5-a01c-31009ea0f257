<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ua" model="res.partner">
        <field name="name">UA Company</field>
        <field name="vat"></field>
        <field name="street">10/2 Мечникова вулиця</field>
        <field name="city">Київ</field>
        <field name="country_id" ref="base.ua"/>
        
        <field name="zip">01133</field>
        <field name="phone">+380 50 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.uaexample.com</field>
    </record>

    <record id="demo_company_ua" model="res.company">
        <field name="name">UA Company</field>
        <field name="partner_id" ref="partner_demo_company_ua"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ua')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ua.demo_company_ua'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[ref('l10n_ua.l10n_ua_psbo_chart_template')]"/>
        <value model="res.company" eval="obj().env.ref('l10n_ua.demo_company_ua')"/>
    </function>
</odoo>
