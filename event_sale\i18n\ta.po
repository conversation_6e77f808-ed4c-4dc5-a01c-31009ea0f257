# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event_sale
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-11 13:18+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Tamil (http://www.transifex.com/odoo/odoo-9/language/ta/)\n"
"Language: ta\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_sale
#: code:addons/event_sale/models/event.py:170
#, python-format
msgid " with ticket %s"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Apply"
msgstr "விண்ணப்பி"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_asset_category_id
msgid "Asset Type"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
msgid "Attendee"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_availability
msgid "Available Seat"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_available
msgid "Available Seats"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line_event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line_event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_2_product
#: model:product.template,name:event_sale.event_2_product_product_template
msgid "Conference on Business Applications"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_create_uid
msgid "Created by"
msgstr "உருவாக்கியவர்"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_create_date
msgid "Created on"
msgstr ""
"உருவாக்கப்பட்ட \n"
"தேதி"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_deferred_revenue_category_id
msgid "Deferred Revenue Type"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket_seats_max
msgid ""
"Define the number of available tickets. If you have too much registrations "
"you will not be able to sell tickets anymore. Set 0 to ignore this rule set "
"as unlimited."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product_event_ok
#: model:ir.model.fields,help:event_sale.field_product_template_event_ok
msgid ""
"Determine if a product needs to create automatically an event registration "
"at the confirmation of a sales order line."
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_display_name
msgid "Display Name"
msgstr "காட்சி பெயர்"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_editor_id
msgid "Editor id"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_email
msgid "Email"
msgstr "மின்னஞ்சல்"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_id
msgid "Event"
msgstr ""

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_event_ok
#: model:ir.model.fields,field_description:event_sale.field_product_template_event_ok
#: model:product.product,name:event_sale.product_product_event
#: model:product.template,name:event_sale.product_product_event_product_template
msgid "Event Subscription"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_event_ticket_ids
#: model:ir.model.fields,field_description:event_sale.field_event_registration_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_ticket_id
msgid "Event Ticket"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_event_ticket_ids
msgid "Event Tickets"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_type_id
msgid "Event Type"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_1_product
#: model:product.template,name:event_sale.event_1_product_product_template
msgid "Functional Webinar"
msgstr ""

#. module: event_sale
#: model:event.type,name:event_sale.event_type
msgid "Generic Events"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_id
msgid "ID"
msgstr "ID"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_is_expired
msgid "Is Expired"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket___last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor___last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line___last_update
msgid "Last Modified on"
msgstr "கடைசியாக திருத்திய"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_write_uid
msgid "Last Updated by"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_write_date
msgid "Last Updated on"
msgstr "கடைசியாக புதுப்பிக்கப்பட்டது"

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_max
msgid "Maximum Available Seats"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_name
msgid "Name"
msgstr "பெயர்"

#. module: event_sale
#: code:addons/event_sale/models/event.py:131
#, python-format
msgid "No more available seats for the ticket"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:153
#, python-format
msgid "No more available seats for this ticket"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_0_product
#: model:product.template,name:event_sale.event_0_product_product_template
msgid "Open Days in Los Angeles"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_form
msgid "Origin"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_registration_id
msgid "Original Registration"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_phone
msgid "Phone"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_price
msgid "Price"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_price_reduce
msgid "Price Reduce"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_product_id
msgid "Product"
msgstr "தயாரிப்பு"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr "தயாரிப்பு டெம்ப்ளேட்"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_registration_ids
msgid "Registrations"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_event_registration_ids
msgid "Registrations to Edit"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_sale_order_id
msgid "Sale Order"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration_sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line_sale_order_line_id
msgid "Sale Order Line"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_deadline
msgid "Sales End"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_used
msgid "Seats used"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product_event_type_id
#: model:ir.model.fields,help:event_sale.field_product_template_event_type_id
msgid ""
"Select event types so when we use this product in sales order lines, it will "
"filter events of this type only."
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Skip"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration_sale_order_id
msgid "Source Sale Order"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_1
#: model:event.event.ticket,name:event_sale.event_1_ticket_1
#: model:event.event.ticket,name:event_sale.event_2_ticket_1
#: model:event.event.ticket,name:event_sale.event_3_ticket_1
msgid "Standard"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:21
#, python-format
msgid "Subscription"
msgstr ""

#. module: event_sale
#: model:product.product,name:event_sale.event_3_product
#: model:product.template,name:event_sale.event_3_product_product_template
msgid "Technical Training"
msgstr ""

#. module: event_sale
#: code:addons/event_sale/models/event.py:168
#, python-format
msgid ""
"The registration has been created for event %(event_name)s%(ticket)s from "
"sale order %(order)s"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_ticket_search
msgid "Ticket Type"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Tickets"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product_event_type_id
#: model:ir.model.fields,field_description:event_sale.field_product_template_event_type_id
msgid "Type of Event"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket_seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event_sale
#: selection:event.event.ticket,seats_availability:0
msgid "Unlimited"
msgstr ""

#. module: event_sale
#: model:event.event.ticket,name:event_sale.event_0_ticket_2
#: model:event.event.ticket,name:event_sale.event_2_ticket_2
#: model:event.event.ticket,name:event_sale.event_3_ticket_2
msgid "VIP"
msgstr ""

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line_event_ok
msgid "event_ok"
msgstr ""

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "registration.editor"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "registration.editor.line"
msgstr ""
