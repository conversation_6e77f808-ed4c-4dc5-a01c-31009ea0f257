# -*- coding: utf-8 -*-

{
    'name': 'Libya Payroll',
    'category': 'Human Resources/Payroll',
    'depends': ['hr_payroll_community','hr_employees_masarat', 'hr_attendance'],

    'data': [
        'reports/report.xml',
        'security/ir.model.access.csv',
        'data/structure_types.xml',
        'views/hr_contract_view.xml',
        'wizards/payroll_allowances.xml',
        'views/payrol_allowances_report.xml',
        'wizards/payroll_insurance.xml',
        'wizards/payroll_deduction.xml',
        'views/payroll_insurance_report.xml',
        'views/payroll_deduction_report.xml',
        'wizards/payroll_position.xml',
        'views/payroll_position_report.xml',
        'views/payroll_expenses_report.xml',
        'wizards/payroll_expenses.xml',
        'views/payrol_expenses_all_report.xml',
        'wizards/income_tax.xml',
        'views/payroll_income_taxes_report.xml',
        'wizards/payroll_cheque.xml',
        'views/cheque_report.xml',
        'wizards/payroll_allowances_withoutaccount.xml',
        'views/payroll_allowances_withoutaccount_report.xml',
        'views/hr_employee.xml',
        'views/hr_payslip_edited.xml'
    ],
}

# 'views/hr_payslip_edited.xml'
#'views/hr_employee.xml',
#'views/bank_branch.xml',