# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* link_tracker
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>Nessel<PERSON>ch, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:23+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__absolute_url
msgid "Absolute URL"
msgstr "Absolute URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__label
msgid "Button label"
msgstr "Schaltflächenbezeichnung"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__campaign_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Campaign"
msgstr "Kampagne"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_click_action_statistics
msgid "Click Statistics"
msgstr "Klick-Statistik"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_click_ids
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.utm_campaign_view_kanban
msgid "Clicks"
msgstr "Klicks"

#. module: link_tracker
#: model:ir.model.constraint,message:link_tracker.constraint_link_tracker_code_code
msgid "Code must be unique."
msgstr "Code muss eindeutig sein."

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__link_code_ids
msgid "Codes"
msgstr "Codes"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__country_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Country"
msgstr "Land"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid "Create a link tracker"
msgstr "Einen Link-Tracker erstellen"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__create_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Creating a Link Tracker without URL is not possible"
msgstr "Es ist nicht möglich, einen Link-Tracker ohne URL zu erstellen"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__display_name
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url_host
msgid "Host of the short URL"
msgstr "Host der kurzen URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__id
msgid "ID"
msgstr "ID"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__ip
msgid "Internet Protocol"
msgstr "Internetprotokoll"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click____last_update
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_uid
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__write_date
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__link_id
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__link_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_search
msgid "Link"
msgstr "Link"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_form
msgid "Link Click"
msgstr "Link-Klick"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_graph
msgid "Link Clicks"
msgstr "Link-Klicks"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action
#: model:ir.model,name:link_tracker.model_link_tracker
#: model:ir.ui.menu,name:link_tracker.link_tracker_menu_main
msgid "Link Tracker"
msgstr "Link-Tracker"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_click
msgid "Link Tracker Click"
msgstr "Link-Tracker-Klick"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_link_tracker_code
msgid "Link Tracker Code"
msgstr "Link-Tracker-Code"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid ""
"Link Tracker values (URL, campaign, medium and source) must be unique (%s, "
"%s, %s, %s)."
msgstr ""
"Die Link-Tracker-Werte (URL, Kampagne, Medium und Quelle) müssen eindeutig "
"sein (%s, %s, %s, %s )."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_graph
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Links"
msgstr "Links"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_click_view_tree
msgid "Links Clicks"
msgstr "Link-Klicks"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mail-Rendering-Mixin"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__medium_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Medium"
msgstr "Medium"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_click_action_statistics
msgid "No data yet!"
msgstr "Noch keine Daten vorhanden!"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__count
msgid "Number of Clicks"
msgstr "Anzahl der Klicks"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_utm_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "Anzahl der durch die Kampagne generierten Klicks"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__title
msgid "Page Title"
msgstr "Seitentitel"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__redirected_url
msgid "Redirected URL"
msgstr "Umgeleitete URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_code__code
msgid "Short URL Code"
msgstr "Kurzer URL-Code"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__code
msgid "Short URL code"
msgstr "Kurzer URL-Code"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__source_id
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Source"
msgstr "Quelle"

#. module: link_tracker
#: model:ir.actions.act_window,name:link_tracker.link_tracker_action_campaign
msgid "Statistics of Clicks"
msgstr "Klickstatistik"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__url
msgid "Target URL"
msgstr "Ziel-URL"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__campaign_id
#: model:ir.model.fields,help:link_tracker.field_link_tracker_click__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Diese Bezeichnung hilft Ihnen bei der Analyse Ihres Kampagnenerfolgs, z. B.:"
" Herbstangebot, Weihnachtsangebot"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Dies ist die Kommunikationsmethode: z. B. Postkarte, E-Mail oder "
"Bannerwerbung"

#. module: link_tracker
#: model:ir.model.fields,help:link_tracker.field_link_tracker__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Dies ist die Quelle des Links, z. B. eine Suchmaschine, andere Webdomain "
"oder Name der E-Mail-Liste"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_search
msgid "Title and URL"
msgstr "Titel und URL"

#. module: link_tracker
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker__short_url
msgid "Tracked URL"
msgstr "Nachverfolgte URL"

#. module: link_tracker
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action
#: model_terms:ir.actions.act_window,help:link_tracker.link_tracker_action_campaign
msgid ""
"Trackers are used to collect count stat about click on links and generate "
"short URLs."
msgstr ""
"Tracker werden verwendet, um Zählstatistiken über angeklickte Links zu "
"sammeln und kurze URLs zu generieren."

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "URL"
msgstr "URL"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "UTM"
msgstr "UTM"

#. module: link_tracker
#: model:ir.model,name:link_tracker.model_utm_campaign
#: model:ir.model.fields,field_description:link_tracker.field_link_tracker_click__campaign_id
msgid "UTM Campaign"
msgstr "UTM-Kampagne"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_tree
msgid "Visit Page"
msgstr "Seite besuchen"

#. module: link_tracker
#: code:addons/link_tracker/models/link_tracker.py:0
#, python-format
msgid "Visit Webpage"
msgstr "Website besuchen"

#. module: link_tracker
#: model_terms:ir.ui.view,arch_db:link_tracker.link_tracker_view_form
msgid "Website Link"
msgstr "Website-Link"
