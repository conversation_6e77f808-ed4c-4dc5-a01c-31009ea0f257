<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="partner_info" name="Argentinean partner">

        <!-- show afip responsibility -->
        <div t-attf-class="form-group #{error.get('l10n_ar_afip_responsibility_type_id') and 'o_has_error' or ''} col-xl-6">
            <label class="col-form-label" for="l10n_ar_afip_responsibility_type_id">AFIP Responsibility</label>
            <t t-if="partner.can_edit_vat()">
                <select name="l10n_ar_afip_responsibility_type_id" t-attf-class="form-control #{error.get('l10n_ar_afip_responsibility_type_id') and 'is-invalid' or ''}">
                    <option value="">AFIP Responsibility...</option>
                    <t t-foreach="responsibility_types or []" t-as="resp_type">
                        <option t-att-value="resp_type.id" t-att-selected="resp_type.id == int(responsibility) if responsibility else resp_type.id == partner.l10n_ar_afip_responsibility_type_id.id">
                            <t t-esc="resp_type.name"/>
                        </option>
                    </t>
                </select>
            </t>
            <t t-else="">
                <p class="form-control" t-esc="partner.l10n_ar_afip_responsibility_type_id.name" readonly="1" title="Changing AFIP Responsibility type is not allowed once document(s) have been issued for your account. Please contact us directly for this operation."/>
                <input name="l10n_ar_afip_responsibility_type_id" class="form-control" t-att-value="partner.l10n_ar_afip_responsibility_type_id.id" type='hidden'/>
            </t>
        </div>

        <!-- show identification type -->
        <div t-attf-class="form-group #{error.get('l10n_latam_identification_type_id') and 'o_has_error' or ''} col-xl-6">
            <label class="col-form-label" for="l10n_latam_identification_type_id">Identification Type</label>
            <t t-if="partner.can_edit_vat()">
                <select name="l10n_latam_identification_type_id" t-attf-class="form-control #{error.get('l10n_latam_identification_type_id') and 'is-invalid' or ''}">
                    <option value="">Identification Type...</option>
                    <t t-foreach="identification_types or []" t-as="id_type">
                        <option t-att-value="id_type.id" t-att-selected="id_type.id == int(identification) if identification else id_type.id == partner.l10n_latam_identification_type_id.id">
                            <t t-esc="id_type.name"/>
                        </option>
                    </t>
                </select>
            </t>
            <t t-else="">
                <p class="form-control" t-esc="partner.l10n_latam_identification_type_id.name" readonly="1" title="Changing Identification type is not allowed once document(s) have been issued for your account. Please contact us directly for this operation."/>
                <input name="l10n_latam_identification_type_id" class="form-control" t-att-value="partner.l10n_latam_identification_type_id.id" type='hidden'/>
            </t>
        </div>

    </template>

    <template id="address_b2b" inherit_id="website_sale.address_b2b">
        <xpath expr="//input[@name='vat']/.." position="before">
            <t t-if="mode[1] == 'billing'" positon="inside">
                <t t-if="res_company.country_id.code == 'AR'">
                    <t t-set="partner" t-value="website_sale_order.partner_id"/>
                    <t t-call="l10n_ar_website_sale.partner_info"/>
                </t>
            </t>
        </xpath>
        <label for="vat" position="attributes">
            <attribute name="t-if">res_company.country_id.code != 'AR'</attribute>
        </label>
        <label for="vat" position="after">
            <label t-if="res_company.country_id.code == 'AR'" class="col-form-label label-optional" for="vat">Number</label>
        </label>
    </template>

</odoo>
