# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_lead_website
# 
# Translators:
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON> <w.war<PERSON><PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <judyta.ka<PERSON><PERSON><PERSON><PERSON>@openglobe.pl>, 2021
# <PERSON>z, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <tade<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
"1 kredyt jest zużywany na każdego odwiedzającego, który spełnia warunki "
"ruchu na stronie WWW i którego firmę można zidentyfikować. <br/>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Sygnały </span>"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Szanse </span>"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__active
msgid "Active"
msgstr "Aktywne"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Zarchiwizowane"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "Zasady generowania sygnałów CRM"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "CRM widok ujawniający"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""
"Wybierz, czy chcesz śledzić tylko firmy, czy też firmy i ich kontakty."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Firmy"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "Firmy i ich kontakty"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "Wielkość firmy"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "Maksymalny rozmiar firmy"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "Filtr kontaktów"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Kraje"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Data utworzenia"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "Utwórz regułę konwersji"

#. module: crm_iap_lead_website
#: model_terms:ir.actions.act_window,help:crm_iap_lead_website.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"Twórz reguły generowania sygnałów/szans B2B od odwiedzających Twoją stronę "
"WWW."

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "Śledzenie danych"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Enter Valid Regex."
msgstr "Wprowadź prawidłowe wyrażenie regularne."

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "Filtr włączony"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "Filtruj firmy na podstawie ich wielkości."

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "Rozmiar filtru"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "From"
msgstr "Od"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Wygenerowany sygnał/szansa"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Wysoki"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "Kredyty IAP"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "Adres IP"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "Gałęzie przemysłu"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule____last_update
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Sygnał"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Lead Data"
msgstr "Data sygnału"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "Informacje o generowaniu sygnałów"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "Reguła tworzenia sygnałów"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_view_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "Widoki generowania sygnałów"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid ""
"Lead Generation requires a GeoIP resolver which could not be found on your "
"system. Please consult https://pypi.org/project/GeoIP/."
msgstr ""
"Generowanie sygnałów wymaga resolwera GeoIP, którego nie można znaleźć w "
"systemie. Skontaktuj się z https://pypi.org/project/GeoIP/."

#. module: crm_iap_lead_website
#: model:ir.actions.server,name:crm_iap_lead_website.ir_cron_crm_reveal_lead_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_lead_website.ir_cron_crm_reveal_lead
#: model:ir.cron,name:crm_iap_lead_website.ir_cron_crm_reveal_lead
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr "Generowanie sygnałów: Generowanie sygnałów/szans"

#. module: crm_iap_lead_website
#: model:ir.model,name:crm_iap_lead_website.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Sygnał/Szansa"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"Pozostaw puste, aby zawsze dopasowywać. Odoo nie utworzy sygnału, jeśli nie "
"będzie pasował."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Niski"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Upewnij się czy musisz być zgodny z RODO aby przechowywać dane osobiste. "

#. module: crm_iap_lead_website
#: model:ir.model.constraint,message:crm_iap_lead_website.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "Dozwolonych jest maksymalnie 5 kontaktów!"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Medium"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Missing Library"
msgstr "Brakująca biblioteka"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "Nie odnaleziono"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Liczba kontaktów"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "Liczba wygenerowanych sygnałów"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "Liczba wygenerowanych szans"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"Tylko odwiedzający z następujących krajów zostaną przekształceni w "
"sygnały/szanse (przy użyciu GeoIP)."

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"Tylko odwiedzający z następujących stanów zostaną przekształceni w "
"sygnały/szanse."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Szansa"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "Data szansy"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "Warunki generowania szans"

#. module: crm_iap_lead_website
#: code:addons/crm_iap_lead_website/models/crm_reveal_rule.py:0
#, python-format
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Szansa utworzona przez generowanie potencjalnych sygnałów Odoo "

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "Inne role"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "Preferowana rola"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Priorytet"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Regex do śledzenia stron WWW. Pozostaw puste, aby śledzić całą stronę WWW, "
"lub /, aby kierować na stronę główną. Przykład: /page*, aby śledzić "
"wszystkie strony zaczynające się od /page"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "Ogranicz generowanie sygnałów do tej strony WWW."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Rola"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Reguła"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Nazwa reguły"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Zespół sprzedaży"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Sprzedawca"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "Wyszukaj regułę ujawniania CRM"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "Staż pracy"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "Stan"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "Stany"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Sufiks"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Tagi"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"Jest to liczba kontaktów, które należy śledzić, jeśli ich rola/staż "
"odpowiadają Twoim kryteriom. Ich dane pojawią się w wątku historii "
"wygenerowanych sygnałów/szans. Na każdy śledzony kontakt zużywany jest jeden"
" kredyt."

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"To zostanie to dodane w nazwie wygenerowanego potencjalnego sygnału, dzięki "
"czemu będzie można zidentyfikować sygnał/szansę wygenerowaną z tej reguły"

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "Do przetworzenia"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Typ"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "Wyrażenie URL"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Up to"
msgstr "Do"

#. module: crm_iap_lead_website
#: model:ir.model.fields,help:crm_iap_lead_website.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"Służy do porządkowania reguł o tym samym adresie URL i krajach. Reguły z "
"niższym numerem sekwencyjnym będą przetwarzane jako pierwsze."

#. module: crm_iap_lead_website
#: model:ir.model.fields.selection,name:crm_iap_lead_website.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Bardzo wysoki"

#. module: crm_iap_lead_website
#: model:ir.actions.act_window,name:crm_iap_lead_website.crm_reveal_rule_action
#: model:ir.ui.menu,name:crm_iap_lead_website.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "Zasady wizyt do Sygnałów"

#. module: crm_iap_lead_website
#: model:ir.model.fields,field_description:crm_iap_lead_website.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Strona internetowa"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "Warunki ruchu na stronie WWW"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr "dodatkowe kredyty są zużywane, jeśli firma spełnia tę zasadę."

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "np. /page"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "np. odwiedzający z USA"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "employees"
msgstr "pracownicy"

#. module: crm_iap_lead_website
#: model_terms:ir.ui.view,arch_db:crm_iap_lead_website.crm_reveal_rule_form
msgid "to"
msgstr "do"
