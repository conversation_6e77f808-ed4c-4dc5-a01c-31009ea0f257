# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_live
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_content
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"pl-2\">Loading Video...</span>"
msgstr ""

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_aside
msgid "Chat"
msgstr "چت"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__is_youtube_replay
msgid ""
"Check this option if the video is already available on Youtube to avoid "
"showing 'Direct' options (Chat, ...)"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_url
msgid ""
"Configure this URL so that event attendees can see your Track in video!"
msgstr ""

#. module: website_event_track_live
#: model:ir.model,name:website_event_track_live.model_event_track
msgid "Event Track"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_id
msgid ""
"Extracted from the video URL and used to infer various links "
"(embed/thumbnail/...)"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_chat_available
msgid "Is Chat Available"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_replay
msgid "Is Youtube Replay"
msgstr ""

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.tracks_display_list
msgid "Replay"
msgstr ""

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Replay Video"
msgstr ""

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Starts in"
msgstr ""

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Up Next:"
msgstr ""

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "You just watched:"
msgstr ""

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_url
msgid "Youtube Video URL"
msgstr "URL ویدیوی YouTube"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_id
msgid "Youtube video ID"
msgstr ""
