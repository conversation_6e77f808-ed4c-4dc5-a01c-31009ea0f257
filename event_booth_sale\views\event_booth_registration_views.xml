<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="event_booth_registration_view_form" model="ir.ui.view">
        <field name="name">event.booth.registration.view.form</field>
        <field name="model">event.booth.registration</field>
        <field name="arch" type="xml">
            <form string="Booth Registration">
                <sheet>
                    <div class="oe_title">
                        <h1><field name="partner_id"/></h1>
                    </div>
                    <group>
                        <group string="Details">
                            <field name="event_booth_id"/>
                            <field name="sale_order_line_id"/>
                        </group>
                        <group string="Contact">
                            <field name="contact_name"/>
                            <field name="contact_email"/>
                            <field name="contact_phone"/>
                            <field name="contact_mobile"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_booth_registration_view_tree" model="ir.ui.view">
        <field name="name">event.booth.registration.view.tree</field>
        <field name="model">event.booth.registration</field>
        <field name="arch" type="xml">
            <tree string="Booth Registration">
                <field name="partner_id"/>
                <field name="event_booth_id"/>
                <field name="sale_order_line_id"/>
                <field name="contact_name" optional="hide"/>
                <field name="contact_email" optional="hide"/>
                <field name="contact_phone" optional="hide"/>
                <field name="contact_mobile" optional="hide"/>
            </tree>
        </field>
    </record>

</data></odoo>
