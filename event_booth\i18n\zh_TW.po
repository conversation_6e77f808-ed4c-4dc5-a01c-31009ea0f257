# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * event_booth
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Ren<PERSON> Email</b>:"
msgstr "<b>租用人電郵</b>："

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Mobile</b>:"
msgstr "<b>租用人手提</b>："

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Name</b>:"
msgstr "<b>租用人名稱</b>："

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "<b>Renter Phone</b>:"
msgstr "<b>租用人電話</b>："

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
msgid "<span class=\"o_stat_text\">Booths</span>"
msgstr "<span class=\"o_stat_text\">攤位</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span class=\"text-white\">4m²</span>"
msgstr "<span class=\"text-white\">4平方公尺</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span class=\"text-white\">8m²</span>"
msgstr "<span class=\"text-white\">8平方公尺</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 Branded Booth</span>"
msgstr "<span>1 個品牌攤位</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>1 desk</span>"
msgstr "<span>1張桌子</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>10 + 1 passes</span>"
msgstr "<span>10+1通行證</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>100 words description on website</span>"
msgstr "<span>網站上的100字說明</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 Branded Booth</span>"
msgstr "<span>2 品牌攤位</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 desks</span>"
msgstr "<span>2張桌子</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>2 x 46\" display screens</span>"
msgstr "<span>2 x 46\" 螢幕</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "<span>46\" display screen</span>"
msgstr "<span>46\" 螢幕</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "<span>50 words description on website</span>"
msgstr "<span>網站上的50字說明</span>"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "<span>Logo &amp; link on website</span>"
msgstr "<span>網站上的logo和連結</span>"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__active
msgid "Active"
msgstr "啟用"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_ids
msgid "Activities"
msgstr "活動"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_search
msgid "Archived"
msgstr "已歸檔"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__available
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Available"
msgstr "可用"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count_available
msgid "Available Booths"
msgstr "可用攤位"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_booked_template
msgid "Booth"
msgstr "攤位"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_00_event_7
#: model:event.booth,name:event_booth.event_booth_0_event_0
msgid "Booth A1"
msgstr "攤位A1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_01_event_7
#: model:event.booth,name:event_booth.event_booth_1_event_0
msgid "Booth A2"
msgstr "攤位A2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_02_event_7
#: model:event.booth,name:event_booth.event_booth_2_event_0
msgid "Booth A3"
msgstr "攤位A3"

#. module: event_booth
#: model:mail.message.subtype,name:event_booth.mt_event_booth_booked
msgid "Booth Booked"
msgstr "攤位已預訂"

#. module: event_booth
#: model:ir.ui.menu,name:event_booth.menu_event_booth_category
msgid "Booth Categories"
msgstr "攤位類別"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_category_action
#: model:ir.model.fields,field_description:event_booth.field_event_booth__booth_category_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__booth_category_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Booth Category"
msgstr "攤位類別"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_event__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr "攤位仍然可用的攤位類別。前台使用"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Booth Type"
msgstr "攤位類型"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid ""
"Booth categories are used to represent the different types of booths you "
"rent (Premium Booth, Table and Chairs, ...)"
msgstr "攤位類別用於代表您租用的不同類型的攤位（高級攤位、桌椅……）"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_booth_action
#: model:ir.actions.act_window,name:event_booth.event_booth_action_from_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_ids
#: model:ir.model.fields,field_description:event_booth.field_event_type__event_type_booth_ids
#: model:ir.ui.menu,name:event_booth.menu_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_tree_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_event_view_form
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_view_form
msgid "Booths"
msgstr "攤位"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Booths are the physical stands that you rent during your event."
msgstr "攤位是您在活動期間租用的實體攤位。"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_action_from_event
msgid "Create a Booth"
msgstr "建立攤位"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_booth_category_action
msgid "Create a Booth Category"
msgstr "建立攤位類別"

#. module: event_booth
#: model_terms:ir.actions.act_window,help:event_booth.event_type_booth_action
msgid "Create a Type Booth"
msgstr "建立類型攤位"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_uid
msgid "Created by"
msgstr "創立者"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__create_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__create_date
msgid "Created on"
msgstr "建立於"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__description
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "Description"
msgstr "說明"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__display_name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_event
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_id
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event"
msgstr "活動"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Event Booth"
msgstr "活動攤位"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_booth_category
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_ids
msgid "Event Booth Category"
msgstr "活動攤位類別"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "活動攤位類別可用"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type_booth
msgid "Event Booth Template"
msgstr "活動攤位模板"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__event_type_id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__event_type_id
msgid "Event Category"
msgstr "活動類別"

#. module: event_booth
#: model:ir.model,name:event_booth.model_event_type
msgid "Event Template"
msgstr "活動模板"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Event Type Booth"
msgstr "活動類型攤位"

#. module: event_booth
#: model:ir.actions.act_window,name:event_booth.event_type_booth_action
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_tree_from_type
msgid "Event Type Booths"
msgstr "活動類型攤位"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_20_event_7
msgid "Gold Booth 1"
msgstr "金級攤位 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_21_event_7
msgid "Gold Booth 2"
msgstr "金級攤位 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_22_event_7
msgid "Gold Booth 3"
msgstr "金級攤位 3"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_search
msgid "Group By"
msgstr "分組依據"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__id
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__id
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__id
msgid "ID"
msgstr "ID"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction
#: model:ir.model.fields,help:event_booth.field_event_booth__message_unread
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1920
msgid "Image"
msgstr "圖像"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_1024
msgid "Image 1024"
msgstr "圖像 1024"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_128
msgid "Image 128"
msgstr "圖像 128"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_256
msgid "Image 256"
msgstr "圖像 256"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__image_512
msgid "Image 512"
msgstr "圖像 512"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__is_available
msgid "Is Available"
msgstr "可用"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth____last_update
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category____last_update
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth____last_update
msgid "Last Modified on"
msgstr "最後修改於"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_uid
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__write_date
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_ids
msgid "Messages"
msgstr "訊息"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__name
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__name
#: model:ir.model.fields,field_description:event_booth.field_event_type_booth__name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "Name"
msgstr "名稱"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "需要處理的消息數量"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__message_unread_counter
msgid "Number of unread messages"
msgstr "未讀訊息的數量"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_10_event_7
msgid "OpenWood Demonstrator 1"
msgstr "OpenWood 演示器 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_11_event_7
msgid "OpenWood Demonstrator 2"
msgstr "OpenWood 演示器 2"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_12_event_7
msgid "OpenWood Demonstrator 3"
msgstr "OpenWood 演示器 3"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_premium
msgid "Premium"
msgstr "進階"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_premium
msgid "Premium Booth"
msgstr "高級攤位"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_0
msgid "Premium Booth A4"
msgstr "高級攤位 A4"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_2_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_2
msgid "Premium Showbooth 1"
msgstr "高級展台 1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_3_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_3
msgid "Premium Showbooth 2"
msgstr "高級展台 2"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__partner_id
msgid "Renter"
msgstr "租用人"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_email
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Email"
msgstr "租用人電郵"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_mobile
msgid "Renter Mobile"
msgstr "租用人手提"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_name
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Renter Name"
msgstr "租用人名稱"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__contact_phone
msgid "Renter Phone"
msgstr "租用人電話"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth_category__sequence
msgid "Sequence"
msgstr "序號"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_0_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_0
msgid "Showbooth 1"
msgstr "攤位1"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_1_event_2
#: model:event.type.booth,name:event_booth.event_type_booth_demo_conference_1
msgid "Showbooth 2"
msgstr "攤位2"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__state
msgid "Shows the availability of a Booth"
msgstr "顯示攤位的可用性"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_standard
msgid "Standard"
msgstr "標準"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_standard
msgid "Standard Booth"
msgstr "標準攤位"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__state
msgid "Status"
msgstr "狀態"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_event__event_booth_count
msgid "Total Booths"
msgstr "攤位總數"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: event_booth
#: model:ir.model.fields.selection,name:event_booth.selection__event_booth__state__unavailable
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_search
msgid "Unavailable"
msgstr "無法預約"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_unread
msgid "Unread Messages"
msgstr "未讀消息"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未讀消息計數器"

#. module: event_booth
#: model_terms:event.booth.category,description:event_booth.event_booth_category_vip
msgid "VIP"
msgstr "VIP"

#. module: event_booth
#: model:event.booth.category,name:event_booth.event_booth_category_vip
msgid "VIP Booth"
msgstr "貴賓攤位"

#. module: event_booth
#: model:event.booth,name:event_booth.event_booth_4_event_0
msgid "VIP Booth A5"
msgstr "貴賓攤位A5"

#. module: event_booth
#: model:ir.model.fields,field_description:event_booth.field_event_booth__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: event_booth
#: model:ir.model.fields,help:event_booth.field_event_booth__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_from_event
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:event_booth.event_type_booth_view_form_from_type
msgid "e.g. First Booth Alley 1"
msgstr "例如第一攤位巷 1"

#. module: event_booth
#: model_terms:ir.ui.view,arch_db:event_booth.event_booth_category_view_form
msgid "e.g. Premium Booth"
msgstr "例如高級攤位"
