# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_stock
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2015-10-01 09:22+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON>by<PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterms:</strong>"
msgstr ""

#. module: sale_stock
#: selection:sale.config.settings,module_delivery:0
msgid "Allow adding shipping costs"
msgstr "Ad yeǧǧ timerna n tesqamin n usiweḍ"

#. module: sale_stock
#: selection:sale.config.settings,group_mrp_properties:0
msgid "Allow setting manufacturing order properties per order line (advanced)"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_config_settings_group_mrp_properties
msgid "Allows you to tag sales order lines with properties."
msgstr ""
"Ak yeǧǧ ad ternuḍ tibzimin ɣef izirigen n tladna n uznuzu akk d iglamen"

#. module: sale_stock
#: selection:sale.config.settings,group_route_so_lines:0
msgid "Choose specific routes on sales order lines (advanced)"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Tikebbaniyin"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_default_picking_policy
msgid "Default Shipping Policy"
msgstr ""

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver all products at once"
msgstr "Siweḍ akkw ifarisen deg iwet tikelt"

#. module: sale_stock
#: selection:sale.order,picking_policy:0
msgid "Deliver each product when available"
msgstr "Siweḍ yal afaris ticki illa"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Asiweḍ"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_delivery_count
msgid "Delivery Orders"
msgstr "Tiludna n usiweḍ"

#. module: sale_stock
#: selection:sale.config.settings,group_mrp_properties:0
msgid "Don't use manufacturing properties (recommended as its easier)"
msgstr ""

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_route_so_lines
msgid "Enable Route on Sales Order Line"
msgstr "Rmed i bardan ɣef izirigen n tladna n tiɣin"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_product_product_expense_policy
msgid "Expense Invoice Policy"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_product_product_expense_policy
msgid ""
"If you invoice at cost, the expense will be invoiced on the sale order at "
"the cost of the analytic line;if you invoice at sales price, the price of "
"the product will be used instead."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_account_invoice_incoterms_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_incoterm
msgid "Incoterms"
msgstr "Incoterms"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_account_invoice_incoterms_id
msgid ""
"Incoterms are series of sales terms. They are used to divide transaction "
"costs and responsibilities between buyer and seller and reflect state-of-the-"
"art transportation practices."
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Les Incoterms sont une série de termes commerciaux prédéfinie utilisés dans "
"les transactions internationales."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice
msgid "Invoice"
msgstr "Tafaturt"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_invoice_line
msgid "Invoice Line"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised "
"date, to cope with unexpected delays in the supply chain."
msgstr ""

#. module: sale_stock
#: selection:sale.config.settings,group_route_so_lines:0
msgid "No order specific routes like MTO or drop shipping"
msgstr ""

#. module: sale_stock
#: selection:sale.config.settings,module_delivery:0
msgid "No shipping costs on sales orders"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:156
#, python-format
msgid "Not enough inventory!"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/res_config.py:42
#, python-format
msgid "Only administrators can change the settings"
msgstr "Ala anebdal i g-zemren a d-isnifel tawila."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_group_route_so_lines
msgid "Order Routing"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:167
#, python-format
msgid "Ordered quantity decreased!"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_product_packaging
msgid "Packaging"
msgstr "Tanagalt"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_ids
msgid "Picking associated to this sale"
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_order
msgid "Procurement"
msgstr "Asigeẓ"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_product
#, fuzzy
msgid "Product"
msgstr "Taneɣruft n ufaris"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_product_tmpl_id
msgid "Product Template"
msgstr "Taneɣruft n ufaris"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_group_mrp_properties
msgid "Properties on SO Lines"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line_route_id
msgid "Route"
msgstr "Abrid"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking_sale_id
msgid "Sale Order"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_form_view_inherit_sale_stock
msgid "Sale Order Lines"
msgstr "Izirigen n tludna n uznuzu"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
msgid "Sales Order"
msgstr "Taladna n uznuzu"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Izirig n tladna n uznuzu"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Orders Statistics"
msgstr "Tiddadanin ɣef tludna uznuzu"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company_security_lead
msgid "Sales Safety Days"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route_sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Izmer ad iţufren deg uzirig n tladna n uznuzu"

#. module: sale_stock
#: selection:sale.config.settings,default_picking_policy:0
msgid "Ship all products at once, without back orders"
msgstr ""

#. module: sale_stock
#: selection:sale.config.settings,default_picking_policy:0
msgid "Ship products when some are available, and allow back orders"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_config_settings_module_delivery
#: model_terms:ir.ui.view,arch_db:sale_stock.view_sales_config_inherit_sale_stock
msgid "Shipping"
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Shipping Information"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_picking_policy
msgid "Shipping Policy"
msgstr "Tarrayt n uceggaɛ"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Amussu n uselɣas"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:213
#, python-format
msgid "This product is packaged by %s %s. You should sell %s %s."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report_warehouse_id
msgid "Warehouse"
msgstr "Agadir"

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:212
#, python-format
msgid "Warning"
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:168
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""

#. module: sale_stock
#: code:addons/sale_stock/sale_stock.py:157
#, python-format
msgid ""
"You plan to sell %.2f %s but you only have %.2f %s available!\n"
"The stock on hand is %.2f %s."
msgstr ""

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_config_settings
msgid "sale.config.settings"
msgstr ""
