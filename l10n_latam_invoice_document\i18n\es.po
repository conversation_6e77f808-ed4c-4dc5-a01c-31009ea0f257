# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_latam_invoice_document
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-03-30 18:44+0000\n"
"PO-Revision-Date: 2020-03-30 18:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de Plan de Cuentas"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Revocación de movimiento en cuenta"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__active
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Active"
msgstr "Activo"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__internal_type
msgid ""
"Analog to odoo account.move.move_type but with more options allowing to "
"identify the kind of document we are working with. (not only related to "
"account.move, could be for documents of other models like stock.picking)"
msgstr ""
"Análogo a account.move.type de Odoo pero con más opciones, permitiendo "
"identificar el tipo de documento sobre el que estamos trabajando. (no "
"solamente relativo a account.move, podría ser relativo a otros modelos, como"
" por ejemplo stock.picking)"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Archived"
msgstr "Archivado"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__code
msgid "Code"
msgstr "Código"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__code
msgid "Code used by different localizations"
msgstr "Código usando por diferentes localizaciones"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__country_id
msgid "Country"
msgstr "País"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_country_code
msgid "Country Code"
msgstr "Código País"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_country_code
msgid "Country Code (LATAM)"
msgstr "Código País (LATAM)"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__country_id
msgid "Country in which this type of document is valid"
msgstr "País donde el tipo de documento es valido"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__credit_note
msgid "Credit Notes"
msgstr "Notas de Crédito"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__debit_note
msgid "Debit Notes"
msgstr "Notas de Débito"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__display_name
msgid "Display Name"
msgstr "Nombre a mostrar"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__doc_code_prefix
msgid "Document Code Prefix"
msgstr "Prefijo del código de documento"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_number
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_number
msgid "Document Number"
msgstr "Número de Documento"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_invoice_report__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_document_type_id
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_document_type_id
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_invoice_report_search
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_account_move_line_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_form
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_tree
msgid "Document Type"
msgstr "Tipo de Documento"

#. module: l10n_latam_invoice_document
#: model:ir.actions.act_window,name:l10n_latam_invoice_document.action_document_type
#: model:ir.ui.menu,name:l10n_latam_invoice_document.menu_document_type
msgid "Document Types"
msgstr "Tipos de Documentos"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Group By..."
msgstr "Agrupar por..."

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__id
msgid "ID"
msgstr "ID (identificación)"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_journal__l10n_latam_use_documents
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_move__l10n_latam_use_documents
msgid ""
"If active: will be using for legal invoicing (invoices, debit/credit notes)."
" If not set means that will be used to register accounting entries not "
"related to invoicing legal documents. For Example: Receipts, Tax Payments, "
"Register journal entries"
msgstr ""
"Si esta activo: será utilizado para facturación (facturas, notas de débito y"
" crédito). SI esta desactivado significa que que será usado para registrar "
"movimientos contables que no están relacionas con facturación como tal. Por "
"ejemplo: Recibos, Pagos de Impuestos, Registrar asientos contables"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__internal_type
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Internal Type"
msgstr "Tipo interno"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields.selection,name:l10n_latam_invoice_document.selection__l10n_latam_document_type__internal_type__invoice
msgid "Invoices"
msgstr "Facturas"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estadísticas de facturas"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move
msgid "Journal Entries"
msgstr "Asientos contables"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_amount_untaxed
msgid "L10N Latam Amount Untaxed"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_available_document_type_ids
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_available_document_type_ids
msgid "L10N Latam Available Document Type"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_company_use_documents
msgid "L10N Latam Company Use Documents"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_net
msgid "L10N Latam Price Net"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_subtotal
msgid "L10N Latam Price Subtotal"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_price_unit
msgid "L10N Latam Price Unit"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_tax_ids
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_line__l10n_latam_tax_ids
msgid "L10N Latam Tax"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_use_documents
msgid "L10N Latam Use Documents"
msgstr ""

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: l10n_latam_invoice_document
#: model:ir.model,name:l10n_latam_invoice_document.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr "Tipo de Documento Latam"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Localization"
msgstr "Localización"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_manual_document_number
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move_reversal__l10n_latam_manual_document_number
msgid "Manual Number"
msgstr "Número Manual"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__name
msgid "Name"
msgstr "Nombre"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__report_name
msgid "Name on Reports"
msgstr "Nombre en Reportes"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__report_name
msgid "Name that will be printed in reports, for example \"CREDIT NOTE\""
msgstr "Nombre que será impreso en los reportes, por ejemplo \"NOTA DE CREDITO\""

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "Please set the document number on the following invoices %s."
msgstr ""
"Por favor asigne el número de documento en las siguientes facturas %s."

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__doc_code_prefix
msgid ""
"Prefix for Documents Codes on Invoices and Account Moves. For eg. 'FA ' will"
" build 'FA 0001-0000001' Document Number"
msgstr ""
"Prefijo para Códigos de Documentos en Facturas y Asientos. Por eje: 'FA ' "
"será construirá 'FA 0001-0000001' como el numero de documento"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show active document types"
msgstr "Mostrar tipos de documento activos"

#. module: l10n_latam_invoice_document
#: model_terms:ir.ui.view,arch_db:l10n_latam_invoice_document.view_document_type_filter
msgid "Show archived document types"
msgstr "Mostrar tipos de documento archivados"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_journal__l10n_latam_country_code
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_account_move__l10n_latam_country_code
msgid "Technical field used to hide/show fields regarding the localization"
msgstr ""
"Campo técnico usado para esconder/mostrar dependiendo de la liocalización"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__name
msgid "The document name"
msgstr "El nombre del documento"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid ""
"The journal require a document type but not document type has been selected "
"on invoices %s."
msgstr ""
"El diario requiere un tipo de documento pero no hay tipo de documento "
"seleccionado en las facturas %s."

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,help:l10n_latam_invoice_document.field_l10n_latam_document_type__sequence
msgid ""
"To set in which order show the documents type taking into account the most "
"commonly used first"
msgstr ""
"Para seleccionar en que orden deben mostrar los tipos de documento teniendo "
"en cuenta los más usados comunmente"

#. module: l10n_latam_invoice_document
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_journal__l10n_latam_use_documents
#: model:ir.model.fields,field_description:l10n_latam_invoice_document.field_account_move__l10n_latam_use_documents
msgid "Use Documents?"
msgstr "Usa Documentos?"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "Vendor bill number must be unique per vendor and company."
msgstr ""
"El número de la Factura de Proveedor debe ser único por proveedor y por "
"compañía."

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "We do not accept the usage of document types on receipts yet. "
msgstr "Todavía no aceptamos el uso de tipos de documentos en los recibos."

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_journal.py:0
#, python-format
msgid ""
"You can not modify the field \"Use Documents?\" if there are validated "
"invoices in this journal!"
msgstr ""
"No puedes modificar el campo \"Usa Documentos?\" si ya existen facturas "
"validades en este diario!"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a invoice"
msgstr "No puedes utilizar el tipo de documento %s en una factura"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/models/account_move.py:0
#, python-format
msgid "You can not use a %s document type with a refund invoice"
msgstr ""
"No puedes utilizar el tipo de documento %s en una factura de reembolso"

#. module: l10n_latam_invoice_document
#: code:addons/l10n_latam_invoice_document/wizards/account_move_reversal.py:0
#, python-format
msgid ""
"You can only reverse documents with legal invoicing documents from Latin America one at a time.\n"
"Problematic documents: %s"
msgstr ""
"Solo puede revertir documentos con documentos de facturación legales de Latino américa de uno en uno.\n"
"Documentos problemáticos:%s"
