<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_showcase" name="Showcase">
    <div class="s_showcase pt48 pb48 o_mail_snippet_general" data-snippet="s_showcase">
        <!-- TODO: (below) issue with height: `fit-content` is not supported, can we calculate it in px in translation ? 
        empty div height is 0 unless table has defined height (div.col-1 > div.w-50.h100.border-right)-->
        <div class="container" style="height: fit-content;">
            <div class="row no-gutters s_col_no_resize s_col_no_bgcolor s_nb_column_fixed">
                <div class="col-lg-5 text-right pb24" align="right">
                    <div class="mb-2">
                        <h3 class="d-inline-block">First feature</h3>
                        <i class="fa fa-2x fa-desktop text-secondary ml-3"/>
                    </div>
                    <p>A short description of this great feature.</p>
                </div>
                <div class="col-lg-2 o_mail_no_resize" align="left">
                    <div class="w-50 h-100 border-right"/>
                </div>
                <div class="col-lg-5 pb24" align="left">
                    <div class="mb-2">
                        <i class="fa fa-2x fa-heart text-secondary mr-3"/>
                        <h3 class="d-inline-block">Another feature</h3>
                    </div>
                    <p>A short description of this great feature.</p>
                </div>
            </div>
            <div class="row no-gutters s_col_no_resize s_col_no_bgcolor s_nb_column_fixed">
                <div class="col-lg-5 text-right" align="right">
                    <div class="mb-2">
                        <h3 class="d-inline-block">Second feature</h3>
                        <i class="fa fa-2x fa-paint-brush text-secondary ml-3"/>
                    </div>
                    <p>A short description of this great feature.</p>
                </div>
                <div class="col-lg-2 o_mail_no_resize" align="left">
                    <div class="w-50 h-100 border-right"/>
                </div>
                <div class="col-lg-5" align="left">
                    <div class="mb-2">
                        <i class="fa fa-2x fa-gift text-secondary mr-3"/>
                        <h3 class="d-inline-block">Last Feature</h3>
                    </div>
                    <p>A short description of this great feature.</p>
                </div>
            </div>
        </div>
        <div class="container text-center pt32" align="center">
            <a href="#" class="btn btn-primary">Discover all the features</a>
        </div>
    </div>
</template>

</odoo>
