# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * product
#
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-06-21 16:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Peru) (http://www.transifex.com/odoo/odoo-9/language/"
"es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid " Product Category"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_count
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_count
msgid "# of Product Variants"
msgstr ""

#. module: product
#: code:addons/product/pricelist.py:372
#, python-format
msgid "%s %% discount"
msgstr ""

#. module: product
#: code:addons/product/pricelist.py:374
#, python-format
msgid "%s %% discount and %s surcharge"
msgstr ""

#. module: product
#: code:addons/product/product.py:716
#, python-format
msgid "%s (copy)"
msgstr "%s (copiar)"

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_1
msgid "16 GB"
msgstr "16 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_3
#: model:product.template,description_sale:product.product_product_3_product_template
msgid ""
"17\" LCD Monitor\n"
"Processor AMD 8-Core\n"
"512MB RAM\n"
"HDD SH-1"
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_25
#: model:product.template,description:product.product_product_25_product_template
msgid ""
"17\" Monitor\n"
"4GB RAM\n"
"Standard-1294P Processor\n"
"QWERTY keyboard"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_5
msgid "2.4 GHz"
msgstr "2.4 GHz"

#. module: product
#: model:product.product,description_sale:product.product_product_8
#: model:product.template,description_sale:product.product_product_8_product_template
msgid ""
"2.7GHz quad-core Intel Core i5\n"
"                Turbo Boost up to 3.2GHz\n"
"                8GB (two 4GB) memory\n"
"                1TB hard drive\n"
"                Intel Iris Pro graphics\n"
"            "
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_2
msgid "32 GB"
msgstr "32 GB"

#. module: product
#: model:product.product,description_sale:product.product_product_4
#: model:product.product,description_sale:product.product_product_4b
#: model:product.product,description_sale:product.product_product_4c
#: model:product.product,description_sale:product.product_product_4d
#: model:product.template,description_sale:product.product_product_4_product_template
#: model:product.template,description_sale:product.product_product_4b_product_template
#: model:product.template,description_sale:product.product_product_4c_product_template
#: model:product.template,description_sale:product.product_product_4d_product_template
msgid ""
"7.9‑inch (diagonal) LED-backlit, 128Gb\n"
"Dual-core A5 with quad-core graphics\n"
"FaceTime HD Camera, 1.2 MP Photos"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "75 percent less reflection."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"<span attrs=\"{'invisible':[('base', '!=', 'list_price')]}\">Public Price  "
"-  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'standard_price')]}\">Cost  -  </span>\n"
"                            <span attrs=\"{'invisible':[('base', '!=', "
"'pricelist')]}\">Other Pricelist  -  </span>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "<span>kg</span>"
msgstr "<span>kg</span>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Currency</strong>:<br/>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Description</strong>"
msgstr "<strong>Descripción</strong>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Price List Name</strong>:<br/>"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
msgid "<strong>Print date</strong>:<br/>"
msgstr "<strong>Fecha de Impresión</strong>:<br/>"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid ""
"<strong>Warning</strong>: adding or deleting attributes\n"
"                            will delete and recreate existing variants and "
"lead\n"
"                            to the loss of their possible customizations."
msgstr ""

#. module: product
#: code:addons/product/product.py:1011 sql_constraint:product.product:0
#, python-format
msgid "A barcode can only be assigned to one product !"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_category_type
msgid ""
"A category of the view type is a virtual category that can be used as the "
"parent of another category to create a hierarchical structure."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_type
#: model:ir.model.fields,help:product.field_product_template_type
msgid ""
"A consumable is a product for which you don't manage stock, a service is a "
"non-material product provided by a company or an individual."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_sale
#: model:ir.model.fields,help:product.field_product_template_description_sale
msgid ""
"A description of the Product that you want to communicate to your customers. "
"This description will be copied to every Sale Order, Delivery Order and "
"Customer Invoice/Refund"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description_purchase
#: model:ir.model.fields,help:product.field_product_template_description_purchase
msgid ""
"A description of the Product that you want to communicate to your vendors. "
"This description will be copied to every Purchase Order, Receipt and Vendor "
"Bill/Refund."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid "A great Keyboard. Cordless."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_description
#: model:ir.model.fields,help:product.field_product_template_description
msgid ""
"A precise description of the Product, used only for internal information "
"purposes."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"A price list contains rules to be evaluated in order to compute\n"
"                the sales price of the products."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "A screen worthy of iPad."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"About the size of a credit card — and just 5.4 mm thin — iPod nano is the "
"thinnest iPod ever made.\n"
"                                    The 2.5-inch Multi-Touch display is "
"nearly twice as big as the display on the previous iPod nano,\n"
"                                    so you can see more of the music, "
"photos, and videos you love."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_active
#: model:ir.model.fields,field_description:product.field_product_product_active
#: model:ir.model.fields,field_description:product.field_product_template_active
#: model:ir.model.fields,field_description:product.field_product_uom_active
msgid "Active"
msgstr "Activo"

#. module: product
#: model:product.category,name:product.product_category_all
msgid "All"
msgstr "Todos"

#. module: product
#: code:addons/product/pricelist.py:367
#, python-format
msgid "All Products"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "All general settings about this product are managed on"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"And at the Apple Online Store, you can configure your iMac with an even more "
"powerful Intel Core i7 processor, up to 3.5GHz."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "And because it’s so easy to use, it’s easy to love."
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_7
#: model:product.template,name:product.product_product_7_product_template
msgid "Apple In-Ear Headphones"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_9
#: model:product.template,name:product.product_product_9_product_template
msgid "Apple Wireless Keyboard"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "Applicable On"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_applied_on
msgid "Apply On"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Archived"
msgstr "Archivado"

#. module: product
#: model:product.product,name:product.product_assembly
#: model:product.template,name:product.product_assembly_product_template
msgid "Assembly Service Cost"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_sequence
msgid "Assigns the priority to the list of product vendor."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_attribute_id
msgid "Attribute"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_extra
msgid "Attribute Price Extra"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_price_ids
msgid "Attribute Prices"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.variants_action
#: model:ir.model.fields,field_description:product.field_product_attribute_line_value_ids
#: model:ir.ui.menu,name:product.menu_variants_action
msgid "Attribute Values"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.attribute_action
#: model:ir.model.fields,field_description:product.field_product_product_attribute_value_ids
#: model:ir.ui.menu,name:product.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:product.product_attribute_value_view_tree
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Attributes"
msgstr "Atributos"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary input for portable devices"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Auxiliary port lets you connect other audio sources, like an MP3 player"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_barcode
#: model:ir.model.fields,field_description:product.field_product_template_barcode
msgid "Barcode"
msgstr "Código de Barras"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_base
msgid ""
"Base price for computation. \n"
" Public Price: The base price will be the Sale/public Price. \n"
" Cost Price : The base price will be the cost price. \n"
" Other Pricelist : Computation of the base price based on another Pricelist."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_list_price
#: model:ir.model.fields,help:product.field_product_template_list_price
msgid ""
"Base price to compute the customer price. Sometimes called the catalog price."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base
msgid "Based on"
msgstr ""

#. module: product
#: model:product.product,name:product.consu_delivery_03
#: model:product.template,name:product.consu_delivery_03_product_template
msgid "Basic Computer"
msgstr ""

#. module: product
#: model:product.product,name:product.membership_2
#: model:product.template,name:product.membership_2_product_template
msgid "Basic Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Beautiful 7.9‑inch display."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Beautiful widescreen display."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image
msgid "Big-sized image"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor_inv
msgid "Bigger Ratio"
msgstr ""

#. module: product
#: selection:product.uom,uom_type:0
msgid "Bigger than the reference Unit of Measure"
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_4
msgid "Black"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bluetooth connectivity"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_5b
#: model:product.template,name:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Bose Mini Bluetooth Speaker."
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_5b
#: model:product.template,description_sale:product.product_product_5b_product_template
msgid "Bose's smallest portable Bluetooth speaker"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Brilliance onscreen. And behind it."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Buttons let you quickly play, pause, change songs, or adjust the volume.\n"
"                                    The smooth anodized aluminum design "
"makes iPod nano feel as good as it sounds.\n"
"                                    And iPod nano wouldn’t be iPod nano "
"without gorgeous, hard-to-choose-from color."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_active
msgid ""
"By unchecking the active field you can disable a unit of measure without "
"deleting it."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Calculate Product Price per Unit Based on Pricelist Version."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_rental
#: model:ir.model.fields,field_description:product.field_product_template_rental
msgid "Can be Rent"
msgstr "Puede ser Rentado"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_sale_ok
#: model:ir.model.fields,field_description:product.field_product_template_sale_ok
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Can be Sold"
msgstr "Puede ser Vendido"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Cancel"
msgstr "Cancelar"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_type
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category Type"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "Category name"
msgstr ""

#. module: product
#: code:addons/product/pricelist.py:361
#, python-format
msgid "Category: %s"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Characteristics"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Charges iPod/iPhone"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Charging cradle recharges the battery and serves as a convenient\n"
"                                    home base for your speaker, and it lets "
"you play while it charges."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_child_id
msgid "Child Categories"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid "Click to add a new unit of measure category."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid "Click to add a new unit of measure."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid "Click to create a pricelist."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_template_action
#: model_terms:ir.actions.act_window,help:product.product_template_action_product
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid "Click to define a new product."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Codes"
msgstr "Códigos"

#. module: product
#: model:product.attribute,name:product.product_attribute_2
msgid "Color"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_color
#: model:ir.model.fields,field_description:product.field_product_template_color
msgid "Color Index"
msgstr "Índice de Color"

#. module: product
#: model:product.product,description_sale:product.product_product_6
#: model:product.template,description_sale:product.product_product_6_product_template
msgid ""
"Color: White\n"
"Capacity: 16GB\n"
"Connectivity: Wifi\n"
"Beautiful 7.9-inch display\n"
"Over 375,000 apps\n"
"Ultrafast wireless\n"
"iOS7\n"
"            "
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_company_id
#: model:ir.model.fields,field_description:product.field_product_product_company_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_company_id
#: model:ir.model.fields,field_description:product.field_product_template_company_id
msgid "Company"
msgstr "Compañia"

#. module: product
#: model:product.public.category,name:product.Components
msgid "Components"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Compute Price"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_16
#: model:product.template,name:product.product_product_16_product_template
msgid "Computer Case"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_3
#: model:product.template,name:product.product_product_3_product_template
msgid "Computer SC234"
msgstr ""

#. module: product
#: model:product.public.category,name:product.Computer_all_in_one
msgid "Computer all-in-one"
msgstr ""

#. module: product
#: model:product.public.category,name:product.sub_computers
msgid "Computers"
msgstr ""

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Consumable"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: product
#: code:addons/product/product.py:122
#, python-format
msgid ""
"Conversion from Product UoM %s to Default UoM %s is not possible as they "
"both belong to different Category!."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_cost
#: model:ir.model.fields,field_description:product.field_product_product_standard_price
#: model:ir.model.fields,field_description:product.field_product_template_standard_price
#: selection:product.pricelist.item,base:0
msgid "Cost"
msgstr "Costo"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_standard_price
msgid ""
"Cost of the product template used for standard stock valuation in accounting "
"and used as a base price on purchase orders. Expressed in the default unit "
"of measure of the product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_standard_price
msgid "Cost of the product, in the default unit of measure of the product."
msgstr ""

#. module: product
#: model:product.product,name:product.service_delivery
#: model:product.template,name:product.service_delivery_product_template
msgid "Cost-plus Contract"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_uid
#: model:ir.model.fields,field_description:product.field_product_category_create_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_create_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_uid
#: model:ir.model.fields,field_description:product.field_product_product_create_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_uid
#: model:ir.model.fields,field_description:product.field_product_template_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_uid
#: model:ir.model.fields,field_description:product.field_product_uom_create_uid
msgid "Created by"
msgstr "Creado por"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_line_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_create_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_create_date
#: model:ir.model.fields,field_description:product.field_product_category_create_date
#: model:ir.model.fields,field_description:product.field_product_packaging_create_date
#: model:ir.model.fields,field_description:product.field_product_price_history_create_date
#: model:ir.model.fields,field_description:product.field_product_price_list_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_create_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_create_date
#: model:ir.model.fields,field_description:product.field_product_product_create_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_create_date
#: model:ir.model.fields,field_description:product.field_product_template_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_create_date
#: model:ir.model.fields,field_description:product.field_product_uom_create_date
msgid "Created on"
msgstr "Creado en"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Creating such a stunningly thin design took some equally stunning feats of "
"technological innovation. We refined,re-imagined,or re-engineered everything "
"about iMac from the inside out. The result is an advanced, elegant all-in-"
"one computer that’s as much a work of art as it is state of the art."
msgstr ""

#. module: product
#: model:ir.model,name:product.model_res_currency
#: model:ir.model.fields,field_description:product.field_product_pricelist_currency_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_currency_id
#: model:ir.model.fields,field_description:product.field_product_product_currency_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_currency_id
#: model:ir.model.fields,field_description:product.field_product_template_currency_id
msgid "Currency"
msgstr "Moneda"

#. module: product
#: model:product.product,name:product.product_product_5
#: model:product.template,name:product.product_product_5_product_template
msgid "Custom Computer (kit)"
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_27
#: model:product.template,description:product.product_product_27_product_template
msgid "Custom Laptop based on customer's requirement."
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_5
#: model:product.template,description:product.product_product_5_product_template
msgid "Custom computer shipped in kit."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_partner_ref
msgid "Customer ref"
msgstr ""

#. module: product
#: model:product.product,name:product.product_delivery_02
#: model:product.template,name:product.product_delivery_02_product_template
msgid "Datacard"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_datetime
msgid "Date"
msgstr "Fecha"

#. module: product
#: model:product.uom,name:product.product_uom_day
msgid "Day(s)"
msgstr "Día(s)"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_id
#: model:ir.model.fields,help:product.field_product_template_uom_id
msgid "Default Unit of Measure used for all stock operation."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_uom_po_id
#: model:ir.model.fields,help:product.field_product_template_uom_po_id
msgid ""
"Default Unit of Measure used for purchase orders. It must be in the same "
"category than the default unit of measure."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_delay
msgid "Delivery Lead Time"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description
#: model:ir.model.fields,field_description:product.field_product_template_description
msgid "Description"
msgstr "Descripción"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Quotations"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Description for Vendors"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Design. The thinnest iPod ever."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_sequence
#: model:ir.model.fields,help:product.field_product_attribute_value_sequence
msgid "Determine the display order"
msgstr ""

#. module: product
#: model:product.public.category,name:product.devices
msgid "Devices"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_line_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_price_display_name
#: model:ir.model.fields,field_description:product.field_product_attribute_value_display_name
#: model:ir.model.fields,field_description:product.field_product_category_display_name
#: model:ir.model.fields,field_description:product.field_product_packaging_display_name
#: model:ir.model.fields,field_description:product.field_product_price_history_display_name
#: model:ir.model.fields,field_description:product.field_product_price_list_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_display_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_display_name
#: model:ir.model.fields,field_description:product.field_product_product_display_name
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_display_name
#: model:ir.model.fields,field_description:product.field_product_template_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_display_name
#: model:ir.model.fields,field_description:product.field_product_uom_display_name
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_display_name
msgid "Display Name"
msgstr "Nombre a Mostrar"

#. module: product
#: model:product.uom,name:product.product_uom_dozen
msgid "Dozen(s)"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_03
#: model:product.template,description_sale:product.consu_delivery_03_product_template
msgid ""
"Dvorak keyboard \n"
"            left-handed mouse"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_pricelist_action2
msgid ""
"Each rule include a set of applicability criteria (date range,\n"
"                product category...) and a computation that easily helps to "
"achieve\n"
"                any kind of pricing."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Efficient, high-quality audio"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_end
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_end
msgid "End Date"
msgstr "Fecha Final"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_end
msgid "End date for this vendor price"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_end
msgid "Ending valid for the pricelist item validation"
msgstr ""

#. module: product
#: constraint:product.category:0
msgid "Error ! You cannot create recursive categories."
msgstr "¡Error! No puede crear categorías recursivas"

#. module: product
#: constraint:product.attribute.line:0
msgid "Error ! You cannot use this attribute with the following value."
msgstr ""

#. module: product
#: constraint:product.product:0
msgid ""
"Error! It is not allowed to choose more than one value for a given attribute."
msgstr ""

#. module: product
#: constraint:product.pricelist.item:0
msgid "Error! The minimum margin should be lower than the maximum margin."
msgstr ""

#. module: product
#: constraint:product.pricelist.item:0
msgid ""
"Error! You cannot assign the Main Pricelist as Other Pricelist in PriceList "
"Item!"
msgstr ""

#. module: product
#: constraint:res.currency:0
msgid ""
"Error! You cannot define a rounding factor for the company's main currency "
"that is smaller than the decimal precision of 'Account'."
msgstr ""

#. module: product
#: constraint:decimal.precision:0
msgid ""
"Error! You cannot define the decimal precision of 'Account' as greater than "
"the rounding factor of the company's main currency"
msgstr ""

#. module: product
#: constraint:product.template:0
msgid ""
"Error: The default Unit of Measure and the purchase Unit of Measure must be "
"in the same category."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast Colors are vivid and text "
"is sharp on the iPad mini display.\n"
"                                    But what really makes it stand out is "
"its size. At 7.9 inches,\n"
"                                    it’s perfectly sized to deliver an "
"experience every bit as big as iPad."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Everything you love about iPad — the beautiful\n"
"                                    screen, fast and fluid performance, "
"FaceTime and\n"
"                                    iSight cameras, thousands of amazing "
"apps, 10-hour\n"
"                                    battery life* — is everything you’ll "
"love about\n"
"                                    iPad mini, too. And you can hold it in "
"one hand."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Everything you love about iPad — the beautiful screen,\n"
"                                        fast and fluid performance, FaceTime "
"and iSight cameras, \n"
"                                        thousands of amazing apps, 10-hour "
"battery life* — is everything\n"
"                                        you’ll love about iPad mini, too. "
"And you can hold it in one hand."
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_2
#: model:product.template,description:product.product_product_2_product_template
msgid "Example of product to invoice based on delivery."
msgstr ""

#. module: product
#: model:product.product,description:product.service_order_01
#: model:product.template,description:product.service_order_01_product_template
msgid "Example of product to invoice on order."
msgstr ""

#. module: product
#: model:product.product,description:product.service_cost_01
#: model:product.template,description:product.service_cost_01_product_template
msgid "Example of products to invoice based on cost."
msgstr ""

#. module: product
#: model:product.product,description:product.product_product_1
#: model:product.template,description:product.product_product_1_product_template
msgid "Example of products to invoice based on delivery."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_name
#: model:ir.model.fields,help:product.field_product_pricelist_item_price
msgid "Explicit rule name for this pricelist line."
msgstr ""

#. module: product
#: model:product.product,name:product.service_cost_01
#: model:product.template,name:product.service_cost_01_product_template
msgid "External Audit"
msgstr ""

#. module: product
#: model:product.public.category,name:product.External_Hard_Drive
msgid "External Hard Drive"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Fast connections.The world over."
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Fix Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_fixed_price
msgid "Fixed Price"
msgstr "Precio Fijo"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_min_quantity
msgid ""
"For the rule to apply, bought/sold quantity must be greater than or equal to "
"the minimum quantity specified in this field.\n"
"Expressed in the default unit of measure of the product."
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Formula"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_7
#: model:product.template,description_sale:product.product_product_7_product_template
msgid ""
"Frequency: 5Hz to 21kHz\n"
"Impedance: 23 ohms\n"
"Sensitivity: 109 dB SPL/mW\n"
"Drivers: two-way balanced armature\n"
"Cable length: 1065 mm\n"
"Weight: 10.2 grams\n"
"            "
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Friendly to the environment."
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_1
#: model:product.template,name:product.product_product_1_product_template
msgid "GAP Analysis Service"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "General Information"
msgstr "Información General"

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Genius. Your own personal DJ."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_packaging_ids
#: model:ir.model.fields,help:product.field_product_template_packaging_ids
msgid ""
"Gives the different ways to package the same product. This has no impact on "
"the picking order and is mainly used if you use the EDI module."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_sequence
msgid ""
"Gives the order in which the pricelist items will be checked. The evaluation "
"gives highest priority to lowest sequence and stops as soon as a matching "
"item is found."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_category_sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr ""
"Indica el orden de secuencia cuando se muestra una lista de categorías de "
"producto."

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sequence
#: model:ir.model.fields,help:product.field_product_template_sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: product
#: selection:product.pricelist.item,applied_on:0
msgid "Global"
msgstr "Global"

#. module: product
#: model:product.product,name:product.membership_0
#: model:product.template,name:product.membership_0_product_template
msgid "Gold Membership"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_24
#: model:product.template,name:product.product_product_24_product_template
msgid "Graphics Card"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_17
#: model:product.template,name:product.product_product_17_product_template
msgid "HDD SH-1"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Have Genius call the tunes."
msgstr ""

#. module: product
#: model:product.public.category,name:product.Headset
msgid "Headset"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Hear, hear."
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_11
#: model:product.product,description_sale:product.product_product_11b
#: model:product.template,description_sale:product.product_product_11_product_template
#: model:product.template,description_sale:product.product_product_11b_product_template
msgid ""
"Height: 76.5 mm\n"
"Width:  39.6 mm\n"
"Depth:  5.4 mm\n"
"Weight: 31 grams"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Highly rated designs."
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_hour
msgid "Hour(s)"
msgstr "Hora(s)"

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"How did we make an already gorgeous widescreen display even better? By "
"making it 75 percent less reflective. And by re-architecting the LCD and "
"moving it right up against the cover glass. So you see your photos, games, "
"movies, and everything else in vivid, lifelike detail."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor_inv
msgid ""
"How many times this Unit of Measure is bigger than the reference Unit of "
"Measure in this category:\n"
"1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_factor
msgid ""
"How much bigger or smaller this unit is compared to the reference Unit of "
"Measure for this category:\n"
"1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "How to get your groove on."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_id
#: model:ir.model.fields,field_description:product.field_product_attribute_line_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_id
#: model:ir.model.fields,field_description:product.field_product_attribute_value_id
#: model:ir.model.fields,field_description:product.field_product_category_id
#: model:ir.model.fields,field_description:product.field_product_packaging_id
#: model:ir.model.fields,field_description:product.field_product_price_history_id
#: model:ir.model.fields,field_description:product.field_product_price_list_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_id
#: model:ir.model.fields,field_description:product.field_product_product_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_id
#: model:ir.model.fields,field_description:product.field_product_template_id
#: model:ir.model.fields,field_description:product.field_product_uom_categ_id
#: model:ir.model.fields,field_description:product.field_product_uom_id
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist_id
msgid "ID"
msgstr "ID"

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "If it's made for iPad, it's made for iPad mini."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_active
msgid ""
"If unchecked, it will allow you to hide the pricelist without removing it."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_active
#: model:ir.model.fields,help:product.field_product_template_active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_image
msgid "Image"
msgstr "Imagen"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image
msgid ""
"Image of the product variant (Big-sized image of product template if false). "
"It is automatically resized as a 1024x1024px image, with aspect ratio "
"preserved."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_medium
msgid ""
"Image of the product variant (Medium-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_small
msgid ""
"Image of the product variant (Small-sized image of product template if "
"false)."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Individually calibrated for true-to-life color."
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_supplierinfo
msgid "Information about a product vendor"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Inside each earpiece is a stainless steel mesh cap that protects the "
"precision acoustic\n"
"                                    components from dust and debris. You can "
"remove the caps for cleaning or replace\n"
"                                    them with an extra set that’s included "
"in the box."
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_2
msgid "Internal"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_categ_id
#: model:ir.model.fields,field_description:product.field_product_template_categ_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Internal Category"
msgstr "Categoría Interna"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_code
#: model:ir.model.fields,field_description:product.field_product_product_default_code
#: model:ir.model.fields,field_description:product.field_product_template_default_code
msgid "Internal Reference"
msgstr "Referencia Interna"

#. module: product
#: model:ir.model.fields,help:product.field_product_product_barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Inventory"
msgstr "Inventario"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_is_product_variant
#: model:ir.model.fields,field_description:product.field_product_template_is_product_variant
msgid "Is a product variant"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Keep it clean."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Key Features"
msgstr ""

#. module: product
#: model:product.public.category,name:product.Keyboard_Mouse
msgid "Keyboard / Mouse"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_27
#: model:product.template,name:product.product_product_27_product_template
msgid "Laptop Customized"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_25
#: model:product.template,name:product.product_product_25_product_template
msgid "Laptop E5023"
msgstr ""

#. module: product
#: model:product.public.category,name:product.laptops
msgid "Laptops"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_line___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_price___last_update
#: model:ir.model.fields,field_description:product.field_product_attribute_value___last_update
#: model:ir.model.fields,field_description:product.field_product_category___last_update
#: model:ir.model.fields,field_description:product.field_product_packaging___last_update
#: model:ir.model.fields,field_description:product.field_product_price_history___last_update
#: model:ir.model.fields,field_description:product.field_product_price_list___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist___last_update
#: model:ir.model.fields,field_description:product.field_product_pricelist_item___last_update
#: model:ir.model.fields,field_description:product.field_product_product___last_update
#: model:ir.model.fields,field_description:product.field_product_supplierinfo___last_update
#: model:ir.model.fields,field_description:product.field_product_template___last_update
#: model:ir.model.fields,field_description:product.field_product_uom___last_update
#: model:ir.model.fields,field_description:product.field_product_uom_categ___last_update
#: model:ir.model.fields,field_description:product.field_report_product_report_pricelist___last_update
msgid "Last Modified on"
msgstr "Ultima Modificación en"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_uid
#: model:ir.model.fields,field_description:product.field_product_attribute_write_uid
#: model:ir.model.fields,field_description:product.field_product_category_write_uid
#: model:ir.model.fields,field_description:product.field_product_packaging_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_history_write_uid
#: model:ir.model.fields,field_description:product.field_product_price_list_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_uid
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_uid
#: model:ir.model.fields,field_description:product.field_product_product_write_uid
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_uid
#: model:ir.model.fields,field_description:product.field_product_template_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_uid
#: model:ir.model.fields,field_description:product.field_product_uom_write_uid
msgid "Last Updated by"
msgstr "Actualizado última vez por"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_line_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_price_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_value_write_date
#: model:ir.model.fields,field_description:product.field_product_attribute_write_date
#: model:ir.model.fields,field_description:product.field_product_category_write_date
#: model:ir.model.fields,field_description:product.field_product_packaging_write_date
#: model:ir.model.fields,field_description:product.field_product_price_history_write_date
#: model:ir.model.fields,field_description:product.field_product_price_list_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_write_date
#: model:ir.model.fields,field_description:product.field_product_pricelist_write_date
#: model:ir.model.fields,field_description:product.field_product_product_write_date
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_write_date
#: model:ir.model.fields,field_description:product.field_product_template_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_categ_write_date
#: model:ir.model.fields,field_description:product.field_product_uom_write_date
msgid "Last Updated on"
msgstr "Ultima Actualización"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_delay
msgid ""
"Lead time in days between the confirmation of the purchase order and the "
"receipt of the products in your warehouse. Used by the scheduler for "
"automatic computation of the purchase order planning."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_left
msgid "Left Parent"
msgstr ""

#. module: product
#: model:product.uom.categ,name:product.uom_categ_length
msgid "Length / Distance"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_attribute_line_ids
msgid "Lines"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_litre
msgid "Liter(s)"
msgstr ""

#. module: product
#: model:product.product,name:product.consu_delivery_02
#: model:product.template,name:product.consu_delivery_02_product_template
msgid "Little server"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_packaging_ids
#: model:ir.model.fields,field_description:product.field_product_template_packaging_ids
msgid "Logistical Units"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_uom
msgid "Manage Multiple Units of Measure"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_pricelist_item
msgid "Manage Pricelist Items"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_stock_packaging
msgid "Manage Product Packaging"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_group_product_variant
#: model:res.groups,name:product.group_product_variant
msgid "Manage Product Variants"
msgstr "Administrar Variantes de Producto"

#. module: product
#: model:res.groups,name:product.group_mrp_properties
msgid "Manage Properties of Product"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_uos
msgid "Manage Secondary Unit of Measure"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Max. Margin"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_max_margin
msgid "Max. Price Margin"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_medium
#: model:ir.model.fields,field_description:product.field_product_template_image_medium
msgid "Medium-sized image"
msgstr "Imagen de tamaño mediano"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_medium
msgid ""
"Medium-sized image of the product. It is automatically resized as a "
"128x128px image, with aspect ratio preserved, only when the image exceeds "
"one of those sizes. Use this field in form views or some kanban views."
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_1
msgid "Memory"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Min. Margin"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_min_margin
msgid "Min. Price Margin"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_min_quantity
msgid "Min. Quantity"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_min_qty
msgid "Minimal Quantity"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "More energy efficient."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "More features."
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_20
#: model:product.template,name:product.product_product_20_product_template
msgid "Motherboard I9P57"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_10
#: model:product.template,name:product.product_product_10_product_template
msgid "Mouse, Optical"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_12
#: model:product.template,name:product.product_product_12_product_template
msgid "Mouse, Wireless"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Music. It's what beats inside."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_name
#: model:ir.model.fields,field_description:product.field_product_category_complete_name
#: model:ir.model.fields,field_description:product.field_product_category_name
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_name
#: model:ir.model.fields,field_description:product.field_product_product_name
#: model:ir.model.fields,field_description:product.field_product_template_name
#: model:ir.model.fields,field_description:product.field_product_uom_categ_name
msgid "Name"
msgstr "Nombre"

#. module: product
#: model:product.public.category,name:product.network
msgid "Network"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "New Price ="
msgstr ""

#. module: product
#: selection:product.category,type:0
msgid "Normal"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Notes"
msgstr "Notas"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Other Information"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_base_pricelist_id
#: selection:product.pricelist.item,base:0
msgid "Other Pricelist"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Over 375,000 apps."
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_packaging
#: model_terms:ir.ui.view,arch_db:product.product_packaging_form_view
#: model_terms:ir.ui.view,arch_db:product.product_packaging_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Packaging"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_name
msgid "Packaging Type"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.action_packaging_view
msgid "Packagings"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_id
msgid "Parent Category"
msgstr "Categoría Padre"

#. module: product
#: model:ir.model,name:product.model_res_partner
msgid "Partner"
msgstr "Partner"

#. module: product
#: model:product.public.category,name:product.Pen_Drive
msgid "Pen Drive"
msgstr ""

#. module: product
#: selection:product.pricelist.item,compute_price:0
msgid "Percentage (discount)"
msgstr "Porcentaje (descuento)"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_percent_price
msgid "Percentage Price"
msgstr "Precio del Porcentaje"

#. module: product
#: model:product.category,name:product.product_category_5
msgid "Physical"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Playlists. The perfect mix for every mood."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Plays where you play"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid ""
"Powered by fourth-generation Intel Core processors, this iMac is the fastest "
"yet. Every model in the lineup comes standard with a quad-core Intel Core i5 "
"processor, starting at 2.7GHz and topping out at 3.4GHz."
msgstr ""

#. module: product
#: model:product.product,name:product.service_order_01
#: model:product.template,name:product.service_order_01_product_template
msgid "Prepaid Consulting"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price
#: model:ir.model.fields,field_description:product.field_product_product_price
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_price
#: model:ir.model.fields,field_description:product.field_product_template_price
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Price"
msgstr "Precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Price Computation"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_discount
msgid "Price Discount"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_price_extra
msgid "Price Extra"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_attribute_value_price_extra
msgid ""
"Price Extra: Extra price for the variant with this attribute value on sale "
"price. eg. 200 price extra, 1000 + 200 = 1200."
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.action_product_price_list
#: model:ir.model,name:product.model_product_price_list
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.report_pricelist
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Price List"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_round
msgid "Price Rounding"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_price_surcharge
msgid "Price Surcharge"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
msgid "Price:"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_price_list
msgid "PriceList"
msgstr ""

#. module: product
#: model:ir.actions.report.xml,name:product.action_report_pricelist
#: model:ir.model,name:product.model_product_pricelist
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_product_pricelist_id
#: model:ir.model.fields,field_description:product.field_product_template_pricelist_id
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist"
msgstr "Lista de Precios"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_applied_on
msgid "Pricelist Item applicable on selected option"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_ids
#: model:ir.model.fields,field_description:product.field_product_product_item_ids
#: model:ir.model.fields,field_description:product.field_product_template_item_ids
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Pricelist Items"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_name
msgid "Pricelist Name"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_pricelist_item
msgid "Pricelist item"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_action2
#: model:ir.ui.menu,name:product.menu_product_pricelist_main
msgid "Pricelists"
msgstr ""

#. module: product
#: model:res.groups,name:product.group_product_pricelist
msgid "Pricelists On Product"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "Pricelists are managed on"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Pricing"
msgstr "Precio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_product_price_list
msgid "Print"
msgstr "Imprimir"

#. module: product
#: model:product.public.category,name:product.printer
msgid "Printer"
msgstr "Impresora"

#. module: product
#: model:product.product,name:product.product_product_22
#: model:product.template,name:product.product_product_22_product_template
msgid "Processor Core i5 2.70 Ghz"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action
#: model:ir.model,name:product.model_product_product
#: model:ir.model.fields,field_description:product.field_product_packaging_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_price_history_product_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_id
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model_terms:ir.ui.view,arch_db:product.product_template_tree_view
#: selection:product.pricelist.item,applied_on:0
#: model:res.request.link,name:product.req_link_product
msgid "Product"
msgstr "Producto"

#. module: product
#: model:ir.model,name:product.model_product_attribute
msgid "Product Attribute"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_price_value_id
msgid "Product Attribute Value"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_attribute_line_ids
#: model:ir.model.fields,field_description:product.field_product_template_attribute_line_ids
msgid "Product Attributes"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_category_action_form
#: model:ir.ui.menu,name:product.menu_product_category_action_form
#: model_terms:ir.ui.view,arch_db:product.product_category_list_view
#: model_terms:ir.ui.view,arch_db:product.product_category_search_view
msgid "Product Categories"
msgstr "Categorías de Producto"

#. module: product
#: model:ir.model,name:product.model_product_category
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_categ_id
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_product_manager
#: model:ir.model.fields,field_description:product.field_product_template_product_manager
msgid "Product Manager"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Product Name"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_template
#: model:ir.model.fields,field_description:product.field_product_attribute_line_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_attribute_price_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_product_product_tmpl_id
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_tmpl_id
#: model_terms:ir.ui.view,arch_db:product.product_search_form_view
msgid "Product Template"
msgstr "Plantilla de Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_type
#: model:ir.model.fields,field_description:product.field_product_template_type
msgid "Product Type"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_uom
msgid "Product Unit of Measure"
msgstr "Unidad de Medida del Producto"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_id
#: model_terms:ir.ui.view,arch_db:product.product_normal_form_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
#: selection:product.pricelist.item,applied_on:0
msgid "Product Variant"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_normal_action_sell
#: model:ir.actions.act_window,name:product.product_variant_action
#: model:ir.ui.menu,name:product.menu_products
#: model_terms:ir.ui.view,arch_db:product.product_product_tree_view
msgid "Product Variants"
msgstr "Variantes de Producto"

#. module: product
#: model:ir.model,name:product.model_product_uom_categ
msgid "Product uom categ"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_template_action
#: model:ir.actions.act_window,name:product.product_template_action_product
#: model:ir.model.fields,field_description:product.field_product_product_product_variant_ids
#: model:ir.model.fields,field_description:product.field_product_template_product_variant_ids
#: model:ir.ui.menu,name:product.menu_product_template_action
#: model:ir.ui.menu,name:product.prod_config_main
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products"
msgstr "Productos"

#. module: product
#: model:ir.actions.report.xml,name:product.report_product_label
msgid "Products Labels"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_tree
msgid "Products Price List"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view_search
msgid "Products Price Search"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
msgid "Products your store in the inventory"
msgstr ""

#. module: product
#: code:addons/product/product.py:824
#, python-format
msgid "Products: "
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_template_lst_price
#: selection:product.pricelist.item,base:0
msgid "Public Price"
msgstr ""

#. module: product
#: model:product.pricelist,name:product.list0
msgid "Public Pricelist"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_purchase
#: model:ir.model.fields,field_description:product.field_product_template_description_purchase
msgid "Purchase Description"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_po_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_po_id
msgid "Purchase Unit of Measure"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_packaging_qty
msgid "Quantity by Package"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty1
msgid "Quantity-1"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty2
msgid "Quantity-2"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty3
msgid "Quantity-3"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty4
msgid "Quantity-4"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_list_qty5
msgid "Quantity-5"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_13
#: model:product.template,name:product.product_product_13_product_template
msgid "RAM SR5"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_factor
msgid "Ratio"
msgstr ""

#. module: product
#: selection:product.uom,uom_type:0
msgid "Reference Unit of Measure for this category"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Remote control for power, volume, track seek"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_category_parent_right
msgid "Right Parent"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, apps made for iPad also work with iPad mini.\n"
"                                    They’re immersive, full-screen apps that "
"let you do almost anything\n"
"                                    you can imagine. And with automatic "
"updates,\n"
"                                    you're always getting the best "
"experience possible."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid ""
"Right from the start, there’s a lot to love about\n"
"                                    iPad. It’s simple yet powerful. Thin and "
"light yet\n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    be just about anything."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"Right from the start, there’s a lot to love about iPad.\n"
"                                   It’s simple yet powerful. Thin and light "
"yet full-\n"
"                                   featured. It can do just about everything "
"and be just\n"
"                                   about anything.And because it’s so easy "
"to use, it’s\n"
"                                   easy to love."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid "Rounding Method"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_rounding
msgid "Rounding Precision"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sale Conditions"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_description_sale
#: model:ir.model.fields,field_description:product.field_product_template_description_sale
msgid "Sale Description"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_list_price
#: model:ir.model.fields,field_description:product.field_product_product_lst_price
#: model:ir.model.fields,field_description:product.field_product_template_list_price
msgid "Sale Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_res_partner_property_product_pricelist
msgid "Sale Pricelist"
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_1
msgid "Saleable"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Sales"
msgstr "Ventas"

#. module: product
#: model:res.groups,name:product.group_sale_pricelist
msgid "Sales Pricelists"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Say you’re listening to a song you love and you want to stay in the mood.\n"
"                                        Just tap Genius. It finds other "
"songs on iPod nano that go great together\n"
"                                        and makes a Genius playlist for you. "
"For more song combinations\n"
"                                        you wouldn’t have thought of "
"yourself, create Genius Mixes in iTunes\n"
"                                        and sync the ones you like to iPod "
"nano. Then tap Genius Mixes and\n"
"                                        rediscover songs you haven’t heard "
"in a while — or find music you forgot you even had."
msgstr ""

#. module: product
#: model:product.public.category,name:product.Screen
msgid "Screen"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_categ_id
#: model:ir.model.fields,help:product.field_product_template_categ_id
msgid "Select category for the current product"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_sequence
#: model:ir.model.fields,field_description:product.field_product_attribute_value_sequence
#: model:ir.model.fields,field_description:product.field_product_category_sequence
#: model:ir.model.fields,field_description:product.field_product_packaging_sequence
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_sequence
#: model:ir.model.fields,field_description:product.field_product_product_sequence
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_sequence
#: model:ir.model.fields,field_description:product.field_product_template_sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: product
#: model:product.product,name:product.consu_delivery_01
#: model:product.public.category,name:product.server
#: model:product.template,name:product.consu_delivery_01_product_template
msgid "Server"
msgstr "Servidor"

#. module: product
#: code:addons/product/product.py:474
#, python-format
msgid "Service"
msgstr "Servicio"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_search_view
#: model:product.category,name:product.product_category_3
#: model:product.public.category,name:product.services
msgid "Services"
msgstr "Servicios"

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_round
msgid ""
"Sets the price so that it is a multiple of this value.\n"
"Rounding is applied after the discount and before the surcharge.\n"
"To have prices that end in 9.99, set rounding 10, surcharge -0.01"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_base_config_settings_company_share_product
msgid "Share product to all companies"
msgstr "Compartir el producto a todas las compañías"

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_company_share_product
msgid ""
"Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is "
"defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company "
"is defined). Product not related to a company are visible for all companies."
msgstr ""
"Comparte su producto a todas las empresas definidas en su instancia.\n"
" * Marcada: Los productos son visibles para todas las compañías, incluso si "
"una empresa se ​​define en la empresa.\n"
" * Desmarcada: Cada compañía sólo puede ver sus producto (producto donde la "
"compañía se ​​define). Los productos no relacionados con una empresa son "
"visibles por todas las compañías."

#. module: product
#: model:product.product,name:product.membership_1
#: model:product.template,name:product.membership_1_product_template
msgid "Silver Membership"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Sleek, compact design"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_small
#: model:ir.model.fields,field_description:product.field_product_template_image_small
msgid "Small-sized image"
msgstr "Imagen de tamaño pequeño"

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image_small
msgid ""
"Small-sized image of the product. It is automatically resized as a 64x64px "
"image, with aspect ratio preserved. Use this field anywhere a small image is "
"required."
msgstr ""

#. module: product
#: selection:product.uom,uom_type:0
msgid "Smaller than the reference Unit of Measure"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Soft covers are available separately in blue, green or orange. Pick a color "
"to match your style."
msgstr ""

#. module: product
#: model:product.category,name:product.product_category_4
#: model:product.public.category,name:product.Software
msgid "Software"
msgstr ""

#. module: product
#: model:product.public.category,name:product.Speakers
msgid "Speakers"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_categ_id
msgid ""
"Specify a product category if this rule only applies to products belonging "
"to this category or its children categories. Keep empty otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_id
msgid ""
"Specify a product if this rule only applies to one product. Keep empty "
"otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_product_tmpl_id
msgid ""
"Specify a template if this rule only applies to one product template. Keep "
"empty otherwise."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_sale_ok
#: model:ir.model.fields,help:product.field_product_template_sale_ok
msgid "Specify if the product can be selected in a sales order line."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_surcharge
msgid ""
"Specify the fixed amount to add or substract(if negative) to the amount "
"calculated with the discount."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_max_margin
msgid "Specify the maximum amount of margin over the base price."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_price_min_margin
msgid "Specify the minimum amount of margin over the base price."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_date_start
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_date_start
msgid "Start Date"
msgstr "Fecha de Inicio"

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_date_start
msgid "Start date for this vendor price"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_pricelist_item_date_start
msgid "Starting date for the pricelist item validation"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_supplierinfo_type_action
msgid "Supplier Pricelist"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_2
#: model:product.template,name:product.product_product_2_product_template
msgid "Support Contract (on timesheet)"
msgstr ""

#. module: product
#: model:product.product,name:product.product_delivery_01
#: model:product.template,name:product.product_delivery_01_product_template
msgid "Switch, 24 ports"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "Sync to your heart’s content."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"Tap to play your favorite songs. Or entire albums.\n"
"                                    Or everything by one artist. You can "
"even browse by genres or composers.\n"
"                                    Flip through your music: Album art looks "
"great on the bigger screen.\n"
"                                    Or to keep things fresh, give iPod nano "
"a shake and it shuffles to a different song in your music library."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_name_template
msgid "Template Name"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"The Apple In-Ear Headphones deliver a truly immersive sound experience by "
"drastically\n"
"                                    reducing unwanted outside noises. The "
"soft, silicone ear tips fit snugly and comfortably\n"
"                                    in your ear, creating a seal that "
"isolates your music from your surroundings.\n"
"                                    Three different sizes of ear tips are "
"included so you can find a perfect fit for each ear.\n"
"                                    Also included are a convenient carrying "
"case for the ear tips and a cable-control case\n"
"                                    for the headphones themselves."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The Bose® SoundLink® mini is Bose's smallest portable Bluetooth speaker. Its "
"ultra-compact size fits in the \n"
"                                    palm of your hand, yet gives you full, "
"natural sound wirelessly from your iPhone, iPad, or iPod. Grab it and go \n"
"                                    full-featured. It can do just about "
"everything and\n"
"                                    experience music just about anywhere."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The SoundLink® Mini speaker is small and light enough\n"
"                                        to tuck into your bag. It weighs in "
"at just 1.5 pounds.\n"
"                                        Its low profile lets you place it "
"almost anywhere and\n"
"                                        provides a low center of gravity "
"that makes it nearly\n"
"                                        impossible to tip over."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_item_form_view
msgid ""
"The computed price is expressed in the default Unit of Measure of the "
"product."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_uom_rounding
msgid ""
"The computed quantity will be a multiple of this value. Use 1.0 for a Unit "
"of Measure that cannot be further split, such as a piece."
msgstr ""

#. module: product
#: sql_constraint:product.uom:0
msgid "The conversion ratio for a unit of measure cannot be 0!"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "The desktop. In its most advanced form ever"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_sequence
msgid "The first in the sequence is the default one."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "The full iPad experience."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_9
#: model:product.template,website_description:product.product_product_9_product_template
msgid ""
"The incredibly thin Apple Wireless Keyboard uses Bluetooth technology,\n"
"                                    which makes it compatible with iPad. And "
"you’re free to type wherever\n"
"                                    you like — with the keyboard in front of "
"your iPad or on your lap."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_min_qty
msgid ""
"The minimal quantity to purchase from this vendor, expressed in the vendor "
"Product Unit of Measure if not any, in the default unit of measure of the "
"product otherwise."
msgstr ""

#. module: product
#: code:addons/product/product.py:332
#, python-format
msgid ""
"The operation cannot be completed:\n"
"You are trying to delete an attribute value with a reference on a product "
"variant."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_price
msgid "The price to purchase a product"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"The product form contains information to simplify the sale\n"
"                process: price, notes in the quotation, accounting data,\n"
"                procurement methods, etc."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"The product form contains information to simplify the sale process: price, "
"notes in the quotation, accounting data, procurement methods, etc."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The rechargeable lithium-ion battery delivers up to seven hours of "
"playtime.\n"
"                                    And at home, you can listen even longer—"
"the charging cradle lets\n"
"                                    you listen while it charges."
msgstr ""

#. module: product
#: model:product.product,description_sale:product.product_product_9
#: model:product.template,description_sale:product.product_product_9_product_template
msgid ""
"The sleek aluminium Apple Wireless Keyboard.\n"
"            "
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"The speaker has a range of about 30 feet, so you can enjoy\n"
"                                    the sound you want without wires. It "
"pairs easily with your\n"
"                                    smartphone, iPad® or other Bluetooth "
"device.\n"
"                                    And it remembers the most recent six "
"devices you've used,\n"
"                                    so reconnecting is even simpler."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_packaging_qty
msgid "The total number of products you can put by pallet or box."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_volume
#: model:ir.model.fields,help:product.field_product_template_volume
msgid "The volume in m3."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_weight
#: model:ir.model.fields,help:product.field_product_template_weight
msgid "The weight of the contents in Kg, not including any packaging, etc."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
msgid "There is less of it, but no less to it."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "There's less of it, but no less to it."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"There’s another way to get a good mix of music on iPod: Let Genius do the "
"work.\n"
"                                        Activate Genius in iTunes on your "
"computer, and it automatically finds songs that sound\n"
"                                        great together. Then it creates "
"Genius Mixes, which you can easily sync to your iPod.\n"
"                                        It’s the perfect way to rediscover "
"songs you haven’t listened to in forever."
msgstr ""

#. module: product
#: sql_constraint:product.attribute.value:0
msgid "This attribute value already exists !"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_uom
msgid "This comes from the product form."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_image_variant
msgid ""
"This field holds the image used as image for the product variant, limited to "
"1024x1024px."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_template_image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_qty
msgid "This is a quantity which is converted into Default Unit of Measure."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_product_price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on requests for quotation."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "This note will be displayed on the quotations."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_res_partner_property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_code
msgid ""
"This vendor's product code will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_name
msgid ""
"This vendor's product name will be used when printing a request for "
"quotation. Keep empty to use the internal one."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid "Two is better than one."
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_uom_type
msgid "Type"
msgstr "Tipo"

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"USB port allows for software update to ensure ongoing Bluetooth device "
"compatibility"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Ultrafast wireless."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_8
#: model:product.template,website_description:product.product_product_8_product_template
msgid "Ultrathin design"
msgstr ""

#. module: product
#: model:product.uom.categ,name:product.product_uom_categ_unit
msgid "Unit"
msgstr "Unidad"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_uom_id
#: model:ir.model.fields,field_description:product.field_product_template_uom_id
#: model:ir.model.fields,field_description:product.field_product_uom_name
msgid "Unit of Measure"
msgstr "Unidad de Medida"

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_categ_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_categ_form_action
msgid "Unit of Measure Categories"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_uom_category_id
msgid "Unit of Measure Category"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_unit
msgid "Unit(s)"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_uom_form_action
#: model:ir.ui.menu,name:product.menu_product_uom_form_action
#: model:ir.ui.menu,name:product.next_id_16
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
#: model_terms:ir.ui.view,arch_db:product.product_uom_tree_view
msgid "Units of Measure"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_categ_form_view
msgid "Units of Measure categories"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_categ_form_action
msgid ""
"Units of measure belonging to the same category can be\n"
"                converted between each others. For example, in the category\n"
"                <i>'Time'</i>, you will have the following units of "
"measure:\n"
"                Hours, Days."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Universal iPod docking station fits most iPod/iPhone models"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_7
#: model:product.template,website_description:product.product_product_7_product_template
msgid ""
"Unlike many small headphones, each earpiece of the Apple In-Ear Headphones\n"
"                                    contains two separate high-performance "
"drivers — a woofer to handle bass and\n"
"                                    mid-range sounds and a tweeter for high-"
"frequency audio. These dedicated\n"
"                                    drivers help ensure accurate, detailed "
"sound across the entire sonic spectrum.\n"
"                                    The result: you’re immersed in the music "
"and hear details you never knew existed.\n"
"                                    Even when listening to an old favorite, "
"you may feel like you’re hearing it for the first time."
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Validity"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_name
msgid "Value"
msgstr "Valor"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_ids
msgid "Values"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_price_extra
msgid "Variant Extra Price"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_image_variant
msgid "Variant Image"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Variant Information"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Variant Prices"
msgstr ""

#. module: product
#: model:ir.actions.act_window,name:product.product_attribute_value_action
#: model_terms:ir.ui.view,arch_db:product.attribute_tree_view
#: model_terms:ir.ui.view,arch_db:product.variants_tree_view
msgid "Variant Values"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_attribute_value_product_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_kanban_view
#: model_terms:ir.ui.view,arch_db:product.product_template_only_form_view
msgid "Variants"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_name
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "Vendor"
msgstr "Proveedor"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_tree_view
msgid "Vendor Information"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_code
msgid "Vendor Product Code"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_name
msgid "Vendor Product Name"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_supplierinfo_product_uom
msgid "Vendor Unit of Measure"
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_name
msgid "Vendor of this product"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_seller_ids
#: model:ir.model.fields,field_description:product.field_product_template_seller_ids
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "Vendors"
msgstr "Proveedores"

#. module: product
#: selection:product.category,type:0
msgid "View"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_volume
#: model:ir.model.fields,field_description:product.field_product_template_volume
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
#: model:product.uom.categ,name:product.product_uom_categ_vol
msgid "Volume"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid "Volume control on main system"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_5b
#: model:product.template,website_description:product.product_product_5b_product_template
msgid ""
"Wall charger can be plugged into the cradle or directly into the speaker"
msgstr ""

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_warranty
#: model:ir.model.fields,field_description:product.field_product_template_warranty
msgid "Warranty"
msgstr "Garantía"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_product_weight
#: model:ir.model.fields,field_description:product.field_product_template_weight
#: model:product.uom.categ,name:product.product_uom_categ_kgm
msgid "Weight"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "Weights"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid "When one playlist isn’t enough."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_product_supplierinfo_product_id
msgid ""
"When this field is filled in, the vendor data will only apply to the variant."
msgstr ""

#. module: product
#: model:product.attribute.value,name:product.product_attribute_value_3
msgid "White"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_4
#: model:product.product,website_description:product.product_product_4b
#: model:product.product,website_description:product.product_product_4c
#: model:product.product,website_description:product.product_product_4d
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_4_product_template
#: model:product.template,website_description:product.product_product_4b_product_template
#: model:product.template,website_description:product.product_product_4c_product_template
#: model:product.template,website_description:product.product_product_4d_product_template
#: model:product.template,website_description:product.product_product_6_product_template
msgid "Why you'll love an iPad."
msgstr ""

#. module: product
#: model:product.attribute,name:product.product_attribute_3
msgid "Wi-Fi"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_6
#: model:product.template,website_description:product.product_product_6_product_template
msgid ""
"With advanced Wi‑Fi that’s up to twice as fast as\n"
"                                   any previous-generation iPad and access "
"to fast\n"
"                                   cellular data networks around the world, "
"iPad mini\n"
"                                   lets you download content, stream video,\n"
"                                   and browse the web at amazing speeds."
msgstr ""

#. module: product
#: model:ir.model.fields,help:product.field_base_config_settings_group_product_variant
msgid ""
"Work with product variant allows you to define some variant of the same "
"products, an ease the product management in the ecommerce for example"
msgstr ""
"Trabajar con variantes de producto le permite definir algunas variante de "
"los mismos productos, una facilidad de la gestión de productos en el "
"comercio electrónico, por ejemplo."

#. module: product
#: model:product.uom.categ,name:product.uom_categ_wtime
msgid "Working Time"
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_uom_form_action
msgid ""
"You must define a conversion rate between several Units of\n"
"                Measure within the same category."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_normal_action
#: model_terms:ir.actions.act_window,help:product.product_normal_action_sell
#: model_terms:ir.actions.act_window,help:product.product_variant_action
msgid ""
"You must define a product for everything you sell, whether it's\n"
"                a physical product, a consumable or a service you offer to\n"
"                customers."
msgstr ""

#. module: product
#: model_terms:ir.actions.act_window,help:product.product_template_action
msgid ""
"You must define a product for everything you sell, whether it's a physical "
"product, a consumable or a service you offer to customers."
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"You probably have multiple playlists in iTunes on your computer.\n"
"                                        One for your commute. One for the "
"gym. Sync those playlists\n"
"                                        to iPod, and you can play the "
"perfect mix for whatever\n"
"                                        mood strikes you. VoiceOver tells "
"you the name of each playlist,\n"
"                                        so it’s easy to switch between them "
"and find the one you want without looking."
msgstr ""

#. module: product
#: model:product.product,name:product.product_order_01
#: model:product.template,name:product.product_order_01_product_template
msgid "Zed+ Antivirus"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_base_config_settings
msgid "base.config.settings"
msgstr "base.config.settings"

#. module: product
#: model:product.uom,name:product.product_uom_cm
msgid "cm"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "days"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_decimal_precision
msgid "decimal.precision"
msgstr "decimal.precision"

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_category_form_view
msgid "e.g. Lamps"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "e.g. Odoo Enterprise Susbcription"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_pricelist_view
msgid "e.g. USD Retailers"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (reference unit) = ratio * (this unit)"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_uom_form_view
msgid "e.g: 1 * (this unit) = ratio * (reference unit)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_floz
msgid "fl oz"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_foot
msgid "foot(ft)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_gal
msgid "gal(s)"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_8
#: model:product.template,name:product.product_product_8_product_template
msgid "iMac"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_6
#: model:product.template,name:product.product_product_6_product_template
msgid "iPad Mini"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_4
#: model:product.product,name:product.product_product_4b
#: model:product.product,name:product.product_product_4c
#: model:product.product,name:product.product_product_4d
#: model:product.template,name:product.product_product_4_product_template
#: model:product.template,name:product.product_product_4b_product_template
#: model:product.template,name:product.product_product_4c_product_template
#: model:product.template,name:product.product_product_4d_product_template
msgid "iPad Retina Display"
msgstr ""

#. module: product
#: model:product.product,name:product.product_product_11
#: model:product.product,name:product.product_product_11b
#: model:product.template,name:product.product_product_11_product_template
#: model:product.template,name:product.product_product_11b_product_template
msgid "iPod"
msgstr ""

#. module: product
#: model:product.product,website_description:product.product_product_11
#: model:product.product,website_description:product.product_product_11b
#: model:product.template,website_description:product.product_product_11_product_template
#: model:product.template,website_description:product.product_product_11b_product_template
msgid ""
"iTunes on your Mac or PC makes it easy to load up\n"
"                                        your iPod. Just choose the "
"playlists, audiobooks,\n"
"                                        podcasts, and other audio files you "
"want, then sync."
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_inch
msgid "inch(es)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_kgm
msgid "kg"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_km
msgid "km"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_lb
msgid "lb(s)"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_mile
msgid "mile(s)"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_template_form_view
msgid "months"
msgstr "meses"

#. module: product
#: model:product.uom,name:product.product_uom_oz
msgid "oz(s)"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_attribute_line
msgid "product.attribute.line"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_attribute_price
msgid "product.attribute.price"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_attribute_value
msgid "product.attribute.value"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_product_price_history
msgid "product.price.history"
msgstr ""

#. module: product
#: model:product.uom,name:product.product_uom_qt
msgid "qt"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_02
#: model:product.template,description_sale:product.consu_delivery_02_product_template
msgid ""
"raid 1 \n"
"            512ECC ram"
msgstr ""

#. module: product
#: model:product.product,description_sale:product.consu_delivery_01
#: model:product.template,description_sale:product.consu_delivery_01_product_template
msgid ""
"raid 10 \n"
"            2048ECC ram"
msgstr ""

#. module: product
#: model:ir.model,name:product.model_report_product_report_pricelist
msgid "report.product.report_pricelist"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.view_partner_property_form
msgid "the parent company"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_variant_easy_edit_view
msgid "the product template"
msgstr ""

#. module: product
#: model_terms:ir.ui.view,arch_db:product.product_supplierinfo_form_view
msgid "to"
msgstr "para"

#. module: product
#: model:ir.model.fields,field_description:product.field_product_price_history_company_id
#: model:ir.model.fields,field_description:product.field_product_pricelist_item_compute_price
msgid "unknown"
msgstr "desconocido"

#~ msgid "Action Needed"
#~ msgstr "Se Necesita Acción"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Fecha del ultimo mensaje actualizado en el registro."

#~ msgid "Followers"
#~ msgstr "Seguidores"

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid "Followers (Partners)"
#~ msgstr "Seguidores (Partners)"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si está marcado nuevos mensajes requieren su atención."

#~ msgid "If checked, new messages require your attention."
#~ msgstr "Si está marcado, nuevos mensajes requieren su atención."

#~ msgid "Is Follower"
#~ msgstr "Es Seguidor"

#~ msgid "Last Message Date"
#~ msgstr "Fecha del último mensaje"

#~ msgid "Messages and communication history"
#~ msgstr "Historial de mensajes y comunicación"

#~ msgid "Number of Actions"
#~ msgstr "Número de Acciones"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren acción"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leídos"

#~ msgid "Status"
#~ msgstr "Estado"

#~ msgid "Unread Messages Counter"
#~ msgstr "Contador de Mensajes sin Leer"
