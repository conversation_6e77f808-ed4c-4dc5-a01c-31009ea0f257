<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Activities -->
        <record id="mail_act_sale_upsell" model="mail.activity.type">
            <field name="name">Order Upsell</field>
            <field name="icon">fa-line-chart</field>
            <field name="res_model">sale.order</field>
        </record>

        <!-- Sale-related subtypes for messaging / Chatter -->
        <record id="mt_order_sent" model="mail.message.subtype">
            <field name="name">Quotation sent</field>
            <field name="res_model">sale.order</field>
            <field name="default" eval="False"/>
            <field name="description">Quotation sent</field>
        </record>
        <record id="mt_order_confirmed" model="mail.message.subtype">
            <field name="name">Sales Order Confirmed</field>
            <field name="res_model">sale.order</field>
            <field name="default" eval="False"/>
            <field name="description">Quotation confirmed</field>
        </record>

        <!-- Salesteam-related subtypes for messaging / Chatter -->
        <record id="mt_salesteam_order_sent" model="mail.message.subtype">
            <field name="name">Quotation sent</field>
            <field name="sequence">20</field>
            <field name="res_model">crm.team</field>
            <field name="default" eval="True"/>
            <field name="parent_id" ref="sale.mt_order_sent"/>
            <field name="relation_field">team_id</field>
        </record>
        <record id="mt_salesteam_order_confirmed" model="mail.message.subtype">
            <field name="name">Sales Order Confirmed</field>
            <field name="sequence">21</field>
            <field name="res_model">crm.team</field>
            <field name="default" eval="True"/>
            <field name="parent_id" ref="sale.mt_order_confirmed"/>
            <field name="relation_field">team_id</field>
        </record>
        <record id="mt_salesteam_invoice_created" model="mail.message.subtype">
            <field name="name">Invoice Created</field>
            <field name="sequence">22</field>
            <field name="res_model">crm.team</field>
            <field name="parent_id" ref="account.mt_invoice_created"/>
            <field name="relation_field">team_id</field>
        </record>
        <record id="mt_salesteam_invoice_confirmed" model="mail.message.subtype">
            <field name="name">Invoice Confirmed</field>
            <field name="sequence">23</field>
            <field name="res_model">crm.team</field>
            <field name="parent_id" ref="account.mt_invoice_validated"/>
            <field name="relation_field">team_id</field>
        </record>
    </data>
</odoo>
