# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_timesheet
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:54+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Record timesheets\n"
"                </p><p>\n"
"                    You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Registrazione dei fogli ore.\n"
"                </p><p>\n"
"                    È possibile registrare e monitorare le ore di lavoro giornaliere per\n"
"                    progetto. Il tempo impiegato su un progetto diventerà un costo e, se necessario, potrà\n"
"                    essere rifatturato al cliente.\n"
"                </p>\n"
"            "

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice___candidate_orders
msgid " Candidate Orders"
msgstr "Ordini candidati"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__billable_percentage
msgid ""
"% of timesheets that are billable compared to the total number of timesheets"
" linked to the AA of the project, rounded to the unit."
msgstr ""
"% di fogli ore fatturabili rispetto al numero totale di fogli ore legati "
"all'AA del progetto, arrotondato all'unità."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "%(amount)s %(label)s will be added to the new Sales Order."
msgstr "Aggiunta di %(amount)s %(label)s al nuovo ordine di vendita."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", for a revenue of"
msgstr ", per un ricavo di"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid ", leading to a"
msgstr "portando a"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "<em class=\"font-weight-normal text-muted\">Timesheets for Invoice:</em>"
msgstr "<em class=\"font-weight-normal text-muted\">Fogli ore per Fattura:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order "
"item:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Fogli ore per l'articolo "
"dell'ordine di vendita:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid ""
"<em class=\"font-weight-normal text-muted\">Timesheets for sales order:</em>"
msgstr ""
"<em class=\"font-weight-normal text-muted\">Fogli ore per ordine di "
"vendita:</em>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Icona freccia\" "
"title=\"Freccia\"/>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Time Billing</span>"
msgstr "<span class=\"o_form_label\">Fatturazione tempi</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                        Billable Time\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                        Tempo fatturabile\n"
"                        </span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.account_invoice_view_form_inherit_sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_order_form_inherit_sale_timesheet
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Registrate</span>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoiced:</strong>"
msgstr "<strong>Fatturato:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Invoices:</strong>"
msgstr "<strong>Fatturare:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>Sales Order:</strong>"
msgstr "<strong>Ordine di vendita:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_task_inherit
msgid "<strong>To invoice:</strong>"
msgstr "<strong>Da fatturare:</strong>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Profitability</u>"
msgstr "<u>Redditività</u>"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "<u>Sold</u>"
msgstr "<u>Venduto</u>"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"In base alla configurazione del prodotto, la quantità consegnata può essere calcolata automaticamente tramite una procedura.\n"
"  - Manuale: quantità impostata manualmente sulla riga\n"
"  - Analitica da spese: quantità calcolata come somma delle spese confermate\n"
"  - Foglio ore: quantità calcolata come somma delle ore registrate sui lavori collegati alla riga di vendita\n"
"  - Movimenti di magazzino: quantità proveniente da prelievi confermati\n"

#. module: sale_timesheet
#: model:project.project,name:sale_timesheet.project_support
msgid "After-Sales Services"
msgstr "Servizi post vendita"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_profitability_report__other_revenues
msgid ""
"All revenues that are not from timesheets and that are linked to the "
"analytic account of the project."
msgstr ""
"Tutti i ricavi che non provengono dai fogli ore e che sono collegati al "
"conto analitico del progetto."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_invoiced
msgid "Amount Invoiced"
msgstr "Importo Fatturato"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_invoiced
msgid "Amount Re-invoiced"
msgstr "Importo rifatturato"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__amount_untaxed_to_invoice
msgid "Amount to Invoice"
msgstr "Importo da fatturare"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_amount_untaxed_to_invoice
msgid "Amount to Re-invoice"
msgstr "Importo da rifatturare"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid "Amount to invoice"
msgstr "Importo da fatturare"

#. module: sale_timesheet
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_create_sale_order_line_unique_employee_per_wizard
#: model:ir.model.constraint,message:sale_timesheet.constraint_project_sale_line_employee_map_uniqueness_employee
msgid ""
"An employee cannot be selected more than once in the mapping. Please remove "
"duplicate(s) and try again."
msgstr ""
"Impossibile selezionare più di una volta un dipendente nella mappatura. "
"Rimuovere i duplicati e riprovare."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__analytic_account_id
msgid "Analytic Account"
msgstr "Conto analitico"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Riga analitica"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Righe analitiche"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "At least one line should be filled."
msgstr "Deve essere compilata almeno una riga."

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_manual
msgid "Based on Milestones"
msgstr "In base a milestone"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__delivered_timesheet
msgid "Based on Timesheets"
msgstr "Basato sui fogli ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__allow_billable
msgid "Billable"
msgstr "Fatturabile"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__billable_percentage
msgid "Billable Percentage"
msgstr "Percentuale Fatturabile"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Billable Time"
msgstr "Tempo fatturabile"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_type
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billable Type"
msgstr "Tipo fatturazione"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed at a Fixed Price"
msgstr "Fatturati a prezzo fisso"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_fixed
msgid "Billed at a Fixed price"
msgstr "Fatturati a prezzo fisso"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__billable_time
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Billed on Timesheets"
msgstr "Fatturati su fogli ore"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Billing"
msgstr "Fatturazione"

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_1
#: model:project.task,legend_blocked:sale_timesheet.project_task_2
#: model:project.task,legend_blocked:sale_timesheet.project_task_3
#: model:project.task,legend_blocked:sale_timesheet.project_task_internal
msgid "Blocked"
msgstr "Bloccato"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_4
msgid "Buzz or set as done"
msgstr "Chiama o imposta a completato"

#. module: sale_timesheet
#: model:ir.ui.menu,name:sale_timesheet.menu_timesheet_billing_analysis
msgid "By Billing Type"
msgstr "Per tipo fatturazione"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Cancel"
msgstr "Annulla"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__sale_order_id
msgid "Choose the Sales Order to invoice"
msgstr "Scegliere l'ordine di vendita da fatturare"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entità commerciale"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__commercial_partner_id
msgid "Commercial Partner"
msgstr "Partner Commerciale"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__company_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__company_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Company"
msgstr "Azienda"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Configure your services"
msgstr "Configura servizi"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost
msgid "Cost"
msgstr "Costo"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__cost_currency_id
msgid "Cost Currency"
msgstr "Valuta costo"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Costs"
msgstr "Costi"

#. module: sale_timesheet
#: model:ir.filters,name:sale_timesheet.ir_filter_project_profitability_report_costs_and_revenues
msgid "Costs and Revenues"
msgstr "Costi e ricavi"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_invoice
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Invoice"
msgstr "Crea fattura"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_invoice
msgid "Create Invoice from project"
msgstr "Creazione fattura da progetto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order_line
msgid "Create SO Line from project"
msgstr "Creazione riga OdV da progetto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_create_sale_order
msgid "Create SO from project"
msgstr "Creazione OdV da progetto"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/models/project.py:0
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
#, python-format
msgid "Create Sales Order"
msgstr "Crea ordine di vendita"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_invoice_view_form
msgid "Create Sales Order from Project"
msgstr "Creare ordine di vendita dal progetto"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.project_project_action_multi_create_sale_order
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_create_sale_order_view_form
msgid "Create a Sales Order"
msgstr "Crea un ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__partner_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__partner_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Customer"
msgstr "Cliente"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_order_timesheet
#: model:product.template,name:sale_timesheet.product_service_order_timesheet_product_template
msgid "Customer Care (Prepaid Hours)"
msgstr "Servizio clienti (ore prepagate)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
msgid "Customer Ratings"
msgstr "Valutazioni cliente"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__partner_id
msgid "Customer of the sales order"
msgstr "Cliente dell'ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__line_date
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Date"
msgstr "Data"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Ordered,"
msgstr "Giorni ordinati,"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Days Remaining)"
msgstr "Giorni rimanenti)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Sales Order Item"
msgstr "Voce predefinita ordine di vendita"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Default Service"
msgstr "Servizio predefinito"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__display_create_order
msgid "Display Create Order"
msgstr "Visualizzare creazione ordine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__display_name
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Effective"
msgstr "Effettivo"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__employee_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__employee_id
msgid "Employee"
msgstr "Dipendente"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__employee_rate
msgid "Employee rate"
msgstr "Tariffa dipendente"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__employee_id
msgid "Employee that has timesheets on the project."
msgstr "Dipendente che ha fogli ore sul progetto."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__sale_line_employee_ids
msgid ""
"Employee/Sale Order Item Mapping:\n"
" Defines to which sales order item an employee's timesheet entry will be linked.By extension, it defines the rate at which an employee's time on the project is billed."
msgstr ""
"Mappatura voce dipendente/ordine di lavoro:\n"
" Definisce a quale ordine di vendita viene collegata una registrazione foglio ore del dipendente. Per estensione, definisce la tariffa a cui viene fatturato il tempo del dipendente sul progetto."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
msgid "End Date"
msgstr "Data fine"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Fixed price services"
msgstr "Servizi a prezzo fisso"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__has_displayed_warning_upsell
msgid "Has Displayed Warning Upsell"
msgstr "Ha visualizzato l'avviso Upsell"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__has_multi_sol
msgid "Has Multi Sol"
msgstr "Ha righe OdV multiple"

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.product,uom_name:sale_timesheet.product_service_order_timesheet
#: model:product.product,uom_name:sale_timesheet.time_product
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_1_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_timesheet_2_product_template
#: model:product.template,uom_name:sale_timesheet.product_service_order_timesheet_product_template
#: model:product.template,uom_name:sale_timesheet.time_product_product_template
msgid "Hours"
msgstr "Ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__id
msgid "ID"
msgstr "ID"

#. module: sale_timesheet
#: model:project.task,legend_normal:sale_timesheet.project_task_1
#: model:project.task,legend_normal:sale_timesheet.project_task_2
#: model:project.task,legend_normal:sale_timesheet.project_task_3
#: model:project.task,legend_normal:sale_timesheet.project_task_4
#: model:project.task,legend_normal:sale_timesheet.project_task_internal
msgid "In Progress"
msgstr "In corso"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__info_invoice
msgid "Info Invoice"
msgstr "Informazioni fattura"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Invoice"
msgstr "Fattura"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Invoice Policy"
msgstr "Politica di fatturazione"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity) on projects or tasks you'll"
" create later on."
msgstr ""
"Fattura in base ai fogli ore (quantità consegnata) su progetti o compiti che"
" creerai in seguito."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a project for "
"the order with a task for each sales order line to track the time spent."
msgstr ""
"Fattura in base ai fogli ore (quantità consegnata), e crea un progetto per "
"l'ordine con un compito per ogni linea d'ordine di vendita per tracciare il "
"tempo speso."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create a task in an "
"existing project to track the time spent."
msgstr ""
"Fattura in base ai fogli ore (quantità consegnata), e crea un compito in un "
"progetto esistente per tracciare il tempo speso."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Invoice based on timesheets (delivered quantity), and create an empty "
"project for the order to track the time spent."
msgstr ""
"Fattura in base ai fogli ore (quantità consegnata), e crea un progetto vuoto"
" per l'ordine per tracciare il tempo speso."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_analytic_line__timesheet_invoice_id
msgid "Invoice created from the timesheet"
msgstr "Fattura creata dal foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__allow_billable
#: model:ir.model.fields,help:sale_timesheet.field_project_task__allow_billable
msgid "Invoice your time and material from tasks."
msgstr "Fattura tempo e materiale dai lavori."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoice your time and material to customers"
msgstr "Fattura tempo e materiale ai clienti"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Fatture"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_form
msgid "Invoicing"
msgstr "Fatturazione"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Invoicing Policy"
msgstr "Politica di fatturazione"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__invoicing_timesheet_enabled
msgid "Invoicing Timesheet Enabled"
msgstr "Fatturazione foglio ore abilitata"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__is_cost_changed
msgid "Is Cost Manually Changed"
msgstr "Il costo viene modificato manualmente"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__is_project_map_empty
msgid "Is Project map empty"
msgstr "Mappatura progetto vuota"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__is_so_line_edited
msgid "Is Sales Order Item Manually Edited"
msgstr "La voce dell'ordine di vendita è modificata manualmente"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_account_move_line
msgid "Journal Item"
msgstr "Movimento contabile"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_2
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_2_product_template
msgid "Junior Architect (Invoice on Timesheets)"
msgstr "Architetto junior (fatturazione su fogli ore)"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_manual
#: model:product.template,name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Kitchen Assembly (Milestones)"
msgstr "Montaggio cucina (obiettivi stabiliti)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report____last_update
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__line_ids
msgid "Lines"
msgstr "Righe"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Impostazione manuale quantità ordine: fattura basata sulle quantità inserite manualmente, senza la creazione di un conto analitico.\n"
"Fogli ore su contratto: fattura basata sulle ore registrate nel relativo foglio ore.\n"
"Creazione lavoro e registrazione ore: crea un lavoro alla conferma dell'ordine di vendita registrando le ore lavorative."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__margin
#, python-format
msgid "Margin"
msgstr "Margine"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Metodo per aggiornare le q.tà consegnate"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Milestone services"
msgstr "Servizi per obiettivo"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "My Projects"
msgstr "I miei progetti"

#. module: sale_timesheet
#: model:project.task,legend_blocked:sale_timesheet.project_task_4
msgid "Need functional or technical help"
msgstr "Richiede assistenza tecnica o funzionale"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid "No activities found"
msgstr "Nessuna attività trovata"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid "No data yet!"
msgstr "Ancora nessun dato."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
msgid "Non Billable"
msgstr "Non fatturabile"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__non_billable
msgid "Non Billable Tasks"
msgstr "Lavori non fatturabili"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_count
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_count
msgid "Number of timesheets"
msgstr "Numero di fogli ore"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_end_invoice_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid ""
"Only timesheets not yet invoiced (and validated, if applicable) from this "
"period will be invoiced. If the period is not indicated, all timesheets not "
"yet invoiced (and validated, if applicable) will be invoiced without "
"distinction."
msgstr ""
"Vengono fatturati solo i fogli ore che, nel periodo, non sono ancora "
"fatturati (e convalidati, se applicabile). Se il periodo non è indicato, "
"tutti i fogli ore non ancora fatturati (e convalidati, se applicabile) senza"
" distinzione."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operazione non supportata"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__order_id
msgid "Order Reference"
msgstr "Riferimento ordine"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Ordered,"
msgstr "Ordinato,"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__expense_cost
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_costs
msgid "Other Costs"
msgstr "Altri costi"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__other_revenues
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__other_revenues
msgid "Other Revenues"
msgstr "Altri ricavi"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,help:sale_timesheet.field_product_template__service_upsell_threshold
msgid ""
"Percentage of time delivered compared to the prepaid amount that must be "
"reached for the upselling opportunity activity to be triggered."
msgstr ""
"Percentuale di tempo consegnato rispetto all'importo prepagato che deve "
"essere raggiunto per far scattare l'attività dell'opportunità di upselling."

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Planned"
msgstr "Pianificata"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_policy__ordered_timesheet
msgid "Prepaid/Fixed Price"
msgstr "Prezzo fisso/prepagato"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__pricing_type
msgid "Pricing"
msgstr "Tariffazione"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_product
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__product_id
msgid "Product"
msgstr "Prodotto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_product_template
msgid "Product Template"
msgstr "Modello prodotto"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__product_id
msgid ""
"Product of the sales order item. Must be a service invoiced based on "
"timesheets on tasks."
msgstr ""
"Prodotto della voce dell'ordine di vendita. Deve essere un servizio "
"fatturato sulla base dei fogli ore sui lavori."

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Profitability"
msgstr "Redditività"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_graph
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Profitability Analysis"
msgstr "Analisi redditività"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_project
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_invoice__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__project_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__project_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project"
msgstr "Progetto"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__company_id
msgid "Project Company"
msgstr "Azienda del progetto"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__currency_id
msgid "Project Currency"
msgstr "Valuta progetto"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__user_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Project Manager"
msgstr "Responsabile progetto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_profitability_report
msgid "Project Profitability Report"
msgstr "Rendiconto sulla redditività del progetto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Mappatura riga vendite e dipendenti del progetto"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__project_template_id
msgid "Project Template"
msgstr "Modello progetto"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_update
msgid "Project Update"
msgstr "Aggiornamento del progetto"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order__project_id
msgid "Project for which we are creating a sales order"
msgstr "Progetto per il quale viene creato un ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__fixed_rate
msgid "Project rate"
msgstr "Tariffa progetto"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__project_id
msgid "Project to make billable"
msgstr "Progetto da rendere fatturabile"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Rating"
msgstr "Valutazione"

#. module: sale_timesheet
#: model:project.task,legend_done:sale_timesheet.project_task_1
#: model:project.task,legend_done:sale_timesheet.project_task_2
#: model:project.task,legend_done:sale_timesheet.project_task_3
#: model:project.task,legend_done:sale_timesheet.project_task_internal
msgid "Ready"
msgstr "Pronto"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Remaining"
msgstr "Residue"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Days on SO"
msgstr "Giorni rimanenti sull'ordine di vendita"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Days on SO:"
msgstr "Giorni rimanenti sull'ordine di vendita:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_available
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours_available
msgid "Remaining Hours Available"
msgstr "Ore rimanenti disponibili"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__remaining_hours_so
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order_line__remaining_hours
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_task_view_form_inherit_sale_timesheet
msgid "Remaining Hours on SO"
msgstr "Ore rimanenti sull'ordine di vendita"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_timesheet_table_inherit
msgid "Remaining Hours on SO:"
msgstr "Ore rimanenti sull'ordine di vendita:"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Remaining)"
msgstr "Rimanente)"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Revenues"
msgstr "Entrate"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.timesheet_action_billing_report
msgid ""
"Review your timesheets by billing type and make sure your time is billable."
msgstr ""
"Esamina i tuoi fogli ore in base al tipo di fatturazione e assicurati che il"
" tuo tempo sia fatturabile."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_order_id
msgid "Sale Order"
msgstr "Ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__so_analytic_account_id
msgid "Sale Order Analytic Account"
msgstr "Conto analitico ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__sale_line_id
msgid "Sale Order Item"
msgstr "Voce ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__sale_line_id
msgid "Sale Order Line"
msgstr "Riga ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__sale_line_employee_ids
msgid "Sale line/Employee map"
msgstr "Mappatura riga di vendita/dipendente"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Fattura di vendita con pagamento anticipato"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model,name:sale_timesheet.model_sale_order
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order__sale_order_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_project_view_kanban_inherit_sale_timesheet
#, python-format
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__order_confirmation_date
msgid "Sales Order Confirmation Date"
msgstr "Data di conferma ordine di vendita"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#: code:addons/sale_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_timesheet.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_search
#, python-format
msgid "Sales Order Item"
msgstr "Voce ordine di vendita"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity."
msgstr ""
"Le linee dell'ordine di vendita definiscono le pietre miliari del progetto "
"da fatturare impostando la quantità consegnata."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"Le linee dell'ordine di vendita definiscono le milestone del progetto da "
"fatturare impostando la quantità consegnata. Crea un progetto per l'ordine "
"con un compito per ogni linea d'ordine di vendita per tracciare il tempo "
"speso."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create a task in an existing project to track the time "
"spent."
msgstr ""
"Le linee dell'ordine di vendita definiscono le milestone del progetto da "
"fatturare impostando la quantità consegnata. Crea un compito in un progetto "
"esistente per tracciare il tempo impiegato."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"Sales order lines define milestones of the project to invoice by setting the"
" delivered quantity. Create an empty project for the order to track the time"
" spent."
msgstr ""
"Le linee dell'ordine di vendita definiscono le pietre miliari del progetto "
"da fatturare impostando la quantità consegnata. Crea un progetto vuoto per "
"l'ordine per tracciare il tempo impiegato."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Ordine di vendita al quale è collegato il lavoro."

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "Ricerca in fattura"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "Ricerca in ordine di vendita"

#. module: sale_timesheet
#: code:addons/sale_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order Item"
msgstr "Ricerca articolo dell'ordine di vendita"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,help:sale_timesheet.field_project_task__timesheet_product_id
msgid ""
"Select a Service product with which you would like to bill your time spent "
"on tasks."
msgstr ""
"Selezionare un prodotto servizio con cui fatturare il tempo impiegato nei "
"lavori."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_id
msgid ""
"Select a billable project on which tasks can be created. This setting must "
"be set for each company."
msgstr ""
"Selezionare un progetto fatturabile nel quale sia possibile creare lavori. "
"L'opzione deve essere impostata per ciascuna azienda."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_product_product__project_template_id
#: model:ir.model.fields,help:sale_timesheet.field_product_template__project_template_id
msgid ""
"Select a billable project to be the skeleton of the new created project when"
" selling the current product. Its stages and tasks will be duplicated."
msgstr ""
"Selezionare un progetto fatturabile da usare come schema del nuovo progetto "
"creato quando viene venduto il prodotto corrente. Le fasi e i lavori vengono"
" duplicati."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""
"Selezionare un progetto non fatturabile nel quale possano essere creati "
"lavori."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Sell services and invoice time spent"
msgstr "Vendita di servizi e fatturazione del tempo impiegato"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.product_service_deliver_timesheet_1
#: model:product.template,name:sale_timesheet.product_service_deliver_timesheet_1_product_template
msgid "Senior Architect (Invoice on Timesheets)"
msgstr "Architetto senior (fatturazione su fogli ore)"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__product_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Service"
msgstr "Servizio"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Politica di fatturazione del servizio"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__service_revenues
msgid "Service Revenues"
msgstr "Ricavi da servizi"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold_ratio
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold_ratio
msgid "Service Upsell Threshold Ratio"
msgstr "Rapporto limite servizio di upselling"

#. module: sale_timesheet
#: model:product.product,name:sale_timesheet.time_product
#: model:product.template,name:sale_timesheet.time_product_product_template
msgid "Service on Timesheet"
msgstr "Servizio su foglio ore"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.product_template_action_default_services
msgid "Services"
msgstr "Servizi"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
#, python-format
msgid "Sold"
msgstr "Vendute"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_advance_payment_inv__date_start_invoice_timesheet
msgid "Start Date"
msgstr "Data inizio"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount Invoiced"
msgstr "Somma dell'importo fatturato"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Amount to Invoice"
msgstr "Somma dell'importo da fatturare"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Margin"
msgstr "Somma del margine"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_tree
msgid "Sum of Timesheet Cost"
msgstr "Somma del costo del foglio ore"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__task_id
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_profitability_report_view_search
msgid "Task"
msgstr "Lavoro"

#. module: sale_timesheet
#: model:ir.model,name:sale_timesheet.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Ricorrenza lavoro"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__project_project__pricing_type__task_rate
msgid "Task rate"
msgstr "Valutazione attività"

#. module: sale_timesheet
#: model:project.project,label_tasks:sale_timesheet.project_support
msgid "Tasks"
msgstr "Lavori"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#: code:addons/sale_timesheet/models/product.py:0
#, python-format
msgid ""
"The %s product is required by the Timesheets app and cannot be archived nor "
"deleted."
msgstr ""
"Il prodotto %s è richiesto dall'applicazione Schede di presenza e non può "
"essere archiviato o cancellato."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The Sales Order cannot be created because you did not enter some employees that entered timesheets on this project. Please list all the relevant employees before creating the Sales Order.\n"
"Missing employee(s): %s"
msgstr ""
"L'ordine di vendita non può essere creato perché non sono stati aggiunti alcuni dipendenti che hanno inserito fogli ore in questo progetto. Aggiungere le persone interessate prima di creare l'ordine di vendita.\n"
"Dipendenti mancanti: %s"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_task__so_analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "Conto analitico collegato a un ordine di vendita."

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "The cost of the project is now at"
msgstr "Il costo del progetto è ora a"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project has already a sale order."
msgstr "Il progetto ha già un ordine di vendita."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "The project is already linked to a sales order item."
msgstr "Progetto già collegato a una voce dell'ordine di vendita."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid ""
"The sales order cannot be created because some timesheets of this project "
"are already linked to another sales order."
msgstr ""
"L'ordine di vendita non può essere creato perché alcuni fogli ore di questo "
"progetto sono già collegati ad un altro ordine."

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#, python-format
msgid "The selected Sales Order should contain something to invoice."
msgstr "L'ordine di vendita selezionato deve contenere qualcosa da fatturare."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_project__pricing_type
#: model:ir.model.fields,help:sale_timesheet.field_project_task__pricing_type
msgid ""
"The task rate is perfect if you would like to bill different services to "
"different customers at different rates. The fixed rate is perfect if you "
"bill a service at a fixed rate per hour or day worked regardless of the "
"employee who performed it. The employee rate is preferable if your employees"
" deliver the same service at a different rate. For instance, junior and "
"senior consultants would deliver the same service (= consultancy), but at a "
"different rate because of their level of seniority."
msgstr ""
"La tariffa per attività è perfetta se vuoi fatturare servizi diversi a "
"clienti diversi a tariffe diverse. La tariffa fissa è perfetta se fatturi un"
" servizio a una tariffa fissa per ora o giorno lavorato indipendentemente "
"dal dipendente che lo ha eseguito. La tariffa dipendente è preferibile se i "
"tuoi dipendenti forniscono lo stesso servizio a una tariffa diversa. Per "
"esempio, i consulenti junior e senior fornirebbero lo stesso servizio (= "
"consulenza), ma ad una tariffa diversa a seconda del loro livello di "
"anzianità."

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_sale_line_employee_map__cost
msgid ""
"This cost overrides the employee's default timesheet cost in employee's HR "
"Settings"
msgstr ""
"Questo costo sovrascrive il costo predefinito del timesheet del dipendente "
"nelle impostazioni HR del dipendente"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid ""
"This will set the unit of measure used to encode timesheet. This will simply provide tools\n"
"        and widgets to help the encoding. All reporting will still be expressed in hours (default value)."
msgstr ""
"Verrà impostata l'unità di misura usata nei fogli ore, con la disponibilità di strumenti e oggetti\n"
"        utili alla gestione. Le rendicontazioni resteranno tutte espresse in ore (valore predefinito)."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_upsell_threshold
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_upsell_threshold
msgid "Threshold"
msgstr "Soglia"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.product_template_view_search_sale_timesheet
msgid "Time-based services"
msgstr "Servizi basati su tempi"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_plan
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_plan_pivot
#: model_terms:ir.ui.view,arch_db:sale_timesheet.timesheet_view_pivot_revenue
msgid "Timesheet"
msgstr "Foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_cost
msgid "Timesheet Cost"
msgstr "Costo foglio ore"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
msgid "Timesheet Costs"
msgstr "Costi foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_profitability_report__timesheet_unit_amount
msgid "Timesheet Duration"
msgstr "Durata foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr "Unità di codifica foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__timesheet_product_id
#: model:ir.model.fields,field_description:sale_timesheet.field_project_task__timesheet_product_id
msgid "Timesheet Product"
msgstr "Prodotto foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__account_analytic_line__timesheet_invoice_type__timesheet_revenues
msgid "Timesheet Revenues"
msgstr "Ricavi da fogli di lavoro"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_total_duration
msgid "Timesheet Total Duration"
msgstr "Durata totale foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_count
msgid "Timesheet activities"
msgstr "Attività foglio ore"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_sale_order__timesheet_ids
msgid "Timesheet activities associated to this sale"
msgstr "Attività del foglio ore associate a questa vendita"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account_move.py:0
#: model:ir.actions.act_window,name:sale_timesheet.action_timesheet_from_invoice
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_from_sales_order
#: model:ir.model.fields,field_description:sale_timesheet.field_account_bank_statement_line__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_move__timesheet_ids
#: model:ir.model.fields,field_description:sale_timesheet.field_account_payment__timesheet_ids
#: model:ir.model.fields.selection,name:sale_timesheet.selection__sale_order_line__qty_delivered_method__timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_hr_timesheet_line_pivot_billing_rate
#, python-format
msgid "Timesheets"
msgstr "Fogli ore"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_advance_payment_inv_timesheet_view_form
msgid "Timesheets Period"
msgstr "Periodo fogli ore"

#. module: sale_timesheet
#: model:ir.actions.act_window,name:sale_timesheet.timesheet_action_billing_report
msgid "Timesheets by Billing Type"
msgstr "Fogli ore in base al tipo di fatturazione"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Timesheets of %s"
msgstr "Fogli ore di %s"

#. module: sale_timesheet
#: model:ir.model.fields.selection,name:sale_timesheet.selection__product_template__service_type__timesheet
msgid "Timesheets on project (one fare per SO/Project)"
msgstr "Fogli ore su progetto (una tariffa per OdV/progetto)"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.res_config_settings_view_form
msgid "Timesheets taken into account when invoicing your time"
msgstr "Fogli ore presi in considerazione per la fatturazione del tempo"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_res_config_settings__invoice_policy
msgid "Timesheets taken when invoicing time spent"
msgstr "Fogli ore utilizzati per la fatturazione del tempo speso"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "Total"
msgstr "Totale"

#. module: sale_timesheet
#. openerp-web
#: code:addons/sale_timesheet/static/src/xml/sale_project_templates.xml:0
#, python-format
msgid "Total Sold"
msgstr "Totale venduto"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_invoice__amount_to_invoice
msgid ""
"Total amount to invoice on the sales order, including all items (services, "
"storables, expenses, ...)"
msgstr ""
"Importo totale da fatturare per l'ordine di vendita, incluse tutte le voci "
"(servizi, conservabili, spese ecc....)"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_account_bank_statement_line__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_move__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_account_payment__timesheet_total_duration
#: model:ir.model.fields,help:sale_timesheet.field_sale_order__timesheet_total_duration
msgid ""
"Total recorded duration, expressed in the encoding UoM, and rounded to the "
"unit"
msgstr ""
"Durata registrata totale, espressa nella UdM di codifica e arrotondata "
"all'unità"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_my_timesheets_inherit
msgid "Total:"
msgstr "Totale:"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_timesheet.field_product_template__service_type
msgid "Track Service"
msgstr "Monitoraggio servizio"

#. module: sale_timesheet
#: model_terms:ir.actions.act_window,help:sale_timesheet.action_timesheet_from_invoice
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""
"Tieni traccia delle ore lavorative giornaliere per progetto e fatturale ai "
"clienti."

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__price_unit
#: model:ir.model.fields,field_description:sale_timesheet.field_project_sale_line_employee_map__price_unit
msgid "Unit Price"
msgstr "Prezzo unitario"

#. module: sale_timesheet
#: model:ir.model.fields,help:sale_timesheet.field_project_create_sale_order_line__price_unit
msgid "Unit price of the sales order item."
msgstr "Prezzo unitario voce dell'ordine di vendita."

#. module: sale_timesheet
#: model:product.product,uom_name:sale_timesheet.product_service_deliver_manual
#: model:product.template,uom_name:sale_timesheet.product_service_deliver_manual_product_template
msgid "Units"
msgstr "Unità"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid "Value does not exist in the pricing type"
msgstr "Il valore non esiste nel tipo di prezzo"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.portal_invoice_page_inherit
#: model_terms:ir.ui.view,arch_db:sale_timesheet.sale_order_portal_content_inherit
msgid "View Timesheets"
msgstr "Visualizza fogli ore"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "Warn the salesperson for an upsell when work done exceeds"
msgstr "Avvertire il venditore per un upsell quando il lavoro fatto supera"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_project__warning_employee_rate
msgid "Warning Employee Rate"
msgstr "Avviso tariffa dipendente"

#. module: sale_timesheet
#: model:ir.model.fields,field_description:sale_timesheet.field_project_create_sale_order_line__wizard_id
msgid "Wizard"
msgstr "Procedura guidata"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_invoice.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "You can only apply this action from a project."
msgstr "L'azione può essere applicata solo da un progetto."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that comes from an "
"expense or a vendor bill."
msgstr ""
"Non è possibile collegare un progetto fatturabile a un elemento dell'ordine "
"di vendita che proviene da una spesa o da una fattura di un venditore."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/project.py:0
#, python-format
msgid ""
"You cannot link a billable project to a sales order item that is not a "
"service."
msgstr ""
"Non puoi collegare un progetto fatturabile a un elemento dell'ordine di "
"vendita che non è un servizio."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot modify timesheets that are already invoiced."
msgstr "Non puoi modificare i fogli ore che sono già fatturati."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/account.py:0
#, python-format
msgid "You cannot remove a timesheet that has already been invoiced."
msgstr "Impossibile rimuovere un foglio ore che è già stato fatturato."

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#, python-format
msgid "day"
msgstr "giorno"

#. module: sale_timesheet
#: code:addons/sale_timesheet/models/sale_order.py:0
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "days"
msgstr "giorni"

#. module: sale_timesheet
#: code:addons/sale_timesheet/wizard/project_create_sale_order.py:0
#, python-format
msgid "hours"
msgstr "ore"

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.project_update_default_description
msgid "margin ("
msgstr "margine ("

#. module: sale_timesheet
#: model_terms:ir.ui.view,arch_db:sale_timesheet.view_product_timesheet_form
msgid "of hours sold. ("
msgstr "delle ore vendute. ("
