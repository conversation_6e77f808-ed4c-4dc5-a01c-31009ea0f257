# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_dropshipping
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting_dropshipping.route_subcontracting_dropshipping
#, python-format
msgid "Dropship Subcontractor on Order"
msgstr "Sipariş Üzerine Transit Fason "

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship Subcontractors"
msgstr "Transit Fason"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship subcontractors with components"
msgstr "Bileşenlerle fason firmaya transit"

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
#, python-format
msgid ""
"It appears some components in this RFQ are not meant for subcontracting. "
"Please create a separate order for these."
msgstr ""
"Bu RFQ'daki bazı bileşenlerin taşeronluk amaçlı olmadığı anlaşılıyor. Lütfen"
" bunlar için ayrı bir sipariş oluşturun."

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "Satınalma Siparişi"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "Stok Kuralı"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_pull_id
msgid "Subcontracting-Dropshipping MTS Rule"
msgstr "Fason-Transit MTS Kuralı"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "Aktarım"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse
msgid "Warehouse"
msgstr "Depo"
