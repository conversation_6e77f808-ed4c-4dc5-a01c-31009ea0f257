# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid ""
".\n"
"                Manual actions may be needed."
msgstr ""
".\n"
"                Можливо знадобляться ручні дії."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Preparation</b>"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> <b>Підготовка</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr "<i class=\"fa fa-fw fa-times\"/> <b>Скасовано</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<i class=\"fa fa-fw fa-truck\"/> <b>Shipped</b>"
msgstr "<i class=\"fa fa-fw fa-truck\"/> <b>Доставлено</b>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" role=\"img\" aria-label=\"Values set here are company-"
"specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">Продажі</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Продано</span>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_delivery_document_inherit_sale_stock
msgid "<strong>Customer Reference:</strong>"
msgstr "<strong>Референс клієнта:</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Замовлення на доставку</strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "<strong>Incoterm: </strong>"
msgstr "<strong>Інкотерм: </strong>"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_invoice_document_inherit_sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.report_saleorder_document_inherit_sale_stock
msgid "<strong>Incoterm:</strong>"
msgstr "<strong>Інкотерм:</strong>"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Товар, що зберігається, - це товар, яким ви керуєте на складі. Необхідно встановити додаток Склад.\n"
"Витратний товар - це товар, для якого склад не керується.\n"
"Послуга - це нематеріальний товар, який ви надаєте."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Відповідно до налаштування товару, доставлена кількість може автоматично обчислюватися за допомогою механізму:\n"
"   - Вручну: кількість на рядку встановлюється вручну\n"
"   - Аналітика з витрат: кількість - сума від опублікованих витрат\n"
"   - Табель: кількість - це сума годин, записаних на завданні, пов'язані з цим рядком продажу\n"
"   - Складські переміщення: кількість надходить від підтверджених комплектувань\n"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_aftersale
msgid "After-Sale"
msgstr "Допродаж"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "All planned operations included"
msgstr "Включені усі заплановані операції"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_rules_report__so_route_ids
msgid "Apply specific routes"
msgstr "Застосуйте конкретні маршрути"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__direct
msgid "As soon as possible"
msgstr "Якомога швидше"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/js/qty_at_date_widget.js:0
#, python-format
msgid "Availability"
msgstr "Наявність"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available"
msgstr "В наявності"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Available in stock"
msgstr "Доступний на складі"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_stock_rules_report__so_route_ids
msgid "Choose to apply SO lines specific routes."
msgstr ""
"Виберіть для застосування рядків спеціальних маршрутів замовлення на продаж."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__effective_date
msgid "Completion date of the first delivery order."
msgstr "Дата завершення першого замовлення на доставку."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_portal_content_inherit_sale_stock
msgid "Date:"
msgstr "Дата:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_users__property_warehouse_id
msgid "Default Warehouse"
msgstr "Склад за замовчуванням"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Delivery"
msgstr "Доставка"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_cancel__display_delivery_alert
msgid "Delivery Alert"
msgstr "Сповіщення доставки"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__delivery_count
msgid "Delivery Orders"
msgstr "Замовлення на доставку"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Дата доставки, яку ви можете гарантувати клієнту, вираховується з "
"мінімального терміну виконання рядків замовлень у випадку товарів послуг. У "
"випадку доставки, політика доставки замовлення буде враховувати або "
"використовувати мінімальний або максимальний час виконання рядків "
"замовлення."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__display_qty_widget
msgid "Display Qty Widget"
msgstr "Відобразити віджет кількості"

#. module: sale_stock
#: model:res.groups,name:sale_stock.group_display_incoterm
msgid "Display incoterms on Sales Order and related invoices"
msgstr ""
"Відображати інкотермс на замовленні на продаж та пов'язаних рахунках-"
"фактурах"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Display incoterms on orders &amp; invoices"
msgstr "Відображати інкотермс на замовленнях та рахунках-фактурах"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"Do not forget to change the partner on the following delivery orders: %s"
msgstr "Не забудьте змінити партнера на наступному замовленні на доставку: %s"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Documentation"
msgstr "Документація"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__effective_date
msgid "Effective Date"
msgstr "Дата відвантаження"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "Exception(s) occurred on the picking:"
msgstr "Виняток стався під час комплектування:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Винятки виникли в замовленні на продаж:"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Exception(s):"
msgstr "Винятки:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Очікувана дата"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Expected Delivery"
msgstr "Очікувана доставка"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.view_order_form_inherit_sale_stock
msgid "Expected:"
msgstr "Очікується:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__forecast_expected_date
msgid "Forecast Expected Date"
msgstr "Прогноз очікуваної дати"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Forecasted Stock"
msgstr "Прогнозований запас"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__free_qty_today
msgid "Free Qty Today"
msgstr "Безкоштовна кількість сьогодні"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__show_json_popover
msgid "Has late picking"
msgstr "Має протерміновані надходження"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"Якщо ви доставляєте всі товари одразу, замовлення на доставку буде "
"заплановано на основі найбільшого часу виконання товару. В іншому випадку "
"вона буде заснована на найкоротшому терміні."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Impacted Transfer(s):"
msgstr "Переміщення, що зазнали впливу:"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__incoterm
msgid "Incoterm"
msgstr "Інкотерм"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__group_display_incoterm
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_sale
msgid "Incoterms"
msgstr "Інкотермс"

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_sale_order__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""
"Міжнародні комерційні умови, комплект міжнародних правил з тлумачення "
"найбільш широко використовуваних торговельних термінів в галузі міжнародної "
"торгівлі."

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_users_view_form
msgid "Inventory"
msgstr "Склад"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_location_route
msgid "Inventory Routes"
msgstr "Маршрути інвентаризації"

#. module: sale_stock
#: model:ir.ui.menu,name:sale_stock.menu_invoiced
msgid "Invoicing"
msgstr "Виставлення рахунків"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__is_mto
msgid "Is Mto"
msgstr "Виробництво на замовлення"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__json_popover
msgid "JSON data for the popover widget"
msgstr "Дані JSON для віджета спливаючого вікна"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Партія/Серійний номер"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Make To Order"
msgstr "Виготовлення на замовлення"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "Manual actions may be needed."
msgstr "Можуть бути необхідні дії вручну."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__use_security_lead
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for delivery that many days earlier than the actual promised date, to cope "
"with unexpected delays in the supply chain."
msgstr ""
"Маржа помилки для дат, обіцяних покупцям. Товари заплановані на доставку на "
"багато днів раніше, ніж фактична обіцяна дата, щоби впоратися з "
"несподіваними затримками в ланцюжку постачання."

#. module: sale_stock
#: model:ir.model.fields,help:sale_stock.field_res_company__security_lead
#: model:ir.model.fields,help:sale_stock.field_res_config_settings__security_lead
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid ""
"Margin of error for dates promised to customers. Products will be scheduled "
"for procurement and delivery that many days earlier than the actual promised"
" date, to cope with unexpected delays in the supply chain."
msgstr ""
"Межа відхилення для терміну постачання товарів замовникам. Поповнення "
"запасів та дата відвантаження буде запланована на цю кількість днів раніше "
"ніж обіцяна дата, щоби впоратися з несподіваними затримками в ланцюгу "
"постачань."

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Метод оновлення доставленої кількості"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Move forward expected delivery dates by"
msgstr "Очікувані наступні дати доставки"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No enough future availaibility"
msgstr "Немає достатньої доступної кількості в майбутньому"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "No future availaibility"
msgstr "Недоступний у майбутньому"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "On"
msgstr "Увімкнути"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Замовлена кількість зменшилась!"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "Політика пакування"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_procurement_group
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__procurement_group_id
msgid "Procurement Group"
msgstr "Група забезпечення"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_product_template
msgid "Product Template"
msgstr "Шаблон товару"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Тип товару"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_available_today
msgid "Qty Available Today"
msgstr "К-сть доступна сьогодні"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr "К-сть для доставки"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_report_product_product_replenishment
msgid "Quotations"
msgstr "Комерційні пропозиції"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Remaining demand available at"
msgstr "Залишок попиту доступний в"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "Reserved"
msgstr "Зарезервовано"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__route_id
msgid "Route"
msgstr "Маршрут"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_move__sale_line_id
msgid "Sale Line"
msgstr "Рядок продажу"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_procurement_group__sale_id
msgid "Sale Order"
msgstr "Замовлення на продаж"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_production_lot_view_form
msgid "Sale Orders"
msgstr "Замовлення на продаж"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_count
msgid "Sale order count"
msgstr "Розрахунок замовлення на продаж"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_report
msgid "Sales Analysis Report"
msgstr "Звіт аналізу продажів"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order
#: model:ir.model.fields,field_description:sale_stock.field_stock_picking__sale_id
msgid "Sales Order"
msgstr "Замовлення на продаж"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Замовлення на продаж скасовано"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_sale_order_line
msgid "Sales Order Line"
msgstr "Рядок замовлення на продаж"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.stock_location_route_view_form_inherit_sale_stock
msgid "Sales Order Lines"
msgstr "Рядки замовлення на продаж"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_production_lot__sale_order_ids
msgid "Sales Orders"
msgstr "Замовлення на продаж"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_company__security_lead
msgid "Sales Safety Days"
msgstr "Дні підстрахування для продажів"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "Schedule deliveries earlier to avoid delays"
msgstr "Плануйте постачання раніше, щоб уникнути затримок"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__scheduled_date
msgid "Scheduled Date"
msgstr "Запланована дата"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Час проведення безпеки"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_res_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Безпека часу проведення для продажів"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_stock_location_route__sale_selectable
msgid "Selectable on Sales Order Line"
msgstr "Можна обирати у рядках"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__one
msgid "Ship all products at once"
msgstr "Завантажте всі товари одразу"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__res_config_settings__default_picking_policy__direct
msgid "Ship products as soon as available, with back orders"
msgstr "Якнайшвидша доставка товарів, зі зворотним замовленням"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_policy
msgid "Shipping Policy"
msgstr "Політика доставки"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.product_template_view_form_inherit_stock
msgid "Sold in the last 365 days"
msgstr "Продано за останні 365 днів"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.sale_order_cancel_view_form_inherit
msgid ""
"Some products have already been delivered. Returns can be created from the "
"Delivery Orders."
msgstr ""
"Деякі товари вже доставлені. Повернення можна створювати за допомогою "
"Замовлень на доставку."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_move
msgid "Stock Move"
msgstr "Складське переміщення "

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__move_ids
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order_line__qty_delivered_method__stock_move
msgid "Stock Moves"
msgstr "Складські переміщення"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_product_product_replenishment
msgid "Stock Replenishment Report"
msgstr "Звіт поповнення складу"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rule
msgid "Stock Rule"
msgstr "Складське правило"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "Звіт правил запасу"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "Звіт правила запасу"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "The delivery"
msgstr "Доставка"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"The delivery address has been changed on the Sales Order<br/>\n"
"                        From <strong>\"%s\"</strong> To <strong>\"%s\"</strong>,\n"
"                        You should probably update the partner on this document."
msgstr ""
"Адреса доставки була змінена на замовленні на продаж<br/>\n"
"                        З <strong>\"%s\"</strong> на <strong>\"%s\"</strong>,\n"
"                        Вам необхідно оновити партнера на цьому документі."

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "This product is replenished on demand."
msgstr "Цей товар поповнюється на вимогу."

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_stock_picking
msgid "Transfer"
msgstr "Переміщення"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__picking_ids
msgid "Transfers"
msgstr "Переміщення"

#. module: sale_stock
#: model:ir.model,name:sale_stock.model_res_users
msgid "Users"
msgstr "Користувачі"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "View Forecast"
msgstr "Переглянути прогноз"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr "Віртуальний доступний на дату"

#. module: sale_stock
#: model:ir.model.fields,field_description:sale_stock.field_sale_order__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_order_line__warehouse_id
#: model:ir.model.fields,field_description:sale_stock.field_sale_report__warehouse_id
msgid "Warehouse"
msgstr "Склад"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid "Warning!"
msgstr "Увага!"

#. module: sale_stock
#: model:ir.model.fields.selection,name:sale_stock.selection__sale_order__picking_policy__one
msgid "When all products are ready"
msgstr "Коли усі товари готові"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "When to start shipping"
msgstr "Коли розпочати доставку"

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the delivery order if needed."
msgstr ""
"Ви зменшили кількість у замовленні! Не забудьте вручну змінити кількість у "
"замовленні на доставку, якщо це потрібно."

#. module: sale_stock
#: code:addons/sale_stock/models/sale_order.py:0
#, python-format
msgid ""
"You cannot decrease the ordered quantity of a sale order line below its delivered quantity.\n"
"Create a return in your inventory first."
msgstr ""

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "cancelled"
msgstr "Скасовано"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.res_config_settings_view_form_stock
msgid "days"
msgstr "дні"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "of"
msgstr "від"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_so
msgid "ordered instead of"
msgstr "замовлено замість"

#. module: sale_stock
#: model_terms:ir.ui.view,arch_db:sale_stock.exception_on_picking
msgid "processed instead of"
msgstr "обробляється замість"

#. module: sale_stock
#. openerp-web
#: code:addons/sale_stock/static/src/xml/sale_stock.xml:0
#, python-format
msgid "will be late."
msgstr "буде запізнено."
