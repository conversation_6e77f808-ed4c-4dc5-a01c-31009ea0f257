<?xml version="1.0"?>
<odoo><data>
    <!-- Design fair -->
    <record id="event_registration_0_0" model="event.registration">
        <field name="event_id" ref="event.event_0"/>
        <field name="event_ticket_id" ref="event.event_0_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_1"/>
    </record>
    <record id="event_registration_0_1" model="event.registration">
        <field name="event_id" ref="event.event_0"/>
        <field name="event_ticket_id" ref="event.event_0_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_2"/>
    </record>
    <record id="event_registration_0_2" model="event.registration">
        <field name="event_id" ref="event.event_0"/>
        <field name="event_ticket_id" ref="event.event_0_ticket_0"/>
        <field name="name"><PERSON></field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>

    <!-- <PERSON> Race -->
    <record id="event_registration_1_0" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="partner_id" ref="base.res_partner_address_1"/>
    </record>
    <record id="event_registration_1_1" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="partner_id" ref="base.res_partner_address_2"/>
    </record>
    <record id="event_registration_1_2" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="name">Piers Morgan</field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>
    <record id="event_registration_1_3" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="partner_id" ref="base.res_partner_address_3"/>
    </record>
    <record id="event_registration_1_4" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="partner_id" ref="base.res_partner_address_4"/>
    </record>
    <record id="event_registration_1_5" model="event.registration">
        <field name="event_id" ref="event.event_1"/>
        <field name="name">Nigel Woodfire</field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>

    <!-- Conference for architects -->
    <record id="event_registration_2_0" model="event.registration">
        <field name="event_id" ref="event.event_2"/>
        <field name="event_ticket_id" ref="event.event_2_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_1"/>
    </record>
    <record id="event_registration_2_1" model="event.registration">
        <field name="event_id" ref="event.event_2"/>
        <field name="event_ticket_id" ref="event.event_2_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_2"/>
    </record>
    <record id="event_registration_2_2" model="event.registration">
        <field name="event_id" ref="event.event_2"/>
        <field name="event_ticket_id" ref="event.event_2_ticket_2"/>
        <field name="name">Piers Morgan</field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>
    <record id="event_registration_2_3" model="event.registration">
        <field name="event_id" ref="event.event_2"/>
        <field name="event_ticket_id" ref="event.event_2_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_3"/>
    </record>
    <record id="event_registration_2_4" model="event.registration">
        <field name="event_id" ref="event.event_2"/>
        <field name="event_ticket_id" ref="event.event_2_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_4"/>
    </record>

    <!-- Live Music Festival -->
    <record id="event_registration_3_0" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="partner_id" ref="base.res_partner_address_1"/>
    </record>
    <record id="event_registration_3_1" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="partner_id" ref="base.res_partner_address_2"/>
    </record>
    <record id="event_registration_3_2" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="name">Piers Morgan</field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>
    <record id="event_registration_3_3" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="partner_id" ref="base.res_partner_address_3"/>
    </record>
    <record id="event_registration_3_4" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="partner_id" ref="base.res_partner_address_4"/>
    </record>
    <record id="event_registration_3_5" model="event.registration">
        <field name="event_id" ref="event.event_3"/>
        <field name="name">Nigel Woodfire</field>
        <field name="email"><EMAIL></field>
        <field name="partner_id" eval="False"/>
    </record>

    <!-- Business Workshop -->
    <record id="event_registration_4_0" model="event.registration">
        <field name="event_id" ref="event.event_4"/>
        <field name="event_ticket_id" ref="event.event_4_ticket_0"/>
        <field name="partner_id" ref="base.res_partner_address_7"/>
    </record>
    <record id="event_registration_4_1" model="event.registration">
        <field name="event_id" ref="event.event_4"/>
        <field name="event_ticket_id" ref="event.event_4_ticket_0"/>
        <field name="partner_id" ref="base.res_partner_address_13"/>
    </record>
    <record id="event_registration_4_2" model="event.registration">
        <field name="event_id" ref="event.event_4"/>
        <field name="event_ticket_id" ref="event.event_4_ticket_0"/>
        <field name="partner_id" ref="base.res_partner_address_14"/>
    </record>

    <!-- OpenWood Collection Online Reveal: Gemini (all) -->
    <record id="event_registration_7_0" model="event.registration">
        <field name="event_id" ref="event.event_7"/>
        <field name="event_ticket_id" ref="event.event_7_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_5"/>
    </record>
    <record id="event_registration_7_1" model="event.registration">
        <field name="event_id" ref="event.event_7"/>
        <field name="event_ticket_id" ref="event.event_7_ticket_1"/>
        <field name="partner_id" ref="base.res_partner_address_10"/>
    </record>
    <record id="event_registration_7_2" model="event.registration">
        <field name="event_id" ref="event.event_7"/>
        <field name="event_ticket_id" ref="event.event_7_ticket_2"/>
        <field name="partner_id" ref="base.res_partner_address_11"/>
    </record>
    <record id="event_registration_7_3" model="event.registration">
        <field name="event_id" ref="event.event_7"/>
        <field name="event_ticket_id" ref="event.event_7_ticket_2"/>
        <field name="partner_id" ref="base.res_partner_address_25"/>
    </record>

    <function model="event.registration"
        name="action_confirm"
        context="{'install_mode' : True}"
        eval="[[ref('event_registration_0_0'), ref('event_registration_0_1'),
                ref('event_registration_1_0'), ref('event_registration_1_1'), ref('event_registration_1_2'),
                ref('event_registration_2_0'), ref('event_registration_2_1'), ref('event_registration_2_2'), ref('event_registration_2_3'),
                ref('event_registration_4_2')]]"
    />

    <function model="event.registration"
        name="action_set_done"
        eval="[[ref('event_registration_4_0'), ref('event_registration_4_1')]]"
    />

</data></odoo>