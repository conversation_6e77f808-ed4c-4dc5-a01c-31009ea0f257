# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-10-05 10:53+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""
"<span> לא נמצאו נתוני חברה על סמך כתובת הדוא\"ל או שכתובת דוא\"ל היא של אחד "
"מספקי הדוא\"ל. לא נצרכו נקודות זכות. </span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""
"<span>לא ניתן היה לבצע העשרת לידים מכיוון שכתובת האימייל לא נראית "
"חוקית.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "אפשר העשרה ידנית"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
#: model:ir.cron,cron_name:crm_iap_enrich.ir_cron_lead_enrichment
#: model:ir.cron,name:crm_iap_enrich.ir_cron_lead_enrichment
msgid "CRM: enrich leads (IAP)"
msgstr "ניהול קשרי לקוחות: העשר לידים (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "העשר"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "העשר לידים עם נתוני החברה"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "העשר הזדמנויות עם נתוני החברה"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "העשרה בוצעה"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "IAP account"
msgstr "חשבון IAP"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "העשרת לידים (על סמך כתובת דוא\"ל)"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Lead enriched (based on email address)"
msgstr "ליד מועשר (על סמך כתובת דוא\"ל)"

#. module: crm_iap_enrich
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
#, python-format
msgid "Lead enriched based on email address"
msgstr "ליד מועשר על סמך כתובת דוא\"ל"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "ליד/הזדמנות"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr "האם שירות IAP להעשרת לידים על סמך דוא\"ל בוצע על ליד זה."

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "Your balance for Lead Enrichment is insufficient. Please go to your"
msgstr "היתרה שלך להעשרת לידים אינו מספיק. אנא עבור אל "

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_credit
msgid "to buy credits."
msgstr "לקנות נקודות זכות."
