<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <record id="partner_demo_company_at" model="res.partner">
    <field name="name">AT Company</field>
    <field name="vat">ATU12345675</field>
    <field name="street">Sternwartestraße</field>
    <field name="city">Innsbruck</field>
    <field name="country_id" ref="base.at"/>

    <field name="zip">6020</field>
    <field name="phone">+43 512 321 54 76</field>
    <field name="email"><EMAIL></field>
    <field name="website">www.atexample.com</field>
  </record>

  <record id="demo_company_at" model="res.company">
    <field name="name">AT Company</field>
    <field name="partner_id" ref="partner_demo_company_at"/>
  </record>

  <function model="res.company" name="_onchange_country_id">
    <value eval="[ref('demo_company_at')]"/>
  </function>

  <function model="res.users" name="write">
    <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
    <value eval="{'company_ids':[(4, ref('l10n_at.demo_company_at'))]}"/>
  </function>

  <function model="account.chart.template" name="try_loading">
    <value eval="[ref('l10n_at.l10n_at_chart_template')]"/>
    <value model="res.company" eval="obj().env.ref('l10n_at.demo_company_at')"/>
    </function>
</odoo>
