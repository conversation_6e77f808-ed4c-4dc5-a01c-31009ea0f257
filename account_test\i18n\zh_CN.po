# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_test
# 
# Translators:
# <PERSON>ery CHEN Fan <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2016-08-05 12:55+0000\n"
"Last-Translator: inspur qiuguodong <<EMAIL>>, 2019\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid ""
"<br/>\n"
"                        <strong>Description:</strong>"
msgstr ""
"<br/>\n"
"                        <strong>描述:</strong>"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "<strong>Name:</strong>"
msgstr "<strong>名称:</strong>"

#. module: account_test
#: model:ir.model,name:account_test.model_report_account_test_report_accounttest
msgid "Account Test Report"
msgstr "会计测试‎‎报告"

#. module: account_test
#: model:ir.model,name:account_test.model_accounting_assert_test
msgid "Accounting Assert Test"
msgstr "会计确认测试"

#. module: account_test
#: model:ir.actions.act_window,name:account_test.action_accounting_assert
#: model:ir.actions.report,name:account_test.account_assert_test_report
#: model:ir.ui.menu,name:account_test.menu_action_license
msgid "Accounting Tests"
msgstr "会计测试"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.report_accounttest
msgid "Accouting tests on"
msgstr "会计测试"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__active
msgid "Active"
msgstr "有效"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_03
msgid "Check if movement lines are balanced and have the same date and period"
msgstr "检查凭证行是否平衡并且有相同的时间和日期"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_07
msgid ""
"Check on bank statement that the Closing Balance = Starting Balance + sum of"
" statement lines"
msgstr "在银行对账单检查，期末余额 = 期初余额 + 本期发生额"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_06
msgid "Check that paid/reconciled invoices are not in 'Open' state"
msgstr "检查支付/已调节发票并不在'开启'状态"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05_2
msgid ""
"Check that reconciled account moves, that define Payable and Receivable "
"accounts, are belonging to reconciled invoices"
msgstr "检查已调节的会计凭证，属于已调节发票的应付科目和应收科目"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_05
msgid ""
"Check that reconciled invoice for Sales/Purchases has reconciled entries for"
" Payable and Receivable Accounts"
msgstr "检查已收和已付科目中已调节的分录相关的销售/采购发票"

#. module: account_test
#: model:accounting.assert.test,desc:account_test.account_test_01
msgid "Check the balance: Debit sum = Credit sum"
msgstr "检查是否平衡：借方合计=贷方合计"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Code Help"
msgstr "代码帮助"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid ""
"Code should always set a variable named `result` with the result of your test, that can be a list or\n"
"a dictionary. If `result` is an empty list, it means that the test was succesful. Otherwise it will\n"
"try to translate and print what is inside `result`.\n"
"\n"
"If the result of your test is a dictionary, you can set a variable named `column_order` to choose in\n"
"what order you want to print `result`'s content.\n"
"\n"
"Should you need them, you can also use the following variables into your code:\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"In any ways, the code must be legal python statements with correct indentation (if needed).\n"
"\n"
"Example: \n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"
msgstr ""
"代码应该总是设​置一个名为\"result\"变量来保存你的测试结果，这可以是列表或字典。 如果\"result\"变量是一个空列表，这意味着测试是成功的。 否则，它会尝试翻译和打印\"result\"变量中的内容.\n"
"\n"
"如果输出的结果在列表或字典中你可以在`result`的内容中设置一个名为`column_order`的变量。\n"
"\n"
"若您需要它们，您同样可以使用以列变量至你的代码中：\n"
"    * cr: cursor to the database\n"
"    * uid: ID of the current user\n"
"\n"
"时刻留意的事是,所编写的代码均需要在python标准语法范围内 (若您需要的话)。\n"
"\n"
"例如:：\n"
"    sql = '''SELECT id, name, ref, date\n"
"             FROM account_move_line \n"
"             WHERE account_id IN (SELECT id FROM account_account WHERE type = 'view')\n"
"          '''\n"
"    cr.execute(sql)\n"
"    result = cr.dictfetchall()"

#. module: account_test
#: model_terms:ir.actions.act_window,help:account_test.action_accounting_assert
msgid "Create a new accounting test"
msgstr "新建一个测试账户"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_uid
msgid "Created by"
msgstr "创建人"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__create_date
msgid "Created on"
msgstr "创建时间"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Description"
msgstr "说明"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__display_name
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Expression"
msgstr "表达式"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__id
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest__id
msgid "ID"
msgstr "ID"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test____last_update
#: model:ir.model.fields,field_description:account_test.field_report_account_test_report_accounttest____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
msgid "Python Code"
msgstr "Python 代码"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__code_exec
msgid "Python code"
msgstr "Python代码"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__sequence
msgid "Sequence"
msgstr "序号"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_01
msgid "Test 1: General balance"
msgstr "测试 1: 总账平衡"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_03
msgid "Test 3: Movement lines"
msgstr "测试 3: 凭证行"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05
msgid ""
"Test 5.1 : Payable and Receivable accountant lines of reconciled invoices"
msgstr "测试 5.1 : 已调节发票的应收及应付分录"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_05_2
msgid "Test 5.2 : Reconcilied invoices and Payable/Receivable accounts"
msgstr "测试 5.2  : 已调节的发票和应收/应付科目"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_06
msgid "Test 6 : Invoices status"
msgstr "测试6  : 发票状态"

#. module: account_test
#: model:accounting.assert.test,name:account_test.account_test_07
msgid "Test 7 : Closing balance on bank statements"
msgstr "测试 7：在银行对账单上关张余额"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__desc
msgid "Test Description"
msgstr "测试描述"

#. module: account_test
#: model:ir.model.fields,field_description:account_test.field_accounting_assert_test__name
msgid "Test Name"
msgstr "测试项"

#. module: account_test
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_form
#: model_terms:ir.ui.view,arch_db:account_test.account_assert_tree
msgid "Tests"
msgstr "测试"

#. module: account_test
#: code:addons/account_test/report/report_account_test.py:53
#, python-format
msgid "The test was passed successfully"
msgstr "测试通过"
