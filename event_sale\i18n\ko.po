# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sale
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Sarah Park, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid ""
".\n"
"            <span>Manual actions may be needed.</span>"
msgstr ""
".\n"
"            <span>수동 작업이 필요할 수 있습니다.</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "<span class=\"o_stat_text\">Sales</span>"
msgstr "<span class=\"o_stat_text\">판매</span>"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "<span>Registration modification for attendee:</span>"
msgstr "<span>참석자 등록 수정:</span>"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,help:event_sale.field_event_type_ticket__description
msgid ""
"A description of the ticket that you want to communicate to your customers."
msgstr "고객에게 전달하려는 티켓에 대한 내용입니다."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"저장 가능 제품은 재고를 관리하는 품목입니다. 창고 앱이 설치되어 있어야 합니다.\n"
"소모품은 재고가 관리되지 않는 품목입니다.\n"
"서비스는 귀하가 제공하는 비물질 품목입니다."

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_order_lines_ids
msgid "All sale order lines pointing to this event"
msgstr "이 행사에 대한 전체 판매 주문 내역"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order__attendee_count
msgid "Attendee Count"
msgstr "참석자 수"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.sale_order_view_form
msgid "Attendees"
msgstr "참석자"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Before confirming"
msgstr "확정 전"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_campaign_id
msgid "Campaign"
msgstr "캠페인"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Cancel"
msgstr "취소"

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_id
msgid ""
"Choose an event and it will automatically create a registration for this "
"event."
msgstr "행사를 선택하면 이 행사에 대한 등록이 자동으로 생성됩니다."

#. module: event_sale
#: model:ir.model.fields,help:event_sale.field_sale_order_line__event_ticket_id
msgid ""
"Choose an event ticket and it will automatically create a registration for "
"this event ticket."
msgstr "행사 티켓을 선택하면 이 행사 티켓에 대한 등록이 자동으로 생성됩니다."

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.event_configurator_action
msgid "Configure an event"
msgstr "행사 구성"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Confirm"
msgstr "승인"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_uid
msgid "Created by"
msgstr "작성자"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__create_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__create_date
msgid "Created on"
msgstr "작성일자"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__currency_id
msgid "Currency"
msgstr "통화"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__description
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__description
msgid "Description"
msgstr "설명"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__display_name
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__display_name
msgid "Display Name"
msgstr "표시명"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor
msgid "Edit Attendee Details on Sales Confirmation"
msgstr "판매 확인에 대한 참석자 세부 정보 편집"

#. module: event_sale
#: model:ir.model,name:event_sale.model_registration_editor_line
msgid "Edit Attendee Line on Sales Confirmation"
msgstr "판매 확인에 대한 참석자 목록 편집"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__editor_id
msgid "Editor"
msgstr "편집가"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__email
msgid "Email"
msgstr "이메일"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_id
msgid "Event"
msgstr "행사"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_configurator
msgid "Event Configurator"
msgstr "행사 구성기"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ok
msgid "Event Ok"
msgstr ""

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_registration
#: model:product.product,name:event_sale.product_product_event
msgid "Event Registration"
msgstr "행사 등록"

#. module: event_sale
#: model:ir.actions.act_window,name:event_sale.action_sale_order_event_registration
msgid "Event Registrations"
msgstr "행사 등록"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_type_ticket
msgid "Event Template Ticket"
msgstr "행사 티켓 서식"

#. module: event_sale
#: model:ir.model,name:event_sale.model_event_event_ticket
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__event_ticket_id
#: model:ir.model.fields,field_description:event_sale.field_sale_order_line__event_ticket_id
#: model:ir.model.fields.selection,name:event_sale.selection__product_template__detailed_type__event
msgid "Event Ticket"
msgstr "행사 티켓"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "행사 티켓"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Exception:"
msgstr "예외:"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__free
msgid "Free"
msgstr "무료"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__id
msgid "ID"
msgstr "ID"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__is_paid
msgid "Is Paid"
msgstr "결제 완료"

#. module: event_sale
#: model:ir.model,name:event_sale.model_account_move
msgid "Journal Entry"
msgstr "분개"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor____last_update
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_uid
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__write_date
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_medium_id
msgid "Medium"
msgstr "중간"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__mobile
msgid "Mobile"
msgstr "모바일"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__name
msgid "Name"
msgstr "이름"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__to_pay
msgid "Not Paid"
msgstr "지불 안됨"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_configurator_view_form
msgid "Ok"
msgstr "확인"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Date"
msgstr "주문일"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Order Ref"
msgstr "주문 참조"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__registration_id
msgid "Original Registration"
msgstr "원본 등록"

#. module: event_sale
#: model:ir.model.fields.selection,name:event_sale.selection__event_registration__payment_status__paid
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Paid"
msgstr "지불됨"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__payment_status
msgid "Payment Status"
msgstr "결제 상태"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__phone
msgid "Phone"
msgstr "전화번호"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price
#: model_terms:ir.ui.view,arch_db:event_sale.event_report_template_full_page_ticket_inherit_sale
msgid "Price"
msgstr "가격"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__price_reduce
msgid "Price Reduce"
msgstr "가격 인하"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "세금을 포함한 가격 인하"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_product
#: model:ir.model.fields,field_description:event_sale.field_event_event_configurator__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_event_ticket__product_id
#: model:ir.model.fields,field_description:event_sale.field_event_type_ticket__product_id
msgid "Product"
msgstr "품목"

#. module: event_sale
#: model:ir.model,name:event_sale.model_product_template
msgid "Product Template"
msgstr "품목 양식"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "품목 유형"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "Registration"
msgstr "등록하기"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__event_registration_ids
msgid "Registrations to Edit"
msgstr "편집할 등록"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Sale Order"
msgstr "판매 주문"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Sales"
msgstr "판매"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_event__sale_price_subtotal
msgid "Sales (Tax Excluded)"
msgstr "판매 (세금 포함)"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales End"
msgstr "판매 종료"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor__sale_order_id
msgid "Sales Order"
msgstr "판매 주문"

#. module: event_sale
#: model:ir.model,name:event_sale.model_sale_order_line
#: model:ir.model.fields,field_description:event_sale.field_event_registration__sale_order_line_id
#: model:ir.model.fields,field_description:event_sale.field_registration_editor_line__sale_order_line_id
msgid "Sales Order Line"
msgstr "판매 주문 내역"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_event_ticket_view_tree_from_event
msgid "Sales Start"
msgstr "판매 시작"

#. module: event_sale
#: model:ir.model.fields,field_description:event_sale.field_event_registration__utm_source_id
msgid "Source"
msgstr "원본"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "Ticket changed from"
msgstr "티켓이 다음에서 변경됨"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_form_inherit_ticket
msgid "Total sales for this event"
msgstr "이 행사의 총 매출"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_registration_ticket_view_form
msgid "Transaction"
msgstr "거래"

#. module: event_sale
#: model:product.product,uom_name:event_sale.product_product_event
msgid "Units"
msgstr "단위"

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.view_event_registration_editor_form
msgid "please give details about the registrations"
msgstr "등록에 관한 세부 사항을 알려주세요."

#. module: event_sale
#: model_terms:ir.ui.view,arch_db:event_sale.event_ticket_id_change_exception
msgid "to"
msgstr "종료"
