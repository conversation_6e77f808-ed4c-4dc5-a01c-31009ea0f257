# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_email_template
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# Arnau Ros, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: Arnau Ros, 2021\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Automatic Email at Invoice"
msgstr "Correu electrònic automàtic en la factura"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
msgid "Body"
msgstr "Missatge"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.email_template_form_simplified
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Email Template"
msgstr "Plantilla de correu electrònic"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_account_move
msgid "Journal Entry"
msgstr "Assentament comptable"

#. module: product_email_template
#: model:ir.model.fields,field_description:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,field_description:product_email_template.field_product_template__email_template_id
msgid "Product Email Template"
msgstr "Plantilla de correu de productes"

#. module: product_email_template
#: model:ir.model,name:product_email_template.model_product_template
msgid "Product Template"
msgstr "Plantilla de producte"

#. module: product_email_template
#: model_terms:ir.ui.view,arch_db:product_email_template.product_template_form_view
msgid "Send a product-specific email once the invoice is validated"
msgstr ""
"Enviar un correu electrònic específic per producte un cop s'hagi validat la "
"factura"

#. module: product_email_template
#: model:ir.model.fields,help:product_email_template.field_product_product__email_template_id
#: model:ir.model.fields,help:product_email_template.field_product_template__email_template_id
msgid ""
"When validating an invoice, an email will be sent to the customer based on "
"this template. The customer will receive an email for each product linked to"
" an email template."
msgstr ""
"En validar una factura, s'enviarà un correu electrònic al client basat en "
"aquesta plantilla. El client rebrà un correu electrònic per cada producte "
"vinculat a una plantilla de correu electrònic."
