{"name": "bootstrap-toggle", "description": "Bootstrap Toggle is a highly flexible Bootstrap plugin that converts checkboxes into toggles", "version": "2.2.1", "keywords": ["bootstrap", "toggle", "bootstrap-toggle", "switch", "bootstrap-switch"], "homepage": "http://www.bootstraptoggle.com", "repository": {"type": "git", "url": "https://github.com/minhur/bootstrap-toggle.git"}, "license": "MIT", "authors": ["<PERSON> <<EMAIL>>"], "main": ["./js/bootstrap-toggle.min.js", "./css/bootstrap-toggle.min.css"], "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}