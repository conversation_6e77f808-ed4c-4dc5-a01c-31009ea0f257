<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- We want to activate product variant by default for easier demoing. -->
        <record id="base.group_user" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('product.group_product_variant'))]"/>
        </record>

        <record id="product_category_2" model="product.category">
            <field name="parent_id" ref="product.product_category_all"/>
            <field name="name">Internal</field>
        </record>
        <record id="product_category_3" model="product.category">
            <field name="parent_id" ref="product.product_category_1"/>
            <field name="name">Services</field>
        </record>
        <record id="product_category_6" model="product.category">
            <field name="parent_id" ref="product.product_category_3"/>
            <field name="name">Saleable</field>
        </record>
        <record id="product_category_4" model="product.category">
            <field name="parent_id" ref="product.product_category_1"/>
            <field name="name">Software</field>
        </record>
        <record id="product_category_5" model="product.category">
            <field name="parent_id" ref="product_category_1"/>
            <field name="name">Office Furniture</field>
        </record>
        <record id="product_category_consumable" model="product.category">
            <field name="parent_id" ref="product_category_all"/>
            <field name="name">Consumable</field>
        </record>

        <!-- Expensable products -->
        <record id="expense_product" model="product.product">
            <field name="name">Restaurant Expenses</field>
            <field name="list_price">14.0</field>
            <field name="standard_price">8.0</field>
            <field name="detailed_type">service</field>
            <field name="categ_id" ref="product.cat_expense"/>
        </record>

        <record id="expense_hotel" model="product.product">
            <field name="name">Hotel Accommodation</field>
            <field name="list_price">400.0</field>
            <field name="standard_price">400.0</field>
            <field name="detailed_type">service</field>
            <field name="uom_id" ref="uom.product_uom_day"/>
            <field name="uom_po_id" ref="uom.product_uom_day"/>
            <field name="categ_id" ref="cat_expense"/>
        </record>

        <!-- Service products -->
        <record id="product_product_1" model="product.product">
            <field name="name">Virtual Interior Design</field>
            <field name="categ_id" ref="product_category_3"/>
            <field name="standard_price">20.5</field>
            <field name="list_price">30.75</field>
            <field name="detailed_type">service</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
            <field name="uom_po_id" ref="uom.product_uom_hour"/>
        </record>

        <record id="product_product_2" model="product.product">
            <field name="name">Virtual Home Staging</field>
            <field name="categ_id" ref="product_category_3"/>
            <field name="standard_price">25.5</field>
            <field name="list_price">38.25</field>
            <field name="detailed_type">service</field>
            <field name="uom_id" ref="uom.product_uom_hour"/>
            <field name="uom_po_id" ref="uom.product_uom_hour"/>
        </record>

        <!-- Physical Products -->

        <record id="product_delivery_01" model="product.product">
            <field name="name">Office Chair</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">55.0</field>
            <field name="list_price">70.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_7777</field>
            <field name="image_1920" type="base64" file="product/static/img/product_chair.png"/>
        </record>

        <record id="product_delivery_02" model="product.product">
            <field name="name">Office Lamp</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">35.0</field>
            <field name="list_price">40.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_8888</field>
            <field name="image_1920" type="base64" file="product/static/img/product_lamp.png"/>
        </record>

        <record id="product_order_01" model="product.product">
            <field name="name">Office Design Software</field>
            <field name="categ_id" ref="product_category_4"/>
            <field name="standard_price">235.0</field>
            <field name="list_price">280.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_9999</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_43-image.jpg"/>
        </record>

        <record id="product_product_3" model="product.product">
            <field name="name">Desk Combination</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="list_price">450.0</field>
            <field name="standard_price">300.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Desk combination, black-brown: chair + desk + drawer.</field>
            <field name="default_code">FURN_7800</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_3-image.jpg"/>
        </record>

        <!-- Variants -->

        <record id="product_attribute_1" model="product.attribute">
            <field name="name">Legs</field>
            <field name="sequence">10</field>
        </record>
        <record id="product_attribute_value_1" model="product.attribute.value">
            <field name="name">Steel</field>
            <field name="attribute_id" ref="product_attribute_1"/>
            <field name="sequence">1</field>
        </record>
        <record id="product_attribute_value_2" model="product.attribute.value">
            <field name="name">Aluminium</field>
            <field name="attribute_id" ref="product_attribute_1"/>
            <field name="sequence">2</field>
        </record>

        <record id="product_attribute_2" model="product.attribute">
            <field name="name">Color</field>
            <field name="sequence">20</field>
        </record>
        <record id="product_attribute_value_3" model="product.attribute.value">
            <field name="name">White</field>
            <field name="attribute_id" ref="product_attribute_2"/>
            <field name="sequence">1</field>
        </record>
        <record id="product_attribute_value_4" model="product.attribute.value">
            <field name="name">Black</field>
            <field name="attribute_id" ref="product_attribute_2"/>
            <field name="sequence">2</field>
        </record>

        <record id="product_attribute_3" model="product.attribute">
            <field name="name">Duration</field>
            <field name="sequence">30</field>
        </record>
        <record id="product_attribute_value_5" model="product.attribute.value">
            <field name="name">1 year</field>
            <field name="attribute_id" ref="product_attribute_3"/>
        </record>
        <record id="product_attribute_value_6" model="product.attribute.value">
            <field name="name">2 year</field>
            <field name="attribute_id" ref="product_attribute_3"/>
        </record>

        <record id="product_product_4_product_template" model="product.template">
            <field name="name">Customizable Desk</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">500.0</field>
            <field name="list_price">750.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">160x80cm, with large legs.</field>
        </record>

        <!-- the product template attribute lines have to be defined before creating the variants -->
        <record id="product_4_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_4_product_template"/>
            <field name="attribute_id" ref="product_attribute_1"/>
            <field name="value_ids" eval="[(6, 0, [ref('product.product_attribute_value_1'), ref('product.product_attribute_value_2')])]"/>
        </record>
        <record id="product_4_attribute_2_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_4_product_template"/>
            <field name="attribute_id" ref="product_attribute_2"/>
            <field name="value_ids" eval="[(6, 0, [ref('product.product_attribute_value_3'), ref('product.product_attribute_value_4')])]"/>
        </record>

        <!--
        Handle automatically created product.template.attribute.value.
        Meaning that the combination between the "customizable desk" and the attribute value "black" will be materialized
        into a "product.template.attribute.value" with the ref "product.product_4_attribute_1_value_1".
        This will allow setting fields like "price_extra" and "exclude_for"
         -->
        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_4_attribute_1_value_1',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_1_value_2',
                'record': obj().env.ref('product.product_4_attribute_1_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_2_value_1',
                'record': obj().env.ref('product.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_4_attribute_2_value_2',
                'record': obj().env.ref('product.product_4_attribute_2_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            },]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_product_4',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_1') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4b',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_1') + obj().env.ref('product.product_4_attribute_2_value_2')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_4c',
                'record': obj().env.ref('product.product_product_4_product_template')._get_variant_for_combination(obj().env.ref('product.product_4_attribute_1_value_2') + obj().env.ref('product.product_4_attribute_2_value_1')),
                'noupdate': True,
            },]"/>
        </function>

        <record id="product_product_4" model="product.product">
            <field name="default_code">FURN_0096</field>
            <field name="standard_price">500.0</field>
            <field name="weight">0.01</field>
            <field name="image_1920" type="base64" file="product/static/img/table02.png"/>
        </record>
        <record id="product_product_4b" model="product.product">
            <field name="default_code">FURN_0097</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table04.png"/>
        </record>
        <record id="product_product_4c" model="product.product">
            <field name="default_code">FURN_0098</field>
            <field name="weight">0.01</field>
            <field name="standard_price">500.0</field>
            <field name="image_1920" type="base64" file="product/static/img/table03.png"/>
        </record>
        <record id="product_product_5" model="product.product">
            <field name="name">Corner Desk Right Sit</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">600.0</field>
            <field name="list_price">147.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM06</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_5-image.png"/>
        </record>

        <record id="product_product_6" model="product.product">
            <field name="name">Large Cabinet</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">800.0</field>
            <field name="list_price">320.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM07</field>
            <field name='weight'>0.330</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_6-image.png"/>
        </record>

        <record id="product_product_7" model="product.product">
            <field name="name">Storage Box</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">14.0</field>
            <field name="list_price">15.8</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM08</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_7-image.png"/>
        </record>

        <record id="product_product_8" model="product.product">
            <field name="name">Large Desk</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">1299.0</field>
            <field name="list_price">1799.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM09</field>
            <field name='weight'>9.54</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_8-image.png"/>
        </record>

        <record id="product_product_9" model="product.product">
            <field name="name">Pedal Bin</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">10.0</field>
            <field name="list_price">47.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM10</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_9-image.png"/>
        </record>

        <record id="product_product_10" model="product.product">
            <field name="name">Cabinet with Doors</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">120.50</field>
            <field name="list_price">140</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">E-COM11</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_10-image.png"/>
        </record>

        <record id="product_product_11_product_template" model="product.template">
            <field name="name">Conference Chair</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">28</field>
            <field name="list_price">33</field>
            <field name="detailed_type">consu</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="image_1920" type="base64" file="product/static/img/product_product_11-image.png"/>
        </record>

        <!-- the product template attribute lines have to be defined before creating the variants -->
        <record id="product_11_attribute_1_product_template_attribute_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="product_product_11_product_template"/>
            <field name="attribute_id" ref="product_attribute_1"/>
            <field name="value_ids" eval="[(6,0,[ref('product.product_attribute_value_1'), ref('product.product_attribute_value_2')])]"/>
        </record>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_11_attribute_1_value_1',
                'record': obj().env.ref('product.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[0],
                'noupdate': True,
            }, {
                'xml_id': 'product.product_11_attribute_1_value_2',
                'record': obj().env.ref('product.product_11_attribute_1_product_template_attribute_line').product_template_value_ids[1],
                'noupdate': True,
            }]"/>
        </function>

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                'xml_id': 'product.product_product_11',
                'record': obj().env.ref('product.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('product.product_11_attribute_1_value_1')),
                'noupdate': True,
            }, {
                'xml_id': 'product.product_product_11b',
                'record': obj().env.ref('product.product_product_11_product_template')._get_variant_for_combination(obj().env.ref('product.product_11_attribute_1_value_2')),
                'noupdate': True,
            },]"/>
        </function>

        <record id="product_product_11" model="product.product">
            <field name="default_code">E-COM12</field>
            <field name="weight">0.01</field>
        </record>
        <record id="product_product_11b" model="product.product">
            <field name="default_code">E-COM13</field>
            <field name="weight">0.01</field>
        </record>

        <record id="product.product_4_attribute_1_value_2" model="product.template.attribute.value">
            <field name="price_extra">50.40</field>
        </record>

        <record id="product.product_11_attribute_1_value_2" model="product.template.attribute.value">
            <field name="price_extra">6.40</field>
        </record>

        <!-- MRP Demo Data-->

        <record id="product_product_12" model="product.product">
            <field name="name">Office Chair Black</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">180</field>
            <field name="list_price">120.50</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_0269</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_12-image.png"/>
        </record>

        <record id="product_product_13" model="product.product">
            <field name="name">Corner Desk Left Sit</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">78.0</field>
            <field name="list_price">85.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_1118</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_13-image.png"/>
        </record>

        <record id="product_product_16" model="product.product">
            <field name="name">Drawer Black</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">20.0</field>
            <field name="list_price">25.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_8900</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_16-image.png"/>
        </record>

        <record id="product_product_20" model="product.product">
            <field name="name">Flipover</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">1700.0</field>
            <field name="list_price">1950.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_9001</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_20-image.png"/>
        </record>
        <record id="product_product_22" model="product.product">
            <field name="name">Desk Stand with Screen</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">2010.0</field>
            <field name="list_price">2100.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_7888</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_22-image.png"/>
        </record>

        <record id="product_product_24" model="product.product">
            <field name="name">Individual Workplace</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">876.0</field>
            <field name="list_price">885.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_0789</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_24-image.png"/>
        </record>

        <record id="product_product_25" model="product.product">
            <field name="name">Acoustic Bloc Screens</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">287.0</field>
            <field name="list_price">295.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">FURN_6666</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_25-image.png"/>
        </record>

        <record id="product_product_27" model="product.product">
            <field name="name">Drawer</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">100.0</field>
            <field name="list_price">110.50</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description">Drawer with two routing possiblities.</field>
            <field name="default_code">FURN_8855</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_27-image.png"/>
        </record>

        <record id="consu_delivery_03" model="product.product">
            <field name="name">Four Person Desk</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">2500.0</field>
            <field name="list_price">2350.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Four person modern office workstation</field>
            <field name="default_code">FURN_8220</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_d03-image.png"/>
        </record>

        <record id="consu_delivery_02" model="product.product">
            <field name="name">Large Meeting Table</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">4500.0</field>
            <field name="list_price">4000.0</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Conference room table</field>
            <field name="default_code">FURN_6741</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_46-image.png"/>
        </record>

        <record id="consu_delivery_01" model="product.product">
            <field name="name">Three-Seat Sofa</field>
            <field name="categ_id" ref="product_category_5"/>
            <field name="standard_price">1000</field>
            <field name="list_price">1500</field>
            <field name="detailed_type">consu</field>
            <field name="weight">0.01</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="description_sale">Three Seater Sofa with Lounger in Steel Grey Colour</field>
            <field name="default_code">FURN_8999</field>
            <field name="image_1920" type="base64" file="product/static/img/product_product_d01-image.png"/>
        </record>

        <!--
    Resource: product.supplierinfo
    -->

        <record id="product_supplierinfo_1" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_6_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">750</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_2" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_6_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">790</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_2bis" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_6_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">3</field>
            <field name="min_qty">3</field>
            <field name="price">785</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_3" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_7_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">13.0</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_4" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_7_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">14.4</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_5" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_8_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">2</field>
            <field name="min_qty">5</field>
            <field name="price">1299</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_6" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_8_product_template"/>
            <field name="name" ref="base.res_partner_12"/>
            <field name="delay">4</field>
            <field name="min_qty">1</field>
            <field name="price">1399</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_7" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_10_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">2</field>
            <field name="min_qty">1</field>
            <field name="price">120.50</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_8" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_11_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">2</field>
            <field name="min_qty">1</field>
            <field name="price">28</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_9" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_13_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">5</field>
            <field name="min_qty">1</field>
            <field name="price">78</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_10" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_16_product_template"/>
            <field name="name" ref="base.res_partner_3"/>
            <field name="delay">1</field>
            <field name="min_qty">1</field>
            <field name="price">20</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_12" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_20_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">1700</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_13" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_20_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">4</field>
            <field name="min_qty">5</field>
            <field name="price">1720</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_14" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_22_product_template"/>
            <field name="name" ref="base.res_partner_2"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">2010</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_15" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_24_product_template"/>
            <field name="name" ref="base.res_partner_2"/>
            <field name="delay">3</field>
            <field name="min_qty">1</field>
            <field name="price">876</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_16" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_25_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">8</field>
            <field name="min_qty">1</field>
            <field name="price">287</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_17" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_delivery_02_product_template"/>
            <field name="name" ref="base.res_partner_2"/>
            <field name="delay">4</field>
            <field name="min_qty">1</field>
            <field name="price">390</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_18" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
            <field name="name" ref="base.res_partner_3"/>
            <field name="delay">2</field>
            <field name="min_qty">12</field>
            <field name="price">90</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_19" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">4</field>
            <field name="min_qty">1</field>
            <field name="price">66</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_20" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_delivery_02_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">5</field>
            <field name="min_qty">1</field>
            <field name="price">35</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_21" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_delivery_01_product_template"/>
            <field name="name" ref="base.res_partner_12"/>
            <field name="delay">7</field>
            <field name="min_qty">1</field>
            <field name="price">55</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_22" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_9_product_template"/>
            <field name="name" ref="base.res_partner_12"/>
            <field name="delay">4</field>
            <field name="min_qty">0</field>
            <field name="price">10</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_23" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_27_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">10</field>
            <field name="min_qty">0</field>
            <field name="price">95.50</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_24" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_12_product_template"/>
            <field name="name" ref="base.res_partner_1"/>
            <field name="delay">3</field>
            <field name="min_qty">0</field>
            <field name="price">120.50</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_25" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_12_product_template"/>
            <field name="name" ref="base.res_partner_4"/>
            <field name="delay">2</field>
            <field name="min_qty">0</field>
            <field name="price">130.50</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record id="product_supplierinfo_26" model="product.supplierinfo">
            <field name="product_tmpl_id" ref="product_product_5_product_template"/>
            <field name="name" ref="base.res_partner_10"/>
            <field name="delay">1</field>
            <field name="min_qty">0</field>
            <field name="price">145</field>
            <field name="currency_id" ref="base.USD"/>
        </record>

        <record forcecreate="True" id="property_product_pricelist_demo" model="ir.property">
            <field name="name">property_product_pricelist</field>
            <field name="fields_id" search="[('model','=','res.partner'),('name','=','property_product_pricelist')]"/>
            <field name="value" eval="'product.pricelist,'+str(ref('list0'))"/>
            <field name="res_id" eval="'res.partner,'+str(ref('base.partner_demo'))"/>
            <field name="company_id" ref="base.main_company"/>
        </record>

    </data>
</odoo>
