# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2021
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# Константи<PERSON>н <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# alenafairy, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: alenafairy, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ". Возможно понадобится ручная действие."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.sale_order_inherited_form_purchase
msgid "<span class=\"o_stat_text\">Purchase</span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.purchase_order_inherited_form_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr ""

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s) occurred on the purchase order(s):"
msgstr "Исключения возникли в заказе на продажу:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Exception(s) occurred on the sale order(s):"
msgstr "Исключение произошло на заказе (ях) на продажу:"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "Exception(s):"
msgstr "Исключение(я):"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_ids
msgid "Generated Purchase Lines"
msgstr "Созданные строки покупки"

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,help:sale_purchase.field_product_template__service_to_purchase
msgid ""
"If ticked, each time you sell this product through a SO, a RfQ is "
"automatically created to buy the product. Tip: don't forget to set a vendor "
"on the product."
msgstr ""
"Если обозначено, каждый раз, когда вы продаете этот товар через СНП, "
"автоматически создается ЗНКП для покупки товара. Совет: не забудьте "
"установить поставщика на товар."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "Manual actions may be needed."
msgstr "Могут быть необходимы действия вручную."

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order__purchase_order_count
msgid "Number of Purchase Order Generated"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order__sale_order_count
msgid "Number of Source Sale"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_sale_order_line__purchase_line_count
msgid "Number of generated purchase items"
msgstr "Количество произведенных позиций"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Ordered quantity decreased!"
msgstr "Заказанное количество уменьшилось!"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_line_id
msgid "Origin Sale Item"
msgstr "происхождения товаров"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_product_template
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: sale_purchase
#: model:ir.model.constraint,message:sale_purchase.constraint_product_template_service_to_purchase
msgid "Product that is not a service can not create RFQ."
msgstr "Товар, который не является услугой, не может создать ЗНКП."

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Заказ на закупку"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Позиция заказа на закупку"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid "Purchase Order generated from %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,help:sale_purchase.field_sale_order_line__purchase_line_ids
msgid ""
"Purchase line generated by this Sales item on order confirmation, or when "
"the quantity was increased."
msgstr ""
"Строка покупки, созданные этим элементом продажи на подтверждении заказа или"
" когда количество увеличивается."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.product_template_form_view_inherit
msgid "Reordering"
msgstr "дозаказа"

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_purchase_order_line__sale_order_id
msgid "Sale Order"
msgstr "Заказ на продажу"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_purchase
#: model:ir.model,name:sale_purchase.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_purchase
#: code:addons/sale_purchase/models/purchase_order.py:0
#, python-format
msgid "Sources Sale Orders %s"
msgstr ""

#. module: sale_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_product__service_to_purchase
#: model:ir.model.fields,field_description:sale_purchase.field_product_template__service_to_purchase
msgid "Subcontract Service"
msgstr "Услуги по субподряду"

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."
msgstr ""
"There is no vendor associated to the product %s. Please define a vendor for "
"this product."

#. module: sale_purchase
#: code:addons/sale_purchase/models/sale_order.py:0
#, python-format
msgid ""
"You are decreasing the ordered quantity! Do not forget to manually update "
"the purchase order if needed."
msgstr ""
"Вы уменьшаете количество заказа! Не забудьте вручную обновить заказ на "
"покупку, если это необходимо."

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "cancelled"
msgstr "отменено"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_cancellation
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_sale_on_purchase_cancellation
msgid "of"
msgstr "из"

#. module: sale_purchase
#: model_terms:ir.ui.view,arch_db:sale_purchase.exception_purchase_on_sale_quantity_decreased
msgid "ordered instead of"
msgstr "заказано вместо"
