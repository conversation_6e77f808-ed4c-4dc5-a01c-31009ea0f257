# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_exhibitor
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-16 13:33+0000\n"
"PO-Revision-Date: 2020-11-16 13:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
#, python-format
msgid ""
")\n"
"                    to meet them !"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_topbar_country
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Country"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_topbar_sponsorship
msgid ""
"<i class=\"fa fa-folder-open\"/>\n"
"                By Sponsorship"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_aside
msgid "<span class=\"h5 mb-0 pt-0 pt-md-3 pb-0 pb-md-2\">Other exhibitors</span>"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
msgid "About"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_main
msgid "Add some exhibitors to get started !"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_topbar_sponsorship
msgid "All Sponsorships"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,help:website_event_track_exhibitor.field_event_sponsor__subtitle
msgid "Catchy marketing sentence for promote"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "Come back between"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_card
msgid "Connect"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_track_exhibitor.event_sponsor_action
msgid "Create a Sponsor / Exhibitor"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_event__display_name
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_type__display_name
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_website_event_menu__display_name
msgid "Display Name"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,help:website_event_track_exhibitor.field_event_type__exhibitor_menu
msgid "Display exhibitors on website"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model:ir.model,name:website_event_track_exhibitor.model_event_event
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
#, python-format
msgid "Event"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model,name:website_event_track_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.actions.act_window,name:website_event_track_exhibitor.event_sponsor_action
msgid "Event Sponsors"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model,name:website_event_track_exhibitor.model_event_type
msgid "Event Template"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_search
msgid "Exhibitor"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__is_exhibitor
msgid "Exhibitor's Chat"
msgstr ""

#. module: website_event_track_exhibitor
#: code:addons/website_event_track_exhibitor/models/event_event.py:0
#, python-format
msgid "Exhibitors"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_track_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_event__id
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_type__id
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_website_event_menu__id
msgid "ID"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
#, python-format
msgid "Join us next time to meet"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_event____last_update
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor____last_update
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_type____last_update
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_card
msgid "Live"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,help:website_event_track_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_card
msgid "Register"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_track_exhibitor.event_sponsor_action
msgid ""
"Sponsors might be advertised on your event pages footer.<br>\n"
"    Exhibitors might have a dedicated page with chat room for people to connect with them."
msgstr ""

#. module: website_event_track_exhibitor
#: code:addons/website_event_track_exhibitor/controllers/website_event_main.py:0
#, python-format
msgid ""
"The event %s starts on %s (%s). \n"
"Join us there to meet %s !"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,help:website_event_track_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_main
msgid "We did not find any exhibitor matching your"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model,name:website_event_track_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr ""

#. module: website_event_track_exhibitor
#: model:ir.model.fields,field_description:website_event_track_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.event_sponsor_view_form
msgid "e.g. : Your best choice for your home"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is not available right now."
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "is over."
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#, python-format
msgid "minutes"
msgstr ""

#. module: website_event_track_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitors_main
msgid "search."
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
#, python-format
msgid "starts in"
msgstr ""

#. module: website_event_track_exhibitor
#. openerp-web
#: code:addons/website_event_track_exhibitor/static/src/xml/event_exhibitor_connect.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_track_exhibitor.exhibitor_main
#, python-format
msgid "starts on"
msgstr ""
