# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* survey
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Констан<PERSON>и<PERSON>н <<EMAIL>>, 2021
# Collex100, 2021
# <PERSON>, 2021
# Андрей <PERSON>ев <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <koro<PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# Сергей Ше<PERSON>н<PERSON>н <<EMAIL>>, 2021
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:18+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Alena Vlasova, 2023\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_4
msgid "$100"
msgstr "$100"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_1
msgid "$20"
msgstr "$20"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_5
msgid "$200"
msgstr "$200"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_6
msgid "$300"
msgstr "$300"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_2
msgid "$50"
msgstr "$50"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_1_choice_3
msgid "$80"
msgstr "$80"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "% completed"
msgstr "% выполнено"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s certification passed"
msgstr "%s аттестации пройдено"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "%s challenge certification"
msgstr "%s конкурсной сертификации"

#. module: survey
#: model:ir.actions.report,print_report_name:survey.certification_report
msgid "'Certification - %s' % (object.survey_id.display_name)"
msgstr "'Сертификация - %s' % (object.survey_id.display_name)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug2
msgid "10 kg"
msgstr "10 кг"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug2
msgid "100 years"
msgstr "100 лет"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug3
msgid "1055"
msgstr "1055"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug3
msgid "116 years"
msgstr "116 лет"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug1
msgid "1227"
msgstr "1227"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug4
msgid "127 years"
msgstr "127  лет"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q1_sug2
msgid "1324"
msgstr "1324"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug1
msgid "1450 km"
msgstr "1450 км"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug3
msgid "16.2 kg"
msgstr "16.2 кг"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug2
msgid "3700 km"
msgstr "3700 км"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "403: Forbidden"
msgstr "403: Запрещено"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug4
msgid "47 kg"
msgstr "47 кг"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "4812"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q2_sug1
msgid "5.7 kg"
msgstr "5.7 кг"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q1_sug3
msgid "6650 km"
msgstr "6650 км"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q3_sug1
msgid "99 years"
msgstr "99 лет"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"<b>Certificate</b>\n"
"                            <br/>"
msgstr ""
"<b>Сертификат</b>\n"
"                            <br/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid ""
"<br/>\n"
"                        <span>Score:</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<br/>by"
msgstr "<br/>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q6
msgid ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "
msgstr ""
"<div class=\"text-center\">\n"
"                <div class=\"media_iframe_video\" data-oe-expression=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" style=\"width: 50%;\">\n"
"                    <div class=\"css_editable_mode_display\"/>\n"
"                    <div class=\"media_iframe_video_size\" contenteditable=\"false\"/>\n"
"                    <iframe src=\"//www.youtube.com/embed/7y4T6yv5L1k?autoplay=0&amp;rel=0\" frameborder=\"0\" contenteditable=\"false\"/>\n"
"                </div><br/>\n"
"            </div>\n"
"        "

#. module: survey
#: model:mail.template,body_html:survey.mail_template_certification
msgid ""
"<div style=\"background:#F0F0F0;color:#515166;padding:10px 0px;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"    <table style=\"width:600px;margin:5px auto;\">\n"
"        <tbody>\n"
"            <tr><td>\n"
"                <!-- We use the logo of the company that created the survey (to handle multi company cases) -->\n"
"                <a href=\"/\"><img t-attf-src=\"/logo.png?company={{ object.survey_id.create_uid.company_id.id }}\" style=\"vertical-align:baseline;max-width:100px;\"/></a>\n"
"            </td><td style=\"text-align:right;vertical-align:middle;\">\n"
"                    Certification: <t t-out=\"object.survey_id.display_name or ''\">Feedback Form</t>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"    <table style=\"width:600px;margin:0px auto;background:white;border:1px solid #e1e1e1;\">\n"
"        <tbody>\n"
"            <tr><td style=\"padding:15px 20px 10px 20px;\">\n"
"                <p>Dear <span t-out=\"object.partner_id.name or 'participant'\">participant</span></p>\n"
"                <p>\n"
"                    Here is, in attachment, your certification document for\n"
"                        <strong t-out=\"object.survey_id.display_name or ''\">Feedback Form</strong>\n"
"                </p>\n"
"                <p>Congratulations for succeeding the test!</p>\n"
"            </td></tr>\n"
"        </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model:mail.template,body_html:survey.mail_template_user_input_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant'\">participant</t><br/><br/>\n"
"        <t t-if=\"object.survey_id.certification\">\n"
"            You have been invited to take a new certification.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            We are conducting a survey and your response would be appreciated.\n"
"        </t>\n"
"        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.get_start_url())\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                <t t-if=\"object.survey_id.certification\">\n"
"                    Start Certification\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    Start Survey\n"
"                </t>\n"
"            </a>\n"
"        </div>\n"
"        <t t-if=\"object.deadline\">\n"
"            Please answer the survey for <t t-out=\"format_date(object.deadline) or ''\">05/05/2021</t>.<br/><br/>\n"
"        </t>\n"
"        Thank you for your participation.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"
msgstr "<i class=\"fa fa-2x\" role=\"img\" aria-label=\"Numeric\" title=\"Numeric\">123..</i>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"
msgstr ""
"<i class=\"fa fa-align-justify fa-4x\" role=\"img\" aria-label=\"Multiple "
"lines\" title=\"Multiple Lines\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-bar-chart\"/> Graph"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-bar-chart\"/> Results"
msgstr "<i class=\"fa fa-bar-chart\"/> Результаты"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-check-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-check-square-o fa-lg\"/> ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-circle-o  fa-lg\"/> answer"
msgstr "<i class=\"fa fa-circle-o  fa-lg\"/> ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"
msgstr ""
"<i class=\"fa fa-circle-o fa-lg\" role=\"img\" aria-label=\"Not checked\" "
"title=\"Not checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "<i class=\"fa fa-close\"/> Close"
msgstr "<i class=\"fa fa-close\"/> Закрыть"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"
msgstr ""
"<i class=\"fa fa-dot-circle-o fa-lg\" role=\"img\" aria-label=\"Checked\" "
"title=\"Checked\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-dot-circle-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-dot-circle-o fa-lg\"/> ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download certification\" title=\"Download certification\"/>\n"
"                                        Загрузить сертификацию"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-alt\"/> All Data"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "<i class=\"fa fa-list-alt\"/> Data"
msgstr "<i class=\"fa fa-list-alt\"/> Данные"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<i class=\"fa fa-list-ol\"/> Most Common"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"
msgstr ""
"<i class=\"fa fa-minus fa-4x\" role=\"img\" aria-label=\"Single Line\" "
"title=\"Single Line\"/>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "<i class=\"fa fa-square-o fa-lg\"/> answer"
msgstr "<i class=\"fa fa-square-o fa-lg\"/> ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<i class=\"fa fa-times\"/> Clear All Filters"
msgstr "<i class=\"fa fa-times\"/> Очистить все фильтры"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q3
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/coniferous.jpg\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4_q4
msgid ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                <img class=\"img-fluid o_we_custom_image d-block mx-auto\" src=\"/survey/static/img/pinus_sylvestris.jpg\" style=\"width: 100%;\"/><br/>\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p4
msgid ""
"<p>\n"
"                We like to say that the apple doesn't fall far from the tree, so here are trees.\n"
"            </p>\n"
"        "
msgstr ""
"<p>\n"
"                Мы любим говорить, что яблоко от яблони недалеко падает, так что вот вам яблони.\n"
"            </p>\n"
"        "

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3
msgid "<p>An apple a day keeps the doctor away.</p>"
msgstr "<p>Кто яблоко в день съедает, у того и доктор не бывает.</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_demo_burger_quiz
msgid ""
"<p>Choose your favourite subject and show how good you are. Ready ?</p>"
msgstr "Выберите ваш любимый вопрос и покажите себя в деле. Готовы?"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1_q4
msgid "<p>Just to categorize your answers, don't worry.</p>"
msgstr ""
"<p>Просто чтобы распределить ваши ответы по категориям, не волнуйтесь.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p1
msgid ""
"<p>Some general information about you. It will be used internally for "
"statistics only.</p>"
msgstr ""
"<p>Некоторая общая информация о вас. Она будет использоваться только для "
"внутренней статистики.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2
msgid "<p>Some questions about our company. Do you really know us?</p>"
msgstr "<p>Несколько вопросов о нашей компании. Вы с нами хорошо знакомы?</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_3
msgid "<p>Test your knowledge of our policies.</p>"
msgstr "<p>Проверьте свои знания нашей политики.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_2
msgid "<p>Test your knowledge of our prices.</p>"
msgstr "<p>Проверьте свое знание наших цен.</p>"

#. module: survey
#: model:survey.question,description:survey.vendor_certification_page_1
msgid "<p>Test your knowledge of your products!</p>"
msgstr "<p>Проверьте свое знание продукции!</p>"

#. module: survey
#: model:survey.survey,description:survey.vendor_certification
msgid "<p>Test your vendor skills!</p>"
msgstr "<p>Проверьте ваши навыки продавца!</p>"

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p1
msgid ""
"<p>This section is about general information about you. Answering them helps"
" qualifying your answers.</p>"
msgstr ""
"<p>Этот раздел посвящен общей информации о вас. Ответы на них помогут "
"оценить ваши ответы.</p>"

#. module: survey
#: model:survey.question,description:survey.survey_feedback_p2
msgid "<p>This section is about our eCommerce experience itself.</p>"
msgstr ""
"<p>Этот раздел посвящен непосредственно опыту электронной коммерции.</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_demo_quiz
msgid ""
"<p>This small quiz will test your knowledge about our Company. Be prepared "
"!</p>"
msgstr ""
"<p>Этот небольшое тест проверит ваши знания о нашей компании. Будьте "
"готовы!</p>"

#. module: survey
#: model:survey.survey,description:survey.survey_feedback
msgid ""
"<p>This survey allows you to give a feedback about your experience with our products.\n"
"    Filling it helps us improving your experience.</p>"
msgstr ""
"<p>Этот опрос позволяет оставить отзыв о вашем опыте использования нашей продукции.\n"
"Ваш ответ поможет нам стать лучше."

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5
msgid "<p>We may be interested by your input.</p>"
msgstr "<p>Нам важен ваш вклад.</p>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"seconds</span>"
msgstr ""
"<span attrs=\"{'invisible': [('is_time_limited', '=', False)]}\"> "
"секунды</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"
msgstr ""
"<span class=\"badge badge-primary only_left_radius\"><i class=\"fa fa-"
"filter\" role=\"img\" aria-label=\"Filter\" title=\"Filter\"/></span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Average </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">средний</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Maximum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">максимум</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "<span class=\"badge badge-secondary only_left_radius\">Minimum </span>"
msgstr "<span class=\"badge badge-secondary only_left_radius\">минимум</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "<span class=\"fa fa-filter\"/>  Filters"
msgstr "<span class=\"fa fa-filter\"/>  Фильтры"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\"> or "
"press Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"<span class=\"font-weight-bold text-muted ml-2 d-none d-md-inline\">or press"
" Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&lt;', 2)]}\">Сертификации</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_company_count', '&gt;', 1)]}\">Сертификация</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.res_partner_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Certifications</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Certification</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&lt;', 2)]}\">Сертификации</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('certifications_count', '&gt;', 1)]}\">Сертификация</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid ""
"<span class=\"o_survey_enter font-weight-bold text-muted ml-2\">or press "
"Enter</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_selection_key
msgid ""
"<span class=\"o_survey_key text-center position-absolute bg-white rounded-"
"left py-0 pl-2\"><span class=\"text-primary text-center text-center w-100 "
"position-relative\">Key</span></span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"
msgstr ""
"<span class=\"o_survey_session_answer_count\">0</span>\n"
"                                     /"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid ""
"<span class=\"o_survey_session_error_invalid_code d-none\">Code is incorrect.</span>\n"
"                                    <span class=\"o_survey_session_error_closed d-none\">Session is finished.</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Answers</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "<span class=\"text-muted\">Success</span>"
msgstr "<span class=\"text-muted\">Успех</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q1
msgid ""
"<span>\"Red\" is not a category, I know what you are trying to do ;)</span>"
msgstr ""
"<span>\"Красный\" - это не категория, я знаю, что вы пытаетесь сделать "
";)</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q6
msgid "<span>Best time to do it, is the right time to do it.</span>"
msgstr "<span>Утро вечера мудренее.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "<span>Date</span>"
msgstr "<span>Дата</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p5_q1
msgid ""
"<span>If you don't like us, please try to be as objective as "
"possible.</span>"
msgstr ""
"<span>Если мы вам не нравимся, пожалуйста, постарайтесь быть как можно более"
" объективными.</span>"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "<span>Number of attemps left</span>:"
msgstr ""

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p2_q1
msgid "<span>Our famous Leader !</span>"
msgstr "<span>Наш знаменитый Лид!</span>"

#. module: survey
#: model:survey.question,description:survey.survey_demo_quiz_p3_q3
msgid "<span>Our sales people have an advantage, but you can do it !</span>"
msgstr ""
"Наши менеджеры по продажам имеют преимущество, но это можете сделать и вы!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "<span>Right answer:</span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "<span>Time limit for this survey: </span>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "<span>Waiting for attendees...</span>"
msgstr "<span>Ожидание участников...</span>"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q4
msgid "A \"Citrus\" could give you ..."
msgstr "\"Цитрус\" может дать вам ..."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "A label must be attached to only one question."
msgstr "Метка должна быть добавлена только к одному вопросу."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_max
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_len_min
msgid "A length must be positive!"
msgstr "Длина должна быть положительной!"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_4
msgid "A little bit overpriced"
msgstr "Немного завышена цена"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_5
msgid "A lot overpriced"
msgstr "Сильно завышена цена"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question_answer__answer_score
msgid ""
"A positive score indicates a correct choice; a negative or null score "
"indicates a wrong answer"
msgstr ""
"Положительная оценка указывает на корректный выбор; отрицательное или "
"нулевое значение показывает неправильный ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "A problem has occurred"
msgstr "Возникла проблема"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "A question can either be skipped or answered, not both."
msgstr ""
"Вопрос может быть либо пропущен, либо решен, но не то и другое одновременно."

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2
msgid "About our ecommerce"
msgstr "О нашей электронной коммерции"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1
msgid "About you"
msgstr "О себе"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_access_mode
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_mode
msgid "Access Mode"
msgstr "Режим доступа"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__access_token
msgid "Access Token"
msgstr "Токен доступа"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_access_token_unique
msgid "Access token should be unique"
msgstr "Токен доступа должен быть уникальным"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__active
msgid "Active"
msgstr "Активно"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_ids
msgid "Activities"
msgstr "Действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформление исключения активности"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_state
msgid "Activity State"
msgstr "Статус действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_icon
msgid "Activity Type Icon"
msgstr "Иконка типа действия"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Add a list of email of recipients (will not be converted into contacts). "
"Separated by commas, semicolons or newline..."
msgstr ""
"Добавьте список электронных адресов получателей (не конвертированы в "
"контакты). Обособленно запятыми, запятыми-точками или строкой ..."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Add a new survey"
msgstr "Добавить новый опрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a question"
msgstr "Добавьте вопрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Add a section"
msgstr "Добавить раздел"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Add existing contacts..."
msgstr "Добавьте существующие контакты ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__emails
msgid "Additional emails"
msgstr "Дополнительные письма"

#. module: survey
#: model:res.groups,name:survey.group_survey_manager
msgid "Administrator"
msgstr "Администратор"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug1
msgid "Africa"
msgstr "Африка"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q6
msgid ""
"After watching this video, will you swear that you are not going to "
"procrastinate to trim your hedge this year ?"
msgstr ""
"После просмотра этого видео вы обещаете, что не будете откладывать стрижку "
"живой изгороди в этом году?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col3
msgid "Agree"
msgstr "Согласиться"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_date_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Date\" questions "
"need an answer"
msgstr "Все вопросы \"Вопрос с оценкой = Да\" и \"Тип вопроса: Дата\" требуют ответа"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_scored_datetime_have_answers
msgid ""
"All \"Is a scored question = True\" and \"Question Type: Datetime\" "
"questions need an answer"
msgstr ""
"Все вопросы \"Вопрос с оценкой = Да\" и \"Тип вопроса: Дата-время\" требуют "
"ответа"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__all
msgid "All questions"
msgstr "Все вопросы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "All surveys"
msgstr "Все опросы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Allow Comments"
msgstr "Разрешить комментарии"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug2
msgid "Amenhotep"
msgstr "Аменхотеп"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_user_input_unique_token
msgid "An access token must be unique!"
msgstr "Токен доступа должен быть уникальным!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_positive_answer_score
msgid "An answer score for a non-multiple choice question cannot be negative!"
msgstr ""
"Балл за ответ на вопрос без множественного выбора не может быть "
"отрицательным!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_leaderboard
msgid "Anonymous"
msgstr "Анонимно"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Answer"
msgstr "Ответ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_type
msgid "Answer Type"
msgstr "Тип ответа"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__deadline
msgid "Answer deadline"
msgstr "Крайний срок для ответа"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_answer_id
msgid "Answer that will trigger the display of the current question."
msgstr "Ответ, который вызовет показ текущего вопроса."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Answered"
msgstr "Отвечено"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__user_input_line_ids
#: model:ir.model.fields,field_description:survey.field_survey_user_input__user_input_line_ids
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Answers"
msgstr "Ответы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_answer_count
msgid "Answers Count"
msgstr "Подсчет ответов"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__public
msgid "Anyone with the link"
msgstr "Любой, у кого есть ссылка"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Появляется в"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug1
msgid "Apple Trees"
msgstr "Яблони"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row1
msgid "Apples"
msgstr "Яблоки"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Archived"
msgstr "Архивировано"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug4
msgid "Art & Culture"
msgstr "Искусство и культура"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug1
msgid "Arthur B. McDonald"
msgstr "Артур Б. Макдональд"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug2
msgid "Asia"
msgstr "Азия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__attachment_ids
msgid "Attachments"
msgstr "Вложения"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_number
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Attempt n°"
msgstr "Попытка номер"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_done_count
msgid "Attempts"
msgstr "Попытки"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Attempts Limit"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__nickname
msgid ""
"Attendee nickname, mainly used to identify him in the survey session "
"leaderboard."
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Attendees are answering the question..."
msgstr "Присутствующие отвечают на вопрос..."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_speed_rating
msgid "Attendees get more points if they answer quickly"
msgstr "Участники получают больше баллов, если отвечают быстро"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__author_id
msgid "Author"
msgstr "Автор"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__author_id
msgid "Author of the message."
msgstr "Автор сообщения."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__certification_mail_template_id
msgid ""
"Automated email sent to the user when he succeeds the certification, "
"containing his certification document."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug3
msgid "Autumn"
msgstr "Осень"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_duration_avg
msgid "Average Duration"
msgstr "Средняя продолжительность"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__answer_duration_avg
msgid "Average duration of the survey (in hours)"
msgstr "Средняя продолжительность опроса (в часах)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_score_avg
msgid "Avg Score %"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug3
msgid "Avicii"
msgstr "Авичи"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Back Button"
msgstr "Кнопка назад"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__background_image
msgid "Background Image"
msgstr "Фоновое изображение"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Badge"
msgstr "Значок"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug3
msgid "Baobab Trees"
msgstr "Деревья баобаба"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug1
msgid "Bees"
msgstr "Пчелы"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug4
msgid "Bricks"
msgstr "Кирпичи"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_burger_quiz
msgid "Burger Quiz"
msgstr "Тест на тему \"Бургер\""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_3
msgid "Cabinet with Doors"
msgstr "Шкаф с дверцами"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row1
msgid "Cactus"
msgstr "Кактус"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Можно отредактировать"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q3
msgid "Can Humans ever directly see a photon ?"
msgstr "Может ли человек когда-либо напрямую увидеть фотон?"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Cancel"
msgstr "Отмена"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Candidates"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
msgid "Certification"
msgstr "Сертификация"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id
msgid "Certification Badge"
msgstr "Значок сертификации"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_badge_id_dummy
msgid "Certification Badge "
msgstr "Значок сертификации"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Certification Badge is not configured for the survey %(survey_name)s"
msgstr "Значок сертификации не настроен для опроса %(survey_name)s"

#. module: survey
#: model:mail.template,report_name:survey.mail_template_certification
msgid "Certification Document"
msgstr "Документ о сертификации"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification Failed"
msgstr "Сертификация не пройдена"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Certification Template"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "Certification n°"
msgstr "Номер сертификата"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_report_layout
msgid "Certification template"
msgstr "Образец сертификата"

#. module: survey
#: model:mail.template,subject:survey.mail_template_certification
msgid "Certification: {{ object.survey_id.display_name }}"
msgstr "Сертификат: {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.report,name:survey.certification_report
#: model:ir.model.fields.selection,name:survey.selection__gamification_challenge__challenge_category__certification
msgid "Certifications"
msgstr "Сертификаты"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_count
msgid "Certifications Count"
msgstr "Подсчет сертификатов"

#. module: survey
#: model:ir.actions.act_window,name:survey.res_partner_action_certifications
msgid "Certifications Succeeded"
msgstr "Успешно пройденные сертификации"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Certified"
msgstr "Сертифицировано"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_1
msgid "Chair floor protection"
msgstr "Защита пола от стула"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Chart"
msgstr "Диаграмма"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,help:survey.field_survey_user_input__is_attempts_limited
msgid "Check this option if you want to limit the number of attempts per user"
msgstr ""
"Выберите этот параметр, если вы хотите ограничить количество попыток для "
"одного пользователя"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug2
msgid "China"
msgstr "Китай"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Choices"
msgstr "Возможные значения"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_blue
msgid "Classic Blue"
msgstr "Классический синий"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_gold
msgid "Classic Gold"
msgstr "Классический золотой"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__classic_purple
msgid "Classic Purple"
msgstr "Классический фиолетовый"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row3
msgid "Clementine"
msgstr "Клементин"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug4
msgid "Cliff Burton"
msgstr "Клифф Бертон"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close"
msgstr "Закрыть"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Close Live Session"
msgstr "Закрыть прямой эфир"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_1
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Color"
msgstr "Цвет"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__color
msgid "Color Index"
msgstr "Цветовая палитра"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_comments
msgid "Comment"
msgstr "Комментарий"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comment_count_as_answer
msgid "Comment Field is an Answer Choice"
msgstr "Поле комментариев - вариант ответа"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_message
msgid "Comment Message"
msgstr "Комментарий"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_res_partner__certifications_company_count
#: model:ir.model.fields,field_description:survey.field_res_users__certifications_company_count
msgid "Company Certifications Count"
msgstr "Подсчет сертификации компании"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__done
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Completed"
msgstr "Завершено"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Compose Email"
msgstr "Написать письмо"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Computing score requires a question in arguments."
msgstr "Вычисление баллов требует вопроса в спорных моментах."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_conditional
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Conditional Display"
msgstr "Условное отображение"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_3
msgid "Conference chair"
msgstr "Руководитель дискуссии"

#. module: survey
#: model_terms:gamification.badge,description:survey.vendor_certification_badge
msgid "Congratulations, you are now official vendor of MyCompany"
msgstr "Поздравляем, теперь вы являетесь официальным поставщиком MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Congratulations, you have passed the test!"
msgstr "Поздравляем, вы прошли тест!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Constraints"
msgstr "Ограничения"

#. module: survey
#: model:ir.model,name:survey.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_conditional_questions
msgid "Contains conditional questions"
msgstr "Содержит  условные вопросы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__body
msgid "Contents"
msgstr "Содержание"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Continue"
msgstr "Далее"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "Continue here"
msgstr "Продолжайте здесь"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug4
msgid "Cookies"
msgstr "Файлы cookie"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#, python-format
msgid "Copied !"
msgstr "Скопировано!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug3
msgid "Cornaceae"
msgstr "Кизиловые"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_1
msgid "Corner Desk Right Sit"
msgstr "Угловой стол с правым сиденьем"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_is_correct
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
#, python-format
msgid "Correct"
msgstr "Правильный"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Correct Answer"
msgstr "Правильный ответ"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_datetime
msgid "Correct date and time answer for this question."
msgstr "Правильный ответ по дате и времени для этого вопроса."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_date
msgid "Correct date answer"
msgstr "Правильный ответ по дате"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_date
msgid "Correct date answer for this question."
msgstr "Правильный ответ на этот вопрос."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_datetime
msgid "Correct datetime answer"
msgstr "Правильный ответ дата-время"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_numerical_box
msgid "Correct number answer for this question."
msgstr "Правильный ответ на этот вопрос."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_numerical_box
msgid "Correct numerical answer"
msgstr "Правильный числовой ответ"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_3
msgid "Correctly priced"
msgstr "Правильно установленная цена"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug3
msgid "Cosmic rays"
msgstr "Космическое излучение"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Create Live Session"
msgstr "Создать сеанс прямого эфира"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_uid
msgid "Created by"
msgstr "Создан"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question__create_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__create_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__create_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__create_date
msgid "Created on"
msgstr "Создан"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating test token is not allowed for you."
msgstr "Создание тестового токена запрещено."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for anybody else than employees is not allowed for internal "
"surveys."
msgstr ""
"Создание токена для кого-либо, кроме сотрудников, не допускается для "
"внутренних опросов."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Creating token for closed/archived surveys is not allowed."
msgstr "Создание токена для закрытых/архивированных опросов не допускается."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"Creating token for external people is not allowed for surveys requesting "
"authentication."
msgstr ""
"Создание токена для внешних людей не допускается для опросов, запрашивающих "
"аутентификацию."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_id
msgid "Current Question"
msgstr "Текущий вопрос"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_start_time
msgid "Current Question Start Time"
msgstr "Время начала текущего вопроса"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_start_time
msgid "Current Session Start Time"
msgstr "Время начала текущей сессии"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_time_limited
msgid "Currently only supported for live sessions."
msgstr "В настоящее время поддерживается только для прямых эфиров."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid ""
"Customers will receive a new token and be able to completely retake the "
"survey."
msgstr "Клиенты получат новый токен и смогут полностью пройти опрос заново."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Customers will receive the same token."
msgstr "Клиенты получат тот же токен."

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_5
msgid "Customizable Lamp"
msgstr "Настраиваемая лампа"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__date
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__date
msgid "Date"
msgstr "Дата"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_date
msgid "Date answer"
msgstr "Дата ответа"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__datetime
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__datetime
msgid "Datetime"
msgstr "Дата-время"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_datetime
msgid "Datetime answer"
msgstr "Ответ в формате дата-время"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__deadline
msgid "Datetime until customer can open the survey and submit answers"
msgstr "Дата, до которой клиент может открыть опрос и отправить ответы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__deadline
msgid "Deadline"
msgstr "Крайний Срок"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__null_value
msgid "Default Value"
msgstr "Значение по умолчанию "

#. module: survey
#: model:ir.model.fields,help:survey.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Определите видимость задачи через меню"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Delete"
msgstr "Удалить"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__description
#: model:ir.model.fields,field_description:survey.field_survey_survey__description
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Description"
msgstr "Описание"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid "Design easily your survey, send invitations and analyze answers."
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_2
msgid "Desk Combination"
msgstr "комбинация стола"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_user_input_line_action
#: model:ir.ui.menu,name:survey.menu_survey_response_line_form
msgid "Detailed Answers"
msgstr "Подробные ответы"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col2
msgid "Disagree"
msgstr "Не соглашаться"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Display"
msgstr "Показать"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question__display_name
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__display_name
#: model:ir.model.fields,field_description:survey.field_survey_survey__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input__display_name
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__allow_value_image
msgid ""
"Display images in addition to answer label. Valid only for simple / multiple"
" choice questions."
msgstr ""

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_1
msgid "Do we sell Acoustic Bloc Screens?"
msgstr "Продаем ли мы экраны Acoustic Bloc?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q3
msgid "Do you have any other comments, questions, or concerns ?"
msgstr "Есть ли у вас другие комментарии, вопросы или замечания?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_5
msgid "Do you think we have missing products in our catalog? (not rated)"
msgstr ""
"Как вы думаете, есть ли в нашем каталоге недостающие товары? (без рейтинга)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug2
msgid "Dogs"
msgstr "Собаки"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q1
msgid "Dogwood is from which family of trees ?"
msgstr "Кизил относится к какому семейству деревьев?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug1
msgid "Douglas Fir"
msgstr "Пихта Дугласа"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_4
msgid "Drawer"
msgstr "ящик"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Dropdown menu"
msgstr "Выпадающее меню"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Edit Survey"
msgstr "Редактировать опрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "Edit in backend"
msgstr "Редактировать в классическом интерфейсе"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__email
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Email"
msgstr "Email"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_mail_template_id
msgid "Email Template"
msgstr "Шаблон письма"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__email_from
msgid "Email address of the sender."
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__description_done
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "End Message"
msgstr "Завершить сообщение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__end_datetime
msgid "End date and time"
msgstr "Дата и время окончания"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Enter Session Code"
msgstr "Введите код сессии"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_error_msg
msgid "Error message"
msgstr "Сообщение об ошибке"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug3
msgid "Europe"
msgstr "Европа"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug3
msgid "European Yew"
msgstr "Тис европейский"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Except Test Entries"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_partner_ids
msgid "Existing Partner"
msgstr "Существующий партнер"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_emails
msgid "Existing emails"
msgstr "Существующие электронные письма"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug2
msgid "Eyjafjallajökull (Iceland)"
msgstr "Eyjafjallajökull (Исландия)"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_2_choice_2
msgid "Fanta"
msgstr "Фанта"

#. module: survey
#: model:survey.survey,title:survey.survey_feedback
msgid "Feedback Form"
msgstr "Форма обратной связи"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_row2
msgid "Ficus"
msgstr "Фикус"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__model_object_field
msgid "Field"
msgstr "Поле"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Filter question"
msgstr "фильтровать вопросы"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_session_manage.js:0
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#, python-format
msgid "Final Leaderboard"
msgstr "Итоговый рейтинг"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__copyvalue
msgid ""
"Final placeholder expression, to be copy-pasted in the desired template "
"field."
msgstr ""
"Окончательное выражение подстановки для копирования-вставки в желаемое поле "
"шаблона."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Finished surveys"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Иконка со шрифтом Font awesome, например. fa-tasks"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__text_box
msgid "Free Text"
msgstr "Произвольный текст"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_text_box
msgid "Free Text answer"
msgstr "Ответ Произвольного текста"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__email_from
msgid "From"
msgstr "От"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q4
msgid "From which continent is native the Scots pine (pinus sylvestris) ?"
msgstr "С какого континента родом шотландская сосна (pinus sylvestris)?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug1
msgid "Fruits"
msgstr "Фрукты"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3
msgid "Fruits and vegetables"
msgstr "Фрукты и овощи"

#. module: survey
#: model:ir.model,name:survey.model_gamification_badge
msgid "Gamification Badge"
msgstr "Гемификация значка"

#. module: survey
#: model:ir.model,name:survey.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Соревнование для геймификации"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug1
msgid "Geography"
msgstr "География"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification_give_badge
msgid "Give Badge"
msgstr "Выдать медаль"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q3
msgid "Give the list of all types of wood we sell."
msgstr "Приведите список всех видов древесины, которые мы продаем."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_open
msgid "Go to"
msgstr "Перейти к"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug1
msgid "Good"
msgstr "Хорошо"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug4
msgid "Good value for money"
msgstr "Хорошее соотношение цены и качества"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug2
msgid "Grapefruits"
msgstr "Грейпфрукты"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Graph"
msgstr "График"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Group By"
msgstr "Группировка"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_mode
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Handle existing"
msgstr "Управление имеющимися"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug1
msgid "Hard"
msgstr "Тяжелый"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_2
msgid "Height"
msgstr "Высота"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug3
msgid "Hemiunu"
msgstr "Хемиуну"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug1
msgid "High quality"
msgstr "Высокое качество"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug2
msgid "History"
msgstr "История"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q3
msgid "How frequently do you buy products online ?"
msgstr "Как часто вы покупаете товары через интернет?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q1
msgid "How long is the White Nile river?"
msgstr "Какова длина реки Белый Нил?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_6
msgid ""
"How many chairs do you think we should aim to sell in a year (not rated)?"
msgstr ""
"Как вы думаете, сколько стульев мы должны рассчитывать продать за год (без "
"учета рейтинга)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_1
msgid "How many days is our money-back guarantee?"
msgstr "Сколько дней действует наша гарантия возврата денег?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q4
msgid "How many times did you order products on our website ?"
msgstr "Сколько раз вы заказывали товары на нашем сайте?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_4
msgid "How many versions of the Corner Desk do we have?"
msgstr "Сколько вариантов углового стола у нас есть?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q3
msgid "How many years did the 100 years war last ?"
msgstr "Сколько лет длилась 100-летняя война?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_1
msgid "How much do we sell our Cable Management Box?"
msgstr "По какой цене мы продаем нашу коробку кабельного управления?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q5
msgid "How often should you water those plants"
msgstr "Как часто вы должны поливать эти растения"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q4
msgid "How old are you ?"
msgstr "Сколько вам лет?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug4
msgid ""
"I actually don't like thinking. I think people think I like to think a lot. "
"And I don't. I do not like to think at all."
msgstr ""
"На самом деле я не люблю думать. Мне кажется, люди думают, что я люблю много"
" думать. А это не так. Я вообще не люблю думать."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug2
msgid ""
"I am fascinated by air. If you remove the air from the sky, all the birds "
"would fall to the ground. And all the planes, too."
msgstr ""
"Меня восхищает воздух. Если убрать воздух из неба, все птицы упадут на "
"землю. И все самолеты тоже."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row5
msgid "I have added products to my wishlist"
msgstr "Я добавил товары в свой список желаний"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug4
msgid "I have no idea, I'm a dog!"
msgstr "Понятия не имею, я же собака!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug3
msgid "I've been noticing gravity since I was very young !"
msgstr "Я замечал гравитацию с раннего детства!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__id
#: model:ir.model.fields,field_description:survey.field_survey_question__id
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__id
#: model:ir.model.fields,field_description:survey.field_survey_survey__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__id
msgid "ID"
msgstr "Идентификатор"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_exception_icon
msgid "Icon"
msgstr "Иконка"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Значок, обозначающий действие исключения."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__access_token
msgid "Identification token"
msgstr "Значок идентификации"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__progression_mode
msgid ""
"If Number is selected, it will display the number of questions answered on "
"the total number of question to answer."
msgstr ""
"Если выбрано значение Число, отображается количество вопросов, на которые "
"даны ответы, от общего числа вопросов, на которые нужно ответить."

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_3
msgid ""
"If a customer purchases a 1 year warranty on 6 January 2020, when do we "
"expect the warranty to expire?"
msgstr ""
"Если клиент приобретает гарантию 6 января 2020 года сроком на 1 год, когда "
"истекает срок действия гарантии?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_2
msgid ""
"If a customer purchases a product on 6 January 2020, what is the latest day "
"we expect to ship it?"
msgstr ""
"Если клиент приобретает товар 6 января 2020 года, в какой последний день мы "
"планируем его отгрузку?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread
msgid "If checked, new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено - некоторые сообщения имеют ошибку доставки."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_email
msgid ""
"If checked, this option will save the user's answer as its email address."
msgstr ""
"Если флажок установлен, то ответ пользователя будет сохранен в качестве его "
"адреса электронной почты."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__save_as_nickname
msgid "If checked, this option will save the user's answer as its nickname."
msgstr ""
"Если флажок установлен, то ответ пользователя будет сохранен в качестве его "
"никнейма."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_conditional
msgid ""
"If checked, this question will be displayed only \n"
"        if the specified conditional answer have been selected in a previous question"
msgstr ""

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__users_can_go_back
msgid "If checked, users can go back to previous pages."
msgstr "Если отмечено, пользователи могут вернуться на предыдущие страницы."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,help:survey.field_survey_survey__users_login_required
msgid ""
"If checked, users have to login before answering even with a valid token."
msgstr ""
"Если флажок установлен, пользователи (даже имеющие действующий токен) должны"
" авторизоваться прежде, чем ответить. "

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,comments_message:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p1_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p2_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p3_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q1
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q2
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q3
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q4
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p4_q6
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5
#: model:survey.question,comments_message:survey.survey_demo_quiz_p5_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q1
#: model:survey.question,comments_message:survey.survey_feedback_p1_q2
#: model:survey.question,comments_message:survey.survey_feedback_p1_q3
#: model:survey.question,comments_message:survey.survey_feedback_p1_q4
#: model:survey.question,comments_message:survey.survey_feedback_p2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q1
#: model:survey.question,comments_message:survey.survey_feedback_p2_q2
#: model:survey.question,comments_message:survey.survey_feedback_p2_q3
#: model:survey.question,comments_message:survey.vendor_certification_page_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_1_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_2_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_1
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_2
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_3
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_4
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_5
#: model:survey.question,comments_message:survey.vendor_certification_page_3_question_6
#, python-format
msgid "If other, please specify:"
msgstr "Если другое, укажите, пожалуйста:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__questions_selection
msgid ""
"If randomized is selected, add the number of random questions next to the "
"section."
msgstr ""
"Если выбран случайный порядок, добавьте количество случайных вопросов "
"напротив этого раздела."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__questions_selection
msgid ""
"If randomized is selected, you can configure the number of random questions "
"by section. This mode is ignored in live session."
msgstr ""
"Если выбран случайный порядок, то вы можете установить количество случайных "
"вопросов в каждом разделе. Эта настройка не используется в режиме живой "
"сессии. "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "If you wish, you can"
msgstr "Если вы хотите, вы можете"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value_image
msgid "Image"
msgstr "Изображение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__allow_value_image
msgid "Images on answers"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug1
msgid "Imhotep"
msgstr "Имхотеп"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug6
msgid "Impractical"
msgstr "Непрактичный"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__in_progress
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__in_progress
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "In Progress"
msgstr "В работе"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q5
msgid "In the list below, select all the coniferous."
msgstr "В приведенном ниже списке выберите все хвойные деревья."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q2
msgid "In which country did the bonsai technique develop ?"
msgstr "В какой стране возникла техника бонсай?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__is_scored_question
msgid ""
"Include this question as part of quiz scoring. Requires an answer and answer"
" score to be taken into account."
msgstr ""
"Включите этот вопрос для подсчета общего количества баллов. Необходимо "
"указать ответ на вопрос и количество баллов за ответ."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Incorrect"
msgstr "Неверно"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug7
msgid "Ineffective"
msgstr "Неэффективно"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_email
msgid "Input must be an email"
msgstr "Исходными данными должен быть адрес электронной почты"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__invite_token
msgid "Invite token"
msgstr "Пригласительный токен"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__access_mode__token
msgid "Invited people only"
msgstr "Только приглашенные"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Редактор"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__certification
msgid "Is a Certification"
msgstr "Сертификация"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__is_correct
msgid "Is a correct answer"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_page
msgid "Is a page?"
msgstr "Страница?"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_session_answer
msgid "Is in a Session"
msgstr "В  сессии"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_user_input__is_session_answer
msgid "Is that user input part of a survey session or not."
msgstr "Является ли этот ввод данных пользователем частью  опроса или нет."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4_q3
msgid "Is the wood of a coniferous hard or soft ?"
msgstr "Древесина хвойных твердая или мягкая?"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug4
msgid "Istanbul"
msgstr "Стамбул"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row2
msgid "It is easy to find the product that I want"
msgstr "Легко найти продукт, который я хочу"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug3
msgid "Iznogoud"
msgstr "Изноугуд"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q3_sug1
msgid ""
"I’ve never really wanted to go to Japan. Simply because I don’t like eating "
"fish. And I know that’s very popular out there in Africa."
msgstr ""
"Я никогда не хотел поехать в Японию. Просто потому, что я не люблю есть "
"рыбу. И я знаю, что это очень популярно в Африке."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug1
msgid "Japan"
msgstr "Япония"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_session_code
msgid "Join Session"
msgstr "Присоединиться к сеансу"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug2
msgid "Kim Jong-hyun"
msgstr "Ким Чен Хен"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p5_q1_sug1
msgid "Kurt Cobain"
msgstr "Курт Кобейн"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__sequence
msgid "Label Sequence order"
msgstr "Заказ Последовательности меток"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__matrix_row_ids
msgid "Labels used for proposed choices: rows of matrix"
msgstr "Обозначения, используемые для предлагаемых вариантов: строки шкалы"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__suggested_answer_ids
msgid ""
"Labels used for proposed choices: simple choice, multiple choice and columns"
" of matrix"
msgstr ""
"Обозначения, используемые для предлагаемых вариантов: простой выбор, "
"множественный выбор и столбцы таблицы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__lang
msgid "Language"
msgstr "Язык"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_4
msgid "Large Desk"
msgstr "большой стол"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question____last_update
#: model:ir.model.fields,field_description:survey.field_survey_question_answer____last_update
#: model:ir.model.fields,field_description:survey.field_survey_survey____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input____last_update
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_uid
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question__write_date
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__write_date
#: model:ir.model.fields,field_description:survey.field_survey_survey__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input__write_date
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__last_displayed_page_id
msgid "Last displayed question/page"
msgstr "Последний отображавшийся вопрос/страница"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Late Activities"
msgstr "Просроченные действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_layout
msgid "Layout"
msgstr "Макет"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
#: model_terms:ir.ui.view,arch_db:survey.user_input_session_manage_content
msgid "Leaderboard"
msgstr "Таблица результатов"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_4
msgid "Legs"
msgstr "ножки"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q3_sug2
msgid "Lemon Trees"
msgstr "Лимонные деревья"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_attempts_limited
#: model:ir.model.fields,field_description:survey.field_survey_user_input__is_attempts_limited
msgid "Limited number of attempts"
msgstr "Ограниченное количество попыток"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Live Session"
msgstr "Прямой эфир"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Live Sessions"
msgstr "Прямые эфиры"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_login_required
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_login_required
msgid "Login Required"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "Login required"
msgstr "Необходим логин"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__template_id
msgid "Mail Template"
msgstr "Mail Template"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__constr_mandatory
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Mandatory Answer"
msgstr "Обязательный ответ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__matrix
msgid "Matrix"
msgstr "Матрица"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_row_ids
msgid "Matrix Rows"
msgstr "Строки матрицы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__matrix_subtype
msgid "Matrix Type"
msgstr "Тип матрицы"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_date
msgid "Max date cannot be smaller than min date!"
msgstr "Максимальная дата не может быть меньше минимальной даты!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_datetime
msgid "Max datetime cannot be smaller than min datetime!"
msgstr ""
"Максимальная дата-время не может быть меньше минимальной даты-времени!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_length
msgid "Max length cannot be smaller than min length!"
msgstr "Максимальная длина не может быть меньше минимальной длины!"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_question_validation_float
msgid "Max value cannot be smaller than min value!"
msgstr "Максимальное значение не может быть меньше минимального значения!"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_date
msgid "Maximum Date"
msgstr "Максимальная дата"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_datetime
msgid "Maximum Datetime"
msgstr "Максимальная дата-время"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_max
msgid "Maximum Text Length"
msgstr "Максимальная длина текста"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_max_float_value
msgid "Maximum value"
msgstr "Максимальное значение"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "Maybe you were looking for"
msgstr "Возможно вы искали"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_date
msgid "Minimum Date"
msgstr "Минимальная дата"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_datetime
msgid "Minimum Datetime"
msgstr "Минимальная дата-время"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_length_min
msgid "Minimum Text Length"
msgstr "Минимальная длина текста"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_min_float_value
msgid "Minimum value"
msgstr "Минимальное значение"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Missed"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_blue
msgid "Modern Blue"
msgstr "Современный синий цвет"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_gold
msgid "Modern Gold"
msgstr "Современный золотой"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__certification_report_layout__modern_purple
msgid "Modern Purple"
msgstr "Современный фиолетовый"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q2_sug3
msgid "Mooses"
msgstr "Лоси"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug4
msgid "Mount Elbrus (Russia)"
msgstr "Гора Эльбрус (Россия)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug3
msgid "Mount Etna (Italy - Sicily)"
msgstr "Гора Этна (Италия - Сицилия)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q3_sug1
msgid "Mount Teide (Spain - Tenerife)"
msgstr "Гора Тейде (Испания - Тенерифе)"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug4
msgid "Mountain Pine"
msgstr "Сосна горная"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__text_box
msgid "Multiple Lines Text Box"
msgstr "Многострочное текстовое поле"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with multiple answers"
msgstr "Несколько выбора с несколькими ответами"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Multiple choice with one answer"
msgstr "Несколько выбора с одним ответом"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__multiple_choice
msgid "Multiple choice: multiple answers allowed"
msgstr "Множественный выбор: разрешено давать несколько ответов"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__simple_choice
msgid "Multiple choice: only one answer"
msgstr "Множественный выбор: только один ответ"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__multiple
msgid "Multiple choices per row"
msgstr "Множественный выбор для ряда"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Крайний срок моей активности "

#. module: survey
#: model:gamification.badge,name:survey.vendor_certification_badge
msgid "MyCompany Vendor"
msgstr "Поставщик MyCompany"

#. module: survey
#: model:survey.survey,title:survey.vendor_certification
msgid "MyCompany Vendor Certification"
msgstr "Сертификация поставщиков MyCompany"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "New"
msgstr "Новые"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug3
msgid "New York"
msgstr "Нью Йорк"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__new
msgid "New invite"
msgstr "Новое приглашение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Календарное событие для следующего действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Крайний срок следующего действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_summary
msgid "Next Activity Summary"
msgstr "Краткое описание следующего действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующего действия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__nickname
msgid "Nickname"
msgstr "Никнейм"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug2
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_1
msgid "No"
msgstr "Нет"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "No attempts left."
msgstr "Попыток больше не осталось."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "No question yet, come back later."
msgstr "Вопросов пока нет, приходите позже."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_question_form
msgid "No questions found"
msgstr "Не найдено ни одного вопроса"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__no_scoring
msgid "No scoring"
msgstr "Без подсчета баллов"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_question_answer_action
msgid "No survey labels found"
msgstr "Не найдено метки опрос"

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.survey_user_input_line_action
msgid "No user input lines found"
msgstr "Входящих строк пользователя не найдено"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug2
msgid "No, it's to small for the human eye."
msgstr "Нет, он слишком мал для человеческого глаза."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_user_input
msgid "Nobody has replied to your surveys yet"
msgstr "Еще никто не ответил на ваши опросы"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q5_sug2
msgid "Norway Spruce"
msgstr "Ель норвежская"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p5_q1_sug2
msgid "Not Good, Not Bad"
msgstr "Ни хорошо, ни плохо"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input__state__new
msgid "Not started yet"
msgstr "Еще не начато"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__number
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__numerical_box
msgid "Number"
msgstr "Номер"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__attempts_limit
#: model:ir.model.fields,field_description:survey.field_survey_user_input__attempts_limit
msgid "Number of attempts"
msgstr "Количество попыток"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__column_nb
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Number of columns"
msgstr "Количество столбцов"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_5
msgid "Number of drawers"
msgstr "Количество ящиков"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_error_counter
msgid "Number of errors"
msgstr "Количество ошибок"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих действия"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ошибкой отправки"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__numerical_box
msgid "Numerical Value"
msgstr "Числовое значение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_numerical_box
msgid "Numerical answer"
msgstr "Числовой ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
msgid "Occurrence"
msgstr "Происхождение"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_2_choice_5
msgid "Office Chair Black"
msgstr "Черный офисный стул"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug1
msgid "Once a day"
msgstr "Один раз в день"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug1
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug3
msgid "Once a month"
msgstr "Один раз в месяц"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q5_sug2
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug2
msgid "Once a week"
msgstr "Один раз в неделю"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p1_q3_sug4
msgid "Once a year"
msgstr "Один раз в год"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__matrix_subtype__simple
msgid "One choice per row"
msgstr "Один выбор на ряд"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_question
msgid "One page per question"
msgstr "По одной странице на каждый вопрос"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__page_per_section
msgid "One page per section"
msgstr "Одна страница на каждый раздел"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_layout__one_page
msgid "One page with all the questions"
msgstr "Одна страница, содержащая все вопросы"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "Only survey users can manage sessions."
msgstr "Управлять сессиями могут только участники опроса."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Open Session Manager"
msgstr "Открыть программу управления сессиями"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Необязательный язык перевода (код ISO) для выбора при отправке электронной "
"почты. Если он не задан, будет использоваться английская версия. Обычно это "
"плейсходлер, который обеспечивает соответствующий язык, например, {{ "
"object.partner_id.lang }}."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__null_value
msgid "Optional value to use if the target field is empty"
msgstr "Необязательное значение, используемое если целевое поле пусто"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Options"
msgstr "Параметры"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/models/survey_question.py:0
#, python-format
msgid "Other (see comments)"
msgstr "Другое (см. комментарии)"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2
msgid "Our Company in a few questions ..."
msgstr "Наша компания в нескольких вопросах ..."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__mail_server_id
msgid "Outgoing mail server"
msgstr "Сервер исходящей почты"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Overall Performance"
msgstr "Общие результаты"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug5
msgid "Overpriced"
msgstr "Завышенная цена"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__page_id
msgid "Page"
msgstr "Страница"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__page_ids
msgid "Pages"
msgstr "Страницы"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p3_q2_sug4
msgid "Papyrus"
msgstr "Папирус"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Partial"
msgstr "Частично"

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Partially"
msgstr "Частично"

#. module: survey
#: model:mail.template,subject:survey.mail_template_user_input_invite
msgid "Participate to {{ object.survey_id.display_name }} survey"
msgstr "Принять участие в опросе {{ object.survey_id.display_name }}"

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_user_input
#: model:ir.ui.menu,name:survey.menu_survey_type_form1
#: model:ir.ui.menu,name:survey.survey_menu_user_inputs
msgid "Participations"
msgstr "Участия"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__partner_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Partner"
msgstr "Партнёр"

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
#, python-format
msgid "Passed"
msgstr "Прошло"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Pay attention to the host screen until the next question."
msgstr "Обращайте внимание на экран до следующего вопроса."

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__progression_mode__percent
msgid "Percentage"
msgstr "Процент"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Performance by Section"
msgstr "Показатели по разделам"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug3
msgid "Perhaps"
msgstr "Возможно"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug2
msgid "Peter W. Higgs"
msgstr "Питер В. Хиггс"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1_q1
msgid "Pick a subject"
msgstr "Выберите тему"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Pie Graph"
msgstr "Круговая диаграмма"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug1
msgid "Pinaceae"
msgstr "Семейство сосновые (Pinaceae)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__copyvalue
msgid "Placeholder Expression"
msgstr "Выражение подстановки"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Please enter at least one valid recipient."
msgstr "Пожалуйста, введите хотя бы одного действительного получателя."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid ""
"Please make sure you have at least one question in your survey. You also "
"need at least one section if you chose the \"Page per section\" layout.<br/>"
msgstr ""
"Убедитесь, что в вашем опросе есть хотя бы один вопрос. Вам также необходим "
"хотя бы один раздел, если вы выбрали макет \"Страница на раздел\".<br/>"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3
msgid "Policies"
msgstr "Условия"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q4_sug1
msgid "Pomelos"
msgstr "Помело"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug8
msgid "Poor quality"
msgstr "Плохое качество"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__predefined_question_ids
msgid "Predefined Questions"
msgstr "Предопределенные вопросы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Preview"
msgstr "Просмотр"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2
msgid "Prices"
msgstr "Цены"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Print"
msgstr "Печать"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1
msgid "Products"
msgstr "Продукты"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__progression_mode
msgid "Progression Mode"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__question_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question"
msgstr "Вопрос"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__matrix_question_id
msgid "Question (as matrix row)"
msgstr "Вопрос (как строка матрицы)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_question_answer_count
msgid "Question Answers Count"
msgstr "Количество ответов на вопрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Question Time Limit"
msgstr "Ограничение времени на вопросы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__question_time_limit_reached
msgid "Question Time Limit Reached"
msgstr "Достигнут лимит времени на вопросы"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__question_type
msgid "Question Type"
msgstr "Тип вопрос"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__triggering_question_id
msgid ""
"Question containing the triggering answer to display the current question."
msgstr "Вопрос, содержащий триггерный ответ для отображения текущего вопроса."

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_question_form
#: model:ir.model.fields,field_description:survey.field_survey_question__question_ids
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_ids
#: model:ir.ui.menu,name:survey.menu_survey_question_form1
#: model:ir.ui.menu,name:survey.survey_menu_questions
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Questions"
msgstr "Вопросы"

#. module: survey
#: model:survey.survey,title:survey.survey_demo_quiz
msgid "Quiz about our Company"
msgstr "Короткий тест о нашей компании"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_success
msgid "Quizz Passed"
msgstr "Тест пройден"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Quizz passed"
msgstr "Тест пройден"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__random_questions_count
msgid "Random questions count"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__questions_selection__random
msgid "Randomized per section"
msgstr ""

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__session_state__ready
msgid "Ready"
msgstr "Готово"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__partner_ids
msgid "Recipients"
msgstr "Получатели"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__answer_count
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Registered"
msgstr "зарегистрировано"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__render_model
msgid "Rendering Model"
msgstr "Модель рендеринга"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Reopen"
msgstr "Возобновить"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__existing_text
msgid "Resend Comment"
msgstr "Отправить комментарий повторно"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
msgid "Resend Invitation"
msgstr "Отправить приглашение повторно"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_invite__existing_mode__resend
msgid "Resend invite"
msgstr "Повторно отправить приглашение"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_id
msgid "Responsible"
msgstr "Ответственный"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Results Overview"
msgstr "Обзор результатов"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_retake
msgid "Retry"
msgstr "Повторить"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_speed_rating
msgid "Reward quick answers"
msgstr "Поощряйте быстрые ответы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "Rewards for challenges"
msgstr "Награды за задачи"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answer:"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Right answers:"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__matrix_row_id
msgid "Row answer"
msgstr "Ответ для ряда"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row1"
msgstr "Строка1"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row2"
msgstr "Строка2"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Row3"
msgstr "Строка3"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Rows"
msgstr "Строки"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug4
msgid "Salicaceae"
msgstr "Семейство ивовые"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_email
msgid "Save as user email"
msgstr "Сохранить как электронную почту пользователя"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__save_as_nickname
msgid "Save as user nickname"
msgstr "Сохранить как никнейм пользователя"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p1_q1_sug3
msgid "Sciences"
msgstr "Наука"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__answer_score
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__answer_score
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "Score"
msgstr "Оценка"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_percentage
msgid "Score (%)"
msgstr "Балл (%)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__answer_score
msgid "Score for this choice"
msgstr "Балл за этот выбор"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__answer_score
msgid "Score value for a correct answer to this question."
msgstr "Значение балла за правильный ответ на этот вопрос."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_scored_question
msgid "Scored"
msgstr "Засчитано"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_type
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_type
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Scoring"
msgstr "Оценивание"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__scoring_type
msgid "Scoring Type"
msgstr "Тип подсчета баллов"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_with_answers
msgid "Scoring with answers at the end"
msgstr "Подсчет баллов с ответами в конце"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_survey__scoring_type__scoring_without_answers
msgid "Scoring without answers at the end"
msgstr "Подсчет баллов без ответов в конце"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_search
msgid "Search Label"
msgstr "Ярлык поиска"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Search Question"
msgstr "Искать вопрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Search Survey"
msgstr "Искать соцопрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "Search User input lines"
msgstr "Искать строки вводимой пользователем информации"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__page_id
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "Section"
msgstr "Раздел"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__question_and_page_ids
msgid "Sections and Questions"
msgstr "Разделы и вопросы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "See results"
msgstr "Посмотреть результаты"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_3
msgid "Select all the available customizations for our Customizable Desk"
msgstr "Выберите все доступные варианты настройки для нашего стола"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_1_question_2
msgid "Select all the existing products"
msgstr "Выберите все существующие продукты"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_2
msgid "Select all the products that sell for $100 or more"
msgstr "Выберите все товары, которые продаются за $100 и более"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__model_object_field
msgid ""
"Select target field from the related document model.\n"
"If it is a relationship field you will be able to select a target field at the destination of the relationship."
msgstr ""
"Выбор целевого поля из связанной модели документа.\n"
"Если это поле взаимоотношений, вы сможете выбрать целевое поле на месте назначения взаимоотношений."

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q3
msgid "Select trees that made more than 20K sales this year"
msgstr "Выберите деревья, которые совершили более 20К продаж в этом году"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__questions_selection
#: model:ir.model.fields,field_description:survey.field_survey_survey__questions_selection
msgid "Selection"
msgstr "Выбор"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Send"
msgstr "Отправить"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__sequence
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__question_sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_code
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Code"
msgstr "Код сессии"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_link
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Session Link"
msgstr "Ссылка на сессию"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_state
msgid "Session State"
msgstr "Статус сессии"

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_session_code_unique
msgid "Session code should be unique"
msgstr "Код сессии должен быть уникальным"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug1
msgid "Shanghai"
msgstr "Шанхай"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_kanban
msgid "Share"
msgstr "Поделиться"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__comments_allowed
msgid "Show Comments Field"
msgstr "Показать поле комментариев"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__session_show_leaderboard
msgid "Show Session Leaderboard"
msgstr "Показать результаты сессии"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Показать все записи, у которых дата следующего действия не превышает текущей"
" даты"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_question__question_type__char_box
msgid "Single Line Text Box"
msgstr "Однострочное текстовое поле"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__skipped
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_question
msgid "Skipped"
msgstr "Пропущено"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q3_sug2
msgid "Soft"
msgstr "Мягкий"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Some emails you just entered are incorrect: %s"
msgstr "Некоторые введенные вами адреса электронной почты неверны: %s"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_header
msgid "Sorry, no one answered this survey yet."
msgstr "К сожалению, никто еще не ответил на этот опрос."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Sorry, you have not been fast enough."
msgstr "Извините, но вы были недостаточно оперативны."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q4_sug4
msgid "South America"
msgstr "Южная Америка"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug4
msgid "South Korea"
msgstr "Южная Корея"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug3
msgid "Space stations"
msgstr "Космические станции"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug1
msgid "Spring"
msgstr "Весна"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p1
msgid "Start"
msgstr "Начало"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Certification"
msgstr "Начать сертификацию"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "Start Survey"
msgstr "Начать соцопрос"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__start_datetime
msgid "Start date and time"
msgstr "Дата и время начала"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__state
msgid "Status"
msgstr "Статус"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус основан на плановых действиях\n"
"Просрочено: срок исполнения истек\n"
"Сегодня: выполнить сегодня\n"
"Запланировано: срок в будущем."

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_row2
msgid "Strawberries"
msgstr "Клубника"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_model_object_field
msgid "Sub-field"
msgstr "Субполе"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__sub_object
msgid "Sub-model"
msgstr "Субмодель"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__subject
msgid "Subject"
msgstr "Тема"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_invite_view_form
msgid "Subject..."
msgstr "Тема..."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "Submit"
msgstr "Подтвердить"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_count
msgid "Success"
msgstr "Успех"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__scoring_success_min
msgid "Success %"
msgstr ""

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__success_ratio
msgid "Success Ratio"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_page_statistics_inner
msgid "Success rate:"
msgstr "Успешный рейтинг"

#. module: survey
#: model:ir.actions.act_window,name:survey.survey_question_answer_action
#: model:ir.ui.menu,name:survey.menu_survey_label_form1
msgid "Suggested Values"
msgstr "Предложенные значения"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__suggested_answer_id
msgid "Suggested answer"
msgstr "Предложенный ответ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question_answer__value
msgid "Suggested value"
msgstr "Предложенное значение"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__suggestion
msgid "Suggestion"
msgstr "Предложение"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug2
msgid "Summer"
msgstr "Лето"

#. module: survey
#: model:ir.model,name:survey.model_survey_survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_question__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_id
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__survey_id
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_tree
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Survey"
msgstr "Опрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_response_line_view_tree
msgid "Survey Answer Line"
msgstr "Строка ответа на соцопрос"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_gamification_badge__survey_ids
msgid "Survey Ids"
msgstr "ID опроса"

#. module: survey
#: model:ir.model,name:survey.model_survey_invite
msgid "Survey Invitation Wizard"
msgstr "Приглашение к участию в опросе"

#. module: survey
#: model:ir.model,name:survey.model_survey_question_answer
#: model_terms:ir.ui.view,arch_db:survey.survey_question_answer_view_tree
msgid "Survey Label"
msgstr "Ярлык соцопроса"

#. module: survey
#: model:ir.model,name:survey.model_survey_question
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "Survey Question"
msgstr "Вопрос соцопроса"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Survey Time Limit"
msgstr "Ограничение по времени на опрос"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__survey_time_limit_reached
msgid "Survey Time Limit Reached"
msgstr "Срок выполнения опроса истек"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__title
msgid "Survey Title"
msgstr "Заголовок опроса"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_start_url
msgid "Survey URL"
msgstr "URL-адрес опроса"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Опрос пользователей ввод"

#. module: survey
#: model:ir.model,name:survey.model_survey_user_input_line
msgid "Survey User Input Line"
msgstr "Строка исходных данных пользователя соцопроса"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_form
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_tree
msgid "Survey User inputs"
msgstr "Исходные данные пользователя соцопроса"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_matrix
msgid "Survey filter"
msgstr "Фильтр опрос"

#. module: survey
#: model:ir.actions.server,name:survey.survey_action_server_clean_test_answers
msgid "Survey: Clean test answers"
msgstr "Опрос: очистить тестовые вопросы"

#. module: survey
#: model:mail.template,name:survey.mail_template_user_input_invite
msgid "Survey: Invite"
msgstr "Опрос: Пригласить"

#. module: survey
#: model:mail.template,name:survey.mail_template_certification
msgid "Survey: Send certification by email"
msgstr ""

#. module: survey
#: model:ir.actions.act_window,name:survey.action_survey_form
#: model:ir.ui.menu,name:survey.menu_survey_form
#: model:ir.ui.menu,name:survey.menu_surveys
msgid "Surveys"
msgstr "Опросы"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug3
msgid "Takaaki Kajita"
msgstr "Такааки Кадзита"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "Test"
msgstr "Проверка"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_view_search
msgid "Test Entries"
msgstr "тестовые записи"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__test_entry
msgid "Test Entry"
msgstr "Тестовая запись"

#. module: survey
#: model:ir.model.fields.selection,name:survey.selection__survey_user_input_line__answer_type__char_box
msgid "Text"
msgstr "Текст"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__value_char_box
msgid "Text answer"
msgstr "Текстовый ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Thank you!"
msgstr "Спасибо!"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "The answer must be in the right type"
msgstr "Ответ должен быть правильно напечатан"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,validation_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,validation_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,validation_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,validation_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "The answer you entered is not valid."
msgstr "Ответ, который вы ввели, неверный."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_attempts_limit_check
msgid ""
"The attempts limit needs to be a positive number if the survey has a limited"
" number of attempts."
msgstr ""
"Ограничение на количество попыток должно быть положительным числом, если "
"опрос имеет ограниченное количество попыток."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_badge_uniq
msgid "The badge for each survey should be unique!"
msgstr "Значок для каждого опроса должен быть уникальным!"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row4
msgid "The checkout process is clear and secure"
msgstr "Процесс оформления заказа понятный и безопасный"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_id
msgid "The current question of the survey session."
msgstr "Текущий вопрос этой сессии."

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is greater than the maximum date: "
msgstr "Выбранная дата превышает максимальную дату:"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "The date you selected is lower than the minimum date: "
msgstr "Выбранная вами дата ниже минимальной даты:"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description
msgid ""
"The description will be displayed on the home page of the survey. You can "
"use this to give the purpose and guidelines to your candidates before they "
"start it."
msgstr ""
"Описание будет отображаться на главной странице опроса. Вы можете "
"использовать его, чтобы сообщить участникам цель опроса и основные правила "
"до того, как они начнут опрос."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following customers have already received an invite"
msgstr "Приглашение уже получили следующие клиенты"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "The following emails have already received an invite"
msgstr "Следующие адреса электронной почты уже получили приглашение"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"The following recipients have no user account: %s. You should create user "
"accounts for them or allow external signup in configuration."
msgstr ""
"У следующих получателей нет учетной записи пользователя: %s. Вам следует "
"создать для них учетные записи пользователей или разрешить внешнюю "
"регистрацию в настройках."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row1
msgid "The new layout and design is fresh and up-to-date"
msgstr "Новый макет и дизайн - новый и актуальный"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "The page you were looking for could not be authorized."
msgstr "Невозможно авторизовать страницу, которую вы искали."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_scoring_success_min_check
msgid "The percentage of success has to be defined between 0 and 100."
msgstr "Процент успеха должен быть установлен в диапазоне от 0 до 100."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__is_time_limited
msgid "The question is limited in time"
msgstr "Вопрос ограничен по времени"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_start
msgid "The session will begin automatically when the host starts."
msgstr "Сессия начнется автоматически после запуска хоста."

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The survey has already started."
msgstr "Опрос уже начался."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__is_time_limited
msgid "The survey is limited in time"
msgstr "Опрос ограничен по времени"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_question_start_time
msgid ""
"The time at which the current question has started, used to handle the timer"
" for attendees."
msgstr ""
"Время начала текущего вопроса используется для управления таймером для "
"участников."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_time_limit_check
msgid ""
"The time limit needs to be a positive number if the survey is time limited."
msgstr ""
"Ограничение по времени должно быть положительным числом, если опрос "
"ограничен по времени."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_row3
msgid "The tool to compare the products is useful to make a choice"
msgstr "Инструмент сравнения продуктов полезен для того, чтобы сделать выбор"

#. module: survey
#: code:addons/survey/controllers/main.py:0
#, python-format
msgid "The user has not succeeded the certification"
msgstr "Пользователь не прошел сертификацию"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid "There was an error during the validation of the survey."
msgstr "При проверке опроса была допущена ошибка."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__column_nb
msgid ""
"These options refer to col-xx-[12|6|4|3|2] classes in Bootstrap for "
"dropdown-based simple and multiple choice questions."
msgstr ""

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This answer must be an email address"
msgstr "Этим ответом должен быть адрес электронной почты"

#. module: survey
#. openerp-web
#: code:addons/survey/static/src/js/survey_form.js:0
#, python-format
msgid "This answer must be an email address."
msgstr "Этим ответом должен быть адрес электронной почты."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
msgid "This certificate is presented to"
msgstr "Этот сертификат вручается"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"This certificate is presented to\n"
"                                <br/>"
msgstr ""
"Этот сертификат вручается\n"
"<br/>"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_code
msgid ""
"This code will be used by your attendees to reach your session. Feel free to"
" customize it however you like!"
msgstr ""
"Этот код будет использоваться участниками для входа на вашу сессию. Не "
"стесняйтесь настроить его по своему усмотрению!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_button_form_view
msgid "This is a test survey."
msgstr ""

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/static/src/js/survey_form.js:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a date"
msgstr "Это не дата"

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: code:addons/survey/tests/test_survey.py:0
#, python-format
msgid "This is not a number"
msgstr "Это не число"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__emails
msgid ""
"This list of emails of recipients will not be converted in contacts.        "
"Emails must be separated by commas, semicolons or newline."
msgstr ""
"Этот список получателей писем не будет преобразован в контакты."
"                      Адреса эл. почты должны быть отделены друг от друга "
"запятыми, точками с запятой или символом новой строки."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__description_done
msgid "This message will be displayed when survey is completed"
msgstr "Это сообщения появится, когда соцопрос будет завершен"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
#: model_terms:ir.ui.view,arch_db:survey.survey_question_tree
msgid "This question depends on another question's answer."
msgstr "Этот вопрос зависит от ответа на другой вопрос."

#. module: survey
#: code:addons/survey/models/survey_question.py:0
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_burger_quiz_p5_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p2_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p3_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q1
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q2
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q3
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q4
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p4_q6
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5
#: model:survey.question,constr_error_msg:survey.survey_demo_quiz_p5_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q3
#: model:survey.question,constr_error_msg:survey.survey_feedback_p1_q4
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q1
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q2
#: model:survey.question,constr_error_msg:survey.survey_feedback_p2_q3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_1_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_2_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_1
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_2
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_3
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_4
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_5
#: model:survey.question,constr_error_msg:survey.vendor_certification_page_3_question_6
#, python-format
msgid "This question requires an answer."
msgstr "Этот вопрос требует ответа."

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid ""
"This survey does not allow external people to participate. You should create"
" user accounts or update survey access mode accordingly."
msgstr ""
"В данном опросе не допускается участие посторонних лиц. Вам следует создать "
"учетные записи пользователей или обновить параметры доступа к опросу."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "This survey is now closed. Thank you for your interest !"
msgstr "Данный опрос завершен. Спасибо за проявленный интерес!"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "This survey is open only to registered people. Please"
msgstr ""
"Этот соцопрос открыт только для зарегистрированных пользователей. "
"Пожалуйста,"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__time_limit
msgid "Time limit (minutes)"
msgstr "Ограничение по времени (минуты)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__time_limit
msgid "Time limit (seconds)"
msgstr "Ограничение по времени (секунды)"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__title
msgid "Title"
msgstr "Обращение"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form
msgid ""
"To take this survey, please close all other tabs on <strong class=\"text-"
"danger\"/>."
msgstr ""
"Чтобы пройти этот опрос, пожалуйста, закройте все другие вкладки <strong "
"class=\"text-danger\"/>."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Today Activities"
msgstr "Действия на сегодня"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p2_q2_sug2
msgid "Tokyo"
msgstr "Токио"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input__scoring_total
msgid "Total Score"
msgstr "Общий балл"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col4
msgid "Totally agree"
msgstr "Полностью согласен"

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q2_col1
msgid "Totally disagree"
msgstr "Категорически не согласен"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p4
msgid "Trees"
msgstr "Деревья"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_answer_id
msgid "Triggering Answer"
msgstr "Триггерный ответ"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__triggering_question_id
msgid "Triggering Question"
msgstr "Триггерный вопрос"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_search
msgid "Type"
msgstr "Тип"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип активности исключения в записи."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__suggested_answer_ids
msgid "Types of answers"
msgstr "Типы ответов"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q1_sug2
msgid "Ulmaceae"
msgstr "Семейство ильмовые"

#. module: survey
#: code:addons/survey/wizard/survey_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"Невозможно отправить сообщение, настройте адрес электронной почты "
"отправителя."

#. module: survey
#. openerp-web
#: code:addons/survey/models/survey_user_input.py:0
#: code:addons/survey/static/src/js/survey_result.js:0
#, python-format
msgid "Unanswered"
msgstr "Не отвечено"

#. module: survey
#: code:addons/survey/models/survey_user_input.py:0
#, python-format
msgid "Uncategorized"
msgstr "Без категории"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_2
msgid "Underpriced"
msgstr "Заниженная цена"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "Unfortunately, you have failed the test."
msgstr "К сожалению, вы не прошли тест."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug3
msgid "Unique"
msgstr "Уникальный"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные сообщения"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_survey_view_search
msgid "Upcoming Activities"
msgstr "Будущие виды деятельности"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__description
msgid ""
"Use this field to add additional explanations about your question or to "
"illustrate it with pictures or a video"
msgstr ""
"Использовать это поле, чтобы добавить дополнительные пояснения к вашему "
"вопросу или проиллюстрировать его картинками или видеороликом"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_question__random_questions_count
msgid ""
"Used on randomized sections to take X random questions from all the "
"questions of that section."
msgstr ""
"Используется в рандомизированных разделах, чтобы взять X случайных вопросов "
"из всех вопросов данного раздела."

#. module: survey
#: model:survey.question.answer,value:survey.survey_feedback_p2_q1_sug2
msgid "Useful"
msgstr "Полезный"

#. module: survey
#: model:res.groups,name:survey.group_survey_user
msgid "User"
msgstr "Пользователь"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_choice
msgid "User Choice"
msgstr "Выбор пользователя"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_user_input_line__user_input_id
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_search
msgid "User Input"
msgstr "Вводимая пользователем информация"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.question_result_number_or_date
#: model_terms:ir.ui.view,arch_db:survey.question_result_text
msgid "User Responses"
msgstr "Ответы пользователя"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_user_input_line_view_form
msgid "User input line details"
msgstr "Детали строк исходных данных пользователя"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__user_input_ids
msgid "User responses"
msgstr "Ответы пользователя"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_go_back
msgid "Users can go back"
msgstr "Пользователи могут вернуться"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_invite__survey_users_can_signup
#: model:ir.model.fields,field_description:survey.field_survey_survey__users_can_signup
msgid "Users can signup"
msgstr "Пользователи могут зарегистрироваться"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_required
msgid "Validate entry"
msgstr "Подтвердить запись"

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_question__validation_error_msg
msgid "Validation Error message"
msgstr "Сообщение об ошибке валидации"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q1_sug2
msgid "Vegetables"
msgstr "Овощи"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_2_question_3_choice_1
msgid "Very underpriced"
msgstr "Очень заниженная цена"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q2_sug3
msgid "Vietnam"
msgstr "Вьетнам"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid ""
"We have registered your answer! Please wait for the host to go to the next "
"question."
msgstr ""
"Мы зарегистрировали ваш ответ! Пожалуйста, подождите ведущего, чтобы перейти"
" к следующему вопросу."

#. module: survey
#: model:ir.model.fields,field_description:survey.field_survey_survey__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайтом"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_5
msgid ""
"What day and time do you think most customers are most likely to call "
"customer service (not rated)?"
msgstr ""
"Как вы думаете, в какой день и время большинство клиентов чаще всего звонят "
"в службу поддержки (вопрос не оценивается)?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_3_question_4
msgid ""
"What day to you think is best for us to start having an annual sale (not "
"rated)?"
msgstr ""
"В какой день, по вашему мнению, лучше всего начать ежегодную распродажу "
"(вопрос не оценивается)?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q2
msgid "What do you think about our new eCommerce ?"
msgstr "Что вы думаете о нашей новой электронной коммерции?"

#. module: survey
#: model:survey.question,title:survey.vendor_certification_page_2_question_3
msgid "What do you think about our prices (not rated)?"
msgstr "Что вы думаете о наших ценах (вопрос не оценивается)?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5_q1
msgid "What do you think about this survey ?"
msgstr "Что вы думаете об этом опросе?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q2
msgid "What is the biggest city in the world ?"
msgstr "Какой самый большой город в мире?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q1
msgid "What is your email ?"
msgstr "Какой у вас адрес электронной почты?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q2
msgid "What is your nickname ?"
msgstr "Какой у вас никнейм?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q2
msgid "What is, approximately, the critical mass of plutonium-239 ?"
msgstr "Какова, примерно, критическая масса плутония-239?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_model_object_field
msgid ""
"When a relationship field is selected as first field, this field lets you "
"select the target field within the destination document model (sub-model)."
msgstr ""
"Когда поле связи выбрано как первое поле, это поле даёт вам выбор целевого "
"поля в пределах модели целевого документа (под-модели)."

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_invite__sub_object
msgid ""
"When a relationship field is selected as first field, this field shows the "
"document model the relationship goes to."
msgstr ""
"Когда связывающее поле выбрано как первое поле, это поле показывает "
"связанную модель документа."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q1
msgid "When did Genghis Khan die ?"
msgstr "Когда умер Чингисхан?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q2
msgid "When did precisely Marc Demo crop its first apple tree ?"
msgstr "Когда именно Марк Демо посадил свою первую яблоню?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q6
msgid "When do you harvest those fruits"
msgstr "Когда вы собираете эти фрукты"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p2_q1
msgid "When is Mitchell Admin born ?"
msgstr "Когда родился Владимир Админ?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q2
msgid "When is your date of birth ?"
msgstr "Когда у вас дата рождения?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1_q3
msgid "Where are you from ?"
msgstr "Откуда вы родом?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p1_q1
msgid "Where do you live ?"
msgstr "Где вы живете?"

#. module: survey
#: model:ir.model.fields,help:survey.field_survey_survey__session_show_leaderboard
msgid ""
"Whether or not we want to show the attendees leaderboard for this survey."
msgstr "Хотим ли мы показать таблицу результатов этого опроса."

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q1
msgid "Which Musician is not in the 27th Club ?"
msgstr "Какого музыканта нет в \"Клубе 27\"?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q1
msgid "Which category does a tomato belong to"
msgstr "К какой группе относится помидор"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p2_q3
msgid "Which is the highest volcano in Europe ?"
msgstr "Какой самый высокий вулкан в Европе?"

#. module: survey
#: model:survey.question,title:survey.survey_feedback_p2_q1
msgid "Which of the following words would you use to describe our products ?"
msgstr ""
"Какое из следующих слов вы бы использовали для описания нашей продукции?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p3_q2
msgid "Which of the following would you use to pollinate"
msgstr ""
"Что из перечисленного ниже вы могли бы использовать для опыления растений"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q2
msgid "Which painting/drawing was not made by Pablo Picasso ?"
msgstr "Какая картина/рисунок не была создана Пабло Пикассо?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p5_q3
msgid "Which quote is from Jean-Claude Van Damme"
msgstr "Какая цитата принадлежит Жан-Клоду Ван Дамму"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p1
msgid "Who are you ?"
msgstr "Кто вы?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p3_q2
msgid "Who is the architect of the Great Pyramid of Giza ?"
msgstr "Кто является архитектором Великой пирамиды Гизы?"

#. module: survey
#: model:survey.question,title:survey.survey_demo_burger_quiz_p4_q1
msgid ""
"Who received a Nobel prize in Physics for the discovery of neutrino "
"oscillations, which shows that neutrinos have mass ?"
msgstr ""
"Кто получил Нобелевскую премию по физике за открытие нейтринных осцилляций, "
"которое показывает, что нейтрино имеют массу?"

#. module: survey
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_3_choice_3
msgid "Width"
msgstr "Ширина"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q1_sug4
msgid "Willard S. Boyle"
msgstr "Уиллард С. Бойл"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p3_q6_sug4
msgid "Winter"
msgstr "Зима"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid ""
"YYYY-MM-DD hh:mm:ss\n"
"                                        <i class=\"fa fa-calendar fa-2x\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>"
msgstr ""

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_quiz_p4_q6_sug1
#: model:survey.question.answer,value:survey.vendor_certification_page_1_question_1_choice_2
msgid "Yes"
msgstr "Да"

#. module: survey
#: model:survey.question.answer,value:survey.survey_demo_burger_quiz_p4_q3_sug1
msgid "Yes, that's the only thing a human eye can see."
msgstr "Да, это единственное, что может увидеть человеческий глаз."

#. module: survey
#: model_terms:ir.actions.act_window,help:survey.action_survey_form
msgid ""
"You can create surveys for different purposes: customer opinion, services "
"feedback, recruitment interviews, employee's periodical evaluations, "
"marketing campaigns, etc."
msgstr ""
"Вы можете создавать опросы для разных целей: мнение клиента, комментарии и "
"предложения по услугам, опросы по поставкам, периодические оценки "
"сотрудников, кампании по маркетингу и т.п."

#. module: survey
#: model:ir.model.constraint,message:survey.constraint_survey_survey_certification_check
msgid ""
"You can only create certifications for surveys that have a scoring "
"mechanism."
msgstr ""
"Вы можете создавать сертификаты только для тех опросов, которые имеют "
"механизм подсчета баллов."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey has no sections."
msgstr ""
"Вы не можете отправить приглашение для опроса \"Одна страница на раздел\", "
"если опрос не имеет разделов."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid ""
"You cannot send an invitation for a \"One page per section\" survey if the "
"survey only contains empty sections."
msgstr ""
"Вы не можете отправить приглашение для опроса \"Одна страница на раздел\", "
"если опрос содержит только пустые разделы."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send an invitation for a survey that has no questions."
msgstr ""
"Вы не можете отправить приглашение на соцопрос, в котором нет вопросов."

#. module: survey
#: code:addons/survey/models/survey_survey.py:0
#, python-format
msgid "You cannot send invitations for closed surveys."
msgstr "Вы не можете отправлять приглашения на закрытые соцопросы."

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You received the badge"
msgstr "Вы получили значок"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "You scored"
msgstr "Вы заработали"

#. module: survey
#: model:survey.question,title:survey.survey_demo_quiz_p5
msgid "Your feeling"
msgstr "Ваше ощущение"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_question_form
msgid "ans"
msgstr "ответ"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "answered"
msgstr "отвечено"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "attempts"
msgstr "попытки"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. No one can solve challenges like you do"
msgstr "Например, никто не может решать задачи так, как это делаете вы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.gamification_badge_form_view_simplified
msgid "e.g. Problem Solver"
msgstr "например, специалист по решению проблем"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "e.g. Satisfaction Survey"
msgstr "например, опрос по оценке качества обслуживания"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid ""
"for successfully completing\n"
"                                <br/>"
msgstr ""
"за успешное выполнение\n"
"<br/> "

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_auth_required
msgid "log in"
msgstr "вход"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_form
msgid "minutes"
msgstr "минут"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "of"
msgstr "из"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_classic
#: model_terms:ir.ui.view,arch_db:survey.certification_report_view_modern
msgid "of achievement"
msgstr "достижения"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press CTRL+Enter"
msgstr "или нажмите CTRL+Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_in_progress
msgid "or press Enter"
msgstr "или нажмите Enter"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_progression
msgid "pages"
msgstr "страницы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_fill_form_done
msgid "review your answers"
msgstr "посмотреть ваши ответы"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_closed_expired
msgid "survey expired"
msgstr "срок действия опроса истек"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_void_content
msgid "survey is empty"
msgstr "опрос не содержит информации"

#. module: survey
#: model_terms:ir.ui.view,arch_db:survey.survey_403_page
msgid "this page"
msgstr "эта страница"
