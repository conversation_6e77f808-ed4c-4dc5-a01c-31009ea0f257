# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON>, 2021
# <PERSON>up <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:21+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: bus
#: model:ir.model.constraint,message:bus.constraint_bus_presence_bus_user_presence_unique
msgid "A user can only have one IM status."
msgstr "사용자는 메신저 상태를 하나만 가질 수 있습니다."

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "자리 비움"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "채널"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "커뮤니케이션 버스"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "작성자"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "작성일자"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "표시명"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "메신저 상태"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus____last_update
#: model:ir.model.fields,field_description:bus.field_bus_presence____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "최근 투표"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "최근 출석"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "메시지"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "오프라인"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "온라인"

#. module: bus
#. openerp-web
#: code:addons/bus/static/src/js/web_client_bus.js:0
#: code:addons/bus/static/src/js/web_client_bus.js:0
#, python-format
msgid "Refresh"
msgstr "새로 고침"

#. module: bus
#. openerp-web
#: code:addons/bus/static/src/js/web_client_bus.js:0
#, python-format
msgid "The page appears to be out of date."
msgstr "페이지가 오래된 것 같습니다."

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "사용자 출석"

#. module: bus
#: model:ir.model,name:bus.model_res_users
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "사용자"

#. module: bus
#: code:addons/bus/controllers/main.py:0
#, python-format
msgid "bus.Bus not available in test mode"
msgstr "bus.Bus는 시험 모드에서는 사용할 수 없음"
