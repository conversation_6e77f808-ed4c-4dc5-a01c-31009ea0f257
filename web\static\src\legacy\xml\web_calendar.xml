<template>
    <div t-name="CalendarView" class="o_calendar_container">
        <div class="o_calendar_view">
            <div class="o_calendar_buttons" role="toolbar" aria-label="Calendar toolbar"/>
            <div class="o_calendar_widget" />
        </div>
        <div class="o_calendar_sidebar_container d-none d-md-block">
            <div class="o_calendar_sidebar">
                <div class="o_calendar_mini"/>
            </div>
        </div>
    </div>

    <t t-name="calendar-box">
        <div t-att-style="typeof color === 'string' ? ('background-color:' + color) + ';' : ''" t-attf-class="o_event #{record.is_highlighted ? 'o_event_hightlight' : ''} #{typeof color === 'number' ? _.str.sprintf('o_calendar_color_%s', color) : 'o_calendar_color_1'} #{record.is_hatched ? 'o_calendar_hatched o_calendar_hatched_' + (typeof color === 'number' ? color : 1): ''} #{record.is_striked ? 'o_calendar_striked o_calendar_striked_' + (typeof color === 'number' ? color : 1): ''}">
            <span t-if="showTime" class="fc-time"/>
            <div class="o_event_title" t-esc="record.display_name"/>
            <div t-if="showLocation and record.location" t-esc="record.location"/>
        </div>
    </t>

    <t t-name="CalendarView.event.popover.placeholder">
        <div t-attf-class="o_cw_popover popover card shadow #{typeof color === 'number' ? _.str.sprintf('o_calendar_color_%s', color) : ''}" role="tooltip">
            <div class="arrow"/>
            <div class="card-header d-flex justify-content-between py-2 pr-2">
                <h4 class="p-0 pt-1">
                    <span class="popover-header border-0"/><span t-if="displayLock" class="ml-1"><i class="fa fa-lock"/></span>
                </h4>
                <span class="o_cw_popover_close ml-4 mt-2 mr-2"><i class="fa fa-close"/></span>
            </div>
            <div class="o_cw_body">
            </div>
        </div>
    </t>

    <t t-name="CalendarView.event.popover">
        <div class="o_cw_body">
            <ul class="list-group list-group-flush">
                <li t-if="!widget.hideDate and widget.eventDate.date" class="list-group-item">
                    <i class="fa fa-fw fa-calendar-o"/>
                    <b class="text-capitalize" t-esc="widget.eventDate.date"/> <small t-if="widget.eventDate.duration"><b t-esc="_.str.sprintf('(%s)', widget.eventDate.duration)"/></small>
                </li>
                <li t-if="!widget.hideTime and widget.eventTime.time" class="list-group-item">
                    <i class="fa fa-fw fa-clock-o"/>
                    <b t-esc="widget.eventTime.time"/> <small t-if="widget.eventTime.duration"><b t-esc="_.str.sprintf('(%s)', widget.eventTime.duration)"/></small>
                </li>
            </ul>
            <ul t-if="widget.isEventDetailsVisible()" class="list-group list-group-flush o_cw_popover_fields_secondary"/>
            <div t-attf-class="card-footer border-top #{ !widget.isEventEditable() &amp;&amp;!widget.isEventDeletable() ? 'o_footer_shrink' : '' }">
                <a t-if="widget.isEventEditable()" href="#" class="btn btn-primary o_cw_popover_edit">Edit</a>
                <a t-if="widget.isEventDeletable()" href="#" class="btn btn-secondary o_cw_popover_delete ml-2">Delete</a>
            </div>
        </div>
    </t>

    <t t-name="CalendarView.sidebar.filter">
        <t t-set="needCollapse" t-value="widget.filters.length > 2 and widget.title"></t>
        <t t-set="titleWithoutSpace" t-value="widget.title.split(' ').join('')"></t>
        <div class="o_calendar_filter">
            <div class="d-flex">
                <div class="o_calendar_filter_items_checkall mr-2" data-value="check_all">
                    <input t-att-id="widget.title + '_id'" type="checkbox" name="selection" class="position-absolute"
                           t-att-checked="widget.filter_check_all[widget.fieldName] ? true : undefined"
                    />
                    <label t-att-for="widget.title + '_id'" class="align-items-center m-0">
                        <span class="o_cw_filter_input_bg o_calendar_filter_all">
                            <i class="fa fa-check position-relative"/>
                        </span>
                    </label>
                </div>
                <div t-if="needCollapse" type="button" data-toggle="collapse"
                     t-attf-data-target="#o_cw_filter_collapse_#{titleWithoutSpace}"
                     class="justify-content-between align-items-center h5">
                    <span t-esc="widget.title"/>
                    <i class="o_cw_filter_collapse_icon fa fa-chevron-down"/>
                </div>
                <h5 t-elif="widget.title" t-esc="widget.title"/>
            </div>
            <div t-attf-class="o_calendar_filter_items #{needCollapse ? 'collapse show' : 0}" aria-expanded="true" t-attf-id="o_cw_filter_collapse_#{titleWithoutSpace}">
                <t t-foreach="widget.filters" t-as="filter" t-if="filter.display == null || filter.display">
                    <t t-set="idForLabel" t-value="_.uniqueId('o_calendar_filter_item_')"/>
                    <t t-set="color" t-value="widget.getColor(filter.color_index)"/>
                    <t t-set="isColorNumber" t-value="typeof color === 'number'"/>

                    <div t-attf-class="o_calendar_filter_item w-100 position-relative mb-2 #{isColorNumber ? _.str.sprintf('o_cw_filter_color_%s', color) : ''}"
                         t-att-data-value="filter.value" t-att-data-id="filter.id">

                        <input type="checkbox" t-att-id="idForLabel" name="selection" class="position-absolute" t-att-checked="filter.active ? true : undefined"/>

                        <label t-att-for="idForLabel" class="d-flex align-items-center m-0">
                            <span t-attf-class="o_cw_filter_input_bg align-items-center d-flex flex-shrink-0 justify-content-center position-relative mr-1 #{widget.avatar_field ? 'o_beside_avatar' : ''}"
                                t-att-style="color and !isColorNumber ? _.str.sprintf('border-color:%s;background-color:%s', color, color) : ''">
                                <i class="fa fa-check position-relative"/>
                            </span>
                            <i t-if="filter.value == 'all'" class="o_cw_filter_avatar fa fa-users fa-fw  flex-shrink-0 mr-1" role="img" aria-label="Avatar" title="Avatar"/>
                            <img t-elif="widget.avatar_field and filter.value" t-attf-src="/web/image/#{widget.avatar_model}/#{filter.value}/#{widget.avatar_field}" class="o_cw_filter_avatar flex-shrink-0 mr-1" alt="Avatar"/>
                            <span class="o_cw_filter_title text-truncate flex-grow" t-esc="filter.label" t-attf-title="#{ ['all', false].includes(filter.value) or !widget.avatar_field ? filter.label : '' }"/>
                        </label>

                        <button t-if="filter.id" class="o_remove btn bg-white text-700 py-0 px-2" title="Remove this favorite from the list" role="img" aria-label="Remove this favorite from the list">
                            <i class="fa fa-times"/>
                        </button>
                    </div>
                </t>
            </div>
        </div>
    </t>

    <t t-name="CalendarView.buttons">
        <div class="o_calendar_buttons">
            <span class="o_calendar_navigation_buttons">
                <t t-call="CalendarView.navigation_buttons" />
            </span>
            <t t-if="!scaleDrop">
                <span class="o_calendar_scale_buttons">
                    <t t-call="CalendarView.scale_buttons" />
                </span>
            </t>
        </div>
    </t>

    <t t-name="CalendarView.navigation_buttons">
        <button class="o_calendar_button_prev btn btn-primary" aria-label="Previous" title="Previous"><span class="fa fa-arrow-left"/></button>
        <button class="o_calendar_button_today btn btn-primary">Today</button>
        <button class="o_calendar_button_next btn btn-primary" aria-label="Next" title="Next"><span class="fa fa-arrow-right"/></button>
    </t>

    <t t-name="CalendarView.scale_buttons">
        <div class="btn-group">
            <button type="button" t-if="scales.includes('day')" class="o_calendar_button_day btn btn-secondary">Day</button>
            <button type="button" t-if="scales.includes('week')" class="o_calendar_button_week btn btn-secondary">Week</button>
            <button type="button" t-if="scales.includes('month')" class="o_calendar_button_month btn btn-secondary">Month</button>
            <button type="button" t-if="scales.includes('year')" class="o_calendar_button_year btn btn-secondary">Year</button>
        </div>
    </t>

    <main t-name="CalendarView.quick_create" class="o_calendar_quick_create">
        <div class="form-group">
            <label for='name' class='col-form-label o_form_label'>Meeting Subject:</label>
            <input type='text' name='name' class="o_input o_required_modifier" t-att-value="widget.dataTemplate.name or None"/>
        </div>
    </main>

    <t t-name="CalendarView.yearEvent.popover">
        <div class="o_cw_body">
            <t t-foreach="groupKeys" t-as="groupKey">
                <div class="font-weight-bold mt-2" t-esc="groupKey"/>
                <t t-foreach="groupedEvents[groupKey]" t-as="event">
                    <t t-set="record" t-value="event.extendedProps.record"/>
                    <t t-set="color" t-value="event.extendedProps.color_index"/>
                    <a href="#"
                        t-attf-class="o_cw_popover_link o_calendar_color_#{typeof color === 'number' ? _.str.sprintf('%s', color) : '1'} o_attendee_status_#{record.is_alone ? 'alone' : record.attendee_status} btn-link d-block"
                        t-att-data-id="record.id"
                        t-att-data-title="record.display_name">
                        <t t-esc="record.start_hour"/> <t t-esc="record.display_name"/>
                    </a>
                </t>
            </t>
            <div class="mt-2">
                <a t-if="canCreate" href="#" class="btn-link o_cw_popover_create">
                    <i class="fa fa-plus"/> Create
                </a>
            </div>
        </div>
    </t>

    <t t-name="CalendarView.yearEvent.popover.placeholder">
        <div class="popover o_cw_popover" style="position: relative">
            <div class="arrow"></div>
            <div class="popover-header"></div>
            <div style="position: absolute; top: 0; right: 0.5rem;">
                <span class="o_cw_popover_close"><i class="fa fa-close small"></i></span>
            </div>
            <div class="popover-body"></div>
        </div>
    </t>
</template>
