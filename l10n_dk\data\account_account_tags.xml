<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!--Resultatopgørelse indtægter-->
    <record id="account_tag_revenue" model="account.account.tag">
        <field name="name">Omsætning</field>
        <field name="applicability">accounts</field>
    </record>

    <!--Resultatopgørelse udgifer-->
    <record id="account_tag_direct_costs" model="account.account.tag">
        <field name="name">Direkte omkostninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_staff_costs" model="account.account.tag">
        <field name="name">Personaleomkostninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_sales_promotions_costs" model="account.account.tag">
        <field name="name">Salgsfremmende omkostninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_cars_equipment_costs" model="account.account.tag">
        <field name="name">Biler og driftmidler</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_rooms_costs" model="account.account.tag">
        <field name="name">Lokaleomkostninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_administration_costs" model="account.account.tag">
        <field name="name">Administrationsomkostninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_depreciation" model="account.account.tag">
        <field name="name">Af- og nedskrivninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_financial_income" model="account.account.tag">
        <field name="name">Finansielle indtægter</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_financial_expenses" model="account.account.tag">
        <field name="name">Finansielle udgifter</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_extraordinary_income" model="account.account.tag">
        <field name="name">Ekstraordinære indtægter</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_extraordinary_expenses" model="account.account.tag">
        <field name="name">Ekstraordinære udgifter</field>
        <field name="applicability">accounts</field>
    </record>

    <!--Assets Aktiver-->
    <record id="account_tag_Goodwill_licenses_etc" model="account.account.tag">
        <field name="name">Goodwill, licenser mm.</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_land_buildings" model="account.account.tag">
        <field name="name">Grunde og bygninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_other_equipment_tools_inventory" model="account.account.tag">
        <field name="name">Andre anlæg, driftsmateriel og inventar</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_inventories" model="account.account.tag">
        <field name="name">Varebeholdninger</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_work_in_progress" model="account.account.tag">
        <field name="name">Igangværende arbejder</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_prepayments" model="account.account.tag">
        <field name="name">Forudbetaling til leverandører</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_receivable" model="account.account.tag">
        <field name="name">Tilgodehavender</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_other_receivables" model="account.account.tag">
        <field name="name">Andre tilgodehavender</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_liquidity" model="account.account.tag">
        <field name="name">Likvide beholdninger</field>
        <field name="applicability">accounts</field>
    </record>

    <!--Liabilities Passiver-->
     <record id="account_tag_equity_individual_enterprises" model="account.account.tag">
        <field name="name">Egenkapital enkeltmandsvirksomheder</field>
        <field name="applicability">accounts</field>
     </record>
     <record id="account_tag_equity_corporations" model="account.account.tag">
        <field name="name">Egenkapital selskaber</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_credit_institutions" model="account.account.tag">
        <field name="name">Kreditinstitutter</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_bank_loans" model="account.account.tag">
        <field name="name">Bankgæld</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_prepayments_from_customers" model="account.account.tag">
        <field name="name">Forudbetalinger fra kunder</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_payable" model="account.account.tag">
        <field name="name">Leverandørgæld</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_vat_taxes" model="account.account.tag">
        <field name="name">Moms og afgifter</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_Payroll_related_liabilities" model="account.account.tag">
        <field name="name">Løn relateret gæld</field>
        <field name="applicability">accounts</field>
    </record>
    <record id="account_tag_other_current_liabilities" model="account.account.tag">
        <field name="name">Anden kortfristet gæld</field>
        <field name="applicability">accounts</field>
    </record>
</odoo>
