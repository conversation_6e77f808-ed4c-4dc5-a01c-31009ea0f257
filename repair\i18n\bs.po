# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * repair
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:18+0000\n"
"PO-Revision-Date: 2018-09-21 13:18+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: repair
#: model:mail.template,report_name:repair.mail_template_repair_quotation
msgid "${(object.name or '').replace('/','_')}"
msgstr "${(object.name or '').replace('/','_')}"

#. module: repair
#: model:mail.template,subject:repair.mail_template_repair_quotation
msgid "${object.partner_id.name} Repair Orders (Ref ${object.name or 'n/a' })"
msgstr ""
"${object.partner_id.name} Nalog popravke (Ref ${object.name or 'n/a' })"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "(<i>Remove</i>)"
msgstr "(<i>Ukloni</i>)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "(update)"
msgstr "(osvježi)"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__state
msgid ""
"* The 'Draft' status is used when a user is encoding a new and unconfirmed repair order.\n"
"* The 'Confirmed' status is used when a user confirms the repair order.\n"
"* The 'Ready to Repair' status is used to start to repairing, user can start repairing only after repair order is confirmed.\n"
"* The 'To be Invoiced' status is used to generate the invoice before or after repairing done.\n"
"* The 'Done' status is set when repairing is completed.\n"
"* The 'Cancelled' status is used when user cancel repair order."
msgstr ""
"* 'U priprem' status se koristi kada korisnik unosi novi i ne potvrđen nalog popravke.\n"
"* 'Potvrđen' status se koristi kada korisnik potvrđuje nalog popravke.\n"
"* 'Spremno za popravku' status se koristi za početak popravke, korisnik može da započne popravak tek nakon što je nalog popravke potvđen.\n"
"* 'Za fakturisanje' status se koristi za kreiranje fakture prije ili nakon što je popravka završena.\n"
"* 'Završen' status je postavljen kada je popravka završena.\n"
"* 'Otkazan' status se koristi kada korisnik otkaže nalog popravke."

#. module: repair
#: model:mail.template,body_html:repair.mail_template_repair_quotation
msgid ""
"<?xml version=\"1.0\"?>\n"
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px;font-size: 13px;\">\n"
"        Hello ${object.partner_id.name},<br/>\n"
"        Here is your repair order ${doc_name} <strong>${object.name}</strong>\n"
"        % if object.origin:\n"
"            (with reference: ${object.origin} )\n"
"        % endif\n"
"        % if object.invoice_method != 'none':\n"
"            amounting in <strong>${format_amount(object.amount_total, object.pricelist_id.currency_id)}.</strong><br/>\n"
"        % else:\n"
"            .<br/>\n"
"        % endif\n"
"        You can reply to this email if you have any questions.\n"
"        <br/><br/>\n"
"        Thank you,\n"
"    </p>\n"
"</div>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<i>(Add)</i>"
msgstr "<i>(Dodaj)</i>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid ""
"<span class=\"o_form_label\">This operation will cancel the Repair process, "
"but will not cancel it's Invoice. Do you want to continue?</span>"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Lot/Serial Number:</strong>"
msgstr "<strong>Lot/Serijski broj:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Operations</strong>"
msgstr "<strong>Operacije</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Parts</strong>"
msgstr "<strong>Dijelovi</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Printing Date:</strong>"
msgstr "<strong>Datum štampanja:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Product to Repair:</strong>"
msgstr "<strong>Proizvodi za popravku:</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Shipping address :</strong>"
msgstr "<strong>Adresa isporuke :</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total Without Taxes</strong>"
msgstr "<strong>Ukupno bez poreza</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Total</strong>"
msgstr "<strong>Ukupno</strong>"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "<strong>Warranty:</strong>"
msgstr "<strong>Garancija:</strong>"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: repair
#: selection:repair.line,type:0
msgid "Add"
msgstr "Dodaj"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add internal notes..."
msgstr "Dodajte interne zabilješke ..."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Add quotation notes..."
msgstr "Dodajte zabilješke predračuna..."

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "After Repair"
msgstr "Poslije popravka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "Before Repair"
msgstr "Prije popravka"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Cancel"
msgstr "Otkaži"

#. module: repair
#: model:ir.model,name:repair.model_repair_cancel
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Cancel Repair"
msgstr "Otkaži popravak"

#. module: repair
#: model:ir.actions.act_window,name:repair.action_cancel_repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid "Cancel Repair Order"
msgstr "Otkaži nalog za popravak"

#. module: repair
#: selection:repair.line,state:0 selection:repair.order,state:0
msgid "Cancelled"
msgstr "Otkazan"

#. module: repair
#: code:addons/repair/models/repair.py:244
#, python-format
msgid "Cannot cancel completed repairs."
msgstr "Ne mogu se otkazati završene popravke."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__partner_id
msgid ""
"Choose partner for whom the order will be invoiced and delivered. You can "
"find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__company_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Company"
msgstr "Kompanija"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Confirm Repair"
msgstr "Potvrdi popravak"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
#: selection:repair.line,state:0 selection:repair.order,state:0
msgid "Confirmed"
msgstr "Potvrđeno"

#. module: repair
#: code:addons/repair/models/repair.py:607
#: code:addons/repair/models/repair.py:667
#, python-format
msgid ""
"Couldn't find a pricelist line matching this product and quantity.\n"
"You have to change either the product, the quantity or the pricelist."
msgstr ""
"Nije bilo moguće pronaći stavku cijenovnika koja odgovara ovom proizvodu i količini.\n"
"Morate da promjeniti proizvod, količinu ili cijenovnik."

#. module: repair
#: model:ir.actions.act_window,name:repair.act_repair_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Create Invoice"
msgstr "Kreriaj Fakturu"

#. module: repair
#: model:ir.model,name:repair.model_repair_order_make_invoice
msgid "Create Mass Invoice (repair)"
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid "Create a new reparation order"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Create invoices"
msgstr "Kreiraj Fakturu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__create_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__create_date
#: model:ir.model.fields,field_description:repair.field_repair_fee__create_date
#: model:ir.model.fields,field_description:repair.field_repair_line__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order__create_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__create_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_id
msgid "Customer"
msgstr "Kupac"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__default_address_id
msgid "Default Address"
msgstr "Zadana adresa"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__address_id
msgid "Delivery Address"
msgstr "Adresa dostave"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__name
#: model:ir.model.fields,field_description:repair.field_repair_line__name
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Description"
msgstr "Opis"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_dest_id
msgid "Dest. Location"
msgstr "Odredišna lokacija"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__display_name
#: model:ir.model.fields,field_description:repair.field_repair_fee__display_name
#: model:ir.model.fields,field_description:repair.field_repair_line__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order__display_name
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__display_name
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_make_invoice
msgid "Do you really want to create the invoice(s)?"
msgstr "Želite li zaista želite kreirati fakturu(e)?"

#. module: repair
#: selection:repair.line,state:0
msgid "Done"
msgstr "Gotovo"

#. module: repair
#: selection:repair.line,state:0
msgid "Draft"
msgstr "U pripremi"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "End Repair"
msgstr "Završi popravak"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Extra Info"
msgstr "Dodatne informacije"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Fees"
msgstr "Nadoknada"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_channel_ids
msgid "Followers (Channels)"
msgstr "Pratioci (Kanali)"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__group
msgid "Group by partner invoice address"
msgstr "Grupiraj po partnerovoj adresi fakture"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "History"
msgstr "Istorija"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__id
#: model:ir.model.fields,field_description:repair.field_repair_fee__id
#: model:ir.model.fields,field_description:repair.field_repair_line__id
#: model:ir.model.fields,field_description:repair.field_repair_order__id
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__id
msgid "ID"
msgstr "ID"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread
msgid "If checked new messages require your attention."
msgstr "Ako je označeno nove poruke će zahtjevati vašu pažnju."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"In a repair order, you can detail the components you remove,\n"
"                add or replace and record the time you spent on the different\n"
"                operations."
msgstr ""
"U nalogu popravke, možete navesti komponente koje ste uklonili,\n"
"dodati ili zamjeniti i zabilježiti vrijeme koje ste utrošili na različitim\n"
"operacijama."

#. module: repair
#: code:addons/repair/models/repair.py:210
#, python-format
msgid "Insufficient Quantity"
msgstr "Nedovoljna količina"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__internal_notes
msgid "Internal Notes"
msgstr "Interne zabilješke"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__move_id
msgid "Inventory Move"
msgstr "Interno kretanje"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Invoice"
msgstr "Faktura"

#. module: repair
#: selection:repair.order,state:0
msgid "Invoice Exception"
msgstr "Fakture izuzeci"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoice_line_id
#: model:ir.model.fields,field_description:repair.field_repair_line__invoice_line_id
msgid "Invoice Line"
msgstr "Stavka fakture"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__invoice_method
msgid "Invoice Method"
msgstr "Metoda fakturisanja"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice address:"
msgstr "Adresa fakture:"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Invoice and shipping address:"
msgstr "Adresa fakture i isporuke:"

#. module: repair
#: code:addons/repair/models/repair.py:385
#, python-format
msgid "Invoice created"
msgstr "Faktura kreirana"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_line__invoiced
#: model:ir.model.fields,field_description:repair.field_repair_order__invoiced
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Invoiced"
msgstr "Fakturisano"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__partner_invoice_id
msgid "Invoicing Address"
msgstr "Adresa za fakturu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel____last_update
#: model:ir.model.fields,field_description:repair.field_repair_fee____last_update
#: model:ir.model.fields,field_description:repair.field_repair_line____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order____last_update
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice____last_update
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_line__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order__write_uid
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_uid
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_cancel__write_date
#: model:ir.model.fields,field_description:repair.field_repair_fee__write_date
#: model:ir.model.fields,field_description:repair.field_repair_line__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order__write_date
#: model:ir.model.fields,field_description:repair.field_repair_order_make_invoice__write_date
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__location_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__location_id
msgid "Location"
msgstr "Lokacija"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__lot_id
#: model:ir.model.fields,field_description:repair.field_repair_order__lot_id
msgid "Lot/Serial"
msgstr "Lot/Serijski"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Glavna zakačka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__move_id
msgid "Move"
msgstr "Kretanje"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__move_id
msgid "Move created by the repair order"
msgstr "Kretanje kreirano od strane naloga popravke"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "My Activities"
msgstr "Moje aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: repair
#: selection:repair.order,invoice_method:0
msgid "No Invoice"
msgstr "Nema fakture"

#. module: repair
#: code:addons/repair/models/repair.py:308
#, python-format
msgid "No account defined for partner \"%s\"."
msgstr "Nema konta definisanog za partnera \"%s\"."

#. module: repair
#: code:addons/repair/models/repair.py:334
#: code:addons/repair/models/repair.py:362
#, python-format
msgid "No account defined for product \"%s\"."
msgstr "Nije definisan konto za proizvod \"%s\"."

#. module: repair
#: code:addons/repair/models/repair.py:598
#: code:addons/repair/models/repair.py:658
#, python-format
msgid "No pricelist found."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:355
#, python-format
msgid "No product defined on fees."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:605
#: code:addons/repair/models/repair.py:665
#, python-format
msgid "No valid pricelist line found."
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Notes"
msgstr "Zabilješke"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_has_error_counter
msgid "Number of error"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Broj nepročitanih poruka"

#. module: repair
#: code:addons/repair/models/repair.py:232
#, python-format
msgid "Only draft repairs can be confirmed."
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__fees_lines
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Operations"
msgstr "Operacije"

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Overdue"
msgstr "Dospjele"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Partner"
msgstr "Partner"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__operations
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Parts"
msgstr "Dijelovi"

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Planned"
msgstr "Planiran"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Price"
msgstr "Cijena"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__pricelist_id
msgid "Pricelist"
msgstr "Cijenovnik"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__pricelist_id
msgid "Pricelist of the selected partner."
msgstr "Cjenovnik odabranog partnera"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Print Quotation"
msgstr "Štampaj predračun"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_id
#: model:ir.model.fields,field_description:repair.field_repair_line__product_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__product_id
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Product"
msgstr "Proizvod"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_qty
msgid "Product Quantity"
msgstr "Količina proizvoda"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom
#: model:ir.model.fields,field_description:repair.field_repair_order__product_uom
msgid "Product Unit of Measure"
msgstr "Jedinica mjere proizvoda"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__product_id
msgid "Product to Repair"
msgstr "Proizvodi za popravak"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__lot_id
msgid "Products repaired are all belonging to this lot"
msgstr "Proizvodi koji su popravljeni svi pripadaju ovom lotu"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__quant_ids
msgid "Quant"
msgstr ""

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__product_uom_qty
#: model:ir.model.fields,field_description:repair.field_repair_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Quantity"
msgstr "Količina"

#. module: repair
#: selection:repair.order,state:0
msgid "Quotation"
msgstr "Predračun"

#. module: repair
#: model:ir.actions.report,name:repair.action_report_repair_order
msgid "Quotation / Order"
msgstr "Ponuda / Nalog"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__quotation_notes
msgid "Quotation Notes"
msgstr "Zabilješke predračuna"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Quotations"
msgstr "Predračuni"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Ready To Repair"
msgstr "Spremno za popravak"

#. module: repair
#: selection:repair.order,state:0
msgid "Ready to Repair"
msgstr "Spremno za popravak"

#. module: repair
#: selection:repair.line,type:0
msgid "Remove"
msgstr "Ukloni"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_stock_move__repair_id
#: model:ir.model.fields,field_description:repair.field_stock_warn_insufficient_qty_repair__repair_id
msgid "Repair"
msgstr "Popravi"

#. module: repair
#: model:ir.model,name:repair.model_repair_fee
msgid "Repair Fees"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Line"
msgstr "Stavka popravke"

#. module: repair
#: model:ir.model,name:repair.model_repair_line
msgid "Repair Line (parts)"
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_repair_order
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Repair Order"
msgstr "Nalog za popravak"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Order #:"
msgstr "Nalog popravke #:"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__repair_id
#: model:ir.model.fields,field_description:repair.field_repair_line__repair_id
msgid "Repair Order Reference"
msgstr "Nalog za popravak br."

#. module: repair
#: model:ir.actions.act_window,name:repair.action_repair_order_tree
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Repair Orders"
msgstr "Nalozi za popravak"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Repair Quotation #:"
msgstr "Ponuda popravke #:"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__name
msgid "Repair Reference"
msgstr "Popravak br."

#. module: repair
#: code:addons/repair/models/repair.py:196
#, python-format
msgid "Repair must be canceled in order to reset it to draft."
msgstr "Popravke moraju biti otkazane kako bi ih postavili u pripremu."

#. module: repair
#: code:addons/repair/models/repair.py:404
#, python-format
msgid "Repair must be confirmed before starting reparation."
msgstr "Popravke moraju biti potvđene kako bi ste započeli popravku."

#. module: repair
#: code:addons/repair/models/repair.py:432
#, python-format
msgid "Repair must be repaired in order to make the product moves."
msgstr ""
"Popravke moraju biti popravljene kako bi ste kreirali kretanja proizvoda."

#. module: repair
#: code:addons/repair/models/repair.py:415
#, python-format
msgid "Repair must be under repair in order to end reparation."
msgstr "Popravke moraju biti pod popravkom kako bi ste završili popravku."

#. module: repair
#: code:addons/repair/wizard/repair_cancel.py:20
#, python-format
msgid "Repair order is not invoiced."
msgstr "Nalog popravke nije fakturisan."

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__repaired
#: selection:repair.order,state:0
msgid "Repaired"
msgstr "Popravljeno"

#. module: repair
#: model:ir.ui.menu,name:repair.menu_repair_order
msgid "Repairs"
msgstr "Popravke"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_tree
msgid "Repairs order"
msgstr "Nalozi za popravak"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Search Repair Orders"
msgstr "Pretraga naloga popravke"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__invoice_method
msgid ""
"Selecting 'Before Repair' or 'After Repair' will allow you to generate "
"invoice before or after the repair is done respectively. 'No invoice' means "
"you don't want to generate invoice for this repair order."
msgstr ""
"Odabirom 'Prije popravke' ili 'Nakon popravke' će vam dozvoliti da kreirate "
"fakturu prije ili nakon završene popravke. 'Baz fakture' znači da ne želite "
"da generišete fakutu za ovaj nalog popravke."

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Send Quotation"
msgstr "Pošalji predračun"

#. module: repair
#: code:addons/repair/models/repair.py:545
#, python-format
msgid "Serial number is required for operation line with product '%s'"
msgstr "Serijski broj je potreban za stavke operacije sa proizvodom '%s'"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Set to Draft"
msgstr "Postavi u pripremu"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__location_id
msgid "Source Location"
msgstr "Izvorna lokacija"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Start Repair"
msgstr "Pokreni popravak"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__state
#: model:ir.model.fields,field_description:repair.field_repair_order__state
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Status"
msgstr "Status"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: repair
#: model:ir.model,name:repair.model_stock_move
msgid "Stock Move"
msgstr "Kretanje zalihe"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_subtotal
#: model:ir.model.fields,field_description:repair.field_repair_line__price_subtotal
msgid "Subtotal"
msgstr "Podukupno"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Tax"
msgstr "Porez"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_line__tax_id
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_tax
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Taxes"
msgstr "Porezi"

#. module: repair
#: sql_constraint:repair.order:0
msgid "The name of the Repair Order must be unique!"
msgstr "Naziv naloga popravke mora biti jedinstven!"

#. module: repair
#: code:addons/repair/models/repair.py:172
#, python-format
msgid ""
"The product unit of measure you chose has a different category than the "
"product unit of measure."
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:246
#, python-format
msgid "The repair order is already invoiced."
msgstr ""

#. module: repair
#: model_terms:ir.actions.act_window,help:repair.action_repair_order_tree
msgid ""
"The repair order uses the warranty date on the Serial Number in\n"
"                order to know if whether the repair should be invoiced to the\n"
"                customer or not."
msgstr ""
"Nalozi popravke koriste datuma garancije na serijskim brojevima \n"
"kako bi znali da li popravka treba da se fakturiše kupcu ili ne."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_line__state
msgid ""
"The status of a repair line is set automatically to the one of the linked "
"repair order."
msgstr ""
"Status stavke popravke je automatski postavljen na jednu od povezanih naloga"
" popravke."

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__location_id
msgid "This is the location where the product to repair is located."
msgstr ""

#. module: repair
#: selection:repair.order,state:0
msgid "To be Invoiced"
msgstr "Za fakturisanje"

#. module: repair
#: selection:repair.order,activity_state:0
msgid "Today"
msgstr "Danas"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_total
msgid "Total"
msgstr "Ukupno"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Total amount"
msgstr "Ukupni iznos"

#. module: repair
#: model:ir.model,name:repair.model_stock_traceability_report
msgid "Traceability Report"
msgstr "Izvještaj praćenja"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__tracking
msgid "Tracking"
msgstr "Praćenje"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_line__type
msgid "Type"
msgstr "Tip"

#. module: repair
#: selection:repair.order,state:0
msgid "Under Repair"
msgstr "U popravku"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_fee__price_unit
#: model:ir.model.fields,field_description:repair.field_repair_line__price_unit
#: model_terms:ir.ui.view,arch_db:repair.report_repairorder
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread
msgid "Unread Messages"
msgstr "Nepročitane poruke"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Brojač nepročitanih poruka"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Neoporezivi iznos"

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form
msgid "Untaxed amount"
msgstr "Neoporezovan iznos"

#. module: repair
#: model:ir.model,name:repair.model_stock_warn_insufficient_qty_repair
msgid "Warn Insufficient Repair Quantity"
msgstr ""

#. module: repair
#: code:addons/repair/models/repair.py:172
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__guarantee_limit
#: model_terms:ir.ui.view,arch_db:repair.view_repair_order_form_filter
msgid "Warranty Expiration"
msgstr "Isticanje garancije"

#. module: repair
#: model:ir.model.fields,field_description:repair.field_repair_order__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: repair
#: model:ir.model.fields,help:repair.field_repair_order__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: repair
#: model_terms:ir.ui.view,arch_db:repair.view_cancel_repair
msgid "Yes"
msgstr "Da"

#. module: repair
#: code:addons/repair/models/repair.py:600
#: code:addons/repair/models/repair.py:660
#, python-format
msgid ""
"You have to select a pricelist in the Repair form !\n"
" Please set one before choosing a product."
msgstr ""
"Morate da odaberete cijenovnik na formi Popravke!\n"
"Molimo odaberite jedan prije odabira proizvoda."

#. module: repair
#: code:addons/repair/models/repair.py:296
#, python-format
msgid "You have to select an invoice address in the repair form."
msgstr ""
