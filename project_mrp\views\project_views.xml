<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="project_project_form_view_inherited" model="ir.ui.view">
        <field name="name">project.project.view.inherited</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.edit_project" />
        <field eval="14" name="priority"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_analytic_account_entries']" position="after">
                <button class="oe_stat_button" type="object" name="action_view_mrp_production"
                    icon="fa-wrench" attrs="{'invisible': [('production_count', '=', 0)]}"
                    groups="mrp.group_mrp_user">
                    <field string="Manufacturing Orders" name="production_count" widget="statinfo"/>
                </button>
                <button class="oe_stat_button" type="object" name="action_view_workorder"
                    icon="fa-cog" attrs="{'invisible': [('workorder_count', '=', 0)]}"
                    groups="mrp.group_mrp_user">
                    <field string="Work Orders" name="workorder_count" widget="statinfo"/>
                </button>
                <button class="oe_stat_button" type="object" name="action_view_mrp_bom"
                    icon="fa-flask" attrs="{'invisible': [('bom_count', '=', 0)]}"
                    groups="mrp.group_mrp_user">
                    <field string="Bills of Materials" name="bom_count" widget="statinfo"/>
                </button>
            </xpath>
        </field>
    </record>
</odoo>
