# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Tutte"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External "
"link\" title=\"External link\"/>"
msgstr ""
"<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"Collegamento "
"esterno\" title=\"Collegamento esterno\"/>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "Attivo"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Tutte le nazioni"

#. module: website_customer
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "Tutti i settori"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "In archivio"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Classe Bootstrap per personalizzare il colore"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "Può pubblicare"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "Nome categoria"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "Classe"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "Chiudi"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "Crea una nuova etichetta contatto"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "Implemented By"
msgstr "Implementato da"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "È pubblicata"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag____last_update
msgid "Last Modified on"
msgstr "Ultima modifica il"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""
"Gestisci le etichette di contatto per una migliore classificazione ai fini "
"di monitoraggio e analisi."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No result found"
msgstr "Nessun risultato trovato"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our References"
msgstr "Le nostre referenze"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Etichetta partner"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"Etichette partner - Queste etichette possono essere usate nel sito web per "
"trovare clienti per settore, o ..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "Partner"

#. module: website_customer
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "Referenze"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "References by Country"
msgstr "Referenze per nazione"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "References by Industries"
msgstr "Referenze per settore"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "References by Tag"
msgstr "Referenze per etichetta"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Ricerca"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "Ricerca etichetta partner"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "URL completo per accedere al documento dal sito web."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Trusted by millions worldwide"
msgstr "Scelto da milioni di persone in tutto il mondo"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "Visibile nel sito web corrente"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "Sito web"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Etichette sito web"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "URL sito web"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "Etichette sito web"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Mappa del mondo"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "reference(s))"
msgstr "referenza/e)"
