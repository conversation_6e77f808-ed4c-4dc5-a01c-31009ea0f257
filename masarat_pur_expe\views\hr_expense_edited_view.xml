<?xml version="1.0"?>
<odoo>

    <record id="hr_expense_view_form_x1" model="ir.ui.view">
        <field name="name">hr.expense.view.form.x1</field>
        <field name="model">hr.expense</field>
        <field name="inherit_id" ref="hr_expense.hr_expense_view_form"/>
        <field name="arch" type="xml">
<!--            <xpath expr="//button[@name='action_submit_expenses']" position="before">-->
<!--                <button name="confirm_price" attrs="{'invisible':[('price_set','=',True)]}" string="Confirm Price" type="object" groups="masarat_pur_expe.group_expense_masarat_type1,masarat_pur_expe.group_expense_masarat_type2" class="oe_highlight"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='product_id']" position="before">-->
<!--                <field name="product_categury_masarat" required="1"/>-->
<!--            </xpath>-->
            <xpath expr="//field[@name='company_id']" position="before">
                <field name="requirement_nature"/>
            </xpath>
<!--            <xpath expr="//field[@name='employee_id']" position="after">-->
<!--                <field name="department_id" options="{'no_open':True}"/>-->
<!--            </xpath>-->
<!--            <xpath expr="//field[@name='unit_amount']" position="replace">-->
<!--                <field name="price_editable" invisible="1"/>-->
<!--                <field name="price_set" invisible="1"/>-->
<!--                <field name="unit_amount" attrs="{'readonly':['|',('price_set','=',True),('price_editable','!=',True)]}" required="0" widget="monetary" options="{'currency_field': 'currency_id', 'field_digits': True}"/>-->
<!--            </xpath>-->

        </field>
    </record>

    <menuitem id="hr_expense.menu_hr_expense_my_expenses_all" sequence="2" parent="hr_expense.menu_hr_expense_my_expenses" action="hr_expense.hr_expense_actions_my_all" name="All My Expenses / طلب شراء"/>

</odoo>

