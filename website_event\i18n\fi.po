# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Ossi <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-27 13:05+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# Ilmoittautumiset"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. Näytetään tulokset  '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(viite:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "(only"
msgstr "(vain"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid ", .oe_country_events, .s_speaker_bio"
msgstr ", .oe_country_events, .s_speaker_bio"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "<b>Vedä ja pudota</b> tämä pala tapahtuman otsikon alapuolelle."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>Loppuu</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>Alkaa</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<b>View all</b>"
msgstr "<b>Näytä kaikki</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""
"<em>Kirjoita tähän lainaus yhdeltä osallistujistasi. Se antaa luottamusta "
"tapahtumiin.</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
msgid ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">Introduction</font>"
msgstr ""
"<font style=\"font-size: 62px;\" "
"class=\"o_default_snippet_text\">Esittely</font>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<i class=\"fa fa-ban mr-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban mr-2\"/>Loppuunmyyty"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban mr-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban mr-2\"/>Julkaisematon"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check mr-2\"/>Registered"
msgstr "<i class=\"fa fa-check mr-2\"/>Ilmoittauduttu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "<i class=\"fa fa-flag mr-2\"/>Events:"
msgstr "<i class=\"fa fa-flag mr-2\"/>Tapahtumat:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Lisää Google Kalenteriin"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Lisää iCal/Outlookiin"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear mr-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
#: model_terms:ir.ui.view,arch_db:website_event.s_country_events
msgid "<i class=\"fa fa-globe mr-2\"/>Upcoming Events"
msgstr "<i class=\"fa fa-globe mr-2\"/>Tulevat tapahtumat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>All Events</span>"
msgstr ""
"<i class=\"fa fa-long-arrow-left text-primary mr-2\"/>\n"
"                            <span>Kaikki tapahtumat</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge badge-secondary text-uppercase "
"o_wevent_badge\">Puhuja</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "<span class=\"navbar-brand h4 my-0 mr-auto\">Events</span>"
msgstr "<span class=\"navbar-brand h4 my-0 mr-auto\">Tapahtumat</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">days</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_days pr-1\">0</span><span "
"class=\"o_countdown_metric pr-1\">päivää</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"py-2 o_wevent_registration_title text-left\">Tickets</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "<span class=\"text-dark font-weight-bold align-middle px-2\">Qty</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>Sold Out\n"
"                                </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                    <i class=\"fa fa-ban mr-2\"/>Loppuunmyyty\n"
"                                </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Verkkotapahtumat</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid ""
"<span>Tickets</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"
msgstr ""
"<span>Liput</span>\n"
"                        <span class=\"btn p-0 close d-none\">×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr ""
"<strong> Tilasit enemmän lippuja kuin saatavilla olevia paikkoja</strong>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "Mennyt tapahtuma"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "Tietoa meistä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Add to Calendar"
msgstr "Lisää kalenteriin"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Kaikki maat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "Kaikki maat"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr "Näytä ja hallitse tapahtumien menuja verkkosivulla."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Apply"
msgstr "Käytä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Ask questions to attendees when registering online"
msgstr "Esitä kysymyksiä osallistujille verkossa ilmoittautumisen yhteydessä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"Vain 13-vuotias Mattti Meikäläinen alkoi jo kehittää ensimmäisiä "
"liiketoimintasovelluksiaan asiakkaille. Maa- ja vesirakentamisen maisterin "
"tutkinnon jälkeen hän perusti TinyERP:n. Tämä oli OpenERP:n ensimmäinen "
"vaihe, josta myöhemmin tuli Odoo, maailmanlaajuisesti asennetuin avoimen "
"lähdekoodin yritysohjelmisto."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "Osallistujat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "Tekijä"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "Voi julkaista"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Sulje"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "Yhteisö"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "Yhteisövalikko"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "Yritys"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Continue"
msgstr "Jatka"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "Etusivun ominaisuudet"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create"
msgstr "Luo"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Create \"%s\""
msgstr "Luo \"%s\""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "Luotu"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Custom Range"
msgstr "Mukautettu aikaväli"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "Päivämäärä ja aika"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Päiväys (uudesta vanhaan)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Päivämäärä (vanhasta uuteen)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "Kuvaus"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "Discard"
msgstr "Hylkää"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "Näytä oma valikko verkkosivustolla"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "Näytä yhteisön välilehti verkkosivustolla"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "Älä unohda painaa <b>tallenna</b>, kun olet valmis."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Email"
msgstr "Sähköposti"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "Päättyy -"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "Tapahtuma"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "Tapahtuman yhteisövalikot"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "Tapahtuman päivä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Tapahtuman tila"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "Tapahtumavalikko"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Event Name"
msgstr "Tapahtuman nimi"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "Tapahtuman ilmoittautuminen"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "Tapahtuman ilmoittautumiset"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Specific"
msgstr "Tapahtumakohtainen"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/customize_options.xml:0
#, python-format
msgid "Event Sub-menu"
msgstr "Tapahtuman alivalikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Subtitle"
msgstr "Tapahtuman alaotsikko"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Tapahtuman tunnisteen kategoria"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "Tapahtuman mallipohja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_details
msgid "Event Title"
msgstr "Tapahtuman otsikko"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Tapahtumaa ei löytynyt!"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Tapahtuma julkaistu"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Tapahtuman julkaisu lopetettu"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#, python-format
msgid "Events"
msgstr "Tapahtumat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Expired"
msgstr "Vanhentunut"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "Tapahtuman rekisteröintinappi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr ""
"Selvitä, mitä ihmiset näkevät ja sanovat tästä tapahtumasta, ja liity "
"keskusteluun."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "Follow Us"
msgstr "Seuraa meitä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "Seuraava sisältö näkyy kaikissa tapahtumissa."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Get the direction"
msgstr "Hanki reittiohjeet"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "Tunniste (ID)"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#, python-format
msgid "Introduction"
msgstr "Esittely"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "Esittelyvalikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "Esittelyvalikot"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "On valmis"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "On meneillään"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "Ilmoittauduttu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "On julkaistu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "Matti Meikäläinen"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu____last_update
msgid "Last Modified on"
msgstr "Viimeksi muokattu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "Sijainti"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "Sijaintivalikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "Sijaintivalikot"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr ""
"Näyttää hyvältä!<b>Julkaistaan</b> nyt tämä sivu, jotta se<b>näkyy</b> "
"verkkosivullasi!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_search
msgid "Main Contact"
msgstr "Pääyhteystieto"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_visitor__parent_id
msgid "Main identity"
msgstr "Pääidentiteetti"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "Valikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Valikkotyyppi"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "Valikot"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "N/A"
msgstr "Ei saatavilla"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Name"
msgstr "Nimi"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.editor.js:0
#, python-format
msgid "New Event"
msgstr "Uusi tapahtuma"

#. module: website_event
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "Seuraavat tapahtumat"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "Verkkosivun menukohteita ei ole vielä!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events found."
msgstr "Tapahtumia ei löytynyt."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "Tähän kävijään ei ole liitetty ilmoittautumista"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "Ei tuloksia. Kokeile jotain muuta hakusanaa. '"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "On Site"
msgstr "Paikan päällä"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Online"
msgstr "Verkossa"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Järjestäjä"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "Koulutuksemme"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__parent_id
msgid "Parent"
msgstr "Ylätaso"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "Menneet tapahtumat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Phone <small>(Optional)</small>"
msgstr "Puhelin <small>(Valinnainen)</small>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Photos"
msgstr "Kuvat"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Please fill in this field"
msgstr "Täytä tämä kenttä"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Please select at least one ticket."
msgstr "Valitse ainakin yksi lippu."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.res_config_settings_view_form
msgid "Questions"
msgstr "Kysymykset"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "Viite:"

#. module: website_event
#. openerp-web
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.layout
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "Ilmoittaudu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "Ilmoittautumispainike"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "Ilmoittautumisvalikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "Ilmoittautumisvalikot"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "Ilmoittaudutut tapahtumat"

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Registration"
msgstr "Ilmoittautuminen"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "Ilmoittautuminen vahvistettu!"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "Ilmoittautumiset"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "Ilmoittautumiset suljettu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "Rekisteröitymiset ovat <b>suljettu</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "Ilmoittautumiset eivät ole vielä avoinna"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "Jäljellä ennen alkua"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "Jäljellä oleva aika ennen tapahtuman alkua (minuutteina)"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
msgid "Restrict publishing to this website."
msgstr "Rajoita julkaisu tälle verkkosivustolle."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Paluu tapahtumalistaukseen."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "Hakukoneoptimoitu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "SHARE"
msgstr "JAA"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales end on"
msgstr "Myynnit loppuvat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sales start on"
msgstr "Myynnit alkavat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "Etsi tapahtumaa..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "See all events from"
msgstr "Näytä kaikki tapahtumat"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Select Venue"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "SEO-nimi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "Näytä verkkosivulla"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Loppuunmyyty"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Valitettavasti tämä tapahtuma ei ole enää saatavilla."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "Alkaa -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "Alkaa tänään"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Start → End"
msgstr "Alku → Loppu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "Alkaa <span/>"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumentin URL-osoite verkkosivustolla."

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "Tässä kuussa"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "Tämä pikanäppäin tuo sinut takaisin tapahtumalomakkeeseen."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr ""
"Tekninen valikko, joka näyttää kaikkien tapahtumien alivalikkojen kohteet."

#. module: website_event
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "Tähän tapahtumaan ei ole enää saatavilla lippuja."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "Lippu #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "Lipunmyynnit alkavat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "Tämän tapahtuman liput ovat <b>Loppuunmyyty</b>"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "Tänään"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Toggle navigation"
msgstr "Kytke navigaatio päälle/pois"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.country_events_list
msgid "Unpublished"
msgstr "Julkaisematon"

#. module: website_event
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "Tulevat tapahtumat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "Use the top button '<b>+ New</b>' to create an event."
msgstr "Käytä yläpainiketta '<b>+ Uusi</b>' luodaksesi tapahtuman."

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr ""
"Käytä tätä <b>pikanäppäintä</b> päästäksesi helposti tapahtuman "
"verkkosivulle."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragrah to write a short text about your events or company."
msgstr ""
"Käytä tätä kappaletta kirjoittaaksesi lyhyen tekstin tapahtumistasi tai "
"yrityksestäsi."

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "Käytetään, kun kohteena ei ole URL-pohjainen menu"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/xml/event_create.xml:0
#, python-format
msgid "Venue"
msgstr "Tapahtumapaikka"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "Näytä"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "Näkyy nykysellä verkkosivulla"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "Vierailija"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Want to change your event configuration? Let's go back to the event form."
msgstr ""
"Haluatko muuttaa tapahtuman konfiguraatiota? Palaa takaisin "
"tapahtumalomakkeeseen."

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
msgid "Website"
msgstr "Verkkosivu"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "Verkkosivun tapahtumavalikko"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "Verkkosivun tapahtumavalikot"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Etusivu"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "Verkkosivuston valikko"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "Verkkosivun valikot"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "Verkkosivun alivalikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "Verkkosivuston osoite"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "Verkkosivun vierailija"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "Verkkosivuston metakuvaus"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "Verkkosivuston meta-avainsanat"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "Verkkosivuston metaotsikko"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "Verkkosivun opengraph kuva"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Onko tapahtuma jo alkanut"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_done
msgid "Whether event is finished"
msgstr "Onko tapahtuma jo päättynyt"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "Joko tapahtuma alkaa tänään, jos se ei vielä ole käynnissä"

#. module: website_event
#. openerp-web
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr ""
"Muokkaa-painikkeella voit <b>muokata</b> verkkosivuja, jotka vierailijat "
"näkevät ilmoittautuessaan."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "available)"
msgstr "saatavilla)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
