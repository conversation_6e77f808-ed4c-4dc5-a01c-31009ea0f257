<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="website_blog" inherit_id="website.snippets" name="Snippet Blog">
    <xpath expr="//t[@id='blog_posts_hook']" position="replace">
        <t t-snippet="website_blog.s_blog_posts" t-thumbnail="/website_blog/static/src/img/s_blog_posts.svg"/>
    </xpath>
</template>

<template id="snippet_options" inherit_id="website.snippet_options" name="Blog snippet options">
    <xpath expr="." position="inside">
        <div data-js="BlogPostTagSelection" data-selector=".o_wblog_post_page_cover" data-target="#o_wblog_post_name">
            <we-many2many string="Tags"
                data-no-preview="true"
                data-model="blog.post"
                data-m2o-field="tag_ids"
                data-set-tags=""
                data-create-method="createTag"/>
        </div>
    </xpath>
    <xpath expr="//*[@data-js='anchor']" position="attributes">
        <attribute name="data-exclude" add=".o_wblog_post_content_field > :not(div, section)" separator=","/>
    </xpath>

    <!-- Hides ContainerWidth option for content in blog posts -->
    <xpath expr="//div[@data-js='ContainerWidth']" position="attributes">
        <attribute name="data-exclude" add="#o_wblog_post_content *" separator=","/>
    </xpath>
</template>

<template id="blog_searchbar_input_snippet_options" inherit_id="website.searchbar_input_snippet_options" name="blog search bar snippet options">
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='scope_opt']" position="inside">
        <we-button data-set-search-type="blogs" data-select-data-attribute="blogs" data-name="search_blogs_opt" data-form-action="/blog">Blogs</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/we-select[@data-name='order_opt']" position="inside">
        <we-button data-set-order-by="published_date asc" data-select-data-attribute="published_date asc" data-dependencies="search_blogs_opt" data-name="order_published_date_asc_opt">Date (old to new)</we-button>
        <we-button data-set-order-by="published_date desc" data-select-data-attribute="published_date desc" data-dependencies="search_blogs_opt" data-name="order_published_date_desc_opt">Date (new to old)</we-button>
    </xpath>
    <xpath expr="//div[@data-js='SearchBar']/div[@data-dependencies='limit_opt']" position="inside">
        <we-checkbox string="Description" data-dependencies="search_blogs_opt" data-select-data-attribute="true" data-attribute-name="displayDescription"
            data-apply-to=".search-query"/>
        <we-checkbox string="Publication Date" data-dependencies="search_blogs_opt" data-select-data-attribute="true" data-attribute-name="displayDetail"
            data-apply-to=".search-query"/>
    </xpath>
</template>
</odoo>
