# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_community
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-06-27 09:50+0000\n"
"PO-Revision-Date: 2022-06-27 09:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hr_payroll_account_community
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "المحاسبة"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "القيد المحاسبي"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#, python-format
msgid "Adjustment Entry"
msgstr "إدخال التعديل"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "الحساب التحليلي"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_contract__analytic_account_id
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_salary_rule__analytic_account_id
msgid "Analytic account"
msgstr "حساب تحليلي"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#, python-format
msgid ""
"As you installed the payroll accounting module you have to choose Debit and "
"Credit account for at least one salary rule in the choosen Salary Structure."
msgstr ""
"أثناء تثبيت وحدة محاسبة كشوف المرتبات ، يجب عليك اختيار المدين و"
"حساب دائن لقاعدة راتب واحدة على الأقل في هيكل الرواتب المختار."

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_line__account_credit
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_salary_rule__account_credit
msgid "Credit account"
msgstr "حساب الائتمان"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "الحساب الدائن"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__date
msgid "Date Account"
msgstr "التاريخ المحاسبي"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "الحساب المدين"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_line__account_debit
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_salary_rule__account_debit
msgid "Debit account"
msgstr "حساب مدين"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "إنشاء قسائم دفع الرواتب لجميع الموظفين الذين تم تحديدهم"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_contract__journal_id
msgid "Journal"
msgstr "مجلة"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "اتركه فارغا لاستخدام تاريخ تأكيد واقفال قسيمة المرتب"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip
msgid "Pay Slip"
msgstr "قسيمة المرتب"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "دفعات المرتب"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_line
msgid "Payslip Line"
msgstr "بنود المرتب"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#, python-format
msgid "Payslip of %s"
msgstr "قسيمة دفع لـ٪ s"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr "يومية الرواتب"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_salary_rule
msgid "Salary Rule"
msgstr "قاعدة مرتبات"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "الضريبة"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_salary_rule__account_tax_id
msgid "Tax account"
msgstr "حساب الضرائب"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "لم تقم مجلة المصروفات \ "٪ s \" بتكوين حساب الائتمان بشكل صحيح!"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account.py:0
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "لم تقم مجلة المصاريف \ "٪ s \" بتكوين حساب الخصم بشكل صحيح!"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip_run__journal_id
msgid "journal"
msgstr "مجلة"