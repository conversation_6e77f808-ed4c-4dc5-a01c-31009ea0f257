# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-08 06:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bs\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:1068
#, python-format
msgid " Add Images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "\" alert with a"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:322
#: code:addons/website/models/website.py:388
#, python-format
msgid "%s (id:%s)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "&amp;times;"
msgstr "&amp;times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.sitemap_index_xml
#: model_terms:ir.ui.view,arch_db:website.sitemap_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"UTF-8\"?&gt;"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.default_xml
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:40
#, python-format
msgid "&nbsp;"
msgstr "&nbsp;"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:55
#, python-format
msgid "&times;"
msgstr "&times;"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "' did not match any pages."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:33
#, python-format
msgid "(could be used in"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
",\n"
"                                updated:"
msgstr ""
",\n"
"                                ažurirano:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", autor:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", updated:"
msgstr ", ažurirano:"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:27
#, python-format
msgid ""
".\n"
"            Changing its name will break these calls."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.default_csv
msgid "1,2,3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1/5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "10s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "12"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "1s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "2 <span class=\"sr-only\">(current)</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2/5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "25%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "2s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3/5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "3s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "4/5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "403: Forbidden"
msgstr "403: Pristup zabranjen"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid "404: Page not found!"
msgstr "404: Stranica nije pronađena!"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5/5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "5s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:15
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:25
#, python-format
msgid ""
"<b>Click on a text</b> to start editing it. <i>It's that easy to edit your "
"content!</i>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories are for everyone even when only written for just one "
"person.</b> If you try to write with a wide general audience in mind, your "
"story will ring false and be bland. No one will be interested. Write for one"
" person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"<b>Great stories have personality.</b> Consider telling a great story that "
"provides personality. Writing a story with personality for potential clients"
" will assists with making a relationship connection. This shows up in small "
"quirks like word choices or phrases. Write from your point of view, not from"
" someone else's experience."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:74
#, python-format
msgid "<b>Install a contact form</b> to improve this page."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:79
#, python-format
msgid ""
"<b>Install new apps</b> to get more features. Let's install the <i>'Contact "
"form'</i> app."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"font-size: 62px; background-color: rgb(255, 255, 255);\">Edit "
"this title</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "<font style=\"font-size: 62px;\"><b>Sell Online.</b> Easily.</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "<font style=\"font-size: 62px;\">A punchy Headline</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "<font style=\"font-size: 62px;\">FAQ</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<font style=\"font-size: 62px;\">Our offers</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "<font style=\"font-size: 62px;\">Slide Title</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_parallax
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "<font style=\"font-size: 62px;\">Your Site Title</font>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"<i class=\"fa fa-1x fa-picture-o mr-2\"/>Add a caption to enhance the "
"meaning of this image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-align-left\"/>Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            Create a Google Project and Get a Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Client ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                                            How to get my Tracking ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows\"/>Background Image Sizing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows\"/>Size"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows-h\"/>Images spacing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows-h\"/>Width"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-arrows-v\"/>Alignment"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clock-o\"/>Scroll Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clock-o\"/>Slideshow speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clock-o\"/>Speed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-clone\"/>Transition"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-columns\"/>Number of columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-diamond\"/> Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eye-slash\"/>Transparent"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Background Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-eyedropper\"/>Filter"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-facebook\"/>Options"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-font\"/>Typography"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-heart\"/>Hearts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-indent\"/> Inner content"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"<i class=\"fa fa-info-circle mr-1\"/> <small>Additional information</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-magic icon-fix\"/> Effect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-magic\"/>Mode"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-magic\"/>Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-magic\"/>Styles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-minus\"/>Thickness"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-paint-brush\"/>Styling"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-pencil\"/>Style"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-picture-o\"/>Background Image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector
msgid ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Add a language..."
msgstr ""
"<i class=\"fa fa-plus-circle\"/>\n"
"                Dodaj jezik..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-plus-circle\"/>Add Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-plus-circle\"/>Add images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-refresh\"/>Re-order"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-square\"/>Squares"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-star\"/>Stars"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-tasks\"/>Progress Bar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-th\"/>Columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<i class=\"fa fa-th-large\"/> Structure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<i class=\"fa fa-th-large\"/> WEBSITE"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-trash\"/>Remove all images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<i class=\"fa fa-trash-o\"/>Remove Slide"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:54
#, python-format
msgid ""
"<p><b>That's it.</b> Your homepage is live.</p><p>Continue adding more pages"
" to your site or edit this page to make it even more awesome.</p>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"            <span class=\"sr-only\">Next</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"            <span class=\"sr-only\">Previous</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-pencil\"/>Edit"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"fa fa-plus\"/>New"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page
msgid ""
"<span class=\"o_add_facebook_page\">\n"
"                <i class=\"fa fa-plus-circle\"/> Add Facebook Page\n"
"            </span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"o_add_images\" style=\"cursor: pointer;\"><i class=\"fa fa-"
"plus-circle\"/> Add Images</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "<span class=\"sr-only\">Toggle Dropdown</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid ""
"<span/>\n"
"                            <span class=\"css_publish\">Unpublished</span>\n"
"                            <span class=\"css_unpublish\">Published</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid ""
"<span>Contact us</span>\n"
"                        <i class=\"fa fa-1x fa-fw fa-arrow-circle-right ml-1\"/>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "<span>Copyright &amp;copy;</span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid "<span>— Jane DOE, CEO of <b>MyCompany</b></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid "<span>— John DOE, CEO of <b>MyCompany</b></span>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "<strong>Error message:</strong>"
msgstr "<strong>Poruka greške:</strong>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"A great way to catch your reader's attention is to tell a story. "
"<br/>Everything you consider writing can be told as a story."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_version
msgid "A/B Testing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "API Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "About us"
msgstr "O nama"

#. module: website
#: code:addons/website/controllers/backend.py:46
#, python-format
msgid "Access Error"
msgstr "Greška pristupa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Action"
msgstr "Akcija"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_redirect__active
msgid "Active"
msgstr "Aktivan"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit you design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:97
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Add Features"
msgstr "Dodaj mogućnosti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:51
#, python-format
msgid "Add Menu Entry"
msgstr "Dodaj meni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Add links to social media on your website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:85
#, python-format
msgid "Add to menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:50
#, python-format
msgid "After having checked how it looks on mobile, <b>close the preview</b>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Alert"
msgstr "Upozorenje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic person in life who can say she loves what she does.\n"
"                                She mentors 100+ in-house developers and looks after the community of over\n"
"                                thousands developers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "An error occured while rendering the template"
msgstr ""

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_google_analytics
msgid "Analytics"
msgstr "Analitika"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Analyze the efficiency of your marketing campaigns by using trackable UTM "
"trackers (campaigns, medium, sources). Create trackers and follow clicks "
"from the Promote menu of your website. Those trackers can be used in Google "
"Analytics or in Odoo reports where you can see the opportunities and sales "
"revenue generated thanks to your links."
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "Aplikacije"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:60
#, python-format
msgid "Are you sure you want to delete this page ?"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "Zakačka"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Autosizing"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
msgid "Available on the Website"
msgstr "Dostupno na website-u"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Back"
msgstr "Natrag"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Background"
msgstr "Pozadina"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:39
#, python-format
msgid "Background colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Badge"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "Početni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr "Velik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Body"
msgstr "Tijelo poruke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bottom"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Boxed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Breadcrumb"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Button"
msgstr "Dugme"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:103
#: code:addons/website/static/src/js/menu/new_content.js:271
#: code:addons/website/static/src/xml/website.xml:27
#: model_terms:ir.ui.view,arch_db:website.500
#, python-format
msgid "Cancel"
msgstr "Otkaži"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Body"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Footer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Card Header"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Catchy Headline"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "Promjeni ikone"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__field_parent
msgid "Child Field"
msgstr "POdređeno polje"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "Podmeniji"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose a pattern"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose an image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose the theme colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your fonts"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Choose your layout"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "Krug"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/customize.js:15
#, python-format
msgid ""
"Click here to choose your main branding color.<br/>It will recompute the "
"palette with suggested matching colors."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:137
#, python-format
msgid "Click to choose more images"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client ID"
msgstr "ID Klijenta"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Client Secret"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Clone this page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:55
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Colors"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Company"
msgstr "Kompanija"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout_footer_copyright
msgid "Company name"
msgstr "Naziv kompanije"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Company team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Components"
msgstr "Komponente"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_social_network
msgid "Configure Social Network"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:44
#, python-format
msgid "Confirmation"
msgstr "Potvrda"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "Povežite se s nama"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Contact"
msgstr "Kontakt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
#: model_terms:ir.ui.view,arch_db:website.404
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Contact Us"
msgstr "Kontaktirajte nas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model:website.menu,name:website.menu_contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Contact us"
msgstr "Kontaktirajte nas"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:430
#: code:addons/website/static/src/xml/website.xml:26
#, python-format
msgid "Continue"
msgstr "Nastavi"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,field_description:website.field_website__country_group_ids
msgid "Country Groups"
msgstr "Grupe zemalja"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:372
#, python-format
msgid "Create Menu"
msgstr "Kreiraj izbornik"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "Kreiraj Stranicu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Create a New Website"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_redirect__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_redirect__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:134
#, python-format
msgid "Custom"
msgstr "Prilagođeno"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Customize Theme"
msgstr "Prilagodi temu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:32
#, python-format
msgid ""
"Customize any block through this menu. Try to change the background of the "
"banner."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:32
#, python-format
msgid "Customize this theme"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/edit.js:81
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_dashboard
msgid "Dashboard"
msgstr "Kontrolna ploča"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dashed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Data"
msgstr "Podaci"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "Uobičajeno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr ""

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Zadani jezik"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
#: model:ir.model.fields,field_description:website.field_website__default_lang_code
msgid "Default language code"
msgstr "Zadani jezički kod"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:60
#: code:addons/website/static/src/js/menu/content.js:937
#, python-format
msgid "Delete Page"
msgstr "Obriši stranicu"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:17
#, python-format
msgid "Delete menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change it's <em>rounded corner</em> style."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Delete this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Demo Logo"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:27
#: code:addons/website/static/src/xml/website.pageProperties.xml:36
#, python-format
msgid "Dependencies"
msgstr "Ovisnosti"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:68
#, python-format
msgid "Description"
msgstr "Opis"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Details"
msgstr "Detalji"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disable autoplay"
msgstr "Onemogući autoreprodukciju"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Disabled"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:47
#: code:addons/website/static/src/js/menu/seo.js:619
#, python-format
msgid "Discard"
msgstr "Odbaci"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "Grupa za raspravu"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_published_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__display_name
#: model:ir.model.fields,field_description:website.field_website_redirect__display_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:16
#, python-format
msgid "Display the badges"
msgstr ""

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:15
#, python-format
msgid "Display the biography"
msgstr ""

#. module: website
#: code:addons/website/models/ir_qweb_fields.py:14
#, python-format
msgid "Display the website description"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Do something"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:110
#, python-format
msgid "Do you want to edit the company data ?"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:28
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "Domen"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:35
#, python-format
msgid "Don't forget to update all links referring to this page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dotted"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Double"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:47
#, python-format
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Prevuci meni desno kako bi kreirali podmeni"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:36
#, python-format
msgid "Drag another block in your page, below the cover."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:20
#, python-format
msgid "Drag the <i>Cover</i> block and drop it in your page."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Dropdown"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:7
#, python-format
msgid "Dropdown menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "Dupliciraj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit"
msgstr "Uredi"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:459
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Edit Menu"
msgstr "Uredi meni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit Top Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Edit code in backend"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Edit in backend"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:16
#, python-format
msgid "Edit menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:32
#, python-format
msgid "Edit my Analytics Client ID"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"Edit the content below this line to adapt the default \"page not found\" "
"page."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:55
#, python-format
msgid "Edit the menu"
msgstr ""

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Email address"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "Email podrška"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Enter email"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Equal height"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Error"
msgstr "Greška"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:22
#, python-format
msgid "Events"
msgstr "Događaji"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "Externi ID"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/widgets/theme.js:74
#: code:addons/website/static/src/js/widgets/theme.js:78
#, python-format
msgid "Extra Color"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Facebook"
msgstr "Facebook"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_facebook
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Facebook nalog"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:557
#, python-format
msgid "Facebook Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:26
#, python-format
msgid "Failed to install \"%s\""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fast"
msgstr "Brzo"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
msgid "Favicon"
msgstr "Favicon"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Features"
msgstr "Karakteristike"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fixed"
msgstr "Fiksno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Float"
msgstr "Decimalni broj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Folded list"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:6
#, python-format
msgid "Follow all the"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Follow your website traffic in Odoo."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Fonts"
msgstr "Fontovi"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Force your user to create an account per website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Form"
msgstr "Obrazac"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind Company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing and the Customer Experience strategies."
msgstr ""

#. module: website
#: selection:website,auth_signup_uninvited:0
msgid "Free sign up"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Full"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "GitHub"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_github
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "GitHub nalog"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:51
#, python-format
msgid "Go To Page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:47
#, python-format
msgid "Go to Link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:106
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Go to Website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:46
#, python-format
msgid ""
"Good Job! You created your first page. Let's check how this page looks like "
"on <b>mobile devices</b>."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:86
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
#, python-format
msgid "Google Analytics"
msgstr "Google Analitika"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "Google Analytics Key"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:51
#, python-format
msgid ""
"Google Analytics initialization failed. Maybe this domain is not whitelisted"
" in your Google Analytics project for this client ID."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_id
#: model:ir.model.fields,field_description:website.field_website__google_management_client_id
msgid "Google Client ID"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_management_client_secret
#: model:ir.model.fields,field_description:website.field_website__google_management_client_secret
msgid "Google Client Secret"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_maps
#: model_terms:ir.ui.view,arch_db:website.company_description
msgid "Google Maps"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_maps_api_key
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Google Plus"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_googleplus
#: model:ir.model.fields,field_description:website.field_website__social_googleplus
msgid "Google+ Account"
msgstr "Google+ nalog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Great products for great people"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "Meža"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_redirect_search
msgid "Group By"
msgstr "Grupiši po"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "Grupe"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:106
#, python-format
msgid "H1"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:107
#, python-format
msgid "H2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "HTML/CSS Editor"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Header"
msgstr "Zaglavlje"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 1"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 3"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 4"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 5"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Headings 6"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:42
#, python-format
msgid "Hide Cover Photo"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:112
#, python-format
msgid "Hide this page from search results"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:56
#, python-format
msgid "Hint: Simply type '#' to create a container menu"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.contentMenu.xml:13
#: model_terms:ir.ui.view,arch_db:website.500
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model:website.menu,name:website.menu_home
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Home"
msgstr "Početna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Home <span class=\"sr-only\">(current)</span>"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:4
#: model:ir.model.fields,field_description:website.field_website__homepage_id
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model_terms:ir.ui.view,arch_db:website.403
#: model_terms:ir.ui.view,arch_db:website.404
#, python-format
msgid "Homepage"
msgstr "Početna stranica"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:63
#, python-format
msgid "How to get my Client ID"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:57
#, python-format
msgid "How to get my Tracking ID"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_published_mixin__id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__id
#: model:ir.model.fields,field_description:website.field_website_redirect__id
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
msgid "ID of the action if defined in a XML file"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "ID pogleda definisan u xml fajlu"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Icon"
msgstr "Znak"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the company logo as the default social share image."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset one or more templates to their <strong>factory "
"settings</strong>."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr ""
"Ako je ovo polje prazno, pogled se odnosi na sve korisnike. U suprotnom, "
"pogled se odnosi samo na korisnike ovih grupa."

#. module: website
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "In main menu"
msgstr ""

#. module: website
#: code:addons/website/controllers/backend.py:53
#, python-format
msgid "Incorrect Client ID / Key"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:110
#: model_terms:ir.ui.view,arch_db:website.index_management
#: model_terms:ir.ui.view,arch_db:website.one_page_line
#, python-format
msgid "Indexed"
msgstr "Indeksirano"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "Nasljeđeni pogled"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Instagram"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_instagram
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:240
#, python-format
msgid "Install"
msgstr "Instalacija"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "Instalacija jezika"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install new language"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "Instalirane aplikacije"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Modules"
msgstr "Instalirani moduli"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:29
#, python-format
msgid "Installing \"%s\""
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:62
#, python-format
msgid "Invalid Facebook Page Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the numbers and\n"
"                                improves them. She is determined to drive success and delivers her professional\n"
"                                acumen to bring Company at the next level."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
msgid "Is published"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:23
#, python-format
msgid "It looks like your file is being called by"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/content/website_root.js:270
#, python-format
msgid ""
"It might be possible to edit the relevant items or fix the issue in <a "
"href=\"%s\">the classic Odoo interface</a>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 1"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Item 2"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "Join us and make your company a better place."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "Ključ"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:92
#: code:addons/website/static/src/xml/website.seo.xml:105
#, python-format
msgid "Keyword"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:87
#, python-format
msgid "Keywords"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
msgid "Languages"
msgstr "Jezici"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Large"
msgstr "Veliko"

#. module: website
#: model:ir.model.fields,field_description:website.field_website____last_update
#: model:ir.model.fields,field_description:website.field_website_menu____last_update
#: model:ir.model.fields,field_description:website.field_website_multi_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_page____last_update
#: model:ir.model.fields,field_description:website.field_website_published_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin____last_update
#: model:ir.model.fields,field_description:website.field_website_redirect____last_update
#: model:ir.model.fields,field_description:website.field_website_seo_metadata____last_update
msgid "Last Modified on"
msgstr "Zadnje mijenjano"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:72
#, python-format
msgid "Last Month"
msgstr "Zadnji mjesec"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_redirect__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_redirect__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:71
#, python-format
msgid "Last Week"
msgstr "Zadnja sedmica"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:73
#, python-format
msgid "Last Year"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "Layout"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Learn more"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "Lijevo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:5
#, python-format
msgid "Let's start designing."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Library"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "Ograničeno prilagođavanje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Line-On-Sides"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link"
msgstr "Veza"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_links
msgid "Link Trackers"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Link button"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:40
#, python-format
msgid "Link my Analytics Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "LinkedIn"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_linkedin
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn nalog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:51
#, python-format
msgid "Loading..."
msgstr "Učitavam..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Local Events"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "Glavni Meni"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:31
#, python-format
msgid "Main colors"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Manage Your Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Manage Your Website Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Manage multiple websites"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Manage this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Masonry"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
#: model_terms:ir.ui.view,arch_db:website.404
msgid "Maybe you were looking for one of these popular pages ?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "Medijum"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Meet the Executive Team"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:328
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Menu"
msgstr "Meni"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:386
#, python-format
msgid "Menu Label"
msgstr "Oznaka menija"

#. module: website
#: code:addons/website/models/website.py:330
#: model:ir.ui.menu,name:website.menu_website_menu_list
#, python-format
msgid "Menus"
msgstr "Meniji"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:26
#, python-format
msgid "Messages"
msgstr "Poruke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as Commercial\n"
"                                Director in the software industry, Mich has helped Company to get where it\n"
"                                is today. Mich is among the best minds."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Middle"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/mobile_view.js:54
#, python-format
msgid "Mobile preview"
msgstr "Mobilni pregled"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "Model"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "Podatci modela"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__model_ids
msgid "Models"
msgstr "Modeli"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:111
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr "Premjesti na početak"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr "Premjesti na kraj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr "Premjesti na sljedeće"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr "Premjesti na prethodno"

#. module: website
#: selection:website.redirect,type:0
msgid "Moved permanently (301)"
msgstr ""

#. module: website
#: selection:website.redirect,type:0
msgid "Moved temporarily (302)"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Multi-Websites"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "My Website"
msgstr "Moj Website"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:44
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Name"
msgstr "Naziv:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Name and favicon of your website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Navbar"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Blog Post"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Channel"
msgstr "Novi kanal"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Event"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Forum"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Job Offer"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:77
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "New Page"
msgstr "Nova stranica"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Product"
msgstr "Novi proizvod"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New Slide"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "Novi prozor"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "New page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "Newsletter"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:29
#: code:addons/website/static/src/xml/website.gallery.xml:35
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Next"
msgstr "Slijedeće"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "Bez prilagođavanja"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "Bez podrške"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No-scroll"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_customize
msgid "None"
msgstr "Ništa"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not SEO optimized"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not in main menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not indexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Not visible"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Note: To hide this page, uncheck it from the top Customize menu."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_count
msgid "Number of languages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Odoo"
msgstr "Odoo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 1 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 2 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Odoo - Sample 3 for three columns"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoo verzija"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Odoo provides essential platform for our project management. Things are "
"better organized and more visible with it."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Odoo • A picture with a caption"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Odoo • Image and Text"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Odoo • Text and Image"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:5
#, python-format
msgid "On Website"
msgstr "Na website-u"

#. module: website
#: selection:website,auth_signup_uninvited:0
msgid "On invitation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Once the selection of available websites by domain is done, you can filter "
"by country group."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "Open Source ERP"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:616
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Optimize SEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Optimize SEO of this page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:31
#, python-format
msgid "Options"
msgstr "Opcije"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "Naruči odmah"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Our Products &amp; Services"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid "Our Team"
msgstr "Naš tim"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                                                    their performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"Our products are designed for small to medium size companies willing to optimize\n"
"                                their performance."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Outline"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:298
#: code:addons/website/models/website.py:362
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#, python-format
msgid "Page"
msgstr "Stranica"

#. module: website
#: code:addons/website/models/website.py:305
#, python-format
msgid "Page <b>%s</b> contains a link to this page"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:369
#, python-format
msgid "Page <b>%s</b> is calling this file"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Page Indexed"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:50
#, python-format
msgid "Page Name"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:68
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Page Properties"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page Published"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:78
#, python-format
msgid "Page Title"
msgstr "Naslov stranice"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:7
#: code:addons/website/static/src/xml/website.pageProperties.xml:57
#: model:ir.model.fields,field_description:website.field_website_page__url
#, python-format
msgid "Page URL"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:300
#: code:addons/website/models/website.py:364
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.user_navbar
#, python-format
msgid "Pages"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pagination"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "Nadređeni meni"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "Podatci vezani za partnera na korisniku"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Pill"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:63
#, python-format
msgid "Please enter valid facebook page URL for preview"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.kanban_contain
msgid "Prev"
msgstr "Prethodno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:57
#: code:addons/website/static/src/xml/website.seo.xml:76
#: code:addons/website/static/src/xml/website.seo.xml:143
#, python-format
msgid "Preview"
msgstr "Pregled"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:23
#: code:addons/website/static/src/xml/website.gallery.xml:34
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:website.page,arch_db:website.bs_debug_page
#, python-format
msgid "Previous"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "Profesionalna"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote"
msgstr "Promocija"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "Promote page on the web"
msgstr "Promoviši stranicu na web-u"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "Javni partner"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "Javni korisnik"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:45
#: code:addons/website/static/src/xml/website.pageProperties.xml:122
#, python-format
msgid "Publish"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:137
#, python-format
msgid "Publish date"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/tours/banner.js:42
#, python-format
msgid "Publish your page by clicking on the <b>Save</b> button."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:37
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Published"
msgstr "Objavljeno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:131
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
#, python-format
msgid "Publishing Date"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "QWeb"
msgstr "QWeb"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "Kvalitet"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_btn
msgid "Read more"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "Pravilo zapisa"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect__url_from
msgid "Redirect From"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:68
#, python-format
msgid "Redirect Old URL"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect__url_to
msgid "Redirect To"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_redirect__type
#: model_terms:ir.ui.view,arch_db:website.view_redirect_search
msgid "Redirection Type"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_redirect_list
msgid "Redirects"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "References"
msgstr "Reference"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
msgid "Registration Website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:111
#, python-format
msgid "Related keywords"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:69
#, python-format
msgid "Rename Page To:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset selected templates"
msgstr "Resetuj odabrane predloške"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Reset templates"
msgstr "Reset predložaka"

#. module: website
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: website
#: model:res.groups,name:website.group_website_publisher
msgid "Restricted Editor"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Review"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "Desno"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded corners"
msgstr "Zaobljeni uglovi"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "SEO metapodatci"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "SEO optimized"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/dashboard.js:93
#: code:addons/website/static/src/js/menu/content.js:46
#: code:addons/website/static/src/js/menu/seo.js:618
#, python-format
msgid "Save"
msgstr "Sačuvaj"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Search"
msgstr "Pretraži"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "Meniji pretrage"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_redirect_search
msgid "Search Redirect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "Pretraži..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "Druga odlika."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:429
#, python-format
msgid "Select a Menu"
msgstr "Odaberi meni"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:125
#, python-format
msgid "Select an image for social share"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove some features."
msgstr "Odaberi i ukloni blokove da uklonite neke odlike."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Select the Website to Configure"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:website.page,arch_db:website.contactus_page
msgid "Send us an email"
msgstr "Pošaljite nam email"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Separated link"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_redirect__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
msgid "Server Action"
msgstr "Serverska akcija"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
msgid "Settings"
msgstr "Postavke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadow"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shadows"
msgstr "Sjenke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Share"
msgstr "Podijeli"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Shipping"
msgstr "Prevoznik"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__customize_show
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "Prikaži kao opcionalno nasljeđeno"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:49
#, python-format
msgid "Show Friend's Faces"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:92
#, python-format
msgid "Show in Top Menu"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "Veličina"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.gallery.xml:17
#, python-format
msgid "Slide image"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideshow"
msgstr "Slideshow"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slow"
msgstr "Sporo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Small"
msgstr "Malo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small Caps"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Social Media"
msgstr "Socijalni Mediji"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Solid"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Something else here"
msgstr ""

#. module: website
#: code:addons/website/controllers/main.py:224
#, python-format
msgid "Sort by Name"
msgstr ""

#. module: website
#: code:addons/website/controllers/main.py:223
#, python-format
msgid "Sort by Url"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__specific_user_account
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr "Kvadrat"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:51
#, python-format
msgid "State colors"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/snippets.options.js:70
#, python-format
msgid "Stay on this page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "TRANSLATE"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:14
#, python-format
msgid "Tabs"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_ir_attachment__key
msgid ""
"Technical field used to resolve multiple attachments in a multi-website "
"environment."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "Tehnički naziv:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:314
#: code:addons/website/models/website.py:381
#, python-format
msgid "Template"
msgstr "Prijedlog"

#. module: website
#: code:addons/website/models/website.py:320
#, python-format
msgid "Template <b>%s (id:%s)</b> contains a link to this page"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:387
#, python-format
msgid "Template <b>%s (id:%s)</b> is calling this file"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "Template fallback"
msgstr ""

#. module: website
#: code:addons/website/models/website.py:316
#: code:addons/website/models/website.py:383
#, python-format
msgid "Templates"
msgstr "Predlošci"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:45
#, python-format
msgid "Text colors"
msgstr ""

#. module: website
#: code:addons/website/controllers/backend.py:54
#, python-format
msgid "The Google Analytics Client ID or Key you entered seems incorrect."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:205
#: code:addons/website/static/src/xml/website.seo.xml:69
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:548
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The error occured while rendering the template"
msgstr "Greška se dogodila prilikom iscrtavanja predloška"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "The following error was raised in the website controller"
msgstr "Sljedeća greška se vratila iz website kontrolera"

#. module: website
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
msgid "The full URL to access the document through the website."
msgstr "Kompletan URL za pristup dokumentu putem website-a."

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
msgid "The full URL to access the server action through the website."
msgstr "Kompletan URL za pristup serverskoj akciji putem website-a."

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/new_content.js:27
#, python-format
msgid "The installation of an App is already in progress."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.403
msgid "The page you were looking for could not be authorized."
msgstr "Stranica koju tražite nije mogla biti autorizovana."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.404
msgid ""
"The page you were looking for could not be found; it is possible you have\n"
"                        typed the address incorrectly, but it has most probably been removed due\n"
"                        to the recent website reorganisation."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "The selected templates will be reset to their factory settings."
msgstr "Odabrani predlošci će biti resetovane na fabričke postavke."

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:63
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website__theme_id
msgid "Theme"
msgstr "Tema"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "There are currently no pages for your website."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:39
#, python-format
msgid "There is no data currently available."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thin"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
#: model:ir.model.fields,help:website.field_website__default_lang_code
msgid "This field is used to set/get locales for user"
msgstr ""
"Ovo polje se koristi za postavljanje/dobavljanje lokalizacije korisnika"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "This is a \""
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component <br/>for "
"calling extra attention to featured content or information."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are administrator of "
"this site."
msgstr ""

#. module: website
#: code:addons/website/models/website.py:333
#, python-format
msgid "This page is in the menu <b>%s</b>"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "This page will be visible on {{ date_formatted }}"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "Sličice"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:18
#, python-format
msgid "Timeline"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:62
#: model_terms:ir.ui.view,arch_db:website.theme_customize
#, python-format
msgid "Title"
msgstr "Naslov"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the column to create a new "
"one as a copy."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Toggle navigation"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Top"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/content.js:424
#, python-format
msgid "Top Menu"
msgstr "Gornji meni"

#. module: website
#: code:addons/website/models/website.py:177
#, python-format
msgid "Top Menu for Website %s"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "Traceback"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track clicks on UTM links"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits in Google Analytics"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Tracking ID"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/editor/rte.summernote.js:17
#, python-format
msgid "Transform the picture (click twice to reset transformation)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transparent"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Twitter"
msgstr "Twitter"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_twitter
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Twitter nalog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:82
#, python-format
msgid "Type"
msgstr "Tip"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:55
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "UA-XXXXXXXX-Y"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underlined"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.index_management
msgid "Unindexed"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "Snaga neograničenog CRMa i podrške"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "Neograničeno prilagođavanje"

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/backend/button.js:37
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.publish_short
#, python-format
msgid "Unpublished"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Url"
msgstr "URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use Google Map on your website ("
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:35
#, python-format
msgid "Use Small Header"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.pageProperties.xml:101
#, python-format
msgid "Use as Homepage"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format.<br/> "
"Don't write about products or services here, write about solutions."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:110
#, python-format
msgid "Used in page content"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:109
#, python-format
msgid "Used in page description"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:106
#, python-format
msgid "Used in page first level heading"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:107
#, python-format
msgid "Used in page second level heading"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.seo.xml:108
#, python-format
msgid "Used in page title"
msgstr ""

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_country_group_ids
#: model:ir.model.fields,help:website.field_website__country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid ""
"User-agent: *\n"
"Sitemap:"
msgstr ""
"User-agent: *\n"
"Mapa sajta:"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "Users"
msgstr "Korisnici"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "Utilities &amp; Typography"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Value"
msgstr "Vrijednost"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Fast"
msgstr "Veoma brzo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Very Slow"
msgstr "Veoma sporo"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_website_page__view_id
msgid "View"
msgstr "Pregled"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "Arhitektura prikaza"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "Naziv pogleda"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "Vrsta pregleda"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "Mod nasljeđivanja pogleda"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.one_page_line
msgid "Visible"
msgstr "Vidljiv"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
msgid "Visible on current website"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:29
#, python-format
msgid "Visits"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.facebook_page.xml:60
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:website.page,arch_db:website.aboutus_page
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                                    life through disruptive products. We build great products to solve your\n"
"                                                    business problems."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's\n"
"                                life through disruptive products. We build great products to solve your\n"
"                                business problems."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:37
#, python-format
msgid "We found these ones:"
msgstr ""

#. module: website
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_redirect__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
msgid "Website"
msgstr "Web stranica"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "Website Domen"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "Website Meni"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
msgid "Website Name"
msgstr "Website Naziv"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Website Pages"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
msgid "Website Path"
msgstr "Website Putanja"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr ""

#. module: website
#: model:ir.model,name:website.model_website_redirect
msgid "Website Redirect"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_redirect_form_view
msgid "Website Redirect Settings"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_redirect_list
#: model_terms:ir.ui.view,arch_db:website.website_redirect_tree_view
msgid "Website Redirects"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr "Website Postavke"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Title"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
msgid "Website Url"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "Website meni"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "Website meta opis"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta ključne riječi"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "Website meta naslov"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
#: model:ir.actions.server,name:website.ir_actions_server_website_google_analytics
msgid "Website: Dashboard"
msgstr ""

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "Website-ovi"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "Websajt-ovi za prevođenje"

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:4
#, python-format
msgid "Welcome to your"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:9
#, python-format
msgid "What do you want to do?"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. <br/>To be "
"successful your content needs to be useful to your readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and background to highlight features."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"You can have 2 websites with same domain AND a condition on country group to"
" select wich website use."
msgstr ""

#. module: website
#: sql_constraint:res.users:0
msgid "You can not have two users with the same login!"
msgstr ""

#. module: website
#: code:addons/website/controllers/backend.py:47
#, python-format
msgid "You do not have sufficient rights to perform that action."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:50
#, python-format
msgid "You do not seem to have access to this Analytics Account."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:417
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:49
#, python-format
msgid "You need to log in to your Google Account before:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "YouTube"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:60
#, python-format
msgid "Your Client ID:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.backend.xml:54
#, python-format
msgid "Your Tracking ID:"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:10
#, python-format
msgid "Your current changes will be saved automatically."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:393
#, python-format
msgid "Your description looks too long."
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/js/menu/seo.js:395
#, python-format
msgid "Your description looks too short."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_pages
msgid "Your search '"
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_youtube
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Youtube nalog"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.http_error_debug
msgid "and evaluating the following expression:"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "breadcrumb"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:71
#, python-format
msgid "e.g. About Us"
msgstr ""

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce Dashboard"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.xml:41
#, python-format
msgid "found(s)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "Odoo instanca, "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_page_ir_ui_view
#: model_terms:website.page,arch_db:website.bs_debug_page
msgid "link"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "name"
msgstr "naziv"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.user_navbar
msgid "or Edit Master"
msgstr "ili Uredi Master"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "page, snippets, ...)"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "px"
msgstr ""

#. module: website
#. openerp-web
#: code:addons/website/static/src/xml/website.editor.xml:6
#, python-format
msgid "signs to get your website ready in no time."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.robots
msgid "sitemap.xml"
msgstr "sitemap.xml"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "url"
msgstr "url"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "website"
msgstr "website"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "with"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "www.odoo.com"
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.500
msgid "yes"
msgstr "da"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "— Jane DOE, CEO of <b>MyCompany</b>"
msgstr ""
