# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_gift_card
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:29+0000\n"
"PO-Revision-Date: 2021-09-14 12:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "<strong>Gift Card Code</strong>"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Amount of the gift card:"
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Barcode"
msgstr "Cod de bare"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regulă cod de bare"

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__generated_gift_card_ids
msgid "Bought Gift Card"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Buy Pos Order Line"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Cancel"
msgstr "Anulează"

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Card expires"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Check a gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmă"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_order_line__gift_card_id
msgid "Deducted from this Gift Card"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Discard"
msgstr "Abandonează"

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__create_set
msgid "Generate a new barcode and set a price"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Generate barcode"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardButton.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: model:ir.actions.report,name:pos_gift_card.gift_card_report_pdf
#: model:ir.model,name:pos_gift_card.model_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__use_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order_line__gift_card_id
#: model:ir.model.fields.selection,name:pos_gift_card.selection__barcode_rule__type__gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_gift_card.res_config_view_form_inherit_pos_coupon
#, python-format
msgid "Gift Card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Gift Card Barcode:"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_order__gift_card_count
msgid "Gift Card Count"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift Card Error"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_product_id
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift Card Product"
msgstr ""

#. module: pos_gift_card
#: model:ir.ui.menu,name:pos_gift_card.pos_gift_card_menu
msgid "Gift Cards"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_pos_config__gift_card_settings
msgid "Gift Cards settings"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card balance is too low."
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Gift card is not valid."
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.pos_gift_card_config_view_form
msgid "Gift card settings"
msgstr ""

#. module: pos_gift_card
#: model_terms:ir.ui.view,arch_db:pos_gift_card.gift_card_template
msgid "Here is your gift card!"
msgstr ""

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configurarea Punctului de Vânzare"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Liniile Punctului de vânzare"

#. module: pos_gift_card
#: model:ir.model,name:pos_gift_card.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comenzile Punctului de vânzare"

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_gift_card__buy_pos_order_line_id
msgid "Pos Order line where this gift card has been bought."
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_gift_card__redeem_pos_order_line_ids
msgid "Pos Redeems"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Remaining amount of the gift card:"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_set
msgid "Scan an existing barcode and set a price"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields.selection,name:pos_gift_card.selection__pos_config__gift_card_settings__scan_use
msgid "Scan an existing barcode with an existing price"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan and set price on gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Scan gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/models.js:0
#, python-format
msgid "This gift card is already applied"
msgstr ""

#. module: pos_gift_card
#: model:ir.model.fields,help:pos_gift_card.field_pos_config__gift_card_product_id
msgid "This product is used as reference on customer receipts."
msgstr "Acest produs este utilizat ca referință la încasările clienților."

#. module: pos_gift_card
#: model:ir.model.fields,field_description:pos_gift_card.field_barcode_rule__type
msgid "Type"
msgstr "Tip"

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/xml/GiftCardPopup.xml:0
#, python-format
msgid "Use a gift card"
msgstr ""

#. module: pos_gift_card
#. openerp-web
#: code:addons/pos_gift_card/static/src/js/GiftCardPopup.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold"
msgstr ""
