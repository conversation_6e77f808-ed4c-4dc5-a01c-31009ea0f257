# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_payment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-09-20 09:53+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Amharic (https://www.transifex.com/odoo/teams/41243/am/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: am\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_payment
#: code:addons/account_payment/models/payment.py:61
#, python-format
msgid "<%s>  transaction (%s) invoice confirmation failed : <%s>"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/models/payment.py:45
#, python-format
msgid "<%s> transaction (%s) failed : <%s>"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/models/payment.py:52
#, python-format
msgid "<%s> transaction (%s) invalid state : %s"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-arrow-circle-right\"/> Pay Now"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"hidden-xs\"> Pay "
"Now</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-check-circle\"/> Paid"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-check-circle\"/> Pending"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_report
msgid ""
"<span class=\"label label-info orders_label_text_align\"><i class=\"fa fa-fw"
" fa-clock-o\"/> Waiting</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_report
msgid ""
"<span class=\"label label-success orders_label_text_align\"><i class=\"fa "
"fa-fw fa-check\"/> Done</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_report
msgid "<strong>Transactions</strong>"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/controllers/payment.py:53
#, python-format
msgid ""
"If we store your payment information on our server, subscription payments "
"will be made automatically."
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_invoice
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction_account_invoice_id
msgid "Invoice"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "Invoice successfully paid."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_invoice_payment_tx_id
msgid "Last Transaction"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_invoice_payment_tx_count
msgid "Number of payment transactions"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/controllers/payment.py:50
#, python-format
msgid "Pay & Confirm"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/models/payment.py:119
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
#, python-format
msgid "Pay Now"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "Pay with"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_invoice_payment_acquirer_id
msgid "Payment Acquirer"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: account_payment
#: code:addons/account_payment/models/account_invoice.py:28
#, python-format
msgid "Payment Transactions"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Status"
msgstr "ሁኔታው"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: impossible to validate invoice."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice state."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: transaction amount issue.<br/>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction issue.<br/>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_invoice_payment_tx_ids
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Transactions"
msgstr ""
