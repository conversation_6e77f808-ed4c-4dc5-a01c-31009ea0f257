# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "Información adicional"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Buy more credits"
msgstr "Comprar más créditos"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/res_company.py:0
#, python-format
msgid "Company auto-completed by Odoo Partner Autocomplete Service"
msgstr ""
"Se autocompletó la empresa con el servicio de autocompletado de contacto de "
"Odoo"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "ID de la base de datos de la empresa"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Opciones de configuración"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Creado el"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr "Enriquecimiento listo"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "IAP Account Token missing"
msgstr "Falta el token de la cuenta de compras dentro de la aplicación"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr "API de autocompletado del contacto de compras dentro de la aplicación"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "Crédito insuficiente"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "Está sincronizado"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "No account token"
msgstr "No hay token de cuenta"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "Not enough credits for Partner Autocomplete"
msgstr "No hay suficientes créditos para autocompletar el contacto"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
#: model:ir.cron,name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr "Autocompletar contacto: sincronización con la base de datos remota"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "Sincronización para autocompletar un contacto"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/res_partner.py:0
#, python-format
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr "Contacto creado por el servicio de autocompletado de contacto de Odoo"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Placeholder"
msgstr "Marcador de posición"

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
#, python-format
msgid "Searching Autocomplete..."
msgstr "Buscar en autocompletar...."

#. module: partner_autocomplete
#. openerp-web
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Set Your Account Token"
msgstr "Configure el token de su cuenta"

#. module: partner_autocomplete
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "Test mode"
msgstr "Modo de prueba"
