# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_vat
# 
# Translators:
# <PERSON>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: <PERSON>, 2018\n"
"Language-Team: Serbian (https://www.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:154
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] does not seem to be valid. \n"
"Note: the expected format is %s"
msgstr ""

#. module: base_vat
#: code:addons/base_vat/models/res_partner.py:153
#, python-format
msgid ""
"The VAT number [%s] for partner [%s] either failed the VIES VAT validation "
"check or did not respect the expected format %s."
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.company_form_vat
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_form
#: model_terms:ir.ui.view,arch_db:base_vat.view_partner_short_form
msgid "VAT"
msgstr "PDV"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr ""

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr ""
