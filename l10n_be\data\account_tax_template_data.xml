<?xml version="1.0" encoding="utf-8"?>
<odoo>

        <record id="attn_VAT-OUT-21-L" model="account.tax.template">
            <field name="sequence">10</field>
            <field name="description">21%</field>
            <field name="name">21%</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_03')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')],
                }),

            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-21-S" model="account.tax.template">
            <field name="sequence">11</field>
            <field name="description">21%</field>
            <field name="name">21% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_03')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-12-S" model="account.tax.template">
            <field name="sequence">20</field>
            <field name="description">12%</field>
            <field name="name">12% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_02')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-12-L" model="account.tax.template">
            <field name="sequence">21</field>
            <field name="description">12%</field>
            <field name="name">12%</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_02')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-06-S" model="account.tax.template">
            <field name="sequence">30</field>
            <field name="description">6%</field>
            <field name="name">6% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_01')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-06-L" model="account.tax.template">
            <field name="sequence">31</field>
            <field name="description">6%</field>
            <field name="name">6%</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_01')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_54')]
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451'),
                    'plus_report_line_ids': [ref('tax_report_line_64')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-S" model="account.tax.template">
            <field name="sequence">40</field>
            <field name="description">0%</field>
            <field name="name">0% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_00')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-L" model="account.tax.template">
            <field name="sequence">41</field>
            <field name="description">0%</field>
            <field name="name">0%</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_00')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-CC" model="account.tax.template">
            <field name="sequence">50</field>
            <field name="description">0%</field>
            <field name="name">0% Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_45')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-EU-S" model="account.tax.template">
            <field name="sequence">60</field>
            <field name="description">0%</field>
            <field name="name">0% EU S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_44')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_48s44')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-EU-L" model="account.tax.template">
            <field name="sequence">61</field>
            <field name="description">0%</field>
            <field name="name">0% EU M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_46L')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_48s46L')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-EU-T" model="account.tax.template">
            <field name="sequence">62</field>
            <field name="description">0%</field>
            <field name="name">0% EU T</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_46T')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_48s46T')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-OUT-00-ROW" model="account.tax.template">
            <field name="sequence">70</field>
            <field name="description">0%</field>
            <field name="name">0% EX</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">sale</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_47')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_49')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-21" model="account.tax.template">
            <field name="sequence">110</field>
            <field name="description">21%</field>
            <field name="name">21% M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-12" model="account.tax.template">
            <field name="sequence">120</field>
            <field name="description">12%</field>
            <field name="name">12% M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-06" model="account.tax.template">
            <field name="sequence">130</field>
            <field name="description">6%</field>
            <field name="name">6% M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-00" model="account.tax.template">
            <field name="sequence">140</field>
            <field name="description">0%</field>
            <field name="name">0% M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

         <record id="attn_TVA-21-inclus-dans-prix" model="account.tax.template">
            <field name="sequence">150</field>
            <field name="description">21%</field>
            <field name="name">21% S.TTC</field>
            <field name="price_include" eval="1"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-S" model="account.tax.template">
            <field name="sequence">210</field>
            <field name="description">21%</field>
            <field name="name">21% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-G" model="account.tax.template">
            <field name="sequence">220</field>
            <field name="description">21%</field>
            <field name="name">21% G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-S" model="account.tax.template">
            <field name="sequence">230</field>
            <field name="description">12%</field>
            <field name="name">12% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-G" model="account.tax.template">
            <field name="sequence">240</field>
            <field name="description">12%</field>
            <field name="name">12% G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-S" model="account.tax.template">
            <field name="sequence">250</field>
            <field name="description">6%</field>
            <field name="name">6% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-G" model="account.tax.template">
            <field name="sequence">260</field>
            <field name="description">6%</field>
            <field name="name">6% G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-S" model="account.tax.template">
            <field name="sequence">270</field>
            <field name="description">0%</field>
            <field name="name">0% S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-G" model="account.tax.template">
            <field name="sequence">280</field>
            <field name="description">0%</field>
            <field name="name">0% G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-21" model="account.tax.template">
            <field name="sequence">310</field>
            <field name="description">21%</field>
            <field name="name">21% IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-12" model="account.tax.template">
            <field name="sequence">320</field>
            <field name="description">12%</field>
            <field name="name">12% IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-06" model="account.tax.template">
            <field name="sequence">330</field>
            <field name="description">6%</field>
            <field name="name">6% IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-00" model="account.tax.template">
            <field name="sequence">340</field>
            <field name="description">0%</field>
            <field name="name">0% IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-21-CC" model="account.tax.template">
            <field name="sequence">410</field>
            <field name="description">21%</field>
            <field name="name">21% M.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-12-CC" model="account.tax.template">
            <field name="sequence">420</field>
            <field name="description">12%</field>
            <field name="name">12% M.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-06-CC" model="account.tax.template">
            <field name="sequence">430</field>
            <field name="description">6%</field>
            <field name="name">6% M.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-00-CC" model="account.tax.template">
            <field name="sequence">440</field>
            <field name="description">0%</field>
            <field name="name">0% M.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-CC" model="account.tax.template">
            <field name="sequence">510</field>
            <field name="description">21%</field>
            <field name="name">21% S.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-CC" model="account.tax.template">
            <field name="sequence">520</field>
            <field name="description">12%</field>
            <field name="name">12% S.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-CC" model="account.tax.template">
            <field name="sequence">530</field>
            <field name="description">6%</field>
            <field name="name">6% S.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-CC" model="account.tax.template">
            <field name="sequence">540</field>
            <field name="description">0%</field>
            <field name="name">0% S.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-21-CC" model="account.tax.template">
            <field name="sequence">610</field>
            <field name="description">21%</field>
            <field name="name">21% IG.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-12-CC" model="account.tax.template">
            <field name="sequence">620</field>
            <field name="description">12%</field>
            <field name="name">12% IG.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-06-CC" model="account.tax.template">
            <field name="sequence">630</field>
            <field name="description">6%</field>
            <field name="name">6% IG.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                    'minus_report_line_ids': [ref('tax_report_line_56')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451056'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-00-CC" model="account.tax.template">
            <field name="sequence">640</field>
            <field name="description">0%</field>
            <field name="name">0% IG.Cocont</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-CAR-EXC" model="account.tax.template">
            <field name="sequence">720</field>
            <field name="description">21%</field>
            <field name="name">21% Car</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 50,
                    'repartition_type': 'tax',
                    'plus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 50,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                }),

                (0,0, {
                    'factor_percent': 50,
                    'repartition_type': 'tax',
                    'minus_report_line_ids': [ref('tax_report_line_82')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 50,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_63')],
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-21-EU" model="account.tax.template">
            <field name="sequence">1110</field>
            <field name="description">21%</field>
            <field name="name">21% EU M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-12-EU" model="account.tax.template">
            <field name="sequence">1120</field>
            <field name="description">12%</field>
            <field name="name">12% EU M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-06-EU" model="account.tax.template">
            <field name="sequence">1130</field>
            <field name="description">6%</field>
            <field name="name">6% EU M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-00-EU" model="account.tax.template">
            <field name="sequence">1140</field>
            <field name="description">0%</field>
            <field name="name">0% EU M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-EU-S" model="account.tax.template">
            <field name="sequence">1210</field>
            <field name="description">21%</field>
            <field name="name">21% EU S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-EU-G" model="account.tax.template">
            <field name="sequence">1220</field>
            <field name="description">21%</field>
            <field name="name">21% EU G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-EU-S" model="account.tax.template">
            <field name="sequence">1230</field>
            <field name="description">12%</field>
            <field name="name">12% EU S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-EU-G" model="account.tax.template">
            <field name="sequence">1240</field>
            <field name="description">12%</field>
            <field name="name">12% EU G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">12</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-EU-S" model="account.tax.template">
            <field name="sequence">1250</field>
            <field name="description">6%</field>
            <field name="name">6% EU S</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-EU-G" model="account.tax.template">
            <field name="sequence">1260</field>
            <field name="description">6%</field>
            <field name="name">6% EU G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-EU-S" model="account.tax.template">
            <field name="sequence">1270</field>
            <field name="description">0%</field>
            <field name="name">0% EU S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_88')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-21-EU" model="account.tax.template">
            <field name="sequence">1310</field>
            <field name="description">21%</field>
            <field name="name">21% EU IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-EU-G" model="account.tax.template">
            <field name="sequence">1280</field>
            <field name="description">0%</field>
            <field name="name">0% EU G</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-12-EU" model="account.tax.template">
            <field name="sequence">1320</field>
            <field name="description">12%</field>
            <field name="name">12% EU IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-06-EU" model="account.tax.template">
            <field name="sequence">1330</field>
            <field name="description">6%</field>
            <field name="name">6% EU IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">6</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                    'factor_percent': 100,
                    'repartition_type': 'base',
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                    'minus_report_line_ids': [ref('tax_report_line_55')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451055'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-00-EU" model="account.tax.template">
            <field name="sequence">1340</field>
            <field name="description">0%</field>
            <field name="name">0% EU IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_86')],
                    'plus_report_line_ids': [ref('tax_report_line_84')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-21-ROW-CC" model="account.tax.template">
            <field name="sequence">2110</field>
            <field name="description">21%</field>
            <field name="name">21% EX M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">21</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-12-ROW-CC" model="account.tax.template">
            <field name="sequence">2120</field>
            <field name="description">12%</field>
            <field name="name">12% EX M</field>
            <field name="amount">12</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-06-ROW-CC" model="account.tax.template">
            <field name="sequence">2130</field>
            <field name="description">6%</field>
            <field name="name">6% EX M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">6</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V81-00-ROW-CC" model="account.tax.template">
            <field name="sequence">2140</field>
            <field name="description">0%</field>
            <field name="name">0% EX M</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_81'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-21-ROW-CC" model="account.tax.template">
            <field name="sequence">2210</field>
            <field name="description">21%</field>
            <field name="name">21% EX S</field>
            <field name="amount">21</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-12-ROW-CC" model="account.tax.template">
            <field name="sequence">2220</field>
            <field name="description">12%</field>
            <field name="name">12% EX S</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-06-ROW-CC" model="account.tax.template">
            <field name="sequence">2230</field>
            <field name="description">6%</field>
            <field name="name">6% EX S</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">6</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V82-00-ROW-CC" model="account.tax.template">
            <field name="sequence">2240</field>
            <field name="description">0%</field>
            <field name="name">0% EX S</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_82'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-21-ROW-CC" model="account.tax.template">
            <field name="sequence">2310</field>
            <field name="description">21%</field>
            <field name="name">21% EX IG</field>
            <field name="amount">21</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_21"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-12-ROW-CC" model="account.tax.template">
            <field name="sequence">2320</field>
            <field name="description">12%</field>
            <field name="name">12% EX IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="amount">12</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_12"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-06-ROW-CC" model="account.tax.template"> <!--merged group-->
            <field name="sequence">2330</field>
            <field name="description">6%</field>
            <field name="name">6% EX IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">6</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_6"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                    'plus_report_line_ids': [ref('tax_report_line_59')],
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                    'minus_report_line_ids': [ref('tax_report_line_57')],
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                    'account_id': ref('a411'),
                }),

                (0,0, {
                    'factor_percent': -100,
                    'repartition_type': 'tax',
                    'account_id': ref('a451057'),
                }),
            ]"/>
        </record>

        <record id="attn_VAT-IN-V83-00-ROW-CC" model="account.tax.template">
            <field name="sequence">2340</field>
            <field name="description">0%</field>
            <field name="name">0% EX IG</field>
            <field name="price_include" eval="0"/>
            <field name="amount">0.0</field>
            <field name="amount_type">percent</field>
            <field name="type_tax_use">purchase</field>
            <field name="active" eval="False"/>
            <field name="chart_template_id" ref="l10nbe_chart_template"/>
            <field name="tax_group_id" ref="tax_group_tva_0"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'plus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'base',
                    'minus_report_line_ids': [ref('tax_report_line_83'), ref('tax_report_line_87')],
                    'plus_report_line_ids': [ref('tax_report_line_85')],
                }),

                (0,0, {
                    'factor_percent': 100,
                    'repartition_type': 'tax',
                }),
            ]"/>
        </record>

</odoo>
