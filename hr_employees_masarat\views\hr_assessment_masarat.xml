<?xml version="1.0"?>
<odoo>

    <record id="hr_assessmint_tree" model="ir.ui.view">
        <field name="name">hr.contract.assessment.element.tree</field>
        <field name="model">hr.contract.assessment.element</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="item_name"/>
                <field name="item_value"/>
            </tree>
        </field>
    </record>

    <record id="hr_trail_contract_assessment_elements_action" model="ir.actions.act_window">
        <field name="name">عناصر تقييم الفترة التجريبية</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.contract.assessment.element</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="hr_annual_assessmint_tree1" model="ir.ui.view">
        <field name="name">hr.assessment.annual.element.tree</field>
        <field name="model">hr.assessment.annual.element</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="item_name"/>
                <field name="item_value"/>
                <field name="item_type" />
            </tree>
        </field>
    </record>

    <record id="hr_assessment_annual_element_action" model="ir.actions.act_window">
        <field name="name">عناصر التقييم</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.assessment.annual.element</field>
        <field name="view_mode">tree,form</field>
    </record>


    <menuitem id="hr_assessment_section1"
              name="Performance appraisal"
              parent="hr.menu_hr_root"
              sequence="10"/>

    <menuitem
            id="menu_human_resources_configuration1"
            name="اعدادات عناصر تقييم الأداء"
            parent="hr_assessment_section1"
            groups="hr_employees_masarat.group_hr_masarat_assessment"
            sequence="10"/>

     <menuitem id="hr_annual_contract_assessment1"
              name="عناصر التقييم"
              parent="menu_human_resources_configuration1"
              action="hr_assessment_annual_element_action"
              sequence="1"/>

    <menuitem id="hr_trail_contract_assessment"
              name="عناصر تقييم الفترة التجريبية"
              parent="menu_human_resources_configuration1"
              action="hr_trail_contract_assessment_elements_action"
              sequence="2"/>

<!--    -->
<!--    -->
<!--    -->

    <record id="hr_assessment_monthly_form" model="ir.ui.view">
        <field name="name">hr.contract.assessment.monthly.form</field>
        <field name="model">hr.contract.assessment.monthly</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div>
                        <h2>
                            <field name="employee_id" placeholder="Employee" widget="selection"/>
                        </h2>
                        <h2>
                            Period
                        </h2>
                        <h2>
                            From:<field name="date_from"/>
                            To:<field name="date_to"/>
                        </h2>
                    </div>
                    <group>
                        <group>
                            <field name="current_user_id"/>
                            <field name="department_id"/>
                            <field name="job_id"/>
<!--                            <field name="contract_id"/>-->
                        </group>
                    </group>
                    <div>
                        <h2>
                            Result :
                            <field name="final_result"/>
                        </h2>
                    </div>
                    <notebook>
                        <page string="Time and Attendance">
                            <group>
                                <field name="time_attendance_ids" widget="one2many_list">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="time_attendance_wight"/>
                                <field name="time_attendance_result"/>
                            </group>
                        </page>
                        <page string="Performance">
                            <group>
                                <field name="performance_ids" widget="one2many_list">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="performance_wight"/>
                                <field name="performance_result"/>
                            </group>
                        </page>
                        <page string="Behaviors">
                            <group>
                                <field name="behaviors_ids" widget="one2many_list">
                                    <tree editable="top" create="false" delete="false">
                                        <field name="seq_number" readonly="1"/>
                                        <field name="item_name" readonly="1"/>
                                        <field name="item_value" readonly="1"/>
                                        <field name="item_score"/>
                                    </tree>
                                </field>
                                <field name="behaviors_wight"/>
                                <field name="behaviors_result"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="hr_assessment_monthly_tree" model="ir.ui.view">
        <field name="name">hr.contract.assessment.monthly.tree</field>
        <field name="model">hr.contract.assessment.monthly</field>
        <field name="arch" type="xml">
            <tree>
                <field name="assessment_name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="job_id"/>
                <field name="final_result"/>
            </tree>
        </field>
    </record>

    <record id="hr_assessment_monthly_action" model="ir.actions.act_window">
        <field name="name">تقيم الموظفين الشهري</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.contract.assessment.monthly</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="hr_assessment_by_employee1"
              name="تقييم الموظفين الشهري"
              parent="hr_assessment_section1"
              action="hr_assessment_monthly_action"
              sequence="2"/>

</odoo>
