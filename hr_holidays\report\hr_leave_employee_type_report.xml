<?xml version="1.0"?>
<odoo>
    <record id="view_search_hr_holidays_employee_type_report" model="ir.ui.view">
        <field name="name">hr.holidays.filter</field>
        <field name="model">hr.leave.employee.type.report</field>
        <field name="arch" type="xml">
            <search string="Search Time Off">
                <field name="employee_id"/>
                <field name="date_from"/>
                <filter name="year" date="date_from" default_period="this_year" string="Period"/>
                <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                <filter string="Employee" name="employee" context="{'group_by':'employee_id'}"/>
            </search>
        </field>
    </record>


    <record id="hr_leave_employee_type_report" model="ir.ui.view">
        <field name="name">hr.leave.employee.type.report.view.pivot</field>
        <field name="model">hr.leave.employee.type.report</field>
        <field name="arch" type="xml">
            <pivot sample="1" disable_linking="1">
                <field name="employee_id" type="row"/>
                <field name="number_of_days" type="measure"/>
                <field name="leave_type" type="col"/>
                <field name="holiday_status" type="col"/>
            </pivot>
        </field>
    </record>

    <record id="action_hr_holidays_by_employee_and_type_report" model="ir.actions.server">
        <field name="name">Time off Analysis by Employee and Time Off Type</field>
        <field name="model_id" ref="hr_holidays.model_hr_leave_employee_type_report"/>
        <field name="binding_model_id" ref="hr.model_hr_employee"/>
        <field name="state">code</field>
        <field name="groups_id" eval="[(4, ref('base.group_no_one'))]"/>
        <field name="code">
            action = model.action_time_off_analysis()
        </field>
    </record>

</odoo>
