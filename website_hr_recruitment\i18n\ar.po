# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_hr_recruitment
# 
# Translators:
# <PERSON>, 2021
# Wil <PERSON>do<PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_hr_recruitment
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "%s's Application"
msgstr "تطبيق %s "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "12 days / year, including <br/>6 of your choice."
msgstr "12 يوم / السنة، من ضمنها <br/>6 من اختيارك. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<br/><i class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"الهاتف \"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr ""
"<i class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"البريد"
" الإلكتروني \"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Publication date\" role=\"img\" "
"aria-label=\"Publication date\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-clock-o\" title=\"Publication date\" role=\"img\" "
"aria-label=\"تاريخ النشر \"/>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "<small><b>READ</b></small>"
msgstr "<small><b>قراءة</b></small> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "<span class=\"o_recruitment_purple\">Published</span>"
msgstr "<span class=\"o_recruitment_purple\">تم النشر</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Department</span>"
msgstr "<span class=\"s_website_form_label_content\">القسم</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Job</span>"
msgstr "<span class=\"s_website_form_label_content\">الوظيفة</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Resume</span>"
msgstr "<span class=\"s_website_form_label_content\">السيرة الذاتية</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "<span class=\"s_website_form_label_content\">Short Introduction</span>"
msgstr "<span class=\"s_website_form_label_content\">مقدمة قصيرة</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">بريدك الإلكتروني</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">اسمك</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid ""
"<span class=\"s_website_form_label_content\">Your Phone Number</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">رقم هاتفك</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span> "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_kanban_referal_extends
msgid ""
"<span title=\"Link Trackers\"><i class=\"fa fa-lg fa-link\" role=\"img\" "
"aria-label=\"Link Trackers\"/></span>"
msgstr ""
"<span title=\"متتبعات الروابط \"><i class=\"fa fa-lg fa-link\" role=\"img\" "
"aria-label=\"Link Trackers\"/></span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid ""
"<span>\n"
"                                            We usually reply between one and three days.<br/>\n"
"                                            Feel free to contact him/her if you have further questions.\n"
"                                        </span>"
msgstr ""
"<span>\n"
"                                            عادةً نرد في فترة تتراوح ما بين يوم وثلاثة أيام.<br/>\n"
"                                            يمكنك التواصل معه/معها إذا أردت طرح أية أسئلة أخرى.\n"
"                                        </span>"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "A full-time position <br/>Attractive salary package."
msgstr "منصب بدوام كامل <br/>باقة راتب مغرية. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid "About us"
msgstr "من نحن"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Achieve monthly sales objectives"
msgstr "تحقيق أهداف المبيعات الشهرية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Additional languages"
msgstr "لغات إضافية"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Administrative Work"
msgstr "العمل الإداري "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_countries
msgid "All Countries"
msgstr "كافة الدول"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_departments
msgid "All Departments"
msgstr "كافة الأقسام"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_filter_by_offices
msgid "All Offices"
msgstr "كافة المكاتب "

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_applicant
msgid "Applicant"
msgstr "المتقدم للوظيفة "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Applied Job"
msgstr "الوظيفة التي تم التقديم لها "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Apply Job"
msgstr "التقدم للوظيفة"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Apply Now!"
msgstr "قم بالتقديم الآن! "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"As an employee of our company, you will <b>collaborate with each department\n"
"                        to create and deploy disruptive products.</b> Come work at a growing company\n"
"                        that offers great benefits with opportunities to moving forward and learn\n"
"                        alongside accomplished leaders. We're seeking an experienced and outstanding\n"
"                        member of staff.\n"
"                        <br/><br/>\n"
"                        This position is both <b>creative and rigorous</b> by nature you need to think\n"
"                        outside the box. We expect the candidate to be proactive and have a \"get it done\"\n"
"                        spirit. To be successful, you will have solid solving problem skills."
msgstr ""
"كموظف في شركتنا، سوف <b>تتعاون مع كل من الأقسام\n"
"                        لإنشاء المنتجات الثورية وجعلها متاحة للاستخدام.</b> انضم إلينا للعمل في شركة في نمو مستمر\n"
"                        والتي تمنحك الكثير من الفوائد والفرص للمضي قدماً والتعلم \n"
"                        بجانب القادة المتميزين. نحن نبحث عن موظف ذو خبرة\n"
"                        ومميز في عمله.\n"
"                        <br/><br/>\n"
"                        هذا المنصب <b>مبدع وحازم</b> في آن واحد. عليك التفكير خارج\n"
"                        الصندوق بطبيعتك. نتوقع من المُرشح أن يكون سبّاقاً وأن تكون لديه روح مفعمة بالعزم والحيوية\n"
"                        حتى يكون ناجحاً، من المهم أن يكون لديه مهارة حل المشاكل. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Autonomy"
msgstr "الاستقلالية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Bachelor Degree or Higher"
msgstr "شهادة البكالريوس أو ما يفوقها "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__can_publish
msgid "Can Publish"
msgstr "بإمكانه النشر "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "Congratulations!"
msgstr "تهانينا!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Contact us"
msgstr "تواصل معنا"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "Continue To Our Website"
msgstr "المتابعة إلى موقعنا الإلكتروني "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Create content that will help our users on a daily basis"
msgstr "اصنع محتوى سيساعد مستخدمينا بشكل يومي "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Create new job pages from the <strong><i>+New</i></strong> top-right button."
msgstr ""
"أنشئ صفحات جديدة للوظائف من زر <strong><i>+جديد</i></strong> بأعلى اليسار. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Customer Relationship"
msgstr "علاقة العميل "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#: model:ir.model,name:website_hr_recruitment.model_hr_department
#, python-format
msgid "Department"
msgstr "القسم"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Discover our products."
msgstr "استكشف منتجاتنا. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                You can make a real contribution to the success of the company.\n"
"                <br/>\n"
"                Several activities are often organized all over the year, such as weekly\n"
"                sports sessions, team building events, monthly drink, and much more"
msgstr ""
"لدى كل موظف الفرصة لرؤية البصمة التي يتركها عمله.\n"
"                        بمقدورك أن تساهم بشكل كبير في نجاح الشركة.\n"
"                        <br/>\n"
"                        يتم تنظيم العديد من الأنشطة على مدار العام، كالتجمعات\n"
"                        الرياضية الأسبوعية وفعاليات بناء الفِرَق، والمشروبات الشهرية وغير ذلك الكثير "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Eat &amp; Drink"
msgstr "كل واشرب"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Expand your knowledge of various business industries"
msgstr "وسع آفاق معرفتك بمختلف مجالات الأعمال "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Experience in writing online content"
msgstr "خبرة في كتابة المحتوى عبر الإنترنت "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Fruit, coffee and <br/>snacks provided."
msgstr "يتم توفير <br/>الفواكه والقهوة والوجبات الخفيفة. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Google Adwords experience"
msgstr "خبرة في Google Adwords"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Great team of smart people, in a friendly and open culture"
msgstr "فريق رائع من الأفراد اللامعين، في بيئة ودودة ولطيفة "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Highly creative and autonomous"
msgstr "الإبداع والاستقلالية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "In the meantime,"
msgstr "في هذه الأثناء،"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_published
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_form_website_published_button
msgid "Is Published"
msgstr "تم نشره "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Job Application Form"
msgstr "نموذج التقدم لوظيفة"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "Job Detail"
msgstr "تفاصيل الوظيفة"

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: website_hr_recruitment
#: code:addons/website_hr_recruitment/controllers/main.py:0
#, python-format
msgid "Job Title"
msgstr "المسمى الوظيفي"

#. module: website_hr_recruitment
#: code:addons/website_hr_recruitment/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
#, python-format
msgid "Jobs"
msgstr "الوظائف"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Join us and help disrupt the enterprise market!"
msgstr "انضم إلينا وساعدنا على النمو!"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid ""
"Join us, we offer you an extraordinary chance to learn, to\n"
"                                    develop and to be part of an exciting experience and\n"
"                                    team."
msgstr ""
"انضم إلينا، فنحن نوفر فرصة رائعة لكي تتعلم\n"
"                                    وتتطور وتصبح جزءاً من تجربة مثيرة وفريق\n"
"                                    ودود. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Lead the entire sales cycle"
msgstr "ترأس دورة المبيعات بأكملها "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "Look around on our website:"
msgstr "ألقِ نظرة على موقعنا الإلكتروني: "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Master demos of our software"
msgstr "النسخ التجريبية الرئيسية لبرنامجنا "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Must Have"
msgstr "يلزم توافره "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Negotiate and contract"
msgstr "فاوض وتعاقد "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Nice to have"
msgstr "من الجيد توافره "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr "لا مدراء غير متفهمين، أو أدوات سخيفة، أو ساعات عمل متشددة "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""
"لن يضيع أي وقت عند القيام بالعمليات المؤسسية والمسؤوليات الحقيقية "
"والاستقلالية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
msgid "Not published"
msgstr "غير منشور "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "Our Job Offers"
msgstr "عروض وظائفنا"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Our Product"
msgstr "منتجنا "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Passion for software products"
msgstr "الشغف حول منتجات البرامج "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perfect written English"
msgstr "مهارات كتابية ممتازة باللغة الإنجليزية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Perks"
msgstr "المزايا "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Personal Evolution"
msgstr "التطور الشخصي "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Phone Number"
msgstr "رقم الهاتف"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Play any sport with colleagues, <br/>the bill is covered."
msgstr "مارس أي رياضة مع زملائك، <br/>وسوف نقوم بتغطية التكاليف. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_job_website_inherit
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.view_hr_job_tree_inherit_website
msgid "Published"
msgstr "تم النشر "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Qualify the customer needs"
msgstr "قم بتأهيل احتياجات العميل "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "مسؤوليات وتحديات حقيقية في شركة سريعة التطور "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Responsibilities"
msgstr "المسؤوليات "

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_id
msgid "Restrict publishing to this website."
msgstr "قصر إمكانية النشر على هذا الموقع الإلكتروني. "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__is_seo_optimized
msgid "SEO optimized"
msgstr "تم تحسين محركات البحث"

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__seo_name
msgid "Seo name"
msgstr "اسم محسنات محرك البحث "

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_published
msgid "Set if the application is published on the website of the company."
msgstr "قم بالتعيين إذا كان التطبيق منشوراً في الموقع الإلكتروني للشركة. "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Short Introduction"
msgstr "مقدمة قصيرة "

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "مصدر مقدمي الطلبات "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Sport Activity"
msgstr "النشاط الرياضي"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Strong analytical skills"
msgstr "مهارات تحليلية قوية "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.apply
msgid "Submit"
msgstr "إرسال"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Technical Expertise"
msgstr "خبرات تقنية "

#. module: website_hr_recruitment
#: model:ir.model.fields,help:website_hr_recruitment.field_hr_job__website_url
msgid "The full URL to access the document through the website."
msgstr "رابطURL الكامل للوصول إلى المستند من خلال الموقع الإلكتروني. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Trainings"
msgstr "التدريبات "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_recruitment_source__url
msgid "Url Parameters"
msgstr "معايير رابط URL "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "Valid work permit for Belgium"
msgstr "تصريح عمل صالح لبلجيكا "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_published
msgid "Visible on current website"
msgstr "مرئي في الموقع الإلكتروني الحالي "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.job_right_side_bar
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products.\n"
"                                We build great products to solve your business problems."
msgstr ""
"نحن فريق من الأفراد الشغوفين الذين يهدفون إلى تحسين حياة الآخرين من خلال المنتجات الثورية.\n"
"                                نقوم ببناء منتجات لا مثيل لها لحل كافة مشاكل أعمالك. "

#. module: website_hr_recruitment
#: model:ir.model,name:website_hr_recruitment.model_website
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: website_hr_recruitment
#: model:ir.actions.act_url,name:website_hr_recruitment.action_open_website
msgid "Website Recruitment Form"
msgstr "استمارة التوظيف في الموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_url
msgid "Website URL"
msgstr "رابط URL للموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_description
msgid "Website description"
msgstr "وصف الموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_description
msgid "Website meta description"
msgstr "الوصف الدلالي في الموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_keywords
msgid "Website meta keywords"
msgstr "الكلمات الدلالية بالموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_title
msgid "Website meta title"
msgstr "العنوان الدلالي بالموقع الإلكتروني "

#. module: website_hr_recruitment
#: model:ir.model.fields,field_description:website_hr_recruitment.field_hr_job__website_meta_og_img
msgid "Website opengraph image"
msgstr "صورة الرسم البياني المفتوح للموقع الإلكتروني "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What We Offer"
msgstr "ما نقدمه لك "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.default_website_description
msgid "What's great in the job?"
msgstr "ما الرائع بشأن الوظيفة؟ "

#. module: website_hr_recruitment
#: code:addons/website_hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "You cannot apply for this job."
msgstr "لا يمكنك التقديم لهذه الوظيفة. "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Email"
msgstr "بريدك الإلكتروني "

#. module: website_hr_recruitment
#. openerp-web
#: code:addons/website_hr_recruitment/static/src/js/website_hr_recruitment_editor.js:0
#, python-format
msgid "Your Name"
msgstr "اسمك"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "Your application has been posted successfully."
msgstr "تم نشر تطبيقك بنجاح."

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.thankyou_ir_ui_view
#: model_terms:website.page,arch_db:website_hr_recruitment.thankyou
msgid "Your application has been sent to:"
msgstr "تم إرسال تطبيقك إلى:"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.detail
msgid "breadcrumb"
msgstr "آثار التتبع "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "for job opportunities."
msgstr "لفرص العمل. "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "open positions"
msgstr "الوظائف الشاغرة"

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.hr_recruitment_source_kanban_inherit_website
msgid "share it"
msgstr "شاركه "

#. module: website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:website_hr_recruitment.index
msgid "unpublished"
msgstr "غير منشور"
