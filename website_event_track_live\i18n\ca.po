# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_track_live
# 
# Translators:
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:28+0000\n"
"Last-Translator: ma<PERSON><PERSON>, 2022\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_content
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"pl-2\">Loading Video...</span>"
msgstr ""
"<i class=\"fa fa-spin fa-circle-o-notch position-relative\"/>\n"
"                <span class=\"pl-2\">Carregant vídeo...</span>"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.event_track_aside
msgid "Chat"
msgstr "Conversa"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__is_youtube_replay
msgid ""
"Check this option if the video is already available on Youtube to avoid "
"showing 'Direct' options (Chat, ...)"
msgstr ""
"Comproveu aquesta opció si el vídeo ja està disponible a Youtube per evitar "
"mostrar les opcions 'Direct' (Chat, ...)"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_url
msgid ""
"Configure this URL so that event attendees can see your Track in video!"
msgstr ""
"Configureu aquest Enllaç perquè els assistents puguin veure la vostra pista "
"al vídeo!"

#. module: website_event_track_live
#: model:ir.model,name:website_event_track_live.model_event_track
msgid "Event Track"
msgstr "Pista de l'esdeveniment"

#. module: website_event_track_live
#: model:ir.model.fields,help:website_event_track_live.field_event_track__youtube_video_id
msgid ""
"Extracted from the video URL and used to infer various links "
"(embed/thumbnail/...)"
msgstr ""
"Extret de l'Enllaç del vídeo i utilitzat per inferir diversos enllaços "
"(embed/thumbnail/...)"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_chat_available
msgid "Is Chat Available"
msgstr "Està disponible el xat"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__is_youtube_replay
msgid "Is Youtube Replay"
msgstr "És la repetició de Youtube"

#. module: website_event_track_live
#: model_terms:ir.ui.view,arch_db:website_event_track_live.tracks_display_list
msgid "Replay"
msgstr "Repetició"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Replay Video"
msgstr "Repetició Vídeo"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Starts in"
msgstr "Comença a"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "Up Next:"
msgstr "Pròxim pas:"

#. module: website_event_track_live
#. openerp-web
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#: code:addons/website_event_track_live/static/src/xml/website_event_track_live_templates.xml:0
#, python-format
msgid "You just watched:"
msgstr "Acabes de mirar:"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_url
msgid "Youtube Video URL"
msgstr "URL del vídeo de Youtube"

#. module: website_event_track_live
#: model:ir.model.fields,field_description:website_event_track_live.field_event_track__youtube_video_id
msgid "Youtube video ID"
msgstr "ID del vídeo de Youtube"
