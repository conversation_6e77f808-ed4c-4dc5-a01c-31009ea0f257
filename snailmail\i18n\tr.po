# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# Halil, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-11-16 13:19+0000\n"
"PO-Revision-Date: 2021-09-14 12:26+0000\n"
"Last-Translator: Halil, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_cover
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__snailmail_cover
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Add a Cover Page"
msgstr "Kapak Sayfası Ekleme"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Address"
msgstr "Adres"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/notification_group/notification_group.xml:0
#, python-format
msgid "An error occurred when sending a letter with Snailmail."
msgstr "Snailmail ile mektup gönderilirken bir hata oluştu."

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An error occurred when sending the document by post.<br>Error: %s"
msgstr "Belge posta ile gönderilirken bir hata oluştu.<br>Hata: %s"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "An unknown error happened. Please contact the support."
msgstr "Bilinmeyen bir hata oluştu. Lütfen destek ekibiyle iletişime geçin."

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "An unknown error occurred. Please contact our"
msgstr "Bilinmeyen bir hata oluştu. Lütfen bizimle iletişime geçin"

#. module: snailmail
#: code:addons/snailmail/wizard/snailmail_letter_cancel.py:0
#, python-format
msgid ""
"Are you sure you want to discard %s snailmail delivery failures? You won't "
"be able to re-send these letters later!"
msgstr ""

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_id
msgid "Attachment"
msgstr "Ek"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_fname
msgid "Attachment Filename"
msgstr "Ek Dosya Adı"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#, python-format
msgid "Awaiting Dispatch"
msgstr "Gönderim Bekliyor"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__duplex
msgid "Both side"
msgstr "Her iki taraf"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_duplex
msgid "Both sides"
msgstr "Her iki taraf da"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "Buy credits"
msgstr "Kredi Satınal"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__credit_error
msgid "CREDIT_ERROR"
msgstr "CREDIT_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Cancel"
msgstr "İptal"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel Letter"
msgstr "Mektubu iptal et"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Cancel letter"
msgstr "Mektubu iptal et"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Cancel notification in failure"
msgstr "Bildirimi başarısız olarak iptal et"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__canceled
#, python-format
msgid "Canceled"
msgstr "İptal Edildi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__city
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__city
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "City"
msgstr "Semt/İlçe"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
#, python-format
msgid "Close"
msgstr "Kapat"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_company__snailmail_color
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__color
msgid "Color"
msgstr "Renk"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__company_id
msgid "Company"
msgstr "Şirket"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Confirm"
msgstr "Onayla"

#. module: snailmail
#: model:ir.model,name:snailmail.model_res_partner
msgid "Contact"
msgstr "Kontak"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__country_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__country_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Country"
msgstr "Ülke"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__cover
msgid "Cover Page"
msgstr "Kapak Sayfası"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_uid
msgid "Created by"
msgstr "Oluşturan"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__create_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__create_date
msgid "Created on"
msgstr "Oluşturulma"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
msgid "Discard delivery failures"
msgstr "Teslimat hatalarını sil"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_cancel_action
msgid "Discard snailmail delivery failures"
msgstr ""

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_cancel
msgid "Dismiss notification for resend by model"
msgstr "Modele göre yeniden gönderme bildirimini reddet"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__display_name
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__display_name
msgid "Display Name"
msgstr "Görünüm Adı"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__attachment_datas
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Document"
msgstr "Belge"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__res_id
msgid "Document ID"
msgstr "Belge ID"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__error_code
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__error
#, python-format
msgid "Error"
msgstr "Hata"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__format_error
msgid "FORMAT_ERROR"
msgstr "FORMAT_ERROR"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.js:0
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_missing_required_fields_action
#, python-format
msgid "Failed letter"
msgstr "Başarısız mektup"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Başarısızlık türü"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.snailmail_letter_format_error_action
msgid "Format Error"
msgstr "Biçim Hatası"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_format_error
msgid "Format Error Sending a Snailmail Letter"
msgstr "Snailmail ile mektup gönderilirken bir hata oluştu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__help_message
msgid "Help message"
msgstr "Yardım mesajı"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__id
msgid "ID"
msgstr "ID"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_cancel
msgid ""
"If you want to re-send them, click Cancel now, then click on the "
"notification and review them one by one by clicking on the red paper-plane "
"next to each message."
msgstr ""

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__pending
msgid "In Queue"
msgstr "Sırada"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__info_msg
msgid "Information"
msgstr "Bilgi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error____last_update
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields____last_update
msgid "Last Modified on"
msgstr "Son Düzenleme"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_uid
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__write_date
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_mail_message__letter_ids
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__letter_id
msgid "Letter"
msgstr "Mektub"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "Letter sent by post with Snailmail"
msgstr "Snailmail ile posta yoluyla gönderilen mektup"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_list
msgid "Letters"
msgstr "Mektuplar"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__missing_required_fields
msgid "MISSING_REQUIRED_FIELDS"
msgstr "MISSING_REQUIRED_FIELDS"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_message
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_format_error__message_id
msgid "Message"
msgstr "Mesaj"

#. module: snailmail
#: model:ir.model,name:snailmail.model_mail_notification
msgid "Message Notifications"
msgstr "Mesaj Bildirimleri"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,help:snailmail.field_mail_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Mesaj türü: e-posta mesajı için e-posta, sistem mesajı için bildirim,bir "
"kullanıcı cevaplar gibi diğer mesajlar için Yorum"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__model
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_cancel__model
msgid "Model"
msgstr "Model"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_confirm__model_name
msgid "Model Name"
msgstr "Model Adı"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__no_price_available
msgid "NO_PRICE_AVAILABLE"
msgstr "NO_PRICE_AVAILABLE"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Bildirim türü"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__notification_ids
msgid "Notifications"
msgstr "Bildirimler"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "One or more required fields are empty."
msgstr "Bir veya daha fazla zorunlu alan boş."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__report_template
msgid "Optional report to print and attach"
msgstr "Yazdırıp eklemek için seçmeli çıktı"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid ""
"Our service cannot read your letter due to its format.<br/>\n"
"                Please modify the format of the template or update your settings\n"
"                to automatically add a blank cover page to all letters."
msgstr ""
"Hizmetimiz formatı nedeniyle mektubunuzu okuyamıyor.<br/>\n"
"                Lütfen şablonun biçimini değiştirin veya ayarlarınızı güncelleyin\n"
"                tıklayarak tüm harflere otomatik olarak boş bir kapak sayfası ekleyin."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__partner_id
msgid "Partner"
msgstr "İş Ortağı"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Her iki tarafı da yazdır"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_res_config_settings__snailmail_color
msgid "Print In Color"
msgstr "Renkli Yazdır"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb Alanıyla İlgili Kişi"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "Re-send letter"
msgstr "Mektubu tekrar gönder"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__partner_id
msgid "Recipient"
msgstr "Alıcı"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__reference
msgid "Related Record"
msgstr "İlgili Kayıt"

#. module: snailmail
#: model:ir.model,name:snailmail.model_ir_actions_report
msgid "Report Action"
msgstr "Rapor işlemi"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_form
msgid "Send Now"
msgstr "Şimdi Gönder"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_notification_popover/snailmail_notification_popover.js:0
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__state__sent
#, python-format
msgid "Sent"
msgstr "Gönderildi"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__user_id
msgid "Sent by"
msgstr "Sent by"

#. module: snailmail
#: code:addons/snailmail/wizard/snailmail_confirm.py:0
#: model:ir.model.fields.selection,name:snailmail.selection__mail_message__message_type__snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__notification_type__snail
#, python-format
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_confirm
msgid "Snailmail Confirm"
msgstr "Snailmail Onayla"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "Snailmail Confirmation"
msgstr "Snailmail Onayı"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_credit
msgid "Snailmail Credit Error"
msgstr "Snailmail Kredi Hatası"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/models/notification_group/notification_group.js:0
#, python-format
msgid "Snailmail Failures"
msgstr "Snailmail Hataları"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_format
msgid "Snailmail Format Error"
msgstr "Snailmail Format Hatası"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter
#: model:ir.model.fields,field_description:snailmail.field_mail_notification__letter_id
msgid "Snailmail Letter"
msgstr "Snailmail Mektubu"

#. module: snailmail
#: model:ir.actions.act_window,name:snailmail.action_mail_letters
#: model:ir.ui.menu,name:snailmail.menu_snailmail_letters
msgid "Snailmail Letters"
msgstr "Snailmail Mektupları"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_fields
msgid "Snailmail Missing Required Fields"
msgstr "Snailmail Gerekli Alanları Eksik"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_price
msgid "Snailmail No Price Available"
msgstr "Snailmail Fiyat Yok"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__message_id
msgid "Snailmail Status Message"
msgstr "Snailmail Durum İletisi"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_trial
msgid "Snailmail Trial Error"
msgstr "Snailmail Deneme Hatası"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__mail_notification__failure_type__sn_error
msgid "Snailmail Unknown Error"
msgstr "Snailmail Bilinmeyen Hata"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__snailmail_error
#: model:ir.model.fields,field_description:snailmail.field_mail_message__snailmail_error
msgid "Snailmail message in error"
msgstr "Snailmail mesajı hatalı"

#. module: snailmail
#: model:ir.actions.server,name:snailmail.snailmail_print_ir_actions_server
#: model:ir.cron,cron_name:snailmail.snailmail_print
#: model:ir.cron,name:snailmail.snailmail_print
msgid "Snailmail: process letters queue"
msgstr "Snailmail: işlem mektupları kuyruğu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state_id
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__state_id
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "State"
msgstr "Durum"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__state
msgid "Status"
msgstr "Durumu"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street
msgid "Street"
msgstr "Sokak/Cadde"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street 2..."
msgstr "Adres 2..."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Street..."
msgstr "Adres..."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__street2
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__street2
msgid "Street2"
msgstr "Adres2"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__trial_error
msgid "TRIAL_ERROR"
msgstr "TRIAL_ERROR"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The address of the recipient is not complete"
msgstr "Alıcının adresi tam değil"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"The attachment of the letter could not be sent. Please check its content and"
" contact the support if the problem persists."
msgstr ""
"Mektubun eki gönderilemedi. Lütfen içeriğini kontrol edin ve sorun devam "
"ederse destek ekibiyle iletişime geçin."

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The country of the partner is not covered by Snailmail."
msgstr "Ortağın ülkesi Snailmail kapsamında değildir."

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid ""
"The country to which you want to send the letter is not supported by our "
"service."
msgstr ""
"Mektubu göndermek istediğiniz ülke hizmetimiz tarafından "
"desteklenmemektedir."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid ""
"The customer address is not complete. Update the address here and re-send "
"the letter."
msgstr ""
"Müşteri adresi tam değil. Adresi buradan güncelleyin ve mektubu yeniden "
"gönderin."

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid "The document was correctly sent by post.<br>The tracking id is %s"
msgstr ""
"Belge posta yoluyla doğru bir şekilde gönderildi.<br>İzleme kimliği: %s"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid ""
"The letter could not be sent due to insufficient credits on your IAP "
"account."
msgstr "IAP hesabınızdaki yetersiz kredi nedeniyle mektup gönderilemedi."

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:snailmail.field_mail_message__message_type
msgid "Type"
msgstr "Tür"

#. module: snailmail
#: model:ir.model.fields.selection,name:snailmail.selection__snailmail_letter__error_code__unknown_error
msgid "UNKNOWN_ERROR"
msgstr "UNKNOWN_ERROR"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_format_error
msgid "Update Config and Re-send"
msgstr "Yapılandırmayı Güncelle ve Yeniden Gönder"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "Update address and re-send"
msgstr "Adresi güncelleme ve yeniden gönderme"

#. module: snailmail
#: model:ir.model,name:snailmail.model_snailmail_letter_missing_required_fields
msgid "Update address of partner"
msgstr "İş ortağının adresini güncelleme"

#. module: snailmail
#: model:ir.model.fields,help:snailmail.field_snailmail_letter__state
msgid ""
"When a letter is created, the status is 'Pending'.\n"
"If the letter is correctly sent, the status goes in 'Sent',\n"
"If not, it will got in state 'Error' and the error message will be displayed in the field 'Error Message'."
msgstr ""
"Bir mektup oluşturulduğunda, durum 'Beklemede' olur.\n"
"Mektup doğru bir şekilde gönderilirse, durum 'Gönderildi' olarak geçer,\n"
"Aksi takdirde, 'Hata' durumuna geçecek ve hata mesajı 'Hata Mesajı' alanında görüntülenecektir."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "You are about to send this"
msgstr "Bunu göndermek üzeresiniz"

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have an IAP account registered for this service.<br>Please go to "
"<a href=%s target=\"new\">iap.odoo.com</a> to claim your free credits."
msgstr ""
"Bu hizmet için kayıtlı bir IAP hesabınız yok.<br>Ücretsiz kredilerinizi "
"almak için <a href=%s target=\"new\">iap.odoo.com</a> adresine gidin."

#. module: snailmail
#: code:addons/snailmail/models/snailmail_letter.py:0
#, python-format
msgid ""
"You don't have enough credits to perform this operation.<br>Please go to "
"your <a href=%s target=\"new\">iap account</a>."
msgstr ""
"Bu işlemi gerçekleştirmek için yeterli krediniz yok.<br>Lütfen <a href=%s "
"target=\"new\">iap hesabınıza</a> gidin."

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "You need credits on your IAP account to send a letter."
msgstr "Mektup göndermek için IAP hesabınızda krediye ihtiyacınız vardır."

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_letter_missing_required_fields
msgid "ZIP"
msgstr "PK"

#. module: snailmail
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter__zip
#: model:ir.model.fields,field_description:snailmail.field_snailmail_letter_missing_required_fields__zip
msgid "Zip"
msgstr "PK"

#. module: snailmail
#: model_terms:ir.ui.view,arch_db:snailmail.snailmail_confirm_view
msgid "by post. Are you sure you want to continue?"
msgstr "posta yoluyla. Devam etmek istediğinizden emin misiniz?"

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "for further assistance."
msgstr "daha fazla yardım için."

#. module: snailmail
#. openerp-web
#: code:addons/snailmail/static/src/components/snailmail_error_dialog/snailmail_error_dialog.xml:0
#, python-format
msgid "support"
msgstr "destek"
