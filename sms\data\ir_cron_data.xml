<?xml version="1.0" encoding="utf-8"?>
<odoo><data noupdate="1">
    <record forcecreate="True" id="ir_cron_sms_scheduler_action" model="ir.cron">
        <field name="name">SMS: SMS Queue Manager</field>
        <field name="model_id" ref="model_sms_sms"/>
        <field name="state">code</field>
        <field name="code">model._process_queue()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="numbercall">-1</field>
        <field eval="False" name="doall"/>
    </record>
</data></odoo>