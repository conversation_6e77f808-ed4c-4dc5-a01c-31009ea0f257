# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <radoslaw.bieg<PERSON><EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <m.e.grz<PERSON>@gmail.com>, 2021
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <stre<PERSON><PERSON>@gmail.com>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Mariusz, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# Dawid Prus, 2021
# Martin Trigaux, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:27+0000\n"
"Last-Translator: Tadeusz Karpiński <<EMAIL>>, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr " rekordy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# Edytor kodu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d dni temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d godzin temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d minut temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d miesięcy temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d lat temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "%s Files"
msgstr "%s Pliki"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "%s days ago"
msgstr "%s dni temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' nie jest poprawną datą"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "'%s' nie jest poprawną datą"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s' nie jest poprawną datą lub czasem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' nie jest poprawną datą i czasem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' nie jest poprawną liczbą zmiennoprzecinkową (float)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' nie jest poprawną wartością \"integer\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "'%s' nie jest poprawnym polem monetarnym"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' nie jest konwertowalne do daty lub czasu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "'%s' jest niezsynchronizowany z '%s'."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "(%s/%sMb)"
msgstr "(%s/%sMb)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(zmiana)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(policz)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(brak wyników)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(no string)"
msgstr "(brak tekstu)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(brak etykiety)"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "1 record"
msgstr "1 rekord"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2,887.50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span> "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">Podatek 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                                   Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"Rd <br/> Pleasant Hill CA 94523<br/>United States</span> "

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">15.00%</span>"
msgstr "<span id=\"line_tax_ids\">15.00%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">Deco Addict</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.000</span>"
msgstr "<span>5.000</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>Kwota</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>Opis</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2020/07/0003</span>"
msgstr ""
"<span>Faktura</span>\n"
"<span>INV/2020/07/0003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>Warunki płatności: 30 dni</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>Ilość</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>Podatki</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>Cena jednostkowa</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>Biurko dla czterech osób [FURN_8220]<br/>\n"
"Czteroosobowe nowoczesne stanowisko biurowe</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>Sofa trzyosobowa [FURN_8999]<br/>\n"
"Sofa trzyosobowa z leżanką w kolorze stalowo-szarym</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Termin płatności:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>Data faktury:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Wartość</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>Suma</strong>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "Filtr o tej samej nazwie już istnieje."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "Wymagane jest podanie nazwy ulubionego filtra."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr ""
"Wyskakujące okno zostało zablokowane. Zmień ustawienia swojej przeglądarki "
"internetowej aby je odblokować."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "WSZYSTKO"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "JAKIŚ"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "Brak dostępu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "Błąd dostępu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "Dostęp do wszystkich aplikacji Enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action"
msgstr "Akcja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Action ID:"
msgstr "ID akcji:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Widok okna akcji"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "Aktywuj debugowanie zasobów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "Aktywuj testy Debugowanie aktywów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add"
msgstr "Dodaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "Add %s"
msgstr "Dodaj %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "Dodaj własny filtr"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "Dodaj własną grupę"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add a Column"
msgstr "Dodaj kolumnę"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "Dodaj warunek"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Add a line"
msgstr "Dodaj pozycję"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "Dodaj oddział"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Add column"
msgstr "Dodaj kolumnę"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "Dodaj filtr"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "Dodaj nową wartość"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "Dodaj węzeł"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr "Dodaj kontekst dyrektywy qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "Dodaj znacznik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "Dodaj do ulubionych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Additional actions"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "Dodaj: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "Zaadoptuj swój podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt and Sign"
msgstr "Zaadoptuj i podpisz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "Ostrzeżenie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#, python-format
msgid "All"
msgstr "Wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_renderer.js:0
#, python-format
msgid "All day"
msgstr "Cały dzień"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "Wszyscy użytkownicy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Alt"
msgstr "Alt"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Among the"
msgstr "Wśród nich"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "Wystąpił błąd"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "And more"
msgstr "I więcej"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "Dowolny"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr ""
"Pojawia się domyślnie w prawym górnym rogu drukowanych dokumentów (nagłówek "
"raportów)."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Apply"
msgstr "Zastosuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "Archiwizuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Archive All"
msgstr "Zarchiwizuj wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "Czy na pewno chcesz zarchiwizować wszystkie rekordy z tej kolumny?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "Czy na pewno chcesz zarchiwizować wszystkie wybrane rekordy?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "Czy na pewno chcesz zarchiwizować ten rekord?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "Jesteś pewien, że chcesz usunąć tę kolumnę?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "Jesteś pewien, że chcesz usunąć ten filtr?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "Jesteś pewien, że chcesz usunąć te rekordy?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "Jesteś pewien, że chcesz usunąć te rekordy?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "Jesteś pewien, że chcesz usunąć ten rekord?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "Jesteś pewien, że chcesz usunąć ten rekord?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "Czy na pewno chcesz wykonać następującą aktualizację na"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Ascending"
msgstr "Rosnąco"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Attach"
msgstr "Dołącz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Attachment"
msgstr "Załącznik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "Automatycznie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Available fields"
msgstr "Dostępne pola"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Avatar"
msgstr "Awatar"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "Obrazek tła"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Badge"
msgstr "Odznaka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Badges"
msgstr "Odznaki"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Bar Chart"
msgstr "Wykres słupkowy"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "Baza"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "Zostań superużytkownikiem"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr ""
"Pola binarne nie mogą być eksportowane do programu Excel, chyba że ich "
"zawartość jest zakodowana w base64. Wydaje się, że tak nie jest w przypadku "
"%s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Binary file"
msgstr "Plik binarny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "Gwarancja naprawy błędów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button"
msgstr "Przycisk"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Button Type:"
msgstr "Typ przycisku:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt and Sign, I agree that the chosen signature/initials will "
"be a valid electronic representation of my hand-written signature/initials "
"for all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"Klikając przycisk Popraw i Podpisz, zgadzam się, że wybrany podpis/inicjały "
"będą ważną elektroniczną reprezentacją mojego podpisu/inicjałów odręcznych "
"do wszystkich celów, gdy będzie używany na dokumentach, w tym prawnie "
"wiążących umowach."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar"
msgstr "Kalendarz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Calendar toolbar"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_view.js:0
#, python-format
msgid "Calendar view has not defined 'date_start' attribute."
msgstr "Widok kalendzara nie ma zdefiniowanego atrybutu 'date_start'."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "Anuluj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "Anulowanie przesyłania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "Kolor karty: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/change_password.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change Password"
msgstr "Zmień hasło"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Change default:"
msgstr "Zmień domyślne:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Change graph"
msgstr "Zmień wykres"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#, python-format
msgid "Checkbox"
msgstr "Checkbox"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Checkboxes"
msgstr "Checkboxy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "Wybierz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "Wybierz plik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr "Wybierz polecenie debugowania..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Clear"
msgstr "Wyczyść"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "Czytelny podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#, python-format
msgid "Close"
msgstr "Zamknij"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "Kolory"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "Kolumna %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Column title"
msgstr "Tytuł kolumny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Command"
msgstr ""

#. module: web
#: model:ir.model,name:web.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "Firma"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "Dane firmy"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "Układ dokumentu firmy"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "Logo firmy"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "Nazwa firmy"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "Slogan firmy"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "Nazwa firmy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "Porównanie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "Warunek:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "Skonfiguruj układ dokumentu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "Potwierdzenie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "Utracono połączenie. Próbuję połączyć się ponownie..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "Połączenie przywrócone. Jesteś z powrotem online."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Context:"
msgstr "Kontekst:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Control"
msgstr "Kontrola"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "Przyciski panelu sterowania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "Skopiowano !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Copy"
msgstr "Kopia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "Skopiuj tekst błędu do schowka"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "Skopiuj do schowka"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "Prawa autorskie &amp;copy;"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "Nie można połączyć się z serwerem"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "Nie można wyświetlić wybranego obrazu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "Nie można wyświetlić określonego adresu url obrazu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "Nie można serializować XML"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr ""
"Nie można ustawić obrazu okładki: w widoku podano nieprawidłowe pole "
"(\"%s\")"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/helpers/utils.js:0
#, python-format
msgid "Count"
msgstr "Liczba"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "Kraj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Create"
msgstr "Utwórz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "Utwórz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "Utwórz  \"<strong>%s</strong>\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Create a new record"
msgstr "Utwórz nowy rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "Utwórz i edytuj..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Create record"
msgstr "Utwórz rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "Utwórz: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "Data utworzenia:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "Użytkownik tworzący:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Current state"
msgstr "Obecny stan"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "Niestandardowe kolory"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "Ciemnoniebieski"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "Ciemnofioletowy"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "Baza danych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Date & Time"
msgstr "Data i czas"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Day"
msgstr "Dzień"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "Wyłączenie trybu debugowania"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.debug_icon
msgid ""
"Debug mode is activated#{debug_mode_help}. Click here to exit debug mode."
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr "Narzędzia do debugowania..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Decimal"
msgstr "Dziesiętny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "Domyślny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Default:"
msgstr "Domyślne:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Delete"
msgstr "Usuń"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "Usuń element"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "Usuń węzeł"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "Usuń wiersz "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Descending"
msgstr "Malejąco"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Description"
msgstr "Opis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard"
msgstr "Odrzuć"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Discard a record modification"
msgstr "Odrzuć zmiany"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "Odrzuć rekord"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "Czy na pewno chcesz anulować przesyłanie %s?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "Czy na pewno chcesz usunąć ten szablon eksportu?"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "Szablon dokumentu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "Dokumentacja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain"
msgstr "Domena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr "Węzeł domeny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr "Domena nie jest prawidłowo uformowana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr "Nieobsługiwana domena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain:"
msgstr "Domena:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "Nie odchodź jeszcze,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "Nie wychodź jeszcze,<br />ciągle pobieram..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Download"
msgstr "Pobierz"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "Pobierz podgląd PDF"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Download xlsx"
msgstr "Pobierz xlsx"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "Podpisz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "Menu rozwijane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "Duplikuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Edit"
msgstr "Edytuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "Edytuj czynność"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "Edytuj kolumne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit ControlPanelView"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit Domain"
msgstr "Edytuj domenę"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Edit Stage"
msgstr "Edytuj etap"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "Edytuj widok:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Edit a record"
msgstr "Edytuj rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "Edytuj rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "E-mail"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "Włącz profilowanie"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Error, password not changed !"
msgstr "Błąd, hasło niezmienione !"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Esc to discard"
msgstr "Esc, aby odrzucić"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "Kalendarze wszystkich"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "Wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Expand all"
msgstr "Rozwiń wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export"
msgstr "Eksport"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export All"
msgstr "Eksportuj wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Export Data"
msgstr "Eksport danych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Export Format:"
msgstr "Eksportuj format:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "Eksportowanie zgrupowanych danych do csv nie jest obsługiwane."

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "External ID"
msgstr "Identyfikator zewnętrzny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "Link zewnętrzny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr "Nie udało się oszacować kontekstu wyszukiwania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "Fałsz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "Ulubione"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Field:"
msgstr "Pole:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Fields View Get"
msgstr "Pobierz widok pól"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fields to export"
msgstr "Pola do eksportu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File"
msgstr "Plik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "Wysyłanie pliku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "Filtr o tej nazwie już istnieje."

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "Filtry"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Flip axis"
msgstr "Zamień osie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Fold"
msgstr "Zwiń"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "Obserwowane przez"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "Czcionka"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "Stopka"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Tekst stopki wyświetlanej w dolnej części wszystkich raportów."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_controller.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"Ze względu na kompatybilność z programem Excel dane nie mogą być eksportowane, jeśli jest więcej niż 16384 kolumn.\n"
"\n"
"Wskazówka: spróbuj odwrócić oś, filtrować dalej lub zmniejszyć liczbę środków."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "Formularz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "Freedom View (widok wolności)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "Pełna nazwa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "Fuksja"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "Uzyskaj tę funkcję i wiele więcej dzięki Odoo Enterprise!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "Wykres"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "Zielony"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Group By"
msgstr "Grupuj wg"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Handle"
msgstr "Obsługa"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "Tekst nagłówka wyświetlany w górnej części wszystkich raportów."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "Ukryj w Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "Naciśnij DOWN, aby przejść do listy poniżej"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "Naciśnij ENTER, aby"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "Naciśnij ENTER, aby utworzyć"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "Wybierz ENTER aby Zapisać"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "Naciśnij ESCAPE, aby odrzucić"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "Jestem pewien."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "Chcę zaktualizować dane (eksport zgodny z importem)"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "ID"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr ""
"Jeśli zmienisz %s lub %s, synchronizacja zostanie zastosowana ponownie, a "
"dane zostaną zmodyfikowane."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Image"
msgstr "Obraz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "In %s days"
msgstr "W %s dni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Integer"
msgstr "Liczba całkowita"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "Interwał"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Invalid data"
msgstr "Nieprawidłowe dane"

#. module: web
#: code:addons/web/controllers/main.py:0 code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr ""
"Niepoprawna nazwa bazy danych. Dozwolone są tylko znaki alfanumeryczne, "
"podkreślenia, łączniki i kropki."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid domain"
msgstr "Niewłaściwa domena"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr "Nieprawidłowy łańcuch pól"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr ""
"Nieprawidłowy łańcuch pola. Być może użyłeś nieistniejącej nazwy pola lub "
"podążałeś za nierelacyjnym polem."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "Nieprawidłowe pola:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Invalid inherit mode. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""
"Możliwe jest, że czas \"t-call\" nie odpowiada ogólnemu czasowi\n"
"szablonu. Ponieważ czas globalny (w zrzucie) nie uwzględnia.\n"
"czas trwania, który nie jest w renderowaniu (szukanie szablonu, czytanie, dziedziczenie,\n"
"kompilacja...). Podczas renderowania, czas globalny bierze również część czasu na wykonanie\n"
"profilu, a także część niezalogowaną w funkcji wygenerowanej przez qweb."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Kanban Examples"
msgstr "Kanban Przykłady"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr "Kanban: brak akcji dla typu:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Languages"
msgstr "Języki"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "Data ostatniej modyfikacji"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "Data ostatniej modyfikacji:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "Ostatnio modyfikowano przez:"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "Układ"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "Tło szablonu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "Opuść tryb deweloperski"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "Jasnoniebieski"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Line Chart"
msgstr "Wykres liniowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "Lista"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "Załaduj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Load more... ("
msgstr "Wczytaj więcej... ("

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "Pobieranie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "Ładowanie proszę czekać..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading..."
msgstr "Pobieranie..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "Zaloguj"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "Zaloguj się jako superużytkownik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Log out"
msgstr "Wyloguj"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "Podstawowy kolor logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "Drugi kolor logo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Mac"
msgstr "Mac"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr "MailDeliveryException"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Main actions"
msgstr "Główne akcje"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Manage Attachments"
msgstr "Zarządzaj załącznikami"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "Zarządzaj bazami"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "Zarządzaj filtrami"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2many"
msgstr "Many2many"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Many2one"
msgstr "Many2one"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "Dopasuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "Dopasuj rekordy do"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "Dopasuj rekordy z następującą zasadą:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "Może powinieneś przeładować aplikację naciskając F5..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "Miary"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "Średnioniebieski"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "Temat spotkania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "Temat spotkania:"

#. module: web
#: model:ir.model,name:web.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Method:"
msgstr "Metoda:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "Brakujący rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Mobile support"
msgstr "Wsparcie dla urządzeń mobilnych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr "Zasady rekordów modelu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Modifiers:"
msgstr "Modyfikatorzy:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Module %s not loaded or inexistent, or templates of addon being loaded (%s) "
"are misordered"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Monetary"
msgstr "Pieniężny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Month"
msgstr "Miesiąc"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "More"
msgstr "Więcej"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Multiline Text"
msgstr "Tekst wielowierszowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "Moje konto Odoo.com"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "BRAK"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "New"
msgstr "Nowe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "Nowy %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "New Event"
msgstr "Nowe wydarzenie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password"
msgstr "Nowe hasło"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New Password (Confirmation)"
msgstr "Nowe hasło (potwierdzenie)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New design"
msgstr "Nowy wygląd"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "New template"
msgstr "Nowy szablon"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Next"
msgstr "Następny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "Następna strona"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "No"
msgstr "Nie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "Brak aktualizacji:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr "Nie udało się znaleźć akcji o identyfikatorze '%s'"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "Brak koloru"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No commands found"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "Brak danych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "Brak danych do wyświetlenia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "No match found."
msgstr "Nie znaleziono dopasowania."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "Nie znaleziono menu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "No records"
msgstr "Brak rekordów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "No records found!"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No results found"
msgstr "Nic nie znaleziono"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "No template found to inherit from. Module %s and template name %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "No valid record to save"
msgstr "Brak prawidłowego rekordu do zapisania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr "W bieżącej akcji nie udało się znaleźć żadnego widoku typu '%s'."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "None"
msgstr "Brak"

#. module: web
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "Nieustalone"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state"
msgstr "Stan nieaktywny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "Stan nieaktywny, kliknij, aby go zmienić"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Object:"
msgstr "Obiekt:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Aplikacje Odoo będą dostępne wkrótce "

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Błąd klienta"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Odoo Enterprise"
msgstr "Odoo wersja Enterpise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "Błąd serwera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr "Błąd sieci Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr "Błąd serwera Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "Sesja wygasła"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Ostrzeżenie Odoo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr ""
"Z wybranych rekordów %d tylko pierwsze %d zostały "
"zarchiwizowane/niezarchiwizowane."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Old Password"
msgstr "Stare hasło"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "Przy zmianie:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "One2many"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr ""
"Dostęp do tej bazy danych mają tylko pracownicy. Prosimy o kontakt z "
"administratorem."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr "Usunięto tylko pierwsze rekordy %d (z %d wybranych)"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr ""
"Tylko typy %(supported_types)s są obsługiwane dla kategorii (znaleziony typ "
"%(field_type)s)"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr ""
"Tylko typy %(supported_types)s są obsługiwane dla filtra (znaleziony typ "
"%(field_type)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "Tylko ty"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open Command Palette"
msgstr "Otwórz paletę poleceń"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "Otwórz widok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the next record"
msgstr "Otwórz następny rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open the previous record"
msgstr "Otwórz poprzedni rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to kanban view"
msgstr "Otwórz w widoku Kanban"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Open to list view"
msgstr "Otwórz w widoku listy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "Otwórz: "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_controller.js:0
#, python-format
msgid "Open: %s"
msgstr "Otwórz: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr "Kolumny opcjonalne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "Pomarańczowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#, python-format
msgid "Other"
msgstr "Inne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "PDF Viewer"
msgstr "Przeglądarka PDF"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "Kontrolki PDF"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"Strona:\n"
"                    <span class=\"page\"/>\n"
"                    z\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "Strona: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr "Pager"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "Format papieru"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "Kontrahent"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "Hasło"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage"
msgstr "Procentowo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "Procentowy udział"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "Wybierz kolor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Pie Chart"
msgstr "Wykres kołowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Pie chart cannot mix positive and negative numbers. "
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr ""
"Wykres kołowy nie może mieszać liczb dodatnich i ujemnych. Spróbuj zmienić "
"domenę, aby wyświetlała tylko pozytywne wyniki"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "Pivot"

#. module: web
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "Pivot %(title)s (%(model_name)s)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_view.xml:0
#, python-format
msgid "Pivot settings"
msgstr "Ustawienia pivot"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "Prosimy o cierpliwość."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "Kliknij najpierw przycisk \"zapisz\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "Proszę wprowadzić wartość liczbową"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "Proszę wprowadzić nazwę zapisywanej listy pól"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "Proszę zapisać przed załączeniem pliku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "Wybierz pola do eksportu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "Wprowadź pola do zapisu w pliku eksportu..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "Zaktualizuj tłumaczenia:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr "Użyj przycisku kopiuj, aby zgłosić błąd do pomocy technicznej."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2020/07/0003</span></b>"
msgstr ""
"W treści przelewu prosimy podać: <b><span>\n"
"                           INV/2020/07/0003</span></b>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "Napędzany przez %s %s"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "Powered by <span>Odoo</span>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "Preferencje"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "Podgląd"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "Podgląd raportu zewnętrznego"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "Podgląd raportu wewnętrznego"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "Logo podglądowe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Previous"
msgstr "Poprzedni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "Poprzedni okres"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "Rok poprzedni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "Poprzednie menu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "Poprzednia strona"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "Kolor podstawowy "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Print"
msgstr "Drukuj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/report.xml:0
#, python-format
msgid "Printing options"
msgstr "Opcje drukowania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Priority"
msgstr "Priorytet"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#: code:addons/web/static/src/legacy/xml/file_upload_progress_card.xml:0
#, python-format
msgid "Processing..."
msgstr "Przetwarzanie..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Progress Bar"
msgstr "Pasek postępu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "Purpurowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "Qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "Kwartał"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Quick add"
msgstr "Szybkie dodawanie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Quick search: %s"
msgstr "Szybkie wyszukiwanie: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr "Obraz pola Qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Radio"
msgstr "Radio"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr "Rekord qweb"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr "Rekord sql"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr "Ślady rekordów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "Czerwony"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Refresh"
msgstr "Odśwież"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "Regeneruj pakiety zasobów"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "Relation not allowed"
msgstr "Relacja niedozwolona"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "Relacja do podążania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation:"
msgstr "Powiązania:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remaining Days"
msgstr "Pozostałe dni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Remove"
msgstr "Usuń"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Remove Cover Image"
msgstr "Usuń zdjęcie główne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Remove field"
msgstr "Usuń pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "Usuń z ulubionych"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "Usuń znacznik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "Usuń to ulubione z listy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "Raport"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "Stopka raportu"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "Układ raportu"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr "Podgląd układu raportu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "Limit czasu żądania"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "Przywrócenie kolorów logo"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "Uruchom test \"Kliknij wszędzie\""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "Uruchom testy mobilne JS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "Uruchom testy JS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "SIGNATURE"
msgstr "PODPIS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "Łososiowy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "Zapisz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & Close"
msgstr "Zapisz i zamknij"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Save & New"
msgstr "Zapisz i utwórz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Save a record"
msgstr "Zapisz rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "Zapisz jako:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "Zapisz bieżące wyszukiwanie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "Zapisz jako domyślne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "Zapisz rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search"
msgstr "Szukaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search More..."
msgstr "Szukaj dalej..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_service.js:0
#, python-format
msgid "Search for a command..."
msgstr "Szukaj polecenia..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "Szukaj rekordy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "Search..."
msgstr "Szukaj..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Search: %s"
msgstr "Szukaj: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "Drugi kolor"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "Zobacz szczegóły"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "See examples"
msgstr "Zobacz przykłady"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select"
msgstr "Wybierz"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr ""
"Wybierz <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "Wybierz styl podpisu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "Wybierz model by dodać filtr."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "Wybierz widok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select all"
msgstr "Wybierz wszystkie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select field"
msgstr "Wybierz pole"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Selected records"
msgstr "Wybrane rekordy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Selection"
msgstr "Wybór"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Selection:"
msgstr "Wybór:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Default"
msgstr "Ustaw jako domyślny"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "Set Defaults"
msgstr "Ustaw domyślne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "Dodaj zdjęcie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr "Ustaw stan kanban..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "Ustaw priorytet..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "Ustaw strefę czasową na swoim użytkowniku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set kanban state..."
msgstr "Ustaw stan kanban..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set priority..."
msgstr "Ustaw priorytet..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Settings"
msgstr "Ustawienia"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "Współdziel z wszystkimi użytkownikami"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "Skróty"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "Pokaż pola podrzędne"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "Wyświetla lokalne moduły"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "Podpis"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "Rozmiar:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr ""
"Coś się stało podczas próby kontaktu z serwerem, sprawdź czy serwer jest "
"online i czy nadal masz działające połączenie sieciowe."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Something horrible happened"
msgstr "Stało się coś strasznego"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Sort graph"
msgstr "Sortowanie wykresu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Special:"
msgstr "Specjalne:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_view.xml:0
#, python-format
msgid "Stacked"
msgstr "Ułożone"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Start typing..."
msgstr "Zacznij pisać..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "Ciągle pobieram..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "Trwa pobieranie...<br />Bądź cierpliwy."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "Styl"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "Style"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "Wsparcie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "Błąd składni"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Tags"
msgstr "Tagi"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "Poświęć chwilę na kawę,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "Zrób sobie przerwę na kawę,<br /> pobieranie potrwa..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "NIP"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Technical Translation"
msgstr "Tłumaczenie techniczne"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
msgid "Tel:"
msgstr "Tel:"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Template %s already exists in module %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Template:"
msgstr "Szablon:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Text"
msgstr "Tekst"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr ""
"Zawartość tej komórki jest zbyt długa dla pliku XLSX (więcej niż %s znaków)."
" Proszę użyć formatu CSV do tego eksportu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "Pole jest puste, nie ma nic do zapisania."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "The new password and its confirmation must be identical."
msgstr "Nowe hasło i jego potwierdzenie muszą być jednakowe."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"The old password you provided is incorrect, your password was not changed."
msgstr ""
"Poprzednie hasło wprowadziłeś niepoprawnie. Hasło nie zostało zmienione."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr ""
"Operacja została przerwana. Zwykle oznacza to, że bieżąca operacja zajmuje "
"zbyt dużo czasu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "Wybrany plik przekracza maksymalny rozmiar %s."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr ""
"Typ pola '%s' musi być typem \"wiele do wielu pól\" w relacji do modelu "
"'ir.attachment'."

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr ""
"Jest zbyt wiele wierszy (%s wiersze, limit: %s) do wyeksportowania jako "
"format Excel 2007-2013 (.xlsx). Rozważ podzielenie eksportu."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "Brak dostępnego obrazu do ustawienia jako okładka."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "Pojawił się problem przy wgrywaniu pliku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr ""
"Ta data jest w przyszłości. Upewnij się, że to jest to, czego oczekujesz."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/datepicker/datepicker.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr "Ta data jest w przyszłości. Upewnij się, że tego właśnie oczekiwałeś."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "Ta domena nie jest obsługiwana."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "Ten plik jest nieprawidłowy. Proszę wybrać obraz."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr ""
"Ten filtr działa globalnie i zostanie usunięty dla wszystkich jeśli będziesz"
" kontynuować."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "To jest przykład zewnętrznego raportu"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "To jest przykład wewnętrznego raportu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr "Ta aktualizacja będzie uwzględniać tylko rekordy bieżącej strony."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Time"
msgstr "Godzina"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"Niezgodność strefy czasowej: Ta strefa czasowa jest inna niż ta w Twojej przeglądarce.\n"
"Proszę ustawić taką samą strefę czasową jak w przeglądarce, aby uniknąć rozbieżności czasowych w systemie."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#, python-format
msgid "Today"
msgstr "Na dzisiaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Toggle"
msgstr "Przełącz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Tomorrow"
msgstr "Jutro"

#. module: web
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "Zbyt wiele przedmiotów do wyświetlenia."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "Suma"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "Tłumacz: %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "Prawda"

#. module: web
#. openerp-web
#: code:addons/web/static/src/views/helpers/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""
"Spróbuj dodać kilka rekordów lub upewnij się, że nie ma\n"
"aktywnego filtru w pasku wyszukiwania."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#, python-format
msgid "Try to change your domain to only display positive results"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Type:"
msgstr "Typ:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "URL"
msgstr "URL"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr ""
"Nie można znaleźć Wkhtmltopdf w tym systemie. Raport zostanie wyświetlony w "
"html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "Przywróć"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unarchive All"
msgstr "Odarchiwizuj wszystko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr "Nieoczekiwany błąd CORS"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr "Niepożądany błąd Javascript"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr "Nieoczekiwana obietnica"

#. module: web
#. openerp-web
#: code:addons/web/controllers/main.py:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_model.js:0
#: code:addons/web/static/src/legacy/js/views/graph/graph_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Undefined"
msgstr "Niezdefiniowane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "Unfold"
msgstr "Rozwiń"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""
"Nieznany błąd CORS\n"
"\n"
"Wystąpił nieznany błąd CORS.\n"
"Błąd prawdopodobnie pochodzi z pliku JavaScript serwowanego z innego pochodzenia.\n"
"(Otwarcie konsoli przeglądarki może dać ci wskazówkę na temat błędu)."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "Nieznany typ nonliteral "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "Odłącz wiersz "

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "Bez nazwy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/graph/graph_view.js:0
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_view.js:0
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "Bez tytułu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Update to:"
msgstr "Uaktualnij do:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/upgrade_fields.js:0
#, python-format
msgid "Upgrade now"
msgstr "Aktualizuj teraz"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "Uaktualnij do enterprise"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "Aktualizuj do przyszłych wersji"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Upload and Set"
msgstr "Prześlij i ustaw"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_bar.js:0
#, python-format
msgid "Upload cancelled"
msgstr "Przesyłanie pliku anulowane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "Wgraj plik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploaded"
msgstr "Przesłane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading"
msgstr "Przesyłanie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "Błąd wgrywania"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Uploading..."
msgstr "Wgrywanie..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_progress_card.js:0
#, python-format
msgid "Uploading... (%s%%)"
msgstr "Przesyładnie pliku... (%s%%)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr "Użyj tego dla mojego kanbana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "Stosuj domyślnie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "Użytkownik"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "Błąd użytkownika"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "Błąd walidacji"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr "Ustawione tu wartości są specyficzne dla danej firmy."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "Zmiana"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "Widok %s"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "Pokaż prawa dostępu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "Pola widoku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#, python-format
msgid "View Metadata"
msgstr "Pokaż Metadane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "Pokaż zasady rekordu"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "Typ widoku"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "Przełącznik widoków"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Warning"
msgstr "Ostrzeżenie"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "Testy sieciowe"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "Testy mobilne w Internecie"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "Testy internetowe"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "Link do strony WWW"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Week"
msgstr "Tydzień"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Widget:"
msgstr "Kontrolka:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "Windows/Linux"
msgstr "Windows/Linux"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr "Zawiń surowy html w ramkę iframe"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "Wrong login/password"
msgstr "Błędny login lub hasło"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/xml/web_calendar.xml:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Year"
msgstr "Rok"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "Żółty"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "Yes"
msgstr "Tak"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Yesterday"
msgstr "Wczoraj"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Nie możesz przesłać tutaj załącznika."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr "Nie można śledzić relacji dla tej konstrukcji ciągu pola"

#. module: web
#: code:addons/web/controllers/main.py:0
#, python-format
msgid "You cannot leave any password empty."
msgstr "Hasło nie może być puste"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "Możesz w to nie wierzyć,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "Może nie wierzysz,<br />ale aplikacja ciągle się ładuje..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr ""
"Musisz zapisać ten nowy rekord przed edycją tłumaczenia. Czy chcesz "
"kontynuować?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr ""
"Musisz uruchomić Odoo z co najmniej dwoma workerami, aby wydrukować wersję "
"PDF raportów."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr ""
"Należy zaktualizować swoją wersję Wkhtmltopdf do co najmniej 0.12.0, aby "
"uzyskać poprawne wyświetlanie nagłówków i stopek, a także obsługę łamania "
"tabel między stronami."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr "Twoja sesja Odoo wygasła. Bieżąca strona zostanie wkrótce odświeżona."

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr ""
"Twoja instalacja Wkhtmltopdf wydaje się być zepsuta. Raport zostanie "
"wyświetlony w html."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/name_and_signature.js:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Your name"
msgstr "Twoja nazwa"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[Brak widgetu %s]"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "dzień temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "około minuty temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "około miesiąca temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "około rok temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "około godziny temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "wszystkie rekordy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "are valid for this update."
msgstr "są ważne dla tej aktualizacji."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "jako nowy rekord"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "na:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "bo się ładuje..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "ale aplikacja w rzeczywistości ładuje się..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "dziecko"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "zawiera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "data"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "nie zawiera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "nie zawiera"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "pobieranie..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "np. Global Business Solutions"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "dla:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "większe niż"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "większe lub równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr "hex"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "w"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "jest"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "jest po"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "jest po lub równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "jest przed"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "jest przed lub równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "jest pomiędzy"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "jest równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is false"
msgstr "nie jest prawdą"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "nie jest"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "nie jest ="

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "nie jest równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "nie jest ustawione"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "jest ustawione"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is true"
msgstr "jest prawdą"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "nadal się ładuje..."

#. module: web
#. openerp-web
#: code:addons/web/static/src/fields/formatters.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "mniejsze niż"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "mniej niż minutę temu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "mniejsze lub równe"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/file_upload_mixin.js:0
#, python-format
msgid "message: %s"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "brak opisu"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "nie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "nie w"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "nie ustawione (false)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "następujących zasad:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "z:"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "lub"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "rodzic"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#, python-format
msgid "props.fields"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr "zapytanie"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "record(s)"
msgstr "rekord(y)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "records ?"
msgstr "rekordach?"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#, python-format
msgid "remaining)"
msgstr "pozostało)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "szukaj"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected"
msgstr "wybrane"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "selected records,"
msgstr "wybrane rekordy,"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "ustaw"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "ustawione (true)"

#. module: web
#. openerp-web
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "well Done!"
msgstr ""

#. module: web
#. openerp-web
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "{\"o_act_window\": actionType === \"ir.actions.act_window\"}"
msgstr ""
