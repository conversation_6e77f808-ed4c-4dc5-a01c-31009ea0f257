# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* fetchmail
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.4\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2021-09-14 12:22+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "การดำเนินการที่ต้องทำกับเมลขาเข้า"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced"
msgstr "ขั้นสูง"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Advanced Options"
msgstr "ตัวเลือกขั้นสูง"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"มีข้อผิดพลาด SSL เกิดขึ้น ตรวจสอบการกำหนดค่า SSL/TLS บนพอร์ตเซิร์ฟเวอร์\n"
" %s"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Archived"
msgstr "เก็บถาวร"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "การทดสอบการเชื่อมต่อล้มเหลว: %s"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"การเชื่อมต่อถูกเข้ารหัสด้วย SSL/TLS ผ่านพอร์ตเฉพาะ (ค่าเริ่มต้น: IMAPS=993, "
"POP3S=995)"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "สร้างบันทึกถัดไป"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "กำหนดลำดับการประมวลผล ค่าที่ต่ำกว่าหมายถึงลำดับความสำคัญที่สูงกว่า"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__display_name
msgid "Display Name"
msgstr "ชื่อที่ใช้แสดง"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "Email Count"
msgstr ""

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Fetch Now"
msgstr "ดึงข้อมูลตอนนี้"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "ชื่อโฮสต์หรือ IP ของเมลเซิร์ฟเวอร์"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__id
msgid "ID"
msgstr "รหัส"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "เซิร์ฟเวอร์ IMAP"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "If SSL required."
msgstr "หากจำเป็นต้องใช้ SSL"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "เซิร์ฟเวอร์จดหมายขาเข้า"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.action_email_server_tree
#: model:ir.ui.menu,name:fetchmail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name !\n"
" %s"
msgstr ""

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "เก็บไฟล์แนบ"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "เก็บต้นฉบับไว้"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "วันที่ดึงข้อมูลล่าสุด"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server____last_update
msgid "Last Modified on"
msgstr "แก้ไขครั้งสุดท้ายเมื่อ"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_uid
msgid "Last Updated by"
msgstr "อัพเดทครั้งสุดท้ายโดย"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__write_date
msgid "Last Updated on"
msgstr "อัพเดทครั้งสุดท้ายเมื่อ"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "เซิร์ฟเวอร์ภายในเครื่อง"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Login Information"
msgstr "ข้อมูลล็อกอิน"

#. module: fetchmail
#: model:ir.actions.server,name:fetchmail.ir_cron_mail_gateway_action_ir_actions_server
#: model:ir.cron,cron_name:fetchmail.ir_cron_mail_gateway_action
#: model:ir.cron,name:fetchmail.ir_cron_mail_gateway_action
msgid "Mail: Fetchmail Service"
msgstr "อีเมล: บริการ Fetchmail"

#. module: fetchmail
#: model:ir.actions.act_window,name:fetchmail.act_server_history
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__name
msgid "Name"
msgstr "ชื่อ"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"ไม่ได้รับการตอบกลับ ตรวจสอบข้อมูลเซิร์ฟเวอร์\n"
" %s"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "ไม่ได้รับการยืนยัน"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "เซิร์ฟเวอร์อีเมลขาออก"

#. module: fetchmail
#: model:ir.model,name:fetchmail.model_mail_mail
msgid "Outgoing Mails"
msgstr "อีเมลขาออก"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: fetchmail
#: model:ir.model.fields.selection,name:fetchmail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "เซิร์ฟเวอร์ POP"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "เซิร์ฟเวอร์ POP/IMAP"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__password
msgid "Password"
msgstr "รหัสผ่าน"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__port
msgid "Port"
msgstr "พอร์ต"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"ประมวลผลอีเมลขาเข้าแต่ละฉบับโดยเป็นส่วนหนึ่งของการสนทนาที่เกี่ยวข้องกับเอกสารประเภทนี้"
" สิ่งนี้จะสร้างเอกสารใหม่สำหรับการสนทนาใหม่ "
"หรือแนบอีเมลติดตามผลกับการสนทนาที่มีอยู่ (เอกสาร)"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Reset Confirmation"
msgstr "รีเซ็ตการยืนยัน"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__script
msgid "Script"
msgstr "สคริปต์"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "ค้นหาเซิร์ฟเวอร์เมลขาเข้า"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server & Login"
msgstr "เซิร์ฟเวอร์ & เข้าสู่ระบบ"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Server Information"
msgstr "ข้อมูลเซิร์ฟเวอร์"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server
msgid "Server Name"
msgstr "ชื่อเซิร์ฟเวอร์"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "ลำดับความสำคัญของเซิร์ฟเวอร์"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "ประเภทเซิร์ฟเวอร์"

#. module: fetchmail
#: code:addons/fetchmail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"เซิร์ฟเวอร์ตอบกลับโดยมีข้อยกเว้นดังต่อไปนี้:\n"
" %s"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type IMAP."
msgstr "ประเภทเซิร์ฟเวอร์ IMAP"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_search
msgid "Server type POP."
msgstr "ประเภทเซิร์ฟเวอร์ POP"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__state
msgid "Status"
msgstr "สถานะ"

#. module: fetchmail
#: model_terms:ir.ui.view,arch_db:fetchmail.view_email_server_form
msgid "Test & Confirm"
msgstr "ทดสอบและยืนยัน"

#. module: fetchmail
#: model:ir.model.fields,field_description:fetchmail.field_fetchmail_server__user
msgid "Username"
msgstr "ชื่อผู้ใช้"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"ควรเก็บสำเนาต้นฉบับแบบฉบับเต็มของอีเมลแต่ละฉบับเพื่อใช้อ้างอิงและแนบไปกับข้อความที่ประมวลผลแต่ละฉบับหรือไม่"
" โดยปกติแล้วจะเพิ่มขนาดฐานข้อมูลข้อความของคุณเป็นสองเท่า"

#. module: fetchmail
#: model:ir.model.fields,help:fetchmail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"ควรดาวน์โหลดไฟล์แนบหรือไม่ หากไม่ได้เปิดใช้งาน "
"อีเมลขาเข้าจะถูกลบออกจากไฟล์แนบก่อนที่จะดำเนินการ"
