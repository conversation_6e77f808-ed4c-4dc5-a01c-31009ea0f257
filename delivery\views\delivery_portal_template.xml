<odoo>
    <template id="sale_order_portal_content_inherit_sale_stock_inherit_website_sale_delivery" name="Shipping tracking on orders followup" inherit_id="sale_stock.sale_order_portal_content_inherit_sale_stock">
        <xpath expr="//div[hasclass('o_sale_stock_picking')]/div" position="after">
            <div t-if="i.carrier_tracking_ref" class="small d-lg-inline-block">
                Tracking:
                <t t-set="multiple_carrier_tracking" t-value="i.get_multiple_carrier_tracking()"/>
                <t t-if="multiple_carrier_tracking">
                     <t t-foreach="multiple_carrier_tracking" t-as="line">
                         <a t-att-href="line[1]" target="_blank"><span t-esc="line[0]"/></a>
                         <span t-if="not line_last"> + </span>
                     </t>
                </t>
                <t t-else="">
                    <t t-if="i.carrier_tracking_url">
                        <a t-att-href="i.carrier_tracking_url" target="_blank"><span t-field="i.carrier_tracking_ref"/></a>
                    </t>
                    <t t-else="">
                        <span t-field="i.carrier_id.name"/> <span t-field="i.carrier_tracking_ref"/>
                    </t>
                </t>
            </div>
            <t t-if="i.carrier_id.get_return_label_from_portal and i.return_label_ids">
                <div>
                    <a class="ml-3"  t-attf-href="/web/content/#{i.return_label_ids[:1].id}?access_token=#{i.return_label_ids[:1].access_token}" target="_blank">Print Return Label</a>
                </div>
            </t>
        </xpath>
    </template>
</odoo>
