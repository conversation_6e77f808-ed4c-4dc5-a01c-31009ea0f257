# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mass_mailing_sms
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:28+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"
msgstr ""
"+32 495 85 85 77\n"
"+33 545 55 55 55"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "24H Stats of %(mailing_type)s \"%(mailing_name)s\""
msgstr "24H-statistieken van %(mailing_type)s \"%(mailing_name)s\""

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"canceled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message have been canceled and will not be "
"sent.</span>"
msgstr ""
"<span name=\"canceled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS-bericht is geannuleerd en wordt niet verzonden.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"failed_text_sms\" attrs=\"{'invisible': [('mailing_type', '!=',"
" 'sms')]}\">SMS Text Message could not be sent.</span>"
msgstr ""
"<span name=\"failed_text_sms\" attrs=\"{'invisible': [('mailing_type', '!=',"
" 'sms')]}\">SMS tekstbericht kon niet verzonden worden.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"next_departure_text\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">This SMS marketing is scheduled for </span>"
msgstr ""
"<span name=\"next_departure_text\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">Deze SMS-Marketing is gepland voor </span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"scheduled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS Text Message are in queue and will be sent soon.</span>"
msgstr ""
"<span name=\"scheduled_text_sms\" attrs=\"{'invisible': [('mailing_type', "
"'!=', 'sms')]}\">SMS tekstbericht zit in wachtrij en wordt binnenkort "
"verstuurd.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<span name=\"sent_sms\" attrs=\"{'invisible': [('mailing_type', '!=', "
"'sms')]}\">SMS Text Message have been sent.</span>"
msgstr ""
"<span name=\"sent_sms\" attrs=\"{'invisible': [('mailing_type', '!=', "
"'sms')]}\">SMS is verzonden.</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<span widget=\"statinfo\">Open Recipient</span>"
msgstr "<span widget=\"statinfo\">Open ontvanger</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_list_view_kanban
msgid "<span>Valid SMS Recipients</span>"
msgstr "<span>Geldige SMS-ontvangers</span>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears you don't have enough IAP credits. Click here to buy credits.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"Het lijkt erop dat je niet genoeg IAP krediet heeft. Klik hier om krediet bij te kopen.\n"
"</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"<strong>\n"
"                            It appears your SMS account is not registered. Click here to set up your account.\n"
"                        </strong>"
msgstr ""
"<strong>\n"
"                           Het lijkt erop dat je sms-account niet is geregistreerd. Klik hier om je account aan te maken.\n"
"                         </strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This SMS could not be sent.</strong>"
msgstr "<strong>Deze SMS kon niet verzonden worden.</strong>"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "<strong>This number appears to be invalid.</strong>"
msgstr "<strong>Dit nummer lijkt ongeldig.</strong>"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "A/B Test: %s"
msgstr "A/B-test: %s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction
msgid "Action Needed"
msgstr "Actie nodig"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "BOUNCED (%i)"
msgstr "BOUNCED (%i)"

#. module: mass_mailing_sms
#: model:utm.tag,name:mass_mailing_sms.mailing_tag_0
msgid "Bioutifoul SMS"
msgstr "Prachtige SMS"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid ""
"Blacklist through SMS Marketing unsubscribe (mailing ID: %s - model: %s)"
msgstr "Blacklist via SMS-Marketing afmelden (mailing ID: %s - model: %s)"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_blacklist
msgid "Blacklisted"
msgstr "Geblacklist"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Telefoon op blacklist is een mobiel nummer"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.phone_blacklist_menu
msgid "Blacklisted Phone Numbers"
msgstr "Op de blacklist geplaatste telefoonnummers"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Telefoon op blacklist Is een vaste lijn"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "CLICKED (%i)"
msgstr "GEKLIKT (%i)"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__utm_campaign_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Campaign"
msgstr "Campagne"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.menu_email_campaigns
msgid "Campaigns"
msgstr "Campagnes"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Cancel"
msgstr "Annuleren"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Carriage-return-separated list of phone numbers"
msgstr "Door Carriage-return gescheiden lijst met telefoonnummers"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_code
msgid "Code"
msgstr "Code"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid ""
"Come back once some SMS Mailings are sent to check out aggregated results."
msgstr ""
"Kom terug zodra enkele sms-mailings zijn verzonden om geaggregeerde "
"resultaten te bekijken."

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_configuration
msgid "Configuration"
msgstr "Configuratie"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid "Create a Mailing List"
msgstr "Maak een mailinglijst"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid "Create a SMS Marketing Mailing"
msgstr "Een sms-marketingmailing maken"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid "Create a mailing contact"
msgstr "Maak een mailingcontact"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_duplicate
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Duplicate"
msgstr "Dupliceren"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#, python-format
msgid "Email Marketing"
msgstr "E-mailmarketing"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Exclude Blacklisted Phone"
msgstr "Blacklisted telefoonnummers uitsluiten"

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_1
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_1
#: model:utm.source,name:mass_mailing_sms.mailing_sms_1_utm_source
msgid "Extra Promo"
msgstr "Extra promo"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__failure_type
msgid "Failure type"
msgstr "Soort mislukking"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Veld gebruikt om opgeschoond telefoonnummer in te bewaren. Helpt bij het "
"versnellen van zoekopdrachten en vergelijkingen."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Relaties)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"For an Email, Subject your Recipients will see in their inbox.\n"
"                    For an SMS Text Message, internal Title of the Message."
msgstr ""
"Voor een e-mail, onderwerp dat je ontvangers in hun inbox zullen zien.\n"
"Voor een SMS-tekstbericht, interne titel van het bericht."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_subject
msgid ""
"For an email, the subject your recipients will see in their inbox.\n"
"For an SMS, the internal title of the message."
msgstr ""
"Voor een e-mail: het onderwerp dat je ontvangers in hun inbox zien.\n"
"Voor een sms, de interne titel van het bericht."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__clicks_ratio
msgid "Highest Click Rate"
msgstr "Hoogste klikfrequentie"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__id
msgid "ID"
msgstr "ID"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid ""
"ID of the related sms.sms. This field is an integer field because the "
"related sms.sms can be deleted separately from its statistics. However the "
"ID is needed for several action and controllers."
msgstr ""
"ID van de gerelateerde sms.sms. Dit veld is een geheel getal omdat de "
"gerelateerde sms.sms afzonderlijk van de statistieken kan worden verwijderd."
" De ID is echter nodig voor verschillende acties en controllers."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "indien aangevinkt hebben sommige leveringen een fout."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Als het telefoonnummer op de blacklist staat, ontvangt de contactpersoon "
"geen mass-sms meer van welke lijst dan ook"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid ""
"Immediately send the SMS Mailing instead of queuing up. Use at your own "
"risk."
msgstr ""
"Verstuur direct de SMS Mailing in plaats van in de rij te staan. Gebruik op "
"eigen risico."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_allow_unsubscribe
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mass_sms_allow_unsubscribe
msgid "Include opt-out link"
msgstr "Inclusief opt-out link"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Geeft aan of een op de blacklist geplaatst telefoonnummer een mobiel nummer "
"is. Helpt bij het onderscheiden welk nummer op de blacklist staat als het "
"model zowel een mobiel als een telefoon veld bevat."

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Geeft aan of een op de blacklist geplaatst nummer een telefoonnummer is. "
"Helpt bij het onderscheiden welk nummer op de blacklist staat als een model "
"zowel een mobiel als een telefoon veld bevat."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_credit
msgid "Insufficient Credit"
msgstr "Onvoldoende krediet"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "Insufficient IAP credits"
msgstr "Onvoldoende IAP krediet"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Insufficient credits"
msgstr "Onvoldoende krediet"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test____last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.link_tracker_menu
msgid "Link Tracker"
msgstr "Link Tracker"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__mailing_id
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_composer__mailing_id
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Mailing"
msgstr "Mailing"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_contact
msgid "Mailing Contact"
msgstr "Mailingcontact"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_list
msgid "Mailing List"
msgstr "Mailinglijst"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_contact_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_contact_menu_sms
msgid "Mailing List Contacts"
msgstr "Mailinglijst contacten"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_list_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mailing_list_menu_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_contacts
msgid "Mailing Lists"
msgstr "Mailinglijsten"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Mail statistieken"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__mailing_type
msgid "Mailing Type"
msgstr "Soort mailing"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_contact_action_sms
msgid ""
"Mailing contacts allow you to separate your marketing audience from your "
"contact directory."
msgstr ""
"Met mailingcontacten kunt je je marketingpubliek scheiden van je "
"contactenlijst."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hoofdbijlage"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__utm_campaign__ab_testing_sms_winner_selection__manual
msgid "Manual"
msgstr "Handmatig"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Marketing"
msgstr "Marketing"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_mailing
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_id
msgid "Mass Mailing"
msgstr "Bulkmailing"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_ids
msgid "Mass SMS"
msgstr "Bulk SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_missing
msgid "Missing Number"
msgstr "Ontbrekend nummer"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__mobile
msgid "Mobile"
msgstr "Mobiel"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_search_sms
msgid "My SMS Marketing"
msgstr "Mijn SMS-Marketing"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_trace_report_action_sms
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_list_action_sms
msgid ""
"No need to import mailing lists, you can send SMS Text Messages to contacts "
"saved in other Odoo apps."
msgstr ""
"Je hoeft geen mailinglijsten te importeren, je kunt SMS-tekstberichten "
"verzenden naar contacten die zijn opgeslagen in andere Odoo-apps."

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_number
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Number"
msgstr "Nummer"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/controllers/main.py:0
#, python-format
msgid "Number %s not found"
msgstr "Cijfer %s niet gevonden"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__mailing_sms_count
msgid "Number of Mass SMS"
msgstr "Aantal bulk SMS'en"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Aantal berichten die actie vereisen"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Number of unread messages"
msgstr "Aantal ongelezen berichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_sms_test__numbers
msgid "Number(s)"
msgstr "Cijfer(s)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "Open Recipient"
msgstr "Ontvanger openen"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_optout
msgid "Opted Out"
msgstr "Afgemeld"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_sms
msgid "Outgoing SMS"
msgstr "Uitgaande SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Telefoon blacklist"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Telefoon/mobiel"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Please enter your phone number"
msgstr "Voer je mobiele telefoonnummer in"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "RECEIVED (%i)"
msgstr "ONTVANGEN (%i)"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Recipients"
msgstr "Ontvangers"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Report for %(expected)i %(mailing_type)s Sent"
msgstr "Rapport voor %(expected)i %(mailing_type)s Verzonden"

#. module: mass_mailing_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_reporting
msgid "Reporting"
msgstr "Rapportages"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_mailing__mailing_type__sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__trace_type__sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__body_plaintext
msgid "SMS Body"
msgstr "SMS inhoud"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_list__contact_count_sms
msgid "SMS Contacts"
msgstr "SMS contacten"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "SMS Content"
msgstr "SMS inhoud"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS ID"
msgstr "SMS-ID"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__sms_sms_id_int
msgid "SMS ID (tech)"
msgstr "SMS ID (technisch)"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/res_users.py:0
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_mailing_action_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_mass_sms
#: model:ir.ui.menu,name:mass_mailing_sms.mass_mailing_sms_menu_root
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
#, python-format
msgid "SMS Marketing"
msgstr "SMS-Marketing"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_trace_report_action_sms
msgid "SMS Marketing Analysis"
msgstr "SMS-Marketing analyse"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "SMS Subscription"
msgstr "SMS abonnement"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_template_id
msgid "SMS Template"
msgstr "SMS sjabloon"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "SMS Text Message"
msgstr "SMS tekstbericht"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "SMS Trace"
msgstr "SMS tracering"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_tree_sms
msgid "SMS Traces"
msgstr "SMS traceringen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__ab_testing_sms_winner_selection
#: model:ir.model.fields,field_description:mass_mailing_sms.field_utm_campaign__ab_testing_sms_winner_selection
msgid "SMS Winner Selection"
msgstr "Selectie van SMS-winnaars"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/sms_composer.py:0
#, python-format
msgid "STOP SMS : %s"
msgstr "STOP SMS : %s"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__phone_sanitized
msgid "Sanitized Number"
msgstr "Opgeschoond nummer"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_tree_sms
msgid "Scheduled"
msgstr "Gepland"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send"
msgstr "Verzenden"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_force_send
msgid "Send Directly"
msgstr "Direct versturen"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Send Now"
msgstr "Nu verzenden"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.utm_campaign_view_form
msgid "Send SMS"
msgstr "Verzend SMS"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Verzend SMS wizard"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid "Send a Sample SMS"
msgstr "Verzend een voorbeeld SMS"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_sms_test_view_form
msgid ""
"Send a sample SMS for testing purpose to the numbers below (carriage-return-"
"separated list)."
msgstr ""
"Stuur voor testdoeleinden een voorbeeld-sms naar de onderstaande nummers "
"(lijst met gescheiden rij- en retourzendingen)."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_server
msgid "Server Error"
msgstr "Server Error"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_sms_sms__mailing_trace_ids
msgid "Statistics"
msgstr "Statistieken"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_trace_view_form_sms
msgid "Status"
msgstr "Status"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_mailing_sms_test
msgid "Test SMS Mailing"
msgstr "Test SMS mailing"

#. module: mass_mailing_sms
#: model:ir.actions.act_window,name:mass_mailing_sms.mailing_sms_test_action
msgid "Test SMS Marketing"
msgstr "Test SMS-Marketing"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS could not be sent to %s:<br>%s"
msgstr "Test-sms kon niet worden verzonden naar %s:<br>%s"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Test SMS marketing"
msgstr "Test SMS-Marketing"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "Test SMS successfully sent to %s"
msgstr "Test-sms succesvol verzonden naar %s"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/wizard/mailing_sms_test.py:0
#, python-format
msgid "The following numbers are not correctly encoded: %s"
msgstr "De volgende nummers zijn niet correct gecodeerd: %s"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "There was an error when trying to unsubscribe"
msgstr "Er was een fout tijdens het afmelden"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"Dit telefoonnummer staat op de blacklist voor sms-marketing. Klik om deze "
"van de blacklist te verwijderen."

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid ""
"This will send SMS to all recipients now. Do you still want to proceed ?"
msgstr ""
"Dit verzend nu een SMS naar alle ontvangers. Wil je nog steeds verder gaan?"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "This will send SMS to all recipients. Do you still want to proceed ?"
msgstr ""
"Hiermee wordt een sms naar alle ontvangers verzonden. Wil je toch doorgaan?"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_subject
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Title"
msgstr "Titel"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_trace__trace_type
msgid "Type"
msgstr "Soort"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Campagne"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "UX Field to propose to Register the SMS IAP account"
msgstr "UX veld om de aankoop van IAP credits voor te stellen"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_mailing__sms_has_insufficient_credit
msgid "UX Field to propose to buy IAP credits"
msgstr "UX veld om de aankoop van IAP credits voor te stellen"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Aantal ongelezen berichten"

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_acc
msgid "Unregistered Account"
msgstr "Niet-geregistreerd account"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_mailing__sms_has_unregistered_account
msgid "Unregistered IAP account"
msgstr "Niet-geregistreerde IAP-account."

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_kanban_sms
msgid "Unregistered account"
msgstr "Niet-geregistreerd account."

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_main
msgid "Unsubscribe me"
msgstr "Afmelden"

#. module: mass_mailing_sms
#: code:addons/mass_mailing_sms/models/mailing_mailing.py:0
#, python-format
msgid "Unsupported %s for mass SMS"
msgstr "Niet ondersteund %s voor bulk SMS"

#. module: mass_mailing_sms
#: model:ir.model,name:mass_mailing_sms.model_res_users
msgid "Users"
msgstr "Gebruikers"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_contact_view_search
msgid "Valid SMS Recipients"
msgstr "Geldige SMS ontvangers"

#. module: mass_mailing_sms
#: model:ir.model.fields,field_description:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: mass_mailing_sms
#: model:ir.model.fields,help:mass_mailing_sms.field_mailing_contact__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "Winner Selection"
msgstr "Winnaar Selectie"

#. module: mass_mailing_sms
#: model_terms:ir.actions.act_window,help:mass_mailing_sms.mailing_mailing_action_sms
msgid ""
"Write an appealing SMS Text Message, define recipients and track its "
"results."
msgstr ""
"Schrijf een aantrekkelijk sms-bericht, definieer ontvangers en volg de "
"resultaten ervan."

#. module: mass_mailing_sms
#: model:ir.model.fields.selection,name:mass_mailing_sms.selection__mailing_trace__failure_type__sms_number_format
msgid "Wrong Number Format"
msgstr "Fout nummer formaat"

#. module: mass_mailing_sms
#: model:mailing.mailing,name:mass_mailing_sms.mailing_sms_0
#: model:mailing.mailing,sms_subject:mass_mailing_sms.mailing_sms_0
#: model:utm.campaign,name:mass_mailing_sms.utm_campaign_0
#: model:utm.source,name:mass_mailing_sms.mailing_sms_0_utm_source
msgid "XMas Promo"
msgstr "Kerstmis promo"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.mailing_mailing_view_form_sms
msgid "e.g. Black Friday SMS coupon"
msgstr "bijv. Black Friday sms-coupon"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully blacklisted"
msgstr "is succesvol geblacklist"

#. module: mass_mailing_sms
#: model_terms:ir.ui.view,arch_db:mass_mailing_sms.blacklist_number
msgid "has been successfully removed from"
msgstr "is succesvol verwijderd van"
